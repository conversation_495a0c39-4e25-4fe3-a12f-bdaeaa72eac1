/**
 * New Relic agent configuration.
 *
 * See lib/config.defaults.js in the agent distribution for a more complete
 * description of configuration variables and their potential values.
 */
exports.config = {
  /**
   * Array of application names.
   */
  app_name: [`Node API ${process.env.ENVIRONMENT || 'local'}`],

  /**
   * This will still startup New Relic. It just wont send data.
   * Set NEW_RELIC_ENABLED environment variable to true to enable
   *
   * @env NEW_RELIC_ENABLED
   */
  agent_enabled: false,

  /**
   * Your New Relic license key.
   */
  license_key: '6e6e1c285cb1b4a5818666ffceaee38c6001e76c',
  logging: {
    /**
     * Level at which to log. 'trace' is most useful to New Relic when diagnosing
     * issues with the agent, 'info' and higher will impose the least overhead on
     * production applications.
     */
    level: 'info',
  },
};
