{"client.invenco-icp-app-enabled": {"label": "", "readonly": true}, "client.invenco-icp-peer-cast-mode": {"label": "", "readonly": true}, "client.invenco-icp-peer-election-state": {"label": "", "readonly": true}, "client.invenco-icp-peer-master": {"label": "", "readonly": true}, "client.invenco-icp-peer-num-peers": {"label": "", "readonly": true}, "cfg.net-terminal-id": {"label": "Terminal ID", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-controller-ip": {"label": "Controller IP Address", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-controller-port": {"label": "Controller Port", "readonly": false, "target": "remote", "type": "number", "format": "string", "options": {"minimum": 1, "maximum": 65355}}, "cfg.net-configurationservice-ip": {"label": "Configuration Service IP Address", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-configurationservice-port": {"label": "Configuration Service Port", "readonly": false, "target": "remote", "type": "number", "format": "string", "options": {"minimum": 1, "maximum": 65355}}, "cfg.net-dhcp-enabled": {"label": "Network Mode", "readonly": false, "target": "remote", "type": "string", "format": "string", "options": {"enum": ["DHCP", "Manual"]}}, "cfg.net-ip-addr": {"label": "Terminal IP Address", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-netmask": {"label": "Network Mask", "readonly": false, "target": "remote", "type": "string", "format": "string", "options": {"enum": ["*********", "***********", "*************"]}}, "cfg.net-gateway": {"label": "Network Gateway", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-dns": {"label": "DNS Address", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-ntp-pool": {"label": "NTP Pool", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-ntp-server": {"label": "NTP Server IP Address", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-log-server-mode": {"label": "Syslog Mode", "readonly": false, "target": "remote", "type": "string", "format": "string", "options": {"enum": ["TCP", "UDP"]}}, "cfg.net-log-server": {"label": "Syslog IP Address", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-log-server-port": {"label": "Syslog Port", "readonly": false, "target": "remote", "type": "number", "format": "string", "options": {"minimum": 1, "maximum": 65355}}, "cfg.mgt-pci-reboot-time": {"label": "PCI Reboot Time", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.rtc-time-zone": {"label": "Timezone", "readonly": false, "target": "remote", "type": "string", "format": "string", "options": {"enum": []}}, "cfg.rtc-date-time-local": {"label": "Date/time", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.aud-volume-master": {"label": "Master Volume", "readonly": false, "target": "remote", "type": "number", "format": "string", "options": {"minimum": 0, "maximum": 100}}, "g7opt.upc-tamper-temperature.gauge.last": {"label": "", "readonly": true, "target": "local"}, "target.serial_number": {"label": "UPC Serial", "readonly": true, "target": "local"}, "target.device_type": {"label": "Model", "readonly": true, "target": "local"}, "g7opt.apc-hw-serialnumber": {"label": "APC Serial", "readonly": true, "target": "local"}, "g7opt.sdc-hw-serialnumber": {"label": "SDC Serial", "readonly": true, "target": "local"}, "g7opt.apc-printer-model": {"label": "APC Printer Model", "readonly": true, "target": "local"}, "cfg.aud.outchannel": {"label": "Audio output channel", "readonly": false, "target": "remote", "type": "string", "format": "string", "options": {"multi": true, "enum": ["Internal", "External"]}}, "cfg.prt-model": {"label": "Printer model", "readonly": false, "target": "startup", "type": "string", "format": "string", "options": {"enum": ["Auto-select", "Zebra", "Citizen/Axhiohm Internal", "Nippon 3”", "Nippon 2”", "<PERSON><PERSON><PERSON><PERSON>"]}}, "cfg.prt-margin-x": {"label": "Printer <PERSON><PERSON>", "readonly": false, "target": "remote", "type": "number", "format": "string", "options": {"minimum": 0.1, "step": 0.05, "maximum": 1.0}}, "cfg.prt-margin-y": {"label": "Printer <PERSON><PERSON>", "readonly": false, "target": "remote", "type": "number", "format": "string", "options": {"minimum": 0.1, "step": 0.05, "maximum": 1.0}}, "cfg.prt-cut-offset": {"label": "Printer Cut Offset", "readonly": false, "target": "remote", "type": "number", "format": "string", "options": {"minimum": 0.1, "step": 0.05, "maximum": 1.0}}}