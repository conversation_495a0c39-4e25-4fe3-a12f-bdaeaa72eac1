const moment = require('moment');

/**
 * Mapping 'references' from template-strings to 'database fields'
 * in fileupload-request.service.js -> createFileUploadRequest function
 * Used for creating dynamic queries for renaming zip filenames
 * and nested file paths.
 */

/* 
#common reference variables
  cattr#Merchant ID, furts, dtype, appid, uparam#sdtime, uparam#edtime

#template variables for zip file rename
  ****** payment dashboard page ******
  single device : action, dname, dser, dtid, sname, srefid, dpath, action,uparam

  ****** device page ******
  single device : dname, dser, dtid, sname, srefid, dpath
  single site : sname, srefid
  site tags : tags

#template variables for zip file path
  ****** payment dashboard page ******
  paymentDashboard : furid, sname, dname, dser, appid, dpath, action, furts,uparam, dtid
*/

const refDbFieldsMapping = {
  paymentDashboard: {
    furid: {
      defaultValue: ({ fileUploadRequestId }) => fileUploadRequestId || '',
      isDependentOnDb: false,
      isCustomQuery: false,
    },
    sname: {
      fieldName: 'name',
      tableName: 'site',
      whereField: 'site_id',
      reqBodyField: 'siteId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    dname: {
      fieldName: 'name',
      tableName: 'target',
      whereField: 'target_id',
      reqBodyField: 'deviceId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    dser: {
      fieldName: 'serial_number',
      tableName: 'target',
      whereField: 'target_id',
      reqBodyField: 'deviceId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    appid: {
      fieldName: 'application_id',
      tableName: 'device_files',
      whereField: 'id',
      reqBodyField: 'deviceFileId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    dpath: {
      fieldName: 'device_path',
      tableName: 'device_files',
      whereField: 'id',
      reqBodyField: 'deviceFileId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    action: {
      query: `SELECT
                jsonb_array_elements(display_properties) ::JSON ->> 'label' AS "action"
              FROM
                device_files df
              WHERE
                device_id = $1
                and df.id = $2
                and exists (
                SELECT
                  1
                FROM
                  jsonb_array_elements(display_properties) AS item
                WHERE
                  item ->> 'visibility' = 'payments/actions'
              )`,
      setQueryParams: ({ deviceId, deviceFileId }) => [deviceId, deviceFileId],
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: true,
    },
    furts: {
      fieldName: 'created',
      tableName: 'file_upload_request',
      whereField: 'id',
      reqBodyField: 'fileUploadRequestId',
      postProcessing: ({ resolvedValue, options }) => {
        const { format } = options;
        const resolvedDate = new Date(resolvedValue).toISOString();
        const result = moment(resolvedDate).format(format);
        return result;
      },
      defaultValue: options => {
        const { format, timezone } = options;
        return timezone
          ? moment.tz(timezone).format(format)
          : moment().format(format);
      },
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    uparam: {
      isCustomQuery: false,
      isDependentOnDb: false,
      defaultValue: (options, { reqBody, ref }) => {
        const { userParams = null } = reqBody;
        const [, userParamKey] = ref.split('#');
        if (userParams && userParams[userParamKey]) {
          return userParams[userParamKey] || '';
        }
        return '';
      },
    },
    dtid: {
      fieldName: 'fuel_position',
      tableName: 'target',
      whereField: 'target_id',
      reqBodyField: 'deviceId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
  },

  // Root level ref db mapping for fallback
  cattr: {
    'Merchant ID': {
      query: `select
                ad.attribute_definition_id ,
                ad.attribute_value as "defaultValue",
                caev.attribute_override_value as "value"
              from
                custom_attribute.attribute_definition ad
              inner join custom_attribute.custom_attribute_entity_values caev on
                ad.attribute_definition_id = caev.attribute_definition_id
              where
                ad.attribute_name = $1
                and ad.company_id = $2
                and caev.entity_id = $3;`,
      setQueryParams: ({ attributeName, companyId, siteId }) => [
        attributeName,
        companyId,
        siteId,
      ],
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: true,
    },
  },
  furts: {
    defaultValue: options => {
      const { format, timezone } = options;
      return timezone
        ? moment.tz(timezone).format(format)
        : moment().format(format);
    },
    isDependentOnDb: false,
  },
  dtype: {
    fieldName: 'device_type',
    tableName: 'target',
    whereField: 'target_id',
    reqBodyField: 'deviceId',
    defaultValue: () => '',
    isDependentOnDb: true,
    isCustomQuery: false,
  },
  appid: {
    fieldName: 'application_id',
    tableName: 'device_files',
    whereField: 'id',
    reqBodyField: 'deviceFileId',
    defaultValue: () => '',
    isDependentOnDb: true,
    isCustomQuery: false,
  },
  // For zip file rename,requested from device page or payment dashboard
  singleDevice: {
    dname: {
      fieldName: 'name',
      tableName: 'target',
      whereField: 'target_id',
      reqBodyField: 'deviceId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    dser: {
      fieldName: 'serial_number',
      tableName: 'target',
      whereField: 'target_id',
      reqBodyField: 'deviceId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    dtid: {
      fieldName: 'fuel_position',
      tableName: 'target',
      whereField: 'target_id',
      reqBodyField: 'deviceId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    sname: {
      fieldName: 'name',
      tableName: 'site',
      whereField: 'site_id',
      reqBodyField: 'siteId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    srefid: {
      fieldName: 'reference_id',
      tableName: 'site',
      whereField: 'site_id',
      reqBodyField: 'siteId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    dpath: {
      fieldName: 'device_path',
      tableName: 'device_files',
      whereField: 'id',
      reqBodyField: 'deviceFileId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    // For payment dashboard device file path
    action: {
      query: `SELECT
                jsonb_array_elements(display_properties) ::JSON ->> 'label' AS "action"
              FROM
                device_files df
              WHERE
                device_id = $1
                and df.id = $2
                and exists (
                SELECT
                  1
                FROM
                  jsonb_array_elements(display_properties) AS item
                WHERE
                  item ->> 'visibility' = 'payments/actions'
              )`,
      setQueryParams: ({ deviceId, deviceFileId }) => [deviceId, deviceFileId],
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: true,
    },
    uparam: {
      isCustomQuery: false,
      isDependentOnDb: false,
      defaultValue: (options, { reqBody, ref }) => {
        const { userParams = null } = reqBody;
        const [, userParamKey] = ref.split('#');
        if (userParams && userParams[userParamKey]) {
          return userParams[userParamKey] || '';
        }
        return '';
      },
    },
  },

  // For zip file rename,requested from device page - singleSite
  singleSite: {
    sname: {
      fieldName: 'name',
      tableName: 'site',
      whereField: 'site_id',
      reqBodyField: 'siteId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
    srefid: {
      fieldName: 'reference_id',
      tableName: 'site',
      whereField: 'site_id',
      reqBodyField: 'siteId',
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: false,
    },
  },

  // For zip file rename,requested from device page - siteTags
  siteTags: {
    tags: {
      query: `SELECT
                array_to_string(array_agg(t.name::text), ' ') AS tags
              FROM
                tag t
              WHERE
                t.id = ANY($1)`,
      setQueryParams: ({ siteTagIds }) => [siteTagIds],
      defaultValue: () => '',
      isDependentOnDb: true,
      isCustomQuery: true,
    },
  },
};

module.exports = {
  refDbFieldsMapping,
};
