{"type": "VerticalLayout", "elements": [{"type": "Group", "label": "Details", "elements": [{"type": "Control", "scope": "#/properties/cfg.net-terminal-id"}, {"type": "Control", "scope": "#/properties/target.serial_number"}, {"type": "Control", "scope": "#/properties/target.device_type"}]}, {"type": "Group", "label": "ICP Application", "elements": [{"type": "Control", "scope": "#/properties/client.invenco-icp-app-enabled"}, {"type": "Control", "scope": "#/properties/client.invenco-icp-peer-cast-mode"}], "rule": {"effect": "SHOW", "condition": {"type": "LEAF", "scope": "#properties/client.invenco-icp-app-enabled", "expectedValue": true}}}, {"type": "Group", "label": "Date & Time", "elements": [{"type": "Control", "scope": "#/properties/cfg.rtc-time-zone"}, {"type": "Control", "scope": "#/properties/cfg.rtc-date-time-local"}, {"type": "Control", "scope": "#/properties/cfg.net-ntp-pool"}, {"type": "Control", "scope": "#/properties/cfg.net-ntp-server"}]}, {"type": "Group", "label": "Manual IP Address", "elements": [{"type": "Control", "scope": "#/properties/cfg.net-ip-addr"}, {"type": "Control", "scope": "#/properties/cfg.net-netmask"}, {"type": "Control", "scope": "#/properties/cfg.net-gateway"}, {"type": "Control", "scope": "#/properties/cfg.net-dns"}], "rule": {"effect": "SHOW", "condition": {"type": "LEAF", "scope": "#properties/cfg.net-dhcp-enabled", "expectedValue": "Manual"}}}]}