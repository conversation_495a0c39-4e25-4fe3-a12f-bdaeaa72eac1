{
  "version": "01.00.0000",
  "type": "<%= type %>",
  "jobs": [
    {
      "type": "sys.certload",
      "destination": "invenco.system",
      "reporting": false,
      "id": 1,
      "label": "Certificates",
      "onSuccess": "continue",
      "onFail": "exitFail",
      "data": {
        "certificates": [
          <% certificates.forEach(function(cert, index) { %>
            {
              "hierarchy": <%= cert.hierarchy %>,
              "channel": "<%= cert.channel %>",
              "function": "<%= cert.function %>",
              "certs": <%= JSON.stringify(cert.certs) %>
            }<% if (index < certificates.length - 1) { %>,<% } %>
          <% }); %>
        ]
      }
    },
    {
      "type": "sys.reboot",
      "destination": "invenco.system",
      "reporting": false,
      "id": 2,
      "label": "Reboot",
      "onSuccess": "continue",
      "onFail": "exitFail",
      "data": {}
    }
  ]
}