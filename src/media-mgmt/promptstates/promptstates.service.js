const { get } = require('lodash');

const { promptstateRepo } = require('../imports.repo');
const { promptStates, defaultAttribs } = require('../prompts/prompts.types');
const errors = require('../errors');
const validator = require('./promptstates.validator');

/**
 * @typedef CreatePSOArgs
 * @property {string} promptId
 * @property {string} promptType
 * @property {string} code
 */
/**
 * @typedef User
 * @property {string} id
 * @property {string} email
 */
/**
 * @typedef DbPromptStateOverride
 */

module.exports = {
  /**
   * @param {string} promptsetId
   * @param {string} code
   * @returns {string}
   */
  async checkPromptStateCodeExists(promptsetId, code) {
    // eslint-disable-next-line no-return-await
    return await promptstateRepo.checkPromptStateCodeExists(promptsetId, code);
  },

  /**
   * @param {CreatePSOArgs} pso
   * @param {User} user
   * @returns {DbPromptStateOverride}
   */
  async createPromptStatesOverride(ovrs, user, connection) {
    ovrs.forEach(ovr => {
      validator.validateCreatePromptStateOverride(ovr, user);
    });

    const statesToAdd = ovrs.map(ovr => {
      const promptType = get(ovr, 'promptType', 'standard');

      const state = promptStates[promptType];
      if (!state) {
        throw new Error(errors.promptState.notFound);
      }

      const pso = {
        ...state,
        ...ovr,
      };

      return {
        promptId: pso.promptId,
        promptStateName: '',
        code: pso.code,
        description: pso.description,
        secure: pso.secure,
        numericInput: pso.numericInput,
        dynamicText: pso.dynamicText,
        softKeys: pso.softKeys,
        active: true,
        promptType: pso.promptType,
        allowVideo: pso.allowVideo,
        attribs: pso.attribs || defaultAttribs,
        width: pso.width,
        fontSize: pso.fontSize,
        created: new Date(),
        createdBy: user.sub,
        modified: new Date(),
        modifiedBy: user.sub,
      };
    });

    // eslint-disable-next-line no-return-await
    return await promptstateRepo.createPromptStatesOverride(
      statesToAdd,
      connection
    );
  },

  async deletePromptStateOverrideByPSLSId(
    promptSetId,
    promptSetLanguageSupportId,
    connection
  ) {
    // eslint-disable-next-line no-return-await
    return await promptstateRepo.deletePromptStateOverrideByPSLSId(
      promptSetId,
      promptSetLanguageSupportId,
      connection
    );
  },

  async getPromptStateByPrompt(prompt) {
    const promptState = get(prompt, 'promptState', null);

    const promptId = get(prompt, 'id', null);
    if (!promptId) {
      throw new Error(errors.prompt.notFound);
    }

    // new prompt + override has null state
    if (!promptState) {
      // eslint-disable-next-line no-return-await
      return await promptstateRepo.getPromptStateOverrideByPromptId(promptId);
    }

    // legacy prompt + override has override state
    const psoExists =
      await promptstateRepo.getPromptStateOverrideByPromptId(promptId);

    // return override for legacy prompt if it exists
    if (psoExists) {
      return psoExists;
    }

    // otherwise return legacy state
    // eslint-disable-next-line no-return-await
    return await promptstateRepo.getPromptStateById(promptState);
  },

  /**
   * @param {string} pstateOvrId
   * @returns {DbPromptStateOverride}
   */
  async getPromptstateOverrideById(pstateOvrId) {
    // eslint-disable-next-line no-return-await
    return await promptstateRepo.getPromptstateOverrideById(pstateOvrId);
  },

  async checkIfLegacyPrompt(prompt) {
    const pso = await promptstateRepo.getPromptStateOverrideByPromptId(
      prompt.id
    );
    return pso === null;
  },
};
