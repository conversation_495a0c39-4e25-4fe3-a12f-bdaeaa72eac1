const uuid = require('uuid/v4');

const { server } = require('../../../app');

module.exports = {
  checkPromptStateCodeExists: async (promptsetId, code) => {
    const query = `
            SELECT coalesce(pso.code,s.code) as code
            FROM prompt_set ps
            INNER JOIN prompt p
                on ps.id = p.prompt_set
            LEFT JOIN prompt_state s
                on p.prompt_state = s.id
            LEFT JOIN prompt_state_override pso
                on p.id = pso.prompt_id
            WHERE p.prompt_set = $1
            AND p.day_part IS NULL
            AND p.deleted IS FALSE
            AND LOWER(coalesce(pso.code, s.code)) = $2
        `;

    const promptstateResult = await server.db.read.rows(query, [
      promptsetId,
      code.toLowerCase(),
    ]);

    return promptstateResult.length;
  },

  createPromptStatesOverride: async (psos, connection) => {
    const prepareJson = psos.map(pso => ({
      prompt_state_ovr_id: uuid(),
      prompt_id: pso.promptId,
      prompt_state_name: pso.promptStateName,
      code: pso.code,
      description: pso.description,
      secure: pso.secure,
      numeric_input: pso.numericInput,
      dynamic_text: pso.dynamicText,
      soft_keys: pso.softKeys,
      active: pso.active,
      attribs: pso.attribs,
      prompt_type: pso.promptType,
      allow_video: pso.allowVideo,
      width: pso.width,
      font_size: pso.fontSize,
      created: pso.created,
      created_by: pso.createdBy,
      modified: pso.modified,
      modified_by: pso.modifiedBy,
    }));

    const query = `
            INSERT INTO prompt_state_override 
                SELECT * FROM jsonb_populate_recordset(NULL::prompt_state_override, $1::jsonb)
            RETURNING *;
        `;
    const psosResult = connection
      ? (await connection.execute(query, [JSON.stringify(prepareJson)])).rows
      : await server.db.write.execute(query, [JSON.stringify(prepareJson)]);
    return psosResult;
  },

  getPromptStateOverrideByPromptId: async promptId => {
    const query = 'SELECT * FROM prompt_state_override WHERE prompt_id = $1';
    const psoResult = await server.db.read.row(query, [promptId]);

    return psoResult;
  },

  getPromptStateById: async promptstateId => {
    const query = 'SELECT * FROM prompt_state WHERE id = $1';
    const pStateResult = await server.db.read.row(query, [promptstateId]);

    return pStateResult;
  },

  getPromptstateOverrideById: async pstateOvrId => {
    const query =
      'SELECT * FROM prompt_state_override WHERE prompt_state_ovr_id = $1';
    const pStateOvrResult = await server.db.read.row(query, [pstateOvrId]);

    return pStateOvrResult;
  },

  deletePromptStateOverrideByPSLSId: async (
    promptSetId,
    promptSetLanguageSupportId,
    connection
  ) =>
    // eslint-disable-next-line no-return-await
    await connection.execute(
      `
            UPDATE prompt_state_override SET active = false 
                WHERE prompt_id IN (SELECT id FROM prompt WHERE prompt_set = $1 AND prompt_set_language_support_id = $2)`,
      [promptSetId, promptSetLanguageSupportId]
    ),
};
