const { get } = require('lodash');

const errors = require('../errors');
const { promptTypes } = require('../prompts/prompts.types');

module.exports = {
  validateCreatePromptStateOverride: (pso, user) => {
    const createdBy = get(user, 'sub', null);
    if (!createdBy) {
      throw new Error(errors.user.notFound);
    }

    const promptId = get(pso, 'promptId', null);
    if (!promptId) {
      throw new Error(errors.prompt.notFound);
    }

    const promptType = get(pso, 'promptType', null);
    if (!promptTypes.includes(promptType)) {
      throw new Error(errors.prompt.invalidPromptType);
    }

    const code = get(pso, 'code', null);
    if (!code) {
      throw new Error(errors.promptState.codeNotFound);
    }
  },
};
