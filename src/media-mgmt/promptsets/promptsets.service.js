const { promptsetRepo } = require('../imports.repo');
const validator = require('./promptsets.validator');

/**
 * @typedef {DbPromptSet}
 */
/**
 * @typedef User
 * @property {Company} company
 * @property {string} id
 */
/**
 * @typedef {DbProductType}
 * @property {string} device_type
 * @property {number} screen_width
 * @property {number} screen_height
 */

module.exports = {
  /**
   * @param {string} promptsetId
   * @returns {DbPromptSet}
   */
  getPromptSetById: async promptsetId =>
    // eslint-disable-next-line no-return-await
    await promptsetRepo.getPromptSetById(promptsetId),

  /**
   * @param {PromptSet} promptset
   * @returns {DbProductType}
   */
  getPromptSetProductType: async promptset =>
    // eslint-disable-next-line no-return-await
    await promptsetRepo.getPromptSetProductType(promptset),

  /**
   * @param {PromptSet} promptset
   * @param {User} user
   */
  bumpPromptSetVersion: async (promptset, user) => {
    validator.validateBumpVersion(promptset, user);

    await promptsetRepo.updatePromptSetVersion(
      promptset.version,
      promptset.id,
      user.sub
    );
  },

  /**
   *
   * @param {Object} connection
   * @param {Array<UUID>} languageSupportIds
   */
  deletePromptSetLanguageSupportIds: async (connection, languageSupportIds) =>
    // eslint-disable-next-line no-return-await
    await promptsetRepo.deletePromptSetLanguageSupportIds(
      connection,
      languageSupportIds
    ),

  /**
   * Delete Prompt-Sets
   * @param {Array<UUID>} promptSetIds
   * @param {Object} connection
   */
  deletePromptSetByIds: async (promptSetIds, connection) =>
    // eslint-disable-next-line no-return-await
    await promptsetRepo.deletePromptSetByIds(promptSetIds, connection),

  /**
   * Return List of Prompt-Set that do not have any prompt.
   * @param {Array<UUID>} promptSetIds
   * @returns
   */
  getPromptSetWithNoPromptsByIds: async promptSetIds =>
    // eslint-disable-next-line no-return-await
    await promptsetRepo.getPromptSetWithNoPromptsByIds(promptSetIds),

  /**
   * Return List of Prompt-Set with given Language Support Id
   * @param {Array<UUID>} languageSupportIds
   * @returns
   */
  getPromptSetByLanguageSupportIds: async languageSupportIds =>
    // eslint-disable-next-line no-return-await
    await promptsetRepo.getPromptSetByLanguageSupportIds(languageSupportIds),

  setFallbackDefaultByPromptSetIds: async (promptSetIds, connection) =>
    // eslint-disable-next-line no-return-await
    await promptsetRepo.setFallbackDefaultByPromptSetIds(
      promptSetIds,
      connection
    ),
};
