const { get } = require('lodash');

const errors = require('../errors');

module.exports = {
  validateBumpVersion: (promptset, user) => {
    const promptSetId = get(promptset, 'id', null);
    if (!promptSetId) {
      throw new Error(errors.promptSet.notFound);
    }

    const version = get(promptset, 'version', null);
    if (!version) {
      throw new Error(errors.promptSet.versionNotFound);
    }

    const userId = get(user, 'sub', null);
    if (!userId) {
      throw new Error(errors.promptSet.versionNotFound);
    }
  },
};
