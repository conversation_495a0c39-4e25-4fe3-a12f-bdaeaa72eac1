const { server } = require('../../../app');

function bumpMinorVersion(version = '0.0.0') {
  return version
    .split('.')
    .map((number, index) => {
      if (index === 1) {
        // Increment minor
        return parseInt(number, 10) + 1;
      }
      if (index === 2) {
        // Reset patch
        return 0;
      }
      return number;
    })
    .join('.');
}

module.exports = {
  getPromptTypeById: async id => {
    const res = await server.db.read.row(
      `
    SELECT
      id,
      name,
      secure,
      dynamic_text,
      static_text,
      background_asset,
      background_color,
      font_color,
      font_size,
      video_asset,
      html_asset
    FROM prompt_type Where id=$1
  `,
      [id]
    );
    return res;
  },

  getPromptSetById: async promptsetId => {
    const query = 'SELECT * FROM prompt_set WHERE id = $1';
    const promptset = await server.db.read.row(query, [promptsetId]);

    return promptset;
  },

  getPromptSetProductType: async promptset => {
    const query =
      'SELECT device_type, screen_width, screen_height FROM product_type WHERE device_type = $1;';
    const productType = await server.db.read.row(query, [promptset.deviceType]);

    return productType;
  },

  setPromptTypeId: promptType => {
    switch (promptType) {
      case 'standard':
        return 1;
      case 'pin':
        return 3;
      case 'data':
        return 4;
      default:
        return -1;
    }
  },

  updatePromptSetVersion: async (promptsetVersion, promptsetId, userId) => {
    const bumpedVersion = bumpMinorVersion(promptsetVersion);

    const query = `
            UPDATE prompt_set SET
                version = $1,
                modified = NOW(),
                modified_by = $2
            WHERE id = $3
            RETURNING modified;
        `;

    await server.db.write.execute(query, [bumpedVersion, userId, promptsetId]);
  },

  deletePromptSetLanguageSupportIds: async (
    connection,
    promptSetLanguageSupportIds
  ) =>
    // eslint-disable-next-line no-return-await
    await connection.execute(
      `
        UPDATE prompt_set_language_support
            SET deleted = true
            WHERE prompt_set_language_support_id = ANY ($1);
    `,
      [promptSetLanguageSupportIds]
    ),

  deletePromptSetByIds: async (promptSetIds, connection) => {
    await connection.execute(
      `
            UPDATE prompt_set
            SET active = FALSE
            WHERE id = ANY($1);
    `,
      [promptSetIds]
    );

    await connection.execute(
      `
            UPDATE software
            SET active = FALSE
            WHERE related_entity = ANY($1);
    `,
      [promptSetIds]
    );

    await connection.execute(
      `
            UPDATE prompt_set_language_support 
            SET deleted = true 
            WHERE prompt_set_id = ANY($1);
    `,
      [promptSetIds]
    );
  },

  getPromptSetByLanguageSupportIds: async pslsIds =>
    // eslint-disable-next-line no-return-await
    await server.db.read.rows(
      `
            SELECT psls.prompt_set_id, psls.prompt_set_language_support_id
            FROM prompt_set_language_support psls
            WHERE psls.language_support_id = ANY ($1) AND psls.deleted = false;
        `,
      [pslsIds]
    ),

  getPromptSetWithNoPromptsByIds: async promptSetIds =>
    // eslint-disable-next-line no-return-await
    await server.db.read.rows(
      `
            WITH getPromptSetsWithNoPrompts AS (
                SELECT ps.id as prompt_set_id, count(p.id) as prompt_count
                FROM prompt_set ps
                LEFT JOIN prompt p on ps.id = p.prompt_set AND p.deleted = false
                WHERE ps.id = ANY($1)
                GROUP BY ps.id
            )
            SELECT gps.prompt_set_id as prompt_set_id FROM getPromptSetsWithNoPrompts gps WHERE gps.prompt_count = 0;
        `,
      [promptSetIds]
    ),

  setFallbackDefaultByPromptSetIds: async (promptSetIds, connection) => {
    await connection.execute(
      `
        UPDATE prompt_set_language_support SET "default" = true WHERE prompt_set_language_support_id = ANY(
            SELECT psls2.prompt_set_language_support_id as pslsId
            FROM prompt_set ps
                    LEFT JOIN prompt_set_language_support psls
                            on psls.prompt_set_language_support_id = (SELECT prompt_set_language_support_id
                                                                        FROM prompt_set_language_support
                                                                        WHERE prompt_set_id = ps.id
                                                                        AND deleted = false
                                                                        AND "default" = true
                                                                        LIMIT 1)
                    LEFT JOIN prompt_set_language_support psls2
                            on psls2.prompt_set_language_support_id = (SELECT prompt_set_language_support_id
                                                                        FROM prompt_set_language_support
                                                                        WHERE prompt_set_id = ps.id
                                                                        AND deleted = false
                                                                        LIMIT 1)
            WHERE ps.id = ANY ($1) 
            AND ps.active = true AND psls.prompt_set_language_support_id IS NULL
        );
    `,
      [promptSetIds]
    ).rows;
  },
};
