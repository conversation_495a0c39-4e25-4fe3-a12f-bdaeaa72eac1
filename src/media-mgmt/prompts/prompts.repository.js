const uuid = require('uuid');

const { server } = require('../../../app');

module.exports = {
  getPromptById: async (promptId, connection) => {
    const query = 'SELECT * FROM prompt WHERE id = $1';

    return connection
      ? (await connection.execute(query, [promptId])).rows
      : // eslint-disable-next-line no-return-await
        await server.db.read.row(query, [promptId]);
  },

  createPrompts: async (prompts, connection) => {
    const prepareJson = prompts.map(prompt => ({
      id: uuid(),
      device_type: prompt.deviceType,
      prompt_type: prompt.promptType,
      company: prompt.company,
      elements: prompt.elements,
      prompt_set: prompt.promptSet,
      prompt_state: prompt.promptState,
      transaction_state: prompt.transactionState,
      touchmap_id: prompt.touchmapId,
      day_part: prompt.dayPart,
      prompt_set_language_support_id: prompt.promptSetLanguageSupportId,
      screen_option: prompt.screenOption,
    }));

    const values = [];
    const params = prepareJson.map(item => {
      const paramLEngth = values.length;
      const idxStr = [];
      Object.values(item).forEach((v, i) => {
        idxStr.push(`$${i + paramLEngth + 1}`);
        values.push(v);
      });

      return `( ${idxStr.join(', ')} )`;
    });

    const query = `
            INSERT INTO prompt
            (
                id
                , device_type
                , prompt_type
                , company
                , elements
                , prompt_set
                , prompt_state
                , transaction_state
                , touchmap_id
                , day_part
                , prompt_set_language_support_id
                , screen_option
            ) 
            VALUES ${params.join(',')}
            RETURNING *;
        `;

    const promptResult = connection
      ? (await connection.execute(query, values)).rows
      : await server.db.write.execute(query, values);
    return promptResult;
  },

  deletePromptByPromptSetLanguageSupportIds: async (
    promptSetLanguageSupportIds,
    connection
  ) =>
    // eslint-disable-next-line no-return-await
    await connection.execute(
      `
            UPDATE prompt SET deleted = true 
                WHERE prompt_set_language_support_id = ANY ($1);`,
      [promptSetLanguageSupportIds]
    ),

  setPromptSetLanguageSupportIdByPromptSetId: async (
    promptSetId,
    promptSetLanguageSupportId,
    connection
  ) =>
    // eslint-disable-next-line no-return-await
    await connection.execute(
      `
            UPDATE prompt SET prompt_set_language_support_id = $1 
            WHERE prompt_set = $2 AND prompt_set_language_support_id is NULL AND deleted = false`,
      [promptSetLanguageSupportId, promptSetId]
    ),
};
