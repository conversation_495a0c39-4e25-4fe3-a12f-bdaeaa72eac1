const Joi = require('joi');

const restifyErrors = require('restify-errors');
const { server } = require('../../../app');
const env = require('../../../env');
const { requiresRole, validateBody } = require('../../../lib/pre');
const errors = require('../errors');
const promptHandler = require('./prompts.handler');

const promptsRoute = `${env.config.base}/media/prompts`;

/**
 * @swagger
 * /media/prompts:
 *     post:
 *       tags:
 *         - prompt
 *       summary: create prompt and state
 *       produces:
 *         - application/json xxx
 *       parameters:
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *         - description: The prompt values to save
 *           in: body
 *           name: prompt
 *           schema:
 *             properties:
 *               code:
 *                 description: Prompt state code
 *                 type: string
 *               description:
 *                 description: Prompt description
 *                 type: string
 *               promptType:
 *                 description: Prompt Type ['standard','pin','data']
 *                 type: string
 *               promptSetId:
 *                 description: Prompt-Set ID
 *                 type: string
 *       responses:
 *         200:
 *           description: Successful response
 *         401:
 *           $ref: '#/responses/authenticationFailed'
 *         500:
 *           $ref: '#/responses/internalServerError'
 */
server.post(
  {
    path: promptsRoute,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateBody({
      code: Joi.string()
        .regex(/^[a-zA-Z0-9-_]+$/)
        .required()
        .error(
          new restifyErrors.BadRequestError(errors.promptState.codeNotValid)
        ),
      description: Joi.string().required(),
      promptType: Joi.string().required(),
      promptSetId: Joi.string().guid().required(),
    }),
    promptHandler.createPrompt,
  ]
);
