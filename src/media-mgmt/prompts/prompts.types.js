// default prompt states by type
const standardState = {
  type: 'non-secure',
  numericInput: false,
  allowVideo: true,
  softKeys: false,
  touchMask: false,
  dynamicText: false,
  secure: false,
  width: 250,
  fontSize: 24,
  exampleText: 'Enter Message',
};

const pinState = {
  type: 'non-secure',
  numericInput: true,
  allowVideo: true,
  softKeys: false,
  touchMask: false,
  dynamicText: false,
  secure: false,
  width: 250,
  fontSize: 24,
};

const dataState = {
  type: 'secure',
  numericInput: true,
  allowVideo: true,
  softKeys: false,
  touchMask: false,
  dynamicText: false,
  secure: true,
  width: 250,
  fontSize: 24,
};

const promptStates = {
  standard: standardState,
  pin: pinState,
  data: dataState,
};

const promptTypes = ['pin', 'standard', 'data'];

const defaultAttribs = {
  'cless-msg-draw': false,
  'cless-event-msg': true,
};

module.exports = {
  promptStates,
  promptTypes,
  defaultAttribs,
};
