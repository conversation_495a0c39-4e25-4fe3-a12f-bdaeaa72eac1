const { get, isEmpty } = require('lodash');

const errors = require('../errors');
const { promptRepo, promptsetRepo } = require('../imports.repo');
const { config } = require('../../../env');
const { server } = require('../../../app');
const constants = require('../../../lib/app-constants');
const validator = require('./prompts.validator');

/**
 * @typedef DbPrompt
 */
/**
 * @typedef Prompt
 * @property {string} promptType
 * @property {string} promptSetId
 * @property {string} code
 * @property {string} description
 * @property {string} promptSetLanguageSupportId
 */
/**
 * @typedef Company
 * @property {number} id
 */
/**
 * @typedef User
 * @property {Company} company
 * @property {string} id
 */
/**
 * @typedef CreatePromptArgs
 * @property {Prompt} prompt
 * @property {User} user
 */

module.exports = {
  /**
   * @param {string} promptId
   * @returns {DbPrompt}
   */
  async getPromptById(promptId, connection) {
    const promptResult = await promptRepo.getPromptById(promptId, connection);

    return promptResult;
  },

  /**
   * Set Prompt-Set Language Support ID for Prompts without the ID (for Legacy Prompt-Set)
   * @param {UUID} promptSetId
   * @param {UUID} promptSetLanguageSupportId
   * @param {Object} connection
   */
  async setPromptSetLanguageSupportIdByPromptSetId(
    promptSetId,
    promptSetLanguageSupportId,
    connection
  ) {
    // eslint-disable-next-line no-return-await
    return await promptRepo.setPromptSetLanguageSupportIdByPromptSetId(
      promptSetId,
      promptSetLanguageSupportId,
      connection
    );
  },

  /**
   * Soft Delete Prompts by Prompt-Set Language Support ID
   * @param {UUID} languageSupportId
   * @param {Object} connection
   */
  async deletePromptByPromptSetLanguageSupportIds(
    promptSetLanguageSupportIds,
    connection
  ) {
    if (promptSetLanguageSupportIds.length <= 0) {
      return;
    }
    await promptRepo.deletePromptByPromptSetLanguageSupportIds(
      [promptSetLanguageSupportIds],
      connection
    );
  },

  /**
   * @param {CreatePromptArgs} promptRequest
   * @returns {DbPrompt}
   */
  async createPrompts(promptSetId, prompts, user, aws, connection) {
    prompts.forEach(prompt => {
      validator.validateCreatePrompt(prompt, user);
    });

    const promptset = await promptsetRepo.getPromptSetById(promptSetId);
    if (!promptset) {
      throw new Error(errors.promptSet.notFound);
    }

    const promptSetCompany = get(promptset, 'company', null);
    if (promptSetCompany !== user.company.id) {
      throw new Error(errors.user.accessDenied);
    }

    const deviceType = get(promptset, 'deviceType', null);
    if (!deviceType) {
      throw new Error(errors.deviceType.notFound);
    }

    const productType = await promptsetRepo.getPromptSetProductType(promptset);
    if (!productType) {
      throw new Error(errors.deviceType.notFound);
    }

    const promptsToAdd = prompts.map(prompt => ({
      deviceType,
      promptType: promptsetRepo.setPromptTypeId(prompt.promptType),
      company: user.company.id,
      elements: JSON.stringify(prompt.elements),
      promptSet: prompt.promptSet,
      promptState: get(prompt, 'promptState', null),
      transactionState: get(prompt, 'transactionState', 'idle'),
      touchmapId: prompt.touchmapId,
      dayPart: prompt.dayPart,
      promptSetLanguageSupportId: prompt.promptSetLanguageSupportId,
      screenOption: prompt.screenOption,
    }));

    const promptResult = await promptRepo.createPrompts(
      promptsToAdd,
      connection
    );
    if (!promptResult) {
      throw new Error(errors.prompts.createFailed);
    }

    await promptsetRepo.updatePromptSetVersion(
      promptset.version,
      promptset.id,
      user.sub,
      connection
    );

    // only main prompt thumbnail will be displayed
    if (aws) {
      const invokes = promptResult.map(prompt => {
        const lambdaParams = {
          FunctionName: config.media.promptThumbnailLambda,
          InvocationType: 'Event',
          Payload: JSON.stringify({
            promptId: prompt.id,
            promptSetId: promptset.id,
          }),
        };
        return aws.invokeLambda(lambdaParams);
      });
      await Promise.all(invokes);
    }

    return promptResult;
  },

  async renamePrompt(
    connection,
    promptState,
    promptSetId,
    promptId,
    screenOption,
    code,
    description,
    promptCodeExists
  ) {
    // eslint-disable-next-line no-prototype-builtins
    if (promptState.hasOwnProperty('id') || !isEmpty(promptState.id)) {
      const existingPromptStateDetails = await server.db.read.row(
        `SELECT code, description from prompt_state WHERE id=$1`,
        [promptState.id]
      );

      // Check if it is a default prompt
      if (
        existingPromptStateDetails &&
        (existingPromptStateDetails.code !== code ||
          existingPromptStateDetails.description !== description)
      )
        return {
          errorCode: 400,
          errorMsg: errors.prompt.renameDefaultPromptErr,
        };
    }

    // Code must not be empty
    if (code.trim() === '')
      return { errorCode: 400, errorMsg: errors.prompt.emptyPromptName };

    // Description must not be empty
    if (description.trim() === '')
      return { errorCode: 400, errorMsg: errors.prompt.emptyPromptDesc };

    // check for unique prompt name
    const existingPromptStateOvrDetails = await server.db.read.row(
      `SELECT code from prompt_state_override WHERE prompt_id=$1`,
      [promptId]
    );
    if (
      existingPromptStateOvrDetails &&
      existingPromptStateOvrDetails.code !== code
    ) {
      if (promptCodeExists)
        return { errorCode: 400, errorMsg: errors.promptState.stateDuplicate };
    }

    // Check for any other special character
    const pattern = /^[a-zA-Z0-9-_]*$/;
    if (!pattern.test(code))
      return { errorCode: 400, errorMsg: errors.prompt.noSpecialCharacter };

    // Check for aux prompt
    if (
      screenOption === constants.SCREEN_OPTION.AUX &&
      !code.startsWith(constants.AUX_PROMPT_NAME_PREFIX)
    )
      return { errorCode: 400, errorMsg: errors.prompt.promptNameError };

    await connection.execute(
      `
                  UPDATE prompt_state_override
                  SET code = $1,
                  description = $2
                  WHERE prompt_id = $3;
              `,
      [code, description, promptId]
    );
    return { errorCode: null };
  },
};
