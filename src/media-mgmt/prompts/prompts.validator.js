const { get } = require('lodash');

const errors = require('../errors');
const { promptTypes } = require('./prompts.types');

module.exports = {
  validateCreatePrompt: (prompt, user) => {
    const companyId = get(user, 'company.id', null);
    if (!companyId) {
      throw new Error(errors.user.companyNotFound);
    }

    const userId = get(user, 'sub', null);
    if (!userId) {
      throw new Error(errors.user.notFound);
    }

    const promptType = get(prompt, 'promptType', 'standard');
    if (!promptTypes.includes(promptType)) {
      throw new Error(errors.prompt.invalidPromptType);
    }

    const promptSetId = get(prompt, 'promptSet', null);
    if (!promptSetId) {
      throw new Error(errors.promptSet.notFound);
    }

    const elements = get(prompt, 'elements', null);
    if (!elements) {
      throw new Error(errors.promptState.elementsNotFound);
    }
  },

  validateCheckDaypartAssigned: (prompt, daypartId) => {
    const promptId = get(prompt, 'id', null);
    if (!promptId) {
      throw new Error(errors.prompt.notFound);
    }

    const promptSetId = get(prompt, 'promptSet', null);
    if (!promptSetId) {
      throw new Error(errors.promptSet.notFound);
    }

    if (!daypartId) {
      throw new Error(errors.daypart.notFound);
    }
  },
};
