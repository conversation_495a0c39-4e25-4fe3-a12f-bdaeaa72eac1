const { get } = require('lodash');
const restify = require('restify');
const { server } = require('../../../app');
const errorHandler = require('../../../lib/errorhandler');
const errors = require('../errors');
const {
  promptService,
  promptstateService,
  promptsetService,
} = require('../imports.service');
const promptsetRepository = require('../../../handlers/media/promptsets.repository');
const AWS = require('../../../lib/aws');
const promptsetHelper = require('../../../helpers/promptset-helper');
const promptsetServiceLegacy =
  require('../../../handlers/media/promptsets.service')(promptsetRepository);
const constants = require('../../../lib/app-constants');

const { promptStates, promptTypes } = require('./prompts.types');

async function createDefaultElements(promptType, promptSetId, screenOption) {
  const promptset = await promptsetService.getPromptSetById(promptSetId);

  const promptSetProfile = await promptsetHelper.getPromptSetProfileByName(
    promptset.promptSetProfileName
  );
  if (!promptSetProfile) {
    throw new Error('promptSetProfile not found');
  }
  // get main resolution screenwidth and screenheight
  if (screenOption === 'aux') {
    const auxResolution = promptSetProfile.auxResolutions.split('x');
    promptSetProfile.screenWidth = parseInt(auxResolution[0], 10);
    promptSetProfile.screenHeight = parseInt(auxResolution[1], 10);
  } else {
    const mainResolution = promptSetProfile.mainResolutions.split('x');
    promptSetProfile.screenWidth = parseInt(mainResolution[0], 10);
    promptSetProfile.screenHeight = parseInt(mainResolution[1], 10);
  }

  if (!promptTypes.includes(promptType)) {
    throw new Error(errors.prompt.invalidPromptType);
  }

  const productType = await promptsetService.getPromptSetProductType(promptset);
  if (!productType) {
    throw new Error(errors.deviceType.notFound);
  }

  const state = promptStates[promptType];
  if (!state) {
    throw new Error(errors.promptState.notFound);
  }

  const elements = promptsetHelper.createDefaultElements(
    state,
    {
      defaultBg: promptset.bg,
      defaultFontColor: promptset.fontColor,
    },
    promptSetProfile
  );

  if (!elements) {
    throw new Error(errors.promptState.elementsCreateFailed);
  }

  return elements;
}

module.exports = {
  createPrompt: async (req, res, next) => {
    try {
      const { user } = req;
      const { code, description, promptType, promptSetId } = req.body;
      let { screenOption } = req.body;

      // request validation
      // dual display screen validation
      if (screenOption === '') {
        return next(
          new restify.BadRequestError(errors.prompt.InvalidScreenOption)
        );
      }
      if (screenOption !== undefined && screenOption !== '') {
        // prompt name validation
        if (
          constants.SCREEN_OPTION.AUX === screenOption &&
          !code.startsWith(constants.AUX_PROMPT_NAME_PREFIX)
        ) {
          return next(
            new restify.BadRequestError(errors.prompt.promptNameError)
          );
        }

        // minimum character check
        if (
          constants.SCREEN_OPTION.AUX === screenOption &&
          code === constants.AUX_PROMPT_NAME_PREFIX
        ) {
          return next(
            new restify.BadRequestError(
              errors.prompt.promptNameMinimumCharacterError
            )
          );
        }

        // screenOption validation
        if (!constants.SUPPORTED_SCREEN_OPTIONS.includes(screenOption)) {
          return next(
            new restify.BadRequestError(errors.prompt.InvalidScreenOption)
          );
        }
      } else {
        // set default screenOption value as 'main' for single display screen
        screenOption = constants.SCREEN_OPTION.MAIN;
      }

      const companyId = get(user, 'company.id', null);
      if (!companyId) {
        return next(new restify.BadRequestError(errors.user.companyNotFound));
      }

      const userId = get(user, 'sub', null);
      if (!userId) {
        return next(new restify.BadRequestError(errors.user.notFound));
      }

      const promptSet = await promptsetService.getPromptSetById(promptSetId);
      if (!promptSet) {
        return next(new restify.BadRequestError(errors.promptSet.notFound));
      }

      const promptSetLanguages =
        await promptsetServiceLegacy.getPromptSetLanguageById(
          promptSetId,
          true
        );

      if (!promptTypes.includes(promptType)) {
        return next(
          new restify.BadRequestError(errors.prompt.invalidPromptType)
        );
      }

      // check uniqueness of code
      const promptCodeExists =
        await promptstateService.checkPromptStateCodeExists(promptSetId, code);
      if (promptCodeExists) {
        return next(
          new restify.BadRequestError(errors.promptState.stateDuplicate)
        );
      }

      const elements = await createDefaultElements(
        promptType,
        promptSetId,
        screenOption
      );

      const assignment = {
        prompts: [],
        states: [],
        exceptions: [],
      };

      const connection = await server.db.write.getConnection();
      try {
        await connection.execute('BEGIN');
        const languages =
          promptSetLanguages.length > 0 ? promptSetLanguages : [{}];
        const prompts = languages.map(language => {
          const { promptSetLanguageSupportId } = language;
          return {
            promptSet: promptSetId,
            promptType,
            elements,
            code, // to check corresponding promptstate/override is unique,
            promptSetLanguageSupportId,
            screenOption,
          };
        });

        const promptsCreated = await promptService.createPrompts(
          promptSetId,
          prompts,
          user,
          AWS,
          connection
        );
        const psos = promptsCreated.map((prompt, index) => {
          const promptLanguage = promptSetLanguages.find(
            psl =>
              psl.promptSetLanguageSupportId ===
              prompt.promptSetLanguageSupportId
          );
          delete promptsCreated[index].promptSetLanguageSupportId;

          assignment.prompts.push({
            ...prompt,
            ...{
              promptSetLanguageId: promptLanguage
                ? promptLanguage.languageSupportId
                : null,
            },
          });

          return {
            code,
            description,
            promptType,
            promptId: prompt.id,
          };
        });

        // create override state for prompt
        // note: all new prompts have an override state created to break prompt.prompt_state relationship
        const psosCreated = await promptstateService.createPromptStatesOverride(
          psos,
          user,
          connection
        );
        assignment.states.push(...psosCreated);
        if (!psosCreated || psosCreated.length <= 0) {
          throw new Error(errors.promptState.createFailed);
        }

        await connection.execute('COMMIT');
      } catch (err) {
        await connection.execute('ROLLBACK');
        throw err;
      } finally {
        connection.done();
      }

      res.send(assignment);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
