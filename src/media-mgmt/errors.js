const promptErrors = {
  notFound: 'Default prompt not found',
  unknownNotFound: 'Prompt not found', // cases we don't know if day-part or default
  invalidPromptType: 'Invalid prompt type',
  createFailed: 'Failed to create prompt',
  deleteFailed: 'Failed to delete prompt',
  promptNameError: `The name of an auxiliary prompt must start with the prefix 'aux-'.`,
  promptNameMinimumCharacterError: `Code must have atleast 1 character post 'aux-'.`,
  InvalidScreenOption: `Invalid screen option is provided.`,
  invalidPromptLinked: (mainType, auxType, mainPromptName) =>
    `Cannot link aux prompt of type "${auxType}" to a main prompt of type "${mainType}" for <b>${mainPromptName}</b>`,
  invalidPromptSelected: 'Selected prompt is not auxiliary',
  auxPromptNotFound: 'Provided auxiliary prompt does not exist',
  duplicateLinking: mainPromptName =>
    `Selected auxiliary prompt is already linked to ${mainPromptName}.`,
  emptyPromptName: 'Prompt name cannot be empty',
  noSpecialCharacter:
    'Prompt name must not include any special characters except hyphens (-) and underscores (_)',
  renameDefaultPromptErr:
    'Cannot change the name or description of a default prompt',
  deleteDefaultPromptErr: 'Cannot delete a default prompt',
  alreadyDeleted: 'This prompt has already been marked for deletion.',
  emptyPromptDesc: 'Prompt description cannot be empty',
};

const daypartErrors = {
  notFound: 'Daypart not found',
  daypartConflict: 'Daypart already assigned',
  deleteFailed: 'Failed to delete day-part',
};

const promptStateErrors = {
  notFound: 'State not found',
  elementsNotFound: 'Elements not found',
  elementsCreateFailed: 'Could not create default elements',
  codeConflict: 'Unique name required',
  codeNotFound: 'Code not found',
  codeNotValid: 'Code is invalid',
  createFailed: 'Failed to create prompt state',
  stateDuplicate: 'Unique name required',
};

const promptSetErrors = {
  notFound: 'Prompt Set not found',
  versionNotFound: 'Prompt Set version not found',
};

const deviceTypeErrors = {
  notFound: 'Device type not found',
  notSupported: 'Device type is not supported',
};

const userErrors = {
  notFound: 'User not found',
  emailNotFound: 'User email not found',
  companyNotFound: 'User company not found',
  accessDenied: 'User does not have access',
};

const errors = {
  prompt: promptErrors,
  daypart: daypartErrors,
  promptState: promptStateErrors,
  promptSet: promptSetErrors,
  deviceType: deviceTypeErrors,
  user: userErrors,
};

module.exports = errors;
