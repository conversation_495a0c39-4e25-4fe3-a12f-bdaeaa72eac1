/* eslint-disable no-underscore-dangle */
class AppFeatureFlag {
  constructor(db) {
    this.db = db;
    this.map = new Map();
    this.initialized = false;
  }

  async _initialize() {
    const results = await this.db.read.rows(
      `
            SELECT * FROM app_feature_flag;
        `,
      []
    );
    // eslint-disable-next-line no-restricted-syntax
    for (const featureFlag of results) {
      this.map.set(featureFlag.feature, featureFlag.flag);
    }
    this.initialized = true;
  }

  async getInstance() {
    if (!this.initialized) {
      await this._initialize();
    }
    return this;
  }

  verifyFeatureFlag(feature) {
    if (this.map.has(feature)) {
      const flag = this.map.get(feature);
      if (!flag) {
        return {
          isEnabled: false,
          code: 404,
          message: `${feature} is not enabled yet`,
        };
      }
    }
    return { isEnabled: true, code: 200 };
  }

  fetchAllFeatureFlags() {
    const featureFlags = {};
    this.map.forEach((value, key) => {
      featureFlags[key] = value;
    });
    return featureFlags;
  }
}

module.exports = AppFeatureFlag;
