const { server } = require('../../app');

/**
 * Get company languages.
 * @param {Array<UUID>} companyIds optional
 * @param {Array<UUID>} languageSupportIds optional
 * @returns list of company languages.
 */
const getCompanyLanguages = async (companyIds, languageSupportIds) => {
  const whereClause = [];
  const params = [companyIds, languageSupportIds].filter(Boolean);

  if (companyIds) {
    whereClause.push(`company_id = ANY($${whereClause.length + 1})`);
  }

  if (languageSupportIds) {
    whereClause.push(`language_support_id = ANY($${whereClause.length + 1})`);
  }

  // eslint-disable-next-line no-return-await
  return await server.db.read.rows(
    `
        SELECT language_support_id, company_id, language, iso_code, "default"
            FROM language_support 
        WHERE ${whereClause.join(' AND ')} AND deleted = false
`,
    [params]
  );
};

/**
 * Create Languages
 * @param {*} connection
 * @param {Array<LanguageObject>} languages
 * @returns list of created language
 */
const createCompanyLanguages = async (connection, languages) =>
  connection
    .execute(
      `
        INSERT INTO language_support
        SELECT * FROM jsonb_populate_recordset(NULL::language_support, $1::jsonb)
        RETURNING *;
    `,
      [
        JSON.stringify(
          languages.map(lang => ({
            language_support_id: lang.languageSupportId,
            company_id: lang.companyId,
            language: lang.language,
            iso_code: lang.isoCode,
            default: lang.default,
            deleted: false,
          }))
        ),
      ]
    )
    .then(result => result.rows);

/**
 * Get Company IDs
 * @param {Array<UUID>} companyIds
 * @returns list of Company Ids
 */
const getCompanyIds = async companyIds =>
  // eslint-disable-next-line no-return-await
  await server.db.read.rows(
    `
        SELECT id FROM company WHERE id = ANY($1);
    `,
    [companyIds]
  );

/**
 *
 * @param {*} connection
 * @param {Array<UUID>} languageSupportIds
 * @returns
 */
const deleteCompanyLanguages = async (connection, languageSupportIds) =>
  // eslint-disable-next-line no-return-await
  await connection.execute(
    `
        UPDATE language_support 
            SET deleted = true 
        WHERE language_support_id = ANY ( $1 )
    `,
    [languageSupportIds]
  );

module.exports = {
  getCompanyLanguages,
  createCompanyLanguages,
  deleteCompanyLanguages,
  getCompanyIds,
};
