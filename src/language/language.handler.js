const restify = require('restify');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const promptSetService = require('../media-mgmt/promptsets/promptsets.service');
const promptService = require('../media-mgmt/prompts/prompts.service');
const languageService = require('./language.service');

/**
 * Return list of languages for a company
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
const getCompanyLanguages = async (req, res, next) => {
  const userCompanyId = req.user.company.id;
  const { companyId } = req.params;

  if (userCompanyId !== companyId) {
    return next(new restify.errors.NotFoundError('Company not found.'));
  }

  try {
    const languages = await languageService.getCompanyLanguages(companyId);
    res.send({ data: languages });

    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

/**
 * Create a languageSupport entry for a company
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
const createCompanyLanguage = async (req, res, next) => {
  try {
    const languages = req.body;

    const companyIds = [...new Set(languages.map(i => i.companyId))];
    const existingCompanies =
      await languageService.checkCompanyExist(companyIds);

    // eslint-disable-next-line no-restricted-syntax
    for (const companyId of companyIds) {
      const isExist = existingCompanies.find(i => i.id === companyId);

      if (!isExist) {
        throw new restify.errors.BadRequestError(
          `Company ${companyId} does not exist.`
        );
      }
    }

    const companyLanguages = await languageService.getCompanyLanguages(
      companyIds,
      false
    );

    // Check Pre-Existing Default
    // eslint-disable-next-line no-restricted-syntax
    for (const lang of languages) {
      const langCompanyId = lang.companyId;
      const langIsoCode = lang.isoCode;

      const duplicatedCompanyLang =
        companyLanguages.filter(
          cl => cl.companyId === langCompanyId && cl.isoCode === langIsoCode
        ).length > 0;
      const duplicatedPaylodLang =
        languages.filter(
          x => x.companyId === langCompanyId && x.isoCode === langIsoCode
        ).length > 1;
      if (duplicatedCompanyLang || duplicatedPaylodLang) {
        throw new restify.errors.BadRequestError(
          `Company ${langCompanyId} has duplicated Language Locale: ${langIsoCode}`
        );
      }

      const existingCompanyDefault =
        companyLanguages.filter(
          cl => cl.companyId === langCompanyId && cl.default === true
        ).length > 0;
      const existingPayloadDefault =
        languages.filter(
          cl => cl.companyId === langCompanyId && cl.default === true
        ).length > 1;
      if (
        (existingCompanyDefault || existingPayloadDefault) &&
        lang.default === true
      ) {
        throw new restify.errors.BadRequestError(
          `Company ${langCompanyId} has more than one default Language Locale`
        );
      }
    }

    const createdLangs = await languageService.createLanguage(languages);
    res.send(createdLangs);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

/**
 * soft delete a languageSupport entry for a company
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
const disableCompanyLanguages = async (req, res, next) => {
  try {
    const newLangs = req.body;
    const languageSupportIds = newLangs.map(i => i.languageSupportId);
    const exitingLanguages = await languageService.getCompanyLanguages(
      null,
      languageSupportIds
    );
    // eslint-disable-next-line no-restricted-syntax
    for (const langId of languageSupportIds) {
      const exitingLang = exitingLanguages.find(
        i => i.languageSupportId === langId
      );
      if (!exitingLang) {
        throw new restify.errors.NotFoundError(
          `Language ${langId} does not exist.`
        );
      }

      if (exitingLang.default) {
        throw new restify.errors.BadRequestError(
          `Language ${langId} is Default and can not be disabled.`
        );
      }
    }

    const connection = await server.db.write.getConnection();

    try {
      // Get Affected Prompt-Sets
      const affectedPromptSets =
        await promptSetService.getPromptSetByLanguageSupportIds(
          languageSupportIds
        );
      const affectedPromptSetIds = affectedPromptSets.map(
        aps => aps.promptSetId
      );

      // Delete Prompts related to the language
      const pslsIds = affectedPromptSets.map(i => i.promptSetLanguageSupportId);
      await promptService.deletePromptByPromptSetLanguageSupportIds(
        pslsIds,
        connection
      );
      await promptSetService.deletePromptSetLanguageSupportIds(
        connection,
        pslsIds
      );

      // Delete Prompt-Set have no prompts
      const promptSetsWithNoPrompts =
        await promptSetService.getPromptSetWithNoPromptsByIds(
          affectedPromptSetIds,
          connection
        );
      const promptSetsWithNoPromptsIds = promptSetsWithNoPrompts.map(
        psw => psw.promptSetId
      );
      await promptSetService.deletePromptSetByIds(
        promptSetsWithNoPromptsIds,
        connection
      );

      // Set fallback default language for prompt-sets
      await promptSetService.setFallbackDefaultByPromptSetIds(
        affectedPromptSetIds,
        connection
      );

      // Delete Language Support Entry
      await languageService.deleteLanguage(languageSupportIds, connection);

      await connection.execute('COMMIT');
    } catch (error) {
      await connection.execute('ROLLBACK');
      throw error;
    } finally {
      connection.done();
    }
    res.send({ message: `Disabled ${newLangs.length} Language(s).` });
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

module.exports = {
  getCompanyLanguages,
  createCompanyLanguage,
  disableCompanyLanguages,
};
