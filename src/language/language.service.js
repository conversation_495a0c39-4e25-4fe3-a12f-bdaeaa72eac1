const uuid = require('uuid/v4');

const { server } = require('../../app');
const repository = require('./language.repository');

/**
 * Search companies by ID, and return in a list if exist
 * @param {Array<UUID>} companyIds
 * @returns list of existing companies Ids
 */
const checkCompanyExist = async companyIds =>
  // eslint-disable-next-line no-return-await
  await repository.getCompanyIds(companyIds);

/**
 *
 * @param {Array<UUID>} companyIds
 * @param {Array<UUID>} languageSupportIds
 */
const getCompanyLanguages = async (companyIds, languageSupportIds) =>
  // eslint-disable-next-line no-return-await
  await repository.getCompanyLanguages(companyIds, languageSupportIds);

/**
 *
 * @param {*} connection
 * @param {*} langs
 * @returns
 */
const createLanguage = async langs => {
  const connection = await server.db.write.getConnection();
  try {
    const newLanguages = langs.map(lang => ({
      ...lang,
      ...{ languageSupportId: uuid() },
    }));

    await connection.execute('BEGIN');
    const createdLangs = await repository.createCompanyLanguages(
      connection,
      newLanguages
    );
    await connection.execute('COMMIT');
    return createdLangs;
  } catch (err) {
    await connection.execute('ROLLBACK');
    throw err;
  } finally {
    connection.done();
  }
};

const deleteLanguage = async (languageSupportIds, connection) =>
  // eslint-disable-next-line no-return-await
  await repository.deleteCompanyLanguages(connection, languageSupportIds);

module.exports = {
  createLanguage,
  deleteLanguage,
  checkCompanyExist,
  getCompanyLanguages,
};
