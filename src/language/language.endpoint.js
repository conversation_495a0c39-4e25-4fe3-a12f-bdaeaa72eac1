const Joi = require('joi');

const { server } = require('../../app');
const { validateBody, requiresRole } = require('../../lib/pre');
const env = require('../../env');

const BASE_PATH = env.config.base;
const handler = require('./language.handler');

/**
 * Enable Company Language
 * Add a new Language to selected tenant(company)
 * @swagger
 *   "/companies/internal/languages":
 *     post:
 *       tags:
 *         - Language
 *       summary: "Enable Language For a Tenant"
 *       parameters:
 *         - in: body
 *           name: body
 *           schema:
 *             type: object
 *             properties:
 *               companyId:
 *                 type: string
 *                 description: Company UUID
 *               language:
 *                 type: string
 *                 description: Language Description (Alpha-Numeric)
 *               isoCode:
 *                 type: string
 *                 description: Language ISO 639-1 Code
 *               default:
 *                 type: boolean
 *                 description: Default Language Flag
 *       responses:
 *         "200":
 *           description: "Successful response"
 *           schema:
 *             type: array
 *             items:
 *               type: object
 *               properties:
 *                 languageSupportId:
 *                   type: string
 *                 companyId:
 *                   type: string
 *                 language:
 *                   type: string
 *                 isoCode:
 *                   type: string
 *                 default:
 *                   type: boolean
 *                 deleted:
 *                   type: boolean
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/companies/internal/languages`,
    version: '0.0.1',
  },
  [
    requiresRole(['SUPER_ADMIN']),
    validateBody(
      Joi.array()
        .min(1)
        .items({
          companyId: Joi.string().guid(),
          language: Joi.string().alphanum(),
          isoCode: Joi.string().min(1).max(2),
          default: Joi.boolean(),
        })
    ),
    handler.createCompanyLanguage,
  ]
);

/**
 * Delete Company Language
 * Soft Delete Language Support Entry
 * @swagger
 *  "/companies/internal/languages":
 *     delete:
 *       tags:
 *         - Language
 *       summary: "Disable language for selected companies"
 *       parameters:
 *         - in: body
 *           name: body
 *           schema:
 *             type: array
 *             items:
 *               type: object
 *               properties:
 *                 languageSupportId:
 *                   type: string
 *                   description: Company UUID
 *       responses:
 *         "200":
 *           description: "Successful response"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.del(
  {
    path: `${BASE_PATH}/companies/internal/languages`,
    version: '0.0.1',
  },
  [
    requiresRole(['SUPER_ADMIN']),
    validateBody(
      Joi.array().min(1).items({
        languageSupportId: Joi.string().guid(),
      })
    ),
    handler.disableCompanyLanguages,
  ]
);
