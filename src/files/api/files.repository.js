const { server } = require('../../../app');

/**
 * This function is required for POST: /devices/id/uploadFile so possible this needs to be put into
 * a helper or utility function?
 */
const retrieveFile = fileId => {
  const queryParams = [fileId];
  const file = server.db.read.row(
    `
        SELECT 
            file_url,
            file_name
        FROM file f 
        WHERE 
            f.id=$1;
    `,
    queryParams
  );
  return file;
};

module.exports = {
  retrieveFile,
};
