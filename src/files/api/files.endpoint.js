const Joi = require('joi');

const { server } = require('../../../app');
const env = require('../../../env');
const { requiresRole, validateParams } = require('../../../lib/pre');
const handler = require('./files.handler');

const BASE_PATH = `${env.config.base}/files`;
const VERSION = '1.0.0';

/**
 * @swagger
 *   "/files/{fileId}/content":
 *     get:
 *       tags:
 *         - files
 *       summary: "Get the content of the specified file."
 *       description: "This allows to retrieve the content of the specified file from a device/target"
 *       parameters:
 *         - $ref: '#/parameters/fileIdParam'
 *       responses:
 *         "200":
 *           description: "File content in response body"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "404":
 *           description: "File not found at specified path"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/:id/content`,
    version: VERSION,
  },
  [
    requiresRole(['DEVICE']),
    validateParams({
      id: Joi.string().guid().required().label('id is empty'),
    }),
    handler.getFileContent,
  ]
);
