const errors = require('restify-errors');

const stringUtils = require('../../../lib/string-utils');
const { config } = require('../../../env');

const getS3DownloadConfig = file => {
  const path = file.fileUrl;
  if (!path) {
    throw new errors.NotFoundError();
  }

  const s3Key = decodeURI(stringUtils.getPathName(path));
  const s3Bucket = config.fileUpload.bucket;
  if (!s3Bucket) {
    throw new errors.NotFoundError(
      'S3 bucket configuration for file upload not found'
    );
  }

  const { fileName } = file;
  const s3Config = {
    s3Bucket,
    s3Key,
    fileName,
  };

  return s3Config;
};

module.exports = {
  getS3DownloadConfig,
};
