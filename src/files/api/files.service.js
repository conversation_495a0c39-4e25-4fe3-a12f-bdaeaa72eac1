const errors = require('restify-errors');

const downloadHelper = require('../../../helpers/download-helper');
const { s3Error } = require('../../../lib/app-constants');
const repository = require('./files.repository');
const helper = require('./files.helper');

/**
 * Retrieves file from db and returns it
 * @param {UUID} fileId
 * @returns
 */
const getFile = async fileId => {
  const file = await repository.retrieveFile(fileId);
  if (!file) {
    throw new errors.NotFoundError(
      `File with ID: ${fileId} does not exist or not ready for download`
    );
  }
  return file;
};

/**
 * Calls extername downloadPackage function that downloads file and returns stream piped into response
 * Also handles internal service and s3 errors
 * @param {*} res
 * @param {Object} s3Config
 */
const downloadFromS3 = async (res, s3Config) => {
  try {
    await downloadHelper.downloadPackageFromS3(res, s3Config);
  } catch (error) {
    if (error.code === s3Error.NO_SUCH_KEY.CODE) {
      throw new errors.NotFoundError(s3Error.NO_SUCH_KEY.MESSAGE);
    } else if (error.code === s3Error.NO_SUCH_BUCKET.CODE) {
      throw new errors.NotFoundError(s3Error.NO_SUCH_BUCKET.MESSAGE);
    }
    throw new errors.InternalServerError('Could not download file content.');
  }
};

/**
 * Handles the function calls to retrieve file, build s3 configurations to download file from s3
 * @param {*} res
 * @param {UUID} fileId
 */
const fileContentRetrieval = async (res, fileId) => {
  const file = await getFile(fileId);
  const s3Config = helper.getS3DownloadConfig(file);
  await downloadFromS3(res, s3Config);
};

module.exports.public = {
  fileContentRetrieval,
};

module.exports.private = {
  downloadFromS3,
  getFile,
};
