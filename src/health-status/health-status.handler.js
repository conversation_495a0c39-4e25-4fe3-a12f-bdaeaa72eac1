/* eslint-disable max-len */
const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const deviceHelper = require('../../helpers/device-helper');
const appConstants = require('../../lib/app-constants');
const { MILLISECONDS_PER_WEEK } = require('./health-status.constant');

const prepareSelectSite = `
    SELECT s.site_id, s.created, health as site_health, dc.*
`;

const prepareSelectDevice = `
    SELECT dc.*
`;

const prepareFrom = `
    FROM site s
            INNER JOIN LATERAL get_site_health(s.site_id) health on TRUE
            inner join user_site_authorization usa on usa.site_id = s.site_id
            left join lateral (
        SELECT jsonb_object_agg(cc.health, cc.count)
            filter ( WHERE cc.health is not null AND cc.name is null AND cc.value is null ) health_counts,
            jsonb_agg(
            jsonb_build_object('name', cc.name, 'value', cc.value, 'count', cc.count,
                'devices', cc.devices))
            filter ( WHERE cc.health = 'OUT_OF_SERVICE' AND cc.name is not null AND cc.value is not null ) category_counts
        FROM (
                SELECT th as            health,
                        dh.name,
                        dh.value,
                        array_agg(DISTINCT target_id) devices,
                        count(DISTINCT target_id) count
                FROM target t,
                        lateral CAST((SELECT mdh.health from mv_device_health mdh WHERE mdh.target_id = t.target_id) AS TEXT) th
                        left join lateral (
                        SELECT h.name,
                                h.value
                        FROM ics_state._device_health h
                        WHERE h.device_id = t.target_id
                            AND th = 'OUT_OF_SERVICE'
                            and h.is_critical
                        ) dh on true
                WHERE t.active
                AND t.site_id = s.site_id
                group by grouping sets ( (1),
                                        (1, 2, 3)
                    )
            ) cc
        ) dc on true
    WHERE s.active
    AND s.visible AND usa.user_id = $1
`;

const getCountsPerSite = async userId => {
  const allSites = await server.db.read.rows(
    `${prepareSelectSite}${prepareFrom}`,
    [userId]
  );

  const siteStatus = {
    TOTAL: allSites.length,
    NORMAL: allSites.filter(x => x.siteHealth === 'NORMAL').length,
    WARNING: allSites.filter(x => x.siteHealth === 'WARNING').length,
    CRITICAL: allSites.filter(x => x.siteHealth === 'CRITICAL').length,
    UNKNOWN: allSites.filter(x => x.siteHealth === 'UNKNOWN').length,
    INACTIVE: allSites.filter(x => x.siteHealth === 'INACTIVE').length,
  };

  const dateNowTime = new Date().getTime();

  const siteIsRecent = site =>
    dateNowTime - site.created.getTime() < MILLISECONDS_PER_WEEK;

  const siteHasNoDevices = site => {
    if (!site.healthCounts) {
      return true;
    }

    const deviceCount = Object.values(site.healthCounts).reduce(
      (total, healthCount) => total + healthCount,
      0
    );

    return deviceCount === 0;
  };

  const siteEvents = {
    [appConstants.siteEvents.NEW_SITE]: allSites.filter(
      x => siteIsRecent(x) && siteHasNoDevices(x)
    ).length,
  };

  const getCountPerDeviceHealth = key => {
    let count = 0;
    allSites.forEach(item => {
      if (item.healthCounts && item.healthCounts[key]) {
        count += 1;
      }
    });
    return count;
  };

  const n = [];

  const getCountPerCategory = () => {
    const categories = {};
    const conditions = {};
    allSites.forEach(item => {
      if (item.categoryCounts && item.categoryCounts) {
        const { categoryCounts, siteId } = item;
        categoryCounts.forEach(cat => {
          const { name, value } = cat;
          const match = deviceHelper.getOOSCategoryAndCondition({
            name,
            value,
          });
          if (match) {
            categories[match.category] = (
              categories[match.category] || []
            ).concat([siteId]);
            conditions[match.condition] = (
              conditions[match.condition] || []
            ).concat([siteId]);
          }
        });
      }
    });

    // eslint-disable-next-line no-restricted-syntax
    for (const [key, value] of Object.entries(categories)) {
      categories[key] = [...new Set(value)].length;
    }

    // eslint-disable-next-line no-restricted-syntax
    for (const [key, value] of Object.entries(conditions)) {
      conditions[key] = [...new Set(value)].length;
    }

    return {
      categories,
      conditions,
      n,
    };
  };

  const deviceHealth = {
    OPERATIONAL: getCountPerDeviceHealth('OPERATIONAL'),
    OUT_OF_SERVICE: getCountPerDeviceHealth('OUT_OF_SERVICE'),
    UNKNOWN: getCountPerDeviceHealth('UNKNOWN'),
    INACTIVE: getCountPerDeviceHealth('INACTIVE'),
  };
  const concats = getCountPerCategory();
  const oosCategory = concats.categories;
  const oosCondition = concats.conditions;

  return {
    siteStatus,
    deviceHealth,
    siteEvents,
    oosCategory,
    oosCondition,
  };
};

const getCountsPerDevice = async userId => {
  const allSites = await server.db.read.rows(
    `${prepareSelectDevice}${prepareFrom}`,
    [userId]
  );

  const getCountPerDeviceHealth = key => {
    let count = 0;
    allSites.forEach(item => {
      if (item.healthCounts && item.healthCounts[key]) {
        count += item.healthCounts[key];
      }
    });
    return count;
  };

  const getCountPerCategory = () => {
    const categories = {};
    const conditions = {};
    allSites.forEach(item => {
      if (item.categoryCounts && item.categoryCounts) {
        const { categoryCounts } = item;
        categoryCounts.forEach(cat => {
          const { name, value, devices } = cat;
          const match = deviceHelper.getOOSCategoryAndCondition({
            name,
            value,
          });
          if (match) {
            categories[match.category] = (
              categories[match.category] || []
            ).concat(devices);
            conditions[match.condition] =
              (conditions[match.condition] || 0) + devices.length;
          }
        });
      }
    });

    // eslint-disable-next-line no-restricted-syntax
    for (const [key, value] of Object.entries(categories)) {
      // eslint-disable-next-line no-const-assign
      categories[key] = [...new Set(value)].length;
    }

    return {
      categories,
      conditions,
    };
  };

  const deviceHealth = {
    OPERATIONAL: getCountPerDeviceHealth('OPERATIONAL'),
    OUT_OF_SERVICE: getCountPerDeviceHealth('OUT_OF_SERVICE'),
    UNKNOWN: getCountPerDeviceHealth('UNKNOWN'),
    INACTIVE: getCountPerDeviceHealth('INACTIVE'),
  };
  const oosCategory = getCountPerCategory().categories;
  const oosCondition = getCountPerCategory().conditions;

  return {
    deviceHealth,
    oosCategory,
    oosCondition,
  };
};

module.exports = {
  getStatus: async (req, res, next) => {
    try {
      const userId = req.user.sub;
      const { type } = req.params;

      switch (type) {
        case 'SITE':
          res.send(await getCountsPerSite(userId));
          break;
        case 'DEVICE':
          res.send(await getCountsPerDevice(userId));
          break;
        default:
          break;
      }

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
