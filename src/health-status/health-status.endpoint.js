const Joi = require('joi');

const { server } = require('../../app');
const env = require('../../env');
const { validateQuery } = require('../../lib/pre');
const status = require('./health-status.handler');

const BASE_PATH = `${env.config.base}/health-status`;

/**
 * @swagger
 * /health-status:
 *     get:
 *       tags:
 *         - dashboard
 *       summary: sites/devices status for dashboard widgets
 *       description: Aggregates sites and devices data used for rendering the dashboard widgets
 *       parameters:
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *       responses:
 *        '200':
 *          description: Device successfully created
 *          schema:
 *            type: object
 *            properties:
 *              siteStatus:
 *                type: object
 *                description: Site health count per site based on the parameter. (Does not appear when type is device)
 *                properties:
 *                  TOTAL:
 *                    type: integer
 *                  NORMAL:
 *                    type: integer
 *                  WARNING:
 *                    type: integer
 *                  CRITICAL:
 *                    type: integer
 *                  UNKNOWN:
 *                    type: integer
 *                  INACTIVE:
 *                    type: integer
 *              deviceHealth:
 *                type: object
 *                description: Device health count per site or device based on the parameter.
 *                properties:
 *                  OPERATIONAL:
 *                    type: integer
 *                  OUT_OF_SERVICE:
 *                    type: integer
 *                  UNKNOWN:
 *                    type: integer
 *                  INACTIVE:
 *                    type: integer
 *              siteEvents:
 *                type: object
 *                description: Site event count per site based on the parameter. (Does not appear when type is device)
 *                properties:
 *                  NEW_SITE:
 *                    type: integer
 *              oosCategory:
 *                description: Device Out Of Service Category and count based on the parameter.
 *                type: object
 *              oosCondition:
 *                description: Device Out Of Service condition and count based on the parameter.
 *                type: object
 */
server.get(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    validateQuery({
      type: Joi.string().valid(['SITE', 'DEVICE']).required(),
    }),
    status.getStatus,
  ]
);
