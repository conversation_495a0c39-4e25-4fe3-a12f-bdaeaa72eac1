const restifyErrors = require('restify-errors');
const validateUUID = require('uuid-validate');

const {
  supportedEntityTypesInternalCode,
} = require('./custom-attributes.constant');
const {
  getAttributeValuesBySiteId,
  getSitesByEntityIds,
  getCustomAttributeDefinitionsByCompanyIdRepo,
  getCustomAttributeDefinitionsByIds,
  upsertCustomAttributeValuesEntries,
  upsertBulkCustomAttributeValuesEntries,
  deleteAndUpdateStagedValuesRepo,
  getCustomAttributeStagedStatusBySiteIdRepo,
  getSitesByEntityIdsAndIntegrationIdsRepo,
  getCustomAttributeDefinitionByNameRepo,
  getSitesByCustomAttributeIdAndValuesRepo,
  getStagedDuplicateSiteNameStatusBySiteIdRepo,
  getCustomAttributeValuesByTenantRepo,
} = require('./custom-attributes.repository');
const {
  objectKeysToCamelCase,
  sanitizeData,
  parseValue,
} = require('./custom-attributes.helper');

/**
 * Get custom attribute values
 * @param {string} companyId
 * @param {number} entityType
 * @param {string} entityId
 * @param {number} pageSize
 * @param {number} offset
 * @returns {Promise<{ totalCount: number, results: Array.<object> }>}
 * @param {boolean} isSanitizeData
 */
const getCustomAttributeValues = async (
  companyId,
  entityType,
  entityId,
  pageSize,
  offset,
  isSanitizeData
) => {
  const attributeDefinitions = await getAttributeValuesBySiteId(
    companyId,
    entityType,
    entityId,
    pageSize,
    offset
  );
  if (!isSanitizeData) {
    return {
      totalCount: attributeDefinitions.totalCount,
      results: attributeDefinitions.results
        ? attributeDefinitions.results
            .map(objectKeysToCamelCase)
            .map(parseValue)
        : [],
    };
  }
  // Using JSON_AGG in the query doesn't handle snake->camel casing like usual, so we are transforming it here
  return {
    totalCount: attributeDefinitions.totalCount,
    results: attributeDefinitions.results
      ? attributeDefinitions.results
          .map(objectKeysToCamelCase)
          .map(sanitizeData)
          .map(parseValue)
      : [],
  };
};

/**
 * Search entityId and integrationId
 * @description sites entity type ignore none-uuid entity Ids
 * @param {string} userId
 * @param {string} companyId
 * @param {Array<String>} entityIds
 * @param {Array<String>} integrationIds
 * @param {String} entityType tenants | sites | assets
 * @returns {Promise<Array<object>>}
 */
const getEntityIdsAndIntegrationIds = async (
  userId,
  companyId,
  entityIds = [],
  integrationIds = [],
  entityType = ''
) => {
  if (entityType === 'sites') {
    // eslint-disable-next-line no-return-await
    return await getSitesByEntityIdsAndIntegrationIdsRepo(
      userId,
      companyId,
      entityIds.filter(id => validateUUID(id)), // ignores none-uuid
      integrationIds
    );
  }

  throw new restifyErrors.NotImplementedError(
    `Entity type ${entityType} not implemented.`
  );
};

/**
 * Search entityId
 * @description sites entity type ignore none-uuid entity Ids
 * @param {string} userId
 * @param {string} companyId
 * @param {Array<String>} entityIds
 * @param {String} entityType tenants | sites | assets
 * @returns {Promise<Array<object>>}
 */
const getEntityIds = async (
  userId,
  companyId,
  entityIds = [],
  entityType = ''
) => {
  if (entityType === 'sites') {
    // eslint-disable-next-line no-return-await
    return await getSitesByEntityIds(
      userId,
      companyId,
      entityIds.filter(id => validateUUID(id)) // ignores none-uuid
    );
  }
  throw new restifyErrors.NotImplementedError(
    `Entity type ${entityType} not implemented.`
  );
};

/**
 * Search custom attribute definitions
 * @param {string} companyId
 * @param {string} entityType tenants | sites | assets
 * @param {number} pageSize
 * @param {number} offset
 * @returns {Promise<{ totalCount: number, results: Array.<object> }>} custom attribute definition list
 */
const getCustomAttributeDefinitionsByCompanyId = async (
  companyId,
  entityType,
  pageSize,
  offset
) => {
  const entityCode = supportedEntityTypesInternalCode[entityType];
  if (!entityCode) {
    throw new restifyErrors.NotImplementedError(
      `Entity type ${entityType} not implemented.`
    );
  }

  const attributeDefinitions =
    await getCustomAttributeDefinitionsByCompanyIdRepo(
      companyId,
      entityCode,
      pageSize,
      offset
    );

  // Using JSON_AGG in the query doesn't handle snake->camel casing like usual, so we are transforming it here
  return {
    totalCount: attributeDefinitions.totalCount,
    results: attributeDefinitions.results
      ? attributeDefinitions.results
          .map(objectKeysToCamelCase)
          .map(sanitizeData)
      : [],
  };
};

/**
 * Search custom attribute definitions
 * @param {string} companyId
 * @param {Array<string>} customAttributeDefIds
 * @param {string} entityType tenants | sites | assets
 * @returns {Promise<Array<object>>} custom attribute definition list
 */
const searchCustomAttributeDefinitions = async (
  companyId,
  customAttributeDefIds,
  entityType
) => {
  const entityCode = supportedEntityTypesInternalCode[entityType];
  if (!entityCode) {
    throw new restifyErrors.NotImplementedError(
      `Entity type ${entityType} not implemented.`
    );
  }

  // eslint-disable-next-line no-return-await
  return await getCustomAttributeDefinitionsByIds(
    companyId,
    customAttributeDefIds,
    entityCode
  );
};

/**
 * Update/Insert custom attribute values into the table
 * @description entityId must be the unique ID in the DB. e.g.) site entityId = site_id
 * @param {{ entityId: string; value: string; attributeDefinitionId: number; isStaged: boolean }[]} createOrUpdateItems
 * @param {string} userId
 */
const upsertCustomAttributeValues = async (createOrUpdateItems, userId) =>
  // eslint-disable-next-line no-return-await
  await upsertCustomAttributeValuesEntries(createOrUpdateItems, userId);

const upsertBulkCustomAttributeValues = async (siteIds, attributes, userId) =>
  // eslint-disable-next-line no-return-await
  await upsertBulkCustomAttributeValuesEntries(siteIds, attributes, userId);

/**
 * @param {string} entityType
 * @param {string} entityId
 * @param {string} userId
 */
const deleteAndUpdateStagedValues = async (entityType, entityId, userId) => {
  const entityCode = supportedEntityTypesInternalCode[entityType];
  if (!entityCode) {
    throw new restifyErrors.NotImplementedError(
      `Entity type ${entityType} not implemented.`
    );
  }
  // eslint-disable-next-line no-return-await
  return await deleteAndUpdateStagedValuesRepo(entityId, userId);
};

/**
 * Get the staged status for site transfer
 * @param {string} siteId
 * @param {string} userId
 * @returns {Promise<boolean>} isStaged
 */
const getCustomAttributeStagedStatusBySiteId = async (siteId, userId) =>
  // eslint-disable-next-line no-return-await
  await getCustomAttributeStagedStatusBySiteIdRepo(siteId, userId);

/**
 * Search sites by siteId or integrationId
 * @param {string} userId
 * @param {string} companyId
 * @param {Array<string>} entityIds
 * @param {Array<string>} integrationIds
 * @returns {Promise<Array<Object>>} site ids and integration ids for verification purpose
 */
const getSitesByEntityIdsAndIntegrationIds = async (
  userId,
  companyId,
  entityIds = [],
  integrationIds = []
) =>
  // eslint-disable-next-line no-return-await
  await getSitesByEntityIdsAndIntegrationIdsRepo(
    userId,
    companyId,
    entityIds,
    integrationIds
  );

/**
 * @param {string} companyId
 * @param {string[]} customAttributeNames
 * @param {number} entityTypeCode
 * @returns {Promise.<{
 *   attributeDefinitionId: number,
 *   attributeName: string,
 *   isUserEditable: boolean,
 *   schema: { [key: string]: unknown}
 * }[]>}
 */
const getCustomAttributeDefinitionByName = async (
  companyId,
  customAttributeNames,
  entityTypeCode
) =>
  // eslint-disable-next-line no-return-await
  await getCustomAttributeDefinitionByNameRepo(
    companyId,
    customAttributeNames,
    entityTypeCode
  );

/**
 * Search sites by custom attribute id and array of values
 * @param {string} userId
 * @param {string} companyId
 * @param {number} customAttributeId
 * @param {string[]} customAttributeValues
 * @returns {Promise.<Array<{entityId: string, entityKeyValue: string}>>}
 */
const getSitesByCustomAttributeIdAndValues = async (
  userId,
  companyId,
  customAttributeId,
  customAttributeValues = []
) =>
  // eslint-disable-next-line no-return-await
  await getSitesByCustomAttributeIdAndValuesRepo(
    userId,
    companyId,
    customAttributeId,
    customAttributeValues
  );

/**
 * Get the duplicate site name for site transfer
 * @param {string} siteId
 * @param {string} userId
 * @returns {Promise<boolean>} isDuplicate
 */
const getStagedDuplicateSiteNameStatusBySiteId = async (siteId, userId) => {
  const isStaged = await getCustomAttributeStagedStatusBySiteIdRepo(
    siteId,
    userId
  );
  if (isStaged) {
    // eslint-disable-next-line no-return-await
    return await getStagedDuplicateSiteNameStatusBySiteIdRepo(siteId);
  }
  return {
    isSuccess: false,
    warningMsg: 'The given site does not have any staged custom attributes',
  };
};

/**
 * Get the list of attribute values for a tenant
 * @param {string} companyId
 * @param {string} attribute
 * @param {number} pageSize
 * @param {number} offset
 * @param {string} searchText
 * @returns {Promise<{ totalCount: number, results: Array.<object> }>}
 */
const getCustomAttributeValuesByTenant = async (
  companyId,
  attribute,
  pageSize,
  offset,
  searchText
) => {
  const attributeValues = await getCustomAttributeValuesByTenantRepo(
    companyId,
    attribute,
    pageSize,
    offset,
    searchText
  );
  return {
    totalCount: attributeValues.totalCount,
    results: attributeValues.results
      ? attributeValues.results.map(objectKeysToCamelCase)
      : [],
  };
};

module.exports = {
  getCustomAttributeValues,
  getEntityIdsAndIntegrationIds,
  getEntityIds,
  getCustomAttributeDefinitionsByCompanyId,
  searchCustomAttributeDefinitions,
  upsertCustomAttributeValues,
  upsertBulkCustomAttributeValues,
  deleteAndUpdateStagedValues,
  getCustomAttributeStagedStatusBySiteId,
  getSitesByEntityIdsAndIntegrationIds,
  getCustomAttributeDefinitionByName,
  getSitesByCustomAttributeIdAndValues,
  getStagedDuplicateSiteNameStatusBySiteId,
  getCustomAttributeValuesByTenant,
};
