const restify = require('restify');
const restifyErrors = require('restify-errors');
const { Validator } = require('jsonschema');
const validateUUID = require('uuid-validate');
const { isEqual } = require('lodash');
const logger = require('../../lib/logger').mainLogger();
const { safeToString } = require('../../lib/utils');
const {
  customAttributeUpdateLimit,
  customAttributeValueLengthLimit,
  supportedEntityTypesText,
  stringTemplates,
  customAttributeBulkUpdateLimit,
  customAttributeImport,
} = require('./custom-attributes.constant');
const {
  formatResponseObject,
  formatEntityResponseObject,
  formatAttributeResponseObject,
  isNull,
} = require('./custom-attributes.helper');

/**
 * Check total number of attribute definition length in the payload
 * @param {Array<object>} updateAttribsPayload
 */
const validateAttributeUpdateLimit = updateAttribsPayload => {
  const countAttributes = updateAttribsPayload.reduce(
    (count, current) => count + current.attributes.length,
    0
  );
  if (countAttributes > customAttributeUpdateLimit) {
    throw new restifyErrors.BadRequestError(
      `Total number of attributes should not exceed ${customAttributeUpdateLimit}`
    );
  }
};

/**
 * Validate duplicate entries in an array
 * @param {String} key
 * @param {String} value
 * @param {object} metaContext
 * @returns {(key:string,value:string,arrayObj:Array<object>,metaContext:Array<object>) => void}
 */
const validateDuplicateProperties =
  (key, value, arrayObj = [], metaContext = {}) =>
  () => {
    if (!(key && value && arrayObj)) {
      return;
    }

    const hasDuplicate =
      arrayObj.flatMap(item => item[key]).filter(iKey => iKey && iKey === value)
        .length > 1;

    if (hasDuplicate) {
      throw new restifyErrors.BadRequestError({
        message: `${key} ${value} is duplicated.`,
        context: {
          skipNextValidation: true,
          skipSiblings: true,
          ...metaContext,
        },
      });
    }
  };

/**
 * @private
 * @param {any} value
 * @param {object} schema
 * @param {object} attribValidatorMeta
 * @returns
 */
const validateSchema = (value, schema, attribValidatorMeta = {}) => {
  if (!schema) {
    return;
  }

  // Remove quotes if validating against a string enum
  const validationResponse =
    schema.type === 'string' || schema.type === 'enum'
      ? new Validator().validate(value.replace(/^"(.+(?="$))"$/, '$1'), schema)
      : new Validator().validate(value, schema);

  if (validationResponse.errors.length) {
    const errors = validationResponse.errors.map(
      error => `value ${error.instance} ${error.message}`
    );
    const uniqueErrors = [...new Set(errors)];
    const hasMultipleErrors = uniqueErrors.length > 1;

    const validationError = hasMultipleErrors
      ? // @ts-ignore
        new restify.HttpError({
          statusCode: '400',
          body: {
            code: 'BadRequest',
            message: `Multiple errors found with value ${value}`,
            context: uniqueErrors,
          },
        })
      : new restifyErrors.BadRequestError({
          message: uniqueErrors[0],
          context: {
            skipNextValidation: true,
            skipSiblings: false,
            ...attribValidatorMeta,
          },
        });
    throw validationError;
  }
};

/**
 * Validate object array to contain certain keys
 * @param {Array<object>} attributeDefinitions
 * @param {object} value
 * @param {object} searchRuleObject
 * @param {String} prefixMessage
 * @param {object} attribValidatorMeta
 * @returns {(objectArray:Array<string>,searchRuleObject:object,prefixMessage:string,metaContext:object) => void}
 */
const validateBulkSchema =
  (
    attributeDefinitions,
    value,
    searchRuleObject,
    prefixMessage,
    attribValidatorMeta = {}
  ) =>
  () => {
    if (!(attributeDefinitions && searchRuleObject)) {
      return;
    }

    const attributeDefinition = attributeDefinitions.find(vsm => {
      // eslint-disable-next-line no-restricted-syntax
      for (const key of Object.keys(searchRuleObject)) {
        if (searchRuleObject[key] && vsm[key] === searchRuleObject[key]) {
          return true;
        }
      }

      return false;
    });

    if (!attributeDefinition) {
      throw new restifyErrors.NotFoundError({
        message: `${prefixMessage} doesn't exist`,
        context: {
          skipNextValidation: true,
          skipSiblings: false,
          ...attribValidatorMeta,
        },
      });
    }

    const { schema } = attributeDefinition;
    validateSchema(value, schema, attribValidatorMeta);
  };

/**
 * Validate object array to contain certain keys
 * @param {Array<object>} objectArray
 * @param {object} searchRuleobject
 * @param {String} prefixMessage
 * @param {object} metaContext
 * @returns {(objectArray:Array<string>,searchRuleObject:object,prefixMessage:string,metaContext:object) => void}
 */
const validateKeyValueExist =
  (objectArray, searchRuleobject, prefixMessage, metaContext = {}) =>
  () => {
    if (!(objectArray && searchRuleobject)) {
      return;
    }

    const isEntityExist = objectArray.find(vsm => {
      // eslint-disable-next-line no-restricted-syntax
      for (const key of Object.keys(searchRuleobject)) {
        if (searchRuleobject[key] && vsm[key] === searchRuleobject[key]) {
          return true;
        }
      }

      return false;
    });

    if (!isEntityExist) {
      throw new restifyErrors.NotFoundError({
        message: `${prefixMessage} doesn't exist`,
        context: {
          skipNextValidation: true,
          skipSiblings: false,
          ...metaContext,
        },
      });
    }
  };

/**
 * Validate attribute value
 * @param {any} attributeValue
 * @param {object} metaContext
 * @returns {(attributeValue:string,metaContext:object) => void}
 */
const validateAttributeValues =
  (attributeValue, metaContext = {}) =>
  () => {
    if (isNull(attributeValue)) {
      return;
    }

    const hasExceedLengthLimit =
      safeToString(attributeValue).length > customAttributeValueLengthLimit;
    if (hasExceedLengthLimit) {
      throw new restifyErrors.BadRequestError({
        message: `value has ${attributeValue.length} characters that exceed the ${customAttributeValueLengthLimit} maximum length`,
        context: {
          ...metaContext,
        },
      });
    }
  };

const validateEntityIds = (entityId, entityType, metaContext) => () => {
  if (entityType === 'sites') {
    if (!validateUUID(entityId)) {
      throw new restifyErrors.BadRequestError({
        message: `entityId '${entityId}' is invalid`,
        context: {
          ...metaContext,
          skipNextValidation: true,
        },
      });
    }

    return;
  }

  throw new restifyErrors.NotImplementedError(
    `Entity type ${entityType} not implemented.`
  );
};

/**
 * Validate request payload
 * @param {Array<object>} updateItems
 * @param {Array<object>} entityMeta
 * @param {Array<object>} attributeDefinitions
 * @param {string} entityType
 * @returns {Array<object>} validation result
 */
const validateCustomAttributeRequestItem = (
  updateItems,
  entityMeta,
  attributeDefinitions,
  entityType
) => {
  const responseArr = [];
  const validationFilter = [];

  // eslint-disable-next-line no-restricted-syntax
  for (const { entityId, integrationId, attributes } of updateItems) {
    let hasError = false;

    const validatorMeta = { entityId, integrationId };
    const validators = [
      // Entity Level Validator
      validateDuplicateProperties(
        'entityId',
        entityId,
        updateItems,
        validatorMeta
      ),
      validateDuplicateProperties(
        'integrationId',
        integrationId,
        updateItems,
        validatorMeta
      ),
      validateKeyValueExist(
        entityMeta,
        { entityId, integrationId },
        supportedEntityTypesText[entityType],
        validatorMeta
      ),

      // Attribute Level Validators
      ...attributes.flatMap(attribute => {
        const { attributeDefinitionId, value: attributeValue } = attribute;
        const attribValidatorMeta = { attributeDefinitionId, ...validatorMeta };
        return [
          validateDuplicateProperties(
            'attributeDefinitionId',
            attributeDefinitionId,
            attributes,
            attribValidatorMeta
          ),
          validateKeyValueExist(
            attributeDefinitions,
            { attributeDefinitionId },
            stringTemplates.attributeDefinition,
            attribValidatorMeta
          ),
          validateBulkSchema(
            attributeDefinitions,
            attributeValue,
            { attributeDefinitionId },
            stringTemplates.attributeDefinition,
            attribute
          ),
          validateAttributeValues(attributeValue, attribValidatorMeta),
        ];
      }),
    ];

    const shouldSkipValidation = validationFilter.find(
      filter => filter.entityId === entityId || filter.integrationId === filter
    );

    if (!shouldSkipValidation) {
      validators.every(validateFn => {
        try {
          validateFn();
        } catch (err) {
          hasError = true;
          const { message, statusCode, context } = err;
          if (!statusCode || statusCode === 500) {
            // bubbleup all unexpected errors
            throw err;
          }

          const {
            entityId: iEntityId,
            integrationId: iIntegrationId,
            attributeDefinitionId: iAttributeDefinitionId,
            skipNextValidation,
            skipSiblings,
          } = context || {};

          if (skipSiblings) {
            validationFilter.push(context);
          }

          responseArr.push(
            formatResponseObject(
              iEntityId,
              iIntegrationId,
              iAttributeDefinitionId,
              statusCode,
              [message]
            )
          );
          return !skipNextValidation;
        }

        return true;
      });

      if (!hasError) {
        responseArr.push(
          formatResponseObject(entityId, integrationId, null, 200, null)
        );
      }
    }
  }

  return responseArr;
};

/**
 * Check total number of attribute definition length in the payload
 * @param {Array<object>} entityIds
 * @param {Array<object>} attributes
 */
const validateBulkAttributeUpdateLimit = (entityIds, attributes) => {
  const countUpdates = entityIds.length * attributes.length;
  if (countUpdates > customAttributeBulkUpdateLimit) {
    throw new restifyErrors.BadRequestError(
      `Total number of updates should not exceed ${customAttributeBulkUpdateLimit}`
    );
  }
};

const validateBulkCustomAttributeRequestItem = (
  entityIds,
  attributes,
  entityMeta,
  attributeDefinitions,
  entityType
) => {
  const responseArr = [];
  const validationFilter = [];

  // eslint-disable-next-line no-restricted-syntax
  for (const entityId of entityIds) {
    const validators = [
      validateEntityIds(entityId, entityType, { entityId }),
      validateKeyValueExist(
        entityMeta,
        { entityId },
        supportedEntityTypesText[entityType],
        { entityId }
      ),
    ];

    validators.every(validateFn => {
      try {
        // @ts-ignore
        validateFn();
      } catch (err) {
        const { message, statusCode, context } = err;
        if (!statusCode || statusCode === 500) {
          // bubbleup all unexpected errors
          throw err;
        }

        const {
          entityId: iEntityId,
          skipNextValidation,
          skipSiblings,
        } = context || {};

        if (skipSiblings) {
          validationFilter.push(context);
        }

        responseArr.push(
          formatEntityResponseObject(iEntityId, statusCode, [message])
        );
        return !skipNextValidation;
      }

      return true;
    });
  }

  // eslint-disable-next-line no-restricted-syntax
  for (const attribute of attributes) {
    const { attributeDefinitionId, value } = attribute;

    const validators = [
      validateDuplicateProperties(
        'attributeDefinitionId',
        attributeDefinitionId,
        attributes,
        attribute
      ),
      validateKeyValueExist(
        attributeDefinitions,
        { attributeDefinitionId },
        stringTemplates.attributeDefinition,
        attribute
      ),
      validateBulkSchema(
        attributeDefinitions,
        value,
        { attributeDefinitionId },
        stringTemplates.attributeDefinition,
        attribute
      ),
      validateAttributeValues(value, attribute),
    ];

    const shouldSkipValidation = validationFilter.find(
      filter => filter.attributeDefinitionId === attributeDefinitionId
    );

    if (!shouldSkipValidation) {
      validators.every(validateFn => {
        try {
          // @ts-ignore
          validateFn();
        } catch (err) {
          const { message, statusCode, context } = err;
          if (!statusCode || statusCode === 500) {
            // bubbleup all unexpected errors
            throw err;
          }

          const {
            attributeDefinitionId: iAttributeDefinitionId,
            skipNextValidation,
            skipSiblings,
          } = context || {};

          if (skipSiblings) {
            validationFilter.push(context);
          }

          responseArr.push(
            formatAttributeResponseObject(iAttributeDefinitionId, statusCode, [
              message,
            ])
          );
          return !skipNextValidation;
        }

        return true;
      });
    }
  }

  return responseArr;
};

/**
 * Check Import Limit
 * @private
 * @param {Array<{ [key: string]: any }>} records
 */
const validateAttributeImportLimit = records => {
  const recordLength = records.length;
  if (recordLength > customAttributeImport.importRowLimit) {
    throw new restifyErrors.BadRequestError(
      `Total number of record should not exceed ${customAttributeImport.importRowLimit}`
    );
  }
};

/**
 * Check if key present in the record based on the allowedColumns
 * all columns in allowedColumns array must present in each record as a 'key'
 * @private
 * @param {Array<{ [key: string]: any }>} records
 * @param {string[]} allowedColumns
 */
const validateAttributeImportColumns = (records, allowedColumns) => {
  records.forEach(record => {
    const keys = Object.keys(record);

    if (!isEqual(keys, allowedColumns)) {
      throw new restifyErrors.BadRequestError(
        `Only allowing columns of [${allowedColumns.join(', ')}]`
      );
    }
  });
};

/**
 * Check if the record length is zero
 * @private
 * @param {Array<{ [key: string]: any }>} records
 */
const validateATtributeImportZeroRecord = records => {
  if (records.length <= 0) {
    throw new restifyErrors.BadRequestError(
      'The file provided is either incompatible or does not contain any data'
    );
  }
};

/**
 * Check if attribute definition exist based on the records key (or columns)
 * @private
 * @param {Array<{ [key: string]: any }>} records
 * @param {Array<{attributeDefinitionId: number, attributeName: string, schema?: any, isUserEditable: boolean}>} attributeDefinitions
 */
const validateAttributeImportDefinition = (records, attributeDefinitions) => {
  const columns = records.reduce(
    (acc, obj) => acc.concat(Object.keys(obj)),
    []
  );

  columns.forEach(colName => {
    const attribueDefExist = attributeDefinitions.filter(
      def => def.attributeName === colName
    );
    if (attribueDefExist.length <= 0) {
      throw new restifyErrors.BadRequestError(
        `Specified attribute definition (${colName}) not present in the system`
      );
    }
    if (attribueDefExist.length > 1) {
      throw new restifyErrors.BadRequestError(
        `Specified attribute definition (${colName}) present multiple times in the system`
      );
    }
  });
};

/**
 * @param {string} keyColumnName
 * @param {Array<object>} records
 * @param {Array<object>} attributeDefinitions
 */
const validateAttributeImportSchema = (
  keyColumnName,
  records,
  attributeDefinitions
) => {
  attributeDefinitions.forEach(attributeDefinition => {
    const { attributeName, schema } = attributeDefinition;
    if (attributeName === keyColumnName) {
      // Not storing key value, no need for validation
      return;
    }
    records.forEach(record => {
      const value = record[attributeName];
      validateSchema(value, schema);
    });
  });
};

/**
 * @private
 * @param {string} keyColumnName
 * @param {Array<{ [key: string]: any }>} records
 * @param {Array<{entityId: string; entityKeyValue: string;}>} sites
 */
const validateAttributeImportSites = (keyColumnName, records, sites) => {
  const errors = [];
  records.forEach((r, i) => {
    const keyValue = r[keyColumnName];
    const sipSecretKey = r['SIP Secret Key'].replace(/"/g, '');
    const matchSites = sites.filter(site => site.entityKeyValue === keyValue);
    if (matchSites.length <= 0) {
      errors.push({
        row_number: i + 1,
        merchant_id: keyValue,
        sip_secret_key: sipSecretKey,
        error_message: [
          {
            title: 'Invalid Merchant ID ',
            errorMsg: `Import of row ${i + 1} failed, ${keyColumnName}:${keyValue} - Invalid Merchant ID`,
          },
        ],
      });
    }
    if (matchSites.length > 1) {
      errors.push({
        row_number: i + 1,
        merchant_id: keyValue,
        sip_secret_key: sipSecretKey,
        error_message: [
          {
            title: 'Multiple sites associated with the Merchant ID',
            errorMsg: `Import of row ${i + 1} failed, ${keyColumnName}:${keyValue} - Multiple sites associated with the Merchant ID`,
          },
        ],
      });
    }
    if (records.filter(ir => ir[keyColumnName] === keyValue).length > 1) {
      const existingRowIdx = errors.findIndex(
        item => item.row_number === i + 1
      );
      if (existingRowIdx !== -1) {
        // If row exists, append error
        errors[existingRowIdx].error_message.push({
          title: 'Duplicate Merchant ID in the CSV file',
          errorMsg: `Import of row ${i + 1} failed, ${keyColumnName}:${keyValue} - Duplicate Merchant ID in the CSV file`,
        });
      } else {
        // If row doesn't exist, add new record
        errors.push({
          row_number: i + 1,
          merchant_id: keyValue,
          sip_secret_key: sipSecretKey,
          error_message: [
            {
              title: 'Duplicate Merchant ID in the CSV file',
              errorMsg: `Import of row ${i + 1} failed, ${keyColumnName}:${keyValue} - Duplicate Merchant ID in the CSV file`,
            },
          ],
        });
      }
    }
  });

  if (errors.length > 0) {
    const headers = [...Object.keys(records[0]), 'Error Message'];
    // @ts-ignore
    logger.info(`Error(s) found in the imported data, ${errors}`);
    throw new restify.HttpError({
      statusCode: '400',
      body: {
        code: 'BadRequest',
        message: 'Error(s) found in the imported data',
        context: { headers, errors },
      },
    });
  }
};

/**
 * Validate Import Custom Attribute
 * @param {Array<{ [key: string]: any }>} records
 * @param {Array<{attributeDefinitionId: number, attributeName: string, schema?: any, isUserEditable: boolean}>} attributeDefinitions
 * @param {string} keyColumnName
 * @param {string[]} mapColumns
 * @param {Array<{entityId: string; entityKeyValue: string;}>} lookUpSites
 * @returns
 */
const validateAttributeImportRequestItem = (
  records,
  attributeDefinitions,
  keyColumnName,
  mapColumns,
  lookUpSites
) =>
  Promise.all([
    validateAttributeImportColumns(records, mapColumns),
    validateAttributeImportLimit(records),
    validateATtributeImportZeroRecord(records),
    validateAttributeImportDefinition(records, attributeDefinitions),
    validateAttributeImportSchema(keyColumnName, records, attributeDefinitions),
    validateAttributeImportSites(keyColumnName, records, lookUpSites),
  ]);

module.exports = {
  validateAttributeUpdateLimit,
  validateCustomAttributeRequestItem,
  validateBulkAttributeUpdateLimit,
  validateBulkCustomAttributeRequestItem,
  validateAttributeImportRequestItem,
};
