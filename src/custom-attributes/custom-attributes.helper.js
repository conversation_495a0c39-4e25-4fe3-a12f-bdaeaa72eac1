const _ = require('lodash');

const { deepSanitize } = require('../../lib/utils');
/**
 * Generates array of response object
 * @param {string | null} entityId
 * @param {string | null} integrationId
 * @param {string | null} attributeDefinitionId
 * @param {number} status
 * @param {Array<string> | null} errors
 * @returns {object}
 */
const formatResponseObject = (
  entityId,
  integrationId,
  attributeDefinitionId,
  status,
  errors
) => ({
  ...(entityId ? { entityId } : null),
  ...(integrationId ? { integrationId } : null),
  ...(attributeDefinitionId ? { attributeDefinitionId } : null),
  status,
  ...(errors ? { errors } : null),
});
const formatEntityResponseObject = (entityId, status, errors) => ({
  ...(entityId ? { entityId } : null),
  status,
  ...(errors ? { errors } : null),
});

const formatAttributeResponseObject = (
  attributeDefinitionId,
  status,
  errors
) => ({
  ...(attributeDefinitionId ? { attributeDefinitionId } : null),
  status,
  ...(errors ? { errors } : null),
});

const objectKeysToCamelCase = obj => {
  const itemsNowWithCamelCaseKeys = Object.keys(obj).map(key => ({
    [_.camelCase(key)]: obj[key],
  }));
  return Object.assign({}, ...itemsNowWithCamelCaseKeys);
};

const isNull = value => value === undefined || value === null;

/**
 * @param {object} attributeDefinition
 * @returns {boolean}
 */
const isSecretAttribute = attributeDefinition =>
  !!attributeDefinition.isSecret && attributeDefinition.isSecret === true;

/**
 * @param {object} data
 * @returns {object}
 */
const sanitizeData = data => {
  if (!data) return undefined;
  const copy = _.cloneDeep(data);
  deepSanitize(copy, [], {
    value: isSecretAttribute,
    defaultValue: isSecretAttribute,
  });
  return copy;
};

const parseValue = data => {
  try {
    return {
      ...data,
      value: JSON.parse(data.value),
    };
  } catch {
    return data;
  }
};

/**
 * Transforms data into format stored in database
 * @param {any} value
 * @param {string} schemaType
 * @returns {any}
 */
const transformData = (value, schemaType) => {
  switch (schemaType) {
    case 'string':
      return `"${value || ''}"`;
    case 'number':
      return Number.isNaN(Number(value)) ? value : Number(value);
    case 'boolean':
      if (value.toLowerCase() === 'true' || value.toLowerCase() === 'false') {
        return value.toLowerCase() === 'true';
      }
      break;
    default:
  }
  return value;
};

/**
 * @param {Array<object>} attributes
 * @param {Array<object>} attributeDefinitions
 * @returns {Array<object>}
 */
const transformBulkData = (attributes, attributeDefinitions) => {
  if (!attributeDefinitions) {
    return attributes;
  }
  const schemas = new Map(
    attributeDefinitions.map(attribDef => [
      attribDef.attributeDefinitionId,
      attribDef.schema,
    ])
  );
  return attributes.map(({ value, ...rest }) => {
    const schema = schemas.get(rest.attributeDefinitionId);
    return !(_.isString(value) && schema)
      ? { ...rest, value }
      : { ...rest, value: transformData(value, schema.type) };
  });
};

/**
 * @param {Array<object>} records
 * @param {Array<object>} attributeDefinitions
 * @param {string} keyColumnName
 * @returns {Array<object>}
 */
const transformImportedData = (
  records,
  attributeDefinitions,
  keyColumnName
) => {
  if (!attributeDefinitions) {
    return records;
  }
  const schemas = new Map(
    attributeDefinitions.map(attribDef => [
      attribDef.attributeName,
      attribDef.schema,
    ])
  );
  return records.map(record =>
    Object.fromEntries(
      Object.entries(record).map(entry => {
        const [attribute, value] = entry;
        const schema = schemas.get(attribute);
        // Ignore key column values, these are for IDing sites and are not to be stored in DB
        return !(value && typeof value === 'string' && schema) ||
          attribute === keyColumnName
          ? entry
          : [attribute, transformData(value, schema.type)];
      })
    )
  );
};

const chunkArrayItems = (arr, size) =>
  Array.from({ length: Math.ceil(arr.length / size) }, (v, i) =>
    arr.slice(i * size, i * size + size)
  );

module.exports = {
  formatResponseObject,
  formatEntityResponseObject,
  formatAttributeResponseObject,
  objectKeysToCamelCase,
  isNull,
  sanitizeData,
  transformBulkData,
  transformImportedData,
  chunkArrayItems,
  parseValue,
};
