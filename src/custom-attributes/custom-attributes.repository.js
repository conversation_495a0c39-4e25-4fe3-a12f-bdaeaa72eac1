const { dbTransaction } = require('../../lib/app-constants');
const { server } = require('../../app');
const logger = require('../../lib/logger').mainLogger();
const {
  sendEventOnUpdateSite,
} = require('../../handlers/sites/sites-replication-events');

/**
 * Get Attribute Definitions for a given Company for a Entity Type/ID
 * @param {string} companyId
 * @param {number} entityType
 * @param {string} entityId
 * @param {number} pageSize
 * @param {number} offset
 * @returns {Promise<{ totalCount: number, results: Array.<object> }>} Attribute Definitions with site value overrides if they exist
 */
const getAttributeValuesBySiteId = async (
  companyId,
  entityType,
  entityId,
  pageSize,
  offset
) => {
  /** @type {Array<string | number | boolean | null>}  */
  const params = [companyId, entityType, entityId, pageSize, offset];

  // @ts-ignore
  // eslint-disable-next-line no-return-await
  return await server.db.read.row(
    `
        WITH allAttributesForCompany AS (
                SELECT ad.attribute_definition_id                                                          AS id,
                    COALESCE(caev.is_staged, false)                                                        AS is_staged,
                    ad.custom_attribute_group_id                                                           AS group_Id,
                    cap.attribute_group_name                                                               AS group_Name,
                    ad.attribute_name                                                                      AS name,
                    to_jsonb(COALESCE(caev.attribute_override_value, ad.attribute_value))                  AS value,
                    to_char(COALESCE(caev.created_date, ad.created_date), 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"') AS created_At,
                    to_char(caev.updated_date, 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')                            AS updated_At,
                    ad.is_secret                                                                           AS is_secret
            
                FROM custom_attribute.attribute_definition ad
                        INNER JOIN custom_attribute.custom_attribute_group cap
                                    ON ad.custom_attribute_group_id = cap.custom_attribute_group_id
                        LEFT JOIN custom_attribute.custom_attribute_entity_values caev
                                ON ad.attribute_definition_id = caev.attribute_definition_id
                                    AND caev.entity_id = $3
                                    AND caev.deleted = FALSE
                                    AND caev.is_staged = FALSE
                WHERE ad.company_id = $1
                AND ad.entity_type = $2
                AND ad.deleted = FALSE
            UNION
                SELECT ad.attribute_definition_id                                                              AS id,
                        caev.is_staged                                                                         AS is_staged,
                        ad.custom_attribute_group_id                                                           AS group_Id,
                        cap.attribute_group_name                                                               AS group_Name,
                        ad.attribute_name                                                                      AS name,
                        to_jsonb(COALESCE(caev.attribute_override_value, ad.attribute_value))                  AS value,
                        to_char(COALESCE(caev.created_date, ad.created_date), 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"') AS created_At,
                        to_char(caev.updated_date, 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')                            AS updated_At,
                        ad.is_secret                                                                           AS is_secret

                FROM custom_attribute.attribute_definition ad
                            INNER JOIN custom_attribute.custom_attribute_group cap
                                    ON ad.custom_attribute_group_id = cap.custom_attribute_group_id
                            INNER JOIN custom_attribute.custom_attribute_entity_values caev
                                    ON ad.attribute_definition_id = caev.attribute_definition_id
                                        AND caev.entity_id = $3
                                        AND caev.deleted = FALSE
                                        AND caev.is_staged = TRUE
                WHERE ad.company_id = $1
                AND ad.entity_type = $2
                AND ad.deleted = FALSE
            ORDER BY group_Name, name
        )
        SELECT (SELECT COUNT(1) FROM allAttributesForCompany)                                          AS total_count,
               (SELECT JSON_AGG(a) FROM (SELECT * FROM allAttributesForCompany LIMIT $4 OFFSET $5) AS a) AS results;
        `,
    params
  );
};

/**
 * Search sites by siteId or integrationId
 * @param {string} userId
 * @param {string} companyId
 * @param {Array<string>} entityIds
 * @param {Array<string>} integrationIds
 * @returns {Promise<Array<Object>>} site ids and integration ids for verification purpose
 */
const getSitesByEntityIdsAndIntegrationIdsRepo = async (
  userId,
  companyId,
  entityIds = [],
  integrationIds = []
) =>
  // @ts-ignore
  // eslint-disable-next-line no-return-await
  await server.db.read.rows(
    `
            SELECT s.site_id as entity_id, s.integration_id

            FROM site s
                    INNER JOIN user_site_authorization u ON u.site_id = s.site_id AND u.user_id = $1

            WHERE s.active = true AND company_id = $2
                AND ( s.site_id = ANY($3) OR s.integration_id = ANY($4) );
        `,
    [userId, companyId, entityIds, integrationIds]
  );
/**
 * Search sites by siteId
 * @param {string} userId
 * @param {string} companyId
 * @param {Array<string>} entityIds
 * @returns {Promise<Array<Object>>} site ids and integration ids for verification purpose
 */
const getSitesByEntityIds = async (userId, companyId, entityIds = []) =>
  // @ts-ignore
  // eslint-disable-next-line no-return-await
  await server.db.read.rows(
    `
            SELECT s.site_id as entity_id

            FROM site s
                    INNER JOIN user_site_authorization u ON u.site_id = s.site_id AND u.user_id = $1

            WHERE s.active = true 
                AND s.visible = true 
                AND s.company_id = $2
                AND ( s.site_id = ANY($3) );
        `,
    [userId, companyId, entityIds]
  );
/**
 * Search sites by custom attribute id and array of values
 * @param {string} userId
 * @param {string} companyId
 * @param {number} customAttributeId
 * @param {string[]} customAttributeValues
 * @returns {Promise.<Array<{entityId: string, entityKeyValue: string}>>}
 */
const getSitesByCustomAttributeIdAndValuesRepo = async (
  userId,
  companyId,
  customAttributeId,
  customAttributeValues = []
) =>
  // @ts-ignore
  // eslint-disable-next-line no-return-await
  await server.db.read.rows(
    `
            SELECT s.site_id as entity_id, caev.attribute_override_value as entity_key_value

            FROM site s
                    INNER JOIN user_site_authorization u ON u.site_id = s.site_id AND u.user_id = $1
                    INNER JOIN custom_attribute.custom_attribute_entity_values caev ON caev.entity_id = s.site_id
            WHERE s.active = true
                AND s.visible = true
                AND s.company_id = $2
                AND caev.deleted = FALSE
                AND caev.attribute_definition_id = $3
                AND caev.attribute_override_value = ANY($4)
    `,
    [userId, companyId, customAttributeId, customAttributeValues]
  );
/**
 * Get custom attribute definitions
 * @param {string} companyId
 * @param {number} entityTypeCode 1=Tenant 2=Site 3=Asset
 * @param {number} pageSize
 * @param {number} offset
 * @returns {Promise<{ totalCount: number, results: Array.<object> }>}
 */
const getCustomAttributeDefinitionsByCompanyIdRepo = async (
  companyId,
  entityTypeCode,
  pageSize,
  offset
) =>
  // @ts-ignore
  // eslint-disable-next-line no-return-await
  await server.db.read.row(
    `
        WITH allAttributesForCompany AS
                 (SELECT ad.attribute_definition_id                                AS id,
                         ad.is_third_party                                         AS is_third_party,
                         ad.searchable_attribute                                   AS is_searchable,
                         ad.custom_attribute_group_id                              AS group_id,
                         cap.attribute_group_name                                  AS group_name,
                         ad.attribute_name                                         AS name,
                         ad.attribute_value::jsonb                                 AS default_value,
                         ad.schema::TEXT                                           AS schema,
                         TO_CHAR(ad.created_date, 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"') AS created_at,
                         TO_CHAR(ad.updated_date, 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"') AS updated_at,
                         ad.is_secret                                              AS is_secret,
                         ad.is_user_editable                                       AS is_user_editable
                  FROM custom_attribute.attribute_definition ad
                           INNER JOIN custom_attribute.custom_attribute_group cap
                                      ON ad.custom_attribute_group_id = cap.custom_attribute_group_id
                  WHERE ad.company_id = $1
                    AND ad.entity_type = $2
                    AND ad.deleted = FALSE)
        SELECT (SELECT COUNT(1) FROM allAttributesForCompany)                                            AS total_count,
               (SELECT JSON_AGG(a) FROM (SELECT * FROM allAttributesForCompany LIMIT $3 OFFSET $4) AS a) AS results;
    `,
    [companyId, entityTypeCode, pageSize, offset]
  );
/**
 * Search custom attribute definitions
 * @param {string} companyId
 * @param {Array<object>} customAttributeDefIds
 * @param {number} entityTypeCode 1=Tenant 2=Site 3=Asset
 * @returns {Promise<Array<object>>}
 */
const getCustomAttributeDefinitionsByIds = async (
  companyId,
  customAttributeDefIds,
  entityTypeCode
) =>
  // @ts-ignore
  // eslint-disable-next-line no-return-await
  await server.db.read.rows(
    `
      SELECT attribute_definition_id, attribute_name, schema
      
      FROM custom_attribute.attribute_definition 
      
      WHERE attribute_definition_id = ANY($1) 
        AND deleted = false
        AND company_id = $2
        AND entity_type = $3
    `,
    [customAttributeDefIds, companyId, entityTypeCode]
  );
/**
 * @param {string} companyId
 * @param {string[]} customAttributeNames
 * @param {number} entityTypeCode
 * @returns {Promise.<{
 *   attributeDefinitionId: number,
 *   attributeName: string,
 *   isUserEditable: boolean,
 *   schema: { [key: string]: unknown}
 * }[]>}
 */
const getCustomAttributeDefinitionByNameRepo = async (
  companyId,
  customAttributeNames,
  entityTypeCode
) =>
  // @ts-ignore
  // eslint-disable-next-line no-return-await
  await server.db.read.rows(
    `
      SELECT attribute_definition_id, attribute_name, schema, is_user_editable
      
      FROM custom_attribute.attribute_definition 
      
      WHERE attribute_name = ANY($1) 
        AND deleted = false
        AND company_id = $2
        AND entity_type = $3
    `,
    [customAttributeNames, companyId, entityTypeCode]
  );
/**
 * Update custom attribute values
 * @param {{ entityId: string; value: string; attributeDefinitionId: number; isStaged: boolean }[]} records
 * @param {string} userId
 * @return {Promise<void>}
 */
const updateCustomAttributeValuesEntries = async (
  records,
  userId,
  connection
) => {
  // @ts-ignore
  const write = connection || server.db.write;

  // eslint-disable-next-line no-return-await
  return await write.execute(
    `
        UPDATE custom_attribute.custom_attribute_entity_values AS cae

        SET updated_by = $1, 
            updated_date = NOW(),
            attribute_override_value = inputValue.attribute_override_value,
            is_staged = inputValue.is_staged

        FROM (
                SELECT * FROM jsonb_populate_recordset(null::custom_attribute.custom_attribute_entity_values, $2::jsonb)
            ) as inputValue

        WHERE cae.attribute_definition_id = inputValue.attribute_definition_id
            AND cae.entity_id = inputValue.entity_id
            AND cae.attribute_override_value != inputValue.attribute_override_value
            AND cae.is_staged = inputValue.is_staged
            AND cae.deleted = FALSE
    `,
    [
      userId,
      JSON.stringify(
        records.map(({ entityId, attributeDefinitionId, value, isStaged }) => ({
          entity_id: entityId,
          attribute_override_value: value,
          attribute_definition_id: attributeDefinitionId,
          is_staged: isStaged,
        }))
      ),
    ]
  );
};

/**
 * Insert into custom attributes values table
 * @param {{ entityId: string; value: string; attributeDefinitionId: number; isStaged: boolean }[]} records
 * @param {string} userId
 * @param {object} connection @see DBClient class
 * @param {boolean} skipIfDuplicate
 * @return {Promise<void>}
 */
const insertCustomAttributeValuesEntries = async (
  records,
  userId,
  connection,
  skipIfDuplicate = true
) => {
  // @ts-ignore
  const write = connection || server.db.write;

  // eslint-disable-next-line no-return-await
  return await write.execute(
    `
        INSERT INTO custom_attribute.custom_attribute_entity_values(
                entity_id,
                attribute_definition_id,
                attribute_override_value,
                created_by,
                created_date,
                updated_by,
                updated_date,
                is_staged
            )
            
            SELECT entity_id,attribute_definition_id,attribute_override_value,created_by,created_date,updated_by,updated_date,is_staged
            
            FROM jsonb_populate_recordset(NULL::custom_attribute.custom_attribute_entity_values, $1::jsonb) as inputValue

            ${
              skipIfDuplicate
                ? `
                -- DO NOT INSERT WHEN A VALUE IS ALREADY STAGE
                WHERE NOT EXISTS (
                    SELECT 1 FROM custom_attribute.custom_attribute_entity_values aev
                    WHERE aev.entity_id = inputValue.entity_id 
                        AND aev.attribute_definition_id = inputValue.attribute_definition_id
                        AND aev.is_staged = inputValue.is_staged
                        AND aev.deleted = FALSE
                )
                -- DO NOT INSERT WHEN THE INCOMING VALUE IS SAME AS THE CURRENT VALUE
                AND NOT EXISTS (
                    SELECT 1 FROM custom_attribute.custom_attribute_entity_values aev
                    WHERE aev.entity_id = inputValue.entity_id
                      AND aev.attribute_definition_id = inputValue.attribute_definition_id
                      AND aev.attribute_override_value = inputValue.attribute_override_value
                      AND aev.deleted = FALSE
                );
                `
                : null
            }
    `,
    [
      JSON.stringify(
        records.map(({ entityId, attributeDefinitionId, value, isStaged }) => ({
          entity_id: entityId,
          attribute_definition_id: attributeDefinitionId,
          attribute_override_value: value,
          created_by: userId,
          created_date: new Date(),
          updated_by: userId,
          updated_date: new Date(),
          is_staged: isStaged,
        }))
      ),
    ]
  );
};

/**
 * Upsert into custom attributes values table
 * @param {{ entityId: string; value: string; attributeDefinitionId: number; isStaged: boolean }[]} records
 * @param {string} userId
 */
const upsertCustomAttributeValuesEntries = async (records, userId) => {
  // @ts-ignore
  const connection = await server.db.write.getConnection();
  try {
    await connection.execute('BEGIN');
    await updateCustomAttributeValuesEntries(records, userId, connection);
    await insertCustomAttributeValuesEntries(records, userId, connection);
    await connection.execute('COMMIT');
  } catch (err) {
    logger.error('Failed upserting custom attributes', err);
    await connection.execute('ROLLBACK');
    throw err;
  } finally {
    connection.done();
  }
};

const upsertBulkCustomAttributeValuesEntries = async (
  siteIds,
  attributes,
  userId
) => {
  // @ts-ignore
  const connection = await server.db.write.getConnection();
  try {
    const mappedAttributes = attributes.map(
      ({ attributeDefinitionId, value }) => ({
        attribute_definition_id: attributeDefinitionId,
        attribute_override_value: value,
      })
    );

    const attributeStrings = JSON.stringify(mappedAttributes);
    const siteStrings = JSON.stringify(siteIds);

    await connection.execute(
      `
                WITH updated AS (
                    UPDATE custom_attribute.custom_attribute_entity_values caev
                        SET attribute_override_value = x.attribute_override_value, updated_by = $3, updated_date = now()
                    FROM jsonb_to_recordset($1) as x(attribute_definition_id int, attribute_override_value varchar(1000))
                    WHERE NOT caev.is_staged AND caev.attribute_definition_id = x.attribute_definition_id AND caev.entity_id in (SELECT siteId::uuid FROM jsonb_array_elements_text($2) siteId)
                    RETURNING caev.entity_id as entity_id, caev.attribute_definition_id as attribute_definition_id
                )
                INSERT INTO custom_attribute.custom_attribute_entity_values(attribute_definition_id, attribute_override_value, entity_id, created_by, created_date, updated_date, updated_by)
                SELECT x.attribute_definition_id, x.attribute_override_value, d.siteId as entity_id, $3 as created_by, now() as created_date, now() as updated_date, $3 as updated_by
                FROM jsonb_to_recordset($1) as x(attribute_definition_id int, attribute_override_value varchar(1000))
                LEFT JOIN LATERAL(SELECT siteId::uuid FROM jsonb_array_elements_text($2) siteId) as d ON TRUE
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM updated u
                    WHERE u.entity_id = d.siteId AND u.attribute_definition_id = x.attribute_definition_id
                );
        `,
      [attributeStrings, siteStrings, userId]
    );
  } catch (err) {
    logger.error('Failed bulk upserting custom attributes', err);
    throw err;
  } finally {
    connection.done();
  }
};

/**
 * Get the staged status for site transfer
 * @param {string} siteId
 * @param {string} userId
 * @returns {Promise<boolean>} isStaged
 */
const getCustomAttributeStagedStatusBySiteIdRepo = async (siteId, userId) => {
  // @ts-ignore
  const { isStaged } = await server.db.read.row(
    `
    SELECT EXISTS (
        SELECT 1
        FROM custom_attribute.attribute_definition ad
          INNER JOIN custom_attribute.custom_attribute_entity_values caev ON caev.attribute_definition_id = ad.attribute_definition_id
          WHERE ad.entity_type = 2
            AND caev.is_staged = TRUE
            AND caev.entity_id = $1
            AND EXISTS(SELECT 1 FROM user_site_authorization usa WHERE usa.user_id = $2 AND usa.site_id = $1)
    ) AS is_staged;
`,
    [siteId, userId]
  );

  return isStaged;
};

const getAttributeValue = (attributes, attributeName) => {
  const stagedAttribute = attributes.find(
    attribute =>
      attribute?.attributeName === attributeName && attribute?.isStaged
  );

  return stagedAttribute
    ? stagedAttribute?.attributeOverrideValue
    : attributes.find(attribute => attribute?.attributeName === attributeName)
        ?.attributeOverrideValue;
};

const generateNewSiteNameAndCheckDuplicate = async siteId => {
  /*
   * Site has staged values for Wholesaler Name, Merchant ID and Station Address check
   */
  const stagedNamingAttributes = await server.db.read.rows(
    `SELECT caev.attribute_definition_id, caev.attribute_override_value, caev.is_staged, ad.attribute_name FROM custom_attribute.custom_attribute_entity_values caev 
        inner join custom_attribute.attribute_definition ad on caev.attribute_definition_id = ad.attribute_definition_id where caev.entity_id = $1
        and ad.attribute_name in ('Wholesaler Name', 'Merchant ID', 'Station Address') and not caev.deleted;
        `,
    [siteId]
  );

  if (
    stagedNamingAttributes &&
    stagedNamingAttributes.length > 2 &&
    stagedNamingAttributes.find(attribute => attribute.isStaged)
  ) {
    const merchantId = getAttributeValue(stagedNamingAttributes, 'Merchant ID');
    const wholesalerName = getAttributeValue(
      stagedNamingAttributes,
      'Wholesaler Name'
    );
    const stationAddress = getAttributeValue(
      stagedNamingAttributes,
      'Station Address'
    );

    if (
      merchantId &&
      merchantId.trim() !== '' &&
      wholesalerName &&
      wholesalerName.trim() !== '' &&
      stationAddress &&
      stationAddress.trim() !== ''
    ) {
      const newSiteName =
        `${merchantId} ${wholesalerName} ${stationAddress}`.replaceAll('"', '');

      const similarSite = await server.db.read.row(
        `
              SELECT site_id
              FROM site
              WHERE lower(name) = $1
            `,
        [newSiteName.toLowerCase()]
      );

      if (!similarSite) {
        return { isSuccess: true, isDuplicate: false, newSiteName };
      }

      return {
        isSuccess: false,
        isDuplicate: true,
        warningMsg: `The site name will not get updated after approval as there is already a site with name: ${newSiteName}`,
      };
    }

    return {
      isSuccess: false,
      isDuplicate: false,
      warningMsg: `One or more attributes missing for site name`,
    };
  }

  return {
    isSuccess: false,
    isDuplicate: false,
    warningMsg: `One or more attributes missing for site name`,
  };
};

/**
 * Get the duplicate site name for site transfer
 * @param {string} siteId
 * @param {string} userId
 * @returns {Promise<boolean>} isStaged
 */
const getStagedDuplicateSiteNameStatusBySiteIdRepo = async siteId => {
  const isDuplicate = await generateNewSiteNameAndCheckDuplicate(siteId);
  return isDuplicate;
};

const deleteStagingForSite = async (siteId, connection) =>
  // eslint-disable-next-line no-return-await
  await connection.execute(
    `
        DELETE
        FROM custom_attribute.custom_attribute_entity_values
        WHERE NOT deleted
          AND entity_id = $1
          AND NOT is_staged
          AND attribute_definition_id IN (SELECT DISTINCT attribute_definition_id
                                          FROM custom_attribute.custom_attribute_entity_values
                                          WHERE NOT deleted
                                            AND entity_id = $1
                                            AND is_staged)
    `,
    [siteId]
  );

const updateStagingForSite = async (siteId, userId, connection) =>
  // eslint-disable-next-line no-return-await
  await connection.execute(
    `
        UPDATE custom_attribute.custom_attribute_entity_values
        SET is_staged    = false,
            updated_by   = $2,
            updated_date = NOW()
        WHERE NOT deleted
          AND entity_id = $1
          AND is_staged
        RETURNING attribute_definition_id, attribute_override_value;
    `,
    [siteId, userId]
  );

const updateSiteName = async (siteId, userId, connection) => {
  const currentSite = await server.db.read.row(
    `
                SELECT s.*, kg.key_group_ref
                FROM site s
                INNER JOIN user_site_authorization u ON u.site_id = s.site_id AND u.user_id = $2
                LEFT JOIN key_group kg ON kg.key_group_id = s.key_group_id
                WHERE s.site_id = $1 AND s.active = true
                ;`,
    [siteId, userId]
  );

  if (!currentSite) {
    logger.error('Site not found for update site name after staging');
    throw new Error('Site not found');
  }

  const genSiteName = await generateNewSiteNameAndCheckDuplicate(siteId);
  const newSitename =
    genSiteName && genSiteName.newSiteName ? genSiteName.newSiteName : false;

  if (newSitename) {
    if (currentSite.name !== newSitename) {
      const sqlQuery = `
                  UPDATE site
                  SET name = $1
                  WHERE site_id = $2
              `;

      await connection.execute(sqlQuery, [newSitename, siteId]);
      await sendEventOnUpdateSite(siteId);

      return { isSuccess: true };
    }

    return {
      isSuccess: false,
      isDuplicate: true,
      warningMsg: `A site with name: ${newSitename} is already exists.`,
    };
  }

  return genSiteName;
};

/**
 * @param {string} siteId
 * @param {string} userId
 */
const deleteAndUpdateStagedValuesRepo = async (siteId, userId) => {
  // @ts-ignore
  const isStaged = await getCustomAttributeStagedStatusBySiteIdRepo(
    siteId,
    userId
  );
  if (!isStaged) {
    return {
      isSuccess: false,
      warningMsg: 'The given site does not have any staged custom attributes',
    };
  }

  const connection = await server.db.write.getConnection();
  try {
    await connection.execute(dbTransaction.BEGIN);
    const updateSiteNameResp = await updateSiteName(siteId, userId, connection);
    await deleteStagingForSite(siteId, connection);
    let updatedResults = await updateStagingForSite(siteId, userId, connection);
    await connection.execute(dbTransaction.COMMIT);
    updatedResults = {
      ...updatedResults,
      isDuplicateSiteName: updateSiteNameResp.isDuplicate,
    };
    return updatedResults;
  } catch (err) {
    logger.error('Failed to delete and update staged attributes', err);
    await connection.execute(dbTransaction.ROLLBACK);
    throw err;
  } finally {
    connection.done();
  }
};

const getCustomAttributeValuesByTenantRepo = async (
  companyId,
  attribute,
  pageSize,
  offset,
  searchText = null
) => {
  try {
    const params = [companyId, attribute];
    if (searchText) {
      params.push(`%${searchText}%`);
    }
    params.push(pageSize, offset);
    let paramIndex = 1;
    let query = `WITH attributeValueForCompany AS
                (SELECT DISTINCT caev.attribute_override_value AS attribute_value 
                FROM custom_attribute.custom_attribute_entity_values caev
                  INNER JOIN custom_attribute.attribute_definition ad 
                    on caev.attribute_definition_id = ad.attribute_definition_id
                WHERE ad.company_id = $${paramIndex}`;
    paramIndex += 1;

    query += ` AND ad.entity_type = 2
                  AND ad.attribute_name = $${paramIndex}`;
    paramIndex += 1;

    query += ` AND caev.deleted = FALSE
                  AND caev.is_staged = false`;

    if (searchText && searchText !== null) {
      query += ` AND caev.attribute_override_value LIKE $${paramIndex}`;
      paramIndex += 1;
    }

    query += `)
                SELECT (SELECT COUNT(1) FROM attributeValueForCompany)                                              AS total_count,
                       (SELECT JSON_AGG(a) from (SELECT * FROM attributeValueForCompany LIMIT $${paramIndex}`;
    paramIndex += 1;

    query += ` OFFSET $${paramIndex}) as a)   AS results;`;

    return await server.db.read.row(query, params);
  } catch (err) {
    logger.error('Failed to get tenant attribute values', err);
    throw err;
  }
};

module.exports = {
  getAttributeValuesBySiteId,
  getSitesByEntityIdsAndIntegrationIdsRepo,
  getSitesByEntityIds,
  getCustomAttributeDefinitionsByCompanyIdRepo,
  getCustomAttributeDefinitionsByIds,
  updateCustomAttributeValuesEntries,
  insertCustomAttributeValuesEntries,
  upsertCustomAttributeValuesEntries,
  upsertBulkCustomAttributeValuesEntries,
  getCustomAttributeStagedStatusBySiteIdRepo,
  deleteAndUpdateStagedValuesRepo,
  getCustomAttributeDefinitionByNameRepo,
  getSitesByCustomAttributeIdAndValuesRepo,
  getStagedDuplicateSiteNameStatusBySiteIdRepo,
  getCustomAttributeValuesByTenantRepo,
};
