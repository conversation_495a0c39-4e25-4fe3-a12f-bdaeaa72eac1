const Joi = require('joi');
const { throttle } = require('restify');
const { server } = require('../../app');
const env = require('../../env');
const { baseRoleGroup, roles } = require('../../lib/app-constants');
const {
  requiresFeatureFlag,
  requiresRole,
  validateParams,
  validateQuery,
  validateBody,
  validateHeaders,
  validateFormFiles,
} = require('../../lib/pre');
const {
  checkMaintenanceFrameAndRoles,
  validateExistsScheduled,
  scheduleDeploy,
} = require('../../helpers/scheduled-deployment-helper');
const handler = require('./custom-attributes.handler');
const {
  supportedEntityTypes,
  customAttributeImport,
} = require('./custom-attributes.constant');
const {
  sendEventOnUpdateCustomAttribute,
  sendEventToDeploymentReportOnUpdateCustomAttribute,
} = require('./custom-attributes.handler');

const updateCustomAttributesThrottle = env.config.updateCustomAttributesThrottle
  ? throttle({
      ...env.config.updateCustomAttributesThrottle,
      username: true,
      ip: false,
      xff: false,
    })
  : (req, res, next) => next();

const getCustomAttributesThrottle = env.config.getCustomAttributesThrottle
  ? throttle({
      ...env.config.getCustomAttributesThrottle,
      username: true,
      ip: false,
      xff: false,
    })
  : (req, res, next) => next();

const BASE_PATH = `${env.config.base}/custom-attributes`;

/**
 * @swagger
 * /custom-attributes/import:
 *     put:
 *       tags:
 *         - custom attributes
 *       summary: |
 *         Import custom attributes from a CSV file
 *         System must have a matching attribute definition for each column name, and those attributes must be editable.
 *       consumes:
 *         - multipart/form-data
 *       parameters:
 *         - in: formData
 *           name: fileContent
 *           type: file
 *           description: CSV file containing the custom attributes to import
 *           required: true
 *       responses:
 *         200:
 *           description: The number of rows imported
 *           schema:
 *             type: object
 *             properties:
 *               message:
 *                 type: string
 *                 description: The success message
 *         "400":
 *           description: The input was malformed
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 description: Error Code
 *                 type: string
 *               message:
 *                 description: Error Message
 *                 type: string
 *               context:
 *                 description: An array of errors in string
 *                 type: array
 *                 items:
 *                   type: string
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.put(
  {
    path: `${BASE_PATH}/import`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    requiresRole([roles.CONFIG_MGMT_DEPLOY]),
    requiresFeatureFlag(['SITE_ATTRIBUTES']),
    requiresFeatureFlag(['SHELL_SUPPORT']),
    validateFormFiles({
      fileContent: {
        name: Joi.string()
          .regex(/\.csv$/)
          .required(),
        size: Joi.number()
          .integer()
          .min(1)
          .max(customAttributeImport.importFileSizeLimit),
      },
    }),
    updateCustomAttributesThrottle,
    handler.putImportCustomAttributes,
    sendEventOnUpdateCustomAttribute,
    sendEventToDeploymentReportOnUpdateCustomAttribute,
  ]
);

/**
 * @swagger
 * /custom-attributes/definitions/{entity}:
 *     get:
 *       tags:
 *         - custom attributes
 *       summary: Get custom attribute definitions for a company
 *       parameters:
 *         - in: path
 *           name: entity
 *           type: string
 *           required: true
 *           description: Type of the Entity
 *         - in: query
 *           name: pageSize
 *           type: integer
 *           required: false
 *           description: ID of the Entity
 *         - in: query
 *           name: pageIndex
 *           type: integer
 *           required: false
 *           description: ID of the Entity
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *       responses:
 *         "200":
 *           description: "Successful response"
 *           schema:
 *            type: object
 *            properties:
 *              resultsMetadata:
 *                $ref: '#/definitions/ResultsMetadata'
 *              results:
 *                type: array
 *                items:
 *                  $ref: '#/definitions/CustomAttributeDefinition'
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "404":
 *           description: "No Attribute definitions found"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/definitions/:entity`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    requiresFeatureFlag(['SITE_ATTRIBUTES']),
    validateParams({
      entity: Joi.string().valid(...supportedEntityTypes),
    }),
    validateQuery({
      pageSize: Joi.number().integer().min(1).max(80).optional(),
      pageIndex: Joi.number().integer().min(0).optional(),
    }),
    getCustomAttributesThrottle,
    handler.getCustomAttributeDefinitions,
  ]
);

/**
 * @swagger
 * /custom-attributes/{entity}/{entityId}:
 *     get:
 *       tags:
 *         - custom attributes
 *       summary: Get custom attributes for a company with any overridden values for a given entity type.
 *       parameters:
 *         - in: path
 *           name: entity
 *           type: string
 *           required: true
 *           description: Type of the Entity
 *         - in: path
 *           name: entityId
 *           type: string
 *           required: true
 *           description: ID of the Entity
 *         - in: query
 *           name: pageSize
 *           type: integer
 *           required: false
 *           description: ID of the Entity
 *         - in: query
 *           name: pageIndex
 *           type: integer
 *           required: false
 *           description: ID of the Entity
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *       responses:
 *         "200":
 *           description: "Successful response"
 *           schema:
 *            type: object
 *            properties:
 *              resultsMetadata:
 *                $ref: '#/definitions/ResultsMetadata'
 *              results:
 *                type: array
 *                items:
 *                  $ref: '#/definitions/CustomAttributeData'
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "404":
 *           description: "Site not found, or no Attribute definitions found"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/:entity/:entityId`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    requiresFeatureFlag(['SITE_ATTRIBUTES']),
    validateParams({
      entity: Joi.string().valid(...supportedEntityTypes),
      entityId: Joi.string().required(),
    }),
    validateQuery(
      {
        pageSize: Joi.number().integer().min(1).max(80).optional(),
        pageIndex: Joi.number().integer().min(0).optional(),
      },
      false
    ),
    getCustomAttributesThrottle,
    handler.getCustomAttributes,
  ]
);
// we don't create the document for this API endpoint. Because, this is internal API
server.get(
  {
    path: `${BASE_PATH}/internal/:entity/:entityId`,
    version: '0.0.1',
  },
  [
    requiresRole([roles.ICS_SYSTEM]),
    validateParams({
      entity: Joi.string().valid(...supportedEntityTypes),
      entityId: Joi.string().required(),
    }),
    validateHeaders({
      tenantid: Joi.string().guid().required(),
    }),
    validateQuery({
      pageSize: Joi.number().integer().min(1).max(80).optional(),
      pageIndex: Joi.number().integer().min(0).optional(),
    }),
    handler.getCustomAttributesInternal,
  ]
);

/**
 * @swagger
 *  "/custom-attributes/{entity}":
 *     put:
 *       tags:
 *         - custom attributes
 *       summary: "Update/Create custom-attributes values"
 *       description: |
 *         Update or Create custom-attributes value entires in the DB
 *         In order to send the request:
 *         - a valid entityId or integrationId is required
 *         - attributes array must not be empty
 *         - attributeDefinitionId must be valid
 *         - isStaged is optional and default = FALSE
 *       parameters:
 *        - in: path
 *          name: entity
 *          type: string
 *          enum: ["sites"]
 *          required: true
 *        - in: body
 *          name: body
 *          required: true
 *          schema:
 *            $ref: "#/definitions/CustomAttributeUpdatePayload"
 *       responses:
 *         "200":
 *           description: "Successful response"
 *           schema:
 *             $ref: "#/definitions/CustomAttributeUpdateResponse"
 *
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "429":
 *           $ref: "#/responses/TooManyRequestsError"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.put(
  {
    path: `${BASE_PATH}/:entity`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN']),
    requiresFeatureFlag(['SITE_ATTRIBUTES']),
    validateParams({
      entity: Joi.string().valid(...supportedEntityTypes),
    }),
    validateBody(
      Joi.array().items(
        Joi.object()
          .keys({
            isStaged: Joi.boolean().optional().default(false),
            integrationId: Joi.alternatives()
              .try(Joi.string(), Joi.number().integer())
              .optional(),
            entityId: Joi.alternatives()
              .try(Joi.string(), Joi.number().integer())
              .optional(),
            attributes: Joi.array()
              .items(
                Joi.object().keys({
                  attributeDefinitionId: Joi.number().integer().required(),
                  value: Joi.alternatives()
                    .try(Joi.any().empty(null).allow(null, '').default(null))
                    .required(),
                })
              )
              .min(1)
              .required(),
            deploymentType: Joi.string()
              .valid('maintenance-window', 'immediate', 'schedule')
              .optional()
              .default('maintenance-window'),
          })
          .xor('integrationId', 'entityId')
          .required()
      ),
      false,
      true
    ),
    updateCustomAttributesThrottle,
    handler.putCustomAttributes,
    handler.sendEventToEventBridgeOnCustomAttributesUpdate,
    handler.sendEventToDeploymentReport,
  ]
);

/**
 * @swagger
 *  "/custom-attributes/{entity}/bulk":
 *     put:
 *       tags:
 *         - custom attributes
 *       summary: "Bulk Update custom-attributes values"
 *       description: |
 *         Bulk Update or Create custom-attributes value entires in the DB
 *         In order to send the request:
 *         - entityId array must be valid
 *         - attributes array must not be empty
 *         - attributeDefinitionId must be valid
 *         Attribute values which are staged cannot be updated.
 *         If there is an existing staged value, then a new unstaged value will be created instead.
 *         - scheduledDateTime (YYYY-MM-DD HH:mm) is required if deploymentType is schedule
 *         - mfaCode contains User's MFA code
 *       parameters:
 *        - in: path
 *          name: entity
 *          type: string
 *          enum: ["sites"]
 *          required: true
 *        - in: body
 *          name: body
 *          required: true
 *          schema:
 *            $ref: "#/definitions/CustomAttributeBulkUpdatePayload"
 *       responses:
 *         "200":
 *           description: "Successful response"
 *           schema:
 *             $ref: "#/definitions/CustomAttributeUpdateBulkResponse"
 *
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "429":
 *           $ref: "#/responses/TooManyRequestsError"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.put(
  {
    path: `${BASE_PATH}/:entity/bulk`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    requiresRole([roles.CONFIG_MGMT_DEPLOY]),
    requiresFeatureFlag(['SITE_ATTRIBUTES']),
    validateParams({
      entity: Joi.string().valid(...supportedEntityTypes),
    }),
    validateBody({
      entityIds: Joi.array().items(Joi.string()).unique().min(1).required(),
      attributes: Joi.array()
        .items(
          Joi.object().keys({
            attributeDefinitionId: Joi.number().integer().required(),
            value: Joi.alternatives()
              .try(Joi.any().empty(null).allow(null, '').default(null))
              .required(),
          })
        )
        .min(1)
        .required(),
      deploymentType: Joi.string()
        .valid('maintenance-window', 'immediate', 'schedule')
        .optional(),
      scheduledDateTime: Joi.date().iso().optional(),
      meta: Joi.string().default(undefined).optional(),
      mfaCode: Joi.string().length(6).required(),
    }),
    updateCustomAttributesThrottle,
    checkMaintenanceFrameAndRoles('UpdateSiteCustomAttribute'),
    validateExistsScheduled('UpdateSiteCustomAttribute'),
    scheduleDeploy('UpdateSiteCustomAttribute'),
    handler.putBulkCustomAttributes,
    handler.sendEventToEventBridgeOnCustomAttributesUpdate,
    handler.sendEventToDeploymentReport,
  ]
);

/**
 * @swagger
 * /custom-attributes/{entity}/{siteId}/is-staged:
 *   get:
 *     tags:
 *       - custom attributes
 *     summary: Check if custom attributes for a specific entity type and site are staged
 *     description: Returns a boolean indicating whether or not custom attributes for the specified entity type and site are staged. Rqeuires 'SITE_ATTRIBUTES'
 *     parameters:
 *       - in: path
 *         name: entity
 *         required: true
 *         description: The type of entity for which custom attributes are being checked
 *         type: string
 *         enum: ["sites"]
 *       - in: path
 *         name: siteId
 *         required: true
 *         description: The ID of the site for which custom attributes are being checked
 *         type: string
 *         format: uuid
 *     responses:
 *       "200":
 *         description: Returns a boolean indicating whether or not custom attributes for the specified entity type and site are staged.
 *         schema:
 *           type: boolean
 *       "400":
 *         $ref: "#/responses/BadRequest"
 *       "401":
 *         $ref: "#/responses/AuthenticationFailed"
 *       "403":
 *         $ref: "#/responses/Forbidden"
 *       "404":
 *         description: "Site not found, or no Attribute definitions found"
 *       "429":
 *         $ref: "#/responses/TooManyRequestsError"
 *       "500":
 *         $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/:entity/:entityId/is-staged`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    requiresFeatureFlag(['SITE_ATTRIBUTES']),
    validateParams({
      entity: Joi.string().valid(...supportedEntityTypes),
      entityId: Joi.string().required(),
    }),
    getCustomAttributesThrottle,
    handler.getCustomAttributeStagedStatus,
  ]
);

/**
 * @swagger
 *  "/custom-attributes/{entity}/{entityId}/is-staged/apply":
 *     put:
 *       tags:
 *         - custom attributes
 *       summary: "Apply staged custom-attributes values"
 *       description: |
 *         Updates all staged values for a given site to be unstaged
 *         Any previously unstaged values for the relevant custom attribute IDs will first be hard-deleted.
 *         CONFIG_MGMT_DEPLOY role is needed, as well as a standard role
 *       parameters:
 *        - in: path
 *          name: entity
 *          type: string
 *          enum: ["sites"]
 *          required: true
 *        - in: path
 *          name: entityId
 *          type: string
 *          required: true
 *          description: ID of the Entity
 *       responses:
 *         "200":
 *           description: "Successful response"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "429":
 *           $ref: "#/responses/TooManyRequestsError"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.put(
  {
    path: `${BASE_PATH}/:entity/:entityId/is-staged/apply`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    requiresRole([roles.CONFIG_MGMT_DEPLOY]),
    requiresFeatureFlag(['SITE_ATTRIBUTES']),
    validateParams({
      entity: Joi.string().valid(...supportedEntityTypes),
      entityId: Joi.string().required(),
    }),
    validateBody(
      Joi.object({
        deploymentType: Joi.string()
          .valid('maintenance-window', 'immediate', 'schedule')
          .optional(),
      }).optional()
    ),
    updateCustomAttributesThrottle,
    handler.putStagedAttributesApply,
    sendEventOnUpdateCustomAttribute,
    sendEventToDeploymentReportOnUpdateCustomAttribute,
  ]
);

/**
 * @swagger
 * /custom-attributes/{entity}/{siteId}/is-staged/is-duplicate-sitename:
 *   get:
 *     tags:
 *       - custom attributes
 *     summary: Check if a site name with the staged values (Merchant ID + Wholesaler Name + Station Address) already exist
 *     description: Returns a boolean indicating whether or not the sitename with staged custom attributes already exists. Rqeuires 'SITE_ATTRIBUTES'
 *     parameters:
 *       - in: path
 *         name: entity
 *         required: true
 *         description: The type of entity for which custom attributes are being checked
 *         type: string
 *         enum: ["sites"]
 *       - in: path
 *         name: siteId
 *         required: true
 *         description: The ID of the site for which custom attributes are being checked
 *         type: string
 *         format: uuid
 *     responses:
 *       "200":
 *         description: Returns an object with properties isDuplicate which is boolean indicating whether site name is duplicate and a warningMsg that is a string.
 *         schema:
 *          type: object
 *          properties:
 *            isDuplicate:
 *              type: boolean
 *            warningMsg:
 *              type: string
 *       "400":
 *         $ref: "#/responses/BadRequest"
 *       "401":
 *         $ref: "#/responses/AuthenticationFailed"
 *       "403":
 *         $ref: "#/responses/Forbidden"
 *       "404":
 *         description: "Site not found, or no Attribute definitions found"
 *       "429":
 *         $ref: "#/responses/TooManyRequestsError"
 *       "500":
 *         $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/:entity/:entityId/is-staged/is-duplicate-sitename`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    requiresFeatureFlag(['SITE_ATTRIBUTES']),
    validateParams({
      entity: Joi.string().valid(...supportedEntityTypes),
      entityId: Joi.string().required(),
    }),
    getCustomAttributesThrottle,
    handler.getCustomAttributeDuplicateSiteNameStatus,
  ]
);

/**
 * @swagger
 * /custom-attributes/attributes:
 *   post:
 *     tags:
 *       - custom attributes
 *     summary: Get the custom attribute value for all the sites within the company
 *     parameters:
 *       - in: body
 *         name: attribute
 *         required: true
 *         description: The custom attribute whose value is required (Eg:- Merchant ID)
 *         type: string
 *       - in: body
 *         name: searchText
 *         required: false
 *         description: The part of key for in string search
 *         type: string
 *       - in: body
 *         name: pageSize
 *         type: integer
 *         required: false
 *         description: Number of records to be returned
 *       - in: body
 *         name: pageIndex
 *         type: integer
 *         required: false
 *         description: Page number
 *       - $ref: '#/parameters/AuthorizationTokenParam'
 *     responses:
 *       "200":
 *         description: "Successful response"
 *         schema:
 *          type: object
 *          properties:
 *            resultsMetadata:
 *              $ref: '#/definitions/ResultsMetadata'
 *            results:
 *              type: array
 *              items:
 *                $ref: '#/definitions/CustomAttributeDefinition'
 *       "400":
 *         $ref: "#/responses/BadRequest"
 *       "401":
 *         $ref: "#/responses/AuthenticationFailed"
 *       "403":
 *         $ref: "#/responses/Forbidden"
 *       "404":
 *         description: "Site not found, or no Attribute found"
 *       "429":
 *         $ref: "#/responses/TooManyRequestsError"
 *       "500":
 *         $ref: "#/responses/InternalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/attributes`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    requiresFeatureFlag(['SITE_ATTRIBUTES']),
    validateBody({
      attribute: Joi.string().required(),
      searchText: Joi.string().optional(),
      pageSize: Joi.number().integer().min(1).max(80).optional(),
      pageIndex: Joi.number().integer().min(0).optional(),
    }),
    getCustomAttributesThrottle,
    handler.getCustomAttributeValuesList,
  ]
);
