const constants = {
  customAttributesDefaultPageSize: 80,
  customAttributeUpdateLimit: 1000,
  customAttributeValueLengthLimit: 1000,
  customAttributeBulkUpdateLimit: 50000,
  customAttributeBulkSpliceLimit: 5000,
  customAttributeImport: {
    encoding: 'utf-8',
    importFileSizeLimit: 5242880,
    importRowLimit: 500,
    columns: {
      keyColumn: 'Merchant ID',
      allowedColumns: ['Merchant ID', 'SIP Secret Key'],
    },
  },
  supportedEntityTypes: ['sites'],
  supportedEntityTypesText: {
    sites: 'Site',
    tenants: 'Tenant',
    assets: 'Asset',
  },
  supportedEntityTypesInternalCode: {
    tenants: 1,
    sites: 2,
    assets: 3,
  },
  stringTemplates: {
    attributeDefinition: 'Attribute definition',
  },
  SITE_ATTRIBUTES: {
    Update: 'UpdateSiteCustomAttribute',
  },
};

module.exports = constants;
