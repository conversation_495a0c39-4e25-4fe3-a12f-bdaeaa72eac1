const restifyErrors = require('restify-errors');
const validateUUID = require('uuid-validate');
const { readFile } = require('fs-extra');
const { isEmpty } = require('lodash');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');
const { getSiteById } = require('../../helpers/sites-helper');
const {
  roles: rolesConstant,
  deploymentType: deploymentTypeConstant,
} = require('../../lib/app-constants');
const AWS = require('../../lib/aws');
const env = require('../../env');

const { parseCSV } = require('../../lib/utils');
const logger = require('../../lib/logger').mainLogger();
const totp = require('../../lib/totp');
const usersHelper = require('../../helpers/users-helper');
const {
  validateAttributeUpdateLimit,
  validateCustomAttributeRequestItem,
  validateBulkAttributeUpdateLimit,
  validateBulkCustomAttributeRequestItem,
  validateAttributeImportRequestItem,
} = require('./custom-attributes.validator');
const {
  customAttributesDefaultPageSize,
  supportedEntityTypesInternalCode,
  customAttributeValueLengthLimit,
  customAttributeImport,
  customAttributeBulkSpliceLimit,
} = require('./custom-attributes.constant');
const {
  getCustomAttributeValues,
  getEntityIdsAndIntegrationIds,
  getEntityIds,
  searchCustomAttributeDefinitions,
  upsertCustomAttributeValues,
  upsertBulkCustomAttributeValues,
  getCustomAttributeDefinitionsByCompanyId,
  getCustomAttributeStagedStatusBySiteId,
  getSitesByEntityIdsAndIntegrationIds,
  getCustomAttributeDefinitionByName,
  getSitesByCustomAttributeIdAndValues,
  deleteAndUpdateStagedValues,
  getStagedDuplicateSiteNameStatusBySiteId,
  getCustomAttributeValuesByTenant,
} = require('./custom-attributes.service');
const {
  transformBulkData,
  transformImportedData,
  chunkArrayItems,
} = require('./custom-attributes.helper');

const getCustomAttributeDefinitions = async (req, res, next) => {
  try {
    const {
      user: {
        company: { id: companyId },
      },
      params: { entity },
    } = req;

    const { pageIndex, pageSize, offset } =
      paginationHelper.parsePaginationParams({
        query: {
          ...req.query,
          pageSize:
            req.query.pageSize === undefined
              ? customAttributesDefaultPageSize
              : req.query.pageSize,
        },
      });

    const attributeDefinitionResponse =
      await getCustomAttributeDefinitionsByCompanyId(
        !isEmpty(req.query.tenantId) ? req.query.tenantId : companyId,
        entity,
        pageSize,
        offset
      );

    if (!attributeDefinitionResponse) {
      return next(
        new restifyErrors.NotFoundError(
          'Custom Attributes do not exist for company'
        )
      );
    }

    const results = {
      resultsMetadata: {
        totalResults: attributeDefinitionResponse.totalCount,
        pageIndex,
        pageSize,
      },
      results: attributeDefinitionResponse.results || [],
    };

    res.send(results);

    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

const getCustomAttributes = async (req, res, next) => {
  try {
    const userId = req.user.sub;
    const { entityId: siteId } = req.params;

    if (!validateUUID(siteId)) {
      return next(
        new restifyErrors.BadRequestError(`Invalid Site Id: ${siteId}`)
      );
    }

    const companyId = req.user.company.id;
    const { pageIndex, pageSize, offset } =
      paginationHelper.parsePaginationParams({
        query: {
          ...req.query,
          pageSize:
            req.query.pageSize === undefined
              ? customAttributesDefaultPageSize
              : req.query.pageSize,
        },
      });

    const targetSite = await getSiteById(siteId, userId, companyId);
    if (!targetSite) {
      return next(new restifyErrors.NotFoundError('Site not found'));
    }

    const targetCompanyId = targetSite.owner.id;
    const entityType = supportedEntityTypesInternalCode.sites;
    const entityId = siteId;

    const attributeDefinitionResponse = await getCustomAttributeValues(
      targetCompanyId,
      entityType,
      entityId,
      pageSize,
      offset,
      true
    );

    if (!attributeDefinitionResponse) {
      return next(
        new restifyErrors.NotFoundError(
          'Custom Attributes do not exist for company'
        )
      );
    }

    const results = {
      resultsMetadata: {
        totalResults: attributeDefinitionResponse.totalCount,
        pageIndex,
        pageSize,
      },
      results: attributeDefinitionResponse.results || [],
    };

    res.send(results);

    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};
const getCustomAttributesInternal = async (req, res, next) => {
  try {
    const { entityId: siteId } = req.params;

    if (!validateUUID(siteId)) {
      return next(
        new restifyErrors.BadRequestError(`Invalid Site Id: ${siteId}`)
      );
    }

    const { pageIndex, pageSize, offset } =
      paginationHelper.parsePaginationParams({
        query: {
          ...req.query,
          pageSize:
            req.query.pageSize === undefined
              ? customAttributeValueLengthLimit
              : req.query.pageSize,
        },
      });

    const entityType = supportedEntityTypesInternalCode.sites;
    const entityId = siteId;

    const attributeDefinitionResponse = await getCustomAttributeValues(
      req.user.company.id,
      entityType,
      entityId,
      pageSize,
      offset,
      false
    );

    if (!attributeDefinitionResponse) {
      return next(
        new restifyErrors.NotFoundError(
          'Custom Attributes do not exist for company'
        )
      );
    }

    const results = {
      resultsMetadata: {
        totalResults: attributeDefinitionResponse.totalCount,
        pageIndex,
        pageSize,
      },
      results: attributeDefinitionResponse.results || [],
    };

    res.send(results);

    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};
const getCustomAttributeStagedStatus = async (req, res, next) => {
  try {
    const userId = req.user.sub;
    const companyId = req.user.company.id;
    const { entityId: siteId } = req.params;

    if (!siteId) {
      return next(new restifyErrors.BadRequestError('entityId not provided'));
    }

    // Check if Site Exist
    const validSiteIds = await getSitesByEntityIdsAndIntegrationIds(
      userId,
      companyId,
      [siteId]
    );
    if (validSiteIds.length <= 0 || validSiteIds[0].entityId !== siteId) {
      return next(new restifyErrors.NotFoundError('Entity not found'));
    }

    // Cehck Staged Status
    const isStaged = await getCustomAttributeStagedStatusBySiteId(
      siteId,
      userId
    );

    res.send(isStaged);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

const getCustomAttributeDuplicateSiteNameStatus = async (req, res, next) => {
  try {
    const userId = req.user.sub;
    const companyId = req.user.company.id;
    const { entityId: siteId } = req.params;

    if (!validateUUID(siteId)) {
      return next(
        new restifyErrors.BadRequestError(`Invalid Site Id: ${siteId}`)
      );
    }

    if (!siteId) {
      return next(new restifyErrors.BadRequestError('entityId not provided'));
    }

    // Check if Site Exist
    const validSiteIds = await getSitesByEntityIdsAndIntegrationIds(
      userId,
      companyId,
      [siteId]
    );
    if (validSiteIds.length <= 0 || validSiteIds[0].entityId !== siteId) {
      return next(new restifyErrors.NotFoundError('Site not found'));
    }

    // Check Staged Duplicate Site Name Status
    const isDuplicateSiteName = await getStagedDuplicateSiteNameStatusBySiteId(
      siteId,
      userId
    );

    res.send(isDuplicateSiteName);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

const putCustomAttributes = async (req, res, next) => {
  const userId = req.user.sub;
  const companyId = req.user.company.id;
  const entityType = req.params.entity;
  const updateItems = req.body;

  try {
    // Payload validators
    validateAttributeUpdateLimit(updateItems);

    // Shared variables
    const entityIds = updateItems
      .flatMap(item => item.entityId)
      .filter(Boolean);
    const integrationIds = updateItems
      .flatMap(item => item.integrationId)
      .filter(Boolean);
    const attributeDefinitionIds = updateItems
      .flatMap(item => item.attributes)
      .flatMap(item => item.attributeDefinitionId);

    const existingEntityMeta = await getEntityIdsAndIntegrationIds(
      userId,
      companyId,
      entityIds,
      integrationIds,
      entityType
    );
    const attributeDefsMeta = await searchCustomAttributeDefinitions(
      companyId,
      attributeDefinitionIds,
      entityType
    );

    const transformedUpdateItems = updateItems.map(
      ({ attributes, ...rest }) => ({
        ...rest,
        attributes: transformBulkData(attributes, attributeDefsMeta),
      })
    );

    const validateResult = validateCustomAttributeRequestItem(
      transformedUpdateItems,
      existingEntityMeta,
      attributeDefsMeta,
      entityType
    );

    /** @type {{ entityId: string; value: string; attributeDefinitionId: number; isStaged: boolean }[]} */
    const updateCandidates = transformedUpdateItems
      .flatMap(item => {
        const { entityId, integrationId, attributes, isStaged } = item;

        const isValid = !!validateResult.find(
          val =>
            val.status === 200 &&
            ((entityId && val.entityId === entityId) ||
              (integrationId && val.integrationId === integrationId))
        );

        if (isValid) {
          const entityIdInt = existingEntityMeta.find(
            em => em.entityId === entityId || em.integrationId === integrationId
          ).entityId;

          return attributes.map(({ attributeDefinitionId, value }) => ({
            entityId: entityIdInt,
            value,
            attributeDefinitionId,
            isStaged,
          }));
        }

        return null;
      })
      .filter(Boolean);

    await upsertCustomAttributeValues(updateCandidates, userId);

    res.send(validateResult);

    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

const putBulkCustomAttributes = async (req, res, next) => {
  const userId = req.user.sub;
  const companyId = req.user.company.id;
  const entityType = req.params.entity;

  // MFA validation
  const { mfaCode } = req.body;
  if (mfaCode) {
    const mfaSecret = await usersHelper.getUserMfaSecretById(userId);
    const isMfaCodeValid = await totp.validateTotp(req, mfaSecret, mfaCode);
    if (!isMfaCodeValid) {
      return next(
        new restifyErrors.ForbiddenError(
          `MFA code ${mfaCode} validation failed`
        )
      );
    }
  }

  const roles = req.user.roles || [];

  if (!roles.includes(rolesConstant.CONFIG_MGMT_DEPLOY)) {
    return next(new restifyErrors.ForbiddenError('Unauthorized User'));
  }

  const { entityIds, attributes } = req.body;

  try {
    // Validate total attributes don't exceed max
    validateBulkAttributeUpdateLimit(entityIds, attributes);

    // check DB for entities
    const existingEntityMeta = await getEntityIds(
      userId,
      companyId,
      entityIds,
      entityType
    );

    // check DB for attributes
    const attributeDefinitionIds = attributes.map(
      item => item.attributeDefinitionId
    );
    const attributeDefinitionsMeta = await searchCustomAttributeDefinitions(
      companyId,
      attributeDefinitionIds,
      entityType
    );

    const transformedAttributes = transformBulkData(
      attributes,
      attributeDefinitionsMeta
    );

    // perform validations against input based on DB results
    const validateResult = validateBulkCustomAttributeRequestItem(
      entityIds,
      transformedAttributes,
      existingEntityMeta,
      attributeDefinitionsMeta,
      entityType
    );

    if (validateResult.length) {
      return res.send(400, validateResult);
    }

    await upsertBulkCustomAttributeValues(
      entityIds,
      transformedAttributes,
      userId
    );

    res.send(200);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

const putStagedAttributesApply = async (req, res, next) => {
  const userId = req.user.sub;
  const companyId = req.user.company.id;
  const { entityId, entity: entityType } = req.params;

  if (isEmpty(req.body)) {
    req.body = { deploymentType: 'immediate' };
  }

  try {
    // Check if Site Exists, and current user can access it
    const validSiteIds = await getSitesByEntityIdsAndIntegrationIds(
      userId,
      companyId,
      [entityId]
    );
    if (validSiteIds.length <= 0 || validSiteIds[0].entityId !== entityId) {
      return next(new restifyErrors.NotFoundError('Entity not found'));
    }

    const attributeDefinitionUpdateResults = await deleteAndUpdateStagedValues(
      entityType,
      entityId,
      userId
    );

    if (
      // @ts-ignore
      attributeDefinitionUpdateResults &&
      // @ts-ignore
      Array.isArray(attributeDefinitionUpdateResults.rows)
    ) {
      // @ts-ignore
      req.attributes = attributeDefinitionUpdateResults.rows.map(attribute => ({
        ...attribute,
        entityId,
      }));
    }

    let isDuplicateSiteName =
      attributeDefinitionUpdateResults.isDuplicateSiteName
        ? {
            isDuplicateSiteName:
              attributeDefinitionUpdateResults.isDuplicateSiteName,
          }
        : {};

    if (attributeDefinitionUpdateResults.warningMsg) {
      isDuplicateSiteName = attributeDefinitionUpdateResults;
    }

    res.send(200, isDuplicateSiteName);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

const putImportCustomAttributes = async (req, res, next) => {
  try {
    const { keyColumn, allowedColumns, encoding } =
      customAttributeImport.columns;

    const userId = req.user.sub;
    const companyId = req.user.company.id;

    const fileObject = /** @type {any}>} * */ (req.files.fileContent);
    const fileData = await readFile(fileObject.path, encoding);

    const records = (() => {
      try {
        return /** @type {Array<{ [key: string]: any }>} * */ (
          parseCSV(fileData, { columns: true, delimiter: ',' })
        );
      } catch (err) {
        throw new restifyErrors.BadRequestError(err.toString());
      }
    })();

    // Ignore(By removing key) all columns that are not part of Shell requirement.
    // Otherwise validator will check each key present matching in the attribute definition table.
    // This is temporary solution and shall be refactored once decided to provide this feature to all customers.
    records.forEach(record => {
      Object.keys(record).forEach(key => {
        if (!allowedColumns.includes(key)) {
          // eslint-disable-next-line no-param-reassign
          delete record[key];
        }
      });
    });

    const attributeDefinitions = await getCustomAttributeDefinitionByName(
      companyId,
      allowedColumns,
      supportedEntityTypesInternalCode.sites
    );
    const lookUpKeyValues = records.map(r => r[keyColumn]);
    const keyColumnAttribDef = attributeDefinitions.find(
      def => def.attributeName === keyColumn
    );
    if (!keyColumnAttribDef) {
      throw new restifyErrors.BadRequestError(
        `Specified attribute definitions (${keyColumn}) not present in the system.`
      );
    }
    const lookUpSites = await getSitesByCustomAttributeIdAndValues(
      userId,
      companyId,
      keyColumnAttribDef.attributeDefinitionId,
      lookUpKeyValues
    );

    const transformedRecords = transformImportedData(
      records,
      attributeDefinitions,
      keyColumn
    );

    await validateAttributeImportRequestItem(
      transformedRecords,
      attributeDefinitions,
      keyColumn,
      allowedColumns,
      lookUpSites
    );

    // Prepare Update Attribute Values
    const updatedSites = /** @type {string[]} * */ ([]);
    const updateTarget = (() => {
      const targetAttribNames = allowedColumns.filter(
        item => item !== keyColumn
      );
      const updateRecords = transformedRecords.reduce((accumulator, r) => {
        const keyValue = r[keyColumn];
        const matchSite = lookUpSites.find(
          site => site.entityKeyValue === keyValue
        );
        if (matchSite) {
          updatedSites.push(matchSite.entityId);
          const attributes = targetAttribNames.reduce(
            (/** @type {any[]} * */ attribAccumulator, attribName) => {
              const attribDef = attributeDefinitions.find(
                attr => attr.attributeName === attribName
              );
              if (attribDef) {
                attribAccumulator.push({
                  entityId: matchSite.entityId,
                  value: r[attribName],
                  attributeDefinitionId: attribDef.attributeDefinitionId,
                  isStaged: false,
                });
              }
              return attribAccumulator;
            },
            []
          );
          accumulator.push(...attributes);
        }
        return accumulator;
      }, []);

      return /** @type {{ entityId: string; value: string; attributeDefinitionId: number; isStaged: boolean }[]} * */ (
        updateRecords.flatMap(i => i)
      );
    })();

    req.attributes = updateTarget
      .map(({ isStaged, attributeDefinitionId, entityId }) =>
        !isStaged ? { attributeDefinitionId, entityId } : undefined
      )
      .filter(Boolean);

    await upsertCustomAttributeValues(updateTarget, userId);

    res.send({ message: `${updatedSites.length} row(s) imported` });
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

const sendEventOnUpdateCustomAttribute = async (req, _res, next) => {
  const companyId = req.user.company.id;
  const { attributes } = req;
  const { deploymentType } = req.body;

  if (attributes) {
    const eventBridgeParams = {
      data: {
        tenantId: companyId,
        attributes,
        deploymentType:
          deploymentType || deploymentTypeConstant.MAINTENANCE_WINDOW,
        updatedBy: req.user.sub,
      },
      detailType:
        env.config.AWS.eventBus.renditions.rules.updateCustomVariables,
      source: 'putCustomAttributes',
    };

    await AWS.sendEventsToEventBridge(eventBridgeParams);
  }

  return next();
};

const sendEventToDeploymentReportOnUpdateCustomAttribute = async (
  req,
  _res,
  next
) => {
  const companyId = req.user.company.id;
  const { attributes } = req;
  const { entityId } = req.params;

  const [{ attributeDefinitionId: merchantIdAttributeDefinitionId }] =
    await getCustomAttributeDefinitionByName(
      companyId,
      ['Merchant ID'],
      supportedEntityTypesInternalCode.sites
    );
  const merchantIdAttribute = attributes.find(
    ({ attributeDefinitionId }) =>
      merchantIdAttributeDefinitionId === attributeDefinitionId
  );

  if (merchantIdAttribute) {
    await AWS.sendEventsToEventBridge({
      data: {
        siteId: entityId,
        merchantId: merchantIdAttribute.attributeOverrideValue,
      },
      detailType:
        env.config.AWS.eventBus.deploymentReport.rules.updateSiteMerchantId,
      source: 'deployment-report',
    });
  }

  return next();
};

const sendEventToEventBridgeOnCustomAttributesUpdate = async (
  req,
  _res,
  next
) => {
  const companyId = req.user.company.id;
  const entityType = req.params.entity;
  const updateItems = req.body;

  const { body } = req;
  const userId = req.user.sub;

  if (Array.isArray(body)) {
    const givenEntityIds = updateItems
      .flatMap(item => item.entityId)
      .filter(Boolean);
    const givenIntegrationIds = updateItems
      .flatMap(item => item.integrationId)
      .filter(Boolean);

    const existingEntityMeta = await getEntityIdsAndIntegrationIds(
      userId,
      companyId,
      givenEntityIds,
      givenIntegrationIds,
      entityType
    );
    const events = body.map(
      ({ isStaged, attributes, entityId, deploymentType, integrationId }) => {
        const entityIdInt = existingEntityMeta.find(
          em => em.entityId === entityId || em.integrationId === integrationId
        ).entityId;
        if (!isStaged) {
          const updatedAttributes = attributes.map(attribute => ({
            ...attribute,
            entityId: entityId || entityIdInt,
          }));
          const allAttrDefIds = chunkArrayItems(
            updatedAttributes,
            customAttributeBulkSpliceLimit
          );
          return allAttrDefIds.map(eachAttr => {
            const eventBridgeParams = {
              data: {
                tenantId: companyId,
                attributes: eachAttr,
                deploymentType:
                  deploymentType || deploymentTypeConstant.MAINTENANCE_WINDOW,
                updatedBy: userId,
              },
              detailType:
                env.config.AWS.eventBus.renditions.rules.updateCustomVariables,
              source: 'putCustomAttributes',
            };
            if (req.body.meta) {
              eventBridgeParams.data.meta = req.body.meta; // add meta to event bridge params
            }
            return AWS.sendEventsToEventBridge(eventBridgeParams);
          });
        }
        return undefined;
      }
    );
    const flattenedPromises = events
      .flat()
      .filter(promise => promise !== undefined);
    await Promise.all(flattenedPromises);
  } else {
    const { entityIds, attributes, deploymentType, meta } = body;
    const updatedAttributes = attributes.reduce((acc, attribute) => {
      entityIds.forEach(entityId => {
        acc.push({
          attributeDefinitionId: attribute.attributeDefinitionId,
          entityId,
        });
      });
      return acc;
    }, []);
    const allAttrDefIds = chunkArrayItems(
      updatedAttributes,
      customAttributeBulkSpliceLimit
    );
    const events = allAttrDefIds.map(eachAttr => {
      const eventBridgeParams = {
        data: {
          tenantId: companyId,
          attributes: eachAttr,
          deploymentType,
          updatedBy: userId,
        },
        detailType:
          env.config.AWS.eventBus.renditions.rules.updateCustomVariables,
        source: 'putCustomAttributes',
      };
      if (meta) {
        eventBridgeParams.data.meta = req.body.meta;
      }
      return AWS.sendEventsToEventBridge(eventBridgeParams);
    });
    await Promise.all(events);
  }
  return next();
};

const sendEventToDeploymentReport = async (req, _res, next) => {
  const companyId = req.user.company.id;
  const entityType = req.params.entity;
  const updateItems = req.body;

  const { body } = req;
  const userId = req.user.sub;

  const [{ attributeDefinitionId: merchantIdAttributeDefinitionId }] =
    await getCustomAttributeDefinitionByName(
      companyId,
      ['Merchant ID'],
      supportedEntityTypesInternalCode.sites
    );

  if (Array.isArray(body)) {
    const givenEntityIds = updateItems
      .flatMap(item => item.entityId)
      .filter(Boolean);

    const givenIntegrationIds = updateItems
      .flatMap(item => item.integrationId)
      .filter(Boolean);

    const existingEntityMeta = await getEntityIdsAndIntegrationIds(
      userId,
      companyId,
      givenEntityIds,
      givenIntegrationIds,
      entityType
    );

    const events = body.map(
      ({ isStaged, attributes, entityId, integrationId }) => {
        const entityIdInt = existingEntityMeta.find(
          em => em.entityId === entityId || em.integrationId === integrationId
        ).entityId;

        const merchantIdAttribute = attributes.find(
          ({ attributeDefinitionId }) =>
            merchantIdAttributeDefinitionId === attributeDefinitionId
        );
        if (!isStaged && merchantIdAttribute) {
          return AWS.sendEventsToEventBridge({
            data: {
              siteId: entityId || entityIdInt,
              merchantId: merchantIdAttribute.value,
            },
            detailType:
              env.config.AWS.eventBus.deploymentReport.rules
                .updateSiteMerchantId,
            source: 'deployment-report',
          });
        }
        return undefined;
      }
    );

    await Promise.all(events);
  } else {
    const { entityIds, attributes } = body;

    const merchantIdAttribute = attributes.find(
      ({ attributeDefinitionId }) =>
        merchantIdAttributeDefinitionId === attributeDefinitionId
    );

    if (merchantIdAttribute) {
      logger.info(
        `Sending event to deployment report for merchantId:${JSON.stringify(merchantIdAttribute)},siteIds: ${JSON.stringify(entityIds)}`
      );
      const events = entityIds
        .filter(entityId => entityId)
        .map(entityId =>
          AWS.sendEventsToEventBridge({
            data: {
              siteId: entityId,
              merchantId: merchantIdAttribute.value,
            },
            detailType:
              env.config.AWS.eventBus.deploymentReport.rules
                .updateSiteMerchantId,
            source: 'deployment-report',
          })
        );
      await Promise.allSettled(events);
    } else {
      logger.info(`MerchantId attribute not found in the attributes`);
    }
  }

  return next();
};

const getCustomAttributeValuesList = async (req, res, next) => {
  try {
    const {
      user: {
        company: { id: companyId },
      },
    } = req;

    const { pageIndex, pageSize, offset } =
      paginationHelper.parsePaginationParams({
        query: {
          ...req.body,
          pageSize:
            req.body.pageSize === undefined
              ? customAttributesDefaultPageSize
              : req.body.pageSize,
        },
      });

    const { attribute, searchText } = req.body;

    const attributeValueListResponse = await getCustomAttributeValuesByTenant(
      !isEmpty(req.body.tenantId) ? req.body.tenantId : companyId,
      attribute,
      pageSize,
      offset,
      searchText
    );

    if (!attributeValueListResponse) {
      return next(
        new restifyErrors.NotFoundError(
          'Custom Attributes do not exist for company'
        )
      );
    }

    const results = {
      resultsMetadata: {
        totalResults: attributeValueListResponse.totalCount,
        pageIndex,
        pageSize,
      },
      results: attributeValueListResponse.results || [],
    };

    res.send(results);

    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

module.exports = {
  getCustomAttributeDefinitions,
  getCustomAttributes,
  getCustomAttributesInternal,
  getCustomAttributeStagedStatus,
  putCustomAttributes,
  putBulkCustomAttributes,
  putImportCustomAttributes,
  putStagedAttributesApply,
  sendEventToEventBridgeOnCustomAttributesUpdate,
  sendEventOnUpdateCustomAttribute,
  sendEventToDeploymentReport,
  sendEventToDeploymentReportOnUpdateCustomAttribute,
  getCustomAttributeDuplicateSiteNameStatus,
  getCustomAttributeValuesList,
};
