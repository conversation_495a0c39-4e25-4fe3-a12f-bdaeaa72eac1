const CONSTANTS = {
  NotificationTypes: {
    FILE_UPLOAD: 'file-upload',
    FILE_UPLOAD_READY: {
      key: 'file-upload-ready',
      message: '%s is ready to download',
      level: 'SUCCESS',
      emailSubject: 'Completed - File Upload Request (%s)',
    },
    FILE_UPLOAD_IN_PROGRESS: {
      key: 'file-upload-in-progress',
      message:
        "%s is now processing. We will send you a notification when it's ready to download",
      level: 'INFO',
      emailSubject: 'In Progress - File Upload Request (%s)',
    },
    FILE_UPLOAD_NO_FILES: {
      key: 'file-upload-no-files',
      message: '%s does not contain any files',
      level: 'WARN',
      emailSubject: 'Completed - File Upload Request (%s)',
    },
  },
  EmailNotificationNameConstants: {
    RECIPIENT: 'recipient',
    ALARM_NAME: 'alarmName',
    RULE_NAME: 'ruleName',
    DEVICE_NAME: 'deviceName',
    DEVICE_SERIAL: 'serialNumber',
    SITE_NAME: 'siteName',
    TIMESTAMP: 'timestamp',
    STATE: 'state',
    URL: 'urlEnvironment',
    COMPANY_NAME: 'companyName',
    RULE_ID: 'ruleId',
    TYPE: 'type',
    RKI_REQUEST_NAME: 'rkiRequestName',
    RKI_NUM_DEVICES: 'rkiNumDevices',
    RKI_REQUEST_CREATOR_NAME: 'rkiRequestCreatorName',
    RKI_REQUEST_STATUS: 'rkiRequestStatus',
    RKI_DEVICE_STATUS: 'rkiDeviceStatus',
    VERSION_COMPONENT: 'component',
    VERSION_VALUE: 'version',
    FILE_UPLOAD_REQUEST_NAME: 'fileUploadRequestName',
  },
  UploadRequestStatus: {
    NEW: 0,
    IN_PROGRESS: 1,
    COMPLETE: 2,
    FAILED: 3,
    CANCELLED: 4,
  },
  FileUploadRequestTmpDownloadDirectory: 'upload',
  DeviceFiles: {
    EPS_SCHEMA: {
      REQ_PARAM_SCHEMA: 'reqParamSchema',
    },
    EPS_FIELDS: {
      DISPLAY_PROPERTIES: 'displayProperties',
      SIZE_ESTIMATE: 'sizeEstimate',
      MAX_AGE_ESTIMATE: 'maxAgeEstimate',
      JOB_PARAM_SCHEMA_ID: 'jobParamSchemaId',
    },
  },
  FileDownloadTypes: {
    SINGLE_DEVICE: 'singleDevice',
    SINGLE_SITE: 'singleSite',
    SITE_TAGS: 'siteTags',
  },
};

module.exports = CONSTANTS;
