const { server } = require('../../../../app');
const {
  DAY_IN_SECONDS,
  DEVICE_FILES_JOB_PARAM_SCHEMAS,
} = require('../../../../lib/app-constants');
const { default: RedisClient } = require('../../../../lib/redis');
const {
  gLibCompress,
  gLibDecompress,
} = require('../../../../helpers/zlip-helper');

/**
 * Get specific device file from the given id
 * @param {Number} deviceId
 * @returns
 */
const getDeviceFiles = async deviceId => {
  const params = [deviceId];
  const result = await server.db.read.rows(
    `
        SELECT
            id, device_path, application_id, current_hash, content_type, writeable, size_estimate, max_age_estimate, job_param_schema_id, display_properties
        FROM
            device_files
        WHERE
            device_id = $1;
    `,
    params
  );
  return result;
};

/**
 * Get a specific device files by given id only if it has a job
 * @param {Number} deviceId
 * @returns
 */
const getDeviceFilesWithJobStatus = async (deviceId, userId) => {
  const params = [deviceId, userId];
  const result = await server.db.read.rows(
    `
        SELECT df.id, df.device_path, df.content_type, df.writeable, df.last_pulled_timestamp,
            df.application_id, df.current_hash, df.last_pulled_hash, df.latest_upload_job, j.status AS job_status,
            df.display_properties
            FROM device_files AS df
            LEFT JOIN job AS j 
            ON j.id = df.latest_upload_job 
            AND j.device_id = df.device_id
            INNER JOIN target AS d
            ON d.target_id = df.device_id
            INNER JOIN user_site_authorization AS usa
            ON usa.site_id = d.site_id
            WHERE df.device_id = $1 AND d.active = true
            AND (EXISTS (
                SELECT 1
                FROM
                jsonb_array_elements(df.display_properties) AS item
                WHERE
                item ->> 'visibility' = 'files'
            ) 
            OR df.display_properties IS NULL)
            AND NOT d.presence = 'OUT_OF_INSTANCE' 
            AND usa.user_id = $2;
    `,
    params
  );
  return result;
};

/**
 *
 * @param {*} targetId
 * @returns
 */
const findDeviceAndAuthorization = async (targetId, userId) => {
  const params = [targetId, userId];
  const result = await server.db.read.row(
    `
        SELECT d.target_id AS id, usa.user_id
        FROM target AS d 
        JOIN user_site_authorization AS usa ON usa.site_id = d.site_id 
        WHERE d.active = TRUE AND d.target_id = $1 AND usa.user_id = $2;`,
    params
  );
  return result;
};
/**
 * Delete a device file entry where it matches the given device file id and path
 * @param {*} connection
 * @param {Number} deviceId
 * @param {String} devicePath
 * @returns
 */
const deleteDeviceFile = async (connection, deviceId, devicePath) => {
  const params = [deviceId, devicePath];
  const result = await connection.execute(
    `
        DELETE FROM device_files 
        WHERE device_id = $1 AND device_path = $2;
    `,
    params
  );
  return result;
};

/**
 * Save a new device file entry
 * @param {*} connection
 * @param {Object} file
 * @param {Number} deviceId
 * @returns
 */
const saveDeviceFile = async (connection, file, deviceId) => {
  const params = [
    file.filePath,
    file.contentType,
    file.writeable,
    file.hash,
    file.applicationId,
    file.timestamp,
    deviceId,
    file.sizeEstimate,
    file.maxAgeEstimate,
    file.jobParamSchemaId,
    JSON.stringify(file.displayProperties),
  ];
  const result = await connection.execute(
    `
        INSERT INTO
            device_files (
                device_id,
                device_path,
                content_type,
                writeable,
                current_hash,
                application_id,
                timestamp,
                size_estimate,
                max_age_estimate,
                job_param_schema_id,
                display_properties
            ) 
        VALUES ($7, $1, $2, $3, $4, $5, $6, $8, $9, $10, $11::jsonb );
    `,
    params
  );
  return result;
};

/**
 * Update a device file entry by the given id parameter
 * @param {*} connection
 * @param {Object} deviceFile
 * @param {Number} id
 * @returns
 */
const updateDeviceFile = async (connection, deviceFile, id) => {
  const params = [
    deviceFile.contentType,
    deviceFile.writeable,
    deviceFile.hash,
    deviceFile.timestamp,
    deviceFile.applicationId,
    deviceFile.sizeEstimate,
    deviceFile.maxAgeEstimate,
    deviceFile.jobParamSchemaId,
    JSON.stringify(deviceFile.displayProperties),
    id,
  ];
  const result = await connection.execute(
    `
        UPDATE
            device_files
        SET
            content_type = $1,
            writeable = $2,
            current_hash = $3,
            timestamp = $4,
            application_id = $5,
            size_estimate = $6,
            max_age_estimate = $7,
            job_param_schema_id = $8,
            display_properties = $9::jsonb
        WHERE
            id = $10;
    `,
    params
  );
  return result;
};

/**
 * Get the device file detail's last pulled path by the given device file id and device id
 * @param {Number} deviceId
 * @param {Number} fileId
 * @returns
 */
const getDeviceFileDetails = async (deviceId, fileId) => {
  const queryParams = [deviceId, fileId];
  const result = await server.db.read.row(
    `
        SELECT 
            last_pulled_path
        FROM device_files d 
        WHERE 
            d.device_id=$1 AND
            d.id=$2;
    `,
    queryParams
  );
  return result;
};

/**
 * Get device file's id, path and application id by the device file id
 * @param {Number} id
 * @returns
 */
const getDeviceFileByDeviceFileId = async id => {
  const queryParams = [id];
  const result = await server.db.read.row(
    `
        SELECT 
            d.device_id,
            d.device_path,
            d.application_id
        FROM device_files d 
        WHERE 
            d.id=$1
    `,
    queryParams
  );
  return result;
};

/**
 * Save a new file entry into the DB.
 * @param {*} connection
 * @param {Object} file
 * @returns
 */
const saveFile = async (connection, file) => {
  const params = [
    file.id,
    file.customerId,
    file.applicationId,
    file.description,
    file.device.id,
    file.fileUrl,
    file.fileName,
    file.created,
    file.createdBy,
    file.installable,
    file.fileSignature,
  ];
  const result = await connection.execute(
    `
        INSERT INTO file( id, customer_id, application_id, description, device_id, file_url, file_name, created, created_by, is_installable, file_signature ) 
        VALUES ( $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11);
        `,
    params
  );
  return result;
};

/**
 * Updates the device file latest_upload_job property by it's given id
 * @param {*} connection
 * @param {String} uploadJobId
 * @param {Number} deviceFileId
 * @returns
 */
const updateDeviceFileWithLatestUploadJob = async (
  connection,
  uploadJobId,
  deviceFileId
) => {
  const params = [uploadJobId, deviceFileId];
  const result = await connection.execute(
    `
                UPDATE device_files SET 
                latest_upload_job = $1 
                WHERE id = $2;
            `,
    params
  );
  return result;
};

/**
 * Save a new device file with a lastest_upload_job entry
 * @param {*} connection
 * @param {Number} deviceId
 * @param {String} applicationId
 * @param {String} filePath
 * @param {*} uploadJobId
 * @param {String} fileUrl
 * @param {Date} timestamp
 * @returns
 */
const saveDeviceFileWithLatestUploadJob = async (
  connection,
  deviceId,
  applicationId,
  filePath,
  uploadJobId,
  fileUrl,
  timestamp
) => {
  const params = [
    deviceId,
    applicationId,
    filePath,
    uploadJobId,
    fileUrl,
    timestamp,
  ];
  const result = await connection.execute(
    `
        INSERT INTO device_files( device_id, application_id, device_path, latest_upload_job, last_pulled_path, timestamp ) 
        VALUES ( $1, $2, $3, $4, $5, $6 );
    `,
    params
  );
  return result;
};

/**
 * Returns the `id` of a schema
 * @todo move to a its own folder
 * @param {String} reqParamSchema
 * @returns {Number} id
 */
const getDeviceFileJobParamSchemaIdByName = async () => {
  const result = await server.db.replica.rows(
    `
        SELECT
            id, name
        FROM
            device_files_job_param_schemas
        WHERE deleted = false;
    `
  );
  return result;
};

const getAllDeviceFileJobParamSchemaIdByNamesInCache = async () => {
  const redisClient = RedisClient.getInstance();
  try {
    const schemaNameCacheValues = await redisClient.getValue(
      `${DEVICE_FILES_JOB_PARAM_SCHEMAS}:allSchemaNames`
    );
    if (schemaNameCacheValues) {
      const schemaNameValues = await gLibDecompress(schemaNameCacheValues);
      return JSON.parse(schemaNameValues) || [];
    }
    const queryResults = await getDeviceFileJobParamSchemaIdByName();
    const compressedValues = await gLibCompress(JSON.stringify(queryResults));
    await redisClient.saveKeyValueWithTTL(
      `${DEVICE_FILES_JOB_PARAM_SCHEMAS}:allSchemaNames`,
      compressedValues,
      7 * DAY_IN_SECONDS
    );
    return queryResults || [];
  } catch (error) {
    throw new Error(
      `Error in device file job param schemaId by names from redis cache ${error}`
    );
  }
};

async function getDeviceFileId(connection, deviceId, devicePath) {
  const query = `
          SELECT * FROM device_files
            WHERE device_id = $1
            AND device_path = $2;
          `;
  const params = [deviceId, devicePath];
  const result = await connection.execute(query, params);
  return result && result.rows && result.rows.length ? result.rows[0] : null;
}

module.exports = {
  getDeviceFileDetails,
  getDeviceFiles,
  getDeviceFilesWithJobStatus,
  updateDeviceFile,
  deleteDeviceFile,
  saveDeviceFile,
  findDeviceAndAuthorization,
  saveFile,
  updateDeviceFileWithLatestUploadJob,
  saveDeviceFileWithLatestUploadJob,
  getDeviceFileByDeviceFileId,
  getAllDeviceFileJobParamSchemaIdByNamesInCache,
  getDeviceFileId,
};
