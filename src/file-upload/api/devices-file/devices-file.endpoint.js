const Joi = require('joi');
const { server } = require('../../../../app');
const env = require('../../../../env');
const {
  requiresRole,
  validateParams,
  validateQuery,
  validateBody,
} = require('../../../../lib/pre');
const { roles, positiveInteger } = require('../../../../lib/app-constants');
const handler = require('./devices-file.handler');

const BASE_PATH = `${env.config.base}/devices`;
const VERSION = '1.0.0';

/**
 * @swagger
 *   "/devices/{id}/files/{fileId}/content":
 *     get:
 *       tags:
 *         - devices
 *       summary: "Get the content of the specified file."
 *       description: "This allows to retrieve the content of the specified file from a device/target"
 *       parameters:
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *         - $ref: '#/parameters/deviceIdParam'
 *         - $ref: '#/parameters/fileIdParam'
 *       responses:
 *         "200":
 *           description: "File content in response body"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "404":
 *           description: "File not found at specified path (path is invalid or lastPulledPath is null)"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 *         "599":
 *           description: "File content type invalid. (allowed types: text/*, application/json)"
 */
server.get(
  {
    path: `${BASE_PATH}/:id/files/:fileId/content`,
    version: VERSION,
  },
  [
    requiresRole([
      roles.COMPANY_ADMIN,
      roles.POWER_USER,
      roles.ICS_SYSTEM,
      roles.ANALYST,
      roles.SPECIALIST,
      roles.USER,
    ]),
    validateParams({
      id: Joi.number()
        .integer()
        .min(positiveInteger.MIN_VALUE)
        .max(positiveInteger.MAX_VALUE),
      fileId: Joi.number()
        .integer()
        .min(positiveInteger.MIN_VALUE)
        .max(positiveInteger.MAX_VALUE),
    }),
    handler.getFileContent,
  ]
);

/**
 * @swagger
 *   "/devices/{id}/uploadfile":
 *     post:
 *       tags:
 *         - devices
 *       summary: "Accept a file upload from a device based on a file upload job."
 *       description: "The file will be uploaded to an AWS S3 bucket."
 *       produces:
 *         - "text/plain"
 *       consumes:
 *         - "multipart/form-data"
 *       parameters:
 *         - $ref: '#/parameters/deviceAuthTokenParam'
 *         - $ref: '#/parameters/deviceIdParam'
 *         - $ref: '#/parameters/fileUploadRequestAppParam'
 *         - in: formData
 *           name: file
 *           description: The file path to be uploaded
 *           type: file
 *           required: true
 *         - $ref: '#/parameters/fileUploadRequestPathParam'
 *       responses:
 *         "204":
 *           description: "File was uploaded successfully"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "405":
 *           description: "Wrong method. Must use POST"
 *         "413":
 *           description: "Uploaded file is larger than the maximum allowed limit"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/:id/uploadfile`,
    version: VERSION,
  },
  [
    requiresRole([roles.DEVICE]),
    validateParams({
      id: Joi.number()
        .integer()
        .min(positiveInteger.MIN_VALUE)
        .max(positiveInteger.MAX_VALUE),
    }),
    validateQuery({
      app: Joi.string(),
      filepath: Joi.string(),
    }),
    handler.uploadDeviceFile,
  ]
);

/**
 * @swagger
 *   "/devices/{id}/files":
 *     get:
 *       tags:
 *         - devices
 *       summary: "Get the list of uploadable files"
 *       description: "This allows to retrieve the list of uploadable files for a target/device"
 *       produces:
 *         - "application/json"
 *       parameters:
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *         - $ref: '#/parameters/deviceIdParam'
 *       responses:
 *         "200":
 *           description: "Uploadable files list successfully retrieved"
 *           schema:
 *             $ref: "#/definitions/DeviceFileRetrieveList"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "404":
 *           description: "Can't find the target device for the given id"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/:id/files`,
    version: VERSION,
  },
  [
    requiresRole([
      roles.USER,
      roles.COMPANY_ADMIN,
      roles.POWER_USER,
      roles.ICS_SYSTEM,
      roles.ANALYST,
      roles.SPECIALIST,
    ]),
    validateParams({
      id: Joi.number()
        .integer()
        .min(positiveInteger.MIN_VALUE)
        .max(positiveInteger.MAX_VALUE),
    }),
    handler.getDeviceFiles,
  ]
);

/**
 * @swagger
 *   "/devices/{id}/files":
 *     post:
 *       tags:
 *         - devices
 *       summary: "Set the list of uploadable files"
 *       description: "This allows to set the list of uploadable files for a target/device"
 *       produces:
 *         - "application/json"
 *       parameters:
 *         - $ref: '#/parameters/deviceIdParam'
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *         - name: body
 *           in: body
 *           description: List of files that are uploadable from device/target
 *           required: true
 *           schema:
 *             $ref: '#/definitions/DeviceFileCreateList'
 *       responses:
 *         "204":
 *           description: "Uploadable file list successfully set"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "404":
 *           description: "Can't find the target device for the given id"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/:id/files`,
    version: VERSION,
  },
  [
    requiresRole([
      roles.COMPANY_ADMIN,
      roles.POWER_USER,
      roles.ICS_SYSTEM,
      roles.ANALYST,
      roles.SPECIALIST,
    ]),
    validateParams({
      id: Joi.number()
        .integer()
        .min(positiveInteger.MIN_VALUE)
        .max(positiveInteger.MAX_VALUE),
    }),
    validateBody(
      Joi.array().items({
        filePath: Joi.string().required(),
        contentType: Joi.string().allow(null).allow('').optional(),
        hash: Joi.string().allow(null).allow('').optional(),
        timestamp: Joi.date().required(),
        applicationId: Joi.string().required(),
        reqParamSchema: Joi.string().allow(null).allow('').optional(),
        displayProperties: Joi.array().allow(null).allow('').optional(),
        sizeEstimate: Joi.number().optional().integer().min(0).allow(null),
        maxAgeEstimate: Joi.number().optional().integer().min(0).allow(null),
      })
    ),
    handler.setDeviceFilesList,
  ]
);

/**
 * @swagger
 *   "/devices/{id}/files/copy":
 *     put:
 *       tags:
 *         - devices
 *       summary: "Copy file for a device"
 *       description: "Copy files to the device from a source device"
 *       parameters:
 *         - $ref: "#/parameters/deviceIdParam"
 *         - $ref: "#/parameters/AuthorizationTokenParam"
 *         - name: body
 *           in: body
 *           description: "The copy payload of the device. Files payload cannot be empty and sourceDeviceId is mandatory"
 *           required: true
 *           schema:
 *             $ref: "#/definitions/CopyFileRequest"
 *       responses:
 *         "204":
 *           description: "Copy file successfully"
 *         "400":
 *           description: "Bad Request. Files payload might be missing"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "404":
 *           description: "Id may be null or can't find the target device for the given id"
 *         "409":
 *           description: "Source file might not be pulled yet or files payload might be empty"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.put(
  {
    path: `${BASE_PATH}/:id/files/copy`,
    version: VERSION,
  },
  [
    requiresRole([
      roles.COMPANY_ADMIN,
      roles.POWER_USER,
      roles.ANALYST,
      roles.SPECIALIST,
    ]),
    validateParams({
      id: Joi.number()
        .integer()
        .min(positiveInteger.MIN_VALUE)
        .max(positiveInteger.MAX_VALUE),
    }),
    validateBody({
      sourceDeviceId: Joi.number()
        .integer()
        .min(positiveInteger.MIN_VALUE)
        .max(positiveInteger.MAX_VALUE),
      files: Joi.array().items({
        applicationId: Joi.string(),
        path: Joi.string(),
      }),
    }),
    handler.copyDeviceFiles,
  ]
);

/**
 * @swagger
 *   "/devices/{id}/files/{deviceFileId}/save":
 *     post:
 *       tags:
 *         - devices
 *       summary: "Save content of the specified file."
 *       description: "Save a file for delivery to the device"
 *       consumes:
 *         - "text/plain; charset=utf-8"
 *       parameters:
 *         - $ref: "#/parameters/AuthorizationTokenParam"
 *         - $ref: "#/parameters/deviceIdParam"
 *         - name: deviceFileId
 *           in: path
 *           description: "The file id"
 *           required: true
 *           type: integer
 *         - name: body
 *           in: body
 *           description: "The content of the file"
 *           required: true
 *           schema:
 *             type: string
 *       responses:
 *         "204":
 *           description: "Save file successfully"
 *         "400":
 *           description: "Bad Request."
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "404":
 *           description: "Id may be null or can't find the target device for the given id"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/:id/files/:deviceFileId/save`,
    version: VERSION,
  },
  [
    requiresRole([
      roles.COMPANY_ADMIN,
      roles.POWER_USER,
      roles.ANALYST,
      roles.SPECIALIST,
    ]),
    validateParams({
      id: Joi.number()
        .min(positiveInteger.MIN_VALUE)
        .max(positiveInteger.MAX_VALUE),
      deviceFileId: Joi.number()
        .integer()
        .min(positiveInteger.MIN_VALUE)
        .max(positiveInteger.MAX_VALUE),
    }),
    validateBody(Joi.string()),
    handler.saveFile,
  ]
);
