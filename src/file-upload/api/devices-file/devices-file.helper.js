const path = require('path');
const errors = require('restify-errors');

const jobHelper = require('../../../../helpers/job-helper');
const { roles, crypto } = require('../../../../lib/app-constants');
const { config } = require('../../../../env');
const stringUtils = require('../../../../lib/string-utils');
const commonHelper =
  require('../devices-file-and-fileupload-request-common/helper').public;

/**
 * Returns true if the statusId is less than the statusId that is equal to complete;
 * @param {Number} statusId
 * @returns {<PERSON>olean}
 */
const isNotInTerminalState = statusId =>
  statusId < jobHelper.jobStatus.COMPLETE;

/**
 * Build device object from given parameters
 * @param {Number} id
 * @param {String} filePath
 * @param {*} contentType
 * @param {*} writeable
 * @param {String} lastPulled
 * @param {String} applicationId
 * @param {*} changed
 * @param {*} pullRequestQueued
 * @param {*} displayProperties
 * @returns
 */
const buildDevice = (
  id,
  filePath,
  contentType,
  writeable,
  lastPulled,
  applicationId,
  changed,
  pullRequestQueued,
  displayProperties
) => ({
  id,
  filePath,
  contentType,
  writeable,
  lastPulled,
  applicationId,
  changed,
  pullRequestQueued,
  displayProperties,
});

/**
 * Returns false if userRoles array contains ICS_SYSTEM role, else true.
 * @param {Array} userRoles
 * @returns
 */
const isHumanUser = userRoles => {
  if (userRoles.find(role => role === roles.ICS_SYSTEM)) {
    return false;
  }
  return true;
};

/**
 * Generate a new filename from the path and uuid
 * @param {String} filePath
 * @param {String} fileUUID
 * @returns
 */
const generateNewFilename = (filePath, fileUUID) => {
  const fileExtension = path.extname(filePath);
  const baseFilename = path.basename(filePath, fileExtension);

  let newFilename = `${baseFilename}-${fileUUID}`;
  if (fileExtension) {
    newFilename = `${newFilename}${fileExtension}`;
  }
  return newFilename;
};

/**
 * Builds the job and returns the job data as object
 * @param {Number} version
 * @param {String} fileURI
 * @param {String} fileHash
 * @param {String} fileName
 * @returns {Object}
 */
const buildJobData = (version, fileURI, fileHash, fileName) => ({
  version,
  fileURI,
  fileHash,
  fileName,
  fileHashAlg: crypto.SHA256,
});

/**
 * Builds the file object from given parameters and returns it
 * @param {string} id
 * @param {String} customerId
 * @param {String} applicationId
 * @param {String} description
 * @param {Number} device
 * @param {String} fileUrl
 * @param {String} fileName
 * @param {String} createdBy
 * @param {*} fileSignature
 * @returns
 */
const buildFile = (
  id,
  customerId,
  applicationId,
  description,
  device,
  fileUrl,
  fileName,
  createdBy,
  fileSignature
) => ({
  id,
  customerId,
  applicationId,
  description,
  device,
  fileUrl,
  fileName,
  created: new Date(),
  createdBy,
  installable: false,
  fileSignature,
});

/**
 * Builds S3 configurations from the last pulled path string.
 * @param {String} lastPulledPath
 * @returns
 */
const buildS3Details = lastPulledPath => {
  const s3Bucket = config.fileUpload.bucket;
  const s3Key = lastPulledPath
    ? decodeURI(stringUtils.getPathName(lastPulledPath))
    : null;
  return {
    Bucket: s3Bucket,
    Key: s3Key,
  };
};

/**
 * Builds the s3 configuration object to send file to s3 with the device file properties
 * @param {Object} deviceFile
 * @returns
 */
const buildS3ConfigWithPath = deviceFile => {
  const s3Key = decodeURI(stringUtils.getPathName(deviceFile.lastPulledPath));
  const s3Bucket = config.fileUpload.bucket;
  if (!s3Bucket) {
    throw new errors.NotFoundError(
      'S3 bucket configuration for file upload not found'
    );
  }

  // eslint-disable-next-line no-param-reassign
  const fileName = stringUtils.getFileName(s3Key);
  const s3Config = {
    s3Bucket,
    s3Key,
    fileName,
  };
  return s3Config;
};

/**
 * Generate the s3FilePath from the given arguments
 * @param {String} filePath
 * @param {UUID} fileUUID
 * @param {Object} device
 * @param {String} appId
 * @param {String} s3FileRootPath
 * @param {Number} deviceId
 * @returns {String} s3FilePath
 */
const getS3FilePath = (
  filePath,
  fileUUID,
  device,
  appId,
  s3FileRootPath,
  deviceId
) => {
  const parentPath = path.dirname(filePath);
  const newFileName = generateNewFilename(filePath, fileUUID);
  const s3Path = commonHelper.generateS3UploadFilePath(
    device,
    parentPath,
    appId,
    s3FileRootPath,
    null,
    deviceId
  );
  const s3FilePath = s3Path.concat('/', newFileName);
  return s3FilePath;
};

module.exports.public = {
  isNotInTerminalState,
  buildDevice,
  isHumanUser,
  generateNewFilename,
  buildJobData,
  buildFile,
  buildS3Details,
  buildS3ConfigWithPath,
  getS3FilePath,
};

module.exports.private = {};
