const errorHandler = require('../../../../lib/errorhandler');
const { httpCode } = require('../../../../lib/app-constants');
const stringUtils = require('../../../../lib/string-utils');
const service = require('./devices-file.service').public;

function parseNullableBoolean(value) {
  if (typeof value === 'boolean') {
    return value;
  }
  if (!value) {
    return null;
  }
  if (value === '') {
    return null;
  }
  return stringUtils.equalsIgnoreCase(value, 'true');
}

module.exports = {
  async getFileContent(req, res, next) {
    try {
      const deviceId = req.params.id;
      const { fileId } = req.params;
      await service.deviceFileContentRetrieval(deviceId, fileId, res);
      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },
  async uploadDeviceFile(req, res, next) {
    try {
      const deviceId = req.params.id;
      const appId = req.params.app;
      const filePath = req.params.filepath;
      await service.deviceFileUploader(req, deviceId, filePath, appId);
      res.send(httpCode.NO_CONTENT.STATUS_CODE);
      return next();
    } catch (e) {
      return errorHandler.onError(req, res, next)(e);
    }
  },
  async getDeviceFiles(req, res, next) {
    try {
      const deviceId = req.params.id;
      const userId = req.user.sub;
      const deviceFilesWithRequiredProperties =
        await service.getDeviceFilesWithRequiredProperties(deviceId, userId);
      res.send(httpCode.OK.STATUS_CODE, deviceFilesWithRequiredProperties);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  async setDeviceFilesList(req, res, next) {
    const deviceId = req.params.id;
    const fileListReq = req.body;
    const userRoles = req.user.getRoles();
    try {
      const fileList = fileListReq.map(file => ({
        ...file,
        timestamp: new Date(file.timestamp),
        writeable: parseNullableBoolean(file.writeable),
      }));
      await service.setDeviceFilesList(deviceId, fileList, userRoles);
      res.send(
        httpCode.NO_CONTENT.STATUS_CODE,
        'Uploadable files list successfully set'
      );
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  async copyDeviceFiles(req, res, next) {
    const context = {
      deviceId: req.params.id,
      deviceFilesToCopy: req.body,
      userRoles: req.user.getRoles(),
      userId: req.user.sub,
      userEmail: req.user.email,
      companyId: req.user.company.id,
    };
    try {
      await service.copyDeviceFiles(context);
      res.send(httpCode.NO_CONTENT.STATUS_CODE);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  async saveFile(req, res, next) {
    try {
      const context = {
        content: req.body,
        companyId: req.user.company.id,
        userEmail: req.user.email,
        userId: req.user.sub,
        currentDateTime: new Date(),
        deviceId: req.params.id,
        deviceFileId: req.params.deviceFileId,
      };
      await service.save(context);
      res.send(httpCode.NO_CONTENT.STATUS_CODE);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
