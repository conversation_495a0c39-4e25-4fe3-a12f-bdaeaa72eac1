const path = require('path');
const fs = require('fs');
const stream = require('stream');
const { createHash } = require('crypto');
const uuid = require('uuid/v4');
const errors = require('restify-errors');
const _ = require('lodash');

const { server } = require('../../../../app');
const AWS = require('../../../../lib/aws');
const downloadHelper = require('../../../../helpers/download-helper');
const jobHelper = require('../../../../helpers/job-helper');
const { config } = require('../../../../env');
const stringUtils = require('../../../../lib/string-utils');
const {
  pathSeperator,
  dbTransaction,
  crypto,
  s3Error,
  devices,
  roles,
} = require('../../../../lib/app-constants');
const fileUploadCommonHelper =
  require('../devices-file-and-fileupload-request-common/helper').public;
const constants = require('../../lib/constants');
const commonRepository = require('../devices-file-and-fileupload-request-common/repository');
const commonService =
  require('../devices-file-and-fileupload-request-common/service').public;
const commonHelper =
  require('../devices-file-and-fileupload-request-common/helper').public;
const logger = require('../../../../lib/logger').mainLogger();
const repository = require('./devices-file.repository');
const helper = require('./devices-file.helper').public;

const s3DeviceFileRootPath = 'device-files';
const s3FileRootPath = 'files';

/**
 * Checks to see if a device file matches a file
 * @param {Object} deviceFile
 * @param {Object} file
 */
const isFileMatch = (deviceFile, file) => {
  if (
    stringUtils.equalsIgnoreCase(deviceFile.devicePath, file.filePath) &&
    stringUtils.equalsIgnoreCase(deviceFile.applicationId, file.applicationId)
  ) {
    if (deviceFile.currentHash && file.hash) {
      return stringUtils.equalsIgnoreCase(deviceFile.currentHash, file.hash);
    }
    return true;
  }
  return false;
};

/**
 * Finds a device file from a list of device files using the file path and application Id
 * @param {Object} deviceFile
 * @param {Array} fileList
 */
const findDeviceFileInList = (deviceFile, fileList) => {
  // eslint-disable-next-line no-restricted-syntax
  for (const file of fileList) {
    if (isFileMatch(deviceFile, file)) {
      return file;
    }
  }
  return null;
};

/**
 *
 * @param {*} deviceId
 * @returns
 */
const getDeviceFilesWithRequiredProperties = async (deviceId, userId) => {
  const deviceFiles = await repository.getDeviceFilesWithJobStatus(
    deviceId,
    userId
  );

  const deviceFilesWithRequiredProperties = deviceFiles.map(deviceFile => {
    let pullRequestQueued = false;
    let changed = false;
    if (deviceFile.currentHash && deviceFile.lastPulledHash) {
      if (
        !stringUtils.equalsIgnoreCase(
          deviceFile.currentHash,
          deviceFile.lastPulledHash
        )
      ) {
        changed = true;
      }
    }

    if (deviceFile.latestUploadJob) {
      pullRequestQueued = helper.isNotInTerminalState(deviceFile.jobStatus);
    }

    const deviceFileWithRequiredProperties = helper.buildDevice(
      deviceFile.id,
      deviceFile.devicePath,
      deviceFile.contentType,
      deviceFile.writeable,
      deviceFile.lastPulledTimestamp
        ? new Date(deviceFile.lastPulledTimestamp).getTime()
        : deviceFile.lastPulledTimestamp,
      deviceFile.applicationId,
      changed,
      pullRequestQueued,
      deviceFile.displayProperties
    );
    return deviceFileWithRequiredProperties;
  });
  return deviceFilesWithRequiredProperties;
};

/**
 * Checks if device payload has updated properties
 * @param {*} deviceFile Device data from DB
 * @param {*} deviceFileFoundInList Device data from payload
 * @returns {Boolean}
 */
const deviceHasPropertiesUpdated = (deviceFile, deviceFileFoundInList) => {
  let hasPropertiesUpdated = false;

  if (deviceFileFoundInList.hash !== deviceFile.currentHash) {
    hasPropertiesUpdated = true;
  }

  if (
    deviceFileFoundInList.reqParamSchema !== null &&
    deviceFile.jobParamSchema == null
  ) {
    hasPropertiesUpdated = true;
  }

  if (
    deviceFileFoundInList.writeable !== deviceFile.writeable ||
    !stringUtils.equalsIgnoreCase(
      deviceFileFoundInList.contentType,
      deviceFile.contentType
    )
  ) {
    hasPropertiesUpdated = true;
  }

  const epsFields = Object.values(constants.DeviceFiles.EPS_FIELDS);
  epsFields.forEach(epsField => {
    if (epsField === constants.DeviceFiles.EPS_FIELDS.DISPLAY_PROPERTIES) {
      if (!_.isEqual(deviceFile[epsField], deviceFileFoundInList[epsField])) {
        hasPropertiesUpdated = true;
      }
    } else if (deviceFile[epsField] !== deviceFileFoundInList[epsField]) {
      hasPropertiesUpdated = true;
    }
  });

  return hasPropertiesUpdated;
};

/**
 *
 * @param {*} connection
 * @param {*} deviceId
 * @param {*} deviceFiles Device records from DB
 * @param {*} fileList Recently uploaded device records
 */
const setDeviceFilesListUsingTransaction = async (
  connection,
  deviceId,
  deviceFiles,
  fileList
) => {
  await connection.execute(dbTransaction.BEGIN);
  try {
    const uniqueFileKeys = new Set();
    const uniqueFileList = [];

    fileList.forEach(file => {
      const key = `${file.applicationId}-${file.filePath}`;
      if (!uniqueFileKeys.has(key)) {
        uniqueFileKeys.add(key);
        uniqueFileList.push(file);
      }
    });
    const filePromises = deviceFiles
      .map(deviceFile => {
        const deviceFileFoundInList = findDeviceFileInList(
          deviceFile,
          uniqueFileList
        );

        if (deviceFileFoundInList) {
          return deviceHasPropertiesUpdated(deviceFile, deviceFileFoundInList)
            ? repository.updateDeviceFile(
                connection,
                deviceFileFoundInList,
                deviceFile.id
              )
            : null;
        }

        return repository.deleteDeviceFile(
          connection,
          deviceId,
          deviceFile.devicePath
        );
      })
      .concat(
        uniqueFileList.map(async file => {
          const deviceFilesContainsFile = deviceFiles.some(deviceFile =>
            isFileMatch(deviceFile, file)
          );

          return !deviceFilesContainsFile
            ? repository.saveDeviceFile(connection, file, deviceId)
            : null;
        })
      );
    await Promise.all(filePromises);
    await connection.execute(dbTransaction.COMMIT);
  } catch (e) {
    await connection.execute(dbTransaction.ROLLBACK);
    throw e;
  } finally {
    connection.done();
  }
};

/**
 * Returns an enriched fileList. This process includes the following
 *  - remove unused "reqParamSchema" sent by the device
 *  - insert "jobParamSchemaId" with a mapped schemaId
 *  - sets a default key/value for EPS fields not sent by the device. Backward compatibility
 * @param {*} fileList
 * @returns {Promise<Array>} fileList a new array
 */
const getEnrichedFileList = async fileList => {
  const files = [];
  const filesWithReqParamSchemaValues = [];
  // Classify files into two groups in a single loop
  fileList.forEach(file => {
    if (!(constants.DeviceFiles.EPS_SCHEMA.REQ_PARAM_SCHEMA in file)) {
      files.push(file);
    } else if (file[constants.DeviceFiles.EPS_SCHEMA.REQ_PARAM_SCHEMA]) {
      filesWithReqParamSchemaValues.push(file);
    }
  });
  const jobParamSchemaResults =
    await repository.getAllDeviceFileJobParamSchemaIdByNamesInCache();
  const schemaResultMap = jobParamSchemaResults.reduce((acc, result) => {
    acc[result.name] = result.id;
    return acc;
  }, {});

  filesWithReqParamSchemaValues.forEach(file => {
    const schemaName = file[constants.DeviceFiles.EPS_SCHEMA.REQ_PARAM_SCHEMA];
    /* eslint-disable no-param-reassign */
    file[constants.DeviceFiles.EPS_FIELDS.JOB_PARAM_SCHEMA_ID] =
      schemaResultMap[schemaName] ?? null;
    delete file[constants.DeviceFiles.EPS_SCHEMA.REQ_PARAM_SCHEMA];
  });

  return [...files, ...filesWithReqParamSchemaValues];
};

/**
 *
 * @param {*} deviceId
 * @param {*} fileList
 */
const setDeviceFilesList = async (deviceId, fileList, userRoles) => {
  if (userRoles && !userRoles.includes(roles.ICS_SYSTEM)) {
    logger.warn(
      `User role: ${userRoles} does not include 'ICS SYSTEM' for device: ${deviceId}.`
    );
    const device = await commonRepository.getDeviceById(deviceId);
    if (!device) {
      throw new errors.NotFoundError(
        `Device not found for device id: ${deviceId}`
      );
    }
  }

  const enrichedFileList = await getEnrichedFileList(fileList);
  const deviceFiles = await repository.getDeviceFiles(deviceId);
  const connection = await server.db.write.getConnection();
  await setDeviceFilesListUsingTransaction(
    connection,
    deviceId,
    deviceFiles,
    enrichedFileList
  );
};

/**
 *
 * @param {*} targetId
 * @param {*} userId
 * @returns
 */
const getDevice = async (targetId, userId) => {
  const device = await repository.findDeviceAndAuthorization(targetId, userId);
  if (!device) {
    throw new errors.NotFoundError(
      `Cannot find device/target with id ${targetId} that is authorized to the user`
    );
  }
  return device;
};

/**
 * Return userId parameter if userRoles contains a human role
 * @param {Array} userRoles
 * @param {uuid} userId
 * @returns
 */
const getHumanUserId = (userRoles, userId) =>
  helper.isHumanUser(userRoles) ? userId : null;

/**
 * Creates a temporary root folder for the s3 files to be stored locally
 * @param {*} s3TmpRootFolder
 * @returns
 */
const ensureS3TmpUploadFolder = async s3TmpRootFolder => {
  if (!fs.existsSync(s3TmpRootFolder)) {
    await commonService.makeTmpS3DownloadDir(s3TmpRootFolder);
  }
  const s3TemporaryUploadFolder = path.join(
    pathSeperator,
    s3TmpRootFolder,
    constants.FileUploadRequestTmpDownloadDirectory
  );
  if (!fs.existsSync(s3TemporaryUploadFolder)) {
    await commonService.makeTmpS3DownloadDir(s3TemporaryUploadFolder);
  }
  return s3TemporaryUploadFolder;
};

/**
 *
 * @param {*} newFilename
 * @param {*} s3TemporaryRootFolder
 * @returns
 */
const createTmpUploadFilePath = async (newFilename, s3TemporaryRootFolder) => {
  // Ensure s3 tmp folder exists
  if (!s3TemporaryRootFolder) {
    throw new errors.NotFoundError(
      'Cannot find configuration for the S3 temporary upload directory'
    );
  }
  const s3TemporaryUploadFolder = await ensureS3TmpUploadFolder(
    s3TemporaryRootFolder
  );
  const uploadTempFilePath = path.join(
    pathSeperator,
    s3TemporaryUploadFolder,
    newFilename
  );
  return uploadTempFilePath;
};

/**
 * Builds an install job entry ready to send to the DB.
 * @param {*} connection
 * @param {Object} fileEntity
 * @param {Object} destinationDevice
 * @param {String} applicationId
 * @param {String} currentUTCDateTime
 * @param {String} expiry
 * @param {String} userId
 * @returns
 */
const createInstallJob = async (
  connection,
  fileEntity,
  destinationDevice,
  applicationId,
  currentUTCDateTime,
  expiry,
  userId
) => {
  const apiDomain = config.url.api;
  const fileInstallJobUrl = `${apiDomain}/files/${fileEntity.id}/content`;

  // install job
  const jobDataVersion = 1;
  const jobData = helper.buildJobData(
    jobDataVersion,
    fileInstallJobUrl,
    fileEntity.fileSignature,
    fileEntity.fileName
  );
  const jsonJobData = JSON.stringify(jobData);
  const job = jobHelper.buildJob(
    destinationDevice.id,
    applicationId,
    jobHelper.jobType.FILE_INSTALL,
    jsonJobData,
    currentUTCDateTime,
    expiry,
    userId
  );

  const installJob = await jobHelper.createJob(connection, job);
  return installJob;
};

/**
 * Builds a fileupload job ready to send to the DB.
 * @param {*} connection
 * @param {Object} destinationDevice
 * @param {String} applicationId
 * @param {String} filePath
 * @param {Object} fileEntity
 * @param {String} currentUTCDateTime
 * @param {String} expiry
 * @param {String} userId
 * @returns the new fileupload job entry sent to the DB.
 */
const createFileUploadJob = async (
  connection,
  destinationDevice,
  applicationId,
  filePath,
  fileEntity,
  currentUTCDateTime,
  expiry,
  userId
) => {
  // Create a file upload job for the same file on the same device that depends on the file install job
  const fileUploadJobDataVersion = 1;
  const urlPathBeforeId = 'devices';
  const urlPathAfterId = 'uploadfile';
  const fileUploadJobData = fileUploadCommonHelper.buildFileUploadJobData(
    fileUploadJobDataVersion,
    destinationDevice.id,
    applicationId,
    filePath,
    fileEntity.fileName,
    urlPathBeforeId,
    urlPathAfterId
  );
  const jsonFileUploadJobData = JSON.stringify(fileUploadJobData);
  const fileUploadJob = jobHelper.buildJob(
    destinationDevice.id,
    applicationId,
    jobHelper.jobType.FILE_UPLOAD,
    jsonFileUploadJobData,
    currentUTCDateTime,
    expiry,
    userId
  );

  const newFileUploadJob = await jobHelper.createJob(connection, fileUploadJob);
  return newFileUploadJob;
};

/**
 * Builds a job dependency entry ready to be send to the DB.
 * @param {*} connection
 * @param {Object} newFileUploadJob
 * @param {Object} installJob
 */
const createJobDependency = async (
  connection,
  newFileUploadJob,
  installJob
) => {
  const jobDependency = {
    jobId: newFileUploadJob.id,
    dependensOn: installJob.id,
    continueOnFail: false,
  };
  await jobHelper.saveJobDependency(connection, jobDependency);
};

/**
 * Prepares parameters to make calls to build install job, fileupload job, and job dependency
 * @param {*} connection
 * @param {Object} destinationDevice
 * @param {String} filePath
 * @param {String} applicationId
 * @param {Object} fileEntity
 * @param {String} userId
 * @param {Date} currentDateTime
 * @returns
 */
const createInstallAndUploadJobs = async (
  connection,
  destinationDevice,
  filePath,
  applicationId,
  fileEntity,
  userId,
  currentDateTime
) => {
  const expirationInSeconds = 2 * 60 * 60;
  const expiry = fileUploadCommonHelper.getExpiryDateTime(
    currentDateTime,
    expirationInSeconds,
    'seconds'
  );
  const currentUTCDateTime =
    fileUploadCommonHelper.getUTCTimestamp(currentDateTime);

  const promises = [
    createInstallJob(
      connection,
      fileEntity,
      destinationDevice,
      applicationId,
      currentUTCDateTime,
      expiry,
      userId
    ),
    createFileUploadJob(
      connection,
      destinationDevice,
      applicationId,
      filePath,
      fileEntity,
      currentUTCDateTime,
      expiry,
      userId
    ),
  ];

  const [installJob, newFileUploadJob] = await Promise.all(promises);
  await createJobDependency(connection, newFileUploadJob, installJob);
  return newFileUploadJob;
};

/**
 * Gets the device id to update it's fileupload job id column.
 * @param {Object} connection
 * @param {Number} deviceId
 * @param {String} filePath
 * @param {String} applicationId
 * @param {String} fileUrl
 * @param {String} uploadJobId
 * @param {Date} timestamp
 * @returns {Promise<Object>}
 */
const setDeviceFileWithLatestUploadJob = async (
  connection,
  deviceId,
  filePath,
  applicationId,
  fileUrl,
  uploadJobId,
  timestamp
) => {
  const deviceFile = await commonRepository.getDeviceFile(
    deviceId,
    filePath,
    applicationId
  );
  if (deviceFile) {
    return repository.updateDeviceFileWithLatestUploadJob(
      connection,
      uploadJobId,
      deviceFile.id
    );
  }

  return repository.saveDeviceFileWithLatestUploadJob(
    connection,
    deviceId,
    applicationId,
    filePath,
    uploadJobId,
    fileUrl,
    timestamp
  );
};

/**
 * Pipes the s3FileStream into a writeStream to generate the SHA256 value to return.
 * @param {String} uploadTmpFilePath
 * @param {Object} lastPulledPathFile
 * @returns
 */
const getSHA256HashByUploadingToTmpDir = (
  uploadTmpFilePath,
  lastPulledPathFile
) => {
  const sha256 = createHash(crypto.SHA256);
  const writeStream = fs.createWriteStream(uploadTmpFilePath);
  const s3FileStream = lastPulledPathFile.createReadStream();
  return new Promise((resolve, reject) => {
    s3FileStream
      .on('data', chunk => {
        sha256.update(chunk);
      })
      .on('error', err => {
        reject(err);
      })
      .pipe(writeStream);

    writeStream.on('error', err => {
      reject(err);
    });
    writeStream.on('finish', () => {
      resolve(sha256.digest('hex'));
    });
  });
};

/**
 *
 * @param {*} filePath
 * @param {*} fileUUID
 * @returns
 */
const createUploadTmpFilePath = async (filePath, fileUUID) => {
  const newFilename = helper.generateNewFilename(filePath, fileUUID);
  const s3TemporaryRootFolder = config.s3UploadTmpDir;
  const uploadTmpFilePath = await createTmpUploadFilePath(
    newFilename,
    s3TemporaryRootFolder
  );
  return uploadTmpFilePath;
};

/**
 *
 * @param {*} uploadTmpFilePath
 * @param {*} lastPulledPath
 * @returns
 */
const downloadAndGetHashOfLastPulledPathFile = async (
  uploadTmpFilePath,
  lastPulledPath
) => {
  const s3Details = helper.buildS3Details(lastPulledPath);
  const lastPulledPathFile = AWS.downloadFromS3(s3Details);
  let sha256hash = null;
  try {
    sha256hash = await getSHA256HashByUploadingToTmpDir(
      uploadTmpFilePath,
      lastPulledPathFile
    );
  } catch (err) {
    try {
      await fs.promises.unlink(uploadTmpFilePath);
    } catch (e) {
      throw new errors.InternalServerError(
        `Failed to unlink the file from temporary directory ${uploadTmpFilePath}`
      );
    }
    if (err.code === s3Error.NO_SUCH_BUCKET.CODE) {
      throw new errors.NotFoundError(s3Error.NO_SUCH_BUCKET.MESSAGE);
    } else if (err.code === s3Error.NO_SUCH_KEY.CODE) {
      throw new errors.NotFoundError(s3Error.NO_SUCH_KEY.MESSAGE);
    }
    throw new errors.InternalServerError(
      `Failed to read file from ${lastPulledPath}. ${err}`
    );
  }
  return sha256hash;
};

/**
 * Retrieves device details from DB, puts together all required S3
 * configurations and calls the downloadPackageFromS3 function
 * @param {Number} deviceId
 * @param {Number} fileId
 */
const getDeviceFile = async (deviceId, fileId) => {
  const deviceFile = await repository.getDeviceFileDetails(deviceId, fileId);
  if (!deviceFile) {
    throw new errors.NotFoundError(
      `DeviceFile with device ID: ${deviceId} and file ID: ${fileId} does not exist or not ready for download`
    );
  }

  if (!deviceFile.lastPulledPath) {
    throw new errors.NotFoundError();
  }
  return deviceFile;
};

/**
 * Calls extername downloadPackage function that downloads file and returns stream piped into response
 * Also handles internal service and s3 errors
 * @param {*} res
 * @param {Object} s3Config
 */
const downloadFromS3 = async (res, s3Config) => {
  try {
    await downloadHelper.downloadPackageFromS3(res, s3Config);
  } catch (error) {
    if (error.code === s3Error.NO_SUCH_KEY.CODE) {
      throw new errors.NotFoundError(s3Error.NO_SUCH_KEY.MESSAGE);
    } else if (error.code === s3Error.NO_SUCH_BUCKET.CODE) {
      throw new errors.NotFoundError(s3Error.NO_SUCH_BUCKET.MESSAGE);
    }
    throw new errors.InternalServerError(
      'Could not download device file content.'
    );
  }
};

/**
 * Handles the function calls for the retrieval of device file, s3 config build and download of file from s3
 * @param {Number} deviceId
 * @param {Number} fileId
 * @param {*} res
 */
const deviceFileContentRetrieval = async (deviceId, fileId, res) => {
  const deviceFile = await getDeviceFile(deviceId, fileId);
  const s3Config = helper.buildS3ConfigWithPath(deviceFile);
  await downloadFromS3(res, s3Config);
};

/**
 * Retrieves the device file upload dependencies from db; device, device file and site
 * @param {Number} deviceId
 * @param {String} filePath
 * @param {String} appId
 * @returns
 */
const getDeviceFileUploadDependencies = async (deviceId, filePath, appId) => {
  const [device, deviceFile] = await Promise.all([
    commonRepository.getDeviceById(deviceId),
    commonRepository.getDeviceFile(deviceId, filePath, appId),
  ]);

  if (!device) {
    throw new errors.NotFoundError(
      `Device with device ID: ${deviceId} does not exist`
    );
  }
  if (!deviceFile) {
    throw new errors.NotFoundError(
      `DeviceFile with device ID: ${deviceId}, ` +
        `filePath: ${filePath} and appId: ${appId} does not exist or not ready for download`
    );
  }

  const { siteId } = device;
  const site = await commonRepository.getSiteDetails(siteId);
  if (!site) {
    throw new errors.NotFoundError(
      `Site with site ID: ${siteId} does not exist`
    );
  }
  const uploadDependencies = {
    device,
    deviceFile,
    site,
  };
  return uploadDependencies;
};

/**
 * Handles the function calls to retrieve device file upload dependencies, s3 file path and then uploading of device file
 * @param {*} req
 * @param {Number} deviceId
 * @param {String} filePath
 * @param {String} appId
 */
const deviceFileUploader = async (req, deviceId, filePath, appId) => {
  const uploadDependencies = await getDeviceFileUploadDependencies(
    deviceId,
    filePath,
    appId
  );
  const s3FilePath = commonHelper.generateS3UploadFilePath(
    uploadDependencies.device,
    filePath,
    appId,
    s3DeviceFileRootPath,
    uploadDependencies.site.name
  );
  await commonService.uploadFile(
    req,
    filePath,
    s3FilePath,
    uploadDependencies.deviceFile,
    false,
    config.fileUpload
  );
};

/**
 *
 * @param {*} connection
 * @param {*} destinationDevice
 * @param {*} sourceDevice
 * @param {*} context
 */
const copyDeviceFilesUsingTransaction = async (
  connection,
  destinationDevice,
  sourceDevice,
  context
) => {
  const currentDateTime = new Date();
  await connection.execute(dbTransaction.BEGIN);
  try {
    // eslint-disable-next-line no-restricted-syntax
    for (const file of context.deviceFilesToCopy.files) {
      // eslint-disable-next-line no-await-in-loop
      const sourceDeviceFile = await commonRepository.getDeviceFile(
        sourceDevice.id,
        file.path,
        file.applicationId
      );
      if (!sourceDeviceFile) {
        throw new errors.NotFoundError(
          `Cannot find the source device file for device id ${sourceDevice.id} with file application id ` +
            `${file.applicationId} and file path ${file.path}`
        );
      }

      if (!sourceDeviceFile.lastPulledPath) {
        throw new errors.ConflictError(
          `Can't copy file because it hasn't been pulled yet, for device id ${sourceDevice.id} with file application id ` +
            `${file.applicationId} and file path ${file.path}`
        );
      }

      // Create an entry for destination target in files table
      const fileUUID = uuid();
      // eslint-disable-next-line no-await-in-loop
      const uploadTmpFilePath = await createUploadTmpFilePath(
        file.path,
        fileUUID
      );
      // eslint-disable-next-line no-await-in-loop
      const sha256hash = await downloadAndGetHashOfLastPulledPathFile(
        uploadTmpFilePath,
        sourceDeviceFile.lastPulledPath
      );

      const description = `Copy file ${file.path} from device: ${sourceDevice.id} to device: ${destinationDevice.id}`;

      // Create file entity for destinationDevice but use source lastPulledPath as fileUrl
      const fileEntity = helper.buildFile(
        fileUUID,
        context.companyId,
        file.applicationId,
        description,
        destinationDevice,
        sourceDeviceFile.lastPulledPath,
        file.path,
        context.userEmail,
        sha256hash
      );
      // eslint-disable-next-line no-await-in-loop
      await repository.saveFile(connection, fileEntity);
      // eslint-disable-next-line no-await-in-loop
      await fs.promises.unlink(uploadTmpFilePath);

      // create install and upload job for destinationDevice
      // eslint-disable-next-line no-await-in-loop
      const uploadJob = await createInstallAndUploadJobs(
        connection,
        destinationDevice,
        file.path,
        file.applicationId,
        fileEntity,
        context.userId,
        currentDateTime
      );

      // Update DeviceFile last upload job to be the upload job created if device file exists, else creates a new device file
      // eslint-disable-next-line no-await-in-loop
      await setDeviceFileWithLatestUploadJob(
        connection,
        destinationDevice.id,
        file.path,
        file.applicationId,
        null,
        uploadJob.id,
        currentDateTime
      );
    }
    await connection.execute(dbTransaction.COMMIT);
  } catch (e) {
    await connection.execute(dbTransaction.ROLLBACK);
    throw e;
  } finally {
    connection.done();
  }
};

/**
 *
 * @param {*} context
 */
const copyDeviceFiles = async context => {
  const humanUserId = getHumanUserId(context.userRoles, context.userId);
  const promises = [
    getDevice(context.deviceId, humanUserId),
    getDevice(context.deviceFilesToCopy.sourceDeviceId, humanUserId),
  ];
  const [destinationDevice, sourceDevice] = await Promise.all(promises);

  if (!context.deviceFilesToCopy.files) {
    throw new errors.ConflictError(
      "Can't copy file because files payload is empty"
    );
  }

  const connection = await server.db.write.getConnection();
  await copyDeviceFilesUsingTransaction(
    connection,
    destinationDevice,
    sourceDevice,
    context
  );
};

/**
 * Save device file in database
 * @param {Object} fileEntity
 * @param {Object} device
 * @param {String} filePath
 * @param {String} appId
 * @param {String} fileUrl
 * @param {String} userId
 * @param {Date} currentDateTime
 * @returns
 */
const saveDeviceFileEntity = async (
  fileEntity,
  device,
  filePath,
  appId,
  fileUrl,
  userId,
  currentDateTime
) => {
  const connection = await server.db.write.getConnection();
  try {
    await connection.execute(dbTransaction.BEGIN);

    // Create file entity for device but use source lastPulledPath as fileUrl
    await repository.saveFile(connection, fileEntity);
    // create install and upload job for device
    const uploadJob = await createInstallAndUploadJobs(
      connection,
      device,
      filePath,
      appId,
      fileEntity,
      userId,
      currentDateTime
    );
    // Update DeviceFile last upload job to be the upload job created if device file exists, else creates a new device file
    await setDeviceFileWithLatestUploadJob(
      connection,
      device.id,
      filePath,
      appId,
      fileUrl,
      uploadJob.id,
      currentDateTime
    );

    return await connection.execute(dbTransaction.COMMIT);
  } catch (error) {
    await connection.execute(dbTransaction.ROLLBACK);
    throw error;
  } finally {
    connection.done();
  }
};

/**
 * Retrieve the device and device file from db
 * @param {*} deviceId
 * @param {*} deviceFileId
 * @returns
 */
const getDeviceAndDeviceFile = async (deviceId, deviceFileId) => {
  const [device, deviceFile] = await Promise.all([
    commonRepository.getDeviceById(deviceId),
    repository.getDeviceFileByDeviceFileId(deviceFileId),
  ]);
  if (!deviceFile) {
    throw new errors.NotFoundError(
      `Device file with ID: ${deviceFileId} does not exist`
    );
  }
  if (
    !device ||
    !device.active ||
    (!device.presence
      ? false
      : device.presence.toString() !== devices.PRESENCE.PRESENT)
  ) {
    throw new errors.NotFoundError(
      `Device ${deviceId} not found or not present`
    );
  }
  // eslint-disable-next-line radix
  if (parseInt(deviceId) !== deviceFile.deviceId) {
    throw new errors.ForbiddenError(
      `Device ${deviceId} has no permissions for file ${deviceFileId}`
    );
  }
  return [device, deviceFile];
};

/**
 * Create file stream from request body to upload to s3 then update device file entry in DB.
 * @param {String} content
 * @param {String} s3Bucket
 * @param {String} s3FilePath
 */
const createStreamAndSendToS3 = async (content, s3Bucket, s3FilePath) => {
  const sha256 = createHash(crypto.SHA256);
  let sha256hash = null;
  const fileStream = new stream.Readable();
  fileStream._read = () => {}; // eslint-disable-line no-underscore-dangle
  fileStream.push(content);
  fileStream.push(null);
  fileStream
    .on('data', data => {
      sha256.update(data);
    })
    .on('end', () => {
      sha256hash = sha256.digest('hex');
    });

  if (!config.fileUpload) {
    throw new errors.NotFoundError(
      'S3 configuration for file upload not found'
    );
  }
  await AWS.uploadToS3(s3Bucket, s3FilePath, fileStream);
  return sha256hash;
};

/**
 * Prepare device file row to be saved
 * @param {*} context
 */
const save = async context => {
  const { content } = context;
  const { companyId } = context;
  const { userEmail } = context;
  const { userId } = context;
  const { currentDateTime } = context;
  const { deviceId } = context;
  const { deviceFileId } = context;
  const [device, deviceFile] = await getDeviceAndDeviceFile(
    deviceId,
    deviceFileId
  );

  const filePath = deviceFile.devicePath;
  const appId = deviceFile.applicationId;
  const fileUUID = uuid();
  const s3FilePath = helper.getS3FilePath(
    filePath,
    fileUUID,
    device,
    appId,
    s3FileRootPath,
    deviceId
  );

  const s3Bucket = config.fileUpload.bucket;
  if (!s3Bucket) {
    throw new errors.NotFoundError(
      'S3 bucket configuration for file upload not found'
    );
  }

  const sha256hash = await createStreamAndSendToS3(
    content,
    s3Bucket,
    s3FilePath
  );

  const fileUrl = `s3://${s3Bucket}/${s3FilePath}`;
  const description = `upload file ${filePath} for device ${deviceId}`;
  const fileEntity = helper.buildFile(
    fileUUID,
    companyId,
    appId,
    description,
    device,
    fileUrl,
    filePath,
    userEmail,
    sha256hash
  );
  await saveDeviceFileEntity(
    fileEntity,
    device,
    filePath,
    appId,
    fileUrl,
    userId,
    currentDateTime
  );
};

module.exports.public = {
  getDeviceFilesWithRequiredProperties,
  setDeviceFilesList,
  copyDeviceFiles,
  save,
  deviceFileContentRetrieval,
  deviceFileUploader,
};

module.exports.private = {
  findDeviceFileInList,
  getDevice,
  getHumanUserId,
  createTmpUploadFilePath,
  getSHA256HashByUploadingToTmpDir,
  createUploadTmpFilePath,
  downloadAndGetHashOfLastPulledPathFile,
  ensureS3TmpUploadFolder,
  createInstallJob,
  createFileUploadJob,
  createJobDependency,
  saveDeviceFileEntity,
  setDeviceFilesListUsingTransaction,
  copyDeviceFilesUsingTransaction,
  getDeviceFile,
  downloadFromS3,
  getDeviceFileUploadDependencies,
  getDeviceAndDeviceFile,
  setDeviceFileWithLatestUploadJob,
  createInstallAndUploadJobs,
  createStreamAndSendToS3,
  getEnrichedFileList,
};
