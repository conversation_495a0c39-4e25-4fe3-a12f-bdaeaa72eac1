const fs = require('fs');
const stream = require('stream');
const { createHash } = require('crypto');
const formidable = require('formidable');
const errors = require('restify-errors');
const { server } = require('../../../../app');
const { pathSeperator, crypto } = require('../../../../lib/app-constants');
const AWS = require('../../../../lib/aws');
const logger = require('../../../../lib/logger').mainLogger();
const helper = require('./helper').public;
const repository = require('./repository');

/**
 * Recursively create directories that do not exist in the path
 * @param {String} uploadPath
 */
const makeTmpS3DownloadDir = async uploadPath => {
  await fs.promises.mkdir(uploadPath, { recursive: true });
};

/**
 * Constructs deviceFile to save in database
 * @param {Object} deviceFile
 * @param {String} s3Bucket
 * @param {String} s3FilePath
 * @param {*} sha256
 * @returns
 */
const setDeviceFileDetails = async (
  deviceFile,
  s3Bucket,
  s3FilePath,
  sha256
) => {
  const deviceFileEntity = helper.updateDeviceFileEntity(
    deviceFile,
    s3Bucket,
    s3FilePath,
    sha256
  );
  logger.info(
    `[setDeviceFileDetails] deviceFileEntity id:${deviceFileEntity?.id} | lastPulledHash: ${deviceFileEntity?.lastPulledHash} | currentHash: ${deviceFileEntity?.currentHash}`
  );
  return repository.setDeviceFileDetails(deviceFileEntity);
};

/**
 * Handles multi media form part, generates sha256 hash and uploads the stream to s3
 * @param {*} part
 * @param {String} s3Bucket
 * @param {String} s3FilePath
 * @returns
 */
const getHashAndUpload = async (part, s3Bucket, s3FilePath) => {
  try {
    let sha256 = null;
    const hash = createHash(crypto.SHA256);
    const s3stream = new stream.PassThrough();

    s3stream.on('data', data => {
      if (data) {
        hash.update(data);
      }
    });

    s3stream.on('end', () => {
      sha256 = hash.digest('hex');
    });
    part.pipe(s3stream);

    s3stream.on('error', err => {
      logger.error(`[getHashAndUpload] s3stream.on.error: ${err}`);
      throw err;
    });

    if (s3FilePath.endsWith(pathSeperator)) {
      await AWS.s3PutObject(s3Bucket, s3FilePath);
      return sha256;
    }

    await AWS.uploadToS3(s3Bucket, s3FilePath, s3stream);
    logger.info(
      `[getHashAndUpload] section s3FilePath: ${s3FilePath} s3Bucket: ${s3Bucket} - Upload complete.`
    );
    return sha256;
  } catch (error) {
    server.log.error(
      `[getHashAndUpload] error: ${error} s3FilePath: ${s3FilePath} s3Bucket: ${s3Bucket}`
    );
    throw error;
  }
};

/**
 * Takes an incoming media type and creates a readStream to send to S3 the updates the DB device file entry
 * @param {Object} req
 * @param {String} filePath
 * @param {String} s3FilePath
 * @param {Object} deviceFile
 * @param {Boolean} isFileUploadRequest
 * @returns
 */
const uploadFile = async (
  req,
  filePath,
  s3FilePath,
  deviceFile,
  isFileUploadRequest,
  fileUploadConfig
) => {
  if (!fileUploadConfig) {
    throw new errors.NotFoundError(
      'S3 configuration for file upload not found'
    );
  }
  const s3Bucket = fileUploadConfig.bucket;
  if (!s3Bucket) {
    throw new errors.NotFoundError(
      'S3 bucket configuration for file upload not found'
    );
  }

  logger.info(
    `[uploadFile] called for filePath: ${filePath} s3FilePath: ${s3FilePath} s3Bucket: ${s3Bucket}`
  );

  let s3FilePathTemp = s3FilePath;

  const form = new formidable.IncomingForm();
  return new Promise((resolve, reject) => {
    form.parse(req);
    let isKeyProvided = false;
    form.onPart = async part => {
      isKeyProvided = true;
      if (!part.name || part.name.trim() === '') {
        logger.error(`[uploadFile] The file key is mandatory`);
        return reject(new errors.BadRequestError('The file key is mandatory'));
      }
      const expectedKeyName = 'file';
      if (part.name !== expectedKeyName) {
        logger.error(
          `[uploadFile] Invalid key name: expected '${expectedKeyName}', got '${part.name}'`
        );
        return reject(
          new errors.BadRequestError(
            `Invalid key name: expected '${expectedKeyName}', got '${part.name}'`
          )
        );
      }
      if (!part.filename) {
        form.handlePart(part);
        logger.error(`[uploadFile] A proper file needs to be uploaded`);
        return reject(
          new errors.BadRequestError('A proper file needs to be uploaded')
        );
      }
      // Check if filepath is a folder (ends with /), if yes use incoming Filename as s3FilePath
      if (filePath.endsWith(pathSeperator) && isFileUploadRequest) {
        s3FilePathTemp = s3FilePath.concat(part.filename);
      }
      try {
        const sha256 = await getHashAndUpload(part, s3Bucket, s3FilePathTemp);
        return resolve(sha256);
      } catch (error) {
        logger.error(`[uploadFile] error: ${error}`);
        return reject(error);
      }
    };
    form.on('end', () => {
      if (!isKeyProvided) {
        logger.error(`[uploadFile] The file key is mandatory`);
        reject(new errors.BadRequestError('The file key is mandatory'));
      }
    });
  })
    .then(async sha256 => {
      await setDeviceFileDetails(deviceFile, s3Bucket, s3FilePathTemp, sha256);
    })
    .catch(err => {
      logger.error(`[uploadFile] error catch: ${err}`);
      throw err;
    });
};

module.exports.public = {
  makeTmpS3DownloadDir,
  uploadFile,
};

module.exports.private = {
  getHashAndUpload,
  setDeviceFileDetails,
};
