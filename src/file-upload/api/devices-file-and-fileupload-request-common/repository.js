const { server } = require('../../../../app');

/**
 * Retreives a device with the provided id from the database
 * @param {Number} deviceId
 * @returns
 */
const getDeviceById = async deviceId => {
  const params = [deviceId];
  const result = await server.db.read.row(
    `
        SELECT 
            target_id as id,
            active,
            presence,
            site_id,
            name,
            serial_number
        FROM target 
        WHERE target_id = $1;
    `,
    params
  );
  return result;
};

/**
 * Get a specific device file with given device id, device path and application id if provided.
 * @param {Number} deviceId
 * @param {String} filePath
 * @param {String} appId
 * @returns
 */
const getDeviceFile = async (deviceId, filePath = null, appId = null) => {
  const queryParams = [deviceId, filePath];
  const whereClauseStringBuilder = ['d.device_id=$1 AND d.device_path=$2'];
  if (appId) {
    queryParams.push(appId);
    whereClauseStringBuilder.push(' AND d.application_id=$3');
  }
  const whereClause = whereClauseStringBuilder.join('');
  const result = await server.db.read.row(
    `
        SELECT 
            d.id,
            d.timestamp,
            d.current_hash,
            d.last_pulled_hash,
            d.last_pulled_timestamp,
            d.last_pulled_path
        FROM device_files d 
        WHERE ${whereClause};
    `,
    queryParams
  );
  return result;
};

/**
 * Get a site's name by id
 * @param {String} siteId
 * @returns
 */
const getSiteDetails = async siteId => {
  const queryParams = [siteId];
  const result = await server.db.read.row(
    `
        SELECT 
            name
        FROM site s 
        WHERE 
            s.site_id=$1 ;
    `,
    queryParams
  );
  return result;
};

/**
 * Update device file entries given columns by id
 * @param {Object} deviceFile
 * @returns
 */
const setDeviceFileDetails = async deviceFile => {
  const queryParams = [
    deviceFile.id,
    deviceFile.lastPulledTimestamp,
    deviceFile.lastPulledPath,
    deviceFile.lastPulledHash,
  ];
  let counter = queryParams.length;
  const setClauseStringBuilder = [
    'last_pulled_timestamp = $2',
    'last_pulled_path = $3',
    'last_pulled_hash = $4',
  ];
  if (deviceFile.timestamp) {
    counter += 1;
    queryParams.push(deviceFile.timestamp);
    setClauseStringBuilder.push(`timestamp = $${counter}`);
  }
  if (deviceFile.currentHash) {
    counter += 1;
    queryParams.push(deviceFile.currentHash);
    setClauseStringBuilder.push(`current_hash = $${counter}`);
  }
  const setClause = setClauseStringBuilder.join(',');
  const result = await server.db.read.row(
    `
        UPDATE device_files
        SET ${setClause}
        WHERE device_files.id = $1;
    `,
    queryParams
  );
  return result;
};

module.exports = {
  getDeviceById,
  getDeviceFile,
  getSiteDetails,
  setDeviceFileDetails,
};
