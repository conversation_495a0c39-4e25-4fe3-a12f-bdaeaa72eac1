const timezone = require('moment-timezone');
const moment = require('moment');
const logger = require('../../../../lib/logger').mainLogger();

const { config } = require('../../../../env');
const { pathSeperator } = require('../../../../lib/app-constants');

const EQUAL_UNICODE = '\\u003d';
const AMPERSAND_UNICODE = '\\u0026';

// The unicode escape sequence was added so the generated url is inline
// with the one generateed from java code. Java used gson library which
// chose to esacpe '=' and '&' in json, this was considered a bug but never
// got fixed
// https://groups.google.com/g/google-gson/c/JDHUo9DWyyM
const jobDataToJson = jobData =>
  jobData &&
  JSON.stringify(jobData)
    .replace(/=/g, EQUAL_UNICODE)
    .replace(/&/g, AMPERSAND_UNICODE);

/**
 *
 * @param {*} currentDateTime
 * @param {*} expirationTimeToAdd
 * @param {*} expirationType
 * @returns
 */
const getExpiryDateTime = (
  currentDateTime,
  expirationTimeToAdd,
  expirationType
) => moment(currentDateTime).add(expirationTimeToAdd, expirationType).format();

/**
 * Convert datetime to a UTC timestamp
 * @param {Date} dateTime
 * @returns
 */
const getUTCTimestamp = dateTime => timezone.tz(dateTime, 'UTC').format();

/**
 *
 * @param {*} path
 * @returns
 */
const getEncodedPath = path => encodeURIComponent(path);

/**
 *
 * @param {*} id
 * @param {*} applicationId
 * @param {*} filePath
 * @param {*} urlPathBeforeId
 * @param {*} urlPathAfterId
 * @returns
 */
const buildUrlToUploadDestination = (
  id,
  applicationId,
  filePath,
  urlPathBeforeId,
  urlPathAfterId
) => {
  const apiDomain = config.url.api;
  const encodedApplicationId = getEncodedPath(applicationId);
  const encodedPath = getEncodedPath(filePath);
  return `${apiDomain}/${urlPathBeforeId}/${id}/${urlPathAfterId}?app=${encodedApplicationId}&filepath=${encodedPath}`;
};

/**
 * Builds the file upload job datan ana returns it as an object
 * @param {Number} version
 * @param {*} id
 * @param {String} applicationId
 * @param {String} filePath
 * @param {String} sourcePath
 * @param {String} urlPathBeforeId
 * @param {String} urlPathAfterId
 * @returns {Object}
 */
const buildFileUploadJobData = (
  version,
  id,
  applicationId,
  filePath,
  sourcePath,
  urlPathBeforeId,
  urlPathAfterId,
  additionalProperties = {}
) => {
  const uploadDestination = buildUrlToUploadDestination(
    id,
    applicationId,
    filePath,
    urlPathBeforeId,
    urlPathAfterId
  );
  const jobData = {
    version,
    sourcePath,
    uploadDestination,
    ...additionalProperties,
  };
  return jobData;
};

/**
 * Takes in parameters to update deviceFile before updating the database.
 * Returns the updated deviceFile object
 * @param {Object} deviceFile
 * @param {String} s3Bucket
 * @param {String} s3FilePath
 * @param {String} sha256
 * @returns {Object} deviceFileEntity
 */
const updateDeviceFileEntity = (deviceFile, s3Bucket, s3FilePath, sha256) => {
  const now = new Date();
  const deviceFileEntity = {
    id: deviceFile.id,
    lastPulledPath: `s3://${s3Bucket + pathSeperator + s3FilePath}`,
    lastPulledTimestamp: now,
    lastPulledHash: sha256,
    currentHash: null,
    timestamp: null,
  };
  if (deviceFile.currentHash) {
    deviceFileEntity.currentHash = sha256;
    deviceFileEntity.timestamp = now;
  }
  return deviceFileEntity;
};

/**
 * Takes parameters and concatenates string parameters to return an upload file path
 * @param {Object} device
 * @param {String} filePath
 * @param {String} appId
 * @param {String} s3DeviceFileRootPath
 * @param {String} siteName
 * @param {Number} deviceId
 * @returns {String} s3UploadFilePath
 */
const generateS3UploadFilePath = (
  device,
  filePath,
  appId,
  s3DeviceFileRootPath,
  siteName = null,
  deviceId = null
) => {
  logger.info(
    `[generateS3UploadFilePath] device: name: ${device.name} serialNumber: ${device.serialNumber} deviceId: ${deviceId} filePath: ${filePath} appId: ${appId} s3DeviceFileRootPath: ${s3DeviceFileRootPath} siteName: ${siteName}`
  );
  const s3FilePathTokens = [];
  s3FilePathTokens.push(s3DeviceFileRootPath);

  if (siteName) {
    s3FilePathTokens.push(
      pathSeperator,
      siteName,
      pathSeperator,
      device.serialNumber,
      '-',
      device.name
    );
  }

  s3FilePathTokens.push(pathSeperator, appId);

  if (deviceId) {
    s3FilePathTokens.push(pathSeperator, deviceId);
  }

  if (filePath.startsWith(pathSeperator)) {
    s3FilePathTokens.push(filePath);
  } else {
    s3FilePathTokens.push(pathSeperator, filePath);
  }
  return s3FilePathTokens.join('');
};

module.exports.public = {
  getExpiryDateTime,
  getUTCTimestamp,
  buildFileUploadJobData,
  updateDeviceFileEntity,
  generateS3UploadFilePath,
  jobDataToJson,
};

module.exports.private = {
  getEncodedPath,
  buildUrlToUploadDestination,
};
