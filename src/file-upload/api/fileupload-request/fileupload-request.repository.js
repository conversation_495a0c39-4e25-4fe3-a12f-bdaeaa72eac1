const { server } = require('../../../../app');
const { deviceHealth } = require('../../../../lib/app-constants');
const logger = require('../../../../lib/logger').mainLogger();
/**
 *
 * @param {*} tagIds
 * @param {*} companyId
 * @param {*} userId
 * @param {*} filesQuery
 * @returns
 */
const findSitesByTagIds = async (tagIds, companyId, userId, filesQuery) => {
  const params = [
    tagIds,
    companyId,
    userId,
    deviceHealth.OPERATIONAL.KEY,
    deviceHealth.OUT_OF_SERVICE.KEY,
  ];
  const sql = `
        SELECT DISTINCT s.site_id, d.target_id, df.device_path, df.application_id 
        FROM site AS s 
        JOIN target AS d ON d.site_id = s.site_id 
        JOIN user_site_authorization AS usa ON usa.site_id = s.site_id 
        LEFT JOIN site_tag AS st ON st.site_id = s.site_id AND st.deleted = false
        LEFT JOIN tag AS t ON t.id = st.tag_id 
        JOIN device_files AS df ON df.device_id = d.target_id 
        WHERE s.active = TRUE AND d.active = TRUE AND t.id = ANY($1) AND t.company_id = $2 
        AND get_device_health( d.target_id ) IN ($4, $5)
        AND d.presence = 'PRESENT' AND usa.user_id = $3 ${filesQuery};`;
  const result = await server.db.read.rows(sql, params);
  return result;
};

/**
 *
 * @param {*} userId
 * @param {*} siteId
 * @param {*} filesQuery
 * @returns
 */
const findSitesByIdWithActiveDevices = async (userId, siteId, filesQuery) => {
  const params = [
    userId,
    siteId,
    deviceHealth.OPERATIONAL.KEY,
    deviceHealth.OUT_OF_SERVICE.KEY,
  ];
  const sql = `
        SELECT d.target_id, df.device_path, df.application_id 
        FROM site AS s 
        JOIN target AS d ON d.site_id = s.site_id
        JOIN user_site_authorization AS usa ON usa.site_id = s.site_id
        JOIN device_files AS df ON df.device_id = d.target_id 
        WHERE s.active = TRUE AND d.active = TRUE AND usa.user_id = $1
        AND get_device_health( d.target_id ) IN ($3, $4)
        AND s.site_id = $2 ${filesQuery};`;
  const result = await server.db.read.rows(sql, params);
  return result;
};

/**
 *
 * @param {*} userId
 * @param {*} targetId
 * @param {*} filesQuery
 * @returns
 */
const findTargetsByIdWithDeviceFile = async (userId, targetId, filesQuery) => {
  const params = [
    userId,
    targetId,
    deviceHealth.OPERATIONAL.KEY,
    deviceHealth.OUT_OF_SERVICE.KEY,
  ];
  const sql = `
        SELECT d.target_id, df.device_path, df.application_id
        FROM site AS s
        JOIN target AS d ON d.site_id = s.site_id
        JOIN user_site_authorization AS usa ON usa.site_id = s.site_id
        JOIN device_files AS df ON df.device_id = d.target_id
        WHERE s.active = TRUE AND d.active = TRUE AND usa.user_id = $1
        AND get_device_health( d.target_id ) IN ($3, $4)
        AND d.target_id = $2 ${filesQuery};`;
  const result = await server.db.read.rows(sql, params);
  return result;
};

/**
 *
 * @param {*} targetId
 * @returns
 */
const getDeviceOwner = async targetId => {
  const params = [targetId];
  const result = await server.db.read.row(
    `
        SELECT c.id 
        FROM target AS d 
        JOIN site AS s ON s.site_id = d.site_id 
        JOIN company AS c ON c.id = s.company_id 
        WHERE s.active = TRUE AND d.active = TRUE AND d.target_id = $1;
        `,
    params
  );
  return result;
};

/**
 *
 * @param {*} fileUploadRequestId
 * @returns
 */
const getFileUploadRequest = async fileUploadRequestId => {
  const params = [fileUploadRequestId];
  const result = await server.db.read.row(
    `
        SELECT id, name, completed, created_by, package_url 
        FROM file_upload_request 
        WHERE id = $1;
        `,
    params
  );
  return result;
};

/**
 *
 * @param {*} fileUploadRequestId
 * @returns
 */
const getFileUploadRequestWithJobs = async fileUploadRequestId => {
  const params = [fileUploadRequestId];
  const result = await server.db.read.row(
    `
        SELECT fur.*, 
        COALESCE(
            array_agg(json_build_object( 'id', j.id, 'status', j.status, 'deviceId', j.device_id ))
            FILTER (WHERE j.id IS NOT NULL),
            '{}'
        ) AS jobs
        FROM file_upload_request AS fur 
        LEFT JOIN file_upload_job AS fuj ON fuj.request_id = fur.id 
        LEFT JOIN job AS j ON j.id = fuj.job_id 
        WHERE fur.id = $1 
        GROUP BY fur.id;
        `,
    params
  );
  return result;
};

/**
 * Retrieves the fileupload-requests joined with the job table through fileupload-job table.
 * @returns
 */
const getFileUploadRequestsWithJobs = async () => {
  const result = await server.db.read.rows(
    `
        SELECT f.*
        FROM (
           SELECT fur.*,
           array_agg(json_build_object( 'id', j.id, 'status', j.status, 'deviceId', j.device_id )) AS jobs
           FROM file_upload_request AS fur
           JOIN file_upload_job AS fuj ON fuj.request_id = fur.id
           JOIN job AS j ON j.id = fuj.job_id
           WHERE fur.completed IS NULL AND (
               fur.packaging_in_progress is NULL OR fur.packaging_in_progress IS FALSE
           )
           GROUP BY fur.id
        ) f
        ORDER BY f.created;
        `,
    []
  );
  return result;
};

/**
 *
 * @param {*} fileUploadRequestId
 * @param {*} userId
 * @returns
 */
const getFileUploadNotification = async (fileUploadRequestId, userId) => {
  const params = [fileUploadRequestId, userId];
  const result = await server.db.read.row(
    `
        SELECT * 
        FROM file_upload_notification 
        WHERE request_id = $1 AND user_id = $2;
        `,
    params
  );
  return result;
};

/**
 *
 * @param {*} siteId
 * @returns
 */
const getSiteOwner = async siteId => {
  const params = [siteId];
  const result = await server.db.read.row(
    `
        SELECT c.id 
        FROM site AS s 
        JOIN company AS c ON c.id = s.company_id 
        WHERE s.active = TRUE AND s.site_id = $1;
        `,
    params
  );
  return result;
};

/**
 *
 * @param {*} siteTagId
 * @returns
 */
const getSiteTagOwner = async siteTagId => {
  const params = [siteTagId];
  const result = await server.db.read.row(
    `
        SELECT DISTINCT c.id 
        FROM tag AS t 
        JOIN company AS c ON c.id = t.company_id 
        WHERE t.id = $1;
        `,
    params
  );
  return result;
};

/**
 *
 * @param {*} userId
 * @returns
 */
const getUserCompany = async userId => {
  const params = [userId];
  const result = await server.db.read.row(
    `
        SELECT DISTINCT c.id 
        FROM ics_user AS u 
        JOIN company AS c ON c.id = u.company_id 
        WHERE u.id = $1;
        `,
    params
  );
  return result;
};

/**
 *
 * @param {*} connection
 * @param {*} deviceId
 * @param {*} devicePath
 * @param {*} applicationId
 * @param {*} jobId
 * @returns
 */
const updateDeviceFile = async (
  connection,
  deviceId,
  devicePath,
  applicationId,
  jobId
) => {
  const params = [deviceId, devicePath, applicationId, jobId];
  const result = (
    await connection.execute(
      `
            UPDATE device_files SET latest_upload_job = $4 
            WHERE device_id = $1 AND device_path = $2 AND application_id = $3;
        `,
      params
    )
  ).rows[0];
  return result;
};

/**
 *
 * @param {*} connection
 * @param {*} notification
 * @returns
 */
const saveUINotification = async (connection, notification) => {
  const params = [
    notification.id,
    notification.relatedEntity,
    notification.type,
    notification.userId,
    notification.created,
    notification.level,
    notification.message,
    notification.read,
  ];
  const result = (
    await connection.execute(
      `
            INSERT INTO notification( id, related_entity, type, user_id, created, level, message, read ) 
            VALUES ( $1, $2, $3, $4, $5, $6, $7, $8 );
        `,
      params
    )
  ).rows[0];
  return result;
};

/**
 *
 * @param {*} userId
 * @returns
 */
const getUserById = async userId => {
  const params = [userId];
  const result = await server.db.read.row(
    `
        SELECT id, email, full_name, company_id
        FROM ics_user WHERE id = $1;
        `,
    params
  );
  return result;
};

/**
 *
 * @param {*} companyId
 * @returns
 */
const getCompanyById = async companyId => {
  const params = [companyId];
  const result = await server.db.read.row(
    `
        SELECT id, name, sender_email 
        FROM company WHERE id = $1;
        `,
    params
  );
  return result;
};
/**
 *
 * @param {*} userGroups
 * @return
 */
const getUserIdsFromUserGroup = async (userGroups = []) => {
  const userGroupParams = userGroups.map(d => `'${d.id}'`).join(',');
  if (userGroupParams) {
    const result = await server.db.read.rows(
      `
    SELECT user_id AS id
    FROM user_group_user 
    WHERE user_group_id 
    IN (${userGroupParams}); `
    );
    return result;
  }
  return null;
};

/**
 *
 * @param {*} connection
 * @param {*} fileUploadRequestId
 * @param {*} userId
 * @returns
 */
const saveFileUploadNotification = async (
  connection,
  fileUploadRequestId,
  userId
) => {
  const params = [fileUploadRequestId, userId];
  const result = (
    await connection.execute(
      `
            INSERT INTO file_upload_notification( request_id, user_id ) 
            VALUES ( $1, $2 );
        `,
      params
    )
  ).rows[0];
  return result;
};

/**
 *
 * @param {*} connection
 * @param {*} fileUploadRequest
 * @returns
 */
const saveFileUploadRequest = async (connection, fileUploadRequest) => {
  const params = [
    fileUploadRequest.id,
    fileUploadRequest.name,
    fileUploadRequest.created,
    fileUploadRequest.createdBy,
    fileUploadRequest.filesJson,
    fileUploadRequest.cancelled,
    fileUploadRequest.packagingInProgress,
    fileUploadRequest.requestedFrom,
    fileUploadRequest.startDate,
    fileUploadRequest.endDate,
  ];
  const result = (
    await connection.execute(
      `
            INSERT INTO file_upload_request( id, name, created, created_by, files_json, cancelled, packaging_in_progress,requested_from, start_date, end_date )
            VALUES ( $1, $2, $3, $4, $5, $6, $7,$8, $9, $10 );
        `,
      params
    )
  ).rows[0];
  return result;
};

/**
 * Updates the fileupload-request's packaging in progress with the provided parameter by given id
 * @param {*} connection
 * @param {*} request
 * @param {Boolean} packagingInProgress
 */
const updatePackagingInProgress = async (
  connection,
  request,
  packagingInProgress
) => {
  const params = [request.id, packagingInProgress];
  const result = await connection.execute(
    `
            UPDATE file_upload_request SET
            packaging_in_progress = $2 
            WHERE id = $1;
        `,
    params
  );
  return result;
};

/**
 * Updates the package url and completed timestamp of fileupload-request by given id
 * @param {*} connection
 * @param {*} fileUploadRequest
 * @param {String} timestamp
 * @param {String} timestamp
 */
const updatePackageUrlAndCompleted = async (
  connection,
  fileUploadRequest,
  packageUrl,
  timestamp
) => {
  const params = [fileUploadRequest.id, packageUrl, timestamp];
  const result = await connection.execute(
    `
            UPDATE file_upload_request SET
            package_url = $2,
            completed = $3
            WHERE id = $1;
        `,
    params
  );
  return result;
};

/**
 * Retrieves the list of users to notify about a fileupload request being completed
 * @param {String} fileUploadRequestId
 * @returns
 */
const getUsersToNotify = async fileUploadRequestId => {
  const params = [fileUploadRequestId];
  const result = await server.db.read.rows(
    `
        SELECT fun.user_id
        FROM file_upload_notification fun
        JOIN file_upload_request fur on fun.request_id = fur.id
        WHERE fur.id = $1
        GROUP BY fun.user_id;
        `,
    params
  );
  return result;
};

/**
 *
 * @param {*} connection
 * @param {*} fileUploadRequestId
 * @param {*} jobId
 * @returns
 */
const createFileUploadJob = async (connection, fileUploadRequestId, jobId) => {
  const params = [fileUploadRequestId, jobId];
  const result = (
    await connection.execute(
      `
            INSERT INTO file_upload_job( request_id, job_id ) 
            VALUES ( $1, $2 );
        `,
      params
    )
  ).rows[0];
  return result;
};

/**
 * @param {*} deviceId
 * @returns
 */

const getSiteIdFromDeviceId = async deviceId => {
  const params = [deviceId];
  const sql = `
  SELECT s.site_id
  FROM target t 
  left join site s on t.site_id = s.site_id
  WHERE target_id = $1 and s.active = TRUE and t.active = TRUE;
  `;
  const result = await server.db.read.rows(sql, params);
  return result;
};

/**
 * @param {*} connection
 * @param {*} query
 * @returns result of the query
 */
const resolveQuery = (connection, query, params) =>
  connection.execute(query, params);

async function getFileUploadRequestDetails(connection, fileUploadRequestId) {
  const query = `select j.device_id AS device_id,j.id as job_id, t.site_id AS site_id, s.company_id AS company_id,
          j.data::JSON ->> 'sourcePath'::varchar AS device_path,
          j.data::JSON ->> 'userParams'::varchar AS user_params
          FROM job j
          INNER JOIN file_upload_job fuj on j.id = fuj.job_id 
          INNER JOIN target t on j.device_id = t.target_id 
          INNER JOIN site s on t.site_id = s.site_id
          WHERE fuj.request_id = $1
          `;
  const params = [fileUploadRequestId];
  const result = await connection.execute(query, params);
  return result && result.rows && result.rows.length ? result.rows[0] : null;
}

const isFeatureFlagEnabled = async (connection, userId, featureFlag) => {
  if (!userId || !featureFlag) {
    logger.warn(
      `Missing required parameters: userId (${userId}), featureFlag (${featureFlag})`
    );
    return false;
  }
  const sql = `
    SELECT cff.feature_flag 
    FROM company_feature_flag cff
    JOIN ics_user iu ON iu.company_id = cff.company 
    WHERE iu.id = $1 AND cff.feature_flag = $2;
  `;

  const params = [userId, featureFlag];
  const result = await connection.execute(sql, params);
  logger.info(`isfeatureEnabled data feature flag ${JSON.stringify(result)}`);
  return result?.rows?.some(row => row.featureFlag === featureFlag) || false;
};

module.exports = {
  findSitesByTagIds,
  findSitesByIdWithActiveDevices,
  findTargetsByIdWithDeviceFile,
  getDeviceOwner,
  getFileUploadRequest,
  getFileUploadRequestWithJobs,
  getFileUploadNotification,
  getSiteOwner,
  getSiteTagOwner,
  getUserCompany,
  updateDeviceFile,
  saveUINotification,
  getUserById,
  getCompanyById,
  saveFileUploadNotification,
  saveFileUploadRequest,
  createFileUploadJob,
  getFileUploadRequestsWithJobs,
  updatePackagingInProgress,
  updatePackageUrlAndCompleted,
  getUsersToNotify,
  getUserIdsFromUserGroup,
  getSiteIdFromDeviceId,
  resolveQuery,
  getFileUploadRequestDetails,
  isFeatureFlagEnabled,
};
