const errorHandler = require('../../../../lib/errorhandler');
const {
  httpCode,
  fileUploadRequestedFrom,
} = require('../../../../lib/app-constants');
const { public: service } = require('./fileupload-request.service');

module.exports = {
  async downloadPackage(req, res, next) {
    try {
      const userId = req.user.sub;
      const username = req.user.fullName;
      const fileUploadRequestId = req.params.id;
      await service.downloadPackage(res, userId, username, fileUploadRequestId);
      res.send(httpCode.OK.STATUS_CODE);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  async createFileUploadRequest(req, res, next) {
    try {
      const companyId = req.user.company.id;
      const userId = req.user.sub;
      const fileUploadRequestReference = req.body;
      const requestedFrom = fileUploadRequestedFrom.DEVICE_PAGE;
      const results = await service.createFileUploadRequest(
        req.getId(),
        companyId,
        userId,
        fileUploadRequestReference,
        requestedFrom,
        fileUploadRequestReference.additionalProperties
      );
      res.send(httpCode.OK.STATUS_CODE, results);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  async getUploadFileRequest(req, res, next) {
    try {
      const fileUploadRequestId = req.params.id;
      const result = await service.getUploadFileRequest(fileUploadRequestId);
      res.send(httpCode.OK.STATUS_CODE, result);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  async uploadFileUploadRequest(req, res, next) {
    try {
      const appId = req.params.app;
      const filePath = req.params.filepath;
      const fileUploadRequestId = req.params.id;
      const deviceId = req.user.sub;
      await service.fileUploadRequestUploader(
        req,
        deviceId,
        fileUploadRequestId,
        filePath,
        appId
      );
      res.send(httpCode.NO_CONTENT.STATUS_CODE);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  async packageFiles(req, res, next) {
    try {
      await service.packageFiles();
      res.send(httpCode.NO_CONTENT.STATUS_CODE);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  async generateZipFileName(req, res, next) {
    try {
      const companyId = req.user.company.id;
      const userId = req.user.sub;
      const response = await service.generateZipFileName({
        reqBody: req.body,
        companyId,
        userId,
      });
      res.send(httpCode.OK.STATUS_CODE, response);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
