const util = require('util');
const fs = require('fs');
const path = require('path');
const moment = require('moment');
const uuid = require('uuid/v4');
const errors = require('restify-errors');

const logService = require('../../../../lib/logger');
const constants = require('../../lib/constants');
const jobHelper = require('../../../../helpers/job-helper');
const { pathSeperator, job } = require('../../../../lib/app-constants');

const logger = logService.mainLogger();

/**
 *
 * @param {*} item
 * @param {*} devicePathAndIdSet
 * @param {*} deviceFiles
 */
const registerDeviceFileToDevice = (item, devicePathAndIdSet, deviceFiles) => {
  if (!item.targetId) {
    throw new errors.NotFoundError('Target/device id not found');
  }
  devicePathAndIdSet.add({
    applicationId: item.applicationId,
    path: item.devicePath,
  }); // This is unnecessary use of set when migrating from Java which needs to be changed
  deviceFiles.set(item.targetId, devicePathAndIdSet);
};

/**
 *
 * @param {*} sites
 * @returns
 */
const getSiteDeviceFiles = sites => {
  const siteDeviceFileMap = new Map();
  if (!sites) {
    return siteDeviceFileMap;
  }
  let devicePathAndIdSet = null;
  let deviceFiles = null;
  sites.forEach(item => {
    deviceFiles = siteDeviceFileMap.get(item.siteId);
    if (!deviceFiles) {
      deviceFiles = new Map();
      devicePathAndIdSet = new Set();
      registerDeviceFileToDevice(item, devicePathAndIdSet, deviceFiles);
      siteDeviceFileMap.set(item.siteId, deviceFiles);
    } else {
      devicePathAndIdSet = deviceFiles.get(item.targetId);
      if (!devicePathAndIdSet) {
        devicePathAndIdSet = new Set();
        registerDeviceFileToDevice(item, devicePathAndIdSet, deviceFiles);
      } else {
        registerDeviceFileToDevice(item, devicePathAndIdSet, deviceFiles);
      }
      siteDeviceFileMap.set(item.siteId, deviceFiles);
    }
  });
  return siteDeviceFileMap;
};

/**
 *
 * @param {*} deviceFilesToUpload
 * @param {*} deviceFiles
 * @returns
 */
const setDevicesToAddUploadRequestsTo = (deviceFilesToUpload, deviceFiles) => {
  if (!deviceFilesToUpload) {
    return;
  }
  let devicePathAndIdSet = new Set();
  deviceFilesToUpload.forEach(item => {
    devicePathAndIdSet = deviceFiles.get(item.targetId);
    if (!devicePathAndIdSet) {
      devicePathAndIdSet = new Set();
    }
    registerDeviceFileToDevice(item, devicePathAndIdSet, deviceFiles);
  });
};

/**
 *
 * @param {*} sitesToAdd
 * @returns
 */
const addDevicesToUploadRequests = sitesToAdd => {
  let devicesToAddUploadRequestsTo = new Map();
  if (sitesToAdd) {
    // eslint-disable-next-line no-restricted-syntax
    for (const devices of sitesToAdd.values()) {
      if (!devicesToAddUploadRequestsTo.size) {
        devicesToAddUploadRequestsTo = devices;
      } else {
        // eslint-disable-next-line no-restricted-syntax
        for (const [key, value] of devices.entries()) {
          devicesToAddUploadRequestsTo.set(key, value);
        }
      }
    }
  }
  return devicesToAddUploadRequestsTo;
};

/**
 *
 * @param {*} filePaths
 * @returns
 */
const buildFilesQuery = filePaths => {
  let files = '';
  const stringBuilder = [' AND ('];
  let filesDetected = false;

  if (!filePaths || !filePaths.length) {
    return files;
  }

  const totalFiles = filePaths.length;
  for (let i = 0; i < totalFiles; i++) {
    const file = filePaths[i];
    const { applicationId } = file;
    const filePath = file.path;
    if (applicationId && filePath) {
      stringBuilder.push(
        "df.application_id = '",
        applicationId,
        "' AND df.device_path = '",
        filePath,
        "'"
      );
      filesDetected = true;
    }

    if (i < totalFiles - 1) {
      stringBuilder.push(' OR ');
    }
  }
  stringBuilder.push(')');

  if (filesDetected) {
    files = stringBuilder.join('');
  }
  return files;
};

/**
 *
 * @param {*} fileUploadRequestReference
 * @param {*} userId
 * @returns
 */
const buildUploadRequest = (
  fileUploadRequestReference,
  userId,
  requestedFrom = null
) => ({
  id: uuid(),
  name: fileUploadRequestReference.name,
  created: new Date(),
  createdBy: userId,
  filesJson: JSON.stringify(fileUploadRequestReference.files),
  cancelled: false,
  packagingInProgress: false,
  requestedFrom,
  startDate: fileUploadRequestReference.startDate,
  endDate: fileUploadRequestReference.endDate,
});

/**
 *
 * @param {*} fileUploadRequestReference
 * @param {*} userId
 * @returns
 */
const getFileUploadRequestNotifications = (
  fileUploadRequestReference,
  userId
) => {
  const listOfUsersToSendNotifications = new Set();
  // add the user that created the request to be a recipient of a notification
  listOfUsersToSendNotifications.add(userId);
  fileUploadRequestReference.users.forEach(user => {
    listOfUsersToSendNotifications.add(user.id);
  });

  const fileUploadNotifications = new Set();
  listOfUsersToSendNotifications.forEach(user => {
    const fileUploadNotification = {
      userId: user,
    };
    fileUploadNotifications.add(fileUploadNotification);
  });
  return fileUploadNotifications;
};

/**
 *
 * @param {*} fileUploadRequestId
 * @returns
 */
const buildRelatedEntityJson = fileUploadRequestId => {
  const relatedEntity = {
    type: 'fileUploadRequest',
    id: fileUploadRequestId,
  };

  return JSON.stringify(relatedEntity);
};

/**
 *
 * @param {*} level
 * @param {*} fileUploadRequestName
 * @returns
 */
const buildUINotificationMessage = (level, fileUploadRequestName) => {
  let message = null;
  switch (level) {
    case constants.NotificationTypes.FILE_UPLOAD_READY.level:
      message = util.format(
        constants.NotificationTypes.FILE_UPLOAD_READY.message,
        fileUploadRequestName
      );
      break;
    case constants.NotificationTypes.FILE_UPLOAD_IN_PROGRESS.level:
      message = util.format(
        constants.NotificationTypes.FILE_UPLOAD_IN_PROGRESS.message,
        fileUploadRequestName
      );
      break;
    case constants.NotificationTypes.FILE_UPLOAD_NO_FILES.level:
      message = util.format(
        constants.NotificationTypes.FILE_UPLOAD_NO_FILES.message,
        fileUploadRequestName
      );
      break;
    default:
      break;
  }
  return message;
};

/**
 *
 * @param {*} fileUploadRequest
 * @param {*} currentTimestamp
 * @param {*} notification
 * @param {*} level
 * @returns
 */
const buildUINotification = (
  fileUploadRequest,
  currentTimestamp,
  notification,
  level
) => {
  const relatedEntityJson = buildRelatedEntityJson(fileUploadRequest.id);
  const message = buildUINotificationMessage(level, fileUploadRequest.name);

  return {
    id: uuid(),
    relatedEntity: relatedEntityJson,
    type: constants.NotificationTypes.FILE_UPLOAD,
    userId: notification.userId,
    created: currentTimestamp,
    level,
    message,
    read: false,
  };
};

/**
 *
 * @param {*} fileUploadRequest
 * @param {*} notification
 * @param {*} level
 * @returns
 */
const buildEmailNotification = (fileUploadRequest, notification, level) => {
  logger.info(
    `[buildEmailNotification] Building an email notification for fileUploadRequest:${fileUploadRequest.id} | user:${notification.userId}`,
    fileUploadRequest,
    notification
  );
  const emailNotification = {
    timestamp: new Date(),
    fileUploadRequestId: fileUploadRequest.id,
    recipient: notification.userId,
    isTrigger: true,
  };

  switch (level) {
    case constants.NotificationTypes.FILE_UPLOAD_READY.level:
      emailNotification.type =
        constants.NotificationTypes.FILE_UPLOAD_READY.key;
      emailNotification.subject = util.format(
        constants.NotificationTypes.FILE_UPLOAD_READY.emailSubject,
        fileUploadRequest.name
      );
      break;
    case constants.NotificationTypes.FILE_UPLOAD_IN_PROGRESS.level:
      emailNotification.type =
        constants.NotificationTypes.FILE_UPLOAD_IN_PROGRESS.key;
      emailNotification.subject = util.format(
        constants.NotificationTypes.FILE_UPLOAD_IN_PROGRESS.emailSubject,
        fileUploadRequest.name
      );
      break;
    case constants.NotificationTypes.FILE_UPLOAD_NO_FILES.level:
      emailNotification.type =
        constants.NotificationTypes.FILE_UPLOAD_NO_FILES.key;
      emailNotification.subject = util.format(
        constants.NotificationTypes.FILE_UPLOAD_NO_FILES.emailSubject,
        fileUploadRequest.name
      );
      break;
    default:
      break;
  }
  return emailNotification;
};

/**
 *
 * @param {*} jobs
 * @returns
 */
const buildJobsSummary = jobs => {
  const totalJobs = jobs.length;
  let inProgressJobs = 0;
  let completeJobs = 0;
  let failedJobs = 0;
  let cancelledJobs = 0;
  let jobsInNewState = 0;

  jobs.forEach(j => {
    const statusId = j.status;
    if (statusId === jobHelper.jobStatus.NEW) {
      jobsInNewState += 1;
    } else if (statusId === jobHelper.jobStatus.IN_PROGRESS) {
      inProgressJobs += 1;
    } else if (statusId === jobHelper.jobStatus.COMPLETE) {
      completeJobs += 1;
    } else if (statusId === jobHelper.jobStatus.FAILED) {
      failedJobs += 1;
    } else if (statusId === jobHelper.jobStatus.CANCELLED) {
      cancelledJobs += 1;
    }
  });

  return {
    totalJobs,
    inProgressJobs,
    completeJobs,
    failedJobs,
    cancelledJobs,
    jobsInNewState,
  };
};

/**
 *
 * @param {*} fileUploadRequest
 * @param {*} jobSummary
 * @returns
 */
const calculateStatus = (fileUploadRequest, jobSummary) => {
  // cancelled check must got first
  if (fileUploadRequest.cancelled) {
    return constants.UploadRequestStatus.CANCELLED;
  }
  if (jobSummary.totalJobs === jobSummary.jobsInNewState) {
    return constants.UploadRequestStatus.NEW;
  }
  if (jobSummary.inProgressJobs > 0) {
    return constants.UploadRequestStatus.IN_PROGRESS;
  }
  if (jobSummary.failedJobs === jobSummary.totalJobs) {
    // all jobs have failed
    return constants.UploadRequestStatus.FAILED;
  }
  if (
    jobSummary.completeJobs +
      jobSummary.failedJobs +
      jobSummary.cancelledJobs ===
    jobSummary.totalJobs
  ) {
    // complete if all jobs are in terminal state
    return constants.UploadRequestStatus.COMPLETE;
  }
  return constants.UploadRequestStatus.NEW;
};

/**
 *
 * @param {*} fileUploadRequest
 * @returns
 */
const buildUploadRequestWithJobs = fileUploadRequest => {
  const jobSummary = buildJobsSummary(fileUploadRequest.jobs);
  return {
    id: fileUploadRequest.id,
    name: fileUploadRequest.name,
    files: fileUploadRequest.filesJson ? fileUploadRequest.filesJson : '[]',
    completed: fileUploadRequest.completed,
    totalJobs: jobSummary.totalJobs,
    jobsInNewState: jobSummary.jobsInNewState,
    inProgressJobs: jobSummary.inProgressJobs,
    completeJobs: jobSummary.completeJobs,
    failedJobs: jobSummary.failedJobs,
    cancelledJobs: jobSummary.cancelledJobs,
    status: calculateStatus(fileUploadRequest, jobSummary),
    packageUrl: !fileUploadRequest.packageUrl
      ? ''
      : fileUploadRequest.packageUrl,
  };
};

/**
 *
 * @param {*} fileUploadRequest
 * @returns
 */
const getPackageDownloadS3Config = (fileUploadRequest, fileUploadConfig) => {
  const { packageUrl } = fileUploadRequest;
  if (!packageUrl) {
    throw new errors.NotFoundError('No package found');
  }

  if (!fileUploadConfig) {
    throw new errors.NotFoundError(
      'S3 configuration for file upload not found'
    );
  }

  const fileName = packageUrl.substring(
    packageUrl.lastIndexOf(pathSeperator) + 1
  );
  const s3Bucket = fileUploadConfig.bucket;
  if (!s3Bucket) {
    throw new errors.NotFoundError(
      'S3 bucket configuration for file upload not found'
    );
  }

  const s3Key = fileUploadRequest.id + pathSeperator + fileName;
  return {
    s3Bucket,
    s3Key,
    fileName,
  };
};

/**
 * Iterates through jobs to check if all are done
 * @param {*} jobs
 * @returns boolean
 */
const areAllJobsDone = jobs => {
  let allJobDone = true;
  // eslint-disable-next-line no-restricted-syntax
  for (const j of jobs) {
    if (!job.DONE_STATUS.includes(j.status)) {
      allJobDone = false;
      break;
    }
  }
  return allJobDone;
};

/**
 * Iterates through jobs to check if any are complete
 * @param {*} jobs
 * @returns boolean
 */
const areAnyJobsComplete = jobs => {
  let anyJobComplete = false;
  // eslint-disable-next-line no-restricted-syntax
  for (const j of jobs) {
    if (job.STATUS.COMPLETED === j.status) {
      anyJobComplete = true;
      break;
    }
  }
  return anyJobComplete;
};

/**
 * Iterates through jobs to check if specific device ID has permission for the jobs
 * @param {*} jobs
 * @param {Number} deviceId
 * @returns {Boolean} hasPermission
 */
const jobHasPermission = (jobs, deviceId, fileUploadRequestId) => {
  let hasPermission = false;
  // eslint-disable-next-line no-restricted-syntax
  for (const j of jobs) {
    if (Number(j.deviceId) === Number(deviceId)) {
      hasPermission = true;
      break;
    }
  }
  if (!hasPermission) {
    throw new errors.ForbiddenError(
      `Device ${deviceId} has no permission to upload file with this request ${fileUploadRequestId}`
    );
  }
};

/**
 * Recursively finds leaf level files from sourceDir and moves them to destinationDir. Later deletes subDirectories of sourceDir
 * @param {*} sourceDir
 * @param {*} destinationDir
 */
async function copyLeafFilesToTempDir(sourceDir, destinationDir) {
  const files = getAllLeafLevelFiles(sourceDir);
  moveFilesToDestinationDir(files, destinationDir);
  await removeDirectories(sourceDir, [destinationDir]);
}

/**
 * Removes all directories inside of sourceDir, except mentioned in keepDirs
 * @param {*} sourceDir
 * @param {*} keepDirs
 */
async function removeDirectories(sourceDir, keepDirs) {
  const files = fs.readdirSync(sourceDir);
  // eslint-disable-next-line no-restricted-syntax
  for (const file of files) {
    const filePath = path.join(sourceDir, file);
    if (fs.lstatSync(filePath).isDirectory() && !keepDirs.includes(filePath)) {
      // eslint-disable-next-line no-await-in-loop
      await fs.promises.rm(filePath, { recursive: true });
    }
  }
}

/**
 * Move files to destinationDir
 * @param {*} files
 * @param {*} destinationDir
 */
function moveFilesToDestinationDir(files, destinationDir) {
  if (!fs.existsSync(destinationDir)) {
    fs.mkdirSync(destinationDir);
  }

  const fileCountDict = {};

  files.forEach(file => {
    const fileName = path.basename(file);
    const fileExtension = path.extname(file);
    const fileNameWithoutExtension = fileName.replace(fileExtension, '');

    fileCountDict[fileName] = (fileCountDict[fileName] || 0) + 1;
    let destinationFilePath = path.join(destinationDir, fileName);
    if (fileCountDict[fileName] > 1) {
      const count = fileCountDict[fileName];
      if (count === 2) {
        const previousFilePath = path.join(destinationDir, fileName);
        fs.renameSync(
          previousFilePath,
          path.join(
            destinationDir,
            `${fileNameWithoutExtension}~1${fileExtension}`
          )
        );
      }

      destinationFilePath = path.join(
        destinationDir,
        `${fileNameWithoutExtension}~${count}${fileExtension}`
      );
    }
    fs.renameSync(file, destinationFilePath);
  });
}

/**
 * Recursively finds all files inside of baseDir and returns them
 * @param {*} baseDir
 * @returns {Array} files
 */
function getAllLeafLevelFiles(baseDir) {
  const files = [];
  fs.readdirSync(baseDir).forEach(file => {
    const filePath = path.join(baseDir, file);
    if (fs.lstatSync(filePath).isDirectory()) {
      files.push(...getAllLeafLevelFiles(filePath).flat(Infinity));
    } else {
      files.push(filePath);
    }
  });
  return files;
}

async function moveFinalFilesToBase(
  basePath,
  tempFilesPath,
  finalFileName,
  appendFileName = false
) {
  const leafFiles = fs.readdirSync(tempFilesPath);
  leafFiles.forEach((file, index) => {
    const ext = file.split('.')[1] ? file.split('.')[1] : '';
    let fileNamePart = `${finalFileName}${leafFiles.length > 1 ? `~${index + 1}` : ''}`;
    if (appendFileName) {
      const [fileNameWithoutExtension] = file.split('.');
      fileNamePart = fileNameWithoutExtension
        ? `${fileNamePart} ${fileNameWithoutExtension}`
        : fileNamePart;
    }

    const fileExtensionPart = `${ext !== '' ? `.${ext}` : ''}`;
    const currFileName = `${fileNamePart}${fileExtensionPart}`;

    const sourcePath = path.join(tempFilesPath, file);
    const destinationPath = path.join(basePath, currFileName);

    fs.cpSync(sourcePath, destinationPath);
  });
  await fs.promises.rm(tempFilesPath, { recursive: true });
}

function applyDateTimeFormat(key, value, format) {
  switch (key) {
    case 'startDate':
    case 'endDate': {
      const defaultDateFormat = 'YYYYMMDD';
      const [year, month, date] = value.split('-');
      let monthValue = '';
      if (month) {
        monthValue = `${Number(month) - 1}`;
      }
      return value
        ? moment()
            .year(year)
            .month(monthValue)
            .date(date)
            .format(format || defaultDateFormat)
        : '';
    }

    case 'startHour':
    case 'endHour': {
      const defaultHoursFormat = 'HH';
      return value
        ? moment()
            .hours(`${value}`)
            .format(format || defaultHoursFormat)
        : '';
    }

    case 'startMinute':
    case 'endMinute': {
      const defaultMinutesFormat = 'mm';
      return value
        ? moment()
            .minutes(`${value}`)
            .format(format || defaultMinutesFormat)
        : '';
    }

    default: {
      return '';
    }
  }
}

module.exports.public = {
  getSiteDeviceFiles,
  setDevicesToAddUploadRequestsTo,
  addDevicesToUploadRequests,
  buildFilesQuery,
  buildUploadRequest,
  getFileUploadRequestNotifications,
  buildUINotification,
  buildEmailNotification,
  buildUploadRequestWithJobs,
  getPackageDownloadS3Config,
  areAllJobsDone,
  areAnyJobsComplete,
  jobHasPermission,
  copyLeafFilesToTempDir,
  moveFinalFilesToBase,
  applyDateTimeFormat,
};

module.exports.private = {
  registerDeviceFileToDevice,
  buildRelatedEntityJson,
  buildUINotificationMessage,
  buildJobsSummary,
  calculateStatus,
  moveFilesToDestinationDir,
  getAllLeafLevelFiles,
  removeDirectories,
};
