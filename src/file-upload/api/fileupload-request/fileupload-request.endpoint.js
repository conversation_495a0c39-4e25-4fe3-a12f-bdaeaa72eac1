const Joi = require('joi');
const momentTz = require('moment-timezone');
const { server } = require('../../../../app');
const env = require('../../../../env');
const {
  requiresRole,
  validateParams,
  validateBody,
  validateQuery,
} = require('../../../../lib/pre');
const { roles, positiveInteger } = require('../../../../lib/app-constants');
const handler = require('./fileupload-request.handler');

const BASE_PATH = `${env.config.base}/fileuploadrequests`;
const VERSION = '1.0.0';

/**
 * @swagger
 *   "/fileuploadrequests/{id}/package":
 *     get:
 *       tags:
 *         - fileuploadrequests
 *       summary: "Download the package"
 *       description: "Download the package associated with this file upload request"
 *       produces:
 *         - "application/octet-stream"
 *       parameters:
 *         - $ref: "#/parameters/AuthorizationTokenParam"
 *         - in: path
 *           name: id
 *           type: string
 *           required: true
 *           description: "The file upload request id"
 *       responses:
 *         "200":
 *           description: "Package was downloaded successfully"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "405":
 *           description: "Wrong method. Must use GET"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/:id/package`,
    version: VERSION,
  },
  [
    validateParams({
      id: Joi.string()
        .guid()
        .required()
        .label('fileUploadRequestId is required'),
    }),
    handler.downloadPackage,
  ]
);

/**
 * @swagger
 *   "/fileuploadrequests":
 *     post:
 *       tags:
 *         - fileuploadrequests
 *       summary: "Create a new file upload request"
 *       description: "Request a device to upload a file to TMS"
 *       produces:
 *         - "application/json"
 *       parameters:
 *         - $ref: "#/parameters/AuthorizationTokenParam"
 *         - in: body
 *           name: body
 *           required: true
 *           description: "An object used to make the fileuploadrequest"
 *           schema:
 *             $ref: "#/definitions/FileUploadCreate"
 *       responses:
 *         "200":
 *           description: "A new file upload request  has been created"
 *           schema:
 *             $ref: "#/definitions/FileUploadRetrieve"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "404":
 *           description: "Not found. Wrong url. Invalid product/site id"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.post(
  {
    path: BASE_PATH,
    version: VERSION,
  },
  [
    requiresRole([
      roles.COMPANY_ADMIN,
      roles.POWER_USER,
      roles.ANALYST,
      roles.SPECIALIST,
      roles.USER,
    ]),
    validateBody({
      name: Joi.string()
        .regex(/^['A-Za-z0-9-&$#._,/ ]{3,100}$/)
        .label('Name cannot contain special characters'),
      files: Joi.array().items({
        applicationId: Joi.string(),
        path: Joi.string(),
      }),
      devices: Joi.array().items({
        id: Joi.number()
          .integer()
          .min(positiveInteger.MIN_VALUE)
          .max(positiveInteger.MAX_VALUE),
      }),
      sites: Joi.array().items({
        id: Joi.string().guid(),
      }),
      siteTags: Joi.array().items({
        id: Joi.number()
          .integer()
          .min(positiveInteger.MIN_VALUE)
          .max(positiveInteger.MAX_VALUE),
      }),
      users: Joi.array().items({
        id: Joi.string().guid(),
      }),
      startDate: Joi.date().format('YYYY-MM-DD').optional(),
      endDate: Joi.date().format('YYYY-MM-DD').optional(),
    }),
    handler.createFileUploadRequest,
  ]
);

/**
 * @swagger
 *   "/fileuploadrequests/{id}":
 *     get:
 *       tags:
 *         - fileuploadrequests
 *       summary: "Get the details of a specific upload request"
 *       description: "Get the details of a specific upload request. Requires READ_ONLY role or better."
 *       produces:
 *         - "application/json"
 *       parameters:
 *         - $ref: "#/parameters/AuthorizationTokenParam"
 *         - in: path
 *           name: id
 *           required: true
 *           description: "The UUID of the file upload request"
 *           type: string
 *       responses:
 *         "200":
 *           description: "A new file upload request  has been created"
 *           schema:
 *             $ref: "#/definitions/FileUploadRetrieve"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "404":
 *           description: "Not found. Wrong url. Invalid product/site id"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/:id`,
    version: VERSION,
  },
  [
    requiresRole([
      roles.COMPANY_ADMIN,
      roles.POWER_USER,
      roles.USER,
      roles.ANALYST,
      roles.SPECIALIST,
    ]),
    validateParams({
      id: Joi.string().guid().required().label('id is empty'),
    }),
    handler.getUploadFileRequest,
  ]
);

/**
 * @swagger
 *   "/fileuploadrequests/{id}/upload":
 *     post:
 *       tags:
 *         - fileuploadrequests
 *       summary: "Accepts a file upload from a device based on a file upload job."
 *       description: "The file will be uploaded to an AWS S3 bucket."
 *       produces:
 *         - "text/plain"
 *       consumes:
 *         - "multipart/form-data"
 *       parameters:
 *         - $ref: '#/parameters/fileUploadRequestIdParam'
 *         - $ref: '#/parameters/fileUploadRequestPathParam'
 *         - $ref: '#/parameters/fileUploadRequestAppParam'
 *         - in: formData
 *           name: file
 *           description: File to upload
 *           type: file
 *           required: true
 *         - $ref: '#/parameters/deviceAuthTokenParam'
 *       responses:
 *         "204":
 *           description: "File was uploaded successfully"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "404":
 *           description: "Not found. Wrong url. Invalid product/site id"
 *         "405":
 *           description: "Wrong method. Must use POST"
 *         "413":
 *           description: "Uploaded file is larger than the maximum allowed limit"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/:id/upload`,
    version: VERSION,
  },
  [
    requiresRole(['DEVICE']),
    validateParams({
      id: Joi.string().guid().required().label('id is empty'),
    }),
    validateQuery({
      filepath: Joi.string(),
      app: Joi.string(),
    }),
    handler.uploadFileUploadRequest,
  ]
);

/**
 * @swagger
 *   "/fileuploadrequests/packagecomplete":
 *     post:
 *       tags:
 *         - fileuploadrequests
 *       summary: "Package completed file upload requests (will be called as a scheduled job). ICS System role required"
 *       description: "Zip the files all together and then transfer zip back to S3."
 *       produces:
 *         - "text/plain"
 *       parameters:
 *         - $ref: "#/parameters/AuthorizationTokenParam"
 *       responses:
 *         "204":
 *           description: "Files were zipped and uploaded to s3 successfully"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "405":
 *           description: "Wrong method. Must use POST"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/packagecomplete`,
    version: VERSION,
  },
  [requiresRole([roles.ICS_SYSTEM]), handler.packageFiles]
);

/**
 * @swagger
 *  "/fileuploadrequests/zipfilename":
 *  post:
 *    tags:
 *      - fileuploadrequests
 *    summary: "Creates zip file name for the file upload request"
 *    description: "Creates zip file name for file upload request based on the name-templates in the config"
 *    produces:
 *     - "application/json"
 *    parameters:
 *      - $ref: "#/parameters/AuthorizationTokenParam"
 *      - in: body
 *        name: body
 *        required: true
 *        description: "An object used to make the fileuploadrequest"
 *        schema:
 *          $ref: "#/definitions/FileUploadZipName"
 *          type: object
 *    responses:
 *      "200":
 *        description: "New name for the zip file has been created"
 *        schema:
 *          $ref: "#/definitions/FileUploadZipNameResponse"
 *      "400":
 *        description: "Bad Request"
 *        schema:
 *          $ref: "#/definitions/ErrorMessage"
 *      "401":
 *        $ref: "#/responses/authenticationFailed"
 *      "403":
 *         description: "No permission"
 *      "500":
 *         $ref: "#/responses/internalServerError"
 * */
server.post(
  {
    path: `${BASE_PATH}/zipfilename`,
    version: VERSION,
  },
  [
    requiresRole([
      roles.COMPANY_ADMIN,
      roles.POWER_USER,
      roles.ANALYST,
      roles.SPECIALIST,
      roles.USER,
    ]),
    validateBody({
      timezone: Joi.string()
        .valid(...momentTz.tz.names())
        .default('UTC'),
      deviceId: Joi.number()
        .integer()
        .min(positiveInteger.MIN_VALUE)
        .max(positiveInteger.MAX_VALUE),
      devices: Joi.array().items({
        id: Joi.number()
          .integer()
          .min(positiveInteger.MIN_VALUE)
          .max(positiveInteger.MAX_VALUE),
      }),
      sites: Joi.array().items({
        id: Joi.string().guid(),
      }),
      siteTags: Joi.array().items({
        id: Joi.number()
          .integer()
          .min(positiveInteger.MIN_VALUE)
          .max(positiveInteger.MAX_VALUE),
      }),
    }),
    handler.generateZipFileName,
  ]
);
