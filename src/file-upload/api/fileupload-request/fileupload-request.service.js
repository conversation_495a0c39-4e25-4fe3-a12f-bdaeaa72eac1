const fs = require('fs');
const path = require('path');
const uuid = require('uuid/v4');
const errors = require('restify-errors');
const { server } = require('../../../../app');
const AWS = require('../../../../lib/aws');
const commonService =
  require('../devices-file-and-fileupload-request-common/service').public;
const commonRepository = require('../devices-file-and-fileupload-request-common/repository');
const downloadHelper = require('../../../../helpers/download-helper');
const logger = require('../../../../lib/logger').mainLogger();
const mailer = require('../../../../lib/mailer');
const jobHelper = require('../../../../helpers/job-helper');
const zipUtils = require('../../../../lib/zip-utils');
const { config, fileRenameTemplatesConfig } = require('../../../../env');
const constants = require('../../lib/constants');
const {
  httpCode,
  s3Error,
  dbTransaction,
  pathSeperator,
  fileUploadRequestedFrom,
} = require('../../../../lib/app-constants');
const commonHelper =
  require('../devices-file-and-fileupload-request-common/helper').public;
const {
  refDbFieldsMapping,
} = require('../../../../resources/configs/ref-db-fields-mapping');
const { getDeviceFileId } = require('../devices-file/devices-file.repository');
const repository = require('./fileupload-request.repository');
const helper = require('./fileupload-request.helper').public;

const maxBatchSize = 10; // Check config location for this ToDo
const moduleName = path.parse(__filename).name;

/**
 * Makes a call to repository function to find sites by the tag ID's
 * @param {*} tagIds
 * @param {String} companyId
 * @param {String} userId
 * @param {String} filesQuery
 * @returns
 */
const getSitesByTags = async (tagIds, companyId, userId, filesQuery) => {
  const results = await repository.findSitesByTagIds(
    tagIds,
    companyId,
    userId,
    filesQuery
  );
  return helper.getSiteDeviceFiles(results);
};

/**
 *
 * @param {*} siteId
 * @param {*} userId
 * @param {*} filesQuery
 * @param {*} devicesToAddUploadRequestsTo
 */
const findSiteWithActiveDevices = async (
  siteId,
  userId,
  filesQuery,
  devicesToAddUploadRequestsTo
) => {
  const results = await repository.findSitesByIdWithActiveDevices(
    userId,
    siteId,
    filesQuery
  );
  helper.setDevicesToAddUploadRequestsTo(results, devicesToAddUploadRequestsTo);
};

/**
 *
 * @param {*} sites
 * @param {*} sitesToAdd
 * @param {*} userId
 * @param {*} filesQuery
 * @param {*} devicesToAddUploadRequestsTo
 */
const addSitesWithActiveDevices = async (
  sites,
  sitesToAdd,
  userId,
  filesQuery,
  devicesToAddUploadRequestsTo
) => {
  // Search for Sites by site id
  if (sites && sites.length) {
    await Promise.all(
      sites.map(site => {
        const deviceFiles = sitesToAdd.get(site.id);
        if (!deviceFiles) {
          // process only those not in tag to prevent duplicate processes
          return findSiteWithActiveDevices(
            site.id,
            userId,
            filesQuery,
            devicesToAddUploadRequestsTo
          );
        }
        return null;
      })
    );
  }
};

/**
 *
 * @param {*} siteTags
 * @param {*} companyId
 * @param {*} userId
 * @param {*} filesQuery
 * @returns
 */
const getSitesToAdd = async (siteTags, companyId, userId, filesQuery) => {
  let sitesToAdd = null;

  // Search for the sites by site tags
  if (siteTags && siteTags.length) {
    const tagIds = [];
    siteTags.forEach(tag => {
      tagIds.push(tag.id);
    });
    sitesToAdd = await getSitesByTags(tagIds, companyId, userId, filesQuery);
  } else {
    sitesToAdd = new Map();
  }
  return sitesToAdd;
};

/**
 *
 * @param {*} companyId
 * @param {*} userId
 * @param {*} fileUploadRequestReference
 * @param {*} filesQuery
 * @returns
 */
const findSitesForUploadRequest = async (
  companyId,
  userId,
  fileUploadRequestReference,
  filesQuery
) => {
  const sitesToAdd = await getSitesToAdd(
    fileUploadRequestReference.siteTags,
    companyId,
    userId,
    filesQuery
  );

  const devicesToAddUploadRequestsTo =
    helper.addDevicesToUploadRequests(sitesToAdd);

  await addSitesWithActiveDevices(
    fileUploadRequestReference.sites,
    sitesToAdd,
    userId,
    filesQuery,
    devicesToAddUploadRequestsTo
  );
  return devicesToAddUploadRequestsTo;
};

/**
 *
 * @param {*} targetId
 * @param {*} userId
 * @param {*} devicesAndFilesToPull
 * @param {*} filesQuery
 */
const findByTargetIdWithDeviceFile = async (
  targetId,
  userId,
  devicesAndFilesToPull,
  filesQuery
) => {
  const results = await repository.findTargetsByIdWithDeviceFile(
    userId,
    targetId,
    filesQuery
  );
  if (!results) {
    throw new errors.NotFoundError(`Device ${targetId} not found`);
  }
  helper.setDevicesToAddUploadRequestsTo(results, devicesAndFilesToPull);
};

/**
 *
 * @param {*} fileUploadRequestReference
 * @param {*} userId
 * @param {*} devicesAndFilesToPull
 * @param {*} filesQuery
 */
const getDevicesForUploadRequest = async (
  fileUploadRequestReference,
  userId,
  devicesAndFilesToPull,
  filesQuery
) => {
  const devicesToFind = fileUploadRequestReference.devices;
  if (devicesToFind && devicesToFind.length) {
    await Promise.all(
      devicesToFind.map(device => {
        const deviceFiles = devicesAndFilesToPull.get(device.id);
        if (!deviceFiles) {
          return findByTargetIdWithDeviceFile(
            device.id,
            userId,
            devicesAndFilesToPull,
            filesQuery
          );
        }
        return null;
      })
    );
  }
};

/**
 *
 * @param {*} companyId
 * @param {*} userId
 * @param {*} fileUploadRequestReference
 * @returns
 */
const findDevicesForFileUploadRequest = async (
  companyId,
  userId,
  fileUploadRequestReference
) => {
  const filesQuery = helper.buildFilesQuery(fileUploadRequestReference.files);

  const devicesAndFilesToPull = await findSitesForUploadRequest(
    companyId,
    userId,
    fileUploadRequestReference,
    filesQuery
  );
  await getDevicesForUploadRequest(
    fileUploadRequestReference,
    userId,
    devicesAndFilesToPull,
    filesQuery
  );
  return devicesAndFilesToPull;
};

/**
 *
 * @param {*} connection
 * @param {*} deviceId
 * @param {*} files
 * @param {*} fileUploadRequestId
 * @param {*} userId
 * @returns
 */
const createFileUploadJob = async (
  connection,
  deviceId,
  files,
  fileUploadRequestId,
  userId,
  additionalProperties = {}
) => {
  if (!files) {
    return [];
  }
  const jobStatusHistoryMessage = 'new fileupload job';
  const expirationHoursToAdd = 2;
  const currentDateTime = new Date();
  const expirationDateTime = commonHelper.getExpiryDateTime(
    currentDateTime,
    expirationHoursToAdd,
    'hours'
  );
  const currentUTCDateTime = commonHelper.getUTCTimestamp(currentDateTime);
  const jobsCreated = [];
  // eslint-disable-next-line no-restricted-syntax
  for (const file of files) {
    const jobDataVersion = 1;
    const urlPathBeforeId = 'fileuploadrequests';
    const urlPathAfterId = constants.FileUploadRequestTmpDownloadDirectory;
    const jobData = commonHelper.buildFileUploadJobData(
      jobDataVersion,
      fileUploadRequestId,
      file.applicationId,
      file.path,
      file.path,
      urlPathBeforeId,
      urlPathAfterId,
      additionalProperties
    );
    const filesToUploadAsJson = commonHelper.jobDataToJson(jobData);
    const job = jobHelper.buildJob(
      deviceId,
      file.applicationId,
      jobHelper.jobType.FILE_UPLOAD,
      filesToUploadAsJson,
      currentUTCDateTime,
      expirationDateTime,
      userId
    );
    // eslint-disable-next-line no-await-in-loop
    const fileUploadJob = await jobHelper.createJob(
      connection,
      job,
      jobStatusHistoryMessage
    );
    // eslint-disable-next-line no-await-in-loop
    await repository.createFileUploadJob(
      connection,
      fileUploadRequestId,
      fileUploadJob.id
    );
    // eslint-disable-next-line no-await-in-loop
    await repository.updateDeviceFile(
      connection,
      deviceId,
      file.path,
      file.applicationId,
      fileUploadJob.id
    );
    jobsCreated.push(fileUploadJob);
  }
  return jobsCreated;
};

/**
 *
 * @param {*} emailNotification
 * @param {*} message
 * @param {*} fileUploadRequest
 */
const postEmail = async (emailNotification, message, fileUploadRequest) => {
  if (!fileUploadRequest) {
    throw new errors.NotFoundError(
      `No file upload request found for id ${emailNotification.fileUploadRequestId}`
    );
  }

  const [user, sender] = await Promise.all([
    repository.getUserById(emailNotification.recipient),
    repository.getUserById(fileUploadRequest.createdBy),
  ]);
  if (!user) {
    throw new errors.NotFoundError(
      `Cannot find recipient setup for file upload request ${fileUploadRequest.name}`
    );
  }

  let emailSenderCompany = null;
  if (sender) {
    emailSenderCompany = await repository.getCompanyById(sender.companyId);
  }

  if (!emailSenderCompany) {
    throw new errors.NotFoundError(
      `Cannot find company setup for file upload request ${fileUploadRequest.name}`
    );
  }

  const notifictation = {
    message,
    path: '/notifications',
  };
  await mailer.sendNotification(
    { to: user.email, from: emailSenderCompany.senderEmail },
    notifictation,
    emailNotification.subject
  );
};

/**
 *
 * @param {*} connection
 * @param {*} fileUploadRequest
 * @param {*} level
 */
const sendNotification = async (connection, fileUploadRequest, level) => {
  const currentTimeStamp = commonHelper.getUTCTimestamp(new Date());
  // eslint-disable-next-line no-restricted-syntax
  for (const notification of fileUploadRequest.fileUploadNotifications) {
    const uiNotification = helper.buildUINotification(
      fileUploadRequest,
      currentTimeStamp,
      notification,
      level
    );
    // eslint-disable-next-line no-await-in-loop
    await repository.saveUINotification(connection, uiNotification);

    const emailNotification = helper.buildEmailNotification(
      fileUploadRequest,
      notification,
      level
    );
    // eslint-disable-next-line no-await-in-loop
    await postEmail(
      emailNotification,
      uiNotification.message,
      fileUploadRequest
    );
  }
};

/**
 *
 * @param {*} fileUploadRequest
 * @param {*} ownerId
 * @returns
 */
const stripOtherOwnersData = async (fileUploadRequest, ownerId) => {
  const strippedData = JSON.parse(JSON.stringify(fileUploadRequest));

  for (let i = 0; i < strippedData.devices.length; i++) {
    // eslint-disable-next-line no-await-in-loop
    const deviceOwner = await repository.getDeviceOwner(
      strippedData.devices[i].id
    );
    const deviceOwnerId = deviceOwner ? deviceOwner.id : null;
    if (deviceOwnerId !== ownerId) {
      strippedData.devices.splice(i, 1);
      i -= 1;
    }
  }

  for (let i = 0; i < strippedData.sites.length; i++) {
    // eslint-disable-next-line no-await-in-loop
    const siteOwner = await repository.getSiteOwner(strippedData.sites[i].id);
    const siteOwnerId = siteOwner ? siteOwner.id : null;
    if (siteOwnerId !== ownerId) {
      strippedData.sites.splice(i, 1);
      i -= 1;
    }
  }

  for (let i = 0; i < strippedData.siteTags.length; i++) {
    // eslint-disable-next-line no-await-in-loop
    const siteOwner = await repository.getSiteTagOwner(
      strippedData.siteTags[i].id
    );
    const siteOwnerId = siteOwner ? siteOwner.id : null;
    if (siteOwnerId !== ownerId) {
      strippedData.siteTags.splice(i, 1);
      i -= 1;
    }
  }

  for (let i = 0; i < strippedData.users.length; i++) {
    // eslint-disable-next-line no-await-in-loop
    const userCompany = await repository.getUserCompany(
      strippedData.users[i].id
    );
    const userCompanyId = userCompany ? userCompany.id : null;
    if (userCompanyId !== ownerId) {
      strippedData.users.splice(i, 1);
      i -= 1;
    }
  }

  return strippedData;
};

/**
 *
 * @param {*} fileUploadRequestId
 * @param {*} userId
 * @param {*} username
 * @returns
 */
const getFileUploadRequestHavingRecipient = async (
  fileUploadRequestId,
  userId,
  username
) => {
  const fileUploadRequest =
    await repository.getFileUploadRequest(fileUploadRequestId);

  if (!fileUploadRequest) {
    throw new errors.NotFoundError(
      `File upload request not found for file upload request id: ${fileUploadRequestId}`
    );
  }

  if (!fileUploadRequest.completed) {
    throw new errors.ConflictError(
      `The file upload request with id ${fileUploadRequestId} is not complete`
    );
  }

  const fileUploadNotification = await repository.getFileUploadNotification(
    fileUploadRequestId,
    userId
  );
  if (!fileUploadNotification) {
    throw new errors.UnauthorizedError(
      `Current user ${username} is not a valid recipient for file upload request with id ${fileUploadRequestId}`
    );
  }
  return fileUploadRequest;
};

/**
 *
 * @param {*} res
 * @param {*} userId
 * @param {*} username
 * @param {*} fileUploadRequestId
 */
const downloadPackage = async (res, userId, username, fileUploadRequestId) => {
  const fileUploadRequest = await getFileUploadRequestHavingRecipient(
    fileUploadRequestId,
    userId,
    username
  );
  const s3Config = helper.getPackageDownloadS3Config(
    fileUploadRequest,
    config.fileUpload
  );
  try {
    await downloadHelper.downloadPackageFromS3(res, s3Config);
  } catch (err) {
    if (err.code === s3Error.NO_SUCH_BUCKET.CODE) {
      throw new errors.NotFoundError(s3Error.NO_SUCH_BUCKET.MESSAGE);
    } else if (err.code === s3Error.NO_SUCH_KEY.CODE) {
      throw new errors.NotFoundError(s3Error.NO_SUCH_KEY.MESSAGE);
    }
    throw new errors.InternalServerError(
      `Could not download the package. Error: ${err}`
    );
  }
};

/**
 *
 * @param {*} connection
 * @param {*} targetDevicesForUploadRequest
 * @param {*} fileUploadRequest
 * @param {*} userId
 * @returns
 */
const getJobsCreatedAndDeviceOwners = async (
  connection,
  targetDevicesForUploadRequest,
  fileUploadRequest,
  userId,
  additionalProperties = {}
) => {
  // 3. Create the jobs that the devices will consume
  const deviceOwners = new Set();
  let allJobsCreated = [];
  // eslint-disable-next-line no-restricted-syntax
  for (const [key, value] of targetDevicesForUploadRequest.entries()) {
    // Auth has been checked
    const deviceId = key;
    const deviceFiles = value;

    const promises = [
      repository.getDeviceOwner(deviceId),
      createFileUploadJob(
        connection,
        deviceId,
        deviceFiles,
        fileUploadRequest.id,
        userId,
        additionalProperties
      ),
    ];

    // eslint-disable-next-line no-await-in-loop
    const [deviceOwner, jobsCreated] = await Promise.all(promises);
    if (deviceOwner && deviceOwner.id) {
      deviceOwners.add(deviceOwner.id);
    }
    allJobsCreated = allJobsCreated.concat(jobsCreated);
  }
  return { allJobsCreated, deviceOwners };
};

/**
 *
 * @param {*} reqId
 * @param {*} deviceOwners
 * @param {*} userId
 * @param {*} companyId
 * @param {*} results
 */
const logFileUploadRequestCreated = async (
  reqId,
  deviceOwners,
  userId,
  companyId,
  results
) => {
  // eslint-disable-next-line no-restricted-syntax
  for (const ownerId of deviceOwners) {
    const strippedData =
      deviceOwners.size > 1
        ? // eslint-disable-next-line no-await-in-loop
          await stripOtherOwnersData(results, ownerId)
        : results;
    const jsonObject = JSON.stringify(strippedData);
    logger.info({
      req_id: deviceOwners.size > 1 ? reqId : uuid(),
      statusCode: httpCode.OK.STATUS_CODE,
      userId,
      companyId,
      res: {
        resourceOwner: ownerId,
        body: jsonObject,
      },
    });
  }
};

/**
 *
 * @param {*} reqId
 * @param {*} companyId
 * @param {*} userId
 * @param {*} fileUploadRequestReference
 * @param {*} targetDevicesForUploadRequest
 * @returns
 */
const createFileUploadRequestUsingTransaction = async (
  reqId,
  companyId,
  userId,
  fileUploadRequestReference,
  targetDevicesForUploadRequest,
  requestedFrom,
  additionalProperties = {}
) => {
  const { level } = constants.NotificationTypes.FILE_UPLOAD_IN_PROGRESS;
  // 2. create the file upload request
  const fileUploadRequest = helper.buildUploadRequest(
    fileUploadRequestReference,
    userId,
    requestedFrom
  );

  const connection = await server.db.write.getConnection();
  await connection.execute(dbTransaction.BEGIN);
  try {
    await repository.saveFileUploadRequest(connection, fileUploadRequest);
    const { allJobsCreated, deviceOwners } =
      await getJobsCreatedAndDeviceOwners(
        connection,
        targetDevicesForUploadRequest,
        fileUploadRequest,
        userId,
        additionalProperties
      );

    // 3. Add the created jobs to the fileUploadRequest
    fileUploadRequest.jobs = allJobsCreated;

    // 4. fetching the user data from each team from usergroup.
    const usersOfTeams = await repository.getUserIdsFromUserGroup(
      fileUploadRequestReference.teams ? fileUploadRequestReference.teams : []
    );

    if (usersOfTeams) {
      fileUploadRequestReference.users.push(...usersOfTeams);
    }
    // 5. Create the FileUploadNotification association with upload request
    fileUploadRequest.fileUploadNotifications =
      helper.getFileUploadRequestNotifications(
        fileUploadRequestReference,
        userId
      );

    // 6. Save recipient notifications (incl. user)
    await Promise.all(
      fileUploadRequestReference.users.map(async user => {
        await repository.saveFileUploadNotification(
          connection,
          fileUploadRequest.id,
          user.id
        );
      })
    );
    await logFileUploadRequestCreated(
      reqId,
      deviceOwners,
      userId,
      companyId,
      fileUploadRequestReference
    );

    await sendNotification(connection, fileUploadRequest, level);
    await connection.execute(dbTransaction.COMMIT);
  } catch (e) {
    await connection.execute(dbTransaction.ROLLBACK);
    throw e;
  } finally {
    connection.done();
  }
  return fileUploadRequest;
};

/**
 *
 * @param {*} reqId
 * @param {*} companyId
 * @param {*} userId
 * @param {*} fileUploadRequestReference
 * @returns
 */
const createFileUploadRequest = async (
  reqId,
  companyId,
  userId,
  fileUploadRequestReference,
  requestedFrom,
  additionalProperties = {}
) => {
  // 1. get the devices that the upload requests will be sent to
  const targetDevicesForUploadRequest = await findDevicesForFileUploadRequest(
    companyId,
    userId,
    fileUploadRequestReference
  );

  const fileUploadRequest = await createFileUploadRequestUsingTransaction(
    reqId,
    companyId,
    userId,
    fileUploadRequestReference,
    targetDevicesForUploadRequest,
    requestedFrom,
    additionalProperties
  );
  const result = helper.buildUploadRequestWithJobs(fileUploadRequest);
  return result;
};

/**
 *
 * @param {*} fileUploadRequestId
 * @returns
 */
const getUploadFileRequest = async fileUploadRequestId => {
  // Get the file upload request, if can't find then throw a 404
  const fileUploadRequest =
    await repository.getFileUploadRequestWithJobs(fileUploadRequestId);
  if (!fileUploadRequest) {
    throw new errors.NotFoundError(
      `FileUploadRequest ${fileUploadRequestId} not found`
    );
  }
  return helper.buildUploadRequestWithJobs(fileUploadRequest);
};

/**
 * Retrieves the objects that the fileuploadRequest upload is dependent on; device, fileuploadrequest, device file, and site
 * @param {Number} deviceId
 * @param {UUID} fileUploadRequestId
 * @param {String} filePath
 * @param {String} appId
 * @returns uploadDependencies
 */
const getFileUploadRequestsUploadDependencies = async (
  deviceId,
  fileUploadRequestId,
  filePath,
  appId
) => {
  const [device, fileUploadRequest, deviceFile] = await Promise.all([
    commonRepository.getDeviceById(deviceId),
    repository.getFileUploadRequestWithJobs(fileUploadRequestId),
    commonRepository.getDeviceFile(deviceId, filePath, appId),
  ]);

  if (!device) {
    throw new errors.NotFoundError(
      `Device with device ID: ${deviceId} does not exist`
    );
  }
  if (!fileUploadRequest) {
    throw new errors.NotFoundError(
      `FileUploadRequest ${fileUploadRequestId} not found`
    );
  }
  if (!deviceFile) {
    throw new errors.NotFoundError(
      `DeviceFiles with deviceId ${deviceId} and devicePath ${filePath} and appId ${appId} not found`
    );
  }

  const { siteId } = device;
  const site = await commonRepository.getSiteDetails(siteId);
  if (!site) {
    throw new errors.NotFoundError(
      `Site with site ID: ${siteId} does not exist`
    );
  }

  const uploadDependencies = {
    device,
    deviceFile,
    fileUploadRequest,
    site,
  };

  logger.info(
    `[getFileUploadRequestsUploadDependencies] uploadDependencies retrieved with deviceId: ${deviceId} and fileUploadRequestId: ${fileUploadRequestId} filePath: ${filePath} appId: ${appId}`
  );
  return uploadDependencies;
};

/**
 * Retrieves upload dependencies, checks device has permission, generate the s3 upload filepath using dependencies properties then uploads file
 * @param {*} req
 * @param {Number} deviceId
 * @param {UUID} fileUploadRequestId
 * @param {String} filePath
 * @param {String} appId
 */
const fileUploadRequestUploader = async (
  req,
  deviceId,
  fileUploadRequestId,
  filePath,
  appId
) => {
  const dependencies = await getFileUploadRequestsUploadDependencies(
    deviceId,
    fileUploadRequestId,
    filePath,
    appId
  );
  helper.jobHasPermission(
    dependencies.fileUploadRequest.jobs,
    deviceId,
    fileUploadRequestId
  );
  const s3FilePath = commonHelper.generateS3UploadFilePath(
    dependencies.device,
    filePath,
    appId,
    fileUploadRequestId,
    dependencies.site.name
  );
  await commonService.uploadFile(
    req,
    filePath,
    s3FilePath,
    dependencies.deviceFile,
    true,
    config.fileUpload
  );
};

/**
 * Finds all fileuploadrequests with jobs and then returns fileuploadrequests up to the maxBatchSize where job statuses are done
 * @returns
 */
const getOutstandingFileUploadRequest = async () => {
  const completedFileUploadRequests =
    await repository.getFileUploadRequestsWithJobs();
  if (completedFileUploadRequests.length <= 0) {
    return null;
  }
  const completedRequests = completedFileUploadRequests.filter(request =>
    helper.areAllJobsDone(request.jobs)
  );

  if (completedRequests.length <= maxBatchSize) {
    return completedRequests;
  }

  const fileUploadRequests = completedRequests.slice(0, maxBatchSize);
  if (!fileUploadRequests) {
    logger.warn('No complete FileUploadRequests');
  }

  return fileUploadRequests;
};

/**
 * Skips zipping and updates fileupload request package url and completed date and sends notifications
 * @param {*} connection
 * @param {Object} fileUploadRequest
 * @param {String} level
 * @param {String} message
 * @returns
 */
const updateRequestAndNotifyUsers = async (
  connection,
  fileUploadRequest,
  url,
  level,
  message
) => {
  const currentTimestamp = commonHelper.getUTCTimestamp(new Date());
  if (message) {
    logger.warn(
      `${moduleName}.updateRequestAndNotifyUsers: ${message} for FileUploadRequest ${fileUploadRequest.name} (${fileUploadRequest.id}), skip zipping`
    );
  }
  await repository.updatePackageUrlAndCompleted(
    connection,
    fileUploadRequest,
    url,
    currentTimestamp
  );
  const usersToNotify = await repository.getUsersToNotify(fileUploadRequest.id);
  await sendNotification(
    connection,
    { ...fileUploadRequest, fileUploadNotifications: usersToNotify },
    level
  );
};

/**
 * Builds s3 configurations to download file object from S3, then downloads stream at destination path
 * @param {String} fileUploadBucket
 * @param {String} s3FilePath
 * @param {String} s3TemporaryUploadFolder
 * @param {String} s3FileName
 */
const downloadFromS3 = async (
  fileUploadBucket,
  s3FilePath,
  s3TemporaryUploadFolder
) => {
  const s3Params = {
    Bucket: fileUploadBucket,
    Key: s3FilePath,
  };
  const fileObj = AWS.downloadFromS3(s3Params);
  await AWS.downloadFromS3Stream(
    fileObj,
    path.join(s3TemporaryUploadFolder, s3FilePath)
  );
};

/**
 * Uploads zip file to s3 and removes temp directory
 * @param {Object} zipDependencies
 * @param {String} fileUploadBucket
 * @param {String} fileUploadRequestId
 * @returns {String} fileUploadRequestPackageUrl
 */
const uploadToS3 = async (zipDependencies, fileUploadBucket) => {
  server.log.info(
    `[uploadToS3] Uploading file ${zipDependencies.zipDestination} to bucket:${fileUploadBucket}`
  );
  const fileUploadStreamS3 = fs.createReadStream(
    zipDependencies.zipDestination
  );
  await AWS.uploadToS3(
    fileUploadBucket,
    zipDependencies.zipFileS3Path,
    fileUploadStreamS3
  );
  await fs.promises.unlink(zipDependencies.zipDestination);
};

/**
 * Manages the zip source, destination and file
 * @param {String} fileUploadRequestName
 * @param {String} fileUploadRequestId
 * @param {String} s3TemporaryRootFolder
 * @param {String} s3TemporaryUploadFolder
 * @param {String} fileUploadBucket
 * @returns fileuplaod request package url
 */
const manageZip = async (
  fileUploadRequestName,
  fileUploadRequestId,
  s3TemporaryRootFolder,
  s3TemporaryUploadFolder,
  fileUploadBucket
) => {
  const zipFileName = fileUploadRequestName
    .replace(/\s/g, '-')
    .replace(/\//g, '|')
    .concat('.zip');
  const zipDestination = path.join(
    s3TemporaryRootFolder,
    fileUploadRequestId.toString().concat('-').concat(zipFileName)
  );
  const zipFileS3Path = path.join(
    fileUploadRequestId,
    pathSeperator,
    zipFileName
  );
  await zipUtils.createArchive(s3TemporaryUploadFolder, zipDestination);
  const fileUploadRequestPackageUrl = `s3://${fileUploadBucket}${pathSeperator}${fileUploadRequestId}${pathSeperator}${zipFileName}`;

  return [
    fileUploadRequestPackageUrl,
    {
      zipDestination,
      zipFileS3Path,
      zipFileName,
    },
  ];
};

const buildS3Config = fileUploadRequest => {
  const fileUploadKey = fileUploadRequest.id.toString() + pathSeperator;
  const fileUploadBucket = config.fileUpload.bucket;
  if (!fileUploadBucket) {
    logger.warn(
      `${moduleName}.completePackagingFile: No file upload bucket configured ${fileUploadRequest.name} (${fileUploadRequest.id}), skip zipping`
    );
    return null;
  }

  const s3Params = {
    Bucket: fileUploadBucket,
    Prefix: fileUploadKey,
    Delimeter: null,
  };
  return s3Params;
};

const getS3ObjectContents = async (connection, fileUploadRequest) => {
  const s3Params = buildS3Config(fileUploadRequest);
  if (!s3Params) {
    return null;
  }
  const s3ObjectListing = await AWS.retrieveS3Files(s3Params);
  const s3Contents = s3ObjectListing.Contents;
  if (!s3Contents || s3Contents.length <= 0) {
    await updateRequestAndNotifyUsers(
      connection,
      fileUploadRequest,
      null,
      constants.NotificationTypes.FILE_UPLOAD_NO_FILES.level,
      'No uploaded files'
    );
    return null;
  }
  const s3Bucket = s3Params.Bucket;
  const s3Dependencies = {
    s3Contents,
    s3Bucket,
  };
  return s3Dependencies;
};

const getZipFilePathTemplate = ({
  companyId,
  typeOfDownload = 'paymentDashboard',
}) => {
  const fileDownloadTemplatesObj =
    fileRenameTemplatesConfig.fileDownloadTemplates;
  const companyTemplateObj = fileDownloadTemplatesObj.companyTemplates.find(
    template => template.companyId === companyId
  );

  if (
    !companyTemplateObj ||
    !companyTemplateObj.nestedFilePathTemplates ||
    !companyTemplateObj.nestedFilePathTemplates[typeOfDownload]
  )
    return null;

  const zipFilePathTemplateObj =
    companyTemplateObj.nestedFilePathTemplates[typeOfDownload];

  return {
    template: zipFilePathTemplateObj.template,
    options: zipFilePathTemplateObj.options,
  };
};

/**
 * Get the correct zip file name template based on company template and typeOfDownload
 * @param {Object} companyId, typeOfDownload, deviceFileId
 * @returns {String} zipFileNameTemplate
 */
const getZipFileNameTemplate = ({
  companyId,
  typeOfDownload,
  deviceFileId = '',
}) => {
  const fileDownloadTemplatesObj =
    fileRenameTemplatesConfig.fileDownloadTemplates;
  let companyTemplateObj = fileDownloadTemplatesObj.companyTemplates.find(
    template => template.companyId === companyId
  );

  if (
    !companyTemplateObj ||
    !companyTemplateObj.zipFileNameTemplates ||
    !companyTemplateObj.zipFileNameTemplates[typeOfDownload]
  ) {
    companyTemplateObj = fileDownloadTemplatesObj.defaultTemplates;
  }

  const zipFileNameTemplatesObj = companyTemplateObj.zipFileNameTemplates;

  let zipFileNameTemplate = '';
  const {
    options,
    paymentDashboardTemplate = '',
    template = '',
  } = zipFileNameTemplatesObj[typeOfDownload];
  if (deviceFileId && paymentDashboardTemplate) {
    zipFileNameTemplate = paymentDashboardTemplate;
  } else {
    zipFileNameTemplate = template;
  }

  return { template: zipFileNameTemplate, options };
};

/**
 * Get the default value based on the ref object
 * @param {Object} refObj, ref, options
 * @returns {String} defaultValue
 */
const getDefaultValue = async ({
  refObj,
  ref,
  options = { [ref]: null },
  reqBody = {},
}) => {
  switch (ref) {
    case 'uparam#startDate':
    case 'uparam#endDate':
    case 'uparam#startHour':
    case 'uparam#endHour':
    case 'uparam#startMinute':
    case 'uparam#endMinute': {
      const [, userParamKey] = ref.split('#');
      const format =
        options[ref] && options[ref].format ? options[ref].format : '';
      const userParamValue =
        reqBody && reqBody.userParams && reqBody.userParams[userParamKey]
          ? reqBody.userParams[userParamKey]
          : '';
      return helper.applyDateTimeFormat(userParamKey, userParamValue, format);
    }

    case 'furid': {
      const { fileUploadRequestId } = reqBody;
      return refObj.defaultValue({ fileUploadRequestId });
    }
    case 'furts': {
      let refOptions = options[ref];
      if (!refOptions) {
        refOptions = {
          format: 'YYYY-MM-DD-HHmm',
        };
      }
      return refObj.defaultValue({
        ...refOptions,
        timezone: reqBody.timezone || 'UTC',
      });
    }

    default: {
      if (refObj && refObj.defaultValue) {
        return refObj.defaultValue(options[ref], { reqBody, ref }) || '';
      }
      return '';
    }
  }
};

/**
 * Handle custom queries
 * @param {Object} refValueMapping, ref, refObj, params
 */
const handleCustomQuery = ({ refValueMapping, ref, refObj, params }) => {
  const { query = '', setQueryParams } = refObj;

  refValueMapping.push({
    ref,
    value: query,
    type: 'query',
    params: setQueryParams(params),
    postProcessing: null,
  });
};

const handleUserParamsMapping = ({
  refValueMapping,
  ref,
  reqBody,
  options,
  typeOfDownload,
}) => {
  const [, userParamName = ''] = ref.split('#');
  const refObj =
    refDbFieldsMapping &&
    refDbFieldsMapping[typeOfDownload] &&
    refDbFieldsMapping[typeOfDownload].uparam
      ? refDbFieldsMapping[typeOfDownload].uparam
      : {};
  const { defaultValue, isDependentOnDb, query, isCustomQuery } = refObj;

  if (!isDependentOnDb || !userParamName || !query) {
    refValueMapping.push({
      ref,
      value: getDefaultValue({
        refObj: { defaultValue },
        ref,
        options,
        reqBody,
      }),
      type: 'value',
    });
    return;
  }

  if (isCustomQuery) {
    handleCustomQuery({
      refValueMapping,
      ref,
      refObj,
      params: { ...reqBody, userParamName },
    });
  }
};

/**
 * Handle custom attribute mappings
 * @param {Object} refValueMapping, ref, reqBody, options
 */
const handleCustomAttributeMapping = ({
  refValueMapping,
  ref,
  reqBody,
  options,
}) => {
  const [, attributeName = ''] = ref.split('#');
  const refObj =
    refDbFieldsMapping &&
    refDbFieldsMapping.cattr &&
    refDbFieldsMapping.cattr[attributeName]
      ? refDbFieldsMapping.cattr[attributeName]
      : {};
  const { defaultValue, isDependentOnDb, query, isCustomQuery } = refObj;

  if (!isDependentOnDb || !attributeName || !query) {
    refValueMapping.push({
      ref,
      value: getDefaultValue({
        refObj: { defaultValue },
        ref,
        options,
        reqBody,
      }),
      type: 'value',
    });
    return;
  }

  if (isCustomQuery) {
    handleCustomQuery({
      refValueMapping,
      ref,
      refObj,
      params: { ...reqBody, attributeName },
    });
  }
};

/**
 * Get ref value mapping from template
 * @param {Object} templateVariableRefs, typeOfDownload, reqBody, options
 * @returns {Array} refValueMapping -> [{ref, value, type}]
 */
const getRefValueMappingFromTemplate = ({
  templateVariableRefs,
  typeOfDownload,
  reqBody,
  options,
}) => {
  const refValueMapping = [];
  for (let i = 0; i < templateVariableRefs.length; i++) {
    let ref = templateVariableRefs[i];
    ref = ref.replace('{', '').replace('}', '');
    let refObj = {};

    if (ref.includes('cattr')) {
      handleCustomAttributeMapping({ refValueMapping, ref, reqBody, options });
      // eslint-disable-next-line no-continue
      continue;
    }

    if (ref.includes('uparam')) {
      handleUserParamsMapping({
        refValueMapping,
        ref,
        reqBody,
        options,
        typeOfDownload,
      });
      // eslint-disable-next-line no-continue
      continue;
    }

    // Get tenantwise refObj, else get default
    refObj =
      refDbFieldsMapping[typeOfDownload] &&
      refDbFieldsMapping[typeOfDownload][ref]
        ? refDbFieldsMapping[typeOfDownload][ref]
        : refDbFieldsMapping[ref];

    const {
      isDependentOnDb = false,
      fieldName = '',
      tableName = '',
      whereField = '',
      reqBodyField = '',
      isCustomQuery = false,
    } = refObj || {};

    if (!isDependentOnDb) {
      refValueMapping.push({
        ref,
        value: getDefaultValue({ refObj, ref, options, reqBody }),
        type: 'value',
        postProcessing: null,
      });
      // eslint-disable-next-line no-continue
      continue;
    }

    if (isCustomQuery) {
      handleCustomQuery({ refValueMapping, ref, refObj, params: reqBody });
      // eslint-disable-next-line no-continue
      continue;
    }

    const areRequiredFieldsPresent =
      fieldName &&
      tableName &&
      whereField &&
      reqBodyField &&
      reqBody[reqBodyField];
    if (!areRequiredFieldsPresent) {
      logger.error(
        `[getRefValueMappingFromTemplate] required fields are missing for ${ref}`
      );
      refValueMapping.push({
        ref,
        value: getDefaultValue({ refObj, ref, options, reqBody }),
        type: 'value',
        postProcessing: null,
      });
      // eslint-disable-next-line no-continue
      continue;
    }

    refValueMapping.push({
      ref,
      value: `select ${fieldName} as "${ref}" from ${tableName} where ${whereField} = '${reqBody[reqBodyField]}'`,
      type: 'query',
      postProcessing:
        refObj && refObj.postProcessing
          ? { call: refObj.postProcessing, args: options }
          : null,
    });
  }
  return refValueMapping;
};

const handleCustomAttributeRefs = ({
  ref,
  result,
  refMappingArr,
  i,
  valueToUpdate,
}) => {
  const [, fieldName] = ref.split('#');
  const { value, defaultValue } = result || {};
  const refMappingArray = [...refMappingArr];
  refMappingArray[i].value = value || defaultValue;
  refMappingArray[i].value = refMappingArray[i].value.replace(/\//g, '-');
  if (!refMappingArray[i].value) {
    refMappingArray[i].value = refDbFieldsMapping.cattr[fieldName]
      ? refDbFieldsMapping.cattr[fieldName].defaultValue()
      : '';
    refMappingArray[i].value = refMappingArray[i].value.replace(/\//g, '-');
  }
  const finalZipFilePath = valueToUpdate.replace(
    `{${ref}}`,
    refMappingArray[i].value
  );
  return { valueToUpdate: finalZipFilePath, refMappingArray };
};

const generateZipFilePath = async (connection, fileUploadRequest) => {
  try {
    const fileUploadRequestDetails =
      await repository.getFileUploadRequestDetails(
        connection,
        fileUploadRequest.id
      );

    const { companyId, deviceId, siteId, devicePath, jobId } =
      fileUploadRequestDetails;
    let { userParams: jobDataUserParams } = fileUploadRequestDetails;
    jobDataUserParams = jobDataUserParams ? JSON.parse(jobDataUserParams) : {};

    const deviceFile = await getDeviceFileId(connection, deviceId, devicePath);
    const deviceFileId = deviceFile.id;

    // Get zip file path template using company id
    const zipFilePathTemplate = getZipFilePathTemplate({
      companyId,
    });

    if (!zipFilePathTemplate) {
      server.log.error(
        `[generateZipFilePath] zipFilePathTemplate not found fileUploadRequest : ${fileUploadRequest.id}`
      );
      return null;
    }

    const { template, options = {} } = zipFilePathTemplate;

    const templateVariableRefs = template.match(/{(.*?)}/g);
    const typeOfDownload = 'paymentDashboard';

    const reqBody = {
      deviceId,
      deviceFileId,
      siteId,
      companyId,
      fileUploadRequestId: fileUploadRequest.id,
      jobId,
      userParams: jobDataUserParams,
    };
    let refMappingArr = getRefValueMappingFromTemplate({
      templateVariableRefs,
      typeOfDownload,
      reqBody,
      options,
    });

    let results = [];
    results = await Promise.all(
      refMappingArr.map(refValueMappingObj => {
        const { type, value, params = [] } = refValueMappingObj;
        if (type === 'query') {
          return repository.resolveQuery(connection, value, params);
        }
        return value; // defaultValue
      })
    );

    let zipFilePath = template;
    for (let i = 0; i < results.length; i++) {
      const refMappingObj = refMappingArr[i];
      const { ref, type, postProcessing } = refMappingObj;
      let result = results[i];
      if (type === 'query') {
        result = result.rows.length ? result.rows[0] : null;
        if (!result) {
          server.log.warn(
            `[generateZipFilePath] Query resolution for ref ${ref} failed fileUploadRequest : ${fileUploadRequest.id}`
          );
          refMappingObj.value = '';
          zipFilePath = zipFilePath.replace(`{${ref}}`, refMappingObj.value);
          // eslint-disable-next-line no-continue
          continue;
        }

        // If ref is a custom attribute
        if (ref.includes('cattr')) {
          const customAttrResult = handleCustomAttributeRefs({
            ref,
            result,
            refMappingArr,
            i,
            valueToUpdate: zipFilePath,
          });
          zipFilePath = customAttrResult.valueToUpdate;
          refMappingArr = customAttrResult.refMappingArray;
          // eslint-disable-next-line no-continue
          continue;
        }

        refMappingObj.value =
          result[ref] || refDbFieldsMapping[typeOfDownload][ref].defaultValue();

        if (postProcessing && postProcessing.args) {
          refMappingObj.value = postProcessing.call({
            options: postProcessing.args[ref],
            resolvedValue: refMappingObj.value,
          });
        }

        refMappingObj.value = `${refMappingObj.value}`.replace(/\//g, '-');

        zipFilePath = zipFilePath.replace(`{${ref}}`, refMappingObj.value);
        // eslint-disable-next-line no-continue
        continue;
      }

      zipFilePath = zipFilePath.replace(`{${ref}}`, result);
    }

    zipFilePath = zipFilePath.trim();

    return zipFilePath;
  } catch (err) {
    server.log.error(`GenerateZipFilePath failed ${err}`);
    return 'root';
  }
};

const moveFileToNewPath = async (
  connection,
  s3TemporaryUploadFolder,
  s3FilePath,
  fileUploadRequest,
  tmpDestinationDir
) => {
  const finalFile = s3FilePath.split('/').pop();
  const nestedBasePath = await generateZipFilePath(
    connection,
    fileUploadRequest
  );

  if (!nestedBasePath) {
    server.log.error(
      `[generateZipFilePath] Could not create nestedBasePath. Returning. fileUploadRequest: ${fileUploadRequest.id}`
    );
    return null;
  }

  await commonService.makeTmpS3DownloadDir(
    path.join(s3TemporaryUploadFolder, tmpDestinationDir, nestedBasePath)
  );

  const completeNestedFilePath = path.join(
    tmpDestinationDir,
    nestedBasePath,
    finalFile
  );

  const sourcePath = path.join(s3TemporaryUploadFolder, s3FilePath);
  const destinationPath = path.join(
    s3TemporaryUploadFolder,
    completeNestedFilePath
  );
  // keep only substring, before the last hash
  const removePath = path.join(
    s3TemporaryUploadFolder,
    s3FilePath.split('/')[0]
  );

  await fs.promises.copyFile(
    sourcePath,
    destinationPath,
    fs.constants.COPYFILE_EXCL
  );

  return { nestedBasePath, completeNestedFilePath, removePath };
};

const downloadS3ContentsToTmpDir = async (connection, fileUploadRequest) => {
  const s3Dependencies = await getS3ObjectContents(
    connection,
    fileUploadRequest
  );
  if (!s3Dependencies) {
    return null;
  }
  const { s3Contents } = s3Dependencies;
  const fileUploadBucket = s3Dependencies.s3Bucket;
  if (!s3Contents) {
    return null;
  }
  const s3TemporaryRootFolder = config.s3UploadTmpDir;
  const s3TemporaryUploadFolder = path.join(
    s3TemporaryRootFolder,
    `upload-${fileUploadRequest.id}`
  );

  let s3FilePath = '';

  // eslint-disable-next-line no-restricted-syntax
  for (const s3Content of s3Contents) {
    s3FilePath = s3Content.Key;
    if (s3FilePath.slice(-1) !== pathSeperator) {
      const s3FolderPath = s3FilePath.substr(
        0,
        s3FilePath.lastIndexOf(pathSeperator)
      );

      // eslint-disable-next-line no-await-in-loop
      await commonService.makeTmpS3DownloadDir(
        path.join(s3TemporaryUploadFolder, s3FolderPath)
      );

      // eslint-disable-next-line no-await-in-loop
      await downloadFromS3(
        fileUploadBucket,
        s3FilePath,
        s3TemporaryUploadFolder
      );
    }
  }

  const paths = {
    fileUploadBucket,
    s3TemporaryRootFolder,
    s3TemporaryUploadFolder,
    s3FilePath, // Only to be used when files are requested from payment dashboard
  };
  return paths;
};

const getFinalFileNameTemplate = ({
  companyId,
  typeOfDownload = 'paymentDashboard',
}) => {
  const fileDownloadTemplatesObj =
    fileRenameTemplatesConfig.fileDownloadTemplates;
  const companyTemplateObj = fileDownloadTemplatesObj.companyTemplates.find(
    template => template.companyId === companyId
  );

  if (
    !companyTemplateObj ||
    !companyTemplateObj.nestedFilePathTemplates ||
    !companyTemplateObj.nestedFilePathTemplates[typeOfDownload]
  )
    return null;

  const zipFilePathTemplateObj =
    companyTemplateObj.nestedFilePathTemplates[typeOfDownload];

  return {
    template: zipFilePathTemplateObj.fileNameTemplate,
    options: zipFilePathTemplateObj.options,
    appendFileName: zipFilePathTemplateObj.appendFileName || false,
  };
};

const generateFinalFileName = async (connection, fileUploadRequest) => {
  try {
    const fileUploadRequestDetails =
      await repository.getFileUploadRequestDetails(
        connection,
        fileUploadRequest.id
      );

    const { companyId, deviceId, siteId, devicePath, jobId } =
      fileUploadRequestDetails;
    let { userParams: jobDataUserParams } = fileUploadRequestDetails;
    jobDataUserParams = jobDataUserParams ? JSON.parse(jobDataUserParams) : {};

    const deviceFile = await getDeviceFileId(connection, deviceId, devicePath);
    const deviceFileId = deviceFile.id;

    // Get zip file path template using company id
    const finalFileNameTemplate = await getFinalFileNameTemplate({
      companyId,
    });

    if (!finalFileNameTemplate) {
      server.log.error(
        `[generateFinalFileName] finalFileNameTemplate not found fileUploadRequest:${fileUploadRequest.id}`
      );
      return null;
    }

    const {
      template,
      options = {},
      appendFileName = false,
    } = finalFileNameTemplate;

    const templateVariableRefs = template.match(/{(.*?)}/g);
    const typeOfDownload = 'paymentDashboard';

    const reqBody = {
      deviceId,
      deviceFileId,
      siteId,
      companyId,
      fileUploadRequestId: fileUploadRequest.id,
      jobId,
      userParams: jobDataUserParams,
    };
    let refMappingArr = getRefValueMappingFromTemplate({
      templateVariableRefs,
      typeOfDownload,
      reqBody,
      options,
    });

    let results = [];
    results = await Promise.all(
      refMappingArr.map(refValueMappingObj => {
        const { type, value, params = [] } = refValueMappingObj;
        if (type === 'query') {
          return repository.resolveQuery(connection, value, params);
        }
        return value; // defaultValue
      })
    );

    let finalFileName = template;
    for (let i = 0; i < results.length; i++) {
      const refMappingObj = refMappingArr[i];
      const { ref, type, postProcessing } = refMappingObj;
      let result = results[i];
      if (type === 'query') {
        result = result.rows.length ? result.rows[0] : null;
        if (!result) {
          server.log.error(
            `[generateFinalFileName] Query resolution for final file name template failed for ref:${ref} fileUploadRequest: ${fileUploadRequest.id} `
          );
          refMappingObj.value = '';
          finalFileName = finalFileName.replace(
            `{${ref}}`,
            refMappingObj.value
          );
          // eslint-disable-next-line no-continue
          continue;
        }

        // If ref is a custom attribute
        if (ref.includes('cattr')) {
          const customAttrResult = handleCustomAttributeRefs({
            ref,
            result,
            refMappingArr,
            i,
            valueToUpdate: finalFileName,
          });
          finalFileName = customAttrResult.valueToUpdate;
          refMappingArr = customAttrResult.refMappingArray;
          // eslint-disable-next-line no-continue
          continue;
        }

        refMappingObj.value =
          result[ref] || refDbFieldsMapping[typeOfDownload][ref].defaultValue();

        if (postProcessing && postProcessing.args) {
          refMappingObj.value = postProcessing.call({
            options: postProcessing.args[ref],
            resolvedValue: refMappingObj.value,
          });
        }

        refMappingObj.value = `${refMappingObj.value}`.replace(/\//g, '-');

        finalFileName = finalFileName.replace(`{${ref}}`, refMappingObj.value);
        // eslint-disable-next-line no-continue
        continue;
      }

      finalFileName = finalFileName.replace(`{${ref}}`, result);
    }

    finalFileName = finalFileName
      .replaceAll('null', '')
      .replace(/[\s\s'"`]+/gi, ' ')
      .trim();

    return { value: finalFileName, appendFileName };
  } catch (err) {
    logger.error(
      { error: err },
      `[generateZipFileName] error: ${err.message} fileUploadRequest: ${fileUploadRequest.id}.`
    );
    return { value: 'file-name', appendFileName: false };
  }
};

/**
 * Serves as a function to prepare all necessary properties in order to download packages, zip and send to S3
 * @param {*} connection
 * @param {Object} fileUploadRequest
 * @returns
 */
const completePackagingFile = async (connection, fileUploadRequest) => {
  if (!fileUploadRequest) {
    logger.warn(
      `${moduleName}.completePackagingFile: No complete FileUploadRequest`
    );
    return;
  }

  const fileUploadRequestName = fileUploadRequest.name;
  try {
    const hasAnyCompleteJob = helper.areAnyJobsComplete(fileUploadRequest.jobs);
    if (!hasAnyCompleteJob) {
      await updateRequestAndNotifyUsers(
        connection,
        fileUploadRequest,
        null,
        constants.NotificationTypes.FILE_UPLOAD_IN_PROGRESS.level,
        'No complete jobs'
      );
      return;
    }
    const enablePullLog = await repository.isFeatureFlagEnabled(
      connection,
      fileUploadRequest?.createdBy,
      'ICS_PULL_LOG_ENABLE'
    );
    logger.info(
      `${enablePullLog} feature enable for pull data ICS_PULL_LOG_ENABLE ${JSON.stringify(fileUploadRequest)}`
    );
    if (enablePullLog) {
      return;
    }
    const paths = await downloadS3ContentsToTmpDir(
      connection,
      fileUploadRequest
    );

    if (!paths) {
      server.log.error(
        `[completePackagingFile] : No s3 download paths available:${fileUploadRequest.id} Returning...`
      );
      return;
    }

    const {
      s3TemporaryUploadFolder,
      s3FilePath,
      s3TemporaryRootFolder,
      fileUploadBucket,
    } = paths;
    const tmpDestinationDir = `tmp-destination-${fileUploadRequest.id}`;
    const tmpFilesDir = `tmp-files-${fileUploadRequest.id}`; // temporary directory for storing leaf files
    let newFilePathsObj = null;
    let isRecursiveUnzippingDone = false;
    // move the files to new path and recursively unzip all the files
    if (
      fileUploadRequest.requestedFrom ===
      fileUploadRequestedFrom.PAYMENT_DASHBOARD
    ) {
      newFilePathsObj = await moveFileToNewPath(
        connection,
        s3TemporaryUploadFolder,
        s3FilePath,
        fileUploadRequest,
        tmpDestinationDir
      );

      if (!newFilePathsObj) {
        server.log.error(
          `[completePackagingFile] Missing new file paths object to completePackagingFile. fileUploadRequest: ${fileUploadRequest.id}`
        );
      } else {
        // eslint-disable-next-line prefer-const
        let { nestedBasePath = '', removePath } = newFilePathsObj;
        nestedBasePath = path.join(
          s3TemporaryUploadFolder,
          tmpDestinationDir,
          nestedBasePath
        );
        server.log.info(
          `[completePackagingFile] [unzipRecursively] started for ${nestedBasePath} fileUploadRequest: ${fileUploadRequest.id}`
        );

        try {
          isRecursiveUnzippingDone = true;
          await zipUtils.unzipRecursively(nestedBasePath);
        } catch (error) {
          isRecursiveUnzippingDone = false;
          server.log.error(
            `[completePackagingFile] [unzipRecursively] failed for ${nestedBasePath} fileUploadRequest: ${fileUploadRequest.id} error:${error}`
          );
        } finally {
          if (!isRecursiveUnzippingDone) {
            await fs.promises.rm(
              path.join(s3TemporaryUploadFolder, tmpDestinationDir),
              { recursive: true }
            );
          } else {
            // Remove the original download directory, if recursive unzipping succeeds
            await fs.promises.rm(removePath, { recursive: true });

            // Copy leaf level files from nestedBasePath to nestedBasePath/tmpFilesDir
            await helper.copyLeafFilesToTempDir(
              nestedBasePath,
              path.join(nestedBasePath, tmpFilesDir)
            );

            const finalFileNameObj = await generateFinalFileName(
              connection,
              fileUploadRequest
            );

            const { value: finalFileName, appendFileName = false } =
              finalFileNameObj;

            await helper.moveFinalFilesToBase(
              nestedBasePath,
              path.join(nestedBasePath, tmpFilesDir),
              finalFileName,
              appendFileName
            );
          }
        }
      }
    }

    const [packageUrl, zipDependencies] = await manageZip(
      fileUploadRequestName,
      fileUploadRequest.id,
      s3TemporaryRootFolder,
      fileUploadRequest.requestedFrom ===
        fileUploadRequestedFrom.PAYMENT_DASHBOARD &&
        newFilePathsObj &&
        isRecursiveUnzippingDone
        ? path.join(s3TemporaryUploadFolder, tmpDestinationDir)
        : s3TemporaryUploadFolder,
      fileUploadBucket
    );

    uploadToS3(zipDependencies, paths.fileUploadBucket);
    await updateRequestAndNotifyUsers(
      connection,
      fileUploadRequest,
      packageUrl,
      constants.NotificationTypes.FILE_UPLOAD_READY.level,
      null
    );
  } catch (err) {
    server.log.error(
      `[completePackagingFile] error:catch fileuploadrequest:${fileUploadRequest.id} err:${err}`
    );
    throw err;
  } finally {
    server.log.info(
      `[completePackagingFile] :finally updatePackagingInProgress:${fileUploadRequest.id}`
    );
    await repository.updatePackagingInProgress(
      connection,
      fileUploadRequest,
      false
    );
  }
};

/**
 * Updates device file before beginning packaging and handles the db transactions
 * @param {Object} fileUploadRequests
 * @returns
 */
const packageFilesAsync = async fileUploadRequests => {
  const connection = await server.db.write.getConnection();
  try {
    const fileUploadRequestIds = fileUploadRequests
      .map(request => request.id)
      .join(',');
    await connection.execute(
      `
                UPDATE file_upload_request SET
                packaging_in_progress = $2 
                WHERE id in ($1);
            `,
      [fileUploadRequestIds, true]
    );

    // eslint-disable-next-line no-restricted-syntax
    for (const request of fileUploadRequests) {
      // eslint-disable-next-line no-await-in-loop
      await completePackagingFile(connection, request);
    }
    // eslint-disable-next-line no-useless-catch
  } catch (error) {
    throw error;
  } finally {
    connection.done();
  }
};

/**
 *
 * @returns
 */
const packageFiles = async () => {
  const fileUploadRequests = await getOutstandingFileUploadRequest();
  if (!fileUploadRequests) {
    return;
  }

  await packageFilesAsync(fileUploadRequests);
};

/**
 * Determines the type of file download
 * @param {Object} reqBody
 * @returns {String} fileDownloadType
 */
const getFileDownloadType = reqBody => {
  const { devices, sites, siteTags } = reqBody;
  const { FileDownloadTypes } = constants;

  if (!devices.length && !sites.length && !siteTags.length)
    return FileDownloadTypes.SITE_TAGS;

  const fileDownloadType = FileDownloadTypes.SINGLE_DEVICE;
  if (sites && sites.length) {
    return FileDownloadTypes.SINGLE_SITE;
  }
  if (siteTags && siteTags.length) {
    return FileDownloadTypes.SITE_TAGS;
  }
  return fileDownloadType;
};

/**
 * Generate default zip file name based on the company template
 * @param {*} reqData
 * @returns {String} zipFileName
 */
async function generateZipFileName(reqData) {
  try {
    const { reqBody, companyId, userParams } = reqData;
    const { sites, deviceFileId = '', deviceId, siteTags } = reqBody;
    const { FileDownloadTypes } = constants;
    const { SINGLE_DEVICE, SINGLE_SITE, SITE_TAGS } = FileDownloadTypes;

    Object.assign(reqBody, { companyId, userParams });

    // Determine the type of download - singleDevice, singleSite, siteTags
    const typeOfDownload = getFileDownloadType(reqBody);
    switch (typeOfDownload) {
      case SINGLE_DEVICE: {
        const results = await repository.getSiteIdFromDeviceId(deviceId);
        reqBody.siteId =
          results.length && results[0].siteId ? results[0].siteId : '';
        break;
      }
      case SINGLE_SITE: {
        reqBody.siteId = sites.length ? sites[0].id : '';
        break;
      }
      case SITE_TAGS: {
        reqBody.siteTagIds = siteTags.map(siteTag => siteTag.id);
        break;
      }
      default: {
        break;
      }
    }

    // Get zip file name template using company id and type of download
    const { template: zipFileNameTemplate, options = {} } =
      getZipFileNameTemplate({
        companyId,
        typeOfDownload,
        deviceFileId,
      });

    const templateVariableRefs = zipFileNameTemplate.match(/{(.*?)}/g);

    // Get ref value mapping object - to form the query - from template variable refs
    let refMappingArr = getRefValueMappingFromTemplate({
      templateVariableRefs,
      typeOfDownload,
      reqBody,
      options,
    });

    // Execute query or use default value
    let results = [];
    const connection = await server.db.read.getConnection();
    try {
      results = await Promise.all(
        refMappingArr.map(refValueMappingObj => {
          const { type, value, params = [] } = refValueMappingObj;
          if (type === 'query') {
            return repository.resolveQuery(connection, value, params);
          }
          return value; // defaultValue
        })
      );
    } catch (error) {
      logger.error(
        `[generateZipFileName] Query resolution for zip file name template failed 1 ${error}`
      );
      throw error;
    } finally {
      connection.done();
    }

    let zipFileName = zipFileNameTemplate;
    for (let i = 0; i < results.length; i++) {
      const refMappingObj = refMappingArr[i];
      const { ref, type } = refMappingObj;
      let result = results[i];
      if (type === 'query') {
        result = result.rows.length ? result.rows[0] : null;
        if (!result) {
          server.log.warn(
            `[generateZipFileName] Query resolution for ref ${ref} failed`,
            { reqData }
          );
          refMappingObj.value = '';
          zipFileName = zipFileName.replace(`{${ref}}`, refMappingObj.value);
          // eslint-disable-next-line no-continue
          continue;
        }

        // If ref is a custom attribute
        if (ref.includes('cattr')) {
          const customAttrResult = handleCustomAttributeRefs({
            ref,
            result,
            refMappingArr,
            i,
            valueToUpdate: zipFileName,
          });
          zipFileName = customAttrResult.valueToUpdate;
          refMappingArr = customAttrResult.refMappingArray;
          // eslint-disable-next-line no-continue
          continue;
        }

        refMappingObj.value =
          result[ref] || refDbFieldsMapping[typeOfDownload][ref].defaultValue();

        zipFileName = zipFileName.replace(`{${ref}}`, refMappingObj.value);
        // eslint-disable-next-line no-continue
        continue;
      }

      zipFileName = zipFileName.replace(`{${ref}}`, result);
    }

    zipFileName = `${zipFileName}`.trim();
    zipFileName = zipFileName.replace(/\//g, '-');

    return { name: zipFileName || 'no-name' };
  } catch (error) {
    server.log.info({ reqData }, '[GenerateZipFileName] debug');
    server.log.error(`[GenerateZipFileName] error : ${error}`);
    return { name: 'no-name' };
  }
}

module.exports.public = {
  downloadPackage,
  createFileUploadRequest,
  getUploadFileRequest,
  packageFiles,
  fileUploadRequestUploader,
  generateZipFileName,
  generateZipFilePath,
};

module.exports.private = {
  getSitesByTags,
  findSiteWithActiveDevices,
  addSitesWithActiveDevices,
  getSitesToAdd,
  findSitesForUploadRequest,
  findByTargetIdWithDeviceFile,
  getDevicesForUploadRequest,
  findDevicesForFileUploadRequest,
  createFileUploadJob,
  postEmail,
  sendNotification,
  stripOtherOwnersData,
  getFileUploadRequestHavingRecipient,
  getJobsCreatedAndDeviceOwners,
  logFileUploadRequestCreated,
  updateRequestAndNotifyUsers,
  downloadFromS3,
  manageZip,
  uploadToS3,
  createFileUploadRequestUsingTransaction,
  getFileUploadRequestsUploadDependencies,
  getOutstandingFileUploadRequest,
  packageFilesAsync,
  completePackagingFile,
};
