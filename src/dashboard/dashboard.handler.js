/* eslint-disable max-len */
const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const deviceHelper = require('../../helpers/device-helper');

const prepareDashboardQuery = `
    select
        s.site_id as id,
        CASE WHEN health = 'CRITICAL' THEN ( s.name ) ELSE NULL END AS name,
        CASE WHEN health = 'CRITICAL' THEN ( s.formatted_address ) ELSE NULL END AS address,
        round(s.longitude, 4) as longitude,
        round(s.latitude,4) as latitude,
        health,
        (
            select
            rah.triggered_timestamp
            from report_alarm_history rah
            where rah.site_id = s.site_id
            and rah.alarm_type = 'site_critical'
            and rah.remedy_timestamp is null
            and rah.device_id is null
            order by rah.triggered_timestamp desc nulls last
            limit 1
        ) last_critical_alarm,
        f.counts as health_counts,
        f.conditions as critical_counts
        
    from site s
    INNER JOIN LATERAL get_site_health(s.site_id) health on TRUE
    left join lateral (
        select
            jsonb_object_agg(h.health, h.count) filter ( where health is not null and h.name is null and h.value is null ) counts,
            jsonb_agg(jsonb_build_object('name', h.name, 'value', h.value, 'count', h.count, 'devices', h.devices)) filter ( where h.name is not null and h.value is not null ) conditions
        from (
            select
            health,
            dh.name,
            dh.value,
            array_agg(DISTINCT target_id) devices,
            count(DISTINCT t.target_id) count
            from public.target t,
            get_device_health(target_id) as health
            left join lateral (
            select
                h.name,
                h.value
                from ics_state._device_health h
                where h.device_id = t.target_id
                and h.is_critical
                and health = 'OUT_OF_SERVICE'
            ) dh on true
            where t.active
            and t.site_id = s.site_id
            group by grouping sets (
                (health, dh.name, dh.value),
                (health)
            )
        ) h
    ) f on true
    where s.active AND s.visible
    and exists (
        select 1
        from user_site_authorization usa
        where usa.site_id = s.site_id
        and usa.user_id = $1
    )
`;

const prepareDashboardMapPinQuery = `
    SELECT
        s.site_id as id,
        s.name,
        s.formatted_address as address,
        (
            select jsonb_agg(t.name)
            from tag t
            inner join site_tag st on t.id = st.tag_id AND st.deleted = false
            where st.site_id = s.site_id
        ) tags
    FROM site s
        JOIN user_site_authorization u ON u.site_id = s.site_id
    WHERE s.site_id = $2 AND u.user_id = $1
`;

module.exports = {
  getDashboard: async (req, res, next) => {
    const userId = req.user.sub;

    try {
      const allSites = await server.db.read.rows(prepareDashboardQuery, [
        userId,
      ]);

      const siteStatus = {
        TOTAL: allSites.length,
        NORMAL: allSites.filter(x => x.health === 'NORMAL').length,
        WARNING: allSites.filter(x => x.health === 'WARNING').length,
        CRITICAL: allSites.filter(x => x.health === 'CRITICAL').length,
        UNKNOWN: allSites.filter(x => x.health === 'UNKNOWN').length,
        INACTIVE: allSites.filter(x => x.health === 'INACTIVE').length,
      };

      const getCountPerDeviceHealth = key => {
        let count = 0;
        allSites.forEach(item => {
          if (item.healthCounts && item.healthCounts[key]) {
            count += item.healthCounts[key];
          }
        });
        return count;
      };

      const deviceStatus = {
        OPERATIONAL: getCountPerDeviceHealth('OPERATIONAL'),
        OUT_OF_SERVICE: getCountPerDeviceHealth('OUT_OF_SERVICE'),
        UNKNOWN: getCountPerDeviceHealth('UNKNOWN'),
        INACTIVE: getCountPerDeviceHealth('INACTIVE'),
      };
      deviceStatus.TOTAL = Object.values(deviceStatus).reduce((a, b) => a + b);

      let allOOSDevices = [];
      const allOOSSites = [];
      const oosCategories = {};
      allSites.forEach(item => {
        if (item.criticalCounts && item.criticalCounts) {
          const { criticalCounts, id } = item;
          criticalCounts.forEach(cat => {
            const { name, value, devices } = cat;
            const match = deviceHelper.getOOSCategoryAndCondition({
              name,
              value,
            });
            if (match) {
              allOOSDevices = allOOSDevices.concat(devices);
              allOOSSites.push(id);

              if (!item.critical_conditions) {
                // eslint-disable-next-line no-param-reassign
                item.critical_conditions = [];
              }
              item.critical_conditions.push(match.category);

              // Check if category exist
              if (!oosCategories[match.category]) {
                oosCategories[match.category] = {
                  name: match.category,
                  deviceCount: 0,
                  siteCount: [],
                  conditions: [],
                };
              }

              oosCategories[match.category].deviceCount = (
                oosCategories[match.category].deviceCount || []
              ).concat(devices);
              oosCategories[match.category].siteCount = (
                oosCategories[match.category].siteCount || []
              ).concat([id]);

              // Check if condition exist
              if (
                !oosCategories[match.category].conditions.find(
                  i => i.name === match.condition
                )
              ) {
                oosCategories[match.category].conditions.push({
                  name: match.condition,
                  deviceCount: 0,
                  siteCount: [],
                });
              }
              const targetCondition = oosCategories[
                match.category
              ].conditions.find(i => i.name === match.condition);
              targetCondition.deviceCount = (
                targetCondition.deviceCount || []
              ).concat(devices);
              targetCondition.siteCount = (
                targetCondition.siteCount || []
              ).concat([id]);
            }
          });
        }
      });

      // eslint-disable-next-line no-restricted-syntax
      for (const [key, value] of Object.entries(oosCategories)) {
        oosCategories[key].deviceCount = [...new Set(value.deviceCount)].length;
        oosCategories[key].siteCount = [...new Set(value.siteCount)].length;

        oosCategories[key].conditions.forEach(condition => {
          // eslint-disable-next-line no-param-reassign
          condition.deviceCount = [...new Set(condition.deviceCount)].length;
          // eslint-disable-next-line no-param-reassign
          condition.siteCount = [...new Set(condition.siteCount)].length;
        });
      }

      const totalAffectedSites = [...new Set(allOOSSites)].length;
      const totalAffectedDevices = [...new Set(allOOSDevices)].length;

      const prepareCriticalSites = allSites.map(site => ({
        ...(site.health === 'CRITICAL'
          ? {
              name: site.name,
              address: site.address,
              critical_conditions: [...new Set(site.critical_conditions)],
              critical_timestamp: site.lastCriticalAlarm,
              device_counts: {
                TOTAL: Object.values(site.healthCounts).reduce((a, b) => a + b),
                UNKNOWN: site.healthCounts.UNKNOWN || 0,
                INACTIVE: site.healthCounts.INACTIVE || 0,
                OPERATIONAL: site.healthCounts.OPERATIONAL || 0,
                OUT_OF_SERVICE: site.healthCounts.OUT_OF_SERVICE || 0,
              },
              id: site.id,
              latitude: parseFloat(site.latitude),
              longitude: parseFloat(site.longitude),
              site_health: site.health,
            }
          : {
              id: site.id,
              latitude: parseFloat(site.latitude),
              longitude: parseFloat(site.longitude),
              site_health: site.health,
            }),
      }));

      res.send({
        siteStatus,
        deviceStatus,
        deviceOOS: {
          totalAffectedSites,
          totalAffectedDevices,
          results: oosCategories,
        },
        sites: prepareCriticalSites,
      });

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  getMarkerSummary: async (req, res, next) => {
    const userId = req.user.sub;
    const { siteId } = req.query;

    try {
      const siteSummary = await server.db.read.row(
        prepareDashboardMapPinQuery,
        [userId, siteId]
      );

      res.send(siteSummary);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
