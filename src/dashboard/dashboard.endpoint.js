const Joi = require('joi');

const { server } = require('../../app');
const env = require('../../env');
const { validateQuery } = require('../../lib/pre');
const dashboard = require('./dashboard.handler');

const BASE_PATH = `${env.config.base}/dashboard`;

/**
 * @swagger
 * /dashboard:
 *     get:
 *       tags:
 *         - dashboard
 *       summary: sites/devices status for dashboard widgets
 *       description: Aggregates sites and devices data used for rendering the dashboard widgets
 *       parameters:
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *       responses:
 *         200:
 *           description: Successful response
 *           schema:
 *             type: object
 *             properties:
 *               deviceOOS:
 *                 type: object
 *                 properties:
 *                   totalAffectedSites:
 *                     type: integer
 *                     description: Total number of sites that has at least one device in OOS status
 *                   totalAffectedDevices:
 *                     type: integer
 *                     description: Total number of devices that are in OOS status
 *                   results:
 *                     type: object
 *                     description: Out of service(OOS) categories and conditions
 *                     properties:
 *                       OOSCondition:
 *                         type: object
 *                         description: OOS category and detail (key name vary depending on condition)
 *                         properties:
 *                           name:
 *                             type: string
 *                             description: category name
 *                           conditions:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 deviceCount:
 *                                   type: integer
 *                                   description: number of out-of-service devices in this condition
 *                                 siteCount:
 *                                   type: integer
 *                                   description: number of out-of-service devices in this condition
 *                                 name:
 *                                   type: string
 *                                   description: condition name
 *                           deviceCount:
 *                             type: integer
 *                             description: number of out-of-service devices in this category
 *                           siteCount:
 *                             type: integer
 *                             description: number of sites contain out-of-service devices in this category
 *               siteStatus:
 *                 type: object
 *                 description: number of sites in each status.
 *                 properties:
 *                   NORMAL:
 *                     type: integer
 *                     description: Total number of NORMAL sites
 *                   CRITICAL:
 *                     type: integer
 *                     description: Total number of CRITICAL sites
 *                   UNKNOWN:
 *                     type: integer
 *                     description: Total number of UNKNOWN sites
 *                   WARNING:
 *                     type: integer
 *                     description: Total number of WARNING sites
 *               deviceStatus:
 *                 type: object
 *                 description: number of devices in each status
 *                 properties:
 *                   OUT_OF_SERVICE:
 *                     type: integer
 *                     description: Total number of OUT_OF_SERVICE devices
 *                   OPERATIONAL:
 *                     type: integer
 *                     description: Total number of OPERATIONAL devices
 *                   INACTIVE:
 *                     type: integer
 *                     description: Total number of INACTIVE devices
 *                   UNKNOWN:
 *                     type: integer
 *                     description: Total number of UNKNOWN devices
 *               sites:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     siteId:
 *                       type: string
 *                       description: id
 *                     critical_conditions:
 *                       type: array
 *                       items:
 *                         type: string
 *                         description: Out-of-service condition category from the devices in the site
 *                     device_counts:
 *                       type: object
 *                       properties:
 *                         TOTAL:
 *                           type: integer
 *                           description: Total number of devices
 *                         INACTIVE:
 *                           type: integer
 *                           description: Total number of INACTIVE devices
 *                         OUT_OF_SERVICE:
 *                           type: integer
 *                           description: Total number of OUT_OF_SERVICE devices
 *                         OPERATIONAL:
 *                           type: integer
 *                           description: Total number of OPERATIONAL devices
 *                         UNKNOWN:
 *                           type: integer
 *                           description: Total number of UNKNOWN devices
 *                     address:
 *                       type: string
 *                       description: Site's address
 *                     name:
 *                       type: string
 *                       description: Site's name
 *                     critical_timestamp:
 *                       type: string
 *                       description: Site critical alarm triggered time/elapsed time
 *                     contact_email:
 *                       type: string
 *                       description: Site's contact email
 *                     phone_number:
 *                       type: string
 *                       description: Site's contact phone
 *                     latitude:
 *                       type: integer
 *                       description: Site's geo coordinates(latitude)
 *                     longitude:
 *                       type: integer
 *                       description: Site's geo coordinates(longitude)
 *                     tags:
 *                       type: array
 *                       description: Site's tags
 *                       items:
 *                         type: string
 *                     site_health:
 *                       type: string
 *                       description: Site's health status (site status)
 *         401:
 *           $ref: '#/responses/authenticationFailed'
 *         500:
 *           $ref: '#/responses/internalServerError'
 */
server.get(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [dashboard.getDashboard]
);

/**
 * @swagger
 * /dashboard/markersummary:
 *     get:
 *       tags:
 *         - dashboard
 *       summary: get site information for the google map marker
 *       parameters:
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *         - company:
 *           type: string
 *           description: Site ID
 *           format: uuid
 *           required: true
 *       responses:
 *         200:
 *           description: Successful response
 *         401:
 *           $ref: '#/responses/authenticationFailed'
 *         500:
 *           $ref: '#/responses/internalServerError'
 */
server.get(
  {
    path: `${BASE_PATH}/markersummary`,
    version: '0.0.1',
  },
  [
    validateQuery({
      siteId: Joi.string().guid(),
    }),
    dashboard.getMarkerSummary,
  ]
);
