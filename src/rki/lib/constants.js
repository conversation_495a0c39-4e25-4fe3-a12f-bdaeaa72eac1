const CONSTANTS = {
  job: {
    destination: {
      system: 'invenco.system',
    },
  },
  NotificationTypes: {
    RKI_REQUEST_CREATED: {
      key: 'rki-request-created',
      message: 'RKI request %s was created',
      level: 'INFO',
    },

    RKI_REQUEST_UPDATED: {
      key: 'rki-request-updated',
      message: 'RKI request %s was updated',
      template: '%s updated RKI request (%s) for %s device(s)',
      subject: 'Pending - RKI request %s',
      level: 'INFO',
    },

    RKI_REQUEST_CREATED_CREATOR: {
      key: 'rki-request-created-creator',
      message: 'RKI request %s was created',
      template: 'RKI request %s for %s device(s) successfully created',
      subject: 'Submitted - RKI request %s',
      level: 'INFO',
    },

    RKI_REQUEST_CREATED_APPROVER: {
      key: 'rki-request-created-approver',
      message: '%s requested you to review RKI request %s',
      template: '%s created a new RKI request (%s) for %s device(s)',
      subject: 'Pending - RKI request %s',
      level: 'INFO',
    },

    RKI_REQUEST_APPROVED: {
      key: 'rki-request-approved',
      message: 'RKI request %s was approved',
      template: 'RKI request %s for %s device(s) was approved',
      subject: 'Approved - RKI request %s',
      level: 'INFO',
    },

    RKI_REQUEST_REJECTED: {
      key: 'rki-request-rejected',
      message: 'RKI request %s was rejected',
      template: 'RKI request %s for %s device(s) was rejected',
      subject: 'Rejected - RKI request %s',
      level: 'INFO',
    },

    RKI_REQUEST_FAILED: {
      key: 'rki-request-failed',
      message:
        'RKI request %s has failed for %s with the following exception "%s"',
      template: 'RKI request %s for %s device(s) has failed',
      subject: 'Failed - RKI request %s',
      level: 'WARN',
    },

    RKI_DEVICE_SUCCESS: {
      key: 'rki-device-success',
      message: 'RKI request %s has completed for %s',
      template: 'RKI request %s has completed for %s',
      subject: 'Key Bundle Applied Success - %s - RKI request %s',
      level: 'INFO',
    },

    RKI_DEVICE_FAILED: {
      key: 'rki-device-failed',
      message: 'RKI request %s has failed for %s',
      template: 'RKI request %s has failed for %s',
      subject: 'Key Bundle Applied Failed - %s - RKI request %s',
      level: 'WARN',
    },
  },
  EmailNotificationNameConstants: {
    RECIPIENT: 'recipient',
    ALARM_NAME: 'alarmName',
    RULE_NAME: 'ruleName',
    DEVICE_NAME: 'deviceName',
    DEVICE_SERIAL: 'serialNumber',
    SITE_NAME: 'siteName',
    TIMESTAMP: 'timestamp',
    STATE: 'state',
    URL: 'urlEnvironment',
    COMPANY_NAME: 'companyName',
    RULE_ID: 'ruleId',
    TYPE: 'type',
    RKI_REQUEST_NAME: 'rkiRequestName',
    RKI_NUM_DEVICES: 'rkiNumDevices',
    RKI_REQUEST_CREATOR_NAME: 'rkiRequestCreatorName',
    RKI_REQUEST_STATUS: 'rkiRequestStatus',
    RKI_DEVICE_STATUS: 'rkiDeviceStatus',
    VERSION_COMPONENT: 'component',
    VERSION_VALUE: 'version',
    FILE_UPLOAD_REQUEST_NAME: 'fileUploadRequestName',
  },
  SnapshotP2PeMetricsConstants: [
    'invenco.system.g7opt.opt-p2pe-aci-ver',
    'invenco.system.g7opt.opt-p2pe-onguard-ver',
  ],
  ValidCertificateIssuerCode: { LEGACY: '02', TAVE: '04' },
};

module.exports = CONSTANTS;
