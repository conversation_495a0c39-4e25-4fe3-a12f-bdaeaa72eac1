const PromiseStateMachine = require('promise-state-machine-es6');

const logger = require('../../../lib/logger').mainLogger();

const states = {
  retrievalSuccess: 0, // receive key bundle success
  retrievalFailed: 1, // receive key bundle failed
  success: 2, // key bundle applied success
  failed: 3, // key bundle applied failed
  removed: 5, // cancelled request
  retrieving: 6, // key retrieval
  ready: 7, // job ready
  installing: 8, // key installing
  approval: null, // rki waiting for approval state
};

const actions = {
  keyRequestRemoved: {
    name: 'key-request-removed',
    from: states.approval,
    to: states.removed,
  },
  keyRetrieval: {
    name: 'key-retrieval',
    from: states.approval,
    to: states.retrieving,
  },
  keyRetrievalSuccess: {
    name: 'receive-key-bundle-success',
    from: states.retrieving,
    to: states.retrievalSuccess,
  },
  keyRetrievalFailed: {
    name: 'receive-key-bundle-failed',
    from: states.retrieving,
    to: states.retrievalFailed,
  },
  keyInstallJobReady: {
    name: 'job-ready',
    from: states.retrievalSuccess,
    to: states.ready,
  },
  keyInstallSuccess: {
    name: 'success',
    from: [states.ready, states.installing],
    to: states.success,
  },
  keyInstallFailed: {
    name: 'failed',
    from: [states.approval, states.ready, states.installing],
    to: states.failed,
  },
  keyExpired: {
    name: 'key-expired',
    from: states.ready,
    to: states.failed,
  },
  keyInstallInProgress: {
    name: 'key-install-in-progress',
    from: states.ready,
    to: states.installing,
  },
};

module.exports = {
  states,
  actions,
  load: async (requestId, connection) => {
    const krd = (
      await connection.execute(
        `
            SELECT status
            FROM key_request_device
            WHERE key_request_device_id = $1
        `,
        [requestId]
      )
    ).rows[0];

    if (!krd) {
      // TODO: add logging
      throw new Error(
        `No key request device found with id: ${requestId}, unable to load state machine.`
      );
    }

    // load actions into the correct structure for the state machine eg `<name>: {from :[], to:'' }`
    const events = {};
    Object.keys(actions).forEach(key => {
      events[actions[key].name] = actions[key];
    });

    const sm = new PromiseStateMachine({
      initial: krd.status,
      events,
    });

    sm.on('transition', async (transition, from, to, message = null) => {
      logger.info(
        `Key request device transition [id: ${requestId} from: ${from}, to: ${to}]`
      );

      if (from === null) {
        await connection.execute(
          `
                    UPDATE key_request_device SET
                        status = $2,
                        message = $3
                    WHERE
                        key_request_device_id = $1 AND
                        status is NULL;
                `,
          [requestId, to, message]
        );
      } else {
        await connection.execute(
          `
                    UPDATE key_request_device SET
                        status = $3,
                        message = $4
                    WHERE
                        key_request_device_id = $1 AND
                        status = $2;
                `,
          [requestId, from, to, message]
        );
      }
    });

    return sm;
  },
};
