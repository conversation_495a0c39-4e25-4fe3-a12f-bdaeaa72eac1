/* eslint-disable no-underscore-dangle */
/* eslint-disable class-methods-use-this */
const uuid = require('uuid/v4');

const logger = require('../../../lib/logger').mainLogger();
const KRSHelper = require('../api/krs/krs-helper');
const {
  jobType: JOB_TYPE,
  jobStatus: JOB_STATUS,
} = require('../../../helpers/job-helper');
const {
  tryUpdatingKeyRequestSessionToComplete,
  getKeyRequestDevicesBySessionId,
} = require('../../../helpers/key-request-helper');
const { server } = require('../../../app');
const requestMachine = require('./key-request-device.statemachine');
const CONSTANTS = require('./constants');

class JobHelper {
  constructor(db) {
    this.db = db;
  }

  async getKeyRequestDeviceById(keyRequestDeviceId, keyRequestStatus) {
    // eslint-disable-next-line no-return-await
    return await this.db.read.row(
      `
            SELECT 
                krd.key_request_device_id,
                krd.key_request_session_id,
                krd.device_id,
                krd.job_id,
                krd.status,
                krd.message,
                krs.request_user_id,
                kg.key_group_id,
                krs.company_id,
                krs."name",
                t.serial_number,
                krs.request_user_id,
                ( SELECT iu.email FROM ics_user iu WHERE iu.id = krs.request_user_id ) AS request_user_email,
                krs.authorizer_user_id,
                ( SELECT iu.email FROM ics_user iu WHERE iu.id = krs.authorizer_user_id ) AS authorizer_user_email,
                krs.second_authorizer_user_id,
                ( SELECT iu.email FROM ics_user iu WHERE iu.id = krs.second_authorizer_user_id ) AS second_authorizer_user_email
            FROM key_request_device krd
            JOIN target t ON t.target_id = krd.device_id
            JOIN key_request_session krs ON krs.key_request_session_id = krd.key_request_session_id     
            JOIN key_group kg ON krs.company_id = kg.company_id AND krs.key_group_ref = kg.key_group_ref 
            WHERE t.active = true AND t.presence = 'PRESENT' AND krd.key_request_device_id = $1 AND krs.status = $2
        ;`,
      [keyRequestDeviceId, keyRequestStatus]
    );
  }

  async handleRKIJobCreation(keyRequestDevice, msgData, sessionId, timestamp) {
    await this.handleKeyRetrieveSuccess(keyRequestDevice.keyRequestDeviceId);
    const connection = await this.db.write.getConnection();

    try {
      const now = new Date();

      logger.info(
        `jobHelper.handleRKIJobCreation starting transaction for ${keyRequestDevice.keyRequestDeviceId}`
      );
      await connection.execute('BEGIN');

      const stateMachine = await requestMachine.load(
        keyRequestDevice.keyRequestDeviceId,
        connection
      );
      const job = await this._createJobForDevice(
        connection,
        keyRequestDevice,
        msgData,
        now
      );

      await this._createDeviceKeyBundle(
        connection,
        keyRequestDevice,
        msgData,
        timestamp
      );
      await this._createJobHistory(connection, job.id, now);
      await this._updateKeyRequestDevice(
        connection,
        keyRequestDevice,
        sessionId,
        job.id,
        timestamp
      );

      await stateMachine[requestMachine.actions.keyInstallJobReady.name]();
      await connection.execute('COMMIT');

      logger.info('jobHelper.handleRKIJobCreation finished transaction');
    } catch (err) {
      logger.error(`jobHelper.handleRKIJobCreation err: ${err}`);

      connection.execute('ROLLBACK');
      throw err;
    } finally {
      connection.done();
    }
  }

  async handleKeyRetrieveSuccess(keyRequestDeviceId) {
    const connection = await this.db.write.getConnection();

    try {
      await connection.execute('BEGIN');
      const stateMachine = await requestMachine.load(
        keyRequestDeviceId,
        connection
      );
      await stateMachine[requestMachine.actions.keyRetrievalSuccess.name]();
      await connection.execute('COMMIT');
    } catch (err) {
      logger.error(`jobHelper.handleKeyRetrieveSuccess err: ${err}`);
      connection.execute('ROLLBACK');
      throw err;
    } finally {
      connection.done();
    }
  }

  async handleKeyRetrieveFailure(keyRequestDevice, message) {
    const connection = await this.db.write.getConnection();
    const { keyRequestDeviceId, keyRequestSessionId } = keyRequestDevice;

    try {
      await connection.execute('BEGIN');
      const stateMachine = await requestMachine.load(
        keyRequestDeviceId,
        connection
      );
      await stateMachine[requestMachine.actions.keyRetrievalFailed.name](
        message
      );
      await tryUpdatingKeyRequestSessionToComplete(
        connection,
        keyRequestSessionId
      );
      await connection.execute('COMMIT');

      const notificationType = CONSTANTS.NotificationTypes.RKI_REQUEST_FAILED;

      const KRDs = await getKeyRequestDevicesBySessionId(keyRequestSessionId);

      const numOfFailedDevices = Array.from(KRDs).filter(
        d => d.status === 1
      ).length;
      const reason = Array.from(KRDs).find(
        d => d.keyRequestDeviceId === keyRequestDeviceId
      ).message;

      // keyRequestDevice data comes from getKeyRequestDeviceById()
      const rkiData = {
        id: keyRequestSessionId,
        companyId: keyRequestDevice.companyId,
        name: keyRequestDevice.name,
        serialNumber: keyRequestDevice.serialNumber,
        reason,
        numOfFailedDevices,
        isAutomatedRKI: keyRequestDevice.name.includes('[Automated]'),
      };

      const creator = await KRSHelper.getKRSCreator(keyRequestSessionId);

      const approvers = [];
      (await KRSHelper.getKRSApprover(keyRequestSessionId)).forEach(
        approver => {
          const { id, fullName, email, status } = approver;
          approvers.push({ id, fullName, email, status });
        }
      );

      await KRSHelper.createRKINotification(
        notificationType,
        rkiData,
        approvers,
        creator
      );
    } catch (err) {
      logger.error(`jobHelper.handleKeyRetrieveFailure err: ${err}`);
      connection.execute('ROLLBACK');
      throw err;
    } finally {
      connection.done();
    }
  }

  async _createJobForDevice(connection, keyRequestDevice, msgData, now) {
    const expiryDate = new Date(now.getTime());
    expiryDate.setDate(now.getDate() + 60);
    const params = [
      uuid(),
      keyRequestDevice.deviceId,
      CONSTANTS.job.destination.system,
      JOB_TYPE.FUTUREX_KEYLOAD_RKI,
      JSON.stringify(msgData),
      JOB_STATUS.NEW,
      now.toISOString(),
      expiryDate.toISOString(),
      keyRequestDevice.requestUserId,
      now.toISOString(),
    ];
    const query = `
    INSERT INTO job
        (id, device_id, destination, type, data, status, embargo, expiry, created_by, created_on )
    VALUES ( $1, $2, $3, $4, $5, $6, $7, $8, $9, $10 )
    RETURNING id, device_id, destination, type, data, status, embargo, expiry as expiration, created_by, created_on
;`;
    try {
      return (await connection.execute(query, params)).rows[0];
    } catch (err) {
      server.log.debug(
        {
          query,
          params,
        },
        `_createJobForDevice error`
      );
      throw err;
    }
  }

  async _createDeviceKeyBundle(connection, keyRequestDevice, msgData, now) {
    const params = [
      keyRequestDevice.deviceId,
      msgData,
      keyRequestDevice.keyGroupId,
      now,
      uuid(),
    ];
    const query = `
    INSERT INTO device_rki_key_bundle
        (device_id, key_certs, key_group_id, date_created, id )
    VALUES ( $1, $2, $3, $4, $5 )
    RETURNING id, device_id, key_certs, key_group_id
;`;
    try {
      return (await connection.execute(query, params)).rows[0];
    } catch (err) {
      server.log.debug(
        {
          query,
          params,
        },
        `_createDeviceKeyBundle error`
      );
      throw err;
    }
  }

  async _createJobHistory(connection, jobId, now) {
    const params = [jobId, JOB_STATUS.NEW, 'new job', now];
    const query = `
    INSERT INTO job_status_history
        (job_id, status, message, created_at)
    VALUES ( $1, $2, $3, $4)
    RETURNING job_id, status, message
;`;
    try {
      return (await connection.execute(query, params)).rows[0];
    } catch (err) {
      server.log.debug(
        {
          query,
          params,
        },
        `_createJobHistory error`
      );
      throw err;
    }
  }

  async _updateKeyRequestDevice(
    connection,
    keyRequestDevice,
    sessionId,
    jobId,
    timestamp
  ) {
    try {
      const params = [
        jobId,
        timestamp,
        sessionId,
        keyRequestDevice.keyRequestDeviceId,
      ];

      logger.info(
        `jobHelper._updateKeyRequestDevice params: ${JSON.stringify(params)}`
      );

      return (
        await connection.execute(
          `
                UPDATE key_request_device
                SET job_id = $1, date_signing = $2, session_id = $3
                WHERE key_request_device_id = $4
                RETURNING key_request_device_id, status, job_id, date_signing, session_id
            ;`,
          params
        )
      ).rows[0];
    } catch (err) {
      logger.error(`jobHelper._updateKeyRequestDevice err: ${err}`);

      throw err;
    }
  }
}

module.exports = JobHelper;
