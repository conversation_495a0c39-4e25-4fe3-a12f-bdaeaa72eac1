const Joi = require('joi');

const { server } = require('../../../../app');
const env = require('../../../../env');
const {
  validateParams,
  validateBody,
  requiresRole,
  validateQuery,
} = require('../../../../lib/pre');
const handler = require('./krs.handler');

const BASE_PATH = `${env.config.base}/rki`;
/**
 * @swagger
 *   "/rki/requests":
 *     post:
 *       tags:
 *         - RKI
 *       summary: "Create Key Request Sesion"
 *       description: "Manual RKI endpoint"
 *       produces:
 *         - "application/json"
 *       parameters:
 *         - in: body
 *           name: body
 *           schema:
 *             type: object
 *             required:
 *              - mfaCode
 *              - keyGroupRef
 *              - companyId
 *             properties:
 *               name:
 *                 type: string
 *                 description: KRS Session name
 *               approvers:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                      id:
 *                        type: string
 *                        format: uuid
 *               deviceRequests:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                      device:
 *                        type: object
 *                        properties:
 *                           id:
 *                            type: number
 *               mfaCode:
 *                 type: string
 *                 description: MFA Code
 *               keyGroupRef:
 *                 type: string
 *                 description: New Key Group for KRS
 *               companyId:
 *                 type: string
 *                 description: Company ID related to new KRS
 *                 format : uuid
 *       responses:
 *         "200":
 *           description: "Successful response"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "404":
 *           description: "Device invalid or User has no right to access device(s)"
 *         "406":
 *           description: MFA code invalid
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/requests`,
    version: '1.0.0',
  },
  [
    requiresRole(['RKI']),
    validateBody({
      name: Joi.string(),
      approvers: Joi.array().items({
        id: Joi.string().guid(),
      }),
      deviceRequests: Joi.array()
        .min(1)
        .items(
          Joi.object({
            device: Joi.object({
              id: Joi.number(),
            }),
          })
        ),
      mfaCode: Joi.string(),
      keyGroupRef: Joi.string(),
      companyId: Joi.string().guid(),
    }),
    handler.krsSessionCreation,
  ]
);
/**
 * @swagger
 *   "/rki/requests":
 *     get:
 *       tags:
 *         - RKI
 *       summary: "Get All Key Request Session with status and pageindex and page size"
 *       description: "Get All Key Request Session with status and pageindex and page size"
 *       produces:
 *         - "application/json"
 *       parameters:
 *         - in: query
 *           name: pageIndex
 *           type: number
 *           required: false
 *           default: 0
 *         - in: query
 *           name: pageSize
 *           type: number
 *           required: false
 *           default: 50
 *         - in: query
 *           name: status
 *           description: krs status (i.e. 1,2,3,4) default empty
 *           type: string
 *           required: false
 *           default: null
 *       responses:
 *         "200":
 *           description: "Successful response"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */

server.get(
  {
    path: `${BASE_PATH}/requests`,
    version: '1.0.0',
  },
  [
    requiresRole(['RKI']),
    validateQuery({
      pageIndex: Joi.number().default(0).integer().min(0).optional(),
      pageSize: Joi.number().default(100).integer().min(1).optional(),
      status: Joi.string()
        .regex(/^\d{1,1}(,\d{1})*(\.\d+)?$/)
        .optional(),
    }),
    handler.getAllRKISessionCreation,
  ]
);

/**
 * @swagger
 *   "/rki/requests/{krsid}":
 *     get:
 *       tags:
 *         - RKI
 *       summary: "Get Key Request Session"
 *       description: "Get Key Request Session by ID"
 *       produces:
 *         - "application/json"
 *       parameters:
 *         - in: path
 *           name: krsid
 *           type: string
 *           format: uuid
 *           required: true
 *       responses:
 *         "200":
 *           description: "Successful response"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/requests/:krsid`,
    version: '1.0.0',
  },
  [
    requiresRole(['RKI']),
    validateParams({
      krsid: Joi.string().guid(),
    }),
    handler.getRKISessionDetail,
  ]
);

/**
 * @swagger
 *   "/rki/requests/{krsid}":
 *     patch:
 *       tags:
 *         - RKI
 *       summary: "Update Key Request Session"
 *       description: "Manual RKI endpoint"
 *       produces:
 *         - "application/json"
 *       parameters:
 *         - in: path
 *           name: krsid
 *           type: string
 *           format: uuid
 *           required: true
 *         - in: body
 *           name: body
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: True for Approval and False for Rejection
 *               approvers:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                      id:
 *                        type: string
 *                        format: uuid
 *               deviceRequests:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                      device:
 *                        type: object
 *                        properties:
 *                           id:
 *                            type: number
 *               mfaCode:
 *                  type: string
 *                  description: MFA Code
 *               keyGroupRef:
 *                  type: string
 *                  description: New Key Group for KRS
 *               companyId:
 *                  type: string
 *                  format: uuid
 *                  description: Company ID related to new KRS
 *       responses:
 *         "200":
 *           description: "Successful response in case of KRS cretaed or any validation error"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "406":
 *           description: MFA code invalid
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.patch(
  {
    path: `${BASE_PATH}/requests/:krsid`,
    version: '1.0.0',
  },
  [
    requiresRole(['RKI']),
    validateParams({
      krsid: Joi.string().guid(),
    }),
    validateBody({
      name: Joi.string(),
      approvers: Joi.array().items({
        id: Joi.string().guid(),
      }),
      deviceRequests: Joi.array()
        .min(1)
        .items(
          Joi.object({
            device: Joi.object({
              id: Joi.number(),
            }),
          })
        ),
      mfaCode: Joi.string(),
      keyGroupRef: Joi.string(),
      companyId: Joi.string().guid(),
    }),
    handler.updateRKISessionCreation,
  ]
);
/**
 * @swagger
 *   "/rki/keygroups":
 *     get:
 *       tags:
 *         - RKI
 *       summary: "Get RKI keygroups"
 *       description: "Get Key keygroups based company ID"
 *       produces:
 *         - "application/json"
 *       parameters:
 *         - in: query
 *           name: serviceRecipientId
 *           type: string
 *           format: uuid
 *           required: true
 *       responses:
 *         "200":
 *           description: "Successful response"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/keygroups`,
    version: '1.0.0',
  },
  [
    requiresRole(['RKI']),
    validateQuery({
      serviceRecipientId: Joi.string().guid(),
    }),
    handler.getRKIKeyGroups,
  ]
);
