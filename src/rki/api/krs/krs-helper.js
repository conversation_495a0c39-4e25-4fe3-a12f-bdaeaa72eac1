const util = require('util');

const { server } = require('../../../../app');
const logger = require('../../../../lib/logger').mainLogger();
const totp = require('../../../../lib/totp');
const usersHelper = require('../../../../helpers/users-helper');
const mailer = require('../../../../lib/mailer');
const mailerHelper = require('../../../../helpers/mailer-helper');
const notificationHelper = require('../../../../helpers/notification-helper');
const CONSTANTS = require('../../lib/constants');

const NotificationTypes = {
  Manual: 0,
  Automated: 1,
  Updated: 2,
};

/**
 * Get All user having RKI role
 * @param {uuid} companyId
 */
async function getUsersWithRkiRoleForCompany(companyIds) {
  try {
    const params = [companyIds, 'RKI'];
    const rkiuser = await server.db.read.rows(
      `
            SELECT *
            FROM ics_user iu
            INNER JOIN user_role ur on iu.id = ur.user_id
            INNER JOIN role r on r.role_id = ur.role_id
            WHERE iu.status = 1
            AND iu.company_id = ANY($1) 
            AND r.role_name = $2
        `,
      params
    );
    return rkiuser;
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.getUsersWithRkiRoleForCompany(companyId: ${companyIds}) error`
    );
    throw e;
  }
}
/**
 * User and site details
 * @param {uuid} siteId
 * @param {uuid} userId
 */
async function getSiteDetails(siteId, userId) {
  try {
    const siteParams = [siteId, userId];
    const site = await server.db.read.row(
      `
            SELECT s.*, kg.key_group_ref, ci.issuer_code
            FROM site s
            JOIN user_site_authorization a ON a.site_id = s.site_id
            LEFT JOIN key_group kg ON kg.key_group_id = s.key_group_id
            LEFT JOIN certificate_issuer ci ON ci.certificate_issuer_id = kg.certificate_issuer_id
            WHERE s.active
            AND a.site_id = $1
            AND a.user_id = $2
        `,
      siteParams
    );
    return site;
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.getSiteDetails(siteId: ${siteId},
                userId: ${userId}) error`
    );
    throw e;
  }
}

/**
 * Key Request Device Snapshot Context
 * @param {uuid} deviceId
 * @returns {object} snapshot_context data for key_request_device
 */
async function getSnapshotContext(deviceId) {
  const p2pePluginMetrics = CONSTANTS.SnapshotP2PeMetricsConstants;
  const snapshotContext = await server.db.read.row(
    `
        SELECT row_to_json(row) as data
        FROM (
                SELECT t.release_version, rdl.name as p2pe_plugin
                FROM target t
                        LEFT JOIN device_versions dv1 on t.target_id = dv1.device_id AND dv1.state = ANY ($1)
                        LEFT JOIN report_data_lookup rdl on concat(rdl.ds, '.', rdl.dn) = dv1.state
                WHERE target_id = $2
                LIMIT 1
        ) row;
    `,
    [p2pePluginMetrics, deviceId]
  );

  return snapshotContext.data;
}

/**
 * Get the device details
 * @param {uuid} deviceId
 */
async function getDeviceDetails(deviceIds) {
  try {
    const devices = await server.db.read.rows(
      `
            select *
            from target t
            where t.target_id=ANY($1::int[])
            and t.active='true'
        `,
      [deviceIds]
    );
    return devices;
  } catch (e) {
    logger.error(
      { error: e, deviceIds },
      `keyRequestHelper.isDeviceActive error`
    );
    throw e;
  }
}

/**
 * Update key_request_device table with RKI new request
 * @param {uuid} keyRequestDeviceId
 * @param {uuid} keyRequestSessionId
 * @param {Number} deviceId
 * @param {Number} jobId
 * @param {Number} status
 * @param {String} message
 * @param {String} original_key_group_ref
 */
async function insertKeyRequestDevice(
  keyRequestDeviceId,
  keyRequestSessionId,
  deviceId,
  krdStatus,
  originalkeygroupref
) {
  const snapshotContext = await getSnapshotContext(deviceId);
  try {
    await server.db.write.execute(
      `
        INSERT INTO key_request_device ( key_Request_device_id, key_request_session_id, device_id,status, original_key_group_ref,context_snapshot)
        VALUES ( $1, $2, $3, $4, $5, $6 );
    `,
      [
        keyRequestDeviceId,
        keyRequestSessionId,
        deviceId,
        krdStatus,
        originalkeygroupref,
        snapshotContext,
      ]
    );
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.insertKeyRequestDevice(keyRequestSessionId: ${keyRequestSessionId},deviceId: ${deviceId} error`
    );
    throw e;
  }
}

/**
 *
 * @param {uuid} keyRequestSessionId
 * @param {uuid} authorizerUserId
 */
async function insertKeyRequestSessionAuthorizer(
  keyRequestSessionId,
  authorizerUserId
) {
  try {
    await server.db.write.execute(
      `
        INSERT INTO key_request_session_authorizer ( key_request_session_id, authorizer_user_id)
        VALUES ( $1, $2) ON CONFLICT (key_request_session_id,authorizer_user_id) DO NOTHING;
    `,
      [keyRequestSessionId, authorizerUserId]
    );
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.insertKeyRequestSessionAuthorizer(keyRequestSessionId: ${keyRequestSessionId},authorizerUserId: ${authorizerUserId} error`
    );
    throw e;
  }
}
/**
 * Create the RKI Session
 * @param {object} KeyRequestSessionObj
 */
async function insertKeyRequestSession(krsData) {
  try {
    await server.db.write.execute(
      `
            INSERT INTO key_request_session (
                key_request_session_id,
                company_id,
                authorized_time,
                request_user_id,
                authorizer_user_id,status,
                name,
                second_authorized_time,
                second_authorizer_user_id,key_group_ref,
                krs_type,
                created,
                expires_time
            )
            VALUES ( $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW() + INTERVAL '7 days' );
        `,
      [
        krsData.id,
        krsData.companyId,
        krsData.authorizedTime,
        krsData.requestUser,
        krsData.authorizerUser,
        krsData.statusId,
        krsData.name,
        krsData.secondAuthorizedTime,
        krsData.secondAuthorizerUserId,
        krsData.keyGroupRef,
        krsData.krsType,
      ]
    );
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.insertKeyRequestSession(keyRequestSessionId: ${krsData} error`
    );
    throw e;
  }
}

/**
 *
 * @param {uuid} userId
 * @param {Number} mfaCode
 */
async function isMfaCodeValid(userId, mfaCode) {
  try {
    // eslint-disable-next-line
    const mfaSecret = await usersHelper.getUserMfaSecretById(userId);
    const MfaCodeValid = await totp.funcvalidateTotp(mfaSecret, mfaCode);
    return MfaCodeValid;
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.isMfaCodeValid(mfaCode: ${mfaCode}) error`
    );
    throw e;
  }
}

/**
 * Get Session related data
 * @param {uuid} sessionId
 */
async function getKeyRequestSession(sessionId) {
  try {
    const sessionData = await server.db.read.rows(
      `
            SELECT
                krd.key_request_session_id,
                krs."name", 
                json_build_object('id', krs.request_user_id,
                                  'fullName', (select full_name from ics_user iu where iu.id=krs.request_user_id)) AS request_user_id,
                CASE 
                    WHEN krs.authorizer_user_id IS NULL then NULL 
                    ELSE json_build_object('id', krs.authorizer_user_id ,
                                           'fullName', (select full_name from ics_user iu where iu.id=krs.authorizer_user_id))
                END AS authorizer_user_id,
                CASE 
                    WHEN krs.second_authorizer_user_id IS NULL THEN NULL 
                    ELSE json_build_object('id', krs.second_authorizer_user_id,
                                           'fullName', (select full_name from ics_user iu where iu.id=krs.second_authorizer_user_id))
                END AS second_authorizer_user_id,
                krs.created,
                krs.expires_time,
                krs.status AS krs_status,
                json_build_object('total', krsv.total,
                                  'inProgress', krsv.in_progress,
                                  'completed', krsv.complete,
                                  'failed', krsv.failed) AS progress,
                krsv.key_group_ref,
                krs.company_id,
                krd.key_request_device_id,
                krd.device_id,
                krd.status AS device_status_id,
                json_build_object('id', t.device_type,
                                  'name', cpt.display_name) AS deviceType,
                t.serial_number,
                t."name" AS device_name,
                s.site_id,
                s."name" AS site_name,
                s.latitude,
                s.longitude, 
                c.id,
                c."name" AS company_name,
                krd.message,
                krd.original_key_group_ref 
            FROM key_request_device krd
            JOIN key_request_session krs ON krd.key_request_session_id = krs.key_request_session_id               
            LEFT JOIN key_request_session_summary_view krsv ON krsv.key_request_session_id = krs.key_request_session_id
            LEFT JOIN key_request_session_status krss ON krs.status = krss.id
            LEFT JOIN target t ON krd.device_id = t.target_id
            LEFT JOIN site s ON s.site_id = t.site_id 
            LEFT JOIN company c ON c.id =krs.company_id
            LEFT JOIN company_product_type cpt ON t.device_type = cpt.device_type and cpt.company = c.id
            WHERE krd.key_request_session_id = $1`,
      [sessionId]
    );

    return sessionData;
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.getKeyRequestSession(sessionId: ${sessionId} error`
    );
    throw e;
  }
}

/**
 * Get All Approver
 * @param {uuid} rkiSessionId
 */
async function getKRSApprover(rkiSessionId) {
  const params = [rkiSessionId];
  try {
    return await server.db.read.rows(
      `
        select 
        iu.id, iu.full_name, iu.email, iu.status from ics_user iu 
        join key_request_session_authorizer a 
        on iu.id=a.authorizer_user_id 
        where a.key_request_session_id =$1 and iu.status = 1
    `,
      params
    );
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.getKRSApprover(rkiSessionId: ${rkiSessionId} error`
    );
    throw e;
  }
}

async function getKRSCreator(rkiSessionId) {
  const params = [rkiSessionId];
  try {
    return await server.db.read.row(
      `
        SELECT iu.id, iu.full_name, iu.email, iu.status
        FROM ics_user iu 
        JOIN key_request_session krs 
        ON iu.id=krs.request_user_id 
        WHERE krs.key_request_session_id =$1 and iu.status = 1
    `,
      params
    );
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.getKRSApprover(rkiSessionId: ${rkiSessionId} error`
    );
    throw e;
  }
}

async function createRKINotification(
  notificationType,
  rkiSessionData,
  targets,
  creator
) {
  const promises = [];
  const { isAutomatedRKI } = rkiSessionData;
  let messageForCreator;
  let messageForTargets;
  let typeForCreator;
  let typeForTargets;
  let subjectForCreator;
  let subjectForTargets;
  let templateForCreator;
  let templateForTargets;
  let notificationLevel;

  try {
    const { key: type } = notificationType;
    const sender = await mailerHelper.getSenderEmailsByCompanyId(
      rkiSessionData.companyId
    );
    const { senderEmail } = sender;

    notificationLevel = notificationType.level;

    switch (type) {
      case CONSTANTS.NotificationTypes.RKI_REQUEST_CREATED.key:
        messageForCreator = util.format(
          CONSTANTS.NotificationTypes.RKI_REQUEST_CREATED_CREATOR.message,
          rkiSessionData.name
        );
        messageForTargets = util.format(
          CONSTANTS.NotificationTypes.RKI_REQUEST_CREATED_APPROVER.message,
          creator.fullName,
          rkiSessionData.name
        );
        typeForCreator =
          CONSTANTS.NotificationTypes.RKI_REQUEST_CREATED_CREATOR.key;
        typeForTargets =
          CONSTANTS.NotificationTypes.RKI_REQUEST_CREATED_APPROVER.key;
        subjectForCreator = util.format(
          CONSTANTS.NotificationTypes.RKI_REQUEST_CREATED_CREATOR.subject,
          rkiSessionData.name
        );
        subjectForTargets = util.format(
          CONSTANTS.NotificationTypes.RKI_REQUEST_CREATED_APPROVER.subject,
          rkiSessionData.name
        );
        templateForCreator = util.format(
          CONSTANTS.NotificationTypes.RKI_REQUEST_CREATED_CREATOR.template,
          rkiSessionData.name,
          rkiSessionData.deviceRequests.length
        );
        templateForTargets = util.format(
          CONSTANTS.NotificationTypes.RKI_REQUEST_CREATED_APPROVER.template,
          creator.fullName,
          rkiSessionData.name,
          rkiSessionData.deviceRequests.length
        );
        break;
      case CONSTANTS.NotificationTypes.RKI_REQUEST_UPDATED.key:
        typeForTargets = notificationType.key;
        messageForTargets = util.format(
          notificationType.message,
          rkiSessionData.name
        );
        subjectForTargets = util.format(
          notificationType.subject,
          rkiSessionData.name
        );
        templateForTargets = util.format(
          notificationType.template,
          creator.fullName,
          rkiSessionData.name,
          rkiSessionData.deviceRequests.length
        );
        break;
      case CONSTANTS.NotificationTypes.RKI_REQUEST_APPROVED.key:
      case CONSTANTS.NotificationTypes.RKI_REQUEST_REJECTED.key:
        messageForTargets = util.format(
          notificationType.message,
          rkiSessionData.name
        );
        messageForCreator = messageForTargets;
        typeForTargets = notificationType.key;
        typeForCreator = typeForTargets;
        subjectForTargets = util.format(
          notificationType.subject,
          rkiSessionData.name
        );
        subjectForCreator = subjectForTargets;
        templateForTargets = util.format(
          notificationType.template,
          rkiSessionData.name,
          rkiSessionData.deviceRequests.length
        );
        templateForCreator = templateForTargets;
        break;
      case CONSTANTS.NotificationTypes.RKI_DEVICE_FAILED.key:
      case CONSTANTS.NotificationTypes.RKI_DEVICE_SUCCESS.key:
        typeForTargets = notificationType.key;
        typeForCreator = typeForTargets;
        messageForTargets = util.format(
          notificationType.message,
          rkiSessionData.name,
          rkiSessionData.serialNumber
        );
        messageForCreator = messageForTargets;
        subjectForTargets = util.format(
          notificationType.subject,
          rkiSessionData.serialNumber,
          rkiSessionData.name
        );
        subjectForCreator = subjectForTargets;
        templateForTargets = util.format(
          notificationType.template,
          rkiSessionData.name,
          rkiSessionData.serialNumber
        );
        templateForCreator = templateForTargets;
        break;
      case CONSTANTS.NotificationTypes.RKI_REQUEST_FAILED.key:
        typeForTargets = notificationType.key;
        typeForCreator = templateForTargets;
        messageForTargets = util.format(
          notificationType.message,
          rkiSessionData.name,
          rkiSessionData.serialNumber,
          rkiSessionData.reason
        );
        messageForCreator = messageForTargets;
        subjectForTargets = util.format(
          notificationType.subject,
          rkiSessionData.name
        );
        subjectForCreator = subjectForTargets;
        templateForTargets = util.format(
          notificationType.template,
          rkiSessionData.name,
          rkiSessionData.numOfFailedDevices
        );
        templateForCreator = templateForTargets;
        break;
      default:
        logger.error(
          `NotificationType not valid: ${type}, no notification will be created`
        );
        break;
    }

    logger.info(
      {
        typeForCreator,
        messageForCreator,
        subjectForCreator,
        templateForCreator,
      },
      `Notification data for ${type} -- Creator`
    );
    logger.info(
      {
        typeForTargets,
        messageForTargets,
        subjectForTargets,
        templateForTargets,
      },
      `Notification data for ${type} -- Targets`
    );

    if (
      !isAutomatedRKI &&
      creator &&
      typeForCreator &&
      messageForCreator &&
      subjectForCreator &&
      templateForCreator
    ) {
      promises.push(
        notificationHelper.createUINotification(
          creator.id,
          messageForCreator,
          { type: 'rkiRequest', id: rkiSessionData.id },
          typeForCreator,
          notificationLevel
        )
      );
      promises.push(
        mailer.sendNotification(
          { to: creator.email, from: senderEmail },
          { message: templateForCreator, path: `/rki/${rkiSessionData.id}` },
          subjectForCreator
        )
      );
    }
    if (
      targets &&
      typeForTargets &&
      messageForTargets &&
      subjectForTargets &&
      templateForTargets
    ) {
      targets.forEach(target => {
        promises.push(
          notificationHelper.createUINotification(
            target.id,
            messageForTargets,
            { type: 'rkiRequest', id: rkiSessionData.id },
            typeForTargets,
            notificationLevel
          )
        );
        promises.push(
          mailer.sendNotification(
            { to: target.email, from: senderEmail },
            { message: templateForTargets, path: `/rki/${rkiSessionData.id}` },
            subjectForTargets
          )
        );
      });
    }
    return await Promise.all(promises);
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.createRKINotification(rkisessiondata: ${rkiSessionData} error`
    );
    throw e;
  }
}

const formatKrsQuery = (companies, params, companyId, status) => {
  let whereClause = 'WHERE krs.status != 3 AND (krs.request_user_id=$1 OR';
  if (companies.length > 0) {
    whereClause = `${whereClause} krs.company_id = ANY ($2))`;
    companies.push(companyId);
    params.push(companies);
  } else {
    whereClause = `${whereClause} krs.company_id=$2)`;
    params.push(companyId);
  }

  if (status) {
    whereClause = `${whereClause} AND krs.status = ANY ($3)`;
    params.push(status.split(','));
  }
  return { whereClause };
};

/**
 *  Get the total count of KRS
 * @param {Object} userDetails
 * @param {Array} statusList
 */
async function getAllKeyRequestSessionSummariesCount(
  userDetails,
  companies,
  status
) {
  try {
    const userId = userDetails.sub;
    const companyId = userDetails.company.id;
    const params = [userId];
    const { whereClause } = formatKrsQuery(
      companies,
      params,
      companyId,
      status
    );

    const query = ` SELECT COUNT (DISTINCT krs) FROM key_request_session krs 
        ${whereClause}`;
    logger.info(
      { query, params },
      'getAllKeyRequestSessionSummariesCount query'
    );
    const krsSummarycount = await server.db.read.rows(query, params);
    return krsSummarycount;
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.getAllKeyRequestSessionSummariesCount(userDetails: ${userDetails} error`
    );
    throw e;
  }
}

/**
 *
 * @param {Object} userDetails
 * @param {Array} statusList
 * @param {Number} pageIndex
 * @param {Number} pageSize
 */
async function getAllKeyRequestSessionSummaries(
  userDetails,
  companies,
  status,
  pageIndex,
  pageSize
) {
  try {
    const offset = pageIndex === 0 ? pageSize : (pageIndex + 1) * pageSize;
    const startSet = pageIndex * pageSize;
    const userId = userDetails.sub;
    const companyId = userDetails.company.id;
    const params = [userId];
    const { whereClause } = formatKrsQuery(
      companies,
      params,
      companyId,
      status
    );

    params.push(startSet, offset);

    const whereClause2 = `${whereClause} ORDER BY krs.created DESC OFFSET $${
      params.length - 1
    } LIMIT $${params.length}`;

    const query = `
        SELECT DISTINCT *,
              (SELECT full_name FROM ics_user iu WHERE iu.id=krs.request_user_id ),
              (SELECT name FROM company c WHERE c.id=krs.company_id) as company_name
        from key_request_session_summary_view krs         
        ${whereClause2}`;

    logger.info({ query, params }, 'getAllKeyRequestSessionSummaries query');
    const krsSummarydata = await server.db.read.rows(query, params);

    return krsSummarydata;
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.getAllKeyRequestSessionSummaries(userDetails: ${userDetails} error`
    );
    throw e;
  }
}

/**
 *  Get the individual Key Session Request.
 * @param {uuid} keyRequestId
 */
async function getKeyRequestSessionDetails(keyRequestId) {
  try {
    const queryParams = [keyRequestId];
    const requestsession = await server.db.read.row(
      `
        select distinct on (krs.key_request_session_id ) krs.key_request_session_id, krs.status,krs.request_user_id as creator
        from key_request_session krs where krs.key_request_session_id=$1
    `,
      queryParams
    );
    return requestsession;
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.getKeyRequestSessionDetails(keyRequestId: ${keyRequestId}, error`
    );
    throw e;
  }
}

/**
 * Update the company ,name and keygroup
 * @param {Object} KeyRequestSessionObj
 */
async function updateKeyRequestSession(keyRequestSessionData) {
  try {
    const sessionId = keyRequestSessionData.krsId;
    const companyId = keyRequestSessionData.krscompanyId;
    const keygroupRef = keyRequestSessionData.keyGroupRef;
    const name = keyRequestSessionData.krsname;
    return server.db.write.execute(
      'UPDATE key_request_session set company_id = $2, name = $3, key_group_ref=$4 where key_request_session_id = $1',
      [sessionId, companyId, name, keygroupRef]
    );
  } catch (e) {
    logger.error(
      { error: e, keyRequestSessionData },
      `keyRequestHelper.updateKeyRequestSession error`
    );
    throw e;
  }
}
/**
 *  Delete the device request and update.
 * @param {UUID} keyRequestSessionId
 */
async function deleteRequestDeviceEntry(keyRequestSessionId) {
  try {
    // delete the devices entries and insert a new entry with same session ID
    await server.db.write.execute(
      'DELETE FROM key_request_device WHERE key_request_session_id = $1',
      [keyRequestSessionId]
    );
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.deleteRequestDeviceEntry(keyRequestSessionId: ${keyRequestSessionId}, error`
    );
    throw e;
  }
}
/**
 * Get the approver list for the KEy request session
 * @param {uuid} keyRequestSessionId
 */
async function getApproverKRSSession(keyRequestSessionId) {
  try {
    const queryParams = [keyRequestSessionId];
    const approvers = await server.db.read.rows(
      'select authorizer_user_id from key_request_session_authorizer where key_request_session_id=$1',
      queryParams
    );
    return approvers;
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.getApproverKRSSession(keyRequestSessionId: ${keyRequestSessionId}, error`
    );
    throw e;
  }
}
/**
 *  Get All Allowed Key group
 * @param {uuid} companyId
 */
async function getAllAllowedKeyGroupByCompany(companyId) {
  try {
    const queryParams = [companyId];
    const keyGroup = await server.db.read.rows(
      `
        SELECT distinct 
        kg.key_group_id,
        kg.key_group_name,
        kg.key_group_ref,
        kg.company_id,
        c."name",
        kg.certificate_issuer_id,
        ci.issuer_code
        FROM key_group kg  
        JOIN company c on kg.company_id = c.id 
        JOIN certificate_issuer ci on kg.certificate_issuer_id = ci.certificate_issuer_id
        WHERE kg.company_id =$1 OR kg.company_id  IN (SELECT cr.company_id
        FROM company_relationship cr WHERE cr.allowed_company_id=$1)
    `,
      queryParams
    );
    return keyGroup;
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.getAllAllowedKeyGroupByCompany(companyId: ${companyId}, error`
    );
    throw e;
  }
}

/**
 *  Get All Key group
 * @param {uuid} companyId
 */
async function getAllKeyGroupByCompany(companyId) {
  try {
    const queryParams = [companyId];
    const keyGroup = await server.db.read.rows(
      `
        SELECT distinct 
        kg.key_group_id,
        kg.key_group_name,
        kg.key_group_ref,
        kg.company_id,
        kg.certificate_issuer_id,
        ci.issuer_code,
        c."name"
        FROM key_group kg  
        JOIN company c on kg.company_id = c.id
        JOIN certificate_issuer ci on kg.certificate_issuer_id = ci.certificate_issuer_id
        WHERE kg.company_id =$1        
    `,
      queryParams
    );
    return keyGroup;
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.getAllKeyGroupByCompany(companyId: ${companyId}, error`
    );
    throw e;
  }
}

/**
 *  Get the allowed company id
 * @param {uuid} companyId
 */
async function getAllowedCompanyId({ serviceCompanyId, serviceRecipientId }) {
  try {
    const queryParams = [serviceCompanyId, serviceRecipientId];
    const keyGroup = await server.db.read.row(
      `
        SELECT cr.allowed_company_id FROM company_relationship cr WHERE cr.allowed_company_id =$1 AND cr.company_id =$2    
    `,
      queryParams
    );
    return keyGroup;
  } catch (e) {
    logger.error(
      e,
      `keyRequestHelper.getAllowedCompanyId(serviceCompanyId: ${serviceCompanyId}, servoceRecipientId: ${serviceRecipientId}, error`
    );
    throw e;
  }
}

/**
 * Get the device details
 * @param {uuid} userId
 * @param {uuid} companyId
 * @param {number} deviceId
 */
async function isAllowedKRS(userId, companyId, deviceIds) {
  try {
    const queryParams = [userId, companyId, deviceIds];
    const devices = await server.db.read.rows(
      `
        SELECT target_id FROM target t 
        JOIN site s on t.site_id =s.site_id 
        JOIN user_site_authorization u on s.site_id = u.site_id
        JOIN company c on s.company_id =c.id 
        WHERE t.active = true AND u.user_id = $1
        AND c.id = $2
        AND t.target_id = ANY($3::int[])
        `,
      queryParams
    );
    return devices;
  } catch (error) {
    logger.error(
      { error, userId, companyId, deviceIds },
      `keyRequestHelper.isAllowedKRS error`
    );
    throw error;
  }
}
module.exports = {
  getUsersWithRkiRoleForCompany,
  getSiteDetails,
  getDeviceDetails,
  insertKeyRequestDevice,
  insertKeyRequestSessionAuthorizer,
  insertKeyRequestSession,
  isMfaCodeValid,
  getKeyRequestSession,
  getKRSApprover,
  getKRSCreator,
  getAllKeyRequestSessionSummariesCount,
  getAllKeyRequestSessionSummaries,
  getKeyRequestSessionDetails,
  updateKeyRequestSession,
  deleteRequestDeviceEntry,
  getApproverKRSSession,
  getAllowedCompanyId,
  getAllKeyGroupByCompany,
  getAllAllowedKeyGroupByCompany,
  NotificationTypes,
  createRKINotification,
  isAllowedKRS,
  getSnapshotContext,
};
