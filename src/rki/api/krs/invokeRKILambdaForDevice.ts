import AWS from '../../../../lib/aws';
import { config } from '../../../../env';
import { KeyRequestDevice, KeyRequestSession } from '../approval/approval';

const { rkiServiceLambda } = config.AWS.Lambda;

export const invokeLambdaForDevice = async (
  krd: KeyRequestDevice,
  keyRequestSession: KeyRequestSession,
  validDeviceCertificate: string | undefined
) =>
  AWS.invokeLambda({
    FunctionName: rkiServiceLambda,
    InvocationType: 'Event',
    Payload: JSON.stringify({
      keyRequestDeviceId: krd.keyRequestDeviceId,
      certificate: validDeviceCertificate,
      oldKeyGroup: krd.keyGroupRef,
      group: keyRequestSession.keyGroupRef,
      issuerCode: keyRequestSession.issuerCode,
    }),
  });
