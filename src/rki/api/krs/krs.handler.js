const uuid = require('uuid/v4');
const restify = require('restify');
const restifyErrors = require('restify-errors');
const moment = require('moment');
const {
  KEY_REQUEST_SESSION_STATUS,
} = require('../../../../helpers/key-request-helper');
const { server } = require('../../../../app');
const CONSTANTS = require('../../lib/constants');
const {
  deviceOnboardingAdapter,
} = require('../../../../helpers/deviceOnboarding-helper');
const companyHelper = require('../../../../helpers/company-helper');
const KRSHelper = require('./krs-helper');

/**
 *
 * @param {String} reason
 * @param {String} sitename
 * @param {Number} noofdevice
 */
const reasonDetails = (reason, sitename, noofdevice) => {
  const LABEL_AUTOMATED = '[Automated]';
  let formattedreason = null;
  // Format the reason
  switch (reason) {
    case 'CREATE':
      formattedreason = `${LABEL_AUTOMATED} Create device in ${sitename}`;
      break;
    case 'MOVE':
      formattedreason = `${LABEL_AUTOMATED} Move device to ${sitename}`;
      break;
    case 'BULKMOVE':
      formattedreason = `${LABEL_AUTOMATED} Move ${noofdevice} devices to ${sitename}`;
      break;
    case 'KEYGROUP_CHANGE':
      formattedreason = `${LABEL_AUTOMATED} Update RKI Keys for ${noofdevice} devices at ${sitename}`;
      break;
    default:
      formattedreason = LABEL_AUTOMATED;
  }

  return formattedreason;
};

/**
 * Mapped the device data as per java
 * @param {output} rkisessiondata
 */
const mappingDevice = rkisessiondata => {
  const devices = [];

  rkisessiondata.forEach(rkisession => {
    devices.push({
      id: rkisession.keyRequestDeviceId,
      device: {
        id: rkisession.deviceId,
        deviceType: rkisession.devicetype,
        serialNumber: rkisession.serialNumber,
        name: rkisession.deviceName,
        keyGroupRef: rkisession.keyGroupRef,
        site: {
          id: rkisession.siteId,
          name: rkisession.siteName,
          latitude: rkisession.latitude,
          longitude: rkisession.longitude,
          owner: {
            id: rkisession.id,
            name: rkisession.companyName,
          },
        },
      },
      status: rkisession.deviceStatusId,
      message: rkisession.message,
      originalKeyGroupRef: rkisession.originalKeyGroupRef,
    });
  });

  return devices;
};

/**
 *  final session output the user
 * @param {class} rkisessionobj
 * @param {array} approverdata
 */
const mappingSessionResponse = async (rkisessionobj, approverdata) => {
  const rkisessiondata = rkisessionobj[0];

  const sessiondata = {
    id: rkisessiondata.keyRequestSessionId,
    name: rkisessiondata.name,
    creator: rkisessiondata.requestUserId,
    authorizer: rkisessiondata.authorizerUserId,
    secondAuthorizer: rkisessiondata.secondAuthorizerUserId,
    created:
      rkisessiondata.created != null
        ? moment(rkisessiondata.created).format('x')
        : null,
    expires:
      rkisessiondata.expiresTime != null
        ? moment(rkisessiondata.expiresTime).format('x')
        : null,
    status: rkisessiondata.krsStatus,
    progress: rkisessiondata.progress,
    keyGroupRef: rkisessiondata.keyGroupRef,
    companyId: rkisessiondata.companyId,
    approvers: approverdata,
    deviceRequests: mappingDevice(rkisessionobj),
  };

  return sessiondata;
};

const mappingKRSSummary = rkisummaries => {
  const mappedrkisummary = [];

  rkisummaries.forEach(rkisummary => {
    mappedrkisummary.push({
      id: rkisummary.keyRequestSessionId,
      name: rkisummary.name,
      creator: {
        id: rkisummary.requestUserId,
        fullName: rkisummary.fullName,
      },
      authorizer: rkisummary.authorizerUserId,
      secondAuthorizer: rkisummary.secondAuthorizerUserId,
      created:
        rkisummary.created != null
          ? moment(rkisummary.created).format('x')
          : null,
      expires:
        rkisummary.expiresTime != null
          ? moment(rkisummary.expiresTime).format('x')
          : null,
      status: rkisummary.status,
      progress: {
        total: rkisummary.total,
        inProgress: rkisummary.inProgress,
        complete: rkisummary.complete,
        failed: rkisummary.failed,
      },
      keyGroupRef: rkisummary.keyGroupRef,
      companyId: rkisummary.companyId,
      companyName: rkisummary.companyName,
    });
  });

  return mappedrkisummary;
};

/**
 * Get the authorixer list
 * @param {uuid} rkisessionId
 */
const participantsDetails = async rkisessionId => {
  const approverlist = [];
  const creator = await KRSHelper.getKRSCreator(rkisessionId);

  (await KRSHelper.getKRSApprover(rkisessionId)).forEach(approver => {
    const { id, fullName, email, status } = approver;
    approverlist.push({ id, fullName, email, status });
  });

  return { approverlist, creator };
};

/**
 * Retrieves active device details for the given device IDs.
 *
 * @param {Array<string>} deviceIds - An array of device IDs to retrieve details for.
 * @returns {Promise<Array<Object|null>>} A promise that resolves to an array of device details objects or null if the device is not active or an error occurred.
 *
 * @throws {Error} If there is an error fetching device details.
 */
const getActiveDeviceDetails = async deviceIds => {
  const deviceDetails = await KRSHelper.getDeviceDetails(deviceIds);

  const deviceDetailsPromises = deviceDetails.map(async device => {
    try {
      const isDeviceTypeRkiEnabled =
        await deviceOnboardingAdapter.isFeatureEnabled(
          device.deviceType,
          'rki_feature'
        );

      if (isDeviceTypeRkiEnabled && device && device.active) {
        return device;
      }
      server.log.info(
        { isDeviceTypeRkiEnabled, device },
        'Device Deem Not Allowed For RKI'
      );
      return null;
    } catch (error) {
      server.log.error({ error, device }, 'Error fetching device details');
      return null; // Or handle the error as needed
    }
  });
  // Wait for all promises to resolve
  const deviceDetailsArray = await Promise.all(deviceDetailsPromises);

  // Filter out null values to get only active devices
  const activeDevices = deviceDetailsArray.filter(device => device !== null);

  return activeDevices;
};

/**
 *  Insert the session object into the KRS session table
 * @param {true/false} isAutomatedRKI - Used to send notification
 */
const rkiRequestSessionResponse = async (
  keyRequestSessionData,
  isAutomatedRKI
) => {
  const deviceRequest = keyRequestSessionData.deviceRequests;
  // Insert into the key_request_session
  await KRSHelper.insertKeyRequestSession(keyRequestSessionData);

  if (deviceRequest.length) {
    await Promise.all(
      deviceRequest.map(async device => {
        await KRSHelper.insertKeyRequestDevice(
          uuid(),
          keyRequestSessionData.id,
          device.targetId,
          null,
          device.keyGroupRef
        );
      })
    );
  }
  // Add authorizerUser key_request_session_authorizer
  const authorizerUser = keyRequestSessionData.approvers;

  await Promise.all(
    authorizerUser.map(async user => {
      await KRSHelper.insertKeyRequestSessionAuthorizer(
        keyRequestSessionData.id,
        user.id ? user.id : user
      );
    })
  );

  // Get All data inserted to db and send mail triggered
  // await Promise.all( promiselist );
  // return the object
  const sessiondata = await KRSHelper.getKeyRequestSession(
    keyRequestSessionData.id
  );
  const { approverlist: approverdata, creator } = await participantsDetails(
    keyRequestSessionData.id
  );

  // Notification --
  if (!sessiondata.length) {
    throw new restify.errors.NotFoundError(
      'Devices details are missing in the table '
    );
  }

  const rkisessiondata = await mappingSessionResponse(
    sessiondata,
    approverdata
  );

  await KRSHelper.createRKINotification(
    CONSTANTS.NotificationTypes.RKI_REQUEST_CREATED,
    { ...rkisessiondata, isAutomatedRKI },
    approverdata,
    creator
  );
  return rkisessiondata;
};

/**
 * This function validates if all the activeDevices are allowed for RKI.
 * @param {*} userId RKI requestor
 * @param {*} krsCompanyId RKI company
 * @param {*} activeDevices List of active devices to be validated
 */
async function validateRKIAllowedDevices(userId, krsCompanyId, activeDevices) {
  const allowedTarget = await KRSHelper.isAllowedKRS(
    userId,
    krsCompanyId,
    activeDevices.map(device => device.targetId)
  );
  const allowedTargetIds = new Set(
    allowedTarget.map(device => device.targetId)
  );
  if (activeDevices.some(device => !allowedTargetIds.has(device.targetId))) {
    throw new restify.errors.NotFoundError('Some Device(s) are not valid');
  }
}

/**
 * To create a new RKI session
 * @param {array} deviceIds
 * @param {String} keyGroupRef
 * @param {String} reason
 * @param {uuid} userId
 * @param {uuid} siteId
 * @param {Object} manualPayload  - For Manual RKI
 */
const createRKIRequestSession = async (
  requestCompanyId,
  deviceIds,
  keyGroupRef,
  issuerCode,
  reason,
  userId,
  siteId,
  manualPayload
) => {
  try {
    let krsName = null;
    let krsApprovers = null;
    let krsCompanyId = null;
    let krsType = null;

    const isAutomatedRKI = !manualPayload;
    const activeDevices = await getActiveDeviceDetails(deviceIds);

    if (!activeDevices.length) {
      server.log.error({ deviceIds }, 'No active devices found');
      throw new restify.errors.NotFoundError(
        'Device(s) are not active or RKI not allowed to the device(s)'
      ); // 404
    }

    if (!isAutomatedRKI) {
      const { mfacode } = manualPayload;
      const isMFAValid = await KRSHelper.isMfaCodeValid(userId, mfacode);
      krsApprovers = manualPayload.approvers;
      krsCompanyId = manualPayload.companyId;
      krsName = reason;
      krsType = 'Manual';

      if (!isMFAValid) {
        throw new restify.errors.NotAcceptableError(
          `MFA code ${mfacode} validation failed`
        );
      }

      if (!krsApprovers.length) {
        throw new restify.errors.NotFoundError('No approvers found');
      }

      if (krsApprovers.includes(userId)) {
        throw new restify.errors.ForbiddenError(
          'User cannot be an approver for their own RKI request'
        );
      }

      const potentialApproverIds = (
        await KRSHelper.getUsersWithRkiRoleForCompany([
          requestCompanyId,
          krsCompanyId,
        ])
      ).map(ap => ap.id);
      // Creator can be a user in service company or service recipient company
      if (!potentialApproverIds.includes(userId)) {
        throw new restify.errors.ForbiddenError(
          'User is not authorize to create RKI request'
        );
      }
      // Approver can be a user in service company or service recipient company
      if (krsApprovers.some(val => !potentialApproverIds.includes(val.id))) {
        throw new restify.errors.NotFoundError('No approvers found');
      }

      activeDevices.forEach(active => {
        if (active.presence == null || active.presence !== 'PRESENT') {
          throw new restify.errors.NotFoundError(
            'Device(s) are not active or RKI not allowed to the device(s)'
          ); // 404
        }
      });

      // Allowed Device check
      await validateRKIAllowedDevices(userId, krsCompanyId, activeDevices);

      // Allowed Key Group Reference Check
      const allowedKeyGroupRefs = (
        await KRSHelper.getAllAllowedKeyGroupByCompany(krsCompanyId)
      ).map(kg => kg.keyGroupRef);
      if (!allowedKeyGroupRefs.includes(keyGroupRef)) {
        throw new restify.errors.NotFoundError(
          `Key group with ref ${keyGroupRef} not found`
        ); // 404
      }
    }

    if (isAutomatedRKI) {
      activeDevices.filter(
        active => active.presence != null || active.presence === 'PRESENT'
      );

      if (!activeDevices.length) {
        throw new restify.errors.NotFoundError('Device(s) are not active'); // 404
      }
      // Get the User permission on site and site detials for approver
      const siteDetails = await KRSHelper.getSiteDetails(siteId, userId);

      if (siteDetails == null || siteDetails === '') {
        throw new restify.errors.NotFoundError(
          `Site (${siteId}) not found or user has no permission`
        ); // 404
      }

      // List of approver
      const potentialApprovers = await KRSHelper.getUsersWithRkiRoleForCompany([
        siteDetails.companyId,
      ]);

      if (!potentialApprovers || !potentialApprovers.length) {
        throw new restify.errors.NotFoundError('No approvers found'); // 404
      }

      const formattedreason = reasonDetails(
        reason,
        siteDetails.name,
        activeDevices.length
      );

      krsName = formattedreason;
      krsApprovers = potentialApprovers;
      krsCompanyId = siteDetails.companyId;
      krsType = 'Automated';
      // Allowed Device check
      await validateRKIAllowedDevices(userId, krsCompanyId, activeDevices);
    }

    const keyRequestSessionData = {
      id: uuid(),
      name: krsName,
      companyId: krsCompanyId,
      authorizedTime: null,
      secondAuthorizedTime: null,
      requestUser: userId,
      authorizerUser: null,
      secondAuthorizerUserId: null,
      statusId: KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
      keyGroupRef,
      deviceRequests: activeDevices,
      approvers: krsApprovers,
      krsType,
    };

    server.log.info(
      `krs.handler.createRKIRequestSession rkiRequestSessionResponse with parameter ${JSON.stringify(
        keyRequestSessionData
      )}.`
    );

    return await rkiRequestSessionResponse(
      keyRequestSessionData,
      isAutomatedRKI
    );
  } catch (error) {
    return {
      error: {
        message: error.message,
        code: error.statusCode,
      },
    };
  }
};

/**
 * Update Key Request Session
 */
const updateKRSSession = async keyRequestSessionData => {
  const deviceRequest = keyRequestSessionData.activedevices;
  // Insert into the key_request_session
  await KRSHelper.updateKeyRequestSession(keyRequestSessionData);
  // Add THE DEVICE SESSION key_request_device
  const promiselist = [];

  if (deviceRequest.length) {
    // remove old device and update with new device list
    await KRSHelper.deleteRequestDeviceEntry(keyRequestSessionData.krsId);

    deviceRequest.forEach(device => {
      promiselist.push(
        KRSHelper.insertKeyRequestDevice(
          uuid(),
          keyRequestSessionData.krsId,
          device.targetId,
          null,
          device.keyGroupRef
        )
      );
    });
  }

  // Add authorizerUser key_request_session_authorizer
  const { approverlist: existingApprover } = await participantsDetails(
    keyRequestSessionData.krsId
  );
  const existingApproverIds = existingApprover.map(ap => ap.id);
  const authorizerUser = keyRequestSessionData.krsapprovers;

  authorizerUser.forEach(user => {
    promiselist.push(
      KRSHelper.insertKeyRequestSessionAuthorizer(
        keyRequestSessionData.krsId,
        user.id ? user.id : user
      )
    );
  });

  // Get All data inserted to db and send mail triggered
  await Promise.all(promiselist);
  // return the object
  const sessiondata = await KRSHelper.getKeyRequestSession(
    keyRequestSessionData.krsId
  );

  if (!sessiondata.length) {
    throw new restify.errors.NotFoundError(
      'Device(s) details are missing in the table '
    );
  }

  const { approverlist: approverdata, creator } = await participantsDetails(
    keyRequestSessionData.krsId
  );
  // Notification --
  const rkisessiondata = await mappingSessionResponse(
    sessiondata,
    approverdata
  );
  // Send notifications out only to new authorizers
  const apporverToNotify = approverdata.filter(
    ap => !existingApproverIds.includes(ap.id)
  );
  if (apporverToNotify.length) {
    await KRSHelper.createRKINotification(
      CONSTANTS.NotificationTypes.RKI_REQUEST_UPDATED,
      rkisessiondata,
      apporverToNotify,
      creator
    );
  }

  return rkisessiondata;
};

/**
 * Updates the Key Request Session (KRS) data.
 *
 * @param {Object} manualpayload - The payload containing the KRS data to be updated.
 * @param {string} manualpayload.keyRequestSessionId - The ID of the key request session.
 * @param {string} manualpayload.mfacode - The MFA code for validation.
 * @param {string} manualpayload.userId - The ID of the user making the request.
 * @param {string} manualpayload.reason - The reason for the KRS update.
 * @param {Array} manualpayload.approvers - The list of approvers for the KRS.
 * @param {string} manualpayload.companyId - The ID of the company associated with the KRS.
 * @param {Array} manualpayload.devicelist - The list of devices associated with the KRS.
 * @param {string} manualpayload.keyGroupRef - The reference for the key group.
 *
 * @returns {Promise<Object>} The result of the KRS update operation.
 * @throws {restify.errors.NotAcceptableError} If MFA code validation fails or if the KRS is already in flight.
 * @throws {restify.errors.NotFoundError} If devices are not active or RKI is not allowed to the devices, or if some devices are not valid.
 * @throws {restify.errors.ForbiddenError} If the user does not have permission to update the KRS.
 */
const updateKRSdata = async manualpayload => {
  try {
    const krsDetails = await KRSHelper.getKeyRequestSessionDetails(
      manualpayload.keyRequestSessionId
    );
    const { mfacode } = manualpayload;
    const { userId } = manualpayload;
    const isMFAValid = await KRSHelper.isMfaCodeValid(userId, mfacode);

    if (!isMFAValid) {
      throw new restify.errors.NotAcceptableError(
        `MFA code ${mfacode} validation failed`
      );
    }

    const krsName = manualpayload.reason;
    const krsApprovers = manualpayload.approvers;
    const krsCompanyId = manualpayload.companyId;
    const devices = manualpayload.devicelist;
    const { keyGroupRef } = manualpayload;
    const krsId = manualpayload.keyRequestSessionId;
    const requestUser = manualpayload.userId;

    const activeDevices = await getActiveDeviceDetails(devices);
    if (!activeDevices.length) {
      server.log.error({ devices }, 'No active devices found');
      throw new restify.errors.NotFoundError(
        'Device(s) are not active or RKI not allowed to the device(s)'
      ); // 404
    }

    activeDevices.forEach(active => {
      if (active.presence == null || active.presence !== 'PRESENT') {
        throw new restify.errors.NotFoundError(
          'Device(s) are not active or RKI not allowed to the device(s)'
        ); // 404
      }
    });
    // Allowed Device check
    await validateRKIAllowedDevices(userId, krsCompanyId, activeDevices);

    // Only creator can update
    if (krsDetails.creator !== manualpayload.userId) {
      throw new restify.errors.ForbiddenError('Permission denied to update');
    }
    // Get current status -if Not in pending  - Update denied
    if (krsDetails.status !== 0) {
      throw new restify.errors.NotAcceptableError(
        'Permission denied - RKI is already inFlight'
      );
    }

    const keyRequestSessionData = {
      krsId,
      krsname: krsName,
      krscompanyId: krsCompanyId,
      requestUser,
      keyGroupRef,
      activedevices: activeDevices,
      krsapprovers: krsApprovers,
    };

    return updateKRSSession(keyRequestSessionData);
  } catch (error) {
    return {
      error: {
        message: error.message,
        code: error.statusCode,
      },
    };
  }
};

module.exports = {
  async krsSessionCreation(req, res, next) {
    try {
      const devicelist = [];
      const { keyGroupRef } = req.body;
      const userId = req.user.sub;
      const requestCompanyId = req.user.company.id;

      req.body.deviceRequests.forEach(device =>
        devicelist.push(device.device.id)
      );

      const reason = req.body.name;
      const manualpayload = {
        companyId: req.body.companyId,
        mfacode: req.body.mfaCode,
        approvers: req.body.approvers,
      };

      const { issuerCode } = await server.db.read.row(
        `
        SELECT ci.issuer_code 
        FROM key_group kg
        JOIN certificate_issuer ci ON ci.certificate_issuer_id = kg.certificate_issuer_id
        WHERE kg.key_group_ref = $1 AND kg.company_id = $2;
        `,
        [keyGroupRef, manualpayload.companyId]
      );

      const results = await createRKIRequestSession(
        requestCompanyId,
        devicelist,
        keyGroupRef,
        issuerCode,
        reason,
        userId,
        null,
        manualpayload
      );

      if (results.error) {
        const err = restifyErrors.makeErrFromCode(
          results.error.code,
          results.error.message
        );
        return next(err);
      }

      res.send(200, results);

      return next();
    } catch (e) {
      return next(new restify.InternalServerError(e)); // 500
    }
  },

  async getAllRKISessionCreation(req, res, next) {
    try {
      const { status } = req.params;
      const pageIndex = req.params.pageIndex
        ? parseInt(req.params.pageIndex, 10)
        : 0;
      const pageSize = req.params.pageSize
        ? parseInt(req.params.pageSize, 10)
        : 50;
      const userDetails = req.user;

      const companies = await companyHelper.getServiceRecipients(
        userDetails.company.id
      );

      const krsCount = await KRSHelper.getAllKeyRequestSessionSummariesCount(
        userDetails,
        companies,
        status
      );
      const krsSummarydata = await KRSHelper.getAllKeyRequestSessionSummaries(
        userDetails,
        companies,
        status,
        pageIndex,
        pageSize
      );

      const results = mappingKRSSummary(krsSummarydata);

      res.send({
        resultsMetadata: {
          totalResults: krsCount[0].count,
          pageIndex: +pageIndex,
          pageSize: +pageSize,
        },
        results,
      });

      return next();
    } catch (e) {
      return next(new restify.InternalServerError(e)); // 500
    }
  },

  async getRKISessionDetail(req, res, next) {
    try {
      const krsId = req.params.krsid;
      const sessiondata = await KRSHelper.getKeyRequestSession(krsId);
      const { approverlist: approverdata } = await participantsDetails(krsId);
      let results = null;

      await Promise.all(sessiondata, approverdata);

      if (sessiondata.length) {
        results = await mappingSessionResponse(sessiondata, approverdata);
        // Expired  KRS
        if (results.status === 3) {
          return next(
            new restify.errors.NotFoundError('KRS is no longer valid')
          );
        }
      } else {
        results = `${krsId} is not valid key request session `;
      }

      res.send(200, results);

      return next();
    } catch (e) {
      return next(new restify.InternalServerError(e)); // 500
    }
  },

  async updateRKISessionCreation(req, res, next) {
    try {
      const keyRequestSessionId = req.params.krsid;
      const devicelist = [];

      req.body.deviceRequests.forEach(device =>
        devicelist.push(device.device.id)
      );

      const manualpayload = {
        companyId: req.body.companyId,
        mfacode: req.body.mfaCode,
        approvers: req.body.approvers,
        keyGroupRef: req.body.keyGroupRef,
        userId: req.user.sub,
        devicelist,
        reason: req.body.name,
        keyRequestSessionId,
      };

      const results = await updateKRSdata(manualpayload);

      if (results.error) {
        const err = restifyErrors.makeErrFromCode(
          results.error.code,
          results.error.message
        );
        return next(err);
      }

      res.send(200, results);

      return next();
    } catch (e) {
      return next(new restify.InternalServerError(e)); // 500
    }
  },

  async getRKIKeyGroups(req, res, next) {
    try {
      const { serviceRecipientId } = req.params;
      const companyKeyGroup = [];

      if (serviceRecipientId !== req.user.company.id) {
        const allowedCompany = await KRSHelper.getAllowedCompanyId({
          serviceRecipientId,
          serviceCompanyId: req.user.company.id,
        });
        if (!allowedCompany)
          return next(
            new restify.errors.ForbiddenError('Permission denied to access')
          );
      }

      const keygroups = await KRSHelper.getAllKeyGroupByCompany(
        serviceRecipientId || req.user.company.id
      );

      keygroups.forEach(keygroup => {
        companyKeyGroup.push({
          id: keygroup.keyGroupId,
          name: keygroup.keyGroupName,
          ref: keygroup.keyGroupRef,
          certificateIssuerId: keygroup.certificateIssuerId,
          issuerCode: keygroup.issuerCode,
          owner: {
            id: keygroup.companyId,
            name: keygroup.name,
          },
        });
      });

      res.send(200, companyKeyGroup);
      return next();
    } catch (e) {
      return next(new restify.InternalServerError(e)); // 500
    }
  },

  createRKIRequestSession,
};
