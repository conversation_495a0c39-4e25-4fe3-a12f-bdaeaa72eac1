const { server } = require('../../../../app');
const env = require('../../../../env');
const handler = require('./docs.handler');

const BASE_PATH = `${env.config.base}/rki/docs`;

/**
 * @swagger
 *  responses:
 *    InternalServerError:
 *      description: "Internal Server Error"
 *    AuthenticationFailed:
 *      description: "Authentication Failed."
 *    Forbidden:
 *      description: "Permission denied."
 *    BadRequest:
 *      description: "The input was malformed"
 *    SwitchingProtocols:
 *      description: "Changing from HTTP to WS protocols"
 *
 */
server.get(
  {
    path: `${BASE_PATH}/api.json`,
    version: '1.0.0',
  },
  [handler.getApiDocs]
);
