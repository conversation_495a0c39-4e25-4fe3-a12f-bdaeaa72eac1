const stringify = require('json-stable-stringify');
const swaggerJSDoc = require('swagger-jsdoc');

const options = {
  swaggerDefinition: {
    info: {
      version: '1.0.0',
      title: 'Invenco Cloud Services RKI API',
      description:
        'This is the REST API documentation for the Invenco Cloud Services Remote Key Injection endpoints',
      termsOfService:
        'http://www.invenco.com/products/invenco-cloud-solutions/',
    },
    consumes: ['application/json'],
    produces: ['application/json'],
    securityDefinitions: {
      Bearer: {
        type: 'apiKey',
        name: 'Authorization',
        in: 'header',
      },
    },
    security: [
      {
        Bearer: [],
      },
    ],
  },
  apis: ['./src/rki/**/*.endpoint.js'],
};

const swaggerSpec = {};

module.exports = {
  getApiDocs: (req, res) => {
    if (!swaggerSpec[req.headers.host]) {
      options.swaggerDefinition.host = req.headers.host;
      options.swaggerDefinition.basePath = req.url.replace('docs/api.json', '');
      options.swaggerDefinition.schemes = [req.isSecure() ? 'https' : 'http'];
      swaggerSpec[req.headers.host] = stringify(swaggerJSDoc(options));
    }
    // Raw Node response to avoid being stringified by Restify
    res.writeHead(200, {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(swaggerSpec[req.headers.host]),
    });

    res.write(swaggerSpec[req.headers.host]);
    return res.end();
  },
};
