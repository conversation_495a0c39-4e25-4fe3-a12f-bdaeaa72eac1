export interface RestifyRequest {
  log: {
    info: (...args: unknown[]) => void;
    error: (...args: unknown[]) => void;
  };
  user: {
    sub: string;
    company: {
      id: string;
    };
  };
  body: {
    mfaCode: string;
    approved: boolean;
  };
  params: {
    keyRequestDeviceId: string;
    keyRequestSessionId: string;
  };
}
export interface RestifyResponse {
  send: (status: number) => void;
}
export interface RestifyNext {
  (error?: unknown): void;
}

export interface KeyRequestDevice {
  keyRequestDeviceId: string;
  deviceId: number;
  certificate?: string;
  keyGroupRef: string;
  status: number;
  isJsonCertificates: boolean;
}

export interface KeyRequestSession {
  keyRequestSessionId: string;
  companyId: string;
  name: string;
  keyGroupRef: string;
  issuerCode: string;
  requestUserId: string;
  authorizedTime: string;
  secondAuthorizedTime: string;
  status: number;
  authorizerUserId?: string;
  secondAuthorizerUserId?: string;
}

export interface RKIApprover {
  id: string;
  fullName: string;
  email: string;
  status: string;
}

export interface Certificate {
  certificate: string;
  issuer_code: string;
}

export interface StateMachineType {
  [x: string]: (arg0: string) => unknown;
}
