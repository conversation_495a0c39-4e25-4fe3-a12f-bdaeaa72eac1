const Joi = require('joi');

const { server } = require('../../../../app');
const env = require('../../../../env');
const {
  validateParams,
  validateBody,
  requiresRole,
} = require('../../../../lib/pre');
const handler = require('./approval.handler');

const BASE_PATH = `${env.config.base}/rki/requests`;

/**
 * @swagger
 *   "/rki/requests/expire":
 *     post:
 *       tags:
 *         - RKI
 *       summary: "Find requests that have passed their expiry time and update their status to be expired"
 *       description: "Find requests that have passed their expiry time and update their status to be expired"
 *       responses:
 *         "204":
 *           description: "Successful response - No content"
 */
server.post(
  {
    path: `${BASE_PATH}/expire`,
    version: '1.0.0',
  },
  [requiresRole(['ICS_SYSTEM']), handler.expire]
);

/**
 * @swagger
 *   "/rki/requests/{keyRequestSessionId}/approve":
 *     post:
 *       tags:
 *         - RKI
 *       summary: "Approve an RKI request and initialize key request retrieval"
 *       description: "Approve an RKI request and initialize key request retrieval"
 *       produces:
 *         - "application/json"
 *       parameters:
 *         - in: path
 *           name: keyRequestSessionId
 *           type: string
 *           format: uuid
 *           required: true
 *         - in: body
 *           name: body
 *           schema:
 *             type: object
 *             properties:
 *               approved:
 *                 type: boolean
 *                 description: True for Approval and False for Rejection
 *               mfaCode:
 *                 type: string
 *                 description: The user's MFA code
 *       responses:
 *         "204":
 *           description: "Successful response - No content"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "406":
 *           description: MFA code invalid
 *         "409":
 *           description: Key request session's status is not in pending approval
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/:keyRequestSessionId/approve`,
    version: '1.0.0',
  },
  [
    requiresRole(['RKI']),
    validateParams({
      keyRequestSessionId: Joi.string().guid().required(),
    }),
    validateBody({
      approved: Joi.boolean(),
      mfaCode: Joi.string(),
    }),
    handler.approveRequest,
  ]
);

/**
 * @swagger
 *   "/rki/requests/{keyRequestSessionId}/cancel":
 *     post:
 *       tags:
 *         - RKI
 *       summary: "Cancel an RKI request"
 *       description: "Cancel an RKI request"
 *       produces:
 *         - "application/json"
 *       parameters:
 *         - in: path
 *           name: keyRequestSessionId
 *           type: string
 *           format: uuid
 *           required: true
 *       responses:
 *         "204":
 *           description: "Successful response - No content"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "406":
 *           description: MFA code invalid
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/:keyRequestSessionId/cancel`,
    version: '1.0.0',
  },
  [
    requiresRole(['RKI']),
    validateParams({
      keyRequestSessionId: Joi.string().guid().required(),
    }),
    validateBody({
      mfaCode: Joi.string(),
    }),
    handler.cancelRequest,
  ]
);
