import restifyErrors from 'restify-errors';
import { server } from '../../../../app';
import usersHelper from '../../../../helpers/users-helper';
import companyHelper from '../../../../helpers/company-helper';
import keyRequestHelper, {
  KEY_REQUEST_SESSION_STATUS,
} from '../../../../helpers/key-request-helper';
import totp from '../../../../lib/totp';
import krdStateMachine from '../../lib/key-request-device.statemachine';
import KRSHelper from '../krs/krs-helper';
import CONSTANTS from '../../lib/constants';
import { config } from '../../../../env';
import { invokeLambdaForDevice } from '../krs/invokeRKILambdaForDevice';
import {
  KeyRequestDevice,
  RestifyRequest,
  KeyRequestSession,
  RestifyResponse,
  RestifyNext,
  RKIApprover,
  Certificate,
  StateMachineType,
} from './approval';

const { rkiServiceLambda } = config.AWS.Lambda;

const handleRemovedDevice = async (krd: KeyRequestDevice) => ({
  msg: 'Failed',
  krdId: krd.keyRequestDeviceId,
});

const handleMissingCertificate = async (
  krd: KeyRequestDevice,
  sm: StateMachineType,
  req: RestifyRequest
) => {
  req.log.info(
    `Device id: ${krd.deviceId} does not have certificate, thus skip to invoke rki-service.`
  );
  await sm[krdStateMachine.actions.keyInstallFailed.name](
    'No certificate found for the device'
  );
  return {
    msg: 'Failed - No certificate found for the device',
    krdId: krd.keyRequestDeviceId,
  };
};

const handleUnsupportedIssuer = async (
  krd: KeyRequestDevice,
  issuerCode: string,
  sm: StateMachineType,
  req: RestifyRequest
) => {
  req.log.info(
    `Key Group Ref: ${krd.keyGroupRef} Issuer Code: ${issuerCode} Unhandled Key Group Issuer Code, thus skip invoke rki-service.`
  );
  await sm[krdStateMachine.actions.keyInstallFailed.name](
    'Unhandled Key Group Certificate Issuer'
  );
  return {
    msg: 'Failed - Unhandled Key Group Certificate Issuer',
    krdId: krd.keyRequestDeviceId,
  };
};

const checkForValidCertificate = (
  certificateArray: Certificate[],
  issuerCode: string
) =>
  certificateArray.find(
    certificate => certificate.issuer_code === issuerCode
  ) || null;

const handleCertificate = async (
  foundValidCertificate: Certificate | null,
  deviceId: number,
  sm: StateMachineType,
  req: RestifyRequest
) => {
  if (foundValidCertificate) {
    return {
      foundValidDeviceCertificate: foundValidCertificate,
      errorMsg: null,
    };
  }
  req.log.info(
    `Device id: ${deviceId} does not have certificate for the correct issuer, thus skip invoke rki-service.`
  );
  await sm[krdStateMachine.actions.keyInstallFailed.name](
    'No certificate found for the key group issuer code.'
  );
  return {
    foundValidDeviceCertificate: null,
    errorMsg: 'Failed - No certificate found for the key group issuer code',
  };
};

const parseCertificates = (
  certificateString: string,
  deviceId: number,
  req: RestifyRequest
) => {
  try {
    const parsed = JSON.parse(certificateString);
    if (!Array.isArray(parsed)) {
      throw new Error('Parsed result is not an array');
    }
    return parsed;
  } catch (error) {
    req.log.info(
      error,
      `Device id: ${deviceId} does not have valid JSON certificate, thus skip invoke rki-service.`
    );
    return null;
  }
};

const handleJsonCertificates = async (
  krd: KeyRequestDevice,
  sm: StateMachineType,
  keyRequestSession: KeyRequestSession,
  req: RestifyRequest
) => {
  if (!krd.certificate) {
    return {
      foundValidDeviceCertificate: null,
      errorMsg: 'Failed - Invalid JSON certificate',
    };
  }
  try {
    const certificateArray = parseCertificates(
      krd.certificate,
      krd.deviceId,
      req
    );
    if (!certificateArray)
      return {
        foundValidDeviceCertificate: null,
        errorMsg: 'Failed - Invalid JSON certificate',
      };

    const foundValidCertificate = checkForValidCertificate(
      certificateArray,
      keyRequestSession.issuerCode
    );
    return await handleCertificate(
      foundValidCertificate,
      krd.deviceId,
      sm,
      req
    );
  } catch (error) {
    req.log.info(
      error,
      `Device id: ${krd.deviceId} encountered an error, thus skip invoke rki-service.`
    );
    await sm[krdStateMachine.actions.keyInstallFailed.name](
      'No valid JSON certificate found for the device'
    );
    return {
      foundValidDeviceCertificate: null,
      errorMsg: 'Failed - No valid JSON certificate found for the device',
    };
  }
};

const validateJsonCertificates = async (
  krd: KeyRequestDevice,
  sm: StateMachineType,
  keyRequestSession: KeyRequestSession,
  req: RestifyRequest
) => {
  if (!krd.certificate) {
    return new Error('Undefined Certificate');
  }
  const { foundValidDeviceCertificate, errorMsg } =
    await handleJsonCertificates(krd, sm, keyRequestSession, req);

  if (!foundValidDeviceCertificate) {
    return new Error(errorMsg || 'No Valid Device Certificate'); // Return an Error object for consistency
  }

  return foundValidDeviceCertificate.certificate; // Return the valid certificate
};

const validateLegacyCertificate = (
  krd: KeyRequestDevice,
  keyRequestSession: KeyRequestSession
) => {
  if (
    keyRequestSession.issuerCode !== CONSTANTS.ValidCertificateIssuerCode.LEGACY
  ) {
    return new Error('Unsupported issuer for legacy certificate'); // Return an Error object
  }

  return krd.certificate; // Return the existing certificate
};

const getValidDeviceCertificate = async (
  krd: KeyRequestDevice,
  sm: StateMachineType,
  keyRequestSession: KeyRequestSession,
  req: RestifyRequest
) => {
  if (krd.isJsonCertificates) {
    return validateJsonCertificates(krd, sm, keyRequestSession, req);
  }
  // if the certificate is not JSON, then it is a legacy certificate
  return validateLegacyCertificate(krd, keyRequestSession);
};

export const processKeyRequestDevice = async (
  krd: KeyRequestDevice,
  conn: {},
  req: RestifyRequest,
  keyRequestSession: KeyRequestSession
) => {
  try {
    const sm = await krdStateMachine.load(krd.keyRequestDeviceId, conn);

    if (krd.status === krdStateMachine.states.removed) {
      return handleRemovedDevice(krd);
    }

    if (!krd.certificate) {
      return await handleMissingCertificate(krd, sm, req);
    }

    if (
      !Object.values(CONSTANTS.ValidCertificateIssuerCode).includes(
        keyRequestSession.issuerCode
      )
    ) {
      return await handleUnsupportedIssuer(
        krd,
        keyRequestSession.issuerCode,
        sm,
        req
      );
    }

    const validDeviceCertificate = await getValidDeviceCertificate(
      krd,
      sm,
      keyRequestSession,
      req
    );

    if (validDeviceCertificate instanceof Error) {
      return {
        msg: validDeviceCertificate.message,
        krdId: krd.keyRequestDeviceId,
      };
    }
    await sm[krdStateMachine.actions.keyRetrieval.name]();

    return await invokeLambdaForDevice(
      krd,
      keyRequestSession,
      validDeviceCertificate
    );
  } catch (e) {
    req.log.error(
      e,
      'Error occurred while updating key_request_device status!'
    );
    return {
      msg: 'Error occurred while updating key_request_device status!',
      krdId: krd.keyRequestDeviceId,
    }; // Default return for the async function
  }
};

export const approveRequest = async (
  req: RestifyRequest,
  res: RestifyResponse,
  next: RestifyNext
) => {
  const { approved, mfaCode } = req.body;
  const { keyRequestSessionId } = req.params;
  const { user } = req;
  const userId = user.sub;
  const serviceCompanyId = user.company.id;
  const mfaSecret = await usersHelper.getUserMfaSecretById(userId);
  const KRS: Partial<KeyRequestSession> = {};

  let isKRSUpdated = false;
  let isAutomatedRKI = false;

  req.log.info(`Authorizing key request session: ${keyRequestSessionId}`);

  if (!(await totp.validateTotp(req, mfaSecret, mfaCode))) {
    return next(new restifyErrors.NotAcceptableError('MFA code invalid')); // 406
  }

  const keyRequestSession =
    await keyRequestHelper.getKeyRequestSessionById(keyRequestSessionId);

  if (!keyRequestSession) {
    return next(
      new restifyErrors.NotFoundError(
        `Key request session with id: ${keyRequestSessionId} not found`
      )
    ); // 404
  }

  if (serviceCompanyId !== keyRequestSession.companyId) {
    const allowedToManage = await KRSHelper.getAllowedCompanyId({
      serviceCompanyId,
      serviceRecipientId: keyRequestSession.companyId,
    });
    if (!allowedToManage) {
      return next(new restifyErrors.ForbiddenError('Permission denied')); // 403
    }
  }

  isAutomatedRKI = keyRequestHelper.isAutomatedKRS(keyRequestSession);

  // for manual the RKI creator should be considered as an implicit approver and he cannot approve request for a second time
  if (!isAutomatedRKI && keyRequestSession.requestUserId === userId) {
    return next(
      new restifyErrors.BadRequestError(
        'This RKI has been already approved by this user'
      )
    ); // 400
  }

  // Check if the request is in pending status
  if (
    keyRequestSession.status !==
      KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION &&
    keyRequestSession.status !==
      KEY_REQUEST_SESSION_STATUS.PENDING_SECOND_AUTHORIZATION
  ) {
    req.log.info(
      'Key request session is not in pending status and thus cannot be authorized or rejected'
    );

    return next(
      new restifyErrors.ConflictError(
        'The current request can no longer be approved or rejected. Please reload.'
      )
    ); // 409
  }

  if (!approved) {
    // RKI rejected
    KRS.status = KEY_REQUEST_SESSION_STATUS.REJECTED;
  } else if (
    keyRequestSession.authorizerUserId &&
    keyRequestSession.authorizerUserId === userId
  ) {
    req.log.info(
      'The second approver is same as the first approver. Approval cannot go ahead.'
    );

    return next(
      new restifyErrors.BadRequestError(
        'This RKI has been already approved by this user'
      )
    );
  } else if (!keyRequestSession.authorizerUserId) {
    // first approval
    KRS.authorizerUserId = userId;
    KRS.authorizedTime = new Date().toISOString();
    KRS.status = isAutomatedRKI
      ? KEY_REQUEST_SESSION_STATUS.PENDING_SECOND_AUTHORIZATION
      : KEY_REQUEST_SESSION_STATUS.IN_PROGRESS;
  } else if (
    keyRequestSession.authorizerUserId &&
    !keyRequestSession.secondAuthorizerUserId
  ) {
    // second approval
    KRS.secondAuthorizerUserId = userId;
    KRS.secondAuthorizedTime = new Date().toISOString();
    KRS.status = KEY_REQUEST_SESSION_STATUS.IN_PROGRESS;
  }

  const conn = await server.db.write.getConnection();

  if (Object.keys(KRS).length) {
    await conn.execute('BEGIN');
    if (
      !(await keyRequestHelper.updateKeyRequestSession(
        keyRequestSessionId,
        KRS,
        conn
      ))
    ) {
      await conn.execute('ROLLBACK');
      conn.done();
      return next(
        new restifyErrors.InternalServerError(
          'Failed to update key request session'
        )
      ); // 500
    }
    isKRSUpdated = true;
  }

  // If its approved then invoke rki service lambda
  if (
    KRS.status === KEY_REQUEST_SESSION_STATUS.IN_PROGRESS &&
    rkiServiceLambda
  ) {
    let keyRequestDevices: KeyRequestDevice[] = [];
    try {
      keyRequestDevices =
        await keyRequestHelper.getKeyRequestDevicesBySessionId(
          keyRequestSessionId
        );
    } catch (e) {
      await conn.execute('ROLLBACK');
      conn.done();
      return next(
        new restifyErrors.InternalServerError('Failed to approve the session')
      ); // 500
    }

    await Promise.all(
      keyRequestDevices.map(krd =>
        processKeyRequestDevice(krd, conn, req, keyRequestSession)
      )
    )
      .then(async result => {
        if (
          Array.isArray(result) &&
          result.every(r => r && r.msg && r.msg.toLowerCase() === 'failed')
        ) {
          const krs = { status: KEY_REQUEST_SESSION_STATUS.COMPLETE };
          // KRS should be updated with status Complete when all KRD in it are either Failed or Removed.
          await keyRequestHelper.updateKeyRequestSession(
            keyRequestSessionId,
            krs,
            conn
          );
        }
        return conn.execute('COMMIT');
      })
      .catch(async e => {
        req.log.error('Error occurred while invoking rki-service: %s', e);
        await conn.execute('ROLLBACK');
        conn.done();
        return next(
          new restifyErrors.InternalServerError('Failed to approve the session')
        ); // 500
      });
  } else if (isKRSUpdated) {
    await conn.execute('COMMIT');
  }

  // Send notifications to users - do not send notifications for intermediate statuses (like pending second approval)
  if (
    KRS.status === KEY_REQUEST_SESSION_STATUS.IN_PROGRESS ||
    KRS.status === KEY_REQUEST_SESSION_STATUS.REJECTED
  ) {
    let notificationType;
    const creator = await KRSHelper.getKRSCreator(
      keyRequestSession.keyRequestSessionId
    );

    const approvers: RKIApprover[] = [];
    (
      await KRSHelper.getKRSApprover(keyRequestSession.keyRequestSessionId)
    ).forEach((approver: RKIApprover) => {
      const { id, fullName, email, status } = approver;
      approvers.push({ id, fullName, email, status });
    });

    if (KRS.status === KEY_REQUEST_SESSION_STATUS.IN_PROGRESS) {
      notificationType = CONSTANTS.NotificationTypes.RKI_REQUEST_APPROVED;
    } else {
      notificationType = CONSTANTS.NotificationTypes.RKI_REQUEST_REJECTED;
    }

    const rkiSessionData = {
      id: keyRequestSession.keyRequestSessionId,
      companyId: keyRequestSession.companyId,
      name: keyRequestSession.name,
      deviceRequests: keyRequestSession.keyRequestDevices,
    };

    await KRSHelper.createRKINotification(
      notificationType,
      { ...rkiSessionData, isAutomatedRKI },
      approvers,
      creator
    );
  }

  conn.done();

  res.send(204);
  return next();
};

export const cancelRequest = async (
  req: RestifyRequest,
  res: RestifyResponse,
  next: RestifyNext
) => {
  const { mfaCode } = req.body;
  const { keyRequestSessionId } = req.params;
  const { user } = req;
  const userId = user.sub;
  const companyId = user.company.id;
  const mfaSecret = await usersHelper.getUserMfaSecretById(userId);
  const krs: Partial<KeyRequestSession> = {};

  req.log.info(`Cancelling key request session: ${keyRequestSessionId}`);

  if (!(await totp.validateTotp(req, mfaSecret, mfaCode))) {
    return next(new restifyErrors.NotAcceptableError('MFA code invalid')); // 406
  }

  try {
    const keyRequestSession =
      await keyRequestHelper.getKeyRequestSessionById(keyRequestSessionId);

    if (!keyRequestSession) {
      return next(
        new restifyErrors.NotFoundError(
          `Key request session with id: ${keyRequestSessionId} not found`
        )
      ); // 404
    }

    if (keyRequestSession.requestUserId !== userId) {
      return next(
        new restifyErrors.ForbiddenError(
          'Permission denied - Authorizer cannot cancel a pending request. Only reject it'
        )
      ); // 403
    }
    const canManageServiceRecipient =
      await companyHelper.canManageServiceRecipient({
        serviceRecipientId: keyRequestSession.companyId,
        serviceManagerId: companyId,
      });
    if (
      keyRequestSession.companyId !== companyId &&
      !canManageServiceRecipient
    ) {
      return next(new restifyErrors.ForbiddenError('Permission denied')); // 403
    }

    if (
      keyRequestSession.status !==
        KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION &&
      keyRequestSession.status !==
        KEY_REQUEST_SESSION_STATUS.PENDING_SECOND_AUTHORIZATION
    ) {
      return next(
        new restifyErrors.BadRequestError(
          'Request can only be cancelled when pending authorization'
        )
      ); // 400
    }

    krs.status = KEY_REQUEST_SESSION_STATUS.CANCELLED;
    krs.authorizerUserId = userId;
    krs.authorizedTime = new Date().toISOString();

    if (Object.keys(krs).length) {
      await keyRequestHelper.updateKeyRequestSession(keyRequestSessionId, krs);
    }

    res.send(204);
    return next();
  } catch (e) {
    return next(new restifyErrors.InternalServerError(e)); // 500
  }
};

export const expire = async (
  req: RestifyRequest,
  res: RestifyResponse,
  next: RestifyNext
) => {
  try {
    const krsList =
      (await keyRequestHelper.getExpiredKeyRequestSessions()) || [];
    const krsChunks = krsList.reduce(
      (
        chunks: string[][],
        item: { keyRequestSessionId: string },
        index: number
      ) => {
        const chunkSize = 10;
        const chunkIndex = Math.floor(index / chunkSize);
        if (!chunks[chunkIndex]) {
          // eslint-disable-next-line no-param-reassign
          chunks[chunkIndex] = []; // start a new chunk
        }
        chunks[chunkIndex].push(item.keyRequestSessionId);
        return chunks;
      },
      []
    );

    await Promise.all(
      krsChunks.map((chunk: string) =>
        server.db.write.execute(
          `
                UPDATE key_request_session SET status = $1
                WHERE key_request_session_id = ANY ($2)`,
          [KEY_REQUEST_SESSION_STATUS.EXPIRED, chunk]
        )
      )
    );

    if (krsList.length > 0) {
      req.log.info(
        `Expired a total of ${krsList.length} keyRequestSessions as they are passed their expiry date`
      );
    }

    res.send(204);
    return next();
  } catch (e) {
    return next(new restifyErrors.InternalServerError(e)); // 500
  }
};
