const crypto = require('crypto');
const restifyErrors = require('restify-errors');
const { config } = require('../../env');
const logger = require('../../lib/logger').mainLogger();
const csrfProtectedRoutes = require('./csrf-protected-routes.json');

const BASE_PATH = config.base;

const CSRF_SECRET = config.csrf?.key || 'CSRF_SECRET_KEY';
const ignoredMethods = ['GET', 'HEAD', 'OPTIONS'];

const generateRandomToken = () => crypto.randomBytes(64).toString('hex');

const createHmacHash = (data, userId) =>
  crypto
    .createHmac('sha256', `${userId}${CSRF_SECRET}`)
    .update(data)
    .digest('hex');

const generateCsrfTokenAndHash = userId => {
  const csrf = generateRandomToken();
  const csrfHash = createHmacHash(csrf, userId);
  return { csrf, csrfHash };
};

const isCsrfProtectedRoute = req => {
  const route = req.route.path.replace(`${BASE_PATH}/`, '');
  return csrfProtectedRoutes[route] ?? false;
};

const verifyCsrfToken = (req, res, next) => {
  const isSystemUser = req.user.roles.includes('ICS_SYSTEM');
  if (
    isSystemUser ||
    ignoredMethods.includes(req.method) ||
    !isCsrfProtectedRoute(req)
  ) {
    return next();
  }

  const userId = req.user.sub;
  const csrfToken = req.headers['csrf-token'];
  const csrfTokenHash = req.headers['csrf-token-h'];

  if (!csrfToken || !csrfTokenHash) {
    return next(new restifyErrors.ForbiddenError('Invalid CSRF token'));
  }

  const expectedHash = createHmacHash(csrfToken, userId);
  if (expectedHash !== csrfTokenHash) {
    logger.error(
      `Invalid CSRF token:${csrfToken} and CsrhHash:${csrfTokenHash}.`
    );
    return next(new restifyErrors.ForbiddenError('Invalid CSRF token'));
  }
  return next();
};

module.exports = {
  generateCsrfTokenAndHash,
  verifyCsrfToken,
};
