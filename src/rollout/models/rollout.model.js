const uuid = require('uuid/v4');
const restify = require('restify');
const moment = require('moment');

const { server } = require('../../../app');
const jobHelper = require('../../../helpers/job-helper');
const logger = require('../../../lib/logger').mainLogger();
const DeploymentPolicy = require('../lib/DeploymentPolicy');
const { deployAction } = require('../lib/rolloutConstants');

const DISPLAYNAME_BY_COMPANY_ID = `
        SELECT display_name
        FROM company_product_type cpt
        WHERE cpt.device_type = $1
        AND cpt.company = $2`;

const DELETE_BY_ID_QUERY = `
    UPDATE release
    SET deleted = true,
        last_modified_by = $1,
        date_time_last_modified = now()
    WHERE release_id = $2;`;

const CANCEL_BY_ID_QUERY = `
    UPDATE release
    SET cancelled = true,
        cancelled_by = $1,
        last_modified_by = $1,
        date_time_last_modified = now(),
        cancel_time = now()
    WHERE release_id = $2;
`;

const GET_JOBS_BY_RELEASE_ID = `
    SELECT j.*
    FROM target_release tr
    JOIN target t
    ON tr.target_id = t.target_id
    JOIN job j on j.id in (tr.download_job, tr.install_job) and j.status <= ${jobHelper.jobStatus.ACCEPT}
    WHERE release_id = $1 and t.active
    ORDER by t.name
`;

const WHERE_CLAUSE = `
    r.deleted IS NOT TRUE AND
    usa.user_id = $1 AND
    (
        uc.company_id = $2 OR
        uc.company_id IN (
            SELECT cr.company_id
            FROM company_relationship cr
            WHERE
                cr.allowed_company_id = $2
        )
    )
`;

const JOIN_BASIC_INFO = `
    JOIN ics_user uc ON r.created_by = uc.id
    JOIN target_release tr ON r.release_id = tr.release_id
    JOIN target t ON tr.target_id = t.target_id
    JOIN user_site_authorization usa ON t.site_id = usa.site_id
`;

const JOIN_BASIC_INFO_2 = `
    JOIN company_product_type cpt on cpt.company = uc.company_id
    JOIN software sw ON r.software_id = sw.software_id
`;

const getRolloutQueryWithCondition = conditions => `
    SELECT distinct(r.release_id) AS id, uc.company_id, r.type, r.schedule_start_time, r.schedule_download_start_time,
          COALESCE(r.schedule_start_time, r.schedule_download_start_time) AS order_col
    FROM release r
    ${JOIN_BASIC_INFO}
    ${JOIN_BASIC_INFO_2}
    WHERE ${conditions}
    ORDER BY order_col DESC
`;

const JOIN_RELEASE_INFO = `
    JOIN ics_user uc ON r.created_by = uc.id
    JOIN company c ON uc.company_id = c.id
    JOIN software sw ON r.software_id = sw.software_id
    LEFT JOIN deploy_policy dp ON r.deploy_policy_id = dp.id
    LEFT JOIN ics_user ca ON r.cancelled_by = ca.id
    LEFT JOIN company cac ON ca.company_id = cac.id
    LEFT JOIN prompt_set ps ON ps.id = (sw.related_entity)::UUID
`;

const JOIN_JOB = `
    LEFT JOIN job ij ON ij.id = tr.install_job
    LEFT JOIN job dj ON dj.id = tr.download_job
`;

const RELEASE_DEPLOYMENT_QUERY = `
    with deployment as (
    select
        tr.target_release_id,
        tr.target_id,
        COALESCE(ij.status, dj.status) AS job_status,
        t.site_id,
        t.name,
        device_type,
        serial_number,
        last_contact,
        last_registered
    from
        release r
    ${JOIN_BASIC_INFO}
    ${JOIN_JOB}
    where
        ${WHERE_CLAUSE}
        AND r.release_id = $3
    )
    select
     json_agg(
         json_strip_nulls(
             json_build_object(
                'id', target_release_id,
                'status', job_status,
                'target', json_build_object
                (
                    'id', target_id,
                    'name', deployment.name,
                    'deviceType', json_build_object('id', device_type, 'name',device_type),
                    'serialNumber', serial_number,
                    'lastContact', round(EXTRACT(EPOCH FROM last_contact) * 1000),
                    'lastRegistered', round(EXTRACT(EPOCH FROM last_registered) * 1000)
                ),
                'site', json_build_object('id', site.site_id, 'name', site.name))
            )
     ) as deployments
    from
        deployment
    inner join site on
        site.site_id = deployment.site_id
`;

/**
 * Returns qury for release data, will restrict rolllouts to only those user
 * have access to when restrictToUser is true
 *
 * @param {boolean} [restrctToUser=true]
 */
const releaseDataQuery = (restrctToUser = true) => `
    WITH releases AS (
        SELECT r.release_id                                     AS id,
            r.name,
            r.description,
            r.cancelled,
            r.deleted,
            r.type,
            r.retry,
            r.install_window,
            r.install_frequency,
            array_agg(DISTINCT tr.download_job)                       AS download_jobs,
            array_agg(DISTINCT tr.install_job)                        AS install_jobs,
            array_agg(DISTINCT tr.target_release_id)                  AS target_releases,
            EXTRACT(EPOCH FROM r.schedule_start_time) * 1000 AS start_time,
            EXTRACT(EPOCH FROM r.schedule_end_time) * 1000   AS end_time,
            r.deploy_action,
      	    EXTRACT(EPOCH FROM r.schedule_download_start_time) * 1000 AS download_start_time,
            EXTRACT(EPOCH FROM r.schedule_download_end_time) * 1000 AS download_end_time,
            r.download_window
        FROM release r
            ${JOIN_BASIC_INFO}
        WHERE r.release_id = ANY ($1)
        ${
          restrctToUser
            ? `
            AND usa.user_id = $2 AND
            (
                uc.company_id = $3 OR
                uc.company_id IN (
                    SELECT cr.company_id
                    FROM company_relationship cr
                    WHERE
                        cr.allowed_company_id = $3
                )
            )
        `
            : ''
        }
        GROUP BY r.release_id
    )
    SELECT *,
    (
        SELECT COUNT(j.id)
        FROM job j
        WHERE j.id = ANY (r.download_jobs)
        AND j.status = 3 -- complete
    )   AS deployments_downloaded_count,
    (
        SELECT json_build_object(
            'total', 
                COUNT(tr.release_id),
			      'cancelled',
                COUNT(CASE WHEN (j2.status = 5 and (j3.status is null or j3.status != all ('{3,4}'))) or (j3.status = any ('{5}')) then j2.id end),
		      	'failed',
		           	COUNT(CASE WHEN (j2.status = 4 and (j3.status is null or j3.status != 5)) or (j3.status = 4) then j2.id end),
		      	'pendingDownload',
			          COUNT(CASE WHEN ((j2.status = 0 or j2.status = 2) and (j3.status is null or j3.status = 0)) then j2.id end),
		      	'pendingInstall',
		          	COUNT(CASE WHEN (j2.status = 3) and (j3.status = any ('{0,2}')) then j2.id end),
            'completed',
		            COUNT(case when ((j3.id is null and j2.status = 3) or (j3.status = 3)) then j2.id end)
    )
        FROM target_release tr
                INNER JOIN job j2 on tr.download_job = j2.id
                LEFT JOIN job j3 on tr.install_job = j3.id
        WHERE tr.target_release_id = ANY (r.target_releases)
        GROUP BY tr.release_id
    ) as file_download_status,
    (
    SELECT json_object_agg(tmp.status, tmp.count)
    FROM (
                SELECT j.status, COUNT(j.status)
                FROM job j
                WHERE j.id = ANY (r.install_jobs)
                GROUP BY j.status
            ) AS tmp
    ) as status,
    (
        SELECT json_object_agg(tmp.status, tmp.count)
        FROM (
            SELECT j.status, COUNT(j.status) AS count
            FROM job j
            WHERE r.deploy_action = '${deployAction.DOWNLOAD}' and j.id = ANY (r.download_jobs)
            GROUP BY j.status
        ) AS tmp
    ) AS download_only_status
    FROM releases r;
`;

const RELATED_INFO_QUERY = `
        SELECT
           r.release_id AS id,
           CASE
             WHEN r.cancelled THEN json_build_object(
                 'id', ca.id,
                 'fullName', ca.full_name,
                 'company', json_build_object(
                     'id', cac.id,
                     'name', cac.name
                   )
               )
             ELSE NULL
             END        AS cancelled_by,

           json_build_object(
               'id', uc.id,
               'fullName', uc.full_name,
               'company', json_build_object(
                   'id', c.id,
                   'name', c.name
                 )
             )          AS created_by,

           json_build_object(
               'id', dp.id,
               'description', dp.description,
               'name', dp.name
             )          AS deployment_policy,

           json_build_object(
               'id', sw.software_id,
               'name', sw.name,
               'relatedEntity', sw.related_entity,
               'softwareFile', sw.software_file,
               'deviceType', sw.device_type,
               'subDeviceType', sw.sub_device_type
             )          AS software,

           CASE
             WHEN r.type = 'media' THEN json_build_object(
                 'status', ps.status,
                 'version', ps.version
               )
             END        AS media
        FROM release r
        ${JOIN_RELEASE_INFO}
        WHERE
            r.release_id = ANY ( $1 )
`;
/**
 * @param {{ execute: (arg0: string, arg1: any[]) => any; }} connection
 * @param {string} jobId
 * @param {string} jobCancellationHistoryMessage
 */
function cancelThisJob(connection, jobId, jobCancellationHistoryMessage) {
  return [
    connection.execute(
      `
            UPDATE job
            SET status = $1, updated = now()
            WHERE id = $2`,
      [5, jobId]
    ),
    // Insert into job status history
    connection.execute(
      `
            INSERT INTO job_status_history
                (job_id, status, message, created_at)
            VALUES ( $1, $2, $3, now() );`,
      [jobId, 5, jobCancellationHistoryMessage]
    ),
  ];
}

const getTargetsById = async (/** @type {number[]} */ targetIds) =>
  server.db.read.row(
    `
            SELECT * FROM public.fn_get_deployable_targets_by_ids($1)
            `,
    [targetIds]
  );

/**
 * get sties that the given userId have access to.
 *
 * @param {string} userId
 * @returns {Promise<{user_id, site_id}>}
 */
const getAccessibleSitesbyUser = (
  /** @type {string} */ userId,
  /** @type {string[]} */ siteIds
) =>
  server.db.read.row(
    `
        select
        (SELECT JSON_OBJECT_AGG(site_id, user_id) sites_allowed
        FROM user_site_authorization usa
        WHERE usa.user_id = $1),
        (
        SELECT jsonb_object_agg(site.site_id, site.*) sites_found
        FROM site
        WHERE site_id = ANY($2))`,
    [userId, siteIds]
  );

const stripAndLogRequestData = async (
  originalRequestId,
  requestBody,
  deviceOwners,
  deviceIds,
  siteIds,
  companyId,
  userId
) => {
  // eslint-disable-next-line no-restricted-syntax
  for (const deviceOwner of deviceOwners) {
    const { companyId: ownerId } = deviceOwner;

    const strippedSitesQuery = server.db.read.rows(
      'SELECT site_id FROM site WHERE site_id = ANY($1) AND company_id = $2',
      [siteIds, ownerId]
    );
    const strippedTargetsQuery = server.db.read.rows(
      `
                SELECT target_id FROM target t
                INNER JOIN site s ON s.site_id = t.site_id
                WHERE t.target_id = ANY($1) AND s.company_id = $2`,
      [deviceIds, ownerId]
    );

    // eslint-disable-next-line no-await-in-loop
    const [strippedSites, strippedTargets] = await Promise.all([
      strippedSitesQuery,
      strippedTargetsQuery,
    ]);

    const strippedRequest = {
      ...requestBody,
      sites: strippedSites,
      targets: strippedTargets,
    };
    const requestId = deviceOwners.length === 1 ? originalRequestId : uuid();
    const data = JSON.stringify(strippedRequest);

    logger.info({
      req_id: requestId,
      statusCode: 200,
      userId,
      companyId,
      res: {
        resourceOwner: ownerId,
        body: data,
      },
    });
  }
};

/**
 *
 *
 * @param {*} connection
 * @param {*} rolloutDto
 * @returns
 */
const save = (connection, rolloutDto) => {
  const rolloutParams = Object.values(rolloutDto);
  return connection.execute(
    `
        INSERT INTO "release"
            (
                date_time_created,
                date_time_last_modified,
                active,
                "name",
                rollout,
                schedule_start_time,
                schedule_end_time,
                description,
                install_window,
                retry,
                kc_user,
                software_id,
                cancelled,
                deleted,
                created_by,
                last_modified_by,
                cancelled_by,
                cancel_time,
                deploy_policy_id,
                "type",
                install_frequency,
                schedule_download_start_time,
                schedule_download_end_time,
                download_window,
                deploy_action
            )
        VALUES (
                $1,
                now(),
                $2,
                $3,
                $4,
                $5,
                $6,
                $7,
                $8,
                $9,
                $10,
                $11,
                $12,
                $13,
                $14,
                $15,
                $16,
                $17,
                $18,
                $19,
                $20,
                $21,
                $22,
                $23,
                $24
            )
        RETURNING *
        `,
    rolloutParams
  );
};

async function cancelJobs(cancellableJobs, userEmail) {
  const jobCancellationHistoryMessage = `cancelled job by ${userEmail}`;
  const connection = await server.db.write.getConnection();
  try {
    await connection.execute('BEGIN');
    await Promise.all(
      cancellableJobs
        .map(job =>
          cancelThisJob(connection, job.id, jobCancellationHistoryMessage)
        )
        .flat()
    );
    await connection.execute('COMMIT');
    connection.done();
  } catch (e) {
    server.log.error('Error when cancelling jobs');
    await connection.execute('ROLLBACK');
    connection.done();
    throw e;
  }
}

const companySpecificDeviceName = async (
  /** @type {string} */ deviceType,
  /** @type {string} */ companyId
) => {
  const params = [deviceType, companyId];
  return server.db.read.row(
    `SELECT display_name
            FROM company_product_type cpt
            WHERE cpt.device_type = $1
                AND cpt.company = $2`,
    params
  );
};

const updateDeploymentCount = (
  /** @type {{ execute: (arg0: string, arg1: any[]) => any; }} */ connection,
  /** @type {number} */ rolloutId,
  /** @type {number} */ deploymentsCount
) =>
  connection.execute(
    ` UPDATE release
          SET deployments_count = $1, date_time_last_modified = now()
          WHERE release_id = $2 `,
    [deploymentsCount, rolloutId]
  );

const deployRollout = async (rolloutDto, software, sortedByTargetNameMap) => {
  const connection = await server.db.write.getConnection();
  let rollout = {};
  try {
    await connection.execute('BEGIN');
    const { 0: firstRow } = (await save(connection, rolloutDto)).rows;
    rollout = firstRow;
    const { totalDeployment } = await new DeploymentPolicy(
      sortedByTargetNameMap,
      rollout,
      rollout.deployPolicyId
    ).createDeployments(connection, software);
    await updateDeploymentCount(connection, rollout.releaseId, totalDeployment);
    await connection.execute('COMMIT');
  } catch (e) {
    logger.error('Error when creating rollout');
    await connection.execute('ROLLBACK');
    throw e;
  } finally {
    connection.done();
  }
  return rollout.releaseId;
};

/**
 *
 * @param {string[]} siteIds
 * @param {string} deviceType
 * @returns {Promise<{site_id: object[]}>}
 */
const getDeployableTargetsForSites = function getDeployableTargetsForSite(
  siteIds,
  deviceType
) {
  return server.db.read.row(
    `
            SELECT * FROM public.fn_get_deployable_targets_for_sites($1, $2)
            `,
    [siteIds, deviceType]
  );
};

/**
 * Retrieves rollout data for a specific release ID.
 * @param {integer} releaseId
 * @returns {Promise<{download_start_time: string, data: object}>}
 */
const getRolloutData = async releaseId => {
  const query = `
        SELECT 
            r.schedule_download_start_time AS download_start_time,
            JSON_OBJECT_AGG(tr.target_id, JSON_BUILD_OBJECT(
                'downloadJobId', tr.download_job,
                'targetReleaseId', tr.target_release_id,
                'status', j.status 
            )) AS data
        FROM 
            "release" r
        JOIN 
            target_release tr ON r.release_id = tr.release_id 
        JOIN 
            job j ON tr.download_job = j.id 
        WHERE 
            r.release_id = $1 
            AND r.deploy_action = $2
            AND j.status = $3
        GROUP BY r.release_id;
    `;

  return server.db.read.row(query, [
    releaseId,
    deployAction.DOWNLOAD,
    jobHelper.jobStatus.COMPLETE,
  ]);
};

/**
 * @param {Object} connection
 * @param {integer} rolloutId
 * @param {Object} rolloutDto
 * @returns {Promise<Object|null>}
 */
const updateRelease = async (connection, rolloutId, rolloutDto) => {
  const query = `
        UPDATE release
        SET schedule_start_time = $2, schedule_end_time = $3, install_window = $4, 
            deploy_policy_id = $5, deploy_action = $6, install_frequency = $7, 
            last_modified_by = $8, date_time_last_modified = NOW()
        WHERE release_id = $1 RETURNING *;
    `;

  const result = await connection.execute(query, [
    rolloutId,
    rolloutDto.scheduleStartTime,
    rolloutDto.scheduleEndTime,
    rolloutDto.install_window,
    rolloutDto.deploy_policy_id,
    rolloutDto.deploymentAction,
    rolloutDto.install_frequency,
    rolloutDto.last_modified_by,
  ]);

  return result?.rows?.[0] || null;
};

/**
 * @param {Object} sortedByTargetNameMap
 * @param {Object} rolloutData
 * @returns {{filteredMap: Object, filterDevicesCount: number}}
 */
const filterTargetMap = (sortedByTargetNameMap, rolloutData) => {
  let filterDevicesCount = 0;
  const filteredMap = Object.fromEntries(
    Object.entries(sortedByTargetNameMap).reduce((acc, [siteId, targets]) => {
      const filtered = targets.filter(
        target => rolloutData[target.target_id] && target.active
      );
      filterDevicesCount += filtered.length;
      acc.push([siteId, filtered]);
      return acc;
    }, [])
  );
  return { filteredMap, filterDevicesCount };
};

/**
 * @param {string} scheduleTime
 * @param {string} downloadTime
 * @returns {boolean}
 */
const isValidScheduleTime = (scheduleTime, downloadTime) => {
  const installStartTime = moment(scheduleTime);
  const downloadStartTime = moment(downloadTime);

  return installStartTime.isBetween(
    downloadStartTime,
    downloadStartTime.clone().add(1, 'week'),
    null,
    '[]'
  );
};

const deployInstallOnlyRollout = async (
  rolloutDto,
  software,
  sortedByTargetNameMap,
  releaseId
) => {
  const connection = await server.db.write.getConnection();
  try {
    const rolloutData = await getRolloutData(releaseId);
    if (!rolloutData) {
      throw new restify.BadRequestError(
        `The specified releaseId ${releaseId} was not found, or no download jobs have been completed for this release`
      );
    }

    if (
      !isValidScheduleTime(
        rolloutDto.scheduleStartTime,
        rolloutData.downloadStartTime
      )
    ) {
      throw new restify.BadRequestError(
        'Schedule start time is not within one week of download start time'
      );
    }

    await connection.execute('BEGIN');
    const rollout = await updateRelease(connection, releaseId, rolloutDto);
    if (!rollout) {
      throw new Error('Failed to update the release');
    }

    const { filteredMap, filterDevicesCount } = filterTargetMap(
      sortedByTargetNameMap,
      rolloutData.data
    );

    const { updateCount } = await new DeploymentPolicy(
      filteredMap,
      rollout,
      rollout.deployPolicyId
    ).createDeployments(connection, software, rolloutData);

    if (updateCount !== filterDevicesCount) {
      throw new Error('Failed to update the target release');
    }
    await connection.execute('COMMIT');
  } catch (e) {
    logger.error(
      `Error when creating install rollout for releaseId ${releaseId}`
    );
    await connection.execute('ROLLBACK');
    throw e;
  } finally {
    connection.done();
  }
  return releaseId;
};

class Rollout {
  /**
   * Creates an instance of Rollout.
   * @param {Number} releaseId
   * @memberof Rollout
   */
  constructor(releaseId) {
    this.releaseId = releaseId;
  }

  async getOneById(userId = null, companyId = null) {
    const params = [[this.releaseId]];
    const restrictToUser = userId != null && companyId != null;
    if (userId && companyId) {
      params.push(userId, companyId);
    }
    const resultDataQuery = server.db.read.rows(
      releaseDataQuery(restrictToUser),
      params
    );
    const relatedInfoQuery = server.db.read.rows(RELATED_INFO_QUERY, [
      [this.releaseId],
    ]);
    return Promise.all([resultDataQuery, relatedInfoQuery]);
  }

  /**
   *
   *
   * @returns {Promise<Job>}
   * @memberof Rollout
   */
  getCancellableJobByRolloutId() {
    const params = [this.releaseId];
    return server.db.read.rows(GET_JOBS_BY_RELEASE_ID, params);
  }

  /**
   *
   *
   * @param {string} userId
   * @param {string} companyId
   * @returns {Promise<Deployment>}
   * @memberof Rollout
   */
  getDeployments(userId, companyId) {
    const params = [userId, companyId, this.releaseId];
    return server.db.read.row(RELEASE_DEPLOYMENT_QUERY, params);
  }

  /**
   *
   *
   * @param {string} userId
   * @returns {Promise<void>}
   * @memberof Rollout
   */
  deleteById(userId) {
    const params = [userId, this.releaseId];
    return server.db.write.execute(DELETE_BY_ID_QUERY, params);
  }

  /**
   *
   *
   * @param {string} userEmail
   * @returns {Promise<void>}
   * @memberof Rollout
   */
  async cancelById(userId, userEmail) {
    const params = [userId, this.releaseId];
    const cancellableJobs = await this.getCancellableJobByRolloutId();
    await cancelJobs(cancellableJobs, userEmail);
    return server.db.write.execute(CANCEL_BY_ID_QUERY, params);
  }
}

/**
 *
 *
 *
 * @param {String} deviceType
 * @param {String} companyId
 * @returns
 */
Rollout.getDeviceDisplayNameByCompany = (deviceType, companyId) => {
  const params = [deviceType, companyId];
  return server.db.read.row(DISPLAYNAME_BY_COMPANY_ID, params);
};

/**
 *
 *
 * @param {Number[]} releaseIds
 * @param {String[]} userId
 * @param {String[]} companyId
 * @returns {Promise<[any, any]>}
 */
Rollout.getManyById = (releaseIds, userId, companyId) => {
  const params = [releaseIds];
  const resultDataQuery = server.db.read.rows(releaseDataQuery(), [
    ...params,
    userId,
    companyId,
  ]);
  const relatedInfoQuery = server.db.read.rows(RELATED_INFO_QUERY, params);
  return Promise.all([resultDataQuery, relatedInfoQuery]);
};

Rollout.queryWithCondition = (params, additionalConditions, pageParams) => {
  const conditions = `
        ${WHERE_CLAUSE}
        ${additionalConditions}
    `;

  const pageQuery =
    pageParams &&
    ` LIMIT ${pageParams.pageSize}
        OFFSET ${pageParams.offset};`;

  return server.db.read.rows(
    `
            SELECT id,type, company_id as rollout_company_id, count (id) OVER()
            FROM (
            ${getRolloutQueryWithCondition(conditions)}
            ) AS tmp
            ${pageQuery || ''}
        `,
    params
  );
};

/**
 * @typedef {Object} RolloutInfo
 * @property {Number} releseId
 * @property {String} type
 * @property {String} type
 * @property {String} rolloutCompanyId
 * @property {Boolean} allSitesAccessible
 */
/**
 * Get information for the rollout with specific id
 *
 *
 * @param {Number} releaseId
 * @param {String} [userId=null]
 * @param {String} [companyId=null]
 * @returns {Promise<RolloutInfo>}
 */
Rollout.getInfoById = (releaseId, userId = null, companyId = null) => {
  let params = [releaseId];

  if (userId && companyId) params = [userId, companyId, ...params];

  const query = `
        select
            distinct r.release_id,
            r.type,
            uc.company_id rollout_company_id,
            count(tr.target_release_id) over (partition by r.release_id) total_deployments_count,
            (
                select
                    count(distinct target_release_id)
                from
                    target_release tr
                join target t on
                    tr.target_id = t.target_id
                join user_site_authorization usa on
                    t.site_id = usa.site_id
                where
                    tr.release_id = $3 AND ${WHERE_CLAUSE}
            ) accessible_deployments_count
        from
            release r
        join ics_user uc on
            r.created_by = uc.id
        join target_release tr on
            r.release_id = tr.release_id
        where
            not r.deleted and r.release_id = $3
    `;

  return server.db.read.row(query, params);
};

/**
 *
 *
 * @param {Number} softwareId
 * @returns
 */
Rollout.checkSoftwareAccess = softwareId =>
  server.db.read.row('SELECT * from software where software_id = $1', [
    softwareId,
  ]);

/**
 *
 *
 * @param {String[]} siteIds
 * @returns {Promise}
 */
Rollout.getCompanyBySites = siteIds =>
  server.db.read.rows('select company_id from site where site_id = ANY($1)', [
    siteIds,
  ]);

Rollout.getDeployableTargetsForSites = getDeployableTargetsForSites;
Rollout.deployRollout = deployRollout;
Rollout.companySpecificDeviceName = companySpecificDeviceName;
Rollout.stripAndLogRequestData = stripAndLogRequestData;
Rollout.getTargetsById = getTargetsById;
Rollout.getAccessibleSitesByUser = getAccessibleSitesbyUser;
Rollout.deployInstallOnlyRollout = deployInstallOnlyRollout;
Rollout.filterTargetMap = filterTargetMap;
Rollout.isValidScheduleTime = isValidScheduleTime;

module.exports = Rollout;
