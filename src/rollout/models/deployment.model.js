const uuid = require('uuid/v4');

const { jobType } = require('../../../helpers/job-helper');
/**
 *
 * Adding info for the target to each of the params used for insertion
 * later
 *
 * @param {*} targetReleaseParams
 * @param {*} jobDependencyParams
 * @param {*} jobCreationParams
 * @param {Number} targetId
 * @param {*} jobEmbargoTime
 * @param {string[]} siteLeaderDownloadJobs
 * @param {boolean} isLeader
 * @param {boolean} isEdgeBox
 */
const createDownloadJob = ({
  targetReleaseParams,
  jobDependencyParams,
  jobCreationParams,
  targetId,
  jobEmbargoTime,
  siteLeaderDownloadJobs,
  isLeader,
  isEdgeBox,
}) => {
  const { downloadJobEmbargoDate, jobExpiryDate, downloadJobExpiryDate } =
    jobEmbargoTime;

  const downloadJobId = uuid();
  const secondDownloadJobId = uuid();

  jobCreationParams.deviceId.push(targetId);
  jobCreationParams.jobId.push(downloadJobId);
  jobCreationParams.jobEmbargoTime.push(downloadJobEmbargoDate);
  jobCreationParams.expiry.push(downloadJobExpiryDate || jobExpiryDate);
  jobCreationParams.type.push(jobType.ROLLOUT_DOWNLOAD);

  targetReleaseParams.downloadJobs.push(
    isLeader && !isEdgeBox ? secondDownloadJobId : downloadJobId
  );

  if (!isLeader && siteLeaderDownloadJobs.length === 2) {
    // Download job for non-leader device depends on leader download job and
    jobDependencyParams.job_id.push(downloadJobId, downloadJobId);
    jobDependencyParams.dependens_on.push(...siteLeaderDownloadJobs);
    jobDependencyParams.continue_on_fail.push(true, true);
  }

  if (isLeader && siteLeaderDownloadJobs.length < 2) {
    siteLeaderDownloadJobs.push(downloadJobId);
    if (!isEdgeBox) {
      jobCreationParams.jobId.push(secondDownloadJobId);
      jobCreationParams.deviceId.push(targetId);
      jobCreationParams.jobEmbargoTime.push(downloadJobEmbargoDate);
      jobCreationParams.expiry.push(jobExpiryDate);
      jobCreationParams.type.push(jobType.ROLLOUT_DOWNLOAD);

      jobDependencyParams.job_id.push(secondDownloadJobId);
      jobDependencyParams.dependens_on.push(downloadJobId);
      jobDependencyParams.continue_on_fail.push(true);
    }
  }

  return { downloadJobId, secondDownloadJobId };
};

/**
 * Creates an install job and updates the necessary parameters for job creation,
 * dependencies, and target release.
 *
 * @param {*} jobCreationParams - Contains arrays to store job creation details.
 * @param {*} jobDependencyParams - Contains arrays to manage job dependencies.
 * @param {*} targetReleaseParams - Contains arrays to store release details.
 * @param {Number} targetId - The ID of the target device.
 * @param {*} jobEmbargoTime - Contains embargo and expiry times for the jobs.
 * @param {string} downloadJobId - The ID of the primary download job.
 * @param {string} secondDownloadJobId - The ID of the secondary download job, if applicable.
 * @param {boolean} isLeader - Indicates if the current target is a leader device.
 * @param {boolean} isEdgeBox - Indicates if the current target is an edge box device.
 * @param {boolean} isInstall - Indicates if the deploy action is install only.
 * @param {*} currentRolloutData - Contains rollout data.
 * @param {*} updateTargetReleaseParams - Contains arrays to store target_release details.
 * @returns {string} - The ID of the created install job.
 */
const createInstallJob = ({
  jobCreationParams,
  jobDependencyParams,
  targetReleaseParams,
  targetId,
  jobEmbargoTime,
  downloadJobId,
  secondDownloadJobId,
  isLeader,
  isEdgeBox,
  isInstall,
  currentRolloutData,
  updateTargetReleaseParams,
}) => {
  const { installJobEmbargoDate, jobExpiryDate } = jobEmbargoTime;

  const installJobId = uuid();

  jobCreationParams.deviceId.push(targetId);
  jobCreationParams.jobId.push(installJobId);
  jobCreationParams.jobEmbargoTime.push(installJobEmbargoDate);
  jobCreationParams.expiry.push(jobExpiryDate);
  jobCreationParams.type.push(jobType.ROLLOUT_INSTALL);

  jobDependencyParams.job_id.push(installJobId);
  jobDependencyParams.dependens_on.push(
    isLeader && !isEdgeBox && !isInstall ? secondDownloadJobId : downloadJobId
  );
  jobDependencyParams.continue_on_fail.push(false);

  if (isInstall) {
    updateTargetReleaseParams.targetReleaseIds.push(
      currentRolloutData?.data?.[targetId]?.targetReleaseId
    );
    updateTargetReleaseParams.installJobIds.push(installJobId);
  } else targetReleaseParams.installJobs.push(installJobId);

  return installJobId;
};

const updateTargetReleaseBatch = async (conn, updateTargetReleaseParams) => {
  const query = `
    UPDATE target_release
    SET
      schedule_start_time_renamed = $1,
      schedule_end_time_renamed = $2,
      install_job = data.install_job
    FROM (
      SELECT UNNEST($3::int4[]) AS target_release_id,
             UNNEST($4::uuid[]) AS install_job
    ) AS data
    WHERE target_release.target_release_id = data.target_release_id;
  `;

  const res = await conn.execute(query, [
    updateTargetReleaseParams.scheduleStartTime,
    updateTargetReleaseParams.scheduleEndTime,
    updateTargetReleaseParams.targetReleaseIds,
    updateTargetReleaseParams.installJobIds,
  ]);
  return res?.rowCount || null;
};

const createJobsBatch = (conn, jobCreationParams) =>
  conn.execute(
    `
        WITH JOB AS (
            INSERT INTO job
                (id, device_id, destination, type, data, status, embargo, expiry, created_by, created_on )
            SELECT * FROM UNNEST ($1::uuid[],$2::int4[],$3::varchar[],$4::varchar[],$5::text[],$6::int4[],$7::timestamptz[],$8::timestamptz[],$9::uuid[],$10::timestamptz[])
        )
        INSERT INTO job_status_history (job_id, status, message, created_at)
        SELECT * FROM UNNEST ($1::uuid[], $6::int4[], $11::text[], $10::timestamptz[]);
        `,
    jobCreationParams
  );

const selectRebootJobs = (conn, params) =>
  conn.execute(
    `
      WITH filtered_jobs AS (
        SELECT device_id, id, expiry, embargo, created_on
        FROM job
        WHERE device_id = ANY($1)
          AND type = $2
          AND status = $3
          AND created_on > $4
          AND embargo <= $5
      ),
      ranked_jobs AS (
        SELECT device_id, id, expiry, embargo,
          ROW_NUMBER() OVER(PARTITION BY device_id ORDER BY created_on DESC, embargo ASC) AS rn
        FROM filtered_jobs
      )
      SELECT device_id, id, expiry
      FROM ranked_jobs
      WHERE rn = 1;
  `,
    params
  );

const updateRebootJobsBatch = (conn, params) =>
  conn.execute(
    `
      UPDATE job
      SET embargo = $1
      WHERE id = ANY($2)
    `,
    params
  );

/**
 *
 *
 * @param {*} conn connection
 * @param {string[]} targetReleasParams
 * @returns {Promise<>}
 */
const createDeploymentsBatch = (conn, targetReleasParams) =>
  conn.execute(
    `
            INSERT INTO target_release
            (release_id,software_id,site_release_id,schedule_start_time_renamed,schedule_end_time_renamed,current_status_id,created_by,target_id,status_last_modified,created,download_job,install_job)
            SELECT * FROM UNNEST ($1::int4[],$2::int4[],$3::int4[],$4::timestamptz[],$5::timestamptz[],$6::int4[],$7::uuid[],$8::int4[],$9::timestamptz[],$10::timestamptz[],$11::uuid[],$12::uuid[]);
        `,
    targetReleasParams
  );

const createJobDependencyBatch = (conn, jobDependencyParams) =>
  conn.execute(
    `
        INSERT INTO job_dependency
            ( job_id, dependens_on, continue_on_fail )
        SELECT * FROM UNNEST ($1::uuid[], $2::uuid[], $3::bool[]); `,
    jobDependencyParams
  );

module.exports = {
  createJobDependencyBatch,
  createDeploymentsBatch,
  createJobsBatch,
  updateRebootJobsBatch,
  selectRebootJobs,
  createDownloadJob,
  createInstallJob,
  updateTargetReleaseBatch,
};
