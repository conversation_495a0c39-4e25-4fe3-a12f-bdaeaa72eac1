const { server } = require('../../../app');
const { jobStatus } = require('../../../helpers/job-helper');
const {
  targetReleaseStatus: TargetReleaseStatus,
  deployAction,
} = require('./rolloutConstants');
const deploymentHelper = require('./deploymentHelper');
const allAtOnceDeploymentPolicy = require('./allAtOnceDeploymentPolicy');
const deviceDependentDeploymentPolicy = require('./deviceDependentDeploymentPolicy');
const consecutiveDeploymentPolicy = require('./consecutiveDeploymentPolicy');
const downloadOnlyDeploymentAction = require('./downloadOnlyDeploymentAction');

const ALL_AT_ONCE_DEPLOYMENT = 'ALL_AT_ONCE_DEPLOYMENT';
const CONSECUTIVE_DEPLOPYMENT = 'CONSECUTIVE_DEPLOPYMENT';
const DEVICE_DEPENDENT_DEPLOYMENT = 'DEVICE_DEPENDENT_DEPLOYMENT';

const DEPLOYMENT_POLICY_MAP = {
  0: DEVICE_DEPENDENT_DEPLOYMENT,
  1: ALL_AT_ONCE_DEPLOYMENT,
  2: CONSECUTIVE_DEPLOPYMENT,
};

class DeploymentPolicy {
  constructor(targetsBySite, rollout, deploymentPolicyId) {
    this.targetsBySite = targetsBySite;
    this.rollout = rollout;
    this.server = server;
    this.deploymentPolicy = DEPLOYMENT_POLICY_MAP[deploymentPolicyId];
    this.deploymentAction = rollout.deployAction;
  }

  /**
   *
   *
   * @param {*} connection
   * @param {deploymentHelper.Software} software
   * @returns  {Promise<number>}
   * @memberof DeploymentPolicy
   */
  async createDeployments(connection, software, currentRolloutData = null) {
    const {
      dateTimeCreated: rolloutCreatedAt,
      scheduleStartTime: rolloutScheduleStartTime,
      scheduleEndTime: rolloutScheduleEndTime,
      releaseId,
      installFrequency,
      softwareId,
      createdBy,
      scheduleDownloadStartTime,
      scheduleDownloadEndTime,
    } = this.rollout;
    const isInstall = this.deploymentAction === deployAction.INSTALL;
    const jobEmbargoTime = deploymentHelper.decideJobEmbargoAndExpiration(
      rolloutCreatedAt,
      rolloutScheduleStartTime,
      rolloutScheduleEndTime,
      scheduleDownloadStartTime,
      scheduleDownloadEndTime
    );
    const targets = Object.values(this.targetsBySite)
      .flat()
      .filter(target => target.active);
    const { destination, jobData } =
      deploymentHelper.decideDestionationAndJobData(software);

    const targetReleaseParams = {
      releaseId: Array(targets.length).fill(releaseId),
      softwareId: Array(targets.length).fill(softwareId),
      siteReleaseId: Array(targets.length).fill(null),
      scheduleStartTime: Array(targets.length).fill(rolloutScheduleStartTime),
      scheduleEndTime: Array(targets.length).fill(rolloutScheduleEndTime),
      currentStatusId: Array(targets.length).fill(
        TargetReleaseStatus.SCHEDULED
      ),
      createdBy: Array(targets.length).fill(createdBy),
      targetIds: targets.map(target => target.target_id || target.targetId),
      statusLastModified: Array(targets.length).fill(new Date()),
      created: Array(targets.length).fill(new Date()),
      downloadJobs: [],
      installJobs: [],
    };

    const jobDependencyParams = {
      job_id: [],
      dependens_on: [],
      continue_on_fail: [],
    };

    const jobSize = deploymentHelper.getJobSize(targets, this.deploymentAction);

    const jobCreationParams = {
      jobId: [],
      deviceId: [],
      destination: Array(jobSize).fill(destination),
      type: [],
      jobData: Array(jobSize).fill(jobData),
      status: Array(jobSize).fill(jobStatus.NEW),
      jobEmbargoTime: [],
      expiry: [],
      createdBy: Array(jobSize).fill(createdBy),
      createdOn: Array(jobSize).fill(new Date()),
      message: Array(jobSize).fill('new job'),
    };

    const updateTargetReleaseParams = {
      targetReleaseIds: [],
      scheduleStartTime: rolloutScheduleStartTime,
      scheduleEndTime: rolloutScheduleEndTime,
      installJobIds: [],
    };

    const deploymentContext = {
      connection,
      targets,
      jobEmbargoTime,
      software,
      userId: createdBy,
      destination,
      jobData,
      targetReleaseParams,
      jobDependencyParams,
      jobCreationParams,
      updateTargetReleaseParams,
      isInstall,
      currentRolloutData,
    };

    try {
      switch (this.deploymentAction || this.deploymentPolicy) {
        case deployAction.DOWNLOAD:
          return downloadOnlyDeploymentAction.deploy(deploymentContext);
        case ALL_AT_ONCE_DEPLOYMENT:
          return allAtOnceDeploymentPolicy.deploy(deploymentContext);
        case CONSECUTIVE_DEPLOPYMENT: {
          return consecutiveDeploymentPolicy.deploy({
            ...deploymentContext,
            installFrequency,
          });
        }
        case DEVICE_DEPENDENT_DEPLOYMENT:
        default:
          return deviceDependentDeploymentPolicy.deploy(deploymentContext);
      }
    } catch (err) {
      this.server.log.error('Error when trying to create deployment');
      await connection.execute('ROLLBACK');
      throw err;
    }
  }
}

module.exports = DeploymentPolicy;
