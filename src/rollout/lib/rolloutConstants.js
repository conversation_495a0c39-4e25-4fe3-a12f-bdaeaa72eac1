const { roles } = require('../../../lib/app-constants');

const rolloutRoles = {
  ALLOW_GET: [
    roles.COMPANY_ADMIN,
    roles.USER,
    roles.POWER_USER,
    roles.ANALYST,
    roles.SPECIALIST,
  ],
  ALLOW_POST: [
    roles.COMPANY_ADMIN,
    roles.POWER_USER,
    roles.MEDIA_DEPLOYER,
    roles.ANALYST,
    roles.SPECIALIST,
  ],
  ALLOW_DEL: [
    roles.COMPANY_ADMIN,
    roles.POWER_USER,
    roles.MEDIA_DEPLOYER,
    roles.ANALYST,
    roles.SPECIALIST,
  ],
};

const errorMsgs = {
  rolloutNotFound: rolloutId => `Rollout with id ${rolloutId} not found`,
  rolloutAccessDenied: cancelOrDelete =>
    `Cannot ${cancelOrDelete} rollout. User and the rollout creator must belong to the same company`,
  siteAccessDeinedForDeletion:
    'Can not delete rollout, user does not have access to all sites',
  rolloutIncomplete: "The rollout hasn't completed or cancelled",
  roleDeniedForDeletion: "User can only delete 'media' rollouts",
  roleDeniedForCreation: "User can only create 'media' rollouts",
  softwareNotFound: 'Software not found',
  siteNotFound: siteId => `Site not found for ${siteId}`,
  siteNotAccessible: siteId => `No permission to access site ${siteId}`,
  rolloutNoDeviceConflict: 'No devices to rollout in the file download',
  targetsNotFound: targetId => `Target not found for ${targetId}`,
  targetPermissionDenied: target =>
    `No permission to access device ${target.name} ${target.targetId}`,
  siteRestrictedForFileDownloads: siteId =>
    `This site is restricted for file download ${siteId}`,
};

const targetReleaseStatus = {
  DELIVERY_PENDING: 1,
  DELIVERY_FAILED: 2,
  DELIVERY: 3,
  SCHEDULED: 4,
  CANCELLED: 5,
  SUCCESSFUL: 6,
  FAILED_VALIDATION: 7,
  DOWNLOAD_COMPLETE: 8,
  IN_PROGRESS: 9,
  FAILED_UPGRADE: 10,
  EXPIRED: 11,
};

const constants = {
  TENANTS: 'tenants',
  FILE_DOWNLOADS: 'file_downloads',
  SITE_LIMIT: 'SiteLimit',
  DEFAULT_SITE_LIMIT: 100,
  TARGET_LIMIT: 'TargetLimit',
  DEFAULT_TARGET_LIMIT: 500,
};

const deployAction = {
  DOWNLOAD: 'download',
  DOWNLOAD_AND_INSTALL: 'downloadAndInstall',
  INSTALL: 'install',
};

module.exports = {
  targetReleaseStatus,
  rolloutRoles,
  errorMsgs,
  constants,
  deployAction,
};
