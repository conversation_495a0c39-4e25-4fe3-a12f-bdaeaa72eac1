const moment = require('moment');

const deployment = require('../models/deployment.model');
const logger = require('../../../lib/logger').mainLogger();
const { decideLeaderJobEmbargo } = require('./deploymentHelper');

const consecutiveDeploymentEmbargo = (
  firstJobEmbargo,
  siteDeployment,
  installFrequency
) => {
  const { installJobEmbargoDate } = firstJobEmbargo;
  const currentTargetInstallTime = moment(installJobEmbargoDate).add(
    installFrequency * siteDeployment,
    'milliseconds'
  );

  return {
    ...firstJobEmbargo,
    installJobEmbargoDate: currentTargetInstallTime,
  };
};

module.exports = {
  /**
   *
   *
   * @param {*} deploymentContext
   * @returns
   */
  async deploy(deploymentContext) {
    let totalDeployment = 0;
    let updateCount = 0;
    const {
      connection,
      jobEmbargoTime: firstJobEmbargo,
      targets,
      targetReleaseParams,
      jobDependencyParams,
      jobCreationParams,
      installFrequency,
      isInstall,
      currentRolloutData,
      updateTargetReleaseParams,
    } = deploymentContext;

    if (targets.length === 0) return { totalDeployment, updateCount };

    try {
      let currentSite = null;
      let siteDeploymentCount = 0;

      let siteLeaderDownloadJobs = [];

      for (let i = 0; i < targets.length; i++) {
        const target = targets[i];
        const targetId = target.target_id || target.targetId;

        if (currentSite && target.site_id !== currentSite) {
          siteDeploymentCount = 0;
          siteLeaderDownloadJobs = [];
        }

        const isLeader = siteDeploymentCount < 2;

        const jobEmbargo = isLeader
          ? decideLeaderJobEmbargo(
              firstJobEmbargo,
              siteDeploymentCount,
              installFrequency
            )
          : consecutiveDeploymentEmbargo(
              firstJobEmbargo,
              siteDeploymentCount,
              installFrequency
            );

        const commonParams = {
          jobCreationParams,
          jobDependencyParams,
          targetReleaseParams,
          targetId,
          jobEmbargoTime: jobEmbargo,
          siteLeaderDownloadJobs,
          isLeader,
          isEdgeBox: target.is_edge_box,
          isInstall,
          updateTargetReleaseParams,
          currentRolloutData,
        };

        const { downloadJobId, secondDownloadJobId } = isInstall
          ? {
              downloadJobId:
                currentRolloutData?.data?.[targetId]?.downloadJobId,
            }
          : deployment.createDownloadJob(commonParams);

        deployment.createInstallJob({
          ...commonParams,
          downloadJobId,
          secondDownloadJobId,
        });

        siteDeploymentCount += 1;
        totalDeployment += 1;
        currentSite = target.site_id;
      }

      await deployment.createJobsBatch(
        connection,
        Object.values(jobCreationParams)
      );

      const operations = [];
      const jobDependencyCreation = deployment.createJobDependencyBatch(
        connection,
        Object.values(jobDependencyParams)
      );
      operations.push(jobDependencyCreation);

      if (isInstall) {
        const updateTargetRelease = deployment.updateTargetReleaseBatch(
          connection,
          updateTargetReleaseParams
        );
        operations.push(updateTargetRelease);
      } else {
        const deploymentCreation = deployment.createDeploymentsBatch(
          connection,
          Object.values(targetReleaseParams)
        );
        operations.push(deploymentCreation);
      }

      const results = await Promise.all(operations);

      if (isInstall) {
        [, updateCount] = results;
      }

      return { totalDeployment, updateCount };
    } catch (err) {
      logger.error(
        'Error when trying to create deployment with consecutive deployment policy'
      );
      await connection.execute('ROLLBACK');
      throw err;
    }
  },
};
