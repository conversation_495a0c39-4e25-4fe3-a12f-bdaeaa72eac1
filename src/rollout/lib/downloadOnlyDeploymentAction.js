const deployment = require('../models/deployment.model');
const logger = require('../../../lib/logger').mainLogger();
const { decideLeaderJobEmbargo } = require('./deploymentHelper');

module.exports = {
  /**
   * @param {{ connection: any;
   * targets: any;
   * jobEmbargoTime: any;
   * software?: import("./deploymentHelper").Software;
   * userId?: any;
   * destination?: string;
   * jobData?: import("./deploymentHelper").JobData;
   * targetReleaseParams: any;
   * jobDependencyParams: any;
   * jobCreationParams: any;
   * }} deploymentContext
   */
  async deploy(deploymentContext) {
    let totalDeployment = 0;
    const {
      connection,
      jobEmbargoTime,
      targets,
      targetReleaseParams,
      jobDependencyParams,
      jobCreationParams,
    } = deploymentContext;

    let currentSite = null;
    let siteDeploymentCount = 0;
    let siteLeaderDownloadJobs = [];
    if (targets.length === 0) return totalDeployment;

    try {
      for (let i = 0; i < targets.length; i++) {
        const target = targets[i];
        const targetId = target.target_id || target.targetId;

        if (currentSite && target.site_id !== currentSite) {
          siteDeploymentCount = 0;
          siteLeaderDownloadJobs = [];
        }
        const isLeader = siteDeploymentCount < 2;
        const jobEmbargo = isLeader
          ? decideLeaderJobEmbargo(jobEmbargoTime, siteDeploymentCount)
          : jobEmbargoTime;

        deployment.createDownloadJob({
          jobCreationParams,
          jobDependencyParams,
          targetReleaseParams,
          targetId,
          jobEmbargoTime: jobEmbargo,
          siteLeaderDownloadJobs,
          isLeader,
          isEdgeBox: target.is_edge_box,
        });

        currentSite = target.site_id;
        siteDeploymentCount += 1;
        totalDeployment += 1;
      }

      await deployment.createJobsBatch(
        connection,
        Object.values(jobCreationParams)
      );
      const jobDependencyCreation = deployment.createJobDependencyBatch(
        connection,
        Object.values(jobDependencyParams)
      );
      const deploymentCreation = deployment.createDeploymentsBatch(
        connection,
        Object.values(targetReleaseParams)
      );

      await Promise.all([jobDependencyCreation, deploymentCreation]);
    } catch (err) {
      logger.error(
        'Error when trying to create deployment in all at once deployment policy'
      );
      await connection.execute('ROLLBACK');
      throw err;
    }
    return { totalDeployment };
  },
};
