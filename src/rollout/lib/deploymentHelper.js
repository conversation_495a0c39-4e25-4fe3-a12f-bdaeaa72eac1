const moment = require('moment');
const logger = require('../../../lib/logger').mainLogger();
const { SHA256 } = require('../../../lib/app-constants').crypto;
const { deployAction } = require('./rolloutConstants');

/**
 * @typedef {Object} Job
 * @property {string} id
 * @property {Number} deviceId
 * @property {string} destination
 * @property {string} type
 * @property {Object} data
 * @property {number} status
 * @property {Date} expiration
 * @property {Date} embargo
 * @property {string} createdBy
 * @property {Date} createdOn
 */
/**
 * @typedef {Object} Software
 * @property {string} version
 * @property {string} softwareFileUrl
 * @property {string} softwareFileSignature
 * @property {string} softwareFile
 * @property {Number} size
 */
/**
 * @typedef {Object} JobData
 * @property {string} version
 * @property {string} fileURI
 * @property {string} fileHash
 * @property {string} fileName
 * @property {string} fileHashAlg
 * @property {Number} fileSize
 */
/**
 * @typedef {Object} JobEmbargoTime
 * @property {Date} downloadJobEmbargoDate
 * @property {Date} installJobEmbargoDate
 * @property {Date} jobExpiryDate
 */

/**
 *
 *
 * @param {Date} rolloutCreatedAt
 * @param {Date} rolloutScheduleStartTime
 * @param {Date} rolloutScheduleEndTime
 * @returns {JobEmbargoTime}
 */
const decideJobEmbargoAndExpiration = (
  rolloutCreatedAt,
  rolloutScheduleStartTime,
  rolloutScheduleEndTime,
  scheduleDownloadStartTime,
  scheduleDownloadEndTime
) => {
  const jobExpiryDate = rolloutScheduleEndTime || scheduleDownloadEndTime;
  return {
    downloadJobEmbargoDate: scheduleDownloadStartTime,
    installJobEmbargoDate: rolloutScheduleStartTime,
    jobExpiryDate,
  };
};

/**
 *
 *
 * @param {string} url
 * @returns string
 */

const rewriteUrl = url => {
  let newURL = url;
  if (!url) return null;

  // Enforce the url to be HTTP @See https://jira.invenco.com/browse/ICS-10124
  newURL = url.replace(/^https:\/\//i, 'http://');

  const tokenizer = 'Software%20Release';
  const tokens = newURL.split(tokenizer);
  logger.info(`Rewriting url: ${newURL}`);

  if (tokens != null && tokens.length === 2) {
    return `${tokens[0]}softwarerelease/${tokenizer}${tokens[1]}`;
  }
  return newURL;
};

/**
 *
 *
 * @param {Software} software
 * @returns string
 */
const decideDestinationSafely = software => {
  let result = 'invenco.system';
  const softwareFileName = software.softwareFile;
  if (softwareFileName && softwareFileName.toLowerCase().endsWith('.jbz')) {
    result = 'invenco.icsagent';
  }
  return result;
};

/**
 *
 *
 * @param {Software} software
 * @returns {{jobData: JobData, destination: string}}
 */
const decideDestionationAndJobData = software => {
  const destination = decideDestinationSafely(software);
  const jobData = {
    version: software.version || 1,
    fileURI: rewriteUrl(software.softwareFileUrl),
    fileHash: software.softwareFileSignature,
    fileName: software.softwareFile,
    fileHashAlg: SHA256,
    fileSize: software.size,
  };

  return {
    destination,
    jobData,
  };
};

/**
 *
 *
 * @param {JobEmbargoTime} firstJobEmbargo
 * @param {Number} siteDeployment
 * @param {Number?} installFrequency
 * @returns
 */
const decideLeaderJobEmbargo = (
  firstJobEmbargo,
  siteDeployment,
  installFrequency = null
) => {
  const { downloadJobEmbargoDate, installJobEmbargoDate, jobExpiryDate } =
    firstJobEmbargo;
  const currentDownloadEmbargoTime = moment(downloadJobEmbargoDate).add(
    2 * siteDeployment,
    'minutes'
  );
  const currentDownloadJobExpiryDate = moment.min(
    moment(currentDownloadEmbargoTime).add(2, 'hours'),
    moment(jobExpiryDate)
  );
  const currentInstallEmbargoTime = installFrequency
    ? moment(installJobEmbargoDate).add(
        installFrequency * siteDeployment,
        'milliseconds'
      )
    : installJobEmbargoDate;

  return {
    downloadJobEmbargoDate: currentDownloadEmbargoTime,
    installJobEmbargoDate: currentInstallEmbargoTime,
    downloadJobExpiryDate: currentDownloadJobExpiryDate,
    jobExpiryDate,
  };
};

const getJobSize = (targets, action) => {
  if (action === deployAction.INSTALL) {
    return targets.length;
  }

  return targets.reduce(
    (acc, { site_id: siteId, is_edge_box: isEdgeBox }) => {
      if (!acc[siteId]) acc[siteId] = 0;
      acc[siteId] += 1;

      if (action === deployAction.DOWNLOAD) {
        if (isEdgeBox) {
          acc.jobSize += 1;
        } else {
          acc.jobSize += acc[siteId] > 2 ? 1 : 2;
        }
      } else if (isEdgeBox) {
        acc.jobSize += 2;
      } else {
        acc.jobSize += acc[siteId] > 2 ? 2 : 3;
      }

      return acc;
    },
    { jobSize: 0 }
  ).jobSize;
};

module.exports = {
  decideJobEmbargoAndExpiration,
  rewriteUrl,
  decideDestionationAndJobData,
  decideLeaderJobEmbargo,
  decideDestinationSafely,
  getJobSize,
};
