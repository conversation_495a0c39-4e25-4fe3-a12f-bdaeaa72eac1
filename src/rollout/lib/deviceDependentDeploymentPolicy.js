const logger = require('../../../lib/logger').mainLogger();
const deployment = require('../models/deployment.model');
const { decideLeaderJobEmbargo } = require('./deploymentHelper');

module.exports = {
  async deploy(deploymentContext) {
    let totalDeployment = 0;
    let updateCount = 0;
    const {
      connection,
      jobEmbargoTime,
      targets,
      targetReleaseParams,
      jobDependencyParams,
      jobCreationParams,
      isInstall,
      currentRolloutData,
      updateTargetReleaseParams,
    } = deploymentContext;
    if (targets.length === 0) return { totalDeployment, updateCount };

    let previousInstallJobId = null;

    try {
      let currentSite = null;
      let siteDeploymentCount = 0;
      let siteLeaderDownloadJobs = [];

      for (let i = 0; i < targets.length; i++) {
        const target = targets[i];
        const targetId = target.target_id || target.targetId;

        if (currentSite && target.site_id !== currentSite) {
          siteDeploymentCount = 0;
          siteLeaderDownloadJobs = [];
        }

        const isLeader = siteDeploymentCount < 2;
        const jobEmbargo = isLeader
          ? decideLeaderJobEmbargo(jobEmbargoTime, siteDeploymentCount)
          : jobEmbargoTime;

        const commonParams = {
          jobCreationParams,
          jobDependencyParams,
          targetReleaseParams,
          targetId,
          jobEmbargoTime: jobEmbargo,
          siteLeaderDownloadJobs,
          isLeader,
          isEdgeBox: target.is_edge_box,
          isInstall,
          updateTargetReleaseParams,
          currentRolloutData,
        };

        const { downloadJobId, secondDownloadJobId } = isInstall
          ? {
              downloadJobId:
                currentRolloutData?.data?.[targetId]?.downloadJobId,
            }
          : deployment.createDownloadJob(commonParams);

        const currentInstallJobId = deployment.createInstallJob({
          ...commonParams,
          downloadJobId,
          secondDownloadJobId,
        });
        if (!isLeader && target.site_id === currentSite) {
          jobDependencyParams.job_id.push(currentInstallJobId);
          jobDependencyParams.dependens_on.push(previousInstallJobId);
          jobDependencyParams.continue_on_fail.push(false);
        }

        if (siteDeploymentCount === 0) {
          logger.info(
            `create download job ${downloadJobId} for first device ${target.name}`
          );
          logger.info(
            `create install job ${currentInstallJobId} for first device ${target.name} that depends on ${downloadJobId}`
          );
        } else {
          logger.info(
            `create install job ${currentInstallJobId} for chained device ${target.name}\
                        that depends on download job ${downloadJobId} and previous install job ${previousInstallJobId}`
          );
        }

        previousInstallJobId = currentInstallJobId;

        currentSite = target.site_id;
        siteDeploymentCount += 1;
        totalDeployment += 1;
      }

      await deployment.createJobsBatch(
        connection,
        Object.values(jobCreationParams)
      );

      const operations = [];
      const jobDependencyCreation = deployment.createJobDependencyBatch(
        connection,
        Object.values(jobDependencyParams)
      );
      operations.push(jobDependencyCreation);

      if (isInstall) {
        const updateTargetRelease = deployment.updateTargetReleaseBatch(
          connection,
          updateTargetReleaseParams
        );
        operations.push(updateTargetRelease);
      } else {
        const deploymentCreation = deployment.createDeploymentsBatch(
          connection,
          Object.values(targetReleaseParams)
        );
        operations.push(deploymentCreation);
      }

      const results = await Promise.all(operations);

      if (isInstall) {
        [, updateCount] = results;
      }

      return { totalDeployment, updateCount };
    } catch (err) {
      logger.error(
        'Error when trying to create deployment with device dependent policy'
      );
      await connection.execute('ROLLBACK');
      connection.done();
      throw err;
    }
  },
};
