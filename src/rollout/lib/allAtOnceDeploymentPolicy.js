const deployment = require('../models/deployment.model');
const logger = require('../../../lib/logger').mainLogger();
const { decideLeaderJobEmbargo } = require('./deploymentHelper');

module.exports = {
  /**
   * @param {{ connection: any;
   * targets: any;
   * jobEmbargoTime: any;
   * software?: import("./deploymentHelper").Software;
   * userId?: any;
   * destination?: string;
   * jobData?: import("./deploymentHelper").JobData;
   * targetReleaseParams: any;
   * jobDependencyParams: any;
   * jobCreationParams: any;
   * }} deploymentContext
   */
  async deploy(deploymentContext) {
    let totalDeployment = 0;
    let updateCount = 0;
    const {
      connection,
      jobEmbargoTime,
      targets,
      targetReleaseParams,
      jobDependencyParams,
      jobCreationParams,
      isInstall,
      currentRolloutData,
      updateTargetReleaseParams,
    } = deploymentContext;

    if (targets.length === 0) return { totalDeployment, updateCount };
    let currentSite = null;
    let siteDeploymentCount = 0;
    let siteLeaderDownloadJobs = [];

    try {
      for (let i = 0; i < targets.length; i++) {
        const target = targets[i];
        const targetId = target.target_id || target.targetId;

        if (currentSite && target.site_id !== currentSite) {
          siteDeploymentCount = 0;
          siteLeaderDownloadJobs = [];
        }
        const isLeader = siteDeploymentCount < 2;
        const jobEmbargo = isLeader
          ? decideLeaderJobEmbargo(jobEmbargoTime, siteDeploymentCount)
          : jobEmbargoTime;

        const commonParams = {
          jobCreationParams,
          jobDependencyParams,
          targetReleaseParams,
          targetId,
          jobEmbargoTime: jobEmbargo,
          siteLeaderDownloadJobs,
          isLeader,
          isEdgeBox: target.is_edge_box,
          isInstall,
          updateTargetReleaseParams,
          currentRolloutData,
        };

        const { downloadJobId, secondDownloadJobId } = isInstall
          ? {
              downloadJobId:
                currentRolloutData?.data?.[targetId]?.downloadJobId,
            }
          : deployment.createDownloadJob(commonParams);

        deployment.createInstallJob({
          ...commonParams,
          downloadJobId,
          secondDownloadJobId,
        });

        currentSite = target.site_id;
        siteDeploymentCount += 1;
        totalDeployment += 1;
      }

      await deployment.createJobsBatch(
        connection,
        Object.values(jobCreationParams)
      );

      const operations = [];
      const jobDependencyCreation = deployment.createJobDependencyBatch(
        connection,
        Object.values(jobDependencyParams)
      );
      operations.push(jobDependencyCreation);

      if (isInstall) {
        const updateTargetRelease = deployment.updateTargetReleaseBatch(
          connection,
          updateTargetReleaseParams
        );
        operations.push(updateTargetRelease);
      } else {
        const deploymentCreation = deployment.createDeploymentsBatch(
          connection,
          Object.values(targetReleaseParams)
        );
        operations.push(deploymentCreation);
      }

      const results = await Promise.all(operations);

      if (isInstall) {
        [, updateCount] = results;
      }
    } catch (err) {
      logger.error(
        'Error when trying to create deployment in all at once deployment policy'
      );
      await connection.execute('ROLLBACK');
      throw err;
    }
    return { totalDeployment, updateCount };
  },
};
