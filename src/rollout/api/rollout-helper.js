const moment = require('moment');
const restify = require('restify');
const _ = require('lodash');
const errors = require('restify-errors');

const logger = require('../../../lib/logger').mainLogger();
const { errorMsgs } = require('../lib/rolloutConstants');
const { server } = require('../../../app');
const { constants, deployAction } = require('../lib/rolloutConstants');
const {
  supportedEntityTypesInternalCode,
} = require('../../entities/api/entity/settings-constants');

const {
  getEntitySettingService,
} = require('../../entities/api/entity/settings/setting.service');

const PRESENT = 'PRESENT';

const rolloutStatusHasCompleted = rollout => {
  const total = rollout.totalDeploymentsCount;
  const successCount = rollout.deploymentsCompleteCount;
  const failureCount = rollout.deploymentsFailCount;
  const cancelCount = rollout.deploymentsCancelCount;

  return (
    total === successCount ||
    total === failureCount ||
    total === cancelCount ||
    total === successCount + failureCount + cancelCount
  );
};

const setDeviceDisplayName = (companySpecificDisplayName, deployments) =>
  deployments.map(deployment => {
    const overrideDeviceType = {
      ...deployment.target.deviceType,
      name: companySpecificDisplayName,
    };
    const target = { ...deployment.target, deviceType: overrideDeviceType };
    return { ...deployment, target };
  });

/**
 *
 * Check whether the device exists and user have correct authorization to access
 *
 * @param {String[]} siteIds
 * @param {{string: string }} sitesAllowed
 * @param {{string: {active: boolean}}} sitesFound
 */
const checkSiteAccess = (siteIds, sitesAllowed, sitesFound, type) => {
  for (let i = 0; i < siteIds.length; i++) {
    const siteId = siteIds[i];
    logger.info(`add rollout by site processing site id ${siteId}`);
    const site = sitesFound && sitesFound[siteId];
    const isSiteActive = site && site.active;

    if (!site || !isSiteActive)
      throw new restify.NotFoundError(errorMsgs.siteNotFound(siteId));
    if (!sitesAllowed[siteId])
      throw new restify.ForbiddenError(errorMsgs.siteNotAccessible(siteId));
    if (type === 'software' && site.disable_software_downloads)
      throw new restify.ForbiddenError(
        errorMsgs.siteRestrictedForFileDownloads(siteId)
      );
  }
};

/**
 *
 *
 * @param {Number[]} deviceIds
 * @param {{string: {active: boolean}}} devicesFound
 * @param {{string: {active: boolean}}} sitesAllowed
 * @param {*} currentDevices
 * @param {*} deviceTypeMap
 * @param {*} devicesToDeployForTheRollout
 * @returns
 */
const checkDeviceAccess = (
  deviceIds,
  devicesFound,
  sitesAllowed,
  currentDevices,
  deviceTypeMap,
  devicesToDeployForTheRollout
) => {
  // map with siteId as key and arrayof device id as value
  const devices = new Map([...devicesToDeployForTheRollout]);
  for (let i = 0; i < deviceIds.length; i++) {
    const deviceId = deviceIds[i];
    logger.info(
      `add rollout by devices detected processing device id ${deviceId}`
    );
    const deviceExists = currentDevices.indexOf(Number(deviceId)) !== -1;

    if (!deviceExists) {
      const device = devicesFound && devicesFound[deviceId];
      if (!device || !device.active)
        throw new restify.NotFoundError(errorMsgs.targetsNotFound(deviceId));
      if (device.presence === PRESENT) {
        logger.debug('device exists. Checking permission...');
        const siteId = device.site_id;
        if (!sitesAllowed[siteId]) {
          throw new restify.ForbiddenError(
            errorMsgs.targetPermissionDenied(device)
          );
        }
        logger.debug(`check product type: ${deviceTypeMap}`);
        if (device.device_type === deviceTypeMap) {
          logger.info(`cache device ${deviceId} to site ${siteId}`);
          const tmpTargets = devices.has(siteId) ? devices.get(siteId) : [];
          tmpTargets.push(device);
          devices.set(siteId, tmpTargets);
        }
      }
    }
  }
  return devices;
};

/**
 * @typedef {Object} Result
 * @property {Number} id
 * @property {String} name
 * @property {String} description
 * @property {Number} startTime
 * @property {Number} endTime
 * @property {Number} installWindow
 * @property {Number} totalDeploymentsCount
 * @property {Number} deploymentsCompleteCount
 * @property {Number} deploymentsFailCount
 * @property {Number} deploymentsCancelCount
 * @property {Number} deploymentsInProgressCount
 * @property {Number} deploymentsPendingDownloadCount
 * @property {Number} deploymentsPendingInstallCount
 * @property {Boolean} cancelled
 * @property {Boolean} deleted
 * @property {String} type
 */
/**
 *
 *
 * @param {*} resultData
 * @param {*} relatedInfo
 * @param {boolean} [verbose=false]
 * @returns {Result[]|Result}
 */
const enrichResults = (
  resultData,
  relatedInfo,
  verbose = false,
  getOne = false
) => {
  const enrichedResults = _.map(resultData, result => {
    const status =
      result.deployAction === deployAction.DOWNLOAD
        ? result.downloadOnlyStatus
        : result.status;

    const deploymentStatusCount = {
      deploymentsCancelCount:
        status?.[server.constants.job.STATUS.CANCELLED] || 0,
      deploymentsCompleteCount:
        status?.[server.constants.job.STATUS.COMPLETED] || 0,
      deploymentsFailCount: status?.[server.constants.job.STATUS.FAILED] || 0,
      deploymentsInProgressCount:
        status?.[server.constants.job.STATUS.IN_PROGRESS] || 0,
      deploymentsPendingDownloadCount:
        result.fileDownloadStatus.pendingDownload || 0,
      deploymentsPendingInstallCount:
        result.fileDownloadStatus.pendingInstall || 0,
      totalDeploymentsCount: result.fileDownloadStatus.total || 0,
    };

    const downloadStatusCount = {
      fileDownloadCancelCount: result.fileDownloadStatus.cancelled || 0,
      fileDownloadFailCount: result.fileDownloadStatus.failed || 0,
      fileDownloadTotalCount: result.fileDownloadStatus.total || 0,
    };

    const enrichedResult = {
      ...result,
      ...deploymentStatusCount,
      ...downloadStatusCount,
    };

    delete enrichedResult.status;
    delete enrichedResult.downloadOnlyStatus;

    delete enrichedResult.downloadJobs;
    delete enrichedResult.installJobs;
    delete enrichedResult.fileDownloadStatus;
    delete enrichedResult.targetReleases;

    // Remove unneeded attributes, this is only used in GET /rollouts
    // endpoint
    if (!verbose) {
      delete enrichedResult.fileDownloadCancelCount;
      delete enrichedResult.fileDownloadFailCount;
      delete enrichedResult.fileDownloadTotalCount;

      delete enrichedResult.deploymentsPendingDownloadCount;
      delete enrichedResult.deploymentsPendingInstallCount;
    }
    const info = _.find(relatedInfo, { id: result.id });
    return _.assign({}, info, enrichedResult);
  });

  if (!enrichedResults.length) return null;
  if (getOne) return enrichedResults[0];
  return enrichedResults;
};

const calculateScheduleEndtTimeByDeploymentPolicy = (
  deploymentPolicyId,
  startTime,
  installWindow
) =>
  deploymentPolicyId === 2
    ? moment(startTime).add('3', 'month').toDate()
    : moment(startTime).add(installWindow, 'ms').toDate();

const calculateDownloadStartAndEndTime = (
  downloadStartTime,
  downloadWindow,
  rolloutScheduleStartTime,
  scheduleEndTime,
  createdAt
) => {
  const scheduleDownloadStartTime = downloadStartTime
    ? moment(downloadStartTime)
    : moment.max(
        moment(rolloutScheduleStartTime).subtract(48, 'h'),
        moment(createdAt)
      );

  const scheduleDownloadEndTime =
    downloadStartTime && downloadWindow
      ? moment(downloadStartTime).add(downloadWindow, 'ms')
      : scheduleEndTime;

  return {
    scheduleDownloadStartTime,
    scheduleDownloadEndTime,
  };
};

const rolloutDtoFromReq = (req, userId, userEmail) => {
  const {
    softwareId,
    startTime,
    installWindow,
    type,
    name,
    description,
    deploymentPolicy,
    retry = false,
    installFrequency = 0,
    downloadStartTime,
    downloadWindow,
    deploymentAction,
  } = req.body;
  const deploymentPolicyId =
    deploymentPolicy || (deploymentAction === deployAction.DOWNLOAD ? null : 0);
  const createdAt = new Date();
  const scheduleEndTime =
    startTime && installWindow
      ? calculateScheduleEndtTimeByDeploymentPolicy(
          deploymentPolicyId,
          startTime,
          installWindow
        )
      : null;

  const { scheduleDownloadStartTime, scheduleDownloadEndTime } =
    calculateDownloadStartAndEndTime(
      downloadStartTime,
      downloadWindow,
      startTime,
      scheduleEndTime,
      createdAt
    );

  return {
    createdAt,
    active: true,
    name,
    rollout: true,
    scheduleStartTime: startTime ? new Date(startTime) : null,
    scheduleEndTime,
    description,
    install_window: installWindow,
    retry,
    kc_user: userEmail,
    software_id: softwareId,
    cancelled: false,
    deleted: false,
    created_by: userId,
    last_modified_by: userId,
    cancelled_by: null,
    cancel_time: null,
    deploy_policy_id: deploymentPolicyId,
    type,
    install_frequency: installFrequency,
    scheduleDownloadStartTime,
    scheduleDownloadEndTime,
    downloadWindow,
    deploymentAction,
  };
};

const allowModifySoftwareRolloutRoles = userRoles =>
  userRoles.some(
    role =>
      role === 'POWER_USER' ||
      role === 'COMPANY_ADMIN' ||
      role === 'ANALYST' ||
      role === 'SPECIALIST'
  );

const generateParamsAndCondition = (
  userId,
  companyId,
  nameParam,
  typeParam,
  deviceParam
) => {
  const params = [userId, companyId];
  let name;
  if (nameParam) {
    params.push(`%${nameParam.toLowerCase()}%`);
    name = `AND LOWER(r.name) LIKE $${params.length}`;
  }

  let type;
  if (typeParam) {
    params.push(typeParam);
    type = `AND r.type = $${params.length}`;
  }

  let deviceType;
  if (deviceParam) {
    deviceType = `AND ((sw.sub_device_type = '${deviceParam}') or (sw.device_type = '${deviceParam}'))`;
  }

  const conditions = `
        ${type || ''}
        ${name || ''}
        ${deviceType || ''}
    `;

  return {
    params,
    conditions,
  };
};

const filterDevices = (devicesToDeployForTheRollout, targetsFound) =>
  new Map(
    [...devicesToDeployForTheRollout.entries()].map(([siteId, devices]) => [
      siteId,
      devices.filter(device => targetsFound[device.target_id]),
    ])
  );

const checkDualDisplay = async (siteTargetIds, targetIds) => {
  const dualDeviceTargetIds = [];
  const QUERY = `SELECT target_id FROM device_profile WHERE target_id=$1 AND aux_display_resolution is not NULL`;
  siteTargetIds.forEach(async tid => {
    const target = await server.db.read.row(QUERY, [tid]);
    if (target?.targetId) dualDeviceTargetIds.push(target.targetId);
  });
  if (dualDeviceTargetIds.length > 0) {
    const commonElements = dualDeviceTargetIds.reduce((acc, element) => {
      if (targetIds.includes(element)) {
        acc.push(element);
      }
      return acc;
    }, []);
    if (commonElements.length > 0) return dualDeviceTargetIds;

    const distinctTIds = siteTargetIds.reduce((acc, element) => {
      if (!dualDeviceTargetIds.includes(element)) {
        acc.push(element);
      }
      return acc;
    }, []);
    return distinctTIds;
  }
  return dualDeviceTargetIds;
};

const getRolloutSiteTargetLimit = async req => {
  const entityType = constants.TENANTS;
  const tenantId = req.user.company.id;
  const params = {
    entityType,
    entityId: tenantId,
    pageSize: 40,
    pageIndex: 0,
    allSites: true,
    fields: [
      'entitySettingsId',
      'entitySettingsDefinitionsId',
      'entityId',
      'entityType',
      'entityValue',
      'featureName',
      'settingName',
    ],
    sort: ['featureName:asc', 'settingName:asc'],
    filters: {
      entityId: { $eq: tenantId },
      entityType: { $eq: supportedEntityTypesInternalCode[entityType] },
      deleted: { $eq: false },
      featureName: { $eq: constants.FILE_DOWNLOADS },
    },
  };

  let data;
  try {
    const result = await getEntitySettingService(params);
    data = result.results;
  } catch (error) {
    logger.error(error);
    throw new errors.NotFoundError(
      `Error in fetching entity settings for tenant Id: ${tenantId}.`
    );
  }

  let siteLimit;
  let targetLimit;

  if (data.length) {
    siteLimit = data.find(
      item =>
        item.entityId === tenantId &&
        item.featureName === constants.FILE_DOWNLOADS &&
        item.settingName === constants.SITE_LIMIT
    );

    targetLimit = data.find(
      item =>
        item.entityId === tenantId &&
        item.featureName === constants.FILE_DOWNLOADS &&
        item.settingName === constants.TARGET_LIMIT
    );
  }

  return {
    siteLimit: siteLimit
      ? JSON.parse(siteLimit.entityValue).siteLimit
      : constants.DEFAULT_SITE_LIMIT,
    targetLimit: targetLimit
      ? JSON.parse(targetLimit.entityValue).targetLimit
      : constants.DEFAULT_TARGET_LIMIT,
  };
};

module.exports = {
  enrichResults,
  generateParamsAndCondition,
  rolloutDtoFromReq,
  calculateScheduleEndtTimeByDeploymentPolicy,
  checkSiteAccess,
  checkDeviceAccess,
  allowModifySoftwareRolloutRoles,
  setDeviceDisplayName,
  rolloutStatusHasCompleted,
  filterDevices,
  getRolloutSiteTargetLimit,
  checkDualDisplay,
  calculateDownloadStartAndEndTime,
};
