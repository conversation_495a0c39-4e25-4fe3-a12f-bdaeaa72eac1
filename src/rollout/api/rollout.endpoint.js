const Joi = require('joi');

const { rolloutRoles } = require('../lib/rolloutConstants');
const { positiveInteger } = require('../../../lib/app-constants');
const { server } = require('../../../app');
const { requiresRole } = require('../../../lib/pre');
const { config } = require('../../../env');
const {
  validateQuery,
  validateParams,
  validateBody,
} = require('../../../lib/pre');
const { deployAction } = require('../lib/rolloutConstants');
const rollout = require('./rollout.handler');

const BASE_PATH = config.base;

server.del(
  {
    path: `${BASE_PATH}/rollouts/:rolloutId`,
    version: '1.0.0',
  },
  [
    requiresRole(rolloutRoles.ALLOW_DEL),
    validateParams({
      rolloutId: Joi.number()
        .integer()
        .min(positiveInteger.MIN_VALUE)
        .max(positiveInteger.MAX_VALUE),
    }),
    rollout.deleteRolloutById,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/rollouts/:rolloutId`,
    version: '1.0.0',
  },
  [
    requiresRole(rolloutRoles.ALLOW_GET),
    validateParams({
      rolloutId: Joi.number().integer().min(positiveInteger.MIN_VALUE),
    }),
    rollout.getRolloutById,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/rollouts`,
    version: '1.0.0',
  },
  [
    requiresRole(rolloutRoles.ALLOW_GET),
    validateQuery({
      name: Joi.string().optional(),
      type: Joi.string().valid(['software', 'media']).optional(),
      pageSize: Joi.number().min(-1).optional(),
      pageIndex: Joi.number().min(0).optional(),
      deviceType: Joi.string()
        // eslint-disable-next-line no-useless-escape
        .regex(/^[a-zA-Z0-9\-]*$/)
        .optional(),
      status: Joi.string()
        // eslint-disable-next-line no-useless-escape
        .regex(/^[a-zA-Z0-9\-]*$/)
        .optional(),
    }),
    rollout.getRollouts,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/rollouts`,
    version: '1.0.0',
  },
  [
    requiresRole(rolloutRoles.ALLOW_POST),
    validateBody(
      {
        name: Joi.string().max(100),
        softwareId: Joi.number()
          .integer()
          .min(0)
          .max(positiveInteger.MAX_VALUE),
        deploymentAction: Joi.string()
          .valid(
            deployAction.DOWNLOAD,
            deployAction.DOWNLOAD_AND_INSTALL,
            deployAction.INSTALL
          )
          .optional(),
        startTime: Joi.number().when('deploymentAction', {
          is: deployAction.DOWNLOAD,
          then: Joi.optional(),
          otherwise: Joi.required(),
        }),
        installWindow: Joi.number().when('deploymentAction', {
          is: deployAction.DOWNLOAD,
          then: Joi.optional(),
          otherwise: Joi.number().min(0).required(),
        }),
        installFrequency: Joi.number().min(0).optional(),
        deploymentPolicy: Joi.number().allow('').empty().optional(),
        retry: Joi.boolean().optional(),
        type: Joi.string().regex(/software|media/),
        description: Joi.string().allow('').max(1000).optional(),
        targets: Joi.array()
          .items({
            // Javascript's integer is larger than postgres can handle
            // therefore using 'magic' number
            id: Joi.number()
              .integer()
              .min(positiveInteger.MIN_VALUE)
              .max(positiveInteger.MAX_VALUE),
          })
          .optional(),
        sites: Joi.array()
          .items({
            id: Joi.string().guid(),
          })
          .optional(),
        mfaCode: Joi.string().allow(null, '').length(6).optional(),
        downloadStartTime: Joi.number().when('deploymentAction', {
          is: Joi.string().valid(
            deployAction.DOWNLOAD,
            deployAction.DOWNLOAD_AND_INSTALL
          ),
          then: Joi.required(),
          otherwise: Joi.optional(),
        }),
        downloadWindow: Joi.number().when('deploymentAction', {
          is: deployAction.DOWNLOAD,
          then: Joi.number().min(0).required(),
          otherwise: Joi.optional(),
        }),
        releaseId: Joi.number().when('deploymentAction', {
          is: deployAction.INSTALL,
          then: Joi.required(),
          otherwise: Joi.optional(),
        }),
      },
      false
    ),
    rollout.createRollout,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/rollouts/:rolloutId/cancel`,
    version: '1.0.0',
  },
  [
    requiresRole(rolloutRoles.ALLOW_POST),
    validateParams({
      rolloutId: Joi.number().integer().min(positiveInteger.MIN_VALUE),
    }),
    rollout.cancelRollout,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/rollouts/:rolloutId/deployments`,
    version: '1.0.0',
  },
  [
    requiresRole(rolloutRoles.ALLOW_GET),
    validateParams({
      rolloutId: Joi.number().integer().min(positiveInteger.MIN_VALUE),
    }),
    rollout.getDeploymentByRolloutId,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/rollouts/createdownload/confirm`,
    version: '1.0.0',
  },
  [
    requiresRole(rolloutRoles.ALLOW_POST),
    validateBody({
      siteCount: Joi.number().min(0),
      targetCount: Joi.number().min(0),
    }),
    rollout.rolloutConfirm,
  ]
);
