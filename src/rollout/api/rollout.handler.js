const restify = require('restify');
const errorHandler = require('../../../lib/errorhandler');
const paginationHelper = require('../../../helpers/pagination-helper');
const logger = require('../../../lib/logger').mainLogger();
const rolloutService = require('../services');
const usersHelper = require('../../../helpers/users-helper');
const totp = require('../../../lib/totp');
const { addApiAuditLog } = require('../../../helpers/software-helper');
const rolloutHelper = require('./rollout-helper');

module.exports = {
  cancelRollout: async (req, res, next) => {
    const userId = req.user.sub;
    const userEmail = req.user.email;
    const companyId = req.user.company.id;
    const { rolloutId } = req.params;

    logger.info(`Cancelling rollout id ${rolloutId}`);
    const serviceContext = {
      userId,
      userEmail,
      companyId,
      rolloutId,
    };

    try {
      const result = await rolloutService.cancelById(serviceContext);
      res.send(result);
      await addApiAuditLog({ req, response: result, statusCode: 200 }); //  Insert into API Audit report
      return next();
    } catch (err) {
      /*  Add the error into Audit log report */
      await addApiAuditLog({
        req,
        response: err,
        statusCode: err.statusCode || 503,
      });
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getDeploymentByRolloutId: async (req, res, next) => {
    const userId = req.user.sub;
    const companyId = req.user.company.id;
    const { rolloutId } = req.params;

    const serviceContext = {
      userId,
      companyId,
      rolloutId,
    };
    try {
      const deploymentResponse =
        await rolloutService.getDeployments(serviceContext);
      res.send(deploymentResponse);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  createRollout: async (req, res, next) => {
    const userId = req.user.sub;
    const userEmail = req.user.email;
    const companyId = req.user.company.id;
    const siteTargetLimit = await rolloutHelper.getRolloutSiteTargetLimit(req);
    const userRoles = req.user.getRoles();
    const { sites = [], targets = [], mfaCode, type } = req.body;
    const { siteLimit, targetLimit } = siteTargetLimit;

    const rolloutDto = rolloutHelper.rolloutDtoFromReq(req, userId, userEmail);

    const targetIds = targets.map(({ id }) => id);
    const siteIds = sites.map(({ id }) => id);
    let mfaValidation = null;

    /* 
    [ICS-14747] MFA prompt
    */
    if (
      type === 'software' &&
      (siteIds.length >= siteLimit || targetIds.length >= targetLimit)
    ) {
      const mfaSecret = await usersHelper.getUserMfaSecretById(userId);
      const isMfaCodeValid = await totp.validateTotp(req, mfaSecret, mfaCode);
      if (!isMfaCodeValid) {
        await addApiAuditLog({
          req,
          response: { message: `MFA code ${mfaCode} validation failed` },
          statusCode: 406,
        });
        return next(
          new restify.errors.ForbiddenError(
            `MFA code ${mfaCode} validation failed`
          )
        );
      }
      mfaValidation = 'MFA was validated Successfully';
    }

    const serviceContext = {
      rolloutDto,
      companyId,
      userId,
      userRoles,
      targetIds,
      siteIds,
      requestId: req.getId(),
      requestBody: req.body,
    };

    try {
      // eslint-disable-next-line prefer-const
      let createdRollout = await rolloutService.create(serviceContext);
      // eslint-disable-next-line dot-notation
      if (mfaValidation) createdRollout['mfaValidation'] = mfaValidation;
      res.send(createdRollout);
      await addApiAuditLog({ req, response: createdRollout, statusCode: 200 }); //  Insert into API Audit report
      return next();
    } catch (err) {
      /*  Add the error into Audit log report */
      await addApiAuditLog({
        req,
        response: err,
        statusCode: err.statusCode || 503,
      });
      return errorHandler.onError(req, res, next)(err);
    }
  },
  getRollouts: async (req, res, next) => {
    const userId = req.user.sub;
    const companyId = req.user.company.id;
    const nameParam = req.query.name;
    const typeParam = req.query.type;
    const deviceParam = req.query.deviceType;
    const statusParam = req.query.status;
    const pageParams = paginationHelper.parsePaginationParams(req);

    const serviceContext = {
      userId,
      companyId,
      pageParams,
      nameParam,
      typeParam,
      deviceParam,
      statusParam,
    };

    try {
      const response = await rolloutService.findAll(serviceContext);
      res.send(response);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getRolloutById: async (req, res, next) => {
    const userId = req.user.sub;
    const companyId = req.user.company.id;
    const { rolloutId } = req.params;

    const serviceContext = {
      userId,
      companyId,
      rolloutId,
    };

    try {
      const response = await rolloutService.findOne(serviceContext);
      res.send(response);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  deleteRolloutById: async (req, res, next) => {
    const userId = req.user.sub;
    const companyId = req.user.company.id;
    const userRoles = req.user.getRoles();
    const { rolloutId } = req.params;

    logger.info(`Deleting rollout id ${rolloutId}`);

    const serviceContext = {
      userId,
      companyId,
      userRoles,
      rolloutId,
    };

    try {
      await rolloutService.deleteById(serviceContext);
      res.send(204);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  rolloutConfirm: async (req, res, next) => {
    try {
      const siteTargetLimit =
        await rolloutHelper.getRolloutSiteTargetLimit(req);
      const { siteCount, targetCount } = req.body;
      const { siteLimit, targetLimit } = siteTargetLimit;
      let mfaRequired = 'Download initiated without MFA';
      logger.info(
        `Confirm rollout with sitesSelected :: ${siteCount}  siteLimit :: ${siteLimit} and targetLimit :: ${targetLimit}`
      );
      if (siteCount >= siteLimit || targetCount >= targetLimit) {
        mfaRequired = 'Download initiated with MFA';
      }

      res.send(200, { message: mfaRequired });

      /*  Insert the API call into Audit log report */
      await addApiAuditLog({
        req,
        response: { message: mfaRequired },
        statusCode: 200,
      });

      return next();
    } catch (err) {
      /*  Add the error into Audit log report */
      await addApiAuditLog({
        req,
        response: err,
        statusCode: err.statusCode || 503,
      });
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
