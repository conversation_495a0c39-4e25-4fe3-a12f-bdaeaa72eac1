const Rollout = require('../../models/rollout.model');
const rolloutHelper = require('../../api/rollout-helper');

module.exports = async function getAll(context) {
  const {
    deviceParam,
    nameParam,
    typeParam,
    pageParams,
    userId,
    companyId,
    statusParam,
  } = context;

  const { conditions, params } = rolloutHelper.generateParamsAndCondition(
    userId,
    companyId,
    nameParam,
    typeParam,
    deviceParam
  );
  const resultReleases = await Rollout.queryWithCondition(
    params,
    conditions,
    // Do not limit when status or device filter is applied.
    statusParam ? null : pageParams
  );
  let releaseIds = [];
  let totalCount = 0;
  let results = [];

  if (resultReleases && resultReleases.length) {
    releaseIds = resultReleases.map(release => release.id);
    totalCount = resultReleases[0].count;
  }

  if (totalCount !== 0) {
    const [resultData, relatedInfo] = await Rollout.getManyById(
      releaseIds,
      userId,
      companyId
    );

    results = rolloutHelper.enrichResults(resultData, relatedInfo, true);

    if (statusParam && results) {
      results = results.filter(result => result[statusParam]);
      totalCount = results.length;

      if (results.length === 0) results = null;
      else if (pageParams) {
        const { pageSize, offset } = pageParams;
        results = results.slice(offset, pageSize + offset);
      }
    }
  }
  return {
    resultsMetadata: {
      totalResults: totalCount,
      pageIndex: pageParams.pageIndex,
      pageSize: pageParams.pageSize,
    },
    results,
  };
};
