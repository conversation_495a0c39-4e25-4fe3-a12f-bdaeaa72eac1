const restify = require('restify');

const Rollout = require('../../models/rollout.model');
const rolloutHelper = require('../../api/rollout-helper');
const { errorMsgs } = require('../../lib/rolloutConstants');
const { positiveInteger } = require('../../../../lib/app-constants');

module.exports = async function getById(context) {
  const { rolloutId, userId, companyId } = context;
  if (rolloutId >= positiveInteger.MAX_VALUE) {
    throw new restify.NotFoundError(errorMsgs.rolloutNotFound(rolloutId));
  }
  const resultRelease = await Rollout.getInfoById(rolloutId, userId, companyId);

  if (
    !resultRelease ||
    (resultRelease && resultRelease.accessibleDeploymentsCount === 0)
  ) {
    throw new restify.NotFoundError(`Rollout ${rolloutId} not found`);
  }

  const rollout = new Rollout(rolloutId);
  const rolloutQuery = await rollout.getOneById(userId, companyId);
  const queryResult = rolloutHelper.enrichResults(...rolloutQuery, false, true);

  return queryResult;
};
