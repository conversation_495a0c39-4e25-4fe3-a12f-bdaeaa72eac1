const restify = require('restify');

const Rollout = require('../../models/rollout.model');
const logger = require('../../../../lib/logger').mainLogger();
const { positiveInteger } = require('../../../../lib/app-constants');
const rolloutHelper = require('../../api/rollout-helper');
const { errorMsgs } = require('../../lib/rolloutConstants');

module.exports = async function cancelById(context) {
  const { rolloutId, userId, userEmail, companyId } = context;
  if (rolloutId >= positiveInteger.MAX_VALUE) {
    throw new restify.NotFoundError(errorMsgs.rolloutNotFound(rolloutId));
  }
  const resultReleases = await Rollout.getInfoById(
    rolloutId,
    userId,
    companyId
  );

  if (!resultReleases) {
    throw new restify.NotFoundError(errorMsgs.rolloutNotFound(rolloutId));
  }

  if (resultReleases && resultReleases.rolloutCompanyId !== companyId) {
    throw new restify.ForbiddenError(errorMsgs.rolloutAccessDenied('cancel'));
  }

  const rollout = new Rollout(rolloutId);
  const rolloutQuery = await rollout.getOneById();
  if (!rolloutQuery || !rolloutQuery.every(result => !!result.length)) {
    throw new restify.ForbiddenError(errorMsgs.rolloutAccessDenied('cancel'));
  }

  const originalRollout = rolloutHelper.enrichResults(
    ...rolloutQuery,
    false,
    true
  );

  const hasReleaseCompleted =
    rolloutHelper.rolloutStatusHasCompleted(originalRollout);

  if (hasReleaseCompleted || originalRollout.cancelled) {
    const cause = originalRollout.cancelled
      ? 'it is already cancelled'
      : 'all deployments have completed';
    const errResponse = `Cannot cancel rollout ${originalRollout.name} (id=${originalRollout.id}) because ${cause}`;
    logger.info(errResponse);
    throw new restify.HttpError({
      statusCode: '460',
      body: { code: 3002, message: errResponse },
    });
  }

  logger.info(
    `rolloout id ${originalRollout.id} name ${originalRollout.name} is cancelled by ${userEmail}`
  );

  await rollout.cancelById(userId, userEmail);
  const updatedRolloutQuery = await rollout.getOneById();
  const updatedRollout = rolloutHelper.enrichResults(
    ...updatedRolloutQuery,
    false,
    true
  );

  return updatedRollout || {};
};
