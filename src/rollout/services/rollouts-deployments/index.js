const restify = require('restify');

const Rollout = require('../../models/rollout.model');
const { positiveInteger } = require('../../../../lib/app-constants');
const { errorMsgs } = require('../../lib/rolloutConstants');
const rolloutHelper = require('../../api/rollout-helper');

module.exports = async function getDeployments(context) {
  const { rolloutId, userId, companyId } = context;
  if (rolloutId >= positiveInteger.MAX_VALUE) {
    throw new restify.NotFoundError(errorMsgs.rolloutNotFound(rolloutId));
  }
  let deploymentResponse = [];

  const { deployments } = await new Rollout(rolloutId).getDeployments(
    userId,
    companyId
  );
  if (deployments) {
    const deviceType = deployments[0].target.deviceType.id;
    const { displayName: companySpecificDeviceName } =
      await Rollout.getDeviceDisplayNameByCompany(deviceType, companyId);
    const isCompanyDisplayNameDifferent =
      companySpecificDeviceName && deviceType !== companySpecificDeviceName;
    deploymentResponse = isCompanyDisplayNameDifferent
      ? rolloutHelper.setDeviceDisplayName(
          companySpecificDeviceName,
          deployments
        )
      : deployments;
  }
  return deploymentResponse;
};
