const restify = require('restify');

const Rollout = require('../../models/rollout.model');
const rolloutHelper = require('../../api/rollout-helper');
const { roles } = require('../../../../lib/app-constants');
const { errorMsgs } = require('../../lib/rolloutConstants');
const logger = require('../../../../lib/logger').mainLogger();
const { deployAction } = require('../../lib/rolloutConstants');
const companyHelper = require('../../../../helpers/company-helper');

const G7_100 = 'G7-100';
const G6_500 = 'G6-500';

const softwareAccessCheck = async (softwareId, companyId) => {
  const software = await Rollout.checkSoftwareAccess(softwareId, companyId);
  if (!software || (software && !software.active)) {
    throw new restify.NotFoundError(errorMsgs.softwareNotFound);
  }

  if (
    software &&
    software.companyId.toLowerCase() !== companyId.toLowerCase()
  ) {
    throw new restify.ForbiddenError('No permission');
  }
  return software;
};

module.exports = async function createRollout(context) {
  const {
    userId,
    companyId,
    userRoles,
    rolloutDto,
    requestBody,
    targetIds,
    siteIds,
    requestId,
  } = context;
  const { software_id: softwareId, type } = rolloutDto;

  const ICS_SYSTEM_USER = userRoles && userRoles.includes(roles.ICS_SYSTEM);

  /* For SQ3 to SQ5 upgrade,
   * allow ICS_SYSTEM user to rollout the deployment of
   * the deployment of certificate bundle package
   * on the device
   */
  const allowCreateSoftwareRollout = ICS_SYSTEM_USER
    ? true
    : rolloutHelper.allowModifySoftwareRolloutRoles(userRoles);

  if (targetIds.length === 0 && siteIds.length === 0) {
    throw new restify.BadRequestError('Sites and Targets both empty');
  }

  if (type !== 'media' && !allowCreateSoftwareRollout) {
    throw new restify.ForbiddenError(errorMsgs.roleDeniedForCreation);
  }

  if (requestBody.deploymentAction) {
    const companyFeatureFlags =
      await companyHelper.getAllFeatureFlagsByCompanyIdInCache(companyId);

    if (!companyFeatureFlags.includes('STAGED_SOFTWARE_ROLLOUT')) {
      throw new restify.ForbiddenError(
        'Staged software rollout is not enabled for this company.'
      );
    }
  }

  const software = await softwareAccessCheck(softwareId, companyId);
  const deviceTypeMap = software.deviceType.startsWith(G7_100)
    ? G7_100
    : software.deviceType;

  if (siteIds.length !== 0)
    logger.info(`add rollout by site detected. Size... ${siteIds.length}`);
  const { sitesAllowed, sitesFound } = await Rollout.getAccessibleSitesByUser(
    userId,
    siteIds
  );

  /*
   * ICS_SYSTEM users are not mapped with any sites
   * skip site access check for ICS_SYSTEM user role
   */
  if (!ICS_SYSTEM_USER)
    rolloutHelper.checkSiteAccess(siteIds, sitesAllowed, sitesFound);

  // At this point all sites found are accessible
  const targetsFromSites = await Rollout.getDeployableTargetsForSites(
    siteIds,
    deviceTypeMap
  );

  const siteTargets = (targetsFromSites && targetsFromSites.siteTargets) || {};
  const devicesFromSites = new Map(Object.entries(siteTargets || {}));
  const targetIdsFromSites = Object.values(siteTargets)
    .flat()
    .map(target => target.target_id); // <Integer, Target>

  // Create deployment for specified targets in the request
  if (targetIds.length !== 0)
    logger.info(`add rollout by devices detected. Size... ${targetIds.length}`);

  let dualDisplayTargetIds = [];
  if (deviceTypeMap === G6_500 || deviceTypeMap === G7_100) {
    dualDisplayTargetIds = await rolloutHelper.checkDualDisplay(
      targetIdsFromSites,
      targetIds
    );
  }
  const { targetsFound } = await Rollout.getTargetsById(
    dualDisplayTargetIds.length > 0 ? dualDisplayTargetIds : targetIds
  );
  const devicesToDeployForTheRollout = rolloutHelper.checkDeviceAccess(
    targetIds.map(Number),
    targetsFound,
    sitesAllowed,
    targetIdsFromSites,
    deviceTypeMap,
    devicesFromSites
  );

  if (devicesToDeployForTheRollout.size === 0)
    throw new restify.ConflictError(errorMsgs.rolloutNoDeviceConflict);

  const filteredDevices =
    deviceTypeMap === G7_100 || dualDisplayTargetIds.length > 0
      ? rolloutHelper.filterDevices(devicesToDeployForTheRollout, targetsFound)
      : null;

  const allSites = [...devicesToDeployForTheRollout.keys()];
  const deviceOwners = await Rollout.getCompanyBySites(allSites);

  await Rollout.stripAndLogRequestData(
    requestId,
    requestBody,
    deviceOwners,
    targetIds,
    siteIds,
    companyId,
    userId
  );
  const sortedByLastContactMap = {};
  // eslint-disable-next-line no-restricted-syntax
  for (const siteId of devicesToDeployForTheRollout.keys()) {
    const currentSiteTargets = filteredDevices
      ? filteredDevices.get(siteId)
      : devicesToDeployForTheRollout.get(siteId);
    const sortedByLastContactTargetList = Object.values(
      currentSiteTargets
    ).sort(
      (a, b) =>
        new Date(b.last_contact).valueOf() - new Date(a.last_contact).valueOf()
    );

    sortedByLastContactMap[siteId] = sortedByLastContactTargetList;
  }

  let rolloutId = null;

  if (rolloutDto.deploymentAction === deployAction.INSTALL) {
    rolloutId = await Rollout.deployInstallOnlyRollout(
      rolloutDto,
      software,
      sortedByLastContactMap,
      requestBody.releaseId
    );
  } else {
    rolloutId = await Rollout.deployRollout(
      rolloutDto,
      software,
      sortedByLastContactMap
    );
  }

  const createdRollout = new Rollout(rolloutId);
  const enrichedResult = rolloutHelper.enrichResults(
    ...(await createdRollout.getOneById()),
    false,
    true
  );

  return enrichedResult;
};
