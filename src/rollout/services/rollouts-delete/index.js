const restify = require('restify');

const logger = require('../../../../lib/logger').mainLogger();
const Rollout = require('../../models/rollout.model');
const rolloutHelper = require('../../api/rollout-helper');
const { errorMsgs } = require('../../lib/rolloutConstants');

module.exports = async function deleteById(context) {
  const { rolloutId, userId, companyId, userRoles } = context;
  const resultRelease = await Rollout.getInfoById(rolloutId, userId, companyId);
  const allowDeleteSoftwareRollout =
    rolloutHelper.allowModifySoftwareRolloutRoles(userRoles);

  if (!resultRelease) {
    throw new restify.NotFoundError(errorMsgs.rolloutNotFound(rolloutId));
  }

  if (resultRelease && resultRelease.rolloutCompanyId !== companyId) {
    throw new restify.ForbiddenError(errorMsgs.rolloutAccessDenied('delete'));
  }

  if (
    resultRelease &&
    resultRelease.type !== 'media' &&
    !allowDeleteSoftwareRollout
  ) {
    throw new restify.ForbiddenError(errorMsgs.roleDeniedForDeletion);
  }

  const rollout = new Rollout(rolloutId);
  const rolloutQuery = await rollout.getOneById(userId, companyId);

  if (!rolloutQuery || !rolloutQuery.every(result => !!result.length)) {
    throw new restify.ForbiddenError(errorMsgs.rolloutAccessDenied('delete'));
  }

  const queryResult = rolloutHelper.enrichResults(...rolloutQuery, false, true);

  if (!rolloutHelper.rolloutStatusHasCompleted(queryResult)) {
    logger.info(
      'Cannot delete rollout. To delete it must be total = successCount OR total = failureCount OR total = cancelCount OR total = (successCount + failureCount + cancelCount)'
    );
    throw new restify.HttpError({
      statusCode: '460',
      message: errorMsgs.rolloutIncomplete,
    });
  }

  logger.info(
    `Rollout is completed. Deleting rollout ${queryResult.name} id=${queryResult.id} by ${userId}`
  );
  await rollout.deleteById(userId);
  return queryResult;
};
