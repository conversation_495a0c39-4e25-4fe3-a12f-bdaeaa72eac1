export const listToMatrix = (list: any[], elementsPerSubArray: number) => {
  // eslint-disable-next-line prefer-const
  let matrix: any[] = [];
  // eslint-disable-next-line one-var
  let idx, matrixIdx;

  for (idx = 0, matrixIdx = -1; idx < list.length; idx += 1) {
    if (idx % elementsPerSubArray === 0) {
      matrixIdx += 1;
      matrix[matrixIdx] = [];
    }

    matrix[matrixIdx].push(list[idx]);
  }

  return matrix;
};
