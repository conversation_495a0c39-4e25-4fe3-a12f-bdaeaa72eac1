interface insertNotificationProps {
  conn: any;
  recepientIds: string[];
  timestamp: Date;
  message: string;
  relatedEntity: { type: string; path: string };
  notificationType: string;
  level: string;
}

export const insertNotification = ({
  conn,
  recepientIds,
  timestamp,
  message,
  relatedEntity,
  notificationType,
  level,
}: insertNotificationProps) =>
  conn.execute(
    `INSERT INTO notification
                            ( id, user_id, created, read, message, related_entity, type, level )
                        VALUES
                            ( uuid_generate_v1mc(), UNNEST(ARRAY[$1::uuid[]]), $2, $3, $4, $5, $6, $7 );
                        `,
    [
      recepientIds,
      timestamp,
      false,
      message,
      relatedEntity,
      notificationType,
      level,
    ]
  );
