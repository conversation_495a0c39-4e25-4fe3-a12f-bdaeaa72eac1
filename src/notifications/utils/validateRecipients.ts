import restify from 'restify-errors';
import { getRecipientsById } from './getRecipientById';
import { getRecipientByRoles } from './getRecipientByRoles';
import { getRoles } from './getRoles';

export const validateRecipients = async (
  tenantId: string,
  recipientIds: string[],
  recipientRoles: string[]
) => {
  if (recipientIds?.length) {
    if (new Set(recipientIds).size !== recipientIds.length) {
      throw new restify.BadRequestError('Found duplicates in recipients list');
    }
    const users = await getRecipientsById(recipientIds);
    if (users?.length !== recipientIds.length) {
      throw new restify.NotFoundError('Recipient not found');
    }
    return users;
  }
  if (recipientRoles?.length) {
    if (new Set(recipientRoles).size !== recipientRoles.length) {
      throw new restify.BadRequestError(
        'Found duplicates in recipient roles list'
      );
    }
    const roles = await getRoles(recipientRoles);
    if (roles.length !== recipientRoles.length) {
      throw new restify.NotFoundError('Recipient Role not found');
    }

    const roleIds = roles.map((role: { roleId: any }) => role.roleId);
    const users = await getRecipientByRoles(roleIds, tenantId);
    if (!users.length) {
      throw new restify.NotFoundError('User not found with role(s)');
    }
    return users;
  }
  throw new restify.BadRequestError(
    'Both RecipientIds and RecipientRoles Not Defined'
  );
};
