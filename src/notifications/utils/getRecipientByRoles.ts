import { server } from '../../../app';
import { userStatus } from '../../../lib/app-constants';

export const getRecipientByRoles = (roleIds: number[], tenantId: string) =>
  server.db.read.rows(
    `
      SELECT DISTINCT u.id, u.company_id, u.email FROM ics_user u
      INNER JOIN user_role ur ON u.id = ur.user_id
      WHERE u.status = $1 AND ur.role_id = ANY($2::int[]) AND u.company_id = $3`,
    [userStatus.ACTIVE, roleIds, tenantId]
  );
