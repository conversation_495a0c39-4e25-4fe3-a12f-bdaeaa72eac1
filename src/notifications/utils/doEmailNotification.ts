import mailer from '../../../lib/mailer';

interface DoEmailNotificationProps {
  logger: {
    error: any;
    info: any;
  };
  tenant: { id: string; senderEmail: string };
  toRecipients?: { id: string; companyId: string; email: string }[];
  bccRecipients?: { id: string; companyId: string; email: string }[];
  ccRecipients?: { id: string; companyId: string; email: string }[];
  isSendEmailNotification: boolean;
  emailNotification: {
    subject: string;
    message: string;
    htmlStyle: string;
  };
}
export const doEmailNotification = ({
  logger,
  tenant,
  toRecipients,
  bccRecipients,
  ccRecipients,
  isSendEmailNotification,
  emailNotification,
}: DoEmailNotificationProps) => {
  if (!isSendEmailNotification) {
    return Promise.resolve();
  }
  logger.info(
    {
      toRecipients,
      bccRecipients,
      ccRecipients,
    },
    `[doEmailNotification] send to recipients`
  );
  const toEmailAddress = toRecipients?.length
    ? toRecipients.map(recipient => recipient.email)
    : [];
  const bccEmailAddress = bccRecipients?.length
    ? bccRecipients.map(recipient => recipient.email)
    : [];
  const ccEmailAddress = ccRecipients?.length
    ? ccRecipients.map(recipient => recipient.email)
    : [];
  return mailer.sendEmailToManyReceipient(
    {
      to: toEmailAddress,
      bcc: bccEmailAddress,
      cc: ccEmailAddress,
      from: tenant.senderEmail,
    },
    {
      style: emailNotification?.htmlStyle || '',
      message:
        emailNotification?.message ||
        emailNotification?.subject ||
        'No Message',
    },
    emailNotification?.subject || 'System Email Notification'
  );
};
