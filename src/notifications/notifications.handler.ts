import restify from 'restify-errors';
import { server } from '../../app';

import errorhandler from '../../lib/errorhandler';
import { validateRecipients } from './utils/validateRecipients';
import { getTenantById } from './utils/getTenantById';
import { doEmailNotification } from './utils/doEmailNotification';
import { insertNotification } from './utils/insertNotification';
import { listToMatrix } from './utils/listToMatrix';
import { MAX_INSERT_NOTIFICATION, MAX_EMAIL_ADDRESS } from './constants';

export const createEmailUINotification = async (
  req: {
    body: {
      recipientIds: string[];
      recipientRoles: string[];
      timestamp: Date;
      level: string;
      type: string;
      relatedEntity: { type: string; path: string };
      message: string;
      tenantId: string;
      isSendEmailNotification: boolean;
      emailNotification: {
        subject: string;
        message: string;
        htmlStyle: string;
      };
    };
    log: {
      error: any;
      info: any;
    };
  },
  res: { send: (arg0: number) => void },
  next: (arg0?: restify.ConflictError | undefined) => any
) => {
  const {
    recipientIds,
    recipientRoles,
    timestamp,
    level,
    type: notificationType,
    relatedEntity,
    message,
    tenantId,
    isSendEmailNotification,
    emailNotification,
  } = req.body;
  let users: { id: string; companyId: string; email: string }[] = [];
  let tenant: { id: string; senderEmail: string };
  try {
    if (isSendEmailNotification) {
      if (!emailNotification) {
        return next(
          new restify.BadRequestError(
            'Email Notification Requires emailNotification object'
          )
        );
      }
      if (!emailNotification?.subject) {
        return next(
          new restify.BadRequestError(
            'Email Notification Requires email notification subject'
          )
        );
      }
    }
    tenant = await getTenantById(tenantId);
    if (!tenant) {
      return next(new restify.NotFoundError('Tenant Not Found By tenantId'));
    }
    if (isSendEmailNotification) {
      if (!tenant.senderEmail) {
        return next(
          new restify.ConflictError(
            `Sender Email Not Defined for tenant ${tenant.id}`
          )
        );
      }
    }
    users = await validateRecipients(tenantId, recipientIds, recipientRoles);
    if (
      !users.every(
        (user: { companyId: any }) => user.companyId === users[0].companyId
      )
    ) {
      return next(
        new restify.ConflictError('Recipient must all be of the same company')
      );
    }
  } catch (err) {
    req.log.error({ err });
    return errorhandler.onError(req, res, next)(err);
  }
  const conn = await server.db.write.getConnection();

  try {
    await conn.execute('BEGIN');

    const insertMatrix = listToMatrix(users, MAX_INSERT_NOTIFICATION);

    await Promise.allSettled(
      insertMatrix.map(insertUsers =>
        insertNotification({
          conn,
          recepientIds: insertUsers.map(
            (recipient: { id: string; companyId: string }) => recipient.id
          ),
          timestamp,
          message,
          relatedEntity,
          notificationType,
          level,
        })
      )
    );
    await conn.execute('COMMIT');

    if (isSendEmailNotification) {
      const emailMatrix = listToMatrix(users, MAX_EMAIL_ADDRESS);
      await Promise.allSettled(
        emailMatrix.map(recipients =>
          doEmailNotification({
            logger: req.log,
            tenant,
            bccRecipients: recipients,
            isSendEmailNotification,
            emailNotification,
          })
        )
      );
    }
  } catch (err) {
    req.log.error({ err });
    await conn.execute('ROLLBACK');
    return errorhandler.onError(req, res, next)(err);
  } finally {
    conn.done();
  }

  res.send(204);
  return next();
};
