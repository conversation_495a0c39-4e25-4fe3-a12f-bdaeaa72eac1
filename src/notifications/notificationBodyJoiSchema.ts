const Joi = require('joi');

export const notificationBodyJoiSchema = {
  type: Joi.string().required(),
  tenantId: Joi.string().guid().required(),
  recipientRoles: Joi.array().items(Joi.string()).optional(),
  recipientIds: Joi.array().items(Joi.string().guid()).optional(),
  timestamp: Joi.date().required(),
  level: Joi.string().required(),
  relatedEntity: Joi.object().keys({
    type: Joi.string().optional(),
    path: Joi.string().optional(),
  }),
  message: Joi.string().required(),
  isSendEmailNotification: Joi.boolean().optional(),
  emailNotification: Joi.object().keys({
    subject: Joi.string(),
    message: Joi.string(),
    htmlStyle: Joi.string().optional(),
  }),
};
