import { requiresRole, validateBody } from '../../lib/pre';
import { server } from '../../app';
import * as env from '../../env';
import { createEmailUINotification } from './notifications.handler';
import { notificationBodyJoiSchema } from './notificationBodyJoiSchema';

const BASE_PATH = `${env.config.base}/notifications`;

/**
 * @swagger
 * /notifications/emailui/create:
 *   post:
 *     tags:
 *       - notifications
 *       - internal
 *     summary: Notification API Version 3
 *     description: Version 3 of create Email and UI notification - include notification by roles and recipients
 *     parameters:
 *       - $ref: '#/parameters/AuthorizationTokenParam'
 *       - name: notificationDetail.
 *         in: body
 *         description: Notification details
 *         required: true
 *         schema:
 *           type: object
 *           required:
 *             - type
 *             - timestamp
 *             - level
 *             - message
 *           properties:
 *             type:
 *               type: string
 *               description: Type of UI notification. i.e. Alarm
 *             tenantId:
 *               type: string
 *               format: uuid
 *               description: tenant ID
 *             recipientRoles:
 *               type: array
 *               items:
 *                 type: string
 *               description: Array of user roles that will recieve this notification
 *             recipientIds:
 *               type: array
 *               items:
 *                 type: string
 *               description: Array of userIds that will recieve this notification
 *             timestamp:
 *               type: string
 *               format: date-time
 *               description: when this notification was created ISO dateformat
 *             level:
 *               type: string
 *               description: SUCCESS|INFO|WARNING|ERROR etc
 *             relatedEntity:
 *               type: object
 *               description: Multi property object but requires 'id' which is a reference to any object in the db
 *               properties:
 *                 type:
 *                   type: string
 *                   description: Notification Type
 *                 path:
 *                   type: string
 *                   description: redirection path
 *             message:
 *               type: string
 *               description: The message that will appear in the UI
 *             isSendEmailNotification:
 *               type: boolean
 *               description: Send Email Notification if true
 *             emailNotification:
 *               type: object
 *               description: Send Email Notification if true
 *               properties:
 *                 subject:
 *                   type: string
 *                   description: Email Subject for emailed notification
 *                 message:
 *                   type: string
 *                   description: Email Message for emailed notification
 *                 htmlStyle:
 *                   type: string
 *                   description: Email Message Style for emailed notification
 *     responses:
 *       204:
 *         description: UI notification created successfully
 *       409:
 *         description: Recipients should belong to the same company
 *       401:
 *         $ref: '#/responses/authenticationFailed'
 *       500:
 *         $ref: '#/responses/internalServerError'
 */
server.post(
  {
    path: `${BASE_PATH}/emailui/create`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateBody(notificationBodyJoiSchema),
    createEmailUINotification,
  ]
);
