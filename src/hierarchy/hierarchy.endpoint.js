const Joi = require('joi');

const { server } = require('../../app');
const env = require('../../env');
const { requiresRole, validateQuery } = require('../../lib/pre');
const handler = require('./hierarchy.handler');

const BASE_PATH = `${env.config.base}/hierarchy`;
const VERSION = '1.0.0';
/**
 * @swagger
 *   "/hierarchy/companies":
 *     get:
 *       tags:
 *         - hierarchy
 *       summary: "Gets the hierarchy data for companies"
 *       description: "This allows the caller to retrieve company hierarchy data"
 *       parameters:
 *         - $ref: '#/parameters/hierarchyRequestPathParam'
 *       responses:
 *         "200":
 *           description: "Company data in response body"
 *           schema:
 *             $ref: "#/definitions/CompanyDataDto"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/companies`,
    version: VERSION,
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateQuery({
      endDate: Joi.date().iso(),
    }),
    handler.getCompanyData,
  ]
);

/**
 * @swagger
 *   "/hierarchy/sites":
 *     get:
 *       tags:
 *         - hierarchy
 *       summary: "Gets the hierarchy data for sites"
 *       description: "This allows the caller to retrieve site hierarchy data"
 *       parameters:
 *         - $ref: '#/parameters/hierarchyRequestPathParam'
 *       responses:
 *         "200":
 *           description: "Site data in response body"
 *           schema:
 *             $ref: "#/definitions/SiteDataDto"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/sites`,
    version: VERSION,
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateQuery({
      endDate: Joi.date().iso(),
    }),
    handler.getSiteData,
  ]
);

/**
 * @swagger
 *   "/hierarchy/assets":
 *     get:
 *       tags:
 *         - hierarchy
 *       summary: "Gets the hierarchy data for assets"
 *       description: "This allows the caller to retrieve asset hierarchy data"
 *       parameters:
 *         - $ref: '#/parameters/hierarchyRequestPathParam'
 *       responses:
 *         "200":
 *           description: "Device data in response body"
 *           schema:
 *             $ref: "#/definitions/AssetDataDto"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/assets`,
    version: VERSION,
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateQuery({
      endDate: Joi.date().iso(),
    }),
    handler.getAssetData,
  ]
);

/**
 * @swagger
 *   "/hierarchy/site-properties":
 *     get:
 *       tags:
 *         - hierarchy
 *       summary: "Gets the site-properties data for sites"
 *       description: "This allows the caller to retrieve site-tag hierarchy data"
 *       parameters:
 *         - $ref: '#/parameters/hierarchyRequestPathParam'
 *       responses:
 *         "200":
 *           description: "Site-Tag data in response body"
 *           schema:
 *             $ref: "#/definitions/SiteTagDataDto"
 *         "400":
 *           description: "Bad Request"
 *           schema:
 *             $ref: "#/definitions/ErrorMessage"
 *         "401":
 *           $ref: "#/responses/authenticationFailed"
 *         "403":
 *           description: "No permission"
 *         "500":
 *           $ref: "#/responses/internalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/site-properties`,
    version: VERSION,
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateQuery({
      endDate: Joi.date().iso(),
    }),
    handler.getSitePropertiesData,
  ]
);
