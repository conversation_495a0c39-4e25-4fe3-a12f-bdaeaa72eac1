const _ = require('lodash');
const errorHandler = require('../../lib/errorhandler');
const { server } = require('../../app');

module.exports = {
  async getCompanyData(req, res, next) {
    try {
      const { endDate } = req.params;
      const companyData = await server.db.read.rows(
        `
                SELECT * from public.fn_ExtractCompanyData($1::timestamptz)
                `,
        [endDate]
      );

      const mappedCompanyData = companyData.map(a => ({
        companyId: a.companyid,
        companyName: a.companyname,
      }));

      res.send(mappedCompanyData);
      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },
  async getSiteData(req, res, next) {
    try {
      const { endDate } = req.params;
      const siteData = await server.db.read.rows(
        `
                SELECT * from public.fn_ExtractSiteData($1::timestamptz)
                `,
        [endDate]
      );

      const mappedSiteData = siteData.map(a => ({
        siteId: a.siteid,
        siteName: a.sitename,
        companyId: a.comanyid,
        active: a.siteactive,
        deletedDate: a.deleteddate,
      }));

      res.send(mappedSiteData);
      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },
  async getAssetData(req, res, next) {
    try {
      const { endDate } = req.params;
      const assetData = await server.db.read.rows(
        `
                SELECT * from public.fn_ExtractAssetData($1::timestamptz)
                `,
        [endDate]
      );

      const mappedAssetData = assetData.map(a => ({
        assetId: a.targetid,
        siteId: a.siteid,
        assetName: a.targetname,
        lastRegistered: a.lastregistered,
        lastContact: a.lastcontact,
        assetStatus: a.statusvalue,
        deviceType: a.devicetype,
        serialNumber: a.serialnumber,
        deletedDate: a.deletetimestamp,
        releaseVersion: a.releaseversion,
        lastEditedDate: a.lastediteddate,
        deploymentType: a.deploymenttype,
        updatedBy: a.updatedby,
      }));

      res.send(mappedAssetData);
      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },
  async getSitePropertiesData(req, res, next) {
    try {
      const { endDate } = req.params;
      const sitePropertiesData = await server.db.read.rows(
        `
                SELECT * from public.fn_ExtractSitePropertiesData($1::timestamptz)
                `,
        [endDate]
      );

      const mappedSitePropertiesData = sitePropertiesData.map(a => ({
        siteId: a.siteid,
        propertyName: 'Tag',
        propertyValue: a.tagid.toString(),
        deleted: a.sitetagdeleted,
        deploymentType: a.deploymenttype,
        updatedBy: a.updatedby,
      }));

      const uniqueTagIds = _.uniq(
        mappedSitePropertiesData.map(p => p.propertyValue)
      );

      const tagData = await server.db.read.rows(
        `
                SELECT * from public.fn_ExtractTagData($1::int[])
                `,
        [uniqueTagIds]
      );

      const mappedTagData = tagData.map(a => ({
        tagId: a.tagid,
        tagName: a.tagname,
        companyId: a.companyid,
        deleted: false,
      }));

      const result = {
        sitePropertiesData: mappedSitePropertiesData,
        tagData: mappedTagData,
      };

      res.send(result);
      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },
};
