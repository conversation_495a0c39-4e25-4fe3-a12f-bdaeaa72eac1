const errors = require('restify-errors');
const { config } = require('../../env');
const { loginFailedTypes } = require('../../lib/app-constants');
const logger = require('../../lib/logger').mainLogger();
const {
  getUserInfo,
  getHubAuthToken,
  decryptToken,
} = require('../../helpers/keycloak-hub-helper');
const authenticateUserRepository = require('./authenticate-user.repository');
const {
  generateLoginFailedResponse,
  generateHubLoginFailedResponse,
} = require('./authenticate-user.helper');
const validators = require('./validators');
const { processLogin, processSuccessfulLogin } = require('./user.service');

const isCaptchaEnabled = !!config.captcha && config.captcha.enabled;

// MFA code should only be 6 digits, nothing else
const AUTH_LOG_REQUIRED = {
  [loginFailedTypes.WRONG_PASSWORD]: ['email-password', false],
  [loginFailedTypes.WRONG_MFA]: ['email-password-mfa', false],
  [loginFailedTypes.MFA_NOT_PROVIDED]: ['email-password-no-mfa', true],
  [loginFailedTypes.USER_LOCKED]: ['email-password', false],
  [loginFailedTypes.USER_LOCKED_RESET]: ['email-password', false],
};

/**
 *
 *
 * @param {number} loginFailedType
 * @param {authenticateUserRepository.User} user
 */
const logAuthHistory = async (loginFailedType, user, featureFlags = []) => {
  const [type, success] = AUTH_LOG_REQUIRED[loginFailedType];
  const isPCIDSSEnabledForTenant = featureFlags.includes('PCI_DSS_COMPLIANCE');
  const dbLoggings = [
    authenticateUserRepository.recordAuthHistory(user.id, type, success),
  ];
  if (
    isPCIDSSEnabledForTenant &&
    loginFailedType === loginFailedTypes.USER_LOCKED_RESET
  ) {
    dbLoggings.push(
      authenticateUserRepository.updateUserLoginData(user.id, 1, null)
    );
  } else if (
    isPCIDSSEnabledForTenant &&
    loginFailedType === loginFailedTypes.USER_LOCKED
  ) {
    if (user.failedLoginAttempts + 1 === 4) {
      dbLoggings.push(
        authenticateUserRepository.updateUserLoginData(
          user.id,
          user.failedLoginAttempts + 1,
          true
        )
      );
      logger.info(`User email ${user.email} - excess failed login attempts.`);
    }
  } else if (loginFailedType === loginFailedTypes.WRONG_PASSWORD) {
    dbLoggings.push(
      authenticateUserRepository.updateUserLoginData(
        user.id,
        user.failedLoginAttempts + 1,
        null
      )
    );
    // Check if failed attempts exceed threshold
    if (isPCIDSSEnabledForTenant && user.failedLoginAttempts + 1 >= 4) {
      dbLoggings.push(
        authenticateUserRepository.updateUserLoginData(
          user.id,
          user.failedLoginAttempts + 1,
          true
        )
      );
      logger.info(`User email ${user.email} - excess failed login attempts.`);
    }
    if (
      isCaptchaEnabled &&
      user.failedLoginAttempts > config.auth.excessiveLoginAttemptsTriggerCount
    ) {
      logger.info(`User email ${user.email} - excess failed login attempts.`);
      dbLoggings.push(
        authenticateUserRepository.recordAuthHistory(
          user.id,
          'excess-failed-login-attempts',
          false
        )
      );
    }
  }
  await Promise.all(dbLoggings).catch(reason =>
    logger.error(`Failed to generate db log, ${reason}`)
  );
};

/**
 *
 *
 * @param {Request} req
 * @param {authenticateUserRepository.User} user
 */
const enrichRequestContext = (req, user) => {
  req._userId = user.id; // eslint-disable-line no-param-reassign, no-underscore-dangle
  req._companyId = user.company ? user.company.id : null; // eslint-disable-line no-param-reassign, no-underscore-dangle
  // eslint-disable-next-line no-param-reassign
  req.user = user;
};

/**
 * @typedef Body
 * @property {string} email
 * @property {string} password
 * @property {string} mfaCode
 * @property {string?} [ captchaResponse ]
 */
/**
 * @typedef Request
 * @property {Body} body
 * @property {string?} [_userId]
 * @property {string?} [ _companyId]
 * @property {authenticateUserRepository.User?} [user]
 */
/**
 *
 *
 * @param {Request} req
 * @returns
 */
const authenticateUser = async req => {
  const { email, password, mfaCode } = req.body;

  logger.info(`[Authn] Start authenticate user: ${email}.`);
  try {
    const user = await validators.validateAuthRequest(email, password);
    // side effect function that adds neccessary attributes to req object
    enrichRequestContext(req, user);

    logger.info(`[Authn] User email and password was valid: ${email}.`);
    await validators.validateMfa(req, user, mfaCode);

    return await processLogin(req, user);
  } catch (e) {
    if (e instanceof validators.LoginValidationError) {
      const featureFlags = e?.user?.company?.featureFlags || [];
      if (AUTH_LOG_REQUIRED[e.failedType]) {
        await logAuthHistory(e.failedType, e.user, featureFlags);
        enrichRequestContext(req, e.user);
      }
      return generateLoginFailedResponse(e.failedType);
    }
    throw e;
  }
};

const authenticateUserHub = async req => {
  try {
    const hubAuthToken = await getHubAuthToken();
    if (!hubAuthToken) {
      throw new errors.UnauthorizedError('Failed to retrieve Hub auth token');
    }
    const decryptedToken = await decryptToken(req.body.token, hubAuthToken);
    if (!decryptedToken) {
      throw new errors.UnauthorizedError('Failed to retrieve decrypted token');
    }
    const keyCloakuser = await getUserInfo(decryptedToken);
    if (!keyCloakuser) {
      throw new errors.UnauthorizedError('Invalid token');
    }
    const { email } = keyCloakuser;
    logger.info(`Authenticate user V2 ${email}`);
    const user = await validators.validateAuthRequest(email, null);

    enrichRequestContext(req, user);

    logger.info(`User email ${email} was valid`);

    return await processSuccessfulLogin(user);
  } catch (e) {
    if (e instanceof validators.LoginValidationError) {
      if (AUTH_LOG_REQUIRED[e.failedType]) {
        await logAuthHistory(e.failedType, e.user);
        enrichRequestContext(req, e.user);
      }
      return generateHubLoginFailedResponse(e.failedType);
    }
    throw e;
  }
};

module.exports = {
  authenticateUser,
  authenticateUserHub,
};
