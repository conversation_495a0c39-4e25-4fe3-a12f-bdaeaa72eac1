const Joi = require('joi');

const env = require('../../env');
const { server } = require('../../app');
const { validateBody } = require('../../lib/pre');
const authenticationHandler = require('./authenticate-user.handler');
const { validateCaptcha } = require('./validators');

const isCaptchaEnabled = env.config.captcha && env.config.captcha.enabled;

server.post({ path: `${env.config.base}/authenticateuser`, version: '0.0.1' }, [
  validateBody({
    email: Joi.string().email(),
    password: Joi.string().regex(/(.){7,100}/),
    mfaCode: Joi.string()
      .regex(/^\d{6}$/)
      .optional(),
    captchaResponse: Joi.string()
      .regex(/(.){7,100}/)
      .optional(),
  }),
  authenticationHandler.authenticateUser,
  validateCaptcha(isCaptchaEnabled),
]);

server.post(
  { path: `${env.config.base}/authenticateuser/hub`, version: '0.0.1' },
  [
    validateBody({
      token: Joi.string().required(),
    }),
    authenticationHandler.authenticateUserHub,
  ]
);
