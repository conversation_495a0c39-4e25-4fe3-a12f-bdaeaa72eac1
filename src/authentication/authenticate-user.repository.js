const { server } = require('../../app');

/**
 *
 *
 * @param {string} userId
 * @param {string} type
 * @param {boolean} success
 */
const recordAuthHistory = async (userId, type, success) => {
  try {
    const query = `
            INSERT INTO auth_history
                ( user_id, type, timestamp, success )
            VALUES
                ( $1, $2, NOW(), $3 );
        `;

    await server.db.write.execute(query, [userId, type, success]);
  } catch (err) {
    server.log.error({ err }, 'Recording auth history failed');
    throw err;
  }
};

/**
 * @typedef Company
 * @property {string} id
 * @property {string} name
 * @property {string[]} roles
 * @property {string[]} featureFlags
 * @property {Number} sessionExpiryUserMins
 */
/**
 * @typedef User
 * @property {string} id
 * @property {string} email
 * @property {string} type
 * @property {string} fullName
 * @property {string} passwordHash
 * @property {string} mfaSecret
 * @property {Date} emailVerified
 * @property {number} status
 * @property {number} failedLoginAttempts
 * @property {boolean | null} lastLocked
 * @property {Date} created
 * @property {Company} company
 * @property {string[]} roles
 */
/**
 *
 *
 * @param {string} email
 * @returns {Promise<User>} first user with corresponding email
 */
const getUserByEmail = async email => {
  try {
    return await server.db.read.row(
      `
            WITH company AS (
                SELECT
                  c.id,
                  c.name,
                  COALESCE(json_agg(cf.feature_flag)
                    FILTER (WHERE cf.feature_flag IS NOT NULL), '[]') AS "featureFlags",
                  c.session_expiry_user_mins AS "sessionExpiryUserMins"
                FROM company c
                LEFT JOIN company_feature_flag cf ON c.id = cf.company
                GROUP BY c.id
            )
            SELECT
              u.id,
              u.email,
              u.full_name,
              u.password_hash,
              u.mfa_secret,
              u.email_verified,
              u.status,
              u.failed_login_attempts,
              u.last_locked,
              u.type,
              u.created,
              row_to_json(c.*) AS company,
              array(
                SELECT r.role_name AS name
                FROM role as r
                JOIN user_role ur ON ur.user_id = u.id AND ur.role_id = r.role_id
              ) as roles
            FROM ics_user u
            LEFT JOIN company c on c.id = u.company_id
            WHERE
              u.email = LOWER($1)
            AND u.status > 0
            ORDER BY status
            FETCH FIRST 1 ROWS ONLY
        `,
      [email]
    );
  } catch (err) {
    server.log.error(err);
    throw err;
  }
};

/**
 *
 *
 * @param {string} userId
 * @param {Number} failedLoginCount
 */
const updateUserLoginData = async (userId, failedLoginCount, lastLocked) => {
  try {
    const query = `
      UPDATE ics_user
      SET failed_login_attempts = $2,
          last_locked = ${lastLocked ? 'current_timestamp' : 'NULL'}
      WHERE id = $1
      AND status = 1
    `;

    // Execute the query
    await server.db.write.execute(query, [userId, failedLoginCount]);
  } catch (err) {
    server.log.error(err);
    throw err;
  }
};

/**
 *
 * @param {string} userId
 */

async function getPasswordLastChanged(userId) {
  const query = `
    SELECT
        uc.timestamp
    FROM ics_user iu 
    JOIN user_change uc ON uc.target_user_id = iu.id
    JOIN user_change_field ucf ON ucf.change_id = uc.id
    WHERE
       ucf.field_name = 'password_hash' 
       AND iu.status = 1 
       AND iu.id = $1
    ORDER BY uc.timestamp DESC 
    LIMIT 1;
  `;
  const result = await server.db.read.row(query, [userId]);
  if (result && result.timestamp) {
    const passwordLastChanged = result.timestamp;
    return new Date(passwordLastChanged).toUTCString();
  }
  return null;
}

module.exports = {
  recordAuthHistory,
  getUserByEmail,
  updateUserLoginData,
  getPasswordLastChanged,
};
