const { config } = require('../../env');
const { httpCode, loginFailedTypes } = require('../../lib/app-constants');

const isCaptchaEnabled = config.captcha && config.captcha.enabled;

const LOGIN_FAILED_RESPONSE = {
  [loginFailedTypes.USER_INACTIVE]: {
    status: httpCode.FORBIDDEN.STATUS_CODE,
    data: {
      message:
        'Your account has expired due to inactivity. Please contact your company admin.',
      isCaptchaEnabled,
    },
  },
  [loginFailedTypes.WRONG_EMAIL]: {
    status: httpCode.UNAUTHORIZED.STATUS_CODE,
    data: {
      failedStatus: true,
      isCaptchaEnabled,
    },
  },
  [loginFailedTypes.WRONG_PASSWORD]: {
    status: httpCode.UNAUTHORIZED.STATUS_CODE,
    data: {
      failedStatus: true,
      isCaptchaEnabled,
    },
  },
  [loginFailedTypes.WRONG_MFA]: {
    status: httpCode.NOT_ACCEPTABLE.STATUS_CODE,
    data: {
      message: 'MFA code validation failed',
      captcha: isCaptchaEnabled,
    },
  },
  [loginFailedTypes.CAPTCHA_FAILED]: {
    status: httpCode.UNAUTHORIZED.STATUS_CODE,
    data: {
      message: 'Authentication failed',
      isCaptchaEnabled,
    },
  },
  [loginFailedTypes.MFA_NOT_PROVIDED]: {
    status: httpCode.NO_CONTENT.STATUS_CODE,
  },
  [loginFailedTypes.EMAIL_NOT_VERIFIED]: {
    status: httpCode.UNAUTHORIZED.STATUS_CODE,
    data: {
      failedStatus: true,
      isCaptchaEnabled,
    },
  },
  [loginFailedTypes.USER_LOCKED]: {
    status: httpCode.UNAUTHORIZED.STATUS_CODE,
    data: {
      failedStatus: true,
      isLocked: true,
      message:
        'Your account has been locked due to multiple failed login attempts. Please try again in 30 minutes or contact your company admin',
    },
  },
  [loginFailedTypes.USER_LOCKED_RESET]: {
    status: httpCode.UNAUTHORIZED.STATUS_CODE,
    data: {
      failedStatus: true,
    },
  },
  [loginFailedTypes.PASSWORD_EXPIRED]: {
    status: httpCode.FORBIDDEN.STATUS_CODE,
    data: {
      message:
        "Please click on the 'Forgot Password' link to reset your password.",
      isCaptchaEnabled,
    },
  },
  default: {
    status: httpCode.UNAUTHORIZED.STATUS_CODE,
    data: {
      message: 'Authentication failed',
      isCaptchaEnabled,
    },
  },
};

const HUB_LOGIN_FAILED_RESPONSE = {
  [loginFailedTypes.USER_INACTIVE]: {
    status: httpCode.FORBIDDEN.STATUS_CODE,
    data: {
      message: 'User is blocked',
    },
  },
  [loginFailedTypes.WRONG_EMAIL]: {
    status: httpCode.UNAUTHORIZED.STATUS_CODE,
    data: {
      failedStatus: true,
      message: 'User does not exist in the system',
    },
  },
  [loginFailedTypes.EMAIL_NOT_VERIFIED]: {
    status: httpCode.UNAUTHORIZED.STATUS_CODE,
    data: {
      failedStatus: true,
      message: 'User email is unverified',
    },
  },
  default: {
    status: httpCode.UNAUTHORIZED.STATUS_CODE,
    data: {
      message: 'Authentication failed',
    },
  },
};

/**
 *
 *
 * @param {Number} loginFailedType
 * @returns
 */
const generateLoginFailedResponse = loginFailedType =>
  LOGIN_FAILED_RESPONSE[loginFailedType] || LOGIN_FAILED_RESPONSE.default;

const generateHubLoginFailedResponse = loginFailedType =>
  HUB_LOGIN_FAILED_RESPONSE[loginFailedType] ||
  HUB_LOGIN_FAILED_RESPONSE.default;

module.exports = {
  generateLoginFailedResponse,
  generateHubLoginFailedResponse,
};
