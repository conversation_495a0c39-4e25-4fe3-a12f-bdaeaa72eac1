const logger = require('../../lib/logger').mainLogger();
const { httpCode } = require('../../lib/app-constants');
const { config } = require('../../env');
const authHelper = require('../../helpers/auth-helper');
const totp = require('../../lib/totp');
const { server } = require('../../app');
const authenticateUserRepository = require('./authenticate-user.repository');

/**
 *
 *
 * @param {*} req
 * @returns
 */
const generateMFANotConfiguredResponse = async req => {
  const mfaTotp = await totp.createTotp(req);

  return {
    status: httpCode.ACCEPTED.STATUS_CODE,
    data: {
      issuer: config.auth.issuer,
      secret: {
        ascii: mfaTotp.ascii,
        base32: mfaTotp.base32,
        hex: mfaTotp.hex,
      },
      otpURL: mfaTotp.otpauth_url,
      qrCodeData: mfaTotp.image.data,
    },
  };
};

/**
 *
 *
 * @param {*} req
 * @param {string} email
 * @returns
 */
const processNoMfaSetup = async (req, email) => {
  logger.info(`User email ${email} MFA is not configured`);
  // eslint-disable-next-line no-return-await
  return await generateMFANotConfiguredResponse(req);
};

/**
 *
 *
 * @param {authenticateUserRepository.User} user
 * @returns
 */
const processSuccessfulLogin = async user => {
  // At this point, the user has valid password and is active in the system
  // Reset the failed count, last_locked
  if (user.failedLoginAttempts > 0) {
    await authenticateUserRepository.updateUserLoginData(user.id, 0, null);
  }

  authenticateUserRepository
    .recordAuthHistory(user.id, 'email-password-mfa', true)
    .catch(reason => {
      logger.error(`Failed to record auth history, ${reason}`);
    });

  const flagInstance = await server.appFeatureFlag.getInstance();
  const flags = flagInstance.fetchAllFeatureFlags();
  const authResponse = authHelper.createAuthResp(user, flags);
  return {
    status: httpCode.OK.STATUS_CODE,
    data: authResponse,
  };
};

/**
 *
 *
 * @param {*} req
 * @param {authenticateUserRepository.User} user
 * @returns
 */
const processLogin = async (req, user) => {
  if (!user.mfaSecret) {
    return processNoMfaSetup(req, user.email);
  }
  return processSuccessfulLogin(user);
};

module.exports = { processLogin, processSuccessfulLogin };
