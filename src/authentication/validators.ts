/* eslint-disable import/no-unused-modules */
import * as recaptcha from '../../lib/recaptcha';
import { mainLogger } from '../../lib/logger';
import * as hashPassword from '../../lib/hash-password';
import {
  loginFailedTypes,
  userStatus,
  httpCode,
} from '../../lib/app-constants';
import * as totp from '../../lib/totp';
import * as authenticateUserRepository from './authenticate-user.repository';

const moment = require('moment');
const { config } = require('../../env');

const logger = mainLogger();

class LoginValidationError extends Error {
  failedType: number;

  user: any;

  /**
   * Creates an instance of LoginValidationError.
   * @param {number} failedType
   * @param {authenticateUserRepository.User} user
   * @memberof LoginValidationError
   */
  constructor(failedType: number, user: any) {
    super();
    this.failedType = failedType;
    this.user = user;
  }
}

const validateCaptcha =
  (isCaptchaEnabled: boolean) => async (req: any, res: any, next: any) => {
    if (isCaptchaEnabled) {
      const { captchaResponse } = req.body;
      if (!captchaResponse) {
        res.send(httpCode.UNAUTHORIZED.STATUS_CODE, {
          message: 'Authentication failed',
          isCaptchaEnabled,
        });
        return;
      }

      logger.info(`Check captcha for email ${req.body.email}`);
      const { success: captchaValidationSuccess } =
        await recaptcha.validateRecaptcha(captchaResponse);

      if (!captchaValidationSuccess) {
        res.send(httpCode.UNAUTHORIZED.STATUS_CODE, {
          message: 'Authentication failed',
          isCaptchaEnabled,
        });
        return;
      }
    }
    next();
  };

/**
 *
 *
 * @param {string} email
 * @param {string} password
 * @returns {Promise<authenticateUserRepository.User>} user
 */
const validateAuthRequest = async (email: string, password: string) => {
  try {
    const user = await authenticateUserRepository.getUserByEmail(email);
    const isPCIDSSEnabledForTenant =
      user?.company?.featureFlags?.includes('PCI_DSS_COMPLIANCE') ?? false;
    if (!user) {
      throw new LoginValidationError(loginFailedTypes.WRONG_EMAIL, user);
    }

    if (isPCIDSSEnabledForTenant && user.lastLocked !== null) {
      const lastLocked = moment.utc(user.lastLocked);
      const currentTime = moment.utc();
      const differenceInMinutes = currentTime.diff(lastLocked, 'minutes');
      if (differenceInMinutes <= (config.auth.lockedUserTimeFrame || 30)) {
        logger.debug({ user }, 'User is locked');
        throw new LoginValidationError(loginFailedTypes.USER_LOCKED, user);
      } else if (!(await hashPassword.verify(password, user.passwordHash))) {
        user.failedLoginAttempts = 0;
        user.lastLocked = null;
        logger.debug({ user }, 'Invalid password');
        throw new LoginValidationError(
          loginFailedTypes.USER_LOCKED_RESET,
          user
        );
      }
    }

    if (!user.emailVerified) {
      logger.debug({ user }, 'Email not verified');
      throw new LoginValidationError(loginFailedTypes.EMAIL_NOT_VERIFIED, user);
    }

    if (user.status === userStatus.IN_ACTIVE) {
      throw new LoginValidationError(loginFailedTypes.USER_INACTIVE, user);
    }

    if (isPCIDSSEnabledForTenant) {
      const passwordExpired = await isPasswordExpired(user.id);
      if (passwordExpired) {
        throw new LoginValidationError(loginFailedTypes.PASSWORD_EXPIRED, user);
      }
    }

    if (password && !(await hashPassword.verify(password, user.passwordHash))) {
      if (isPCIDSSEnabledForTenant && user.failedLoginAttempts + 1 === 4) {
        logger.debug({ user }, 'User is locked due to failed login attempts');
        throw new LoginValidationError(loginFailedTypes.USER_LOCKED, user);
      }
      logger.debug({ user }, 'Invalid password');
      throw new LoginValidationError(loginFailedTypes.WRONG_PASSWORD, user);
    }
    return user;
  } catch (err) {
    logger.error({ err }, '[validateAuthRequest]');
    throw err;
  }
};

/**
 *
 *
 * @param {*} req
 * @param {authenticateUserRepository.User} user
 * @param {string} mfaCode
 */
const validateMfa = async (req: any, user: any, mfaCode: string) => {
  const { mfaSecret } = user;
  if (mfaSecret) {
    const isRequiredMfaProvided = !!(mfaCode && mfaCode.length);
    const isMfaCodeValid =
      isRequiredMfaProvided &&
      (await totp.validateTotp(req, mfaSecret, mfaCode));

    if (!isRequiredMfaProvided) {
      logger.info(
        `[Authn] Password was validated, mfaCode is not provided. User: ${user.email}.`
      );
      throw new LoginValidationError(loginFailedTypes.MFA_NOT_PROVIDED, user);
    }
    if (!isMfaCodeValid) {
      logger.info(
        `[Authn] MFA code validation failed. MFA code: ${mfaCode}. User: ${user.email}.`
      );
      throw new LoginValidationError(loginFailedTypes.WRONG_MFA, user);
    }
  }
};

async function isPasswordExpired(userId: string): Promise<boolean> {
  const passwordLastChanged =
    await authenticateUserRepository.getPasswordLastChanged(userId);

  if (!passwordLastChanged) {
    throw new Error('Unable to fetch password change history');
  }

  const currentTime: number = new Date().getTime();
  const passwordLastChangedDate: number = new Date(
    passwordLastChanged
  ).getTime();

  const passwordExpirationDays = config.passwordExpirationDays || 90;

  const daysSinceLastChange =
    (currentTime - passwordLastChangedDate) / (1000 * 60 * 60 * 24);
  return daysSinceLastChange > passwordExpirationDays;
}

export {
  validateCaptcha,
  validateAuthRequest,
  validateMfa,
  LoginValidationError,
};
