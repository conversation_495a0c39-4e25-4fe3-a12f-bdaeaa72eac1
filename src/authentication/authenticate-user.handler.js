const errorHandler = require('../../lib/errorhandler');
const authenticateUserService = require('./authenticate-user.service');

module.exports = {
  authenticateUser: async (req, res, next) => {
    try {
      const result = await authenticateUserService.authenticateUser(req);
      if (result && !res.headersSent) {
        res.send(result.status, result.data);
      }
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
    return null;
  },

  authenticateUserHub: async (req, res, next) => {
    try {
      const result = await authenticateUserService.authenticateUserHub(req);
      res.send(result.status, result.data);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
