/* eslint-disable import/no-unused-modules */
import { iBaseEntity } from '../../lib/entities.types';

export interface iSiteEntity extends iBaseEntity {
  siteId: string;
  siteName: string;
  ipAddress?: string;
  addressLine1?: string;
  addressLine2?: string;
  addressLine3?: string;
  phoneNumber?: string;
  faxNumber?: string;
  mobileNumber?: string;
  contactPerson?: string;
  contactEmail?: string;
  latitude?: string;
  longitude?: string;
  maxUploadRateKb: number;
  messagingPort?: number;
  uploadPort?: number;
  active?: boolean;
  addressJson?: string;
  deleteTimestamp?: Date;
  dateUpdated?: Date;
  formattedAddress?: string;
  companyId?: string;
  keyGroupId?: string;
  status: number;
  referenceId?: string;
  created: Date;
  timezone_id?: string;
  visible?: boolean;
  hours?: { openAt: number; closeAt: number }[];
  suppressOffhoursAlarm?: boolean;
  isProduction?: boolean;
  integrationId?: string;
}
