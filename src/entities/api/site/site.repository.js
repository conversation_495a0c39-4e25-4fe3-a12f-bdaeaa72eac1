const { mapKeys, camelCase } = require('lodash');

const BaseRepository = require('../../lib/base.repository');
const {
  buildQuery,
  prepareSelectFields,
  matchAndReplaceKey,
  prepareSortFields,
  wrapQueryWithPagination,
} = require('../../lib/entities.helper');
const { roles: rolesConstant } = require('../../../../lib/app-constants');
const SiteEntity = require('./site.entity');

/**
 * @class SiteRepository
 * @extends BaseRepository
 */
class SiteRepository extends BaseRepository {
  constructor() {
    super(SiteEntity);
  }

  async findSiteWithSiteTagsAndAttribs(
    { fields = [], filters, sort, pageIndex = 0, pageSize = 0 },
    userRoles
  ) {
    const createColumnMapping = columnFields => {
      const customAttribsMap = {
        'customAttributes.attributeOverrideValue':
          'caev.attribute_override_value',
        'customAttributes.attributeDefinitionId': 'ad.attribute_definition_id',
        customAttributes: null,
      };

      const returnMap = {
        siteTags: 'array_agg(DISTINCT tag."name")',
      };
      const jsonBuildObjectArray = [];

      // TODO: Refactor below condition and this function itself.
      // eslint-disable-next-line no-restricted-syntax
      for (const [key, value] of Object.entries(customAttribsMap)) {
        const isDotted = key.split('.');
        if (columnFields.includes(key) && key === 'customAttributes') {
          returnMap.customAttributes = `json_agg(
                        DISTINCT jsonb_build_object(
                            'attributeDefinitionId', ad.attribute_definition_id,
                            'attributeOverrideValue', COALESCE(caev.attribute_override_value, ad.attribute_value)
                        ) )
                    `;

          return returnMap;
        }

        if (columnFields.includes(key)) {
          const columnName = isDotted[1];
          const selectClause = value;
          jsonBuildObjectArray.push(...[`'${columnName}'`, selectClause]);
        }
      }

      if (jsonBuildObjectArray.length > 0) {
        returnMap.customAttributes = `
                    json_agg( DISTINCT jsonb_build_object( ${jsonBuildObjectArray.join(
                      ', '
                    )} ) )
                `;
      }

      if (columnFields.includes('defaultSite')) {
        returnMap.featureFlag = 'cff.feature_flag';
      }

      return returnMap;
    };

    const { columnMapping } = this.entityClass.getEntityMeta();
    const columnMappingSelect = {
      ...columnMapping,
      ...createColumnMapping(fields),
    };
    const columnMappingWhere = {
      ...columnMapping,
      ...{
        siteTags: 'tag.name',
        'customAttributes.attributeOverrideValue':
          'caev.attribute_override_value',
        'customAttributes.attributeDefinitionId':
          'caev.attribute_definition_id',
      },
    };
    const joins = [];
    const groupBy = ['site.site_id'];

    // Join on siteTag related operations.
    if (
      fields.includes('siteTags') ||
      sort.includes('siteTags:asc') ||
      sort.includes('siteTags:desc') ||
      Object.keys(filters).includes('siteTags')
    ) {
      joins.push(
        ...[
          {
            type: 'LEFT',
            tableName: 'site_tag',
            on: 'site_tag.site_id = site.site_id AND site_tag.deleted = FALSE',
          },
          {
            type: 'LEFT',
            tableName: 'tag',
            on: 'site_tag.tag_id = tag.id',
          },
        ]
      );
    }

    // Join on device related operations.
    if (
      fields.includes('totalDevices') ||
      sort.includes('totalDevices:asc') ||
      sort.includes('totalDevices:desc') ||
      Object.keys(filters).includes('device')
    ) {
      joins.push(
        ...[
          {
            type: 'LEFT',
            tableName: `(
                SELECT 
                    t.site_id,
                    mv_dh.health,
                    COUNT(t.target_id) AS count
                FROM 
                    target t
                INNER JOIN 
                    mv_device_health mv_dh ON t.target_id = mv_dh.target_id
                WHERE 
                    t.active
                GROUP BY 
                    mv_dh.health, t.site_id
            )AS cc`,
            on: 'cc.site_id = site.site_id',
          },
        ]
      );
    }

    // Join on customAttributes related operations.
    if (
      fields.includes('customAttributes') ||
      fields.includes('customAttributes.attributeOverrideValue') ||
      fields.includes('customAttributes.attributeDefinitionId') ||
      Object.keys(filters).includes('customAttributes') ||
      Object.keys(filters).includes(
        'customAttributes.attributeOverrideValue'
      ) ||
      Object.keys(filters).includes('customAttributes.attributeDefinitionId')
    ) {
      joins.push(
        ...[
          {
            type: 'LEFT',
            tableName: 'custom_attribute.attribute_definition ad',
            on: 'site.company_id = ad.company_id AND searchable_attribute = true AND ad.entity_type = 2 AND ad.deleted = FALSE',
          },
          {
            type: 'LEFT',
            tableName: 'custom_attribute.custom_attribute_entity_values caev',
            on: 'caev.attribute_definition_id = ad.attribute_definition_id AND caev.entity_id = site.site_id AND caev.deleted = FALSE',
          },
        ]
      );

      // eslint-disable-next-line no-param-reassign
      fields = fields.filter(x => !x.startsWith('customAttributes')); // Replace to top level k/v
      fields.push('customAttributes');
    }

    if (!userRoles.includes(rolesConstant.ICS_SYSTEM)) {
      joins.push({
        type: 'LEFT',
        tableName: 'user_site_authorization usa',
        on: 'usa.site_id = site.site_id',
      });

      joins.push({
        type: 'LEFT',
        tableName: 'authorization_site_group_site asgs',
        on: 'asgs.site_id = site.site_id',
      });

      joins.push({
        type: 'LEFT',
        tableName: 'user_group_authorization_site_group ugasg',
        on: 'asgs.authorization_site_group_id = ugasg.authorization_site_group_id',
      });

      joins.push({
        type: 'LEFT',
        tableName: 'user_group_user ugu',
        on: 'ugasg.user_group_id = ugu.user_group_id',
      });
    }

    if (fields.includes('defaultSite')) {
      joins.push(
        ...[
          {
            type: 'LEFT',
            tableName: 'company c',
            on: 'c.default_site = site.site_id',
          },
          {
            type: 'LEFT',
            tableName: 'company_feature_flag cff',
            on: `cff.company = site.company_id and cff.feature_flag = 'BLUEFIN'`,
          },
        ]
      );

      fields.push('featureFlag');
      groupBy.push('c.default_site', 'cff.feature_flag');
    }
    const { query, queryParams } = buildQuery({
      tableName: this.entityClass.getEntityMeta().tableName,
      fields: prepareSelectFields(fields, columnMappingSelect),
      filters: matchAndReplaceKey(filters, columnMappingWhere),
      joins,
      groupBy,
      sort: prepareSortFields(sort, columnMapping),
    });
    const paginatedQuery = wrapQueryWithPagination(
      query,
      queryParams,
      pageIndex,
      pageSize
    );
    const result = await this.dataContext.row(paginatedQuery, queryParams);

    if (result.results) {
      result.results = result.results.map(m => {
        // Create a new object based on m
        const updatedM = { ...m };

        if (updatedM.total_devices) {
          const totalDevices = updatedM.total_devices;

          // Check if totalDevices is null or not an object
          if (totalDevices === null || typeof totalDevices !== 'object') {
            updatedM.total_devices = 0; // Set to 0 if null or not an object
          } else {
            // If it's an object, sum its values
            updatedM.total_devices = Object.values(totalDevices).reduce(
              (acc, value) => acc + value,
              0
            );
          }
        } else {
          // If total_devices doesn't exist, set it to 0
          updatedM.total_devices = 0;
        }

        if (updatedM.feature_flag) {
          updatedM.is_default_site = Boolean(updatedM.default_site);
        }

        delete updatedM.feature_flag;
        delete updatedM.default_site;
        return mapKeys(updatedM, (v, k) => camelCase(k));
      });
    }

    return {
      resultsMetadata: {
        totalResults: result.totalCount,
        pageIndex,
        pageSize,
      },
      results: result.results || [],
    };
  }
}

module.exports = SiteRepository;
