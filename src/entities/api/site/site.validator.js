const Joi = require('joi');

const {
  buildFieldsValidator,
  buildFiltersValidator,
  buildSortValidator,
  buildPageSizeAndIndexValidator,
} = require('../../lib/base.validator');
const {
  stringOpts,
  arrayOpts,
  equalityOpts,
  minPageSize,
  minPageIndex,
  maxPageSize,
} = require('../../lib/entities.constant');
const { validateParams } = require('../../../../lib/pre');
const env = require('../../../../env');
const {
  SITE_MAX_PAGE_SIZE: defaultSiteMaxPageSize,
} = require('./site.constant');

const allowededFilters = {
  siteId: {
    operators: [...equalityOpts, ...arrayOpts],
    validator: Joi.string().guid(),
  },
  siteName: {
    operators: [...equalityOpts, ...stringOpts],
    validator: Joi.string().regex(/^[A-Za-z0-9'._\-#$&/, ]+$/),
  },
  visible: {
    operators: [...equalityOpts],
    validator: Joi.boolean(),
  },
  siteTags: {
    operators: [...stringOpts, ...equalityOpts, ...arrayOpts],
    validator: Joi.string(),
  },
  'customAttributes.attributeDefinitionId': {
    operators: [...equalityOpts, ...arrayOpts],
    validator: Joi.number(),
  },
  'customAttributes.attributeOverrideValue': {
    operators: [...stringOpts, ...equalityOpts, ...arrayOpts],
    validator: Joi.string(),
  },
  integrationId: {
    operators: [...equalityOpts, ...arrayOpts],
    validator: Joi.string(),
  },
};

const getSitesRequestValidator = SITE_MAX_PAGE_SIZE =>
  Joi.object({
    ...buildPageSizeAndIndexValidator(
      minPageSize,
      SITE_MAX_PAGE_SIZE,
      maxPageSize,
      minPageIndex
    ),
    fields: buildFieldsValidator(
      [
        'companyId',
        'siteId',
        'formattedAddress',
        'totalDevices',
        'status',
        'siteName',
        'visible',
        'siteTags',
        'integrationId',
        'customAttributes',
        'customAttributes.attributeDefinitionId',
        'customAttributes.attributeOverrideValue',
        'defaultSite',
      ],
      ['siteId', 'siteName', 'visible']
    ),
    filters: buildFiltersValidator(allowededFilters),
    sort: buildSortValidator(['siteId', 'siteName'], ['siteName:asc']),
  });

// eslint-disable-next-line consistent-return
const reqConversionForRdm = async (req, _res, next) => {
  if (req.body.integrationId.length > 0) {
    req.params.filters = {
      integrationId: {
        $in: req.body.integrationId,
      },
    };
    req.params.pageSize = req.body.integrationId.length;
    return next();
  }
};

const dynamicValidateParams = (req, res, next) => {
  let effectiveSiteMaxPageSize = defaultSiteMaxPageSize;
  if (
    req.method === 'POST' &&
    // eslint-disable-next-line no-underscore-dangle
    req._url.pathname.startsWith(`/${env.config.base}/entities/sites`)
  ) {
    effectiveSiteMaxPageSize = req.params.pageSize;
  }
  const validator = getSitesRequestValidator(effectiveSiteMaxPageSize);
  validateParams(validator, false, true)(req, res, next);
};

module.exports = {
  getSitesRequestValidator,
  reqConversionForRdm,
  dynamicValidateParams,
};
