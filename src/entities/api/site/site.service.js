const { merge, has } = require('lodash');

const { roles: rolesConstant } = require('../../../../lib/app-constants');
const SiteRepository = require('./site.repository');

const getSitesService = async (params, user) => {
  const userRoles = user.getRoles();
  const userId = user.sub;
  const uParams = params;
  uParams.filters = merge(
    {
      active: { $eq: true },
    },
    uParams.filters
  );
  if (!userRoles.includes(rolesConstant.ICS_SYSTEM)) {
    uParams.filters = merge(
      {
        userId: { $eq: userId },
      },
      uParams.filters
    );
  }
  if (has(params, 'allSites') && !params.allSites) {
    const userCompanyId = user.company.id;
    uParams.filters = merge(
      {
        companyId: { $eq: userCompanyId },
      },
      uParams.filters
    );
  }
  const repo = new SiteRepository();
  // eslint-disable-next-line no-return-await
  return await repo.findSiteWithSiteTagsAndAttribs(uParams, userRoles);
};

module.exports = {
  getSitesService,
};
