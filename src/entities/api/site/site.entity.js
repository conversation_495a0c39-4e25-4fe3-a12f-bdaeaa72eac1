const BaseEntity = require('../../lib/base.entity');

/**
 * @class SiteEntity
 * @extends {BaseEntity}
 */
class SiteEntity extends BaseEntity {
  static getEntityMeta() {
    return {
      tableName: 'site',
      primaryKey: 'site_id',
      columnMapping: {
        siteId: 'site.site_id',
        siteName: 'site.name',
        ipAddress: 'site.ip_address',
        addressLine1: 'site.address_line_1',
        addressLine2: 'site.address_line_2',
        addressLine3: 'site.address_line_3',
        phoneNumber: 'site.phone_number',
        faxNumber: 'site.fax_number',
        mobileNumber: 'site.mobile_number',
        contactPerson: 'site.contact_person',
        contactEmail: 'site.contact_email',
        latitude: 'site.latitude',
        longitude: 'site.longitude',
        maxUploadRateKb: 'site.max_upload_rate_kb',
        messagingPort: 'site.messaging_port',
        uploadPort: 'site.upload_port',
        active: 'site.active',
        addressJson: 'site.address_json',
        deleteTimestamp: 'site.delete_timestamp',
        dateUpdated: 'site.date_updated',
        formattedAddress: 'site.formatted_address',
        companyId: 'site.company_id',
        keyGroupId: 'site.key_group_id',
        status: 'site.status',
        referenceId: 'site.reference_id',
        created: 'site.created',
        timezoneId: 'site.timezone_id',
        visible: 'site.visible',
        hours: 'site.hours',
        suppressOffhoursAlarm: 'site.suppress_offhours_alarm',
        isProduction: 'site.is_production',
        integrationId: 'site.integration_id',
        userId: 'usa.user_id',
        totalDevices:
          'JSONB_OBJECT_AGG(cc.health, cc.count) FILTER (WHERE cc.health IS NOT NULL)',
        defaultSite: 'c.default_site',
      },
    };
  }
}

module.exports = SiteEntity;
