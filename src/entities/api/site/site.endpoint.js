const Joi = require('joi');
const { server } = require('../../../../app');
const env = require('../../../../env');
const { baseRoleGroup } = require('../../../../lib/app-constants');
const { requiresRole, validateBody } = require('../../../../lib/pre');
const { getSitesHandler } = require('./site.handler');
const {
  reqConversionForRdm,
  dynamicValidateParams,
} = require('./site.validator');

const BASE_PATH = `${env.config.base}/entities`;

/**
 * @swagger
 * /entities/sites:
 *   get:
 *     tags:
 *       - entities
 *     summary: Retrieve site data
 *     parameters:
 *       - $ref: '#/parameters/AuthorizationTokenParam'
 *       - $ref: '#/parameters/pageIndexParam'
 *       - $ref: '#/parameters/pageSizeParam'
 *       - name: allSites
 *         in: query
 *         description: get all the sites or user company id base
 *         type: boolean
 *         required: false
 *         default: true
 *       - name: fields[]
 *         in: query
 *         description: Fields to retrieve ["companyId", "siteId", "siteName", "visible", "siteTags", "integrationId", "customAttributes", "customAttributes.attributeDefinitionId", "customAttributes.attributeOverrideValue", "formattedAddress"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *         default: ["siteId", "siteName", "visible" ]
 *       - name: filters[siteId][$operator]
 *         in: query
 *         description: Filter by site ID "$eq, $ne, $in"
 *         type: string
 *         required: false
 *       - name: filters[siteName][$operator]
 *         in: query
 *         description: Filter by site name "$eq, $ne, $contains, $notContains, $containsi, $notContainsi, $startsWith, $endsWith"
 *         type: string
 *         required: false
 *       - name: filters[visible][$operator]
 *         in: query
 *         description: Filter by visibility "$eq, $ne"
 *         type: string
 *         required: false
 *       - name: filters[siteTags][$operator]
 *         in: query
 *         description: Filter by site tags "$eq, $ne, $contains, $notContains, $containsi, $notContainsi, $startsWith, $endsWith, $in, $notIn"
 *         type: string
 *         required: false
 *       - name: filters[customAttributes.attributeDefinitionId][$operator]
 *         in: query
 *         description: Filter by custom attribute definition ID "$eq, $ne, $in, $notIn"
 *         type: string
 *         required: false
 *       - name: filters[customAttributes.attributeOverrideValue][$operator]
 *         in: query
 *         description: Filter by custom attribute override value "$eq, $ne, $in, $notIn, $contains, $notContains, $containsi, $notContainsi, $startsWith, $endsWith"
 *         type: string
 *         required: false
 *       - name: filters[integrationId][$operator]
 *         in: query
 *         description: Filter by integration ID "$eq, $ne, $in"
 *         type: string
 *         required: false
 *       - name: sort[]
 *         in: query
 *         description: Fields to sort by ["siteId:asc", "siteName:asc"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *         default: ["siteName:asc"]
 *     responses:
 *       200:
 *         description: Site data retrieved successfully
 *         schema:
 *           type: object
 *           properties:
 *             resultsMetadata:
 *               $ref: '#/definitions/ResultsMetadata'
 *             results:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   companyId:
 *                     type: string
 *                   siteId:
 *                     type: string
 *                   siteName:
 *                     type: string
 *                   visible:
 *                     type: boolean
 *                   siteTags:
 *                     type: array
 *                     items:
 *                       type: string
 *                   customAttributes:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         attributeDefinitionId:
 *                           type: integer
 *                         attributeOverrideValue:
 *                           type: string
 * */
server.get(
  {
    path: `${BASE_PATH}/sites`,
    version: '0.0.1',
  },
  [requiresRole(baseRoleGroup.ALL), dynamicValidateParams, getSitesHandler]
);

server.post(
  {
    path: `${BASE_PATH}/sites`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    validateBody({
      integrationId: Joi.array().items(Joi.string()).min(1).required(),
    }),
    reqConversionForRdm,
    dynamicValidateParams,
    getSitesHandler,
  ]
);
