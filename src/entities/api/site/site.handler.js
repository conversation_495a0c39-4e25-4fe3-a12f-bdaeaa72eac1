const errorhandler = require('../../../../lib/errorhandler');
const { getSitesService } = require('./site.service');

const getSitesHandler = async (req, res, next) => {
  try {
    const { user, params } = req;
    const result = await getSitesService(params, user);
    res.send(result);
    return next();
  } catch (err) {
    return errorhandler.onError(req, res, next)(err);
  }
};

module.exports = {
  getSitesHandler,
};
