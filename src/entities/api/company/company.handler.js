const { merge } = require('lodash');

const errorhandler = require('../../../../lib/errorhandler');
const { roles } = require('../../../../lib/app-constants');
const { getCompaniesService } = require('./company.service');

const getCompaniesHandler = async (req, res, next) => {
  try {
    const { user, params } = req;

    const userCompanyId = user.company.id;

    // Limit search to user company
    const uParams = params;

    const userRoles = req.user.getRoles();
    if (!userRoles.includes(roles.ICS_SYSTEM)) {
      uParams.filters = merge(
        { companyId: { $eq: userCompanyId } },
        uParams.filters
      );
    }
    const result = await getCompaniesService(uParams);

    res.send(result);
    return next();
  } catch (err) {
    return errorhandler.onError(req, res, next)(err);
  }
};

module.exports = {
  getCompaniesHandler,
};
