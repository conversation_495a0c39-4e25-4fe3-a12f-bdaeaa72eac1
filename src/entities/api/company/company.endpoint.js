const { server } = require('../../../../app');
const env = require('../../../../env');
const { baseRoleGroup } = require('../../../../lib/app-constants');
const { validateParams, requiresRole } = require('../../../../lib/pre');
const { getCompaniesHandler } = require('./company.handler');
const { getCompaniesRequestValidator } = require('./company.validator');

const BASE_PATH = `${env.config.base}/entities`;

/**
 * @swagger
 * /entities/companies:
 *   get:
 *     tags:
 *       - entities
 *     summary: Retrieve company data
 *     parameters:
 *       - $ref: '#/parameters/AuthorizationTokenParam'
 *       - $ref: '#/parameters/pageIndexParam'
 *       - name: pageSize
 *         in: query
 *         description: The maximum number of records to return in a page
 *         required: false
 *         type: integer
 *         default: 40
 *       - name: fields[]
 *         in: query
 *         description: Fields to retrieve ["companyId", "companyName", "featureFlags"]
 *         type: array
 *         items:
 *           type: string
 *         default: ["companyId", "companyName", "featureFlags"]
 *         required: false
 *       - name: filters[companyId][$operator]
 *         in: query
 *         description: Filter by company id "$eq, $in".
 *         type: string
 *         required: false
 *       - name: sort[]
 *         in: query
 *         description: Fields to sort by ["companyId:asc", "companyId:desc", "companyName:asc", "companyName:asc"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *     responses:
 *       200:
 *         description: Company data retrieved successfully
 *         schema:
 *           type: object
 *           properties:
 *             resultsMetadata:
 *               $ref: '#/definitions/ResultsMetadata'
 *             results:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   companyId:
 *                     type: string
 *                     example: "5dd04653-b61b-4763-b31d-5f55c8b20d40"
 *                   companyName:
 *                     type: string
 *                     example: "Invenco"
 *                   featureFlags:
 *                     type: array
 *                     items:
 *                        type: string
 *                        example: "MEDIA"
 *                     example: ["MEDIA", "OFFLINE_RKI"]
 *       400:
 *         $ref: "#/responses/BadRequest"
 *       401:
 *         $ref: "#/responses/AuthenticationFailed"
 *       403:
 *         $ref: "#/responses/Forbidden"
 *       500:
 *         $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/companies`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    validateParams(getCompaniesRequestValidator, false, true),
    getCompaniesHandler,
  ]
);
