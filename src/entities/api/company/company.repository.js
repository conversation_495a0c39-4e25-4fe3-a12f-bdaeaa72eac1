const { mapKeys, camelCase } = require('lodash');
const {
  buildQuery,
  prepareSelectFields,
  matchAndReplaceKey,
  prepareSortFields,
  wrapQueryWithPagination,
} = require('../../lib/entities.helper');
const BaseRepository = require('../../lib/base.repository');
const CompanyEntity = require('./company.entity');

class CompanyRepository extends BaseRepository {
  constructor() {
    super(CompanyEntity);
  }

  async get(id) {
    return super.get(id);
  }

  /**
   * @returns {Promise<iResponse<CompanyEntity>>}
   */
  async findCompanyWithFilter({
    fields = [],
    filters,
    sort,
    pageIndex = 0,
    pageSize = 0,
  }) {
    const tmpTables = [];
    const joins = [];
    const { columnMapping } = this.entityClass.getEntityMeta();
    if (fields.includes('featureFlags')) {
      tmpTables.push({
        tableName: 'company_feature_flag_tmp',
        content:
          'SELECT company as company_id, array_agg(feature_flag) AS feature_flags FROM company_feature_flag GROUP BY company',
      });
      joins.push({
        type: 'INNER',
        tableName: 'company_feature_flag_tmp',
        on: 'company_feature_flag_tmp.company_id = company.id',
      });
      columnMapping.featureFlags = 'feature_flags';
    }

    const { query, queryParams } = buildQuery({
      tableName: this.entityClass.getEntityMeta().tableName,
      fields: prepareSelectFields(fields, columnMapping),
      filters: matchAndReplaceKey(filters, columnMapping),
      joins,
      groupBy: null,
      sort: prepareSortFields(sort, columnMapping),
    });

    const paginatedQuery = wrapQueryWithPagination(
      query,
      queryParams,
      pageIndex,
      pageSize,
      tmpTables
    );

    const result = await this.dataContext.row(paginatedQuery, queryParams);

    if (result.results) {
      result.results = result.results.map(m =>
        mapKeys(m, (v, k) => camelCase(k))
      );
    }

    return {
      resultsMetadata: {
        totalResults: result.totalCount,
        pageIndex,
        pageSize,
      },
      results: result.results || [],
    };
  }
}

module.exports = CompanyRepository;
