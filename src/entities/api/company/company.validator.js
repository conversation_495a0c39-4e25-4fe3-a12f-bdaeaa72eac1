const Joi = require('joi');
const { arrayOpts, equalityOpts } = require('../../lib/entities.constant');

const {
  baseEntityValidatorObjectFields,
  buildSortValidator,
  buildFiltersValidator,
  buildFieldsValidator,
} = require('../../lib/base.validator');

const allowdedFields = ['companyId', 'companyName', 'featureFlags'];
const allowededFilters = {
  companyId: {
    operators: [...equalityOpts, ...arrayOpts],
    validator: Joi.string().guid(),
  },
};
const allowdedSort = ['companyId', 'companyName'];

const getCompaniesRequestValidator = Joi.object({
  ...baseEntityValidatorObjectFields,
  fields: buildFieldsValidator(allowdedFields),
  filters: buildFiltersValidator(allowededFilters),
  sort: buildSortValidator(allowdedSort),
}).unknown(false);

module.exports = {
  getCompaniesRequestValidator,
};
