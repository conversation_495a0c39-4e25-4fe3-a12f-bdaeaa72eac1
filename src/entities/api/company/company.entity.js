const BaseEntity = require('../../lib/base.entity');

class CompanyEntity extends BaseEntity {
  static getEntityMeta() {
    return {
      tableName: 'company',
      primaryKey: 'id',
      columnMapping: {
        companyId: 'id',
        companyName: 'name',
      },
    };
  }

  /**
   * @param {import('./company.types').iCompanyEntity} entityObj
   */
  set(entityObj) {
    super.set(entityObj);
  }
}

module.exports = CompanyEntity;
