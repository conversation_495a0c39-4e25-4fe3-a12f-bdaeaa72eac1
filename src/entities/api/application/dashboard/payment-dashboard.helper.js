const getAppsCount = results =>
  results.reduce(
    (acc, device) => {
      const runningCount = device.applications.filter(app =>
        ['running', 'Default', ''].includes(app.status)
      ).length;
      const waitingCount = device.applications.filter(app =>
        ['waiting'].includes(app.status)
      ).length;
      const offlineCount = device.applications.filter(app =>
        ['offline'].includes(app.status)
      ).length;
      acc.running += runningCount;
      acc.waiting += waitingCount;
      acc.offline += offlineCount;
      return acc;
    },
    { running: 0, waiting: 0, offline: 0 }
  );
const getAppWiseDeviceCount = results =>
  results.reduce(
    (acc, device) => {
      const runningCount =
        device.applications.length > 0 &&
        device.applications.every(app =>
          ['running', 'Default', ''].includes(app.status)
        );
      const waitingCount =
        device.applications.length > 0 &&
        device.applications.some(app => ['waiting'].includes(app.status));
      if (runningCount) acc.running += 1;
      if (waitingCount) acc.waiting += 1;
      acc.offline = results.length - (acc.running + acc.waiting);
      return acc;
    },
    { running: 0, waiting: 0, offline: 0 }
  );
module.exports = {
  getAppsCount,
  getAppWiseDeviceCount,
};
