const BaseRepository = require('../../../lib/base.repository');
const IcsConfigEntity = require('./ics-config.entity');

class IcsConfigRepository extends BaseRepository {
  constructor() {
    super(IcsConfigEntity);
  }

  async get(id) {
    return super.get(id);
  }

  /**
   * @param {import('../../lib/base.types').iFindOptions} param
   * @returns {Promise<iResponse<IcsConfigEntity>>}
   */
  async findAll({ filters, fields, sort }) {
    return super.findAll({ filters, fields, sort });
  }
}

module.exports = IcsConfigRepository;
