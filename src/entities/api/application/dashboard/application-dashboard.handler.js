const { merge, isEmpty } = require('lodash');

const errorhandler = require('../../../../../lib/errorhandler');
const logger = require('../../../../../lib/logger').mainLogger();
const SiteRepository = require('../../site/site.repository');
const {
  getSiteApplicationDashboardService,
} = require('./application-dashboard.service');

const getApplicationDashboardHandler = async (req, res, next) => {
  try {
    const { params } = req;
    const site = new SiteRepository();
    let filterCond = { siteId: { $eq: params.siteId } };
    if (params.tenantWise) filterCond = { companyId: { $eq: params.siteId } };
    const siteFound = await site.findAll({
      filters: filterCond,
      fields: ['siteId', 'siteName', 'companyId'],
    });

    if (isEmpty(siteFound.results)) {
      return errorhandler.onError(
        req,
        res,
        next
      )({ statusCode: 404, message: 'Site Not Found' });
    }
    logger.debug({ siteFound }, '[getApplicationDashboardHandler].siteFound');
    const uParams = params;
    if (params.tenantWise)
      uParams.siteIds = siteFound.results.map(el => el.siteId);
    uParams.filters = merge(
      { siteId: { $eq: params.siteId } },
      uParams.filters
    );
    const { companyId } = siteFound.results[0];

    const result = await getSiteApplicationDashboardService({
      params: uParams,
      companyId,
    });
    logger.debug({ result }, '[getApplicationDashboardHandler].result');
    res.send(result);
    return next();
  } catch (err) {
    return errorhandler.onError(req, res, next)(err);
  }
};

module.exports = {
  getApplicationDashboardHandler,
};
