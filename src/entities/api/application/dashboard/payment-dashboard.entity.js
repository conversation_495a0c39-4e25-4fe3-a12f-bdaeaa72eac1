const BaseEntity = require('../../../lib/base.entity');

class PaymentDashboardEntity extends BaseEntity {
  static getEntityMeta() {
    return {
      tableName: 'payment_dashboard',
      primaryKey: 'payment_dashboard_id',
      columnMapping: {
        paymentDashboardId: 'payment_dashboard.payment_dashboard_id',
        deviceId: 'payment_dashboard.device_id',
        state: 'payment_dashboard.state',
        status: 'payment_dashboard.value',
        timestamp: 'payment_dashboard.timestamp',
      },
    };
  }

  /**
   * @param {import('./payment-dashboard.types').iPaymentDashboardEntity} entityObj
   */
  set(entityObj) {
    super.set(entityObj);
  }
}

module.exports = PaymentDashboardEntity;
