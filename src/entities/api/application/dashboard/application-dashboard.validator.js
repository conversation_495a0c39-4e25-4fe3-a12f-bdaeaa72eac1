const Joi = require('joi');

const {
  baseEntityValidatorObjectFields,
  buildFiltersValidator,
  buildSortValidator,
} = require('../../../lib/base.validator');
const {
  stringOpts,
  equalityOpts,
  arrayOpts,
} = require('../../../lib/entities.constant');

const getApplicationDashboardRequestValidator = Joi.object({
  ...baseEntityValidatorObjectFields,
  siteId: Joi.string().required(),
  filters: buildFiltersValidator({
    deviceId: {
      operators: [...equalityOpts],
      validator: Joi.number().integer(),
    },
    deviceName: {
      operators: [...stringOpts, ...equalityOpts],
      validator: Joi.string(),
    },
    status: {
      operators: [...stringOpts, ...equalityOpts],
      validator: Joi.string(),
    },
    app: {
      operators: [...arrayOpts],
      validator: Joi.string(),
    },
  }),
  sort: buildSortValidator(
    ['deviceId', 'deviceName', 'status'],
    ['deviceName:desc']
  ),
}).unknown(false);

module.exports = {
  getApplicationDashboardRequestValidator,
};
