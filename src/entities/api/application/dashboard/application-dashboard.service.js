const { merge, isEmpty } = require('lodash');
const NodeCache = require('node-cache');
const DeviceRepository = require('../../device/device.repository');
const {
  getCompanyDashboardConfigSchema,
} = require('../../../../payments/dashboard/dashboard.service');
const logger = require('../../../../../lib/logger').mainLogger();
const PaymentDashboardRepository = require('./payment-dashboard.repository');
const constants = require('./application-dashboard.constant');
const IcsConfigRepository = require('./ics-config.repository');

const applicationDashboardCache = new NodeCache({
  stdTTL: 15,
});

const getApplicationFilter = async icsConfigRepo => {
  let applicationFilter = applicationDashboardCache.get(
    constants.APPLICATION_FILTER_CACHE_KEY
  );

  if (!applicationFilter) {
    const result = await icsConfigRepo.findAll({
      filters: {
        key: { $eq: constants.APPLICATION_DASHBOARD_APPLICATION_FILTER },
      },
    });
    if (isEmpty(result.results))
      throw new Error(
        `Missing ICS-Config for ${constants.APPLICATION_DASHBOARD_APPLICATION_FILTER}`
      );
    applicationFilter = JSON.parse(result.results[0].value);
    applicationDashboardCache.set(
      constants.APPLICATION_FILTER_CACHE_KEY,
      applicationFilter
    );
  }
  return applicationFilter;
};
const getApplicationSchema = async icsConfigRepo => {
  let applicationFilter = applicationDashboardCache.get(
    constants.APPLICATION_SCHEMA_CACHE_KEY
  );

  if (!applicationFilter) {
    const result = await icsConfigRepo.findAll({
      filters: {
        key: { $eq: constants.APPLICATION_DASHBOARD_APPLICATION_SCHEMA },
      },
    });
    if (isEmpty(result.results))
      throw new Error(
        `Missing ICS-Config for ${constants.APPLICATION_DASHBOARD_APPLICATION_SCHEMA}`
      );
    applicationFilter = JSON.parse(result.results[0].value);
    applicationDashboardCache.set(
      constants.APPLICATION_SCHEMA_CACHE_KEY,
      applicationFilter
    );
  }
  return applicationFilter;
};

const getCompanyApplicationSchema = async ({
  applicationFilter,
  companyId,
  icsConfigRepo,
}) => {
  const companyAppSchemaCacheKey = `${constants.COMPANY_APPLICATION_SCHEMA_PREFIX}${companyId}`;
  const defaultApplicationSchema = await getApplicationSchema(icsConfigRepo);
  const applicationSchema = defaultApplicationSchema;
  logger.debug(
    { defaultApplicationSchema },
    '[getCompanyApplicationSchema].defaultApplicationSchema'
  );

  let companyApplicationSchema = applicationDashboardCache.get(
    companyAppSchemaCacheKey
  );
  logger.debug(
    { companyAppSchemaCacheKey, companyApplicationSchema },
    '[getCompanyApplicationSchema].applicationDashboardCache.get found company config schema'
  );

  if (!companyApplicationSchema) {
    const appNames = applicationFilter.map(({ appName }) => appName);
    logger.debug(
      { companyId, appNames },
      '[getCompanyApplicationSchema].getIcsEPSConfigByCompany'
    );
    companyApplicationSchema = await getCompanyDashboardConfigSchema(
      appNames,
      companyId
    );
    logger.debug(
      { companyApplicationSchema },
      '[getCompanyApplicationSchema].getIcsEPSConfigByCompany found company config schema'
    );
    applicationDashboardCache.set(
      companyAppSchemaCacheKey,
      companyApplicationSchema
    );
  }

  if (!isEmpty(companyApplicationSchema)) {
    // eslint-disable-next-line no-restricted-syntax
    for (const appName of Object.keys(defaultApplicationSchema)) {
      const existing = companyApplicationSchema.find(
        cc => cc.appName === appName
      );
      if (existing && existing.data) {
        applicationSchema[appName] = existing.data;
      }
    }
    logger.debug(
      { applicationSchema },
      '[getCompanyApplicationSchema].mergedApplicationSchema'
    );
  }
  return applicationSchema;
};

const getApplicationDashboardConfigurations = async companyId => {
  const icsConfigRepo = new IcsConfigRepository();
  const applicationFilter = await getApplicationFilter(icsConfigRepo);
  const applicationSchema = await getCompanyApplicationSchema({
    applicationFilter,
    companyId,
    icsConfigRepo,
  });
  return { applicationFilter, applicationSchema };
};

const getSiteApplicationDashboardService = async ({ params, companyId }) => {
  const repo = new PaymentDashboardRepository();
  const { applicationFilter, applicationSchema } =
    await getApplicationDashboardConfigurations(companyId);
  let applicationFilterStates = applicationFilter.map(af => af.state);

  if (params.filters.app) {
    const appValues = params.filters.app.$in;
    applicationFilterStates = applicationFilter
      .filter(a => appValues.includes(a.appName))
      .map(af => af.state);
    /* eslint no-param-reassign: "error" */
    delete params.filters.app;
  }
  const deviceRepo = new DeviceRepository();
  let filters = {};
  if (params.filters.deviceId)
    filters = merge({ targetId: params.filters.deviceId }, filters);
  if (params.filters.deviceName)
    filters = merge({ name: params.filters.deviceName }, filters);

  const sort = [];
  const deviceSort = params.sort.filter(s => s === 'deviceName');

  if (deviceSort) {
    const indexOfFirst = deviceSort.indexOf(':');
    sort.push(`name:${deviceSort.slice(indexOfFirst + 1)}`);
  }

  const { results: siteDevices } = await deviceRepo.getDevicesBySiteIds({
    siteIds: params.tenantWise ? params.siteIds : [params.siteId],
    filters,
    fields: ['targetId', 'siteId', 'name'],
    sort,
  });

  if (isEmpty(siteDevices)) {
    return {
      resultsMetaData: {
        totalResults: 0,
        pageIndex: 0,
        pageSize: params.pageSize,
      },
      applications: applicationFilter.map(af => af.appName),
      results: [],
      statusCounts: {},
    };
  }
  if (params.tenantWise) {
    delete params.siteId;
    delete params.filters.siteId;
  }
  const uParams = {
    ...params,
    applicationFilter,
    applicationSchema,
    siteDevices,
  };
  uParams.filters = merge(
    { state: { $in: applicationFilterStates } },
    { siteId: { $in: params.tenantWise ? params.siteIds : [params.siteId] } },
    uParams.filters
  );
  // eslint-disable-next-line no-return-await
  return await repo.findPaymentDashboardForSite(uParams);
};

module.exports = {
  getSiteApplicationDashboardService,
};
