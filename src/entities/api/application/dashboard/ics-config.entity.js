const BaseEntity = require('../../../lib/base.entity');

class IcsConfigEntity extends BaseEntity {
  static getEntityMeta() {
    return {
      tableName: 'ics_config',
      primaryKey: 'key',
      columnMapping: {
        key: 'ics_config.key',
        value: 'ics_config.value',
        description: 'ics_config.description',
      },
    };
  }

  /**
   * @param {import('./ics-config.types').iIcsConfigEntity} entityObj
   */
  set(entityObj) {
    super.set(entityObj);
  }
}

module.exports = IcsConfigEntity;
