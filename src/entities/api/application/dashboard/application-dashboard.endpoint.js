const { server } = require('../../../../../app');
const env = require('../../../../../env');
const { baseRoleGroup } = require('../../../../../lib/app-constants');
const { validateParams, requiresRole } = require('../../../../../lib/pre');
const {
  getApplicationDashboardHandler,
} = require('./application-dashboard.handler');
const {
  getApplicationDashboardRequestValidator,
} = require('./application-dashboard.validator');

const BASE_PATH = `${env.config.base}/entities`;

/**
 * @swagger
 * /entities/applications/dashboard/${siteId}:
 *   get:
 *     tags:
 *       - entities
 *     summary: Retrieve Device Application Status For Site
 *     parameters:
 *       - $ref: "#/parameters/AuthorizationTokenParam"
 *       - $ref: "#/parameters/pageIndexParam"
 *       - name: siteId
 *         in: path
 *         description: Identifier for the site
 *         required: true
 *         type: string
 *       - name: pageIndex
 *         in: query
 *         description: Page Index
 *         required: false
 *         type: integer
 *         default: 0
 *       - name: pageSize
 *         in: query
 *         description: The maximum number of records to return in a page
 *         required: false
 *         type: integer
 *         default: 40
 *       - name: filters[deviceId][$operator]
 *         in: query
 *         description: Filter by deviceId "$eq, $ne, $contains, $notContains, $startsWith, $endsWith"
 *         type: string
 *         required: false
 *       - name: filters[deviceName][$operator]
 *         in: query
 *         description: Filter by deviceName "$eq, $ne, $contains, $notContains, $startsWith, $endsWith"
 *         type: string
 *         required: false
 *       - name: filters[status][$operator]
 *         in: query
 *         description: Filter by status "$eq, $ne, $contains, $notContains, $startsWith, $endsWith"
 *         type: string
 *         required: false
 *       - name: sort[]
 *         in: query
 *         description: Fields to sort by ["deviceName:asc", "deviceName:desc", "status:asc", "status:desc"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *        - name: tenantWise
 *         in: query
 *         description: Get application running details based on tenant
 *         required: false
 *         type: boolean
 *         default: false
 *       - name: filters[app][$operator]
 *         in: query
 *         description: Filter by deviceId "$in"
 *         type: array
 *         required: false
 *     responses:
 *       200:
 *         description: Device Applications Status retrieved successfully
 *         schema:
 *           type: object
 *           properties:
 *             resultsMetadata:
 *               $ref: "#/definitions/ResultsMetadata"
 *             applications:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   appName:
 *                     type: string
 *                     example: "ifx-eps"
 *                   appDisplayName:
 *                     type: string
 *                     example: "EPS"
 *             results:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   deviceId:
 *                     type: number
 *                     example: "123254"
 *                   deviceName:
 *                     type: string
 *                     example: "OPT G6-400"
 *                   applications:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         appName:
 *                           type: string
 *                           example: "ifx-eps"
 *                         status:
 *                           type: string
 *                           example: "online"
 *                         style:
 *                           type: string
 *                           example: "green"
 *       400:
 *         $ref: "#/responses/BadRequest"
 *       401:
 *         $ref: "#/responses/AuthenticationFailed"
 *       403:
 *         $ref: "#/responses/Forbidden"
 *       500:
 *         $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/applications/dashboard/:siteId`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    validateParams(getApplicationDashboardRequestValidator, false, true),
    getApplicationDashboardHandler,
  ]
);
