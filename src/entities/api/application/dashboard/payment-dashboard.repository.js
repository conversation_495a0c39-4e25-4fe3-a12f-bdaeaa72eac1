const { server } = require('../../../../../app');
const BaseRepository = require('../../../lib/base.repository');
const {
  buildQuery,
  matchAndReplaceKey,
  prepareSortFields,
  prepareSelectFields,
} = require('../../../lib/entities.helper');
const DeviceEntity = require('../../device/device.entity');
const logger = require('../../../../../lib/logger').mainLogger();
const PaymentDashboadEntity = require('./payment-dashboard.entity');
const {
  getAppWiseDeviceCount,
  getAppsCount,
} = require('./payment-dashboard.helper');
/**
 * @class
 */
class PaymentDashboardRepository extends BaseRepository {
  constructor() {
    super(PaymentDashboadEntity);
  }

  getColumnMapping() {
    const joins = [];
    const deviceMeta = DeviceEntity.getEntityMeta();
    const { columnMapping } = this.entityClass.getEntityMeta();

    const columnMappingSelect = {
      ...columnMapping,
      deviceName: `${deviceMeta.columnMapping.name}`,
      siteId: `${deviceMeta.columnMapping.siteId}`,
    };

    joins.push({
      type: 'INNER',
      tableName: `${deviceMeta.tableName}`,
      on: `${deviceMeta.columnMapping.targetId} = ${columnMapping.deviceId}`,
    });
    return { joins, columnMapping, columnMappingSelect };
  }

  /**
   * @param {import('../../../lib/base.types').iFindOptions} param
   * @returns {Promise<import('./application-dashboard.types').iApplicationDashboardRes>}
   */
  async findPaymentDashboardForSite({
    fields = [],
    filters,
    sort,
    pageIndex = 0,
    pageSize = 0,
    applicationFilter,
    applicationSchema,
    siteDevices = [],
  }) {
    const groupBy = [];
    const { joins, columnMappingSelect } = this.getColumnMapping();

    const { query, queryParams } = buildQuery({
      tableName: this.entityClass.getEntityMeta().tableName,
      fields: prepareSelectFields(fields, columnMappingSelect),
      filters: matchAndReplaceKey(filters, columnMappingSelect),
      joins,
      groupBy,
      sort: prepareSortFields(sort, columnMappingSelect),
    });
    logger.debug(
      { filters, sort, siteDevices },
      '[findPaymentDashboardForSite].filters, sort, siteDevices'
    );
    logger.debug(
      { query, queryParams },
      '[findPaymentDashboardForSite].query, queryParams'
    );
    const devicePaymentDashboardData = await server.db.read.rows(
      query,
      queryParams
    );
    logger.debug(
      { devicePaymentDashboardData },
      '[findPaymentDashboardForSite].devicePaymentDashboardData'
    );

    const paymentDashboardResults = siteDevices.map(({ targetId, name }) => ({
      deviceId: targetId,
      deviceName: name,
      applications: [],
    }));
    logger.debug(
      { paymentDashboardResults },
      '[findPaymentDashboardForSite].paymentDashboardResults'
    );

    const uniqueApp = new Set();
    const uniqueApplications = [];
    devicePaymentDashboardData.forEach(
      ({ deviceId, state, status, timestamp }) => {
        const deviceDashboard = paymentDashboardResults.find(
          pdr => pdr.deviceId === deviceId
        );
        if (deviceDashboard) {
          const app = applicationFilter.find(a => a.state === state);
          const appName = app ? app.appName : state;
          const schema = applicationSchema[appName];
          const statusStyle = schema
            ? schema[state]?.values?.find(v => v.name === status)
            : null;
          const style = statusStyle ? statusStyle.style : '';
          const newAppStatus = {
            appName,
            status,
            style,
            timestamp,
          };
          deviceDashboard.applications.push(newAppStatus);
          if (!uniqueApp.has(app.appName)) {
            uniqueApp.add(app.appName);
            uniqueApplications.push(app);
          }
          logger.debug(
            { deviceDashboard },
            '[findPaymentDashboardForSite].deviceDashboard'
          );
        }
      }
    );

    logger.debug(
      { paymentDashboardResults },
      '[findPaymentDashboardForSite].paymentDashboardResults'
    );

    const pagedResult = paymentDashboardResults.slice(
      pageIndex * pageSize,
      (pageIndex + 1) * pageSize
    );
    logger.debug({ pagedResult }, '[findPaymentDashboardForSite].pageResult');

    const applications = uniqueApplications.map(app => {
      const displayName = app.appDisplayName || app.appName;
      return { appName: app.appName, appDisplayName: displayName };
    });

    return {
      resultsMetadata: {
        totalResults: paymentDashboardResults.length,
        pageIndex,
        pageSize,
      },
      applications,
      results: pagedResult || [],
      appStatusCounts: getAppsCount(paymentDashboardResults),
      appWiseDeviceCounts: getAppWiseDeviceCount(paymentDashboardResults),
    };
  }
}

module.exports = PaymentDashboardRepository;
