/**
 * @param {Array.<string>} items
 * @returns { string }
 */
const build = items => {
  const filteredItemString = items.filter(Boolean).join(',');
  return `json_build_object(${filteredItemString})`;
};

/**
 * @param {Array.<string>} fields
 * @returns { {site:string?,company:string?} }
 */
const getNestedMappings = fields => {
  const nestedMaps = {};

  const siteParts = [];
  if (fields.includes('site.id')) {
    siteParts.push(...["'id'", 'site."site_id"']);
  }
  if (fields.includes('site.name')) {
    siteParts.push(...["'name'", 'site."name"']);
  }
  if (fields.includes('site.formatted_address')) {
    siteParts.push(...["'formatted_address'", 'site."formatted_address"']);
  }
  if (fields.includes('site.tags.id')) {
    siteParts.push(
      ...[
        "'tags'",
        "COALESCE(array_agg(DISTINCT jsonb_build_object('id', site_tag.tag_id)),'{}')",
      ]
    );
  }
  if (fields.includes('site.visible')) {
    siteParts.push(...["'visible'", 'site."visible"']);
  }
  if (fields.includes('site.externalRefs.refId')) {
    siteParts.push(
      ...[
        "'externalReferences'",
        "COALESCE(array_agg(DISTINCT jsonb_build_object('referenceId', site_external_references.reference_id, 'referenceType', site_external_references.reference_type)),'{}')",
      ]
    );
  }

  if (siteParts.length) {
    nestedMaps.site = build(siteParts);
  }

  if (fields.includes('company.id')) {
    nestedMaps.company = build(["'id'", 'site."company_id"']);
  }

  return nestedMaps;
};

/**
 * @param {Array.<string>} fields
 * @returns { Array.<string> }
 */
const decomplexifyFields = originalFields => {
  const nonComplexFields = originalFields.filter(
    field =>
      ![
        'site.id',
        'site.name',
        'site.formatted_address',
        'site.tags.id',
        'site.visible',
        'site.externalRefs.refId',
        'site.externalRefs.refType',
        'company.id',
      ].includes(field)
  );

  if (
    originalFields.includes('site.id') ||
    originalFields.includes('site.name') ||
    originalFields.includes('site.tags.id') ||
    originalFields.includes('site.visible') ||
    originalFields.includes('site.externalRefs.refId') ||
    originalFields.includes('site.externalRefs.refType')
  ) {
    nonComplexFields.push('site');
  }
  if (originalFields.includes('company.id')) {
    nonComplexFields.push('company');
  }
  return nonComplexFields;
};

module.exports = {
  getNestedMappings,
  decomplexifyFields,
};
