const { merge } = require('lodash');

const DeviceRepository = require('./device.repository');

const getDevicesService = async (params, user) => {
  const userCompanyId = user.company.id;

  // Limit search to company
  const uParams = params;
  uParams.filters = merge(
    {
      companyId: { $eq: userCompanyId },
      active: { $eq: 'true' },
    },
    uParams.filters
  );

  const repo = new DeviceRepository();
  // eslint-disable-next-line no-return-await
  return await repo.findDeviceWithDeviceTagsAndAttribs(uParams);
};

module.exports = {
  getDevicesService,
};
