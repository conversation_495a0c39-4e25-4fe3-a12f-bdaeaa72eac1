const { mapKeys, camelCase, merge } = require('lodash');
const logger = require('../../../../lib/logger').mainLogger();
const BaseRepository = require('../../lib/base.repository');
const {
  enumToScreenSize,
} = require('../../../../helpers/screensize-enum.json');

const {
  buildQuery,
  prepareSelectFields,
  matchAndReplaceKey,
  prepareSortFields,
  wrapQueryWithPagination,
} = require('../../lib/entities.helper');
const DeviceEntity = require('./device.entity');
const { getNestedMappings, decomplexifyFields } = require('./device.helper');

/**
 * @class DeviceRepository
 * @extends BaseRepository
 */
class DeviceRepository extends BaseRepository {
  constructor() {
    super(DeviceEntity);
  }

  getDevicesByIds(deviceIds) {
    return this.getByIdList(deviceIds);
  }

  getDevicesBySiteIds({ siteIds, filters, fields, sort }) {
    logger.debug(
      { siteIds, filters, fields, sort },
      '[getDevicesBySiteIds].siteIds, filters, fields, sort'
    );
    const uFilters = merge(
      {
        siteId: { $in: siteIds },
        active: { $eq: true },
      },
      filters
    );

    logger.debug({ uFilters }, '[getDevicesBySiteIds].uFilters');
    return this.findAll({ filters: uFilters, fields, sort });
  }

  async findDeviceWithDeviceTagsAndAttribs({
    fields = [],
    filters,
    sort,
    pageIndex = 0,
    pageSize = 0,
  }) {
    const { columnMapping } = this.entityClass.getEntityMeta();
    const nestedMappings = getNestedMappings(fields);
    const columnMappingSelect = {
      ...columnMapping,
      ...nestedMappings,
    };
    const columnMappingWhere = {
      ...columnMapping,
    };

    const groupBy = ['target.target_id', 'site.site_id'];

    const joins = [
      {
        type: 'INNER',
        tableName: 'site',
        on: 'site.site_id = target.site_id AND site.active = TRUE',
      },
    ];

    if (
      fields.includes('site.tags.id') ||
      Object.keys(filters).includes('site.tags.id')
    ) {
      joins.push({
        type: 'LEFT',
        tableName: 'site_tag',
        on: 'site_tag.site_id = site.site_id AND site_tag.deleted = FALSE',
      });
    }

    if (
      fields.includes('site.externalRefs.refId') ||
      Object.keys(filters).includes('site.externalRefs.refId') ||
      Object.keys(filters).includes('site.externalRefs.refType')
    ) {
      joins.push({
        type: 'LEFT',
        tableName: 'site_external_references',
        on: 'site_external_references.site_id = site.site_id AND site_external_references.deleted = FALSE',
      });
    }
    // eslint-disable-next-line prefer-const
    let { query, queryParams } = buildQuery({
      tableName: this.entityClass.getEntityMeta().tableName,
      fields: prepareSelectFields(
        decomplexifyFields(fields),
        columnMappingSelect
      ),
      filters: matchAndReplaceKey(filters, columnMappingWhere),
      joins,
      groupBy,
      sort: prepareSortFields(sort, columnMapping),
    });

    // eslint-disable-next-line no-prototype-builtins
    if (filters.hasOwnProperty('location')) {
      const pattern = /WHERE (.+?) GROUP BY/s;
      const value = filters.location.$containsi;
      const newWhereClause = `
        site.company_id = $1 
        AND target.active = $2 
        AND (LOWER(site.name) LIKE '%' || LOWER('${value}') || '%' 
        OR LOWER(site.formatted_address) LIKE '%' || LOWER('${value}') || '%')
      `;
      query = query.replace(pattern, (match, p1) => {
        if (!p1.includes('site.company_id') && !p1.includes('target.active')) {
          return `WHERE ${newWhereClause} GROUP BY`;
        }
        return `WHERE ${p1} AND (LOWER(site.name) LIKE '%' || LOWER('${value}') || '%' 
          OR LOWER(site.formatted_address) LIKE '%' || LOWER('${value}') || '%') GROUP BY`;
      });
    }
    // eslint-disable-next-line no-prototype-builtins
    if (filters.hasOwnProperty('deviceType')) {
      const pattern = /WHERE (.+?) GROUP BY/s;
      const value = filters.deviceType.$containsi;

      // Extract only the base device type (e.g., G7-100), removing the screen size
      const screenSizeMatch = value.match(/(G7-100)-(\d+)$/);
      const baseDeviceType = screenSizeMatch ? screenSizeMatch[1] : value; // Extract only 'G7-100'

      let newWhereClause;
      let mappedScreenSize;

      if (screenSizeMatch) {
        const screenSize = screenSizeMatch[2]; // Extract the screen size

        // Map screen sizes based on the provided mapping
        const screenSizeMap = {
          8: 2,
          12: 4,
          15: 5,
        };
        mappedScreenSize = screenSizeMap[screenSize];
        queryParams[2] = baseDeviceType;

        // WHERE clause for both base deviceType and screenSize
        newWhereClause = `site.company_id = $1 
          AND target.active = $2
          AND LOWER(target.device_type) LIKE '%' || LOWER('${baseDeviceType}') || '%'
          AND target.screen_size = '${mappedScreenSize}'`;
      } else {
        // WHERE clause for just the base deviceType (no screen size)
        newWhereClause = `site.company_id = $1 
          AND target.active = $2
          AND LOWER(target.device_type) LIKE '%' || LOWER('${baseDeviceType}') || '%'`;
      }

      // Modify the query, ensuring only the base deviceType is used without repetition
      query = query.replace(pattern, (match, p1) => {
        // Only append new conditions if site.company_id and target.active aren't already there
        if (!p1.includes('site.company_id') && !p1.includes('target.active')) {
          return `WHERE ${newWhereClause} GROUP BY`;
        }

        // Add conditions only once, without duplicating deviceType or screenSize
        return `WHERE ${p1}
        ${screenSizeMatch ? `AND target.screen_size = '${mappedScreenSize}'` : ''}
        GROUP BY`;
      });

      // Update the filters.deviceType value to only contain the base deviceType ('G7-100')
      // eslint-disable-next-line no-param-reassign
      filters.deviceType.$containsi = baseDeviceType;
    }

    const paginatedQuery = wrapQueryWithPagination(
      query,
      queryParams,
      pageIndex,
      pageSize
    );
    const result = await this.dataContext.row(paginatedQuery, queryParams);

    if (result.results) {
      result.results = result.results.map(m => {
        if (enumToScreenSize[m.screen_size]) {
          // eslint-disable-next-line no-param-reassign
          m.screen_size = enumToScreenSize[m.screen_size];
        }
        return mapKeys(m, (_v, k) => camelCase(k));
      });
    }

    return {
      resultsMetadata: {
        totalResults: result.totalCount,
        pageIndex,
        pageSize,
      },
      results: result.results || [],
    };
  }
}

module.exports = DeviceRepository;
