const errorhandler = require('../../../../lib/errorhandler');
const { getDevicesService } = require('./device.service');

const getDevicesHandler = async (req, res, next) => {
  try {
    const { user, params } = req;
    const result = await getDevicesService(params, user);
    res.send(result);
    return next();
  } catch (err) {
    return errorhandler.onError(req, res, next)(err);
  }
};

module.exports = {
  getDevicesHandler,
};
