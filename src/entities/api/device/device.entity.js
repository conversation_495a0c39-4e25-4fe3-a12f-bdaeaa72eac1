const BaseEntity = require('../../lib/base.entity');

// Base mappings for columns to camelCase
const tableColumnMapping = {
  targetId: 'target.target_id',
  siteId: 'target.site_id',
  password: 'target.password',
  version: 'target.version',
  lastRegistered: 'target.last_registered',
  lastContact: 'target.last_contact',
  priorityOrder: 'target.priority_order',
  registrationKey: 'target.registration_key',
  name: 'target.name',
  description: 'target.description',
  latitude: 'target.latitude',
  longitude: 'target.longitude',
  maxUploadRateKb: 'target.max_upload_rate_kb',
  messagingPort: 'target.messaging_port',
  serialNumber: 'target.serial_number',
  active: 'target.active',
  deleteTimestamp: 'target.delete_timestamp',
  registrationWindowEnd: 'target.registration_window_end',
  keyGroupId: 'target.key_group_id',
  keyBundleCount: 'target.key_bundle_count',
  certificate: 'target.certificate',
  isJsonCertificates: 'target.is_json_certificates',
  macAddress: 'target.mac_address',
  data: 'target.data',
  status: 'target.status',
  created: 'target.created',
  presence: 'target.presence',
  keyGroupRef: 'target.key_group_ref',
  deviceType: 'target.device_type',
  dateUpdated: 'target.date_updated',
  ipAddress: 'target.ip_address',
  promptSet: 'target.prompt_set',
  realIpAddress: 'target.real_ip_address',
  gatewayAddress: 'target.gateway_address',
  subnetMask: 'target.subnet_mask',
  releaseVersion: 'target.release_version',
  lastRki: 'target.last_rki',
  lastEditedDate: 'target.last_edited_date',
  // installDate: 'target.install_date',
  screenSize: 'target.screen_size',
};

// Mappings needed in API-spec which don't quite match column names, added new ones rather than updated existing ones
const requestMapping = {
  id: 'target.target_id',
  createdAt: 'target.created',
  updatedAt: 'target.date_updated',
};

// Mappings needed to scope the results of the user
const authMappings = {
  companyId: 'site.company_id',
};

// Mappings for complex fields
const complexMappings = {
  'site.id': 'site.site_id',
  'site.name': 'site.name',
  'site.formatted_address': 'site.formatted_address',
  'site.tags.id': 'site_tag.tag_id',
  'site.visible': 'site.visible',
  'site.externalRefs.refId': 'site_external_references.reference_id',
  'site.externalRefs.refType': 'site_external_references.reference_type',
  'company.id': 'site.company_id',
};

/**
 * @class DeviceEntity
 * @extends {BaseEntity}
 */
class DeviceEntity extends BaseEntity {
  static getEntityMeta() {
    return {
      tableName: 'target',
      primaryKey: 'target_id',
      columnMapping: {
        ...tableColumnMapping,
        ...requestMapping,
        ...authMappings,
        ...complexMappings,
      },
    };
  }
}

module.exports = DeviceEntity;
