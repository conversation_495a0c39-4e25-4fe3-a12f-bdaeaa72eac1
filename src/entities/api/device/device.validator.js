const Joi = require('joi');

const {
  buildFieldsValidator,
  buildFiltersValidator,
  buildSortValidator,
  buildPageSizeAndIndexValidator,
} = require('../../lib/base.validator');
const {
  arrayOpts,
  equalityOpts,
  minPageSize,
  minPageIndex,
  stringOpts,
} = require('../../lib/entities.constant');
const {
  DEVICE_MAX_PAGE_SIZE,
  DEVICE_DEFAULT_PAGE_SIZE,
} = require('./device.constant');

const allowedFilters = {
  id: {
    operators: [...equalityOpts, ...arrayOpts],
    validator: Joi.number().integer(),
  },
  'site.id': {
    operators: [...equalityOpts, ...arrayOpts],
    validator: Joi.string().guid(),
  },
  'site.tags.id': {
    operators: [...equalityOpts, ...arrayOpts],
    validator: Joi.number().integer(),
  },
  'site.visible': {
    operators: [...equalityOpts],
    validator: Joi.boolean(),
  },
  'site.externalRefs.refId': {
    operators: [...equalityOpts, ...arrayOpts],
    validator: Joi.string(),
  },
  'site.externalRefs.refType': {
    operators: [...equalityOpts, ...arrayOpts],
    validator: Joi.string(),
  },
  name: {
    operators: [...equalityOpts, ...stringOpts],
    validator: Joi.string().regex(/^[A-Za-z0-9_-]+$/),
  },
  deviceType: {
    operators: [...equalityOpts, ...stringOpts],
    validator: Joi.string().regex(/^[A-Za-z0-9_-]+$/),
  },
  serialNumber: {
    operators: [...equalityOpts, ...stringOpts],
    validator: Joi.string().regex(/^[A-Za-z0-9-]{1,128}$/),
  },
  releaseVersion: {
    operators: [...equalityOpts, ...stringOpts],
    validator: Joi.string().regex(
      /^[Rr](\d+(\.\d*){0,2})?$|^\d+(\.\d*){0,2}$|^\[[Rr]\d+(\.\d*){0,2}\]$|^[Rr]$/
    ),
  },
  'site.name': {
    operators: [...equalityOpts, ...stringOpts],
    validator: Joi.string().regex(
      /^(?!\s)(?!.*\s$)['A-Za-z0-9-&$#._,/ ]{1,100}$/
    ),
  },
  'site.formatted_address': {
    operators: [...equalityOpts, ...stringOpts],
    validator: Joi.string().regex(/^[a-zA-Z0-9\s,.-]+$/),
  },
  location: {
    operators: [...equalityOpts, ...stringOpts],
    validator: Joi.string().regex(
      /^(?!\s)(?!.*\s$)['A-Za-z0-9-&$#._,/ ]{1,100}$/
    ),
  },
};

const getDevicesRequestValidator = Joi.object({
  ...buildPageSizeAndIndexValidator(
    minPageSize,
    DEVICE_MAX_PAGE_SIZE,
    DEVICE_DEFAULT_PAGE_SIZE,
    minPageIndex
  ),
  fields: buildFieldsValidator(
    [
      'id',
      'name',
      'serialNumber',
      'deviceType',
      'screenSize',
      'installDate',
      'releaseVersion',
      'createdAt',
      'updatedAt',
      'site.id',
      'site.name',
      'site.formatted_address',
      'site.tags.id',
      'site.visible',
      'site.externalRefs.refId',
      'site.externalRefs.refType',
      'company.id',
    ],
    [
      'id',
      'name',
      'serialNumber',
      'deviceType',
      'screenSize',
      'installDate',
      'releaseVersion',
      'createdAt',
      'updatedAt',
      'site.id',
      'site.name',
      'site.formatted_address',
      'site.tags.id',
      'site.visible',
      'site.externalRefs.refId',
      'site.externalRefs.refType',
      'company.id',
    ]
  ),
  filters: buildFiltersValidator(allowedFilters),
  sort: buildSortValidator(['id'], ['id:desc']),
});

module.exports = {
  getDevicesRequestValidator,
};
