const Joi = require('joi');
const { server } = require('../../../../../../app');
const env = require('../../../../../../env');
const { requiresRole, validateBody } = require('../../../../../../lib/pre');
const mfaJobHandler = require('./mfa-jobs.handler');

const BASE_PATH = `${env.config.base}/entities`;

/**
 * @swagger
 * /entities/internal/devices/mfa/jobs:
 *   post:
 *     tags:
 *       - entities
 *     summary: Generate Jobs for Action With MFA authentication
 *     consumes:
 *       - application/json
 *     produces:
 *       - application/json
 *     parameters:
 *       - $ref: '#/parameters/AuthorizationTokenParam'
 *       - name: body
 *         in: body
 *         description: The mfaCode, action, and device ids to create jobs for specified action.
 *         required: true
 *         schema:
 *           $ref: '#/definitions/MfaJobsDetails'
 *           required: true
 *     responses:
 *       200:
 *         description: Jobs created successfully
 *         schema:
 *         validDevices:
 *           type: array
 *           items:
 *             type: object
 *         excludedDevices:
 *           type: array
 *           items:
 *             type: object
 *         results:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               deviceId:
 *                 type: number
 *                 example: 12343443
 *               wasCreated:
 *                 type: boolean
 *                 example: true
 *               generateChallengeTokenJob:
 *                 type: object
 *                 properties:
 *                    id:
 *                      type: string
 *                      example: 421c7f02-ba17-11eb-8e90-b3863d314b36
 *                    deviceId:
 *                      type: number
 *                      example: 12343443
 *                    destination:
 *                      type: string
 *                      example: invenco.system
 *                    type:
 *                      type: string
 *                      example: sys.generate-challenge-token
 *                    data:
 *                      type: string
 *                      example: {}
 *                    status:
 *                      type: number
 *                      example: 0
 *                    embargo:
 *                      type: string
 *                      example: 2023-10-02T22:35:48.000Z
 *                    expiration:
 *                      type: string
 *                      example: 2023-10-03T00:35:48.000Z
 *                    createdBy:
 *                      type: string
 *                      example: bede363f-7640-41a3-bb50-fc73cba8987b
 *                    createdOn:
 *                      type: string
 *                      example: 2023-10-02T22:35:48.428Z
 *               processAuthOperationJob:
 *                 type: object
 *                 properties:
 *                    id:
 *                      type: string
 *                      example: 421c7f02-ba17-11eb-8e90-b3863d000000
 *                    deviceId:
 *                      type: number
 *                      example: 12343443
 *                    destination:
 *                      type: string
 *                      example: invenco.system
 *                    type:
 *                      type: string
 *                      example: sys.process-auth-operation
 *                    data:
 *                      type: string
 *                      example: "{\"token\": \"\",\"operation\": \"merchant-reset\",\"params\": [\"vendor-cert\", \"fi-keys\", \"vendor-software\", \"vendor-logs\"]}"
 *                    status:
 *                      type: number
 *                      example: 0
 *                    embargo:
 *                      type: string
 *                      example: 2023-10-02T22:35:48.000Z
 *                    expiration:
 *                      type: string
 *                      example: 2023-10-03T00:35:48.000Z
 *                    createdBy:
 *                      type: string
 *                      example: bede363f-7640-41a3-bb50-fc73cba8987b
 *                    createdOn:
 *                      type: string
 *                      example: 2023-10-02T22:35:48.428Z
 *               dependency:
 *                 type: object
 *                 properties:
 *                    jobId:
 *                      type: string
 *                      example: 421c7f02-ba17-11eb-8e90-b3863d000000
 *                    dependentJobId:
 *                      type: string
 *                      example: 421c7f02-ba17-11eb-8e90-b3863d314b36
 *                    runOnFail:
 *                      type: boolean
 *                      example: false
 *       400:
 *         $ref: "#/responses/BadRequest"
 *       401:
 *         $ref: "#/responses/AuthenticationFailed"
 *       403:
 *         $ref: "#/responses/Forbidden"
 *       500:
 *         $ref: "#/responses/InternalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/internal/devices/mfa/jobs`,
    version: '0.0.1',
  },
  [
    requiresRole(['DEVICE_MERCHANT_RESET']),
    validateBody({
      mfaCode: Joi.string().length(6).required(),
      action: Joi.string().valid(['MERCHANT_RESET']),
      dryRun: Joi.boolean(),
      devices: Joi.array().optional().items(Joi.string()),
      sites: Joi.array().optional().items(Joi.string().guid()),
      tags: Joi.array().optional().items(Joi.number().positive().integer()),
      siteGroups: Joi.array().optional().items(Joi.string().guid()),
    }),
    mfaJobHandler.create,
  ]
);
