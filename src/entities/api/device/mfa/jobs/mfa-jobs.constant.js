module.exports = {
  DEVICE_MERCHANT_RESET: 'DEVICE_MERCHANT_RESET',
  MERCHANT_RESET: 'MERCHANT_RESET',
  SYS_MERCHANT_RESET: 'sys.merchant-reset',
  PROCESS_AUTH_OPERATION: 'sys.process-auth-operation',
  INVENCO_SYSTEM: 'invenco.system',
  SYS_GENERATE_CHALLENGE_TOKEN: 'sys.generate-challenge-token',
  FIND_DEVICES_SELECT: `
    SELECT
        t.target_id as device_id,
        t.serial_number,
        t.name as device_name,
        t.site_id,
        s.name as site_name,
        t.presence,
        get_device_health( target_id ) as status `,
  FIND_DEVICES_GROUPBY: `
        GROUP BY t.target_id,
            t.name,
            t.serial_number,
            t.site_id,
            s.name,
            t.presence
        ORDER BY t.target_id;`,
};
