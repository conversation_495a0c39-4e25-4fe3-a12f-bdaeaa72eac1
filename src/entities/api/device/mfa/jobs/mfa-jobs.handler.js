const restify = require('restify');
const _ = require('lodash');
const errorhandler = require('../../../../../../lib/errorhandler');
const usersHelper = require('../../../../../../helpers/users-helper');
const totp = require('../../../../../../lib/totp');

const {
  createMerchantResetJobs,
  getDevicesForMfaJobs,
} = require('./mfa-jobs.service');

const constants = require('./mfa-jobs.constant');

const create = async (req, res, next) => {
  try {
    const userId = req.user.sub;
    const companyId = req.user.company.id;
    const { user } = req;
    const { dryRun, devices, sites, tags, siteGroups, action, mfaCode } =
      req.body;

    if (!devices && !sites && !tags && !siteGroups) {
      return next(
        new restify.errors.BadRequestError(
          'Filter arrays must contain at least 1 item.'
        )
      );
    }

    if (!dryRun) {
      const mfaSecret = await usersHelper.getUserMfaSecretById(user.sub);

      const isMfaCodeValid = await totp.validateTotp(req, mfaSecret, mfaCode);

      if (!isMfaCodeValid) {
        return next(
          new restify.errors.BadRequestError(
            `MFA code ${mfaCode} validation failed for action: ${action}`
          )
        );
      }
    }

    const hasFilters =
      !_.isEmpty(devices) ||
      !_.isEmpty(sites) ||
      !_.isEmpty(tags) ||
      !_.isEmpty(siteGroups);
    if (!hasFilters) {
      return next(
        new restify.errors.BadRequestError(
          'Filter arrays must contain at least 1 item.'
        )
      );
    }

    const devicesForMfaJobs = await getDevicesForMfaJobs(
      userId,
      companyId,
      devices,
      sites,
      tags,
      siteGroups
    );

    if (_.isEmpty(devicesForMfaJobs)) {
      return next(new restify.errors.NotFoundError('No devices found.'));
    }

    const validDevices = devicesForMfaJobs.map(item =>
      item.serialNumber.toUpperCase()
    );

    const excludedDevices = devices.filter(
      item => !validDevices.includes(item.toUpperCase())
    );

    if (dryRun) {
      res.send(200, {
        validDevices,
        excludedDevices,
      });
      return next();
    }

    if (action === constants.MERCHANT_RESET) {
      const results = await createMerchantResetJobs(
        user,
        devicesForMfaJobs,
        excludedDevices
      );
      res.send(200, {
        validDevices,
        excludedDevices,
        results,
      });
      return next();
    }
    throw new restify.NotFoundError(`MFA Jobs Action ${action} not found`);
  } catch (err) {
    return errorhandler.onError(req, res, next)(err);
  }
};

module.exports = {
  create,
};
