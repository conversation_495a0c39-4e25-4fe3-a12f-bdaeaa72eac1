const { v4 } = require('uuid');
const _ = require('lodash');
const DeviceRepository = require('../../device.repository');
const { server } = require('../../../../../../app');
const jobHelper = require('../../../../../../helpers/job-helper');
const logger = require('../../../../../../lib/logger').mainLogger();
const constants = require('./mfa-jobs.constant');

const prepareFrom = (hasTags, hasSiteGroups) => {
  let from = `
        FROM target t
        JOIN site s ON s.site_id = t.site_id
        JOIN company c ON c.id = s.company_id
        LEFT JOIN company_product_type cpt ON cpt.device_type = t.device_type
        JOIN user_site_authorization u ON u.site_id = s.site_id`;

  if (hasTags) {
    from = `${from} 
        JOIN site_tag st ON st.site_id = s.site_id AND st.deleted = false
        JOIN tag tg ON tg.id = st.tag_id`;
  }

  if (hasSiteGroups) {
    from = `${from} 
        JOIN authorization_site_group_site asgs ON asgs.site_id=s.site_id 
        JOIN authorization_site_group asg ON asg.id=asgs.authorization_site_group_id`;
  }

  return from;
};

const prepareWhere = ({
  userId /* required */,
  companyId /* required */,
  devices,
  sites,
  tags,
  siteGroups,
}) => {
  const params = [];
  params.push(userId);
  let where = `
        WHERE t.active IS true 
        AND u.user_id = $${params.length} `;

  params.push(companyId);
  where = `${where} AND cpt.company = $${params.length} `;

  if (!_.isEmpty(devices)) {
    params.push(devices);
    where = `${where} AND LOWER(t.serial_number) = ANY( $${params.length} ) `;
  }

  if (!_.isEmpty(sites)) {
    params.push(sites);
    where = `${where} AND s.site_id = ANY( $${params.length}::uuid[] ) `;
  }

  if (!_.isEmpty(tags)) {
    params.push(tags);
    where = `${where} AND tg.id = ANY( $${params.length} ) `;
  }

  if (!_.isEmpty(siteGroups)) {
    params.push(siteGroups);
    where = `${where} AND asg.id = ANY( $${params.length}::uuid[] ) `;
  }

  return { params, where };
};

const getDevicesForMfaJobs = async (
  userId,
  companyId,
  devices,
  sites,
  tags,
  siteGroups
) => {
  const fromClause = prepareFrom(!_.isEmpty(tags), !_.isEmpty(siteGroups));

  const devicesLowerCased = devices
    ? devices.map(value => value.toLowerCase())
    : [];

  const whereClause = prepareWhere({
    userId,
    companyId,
    devices: devicesLowerCased,
    sites,
    tags,
    siteGroups,
  });

  const sql = `
            ${constants.FIND_DEVICES_SELECT}
            ${fromClause}
            ${whereClause.where}
            ${constants.FIND_DEVICES_GROUPBY}`;
  // eslint-disable-next-line no-return-await
  return await server.db.read.rows(sql, whereClause.params);
};

const getDevices = async deviceIds => {
  const repo = new DeviceRepository();
  // eslint-disable-next-line no-return-await
  return await repo.getDevicesByIds(deviceIds);
};

const createMerchantResetJob = async (connection, user, device) => {
  const userId = user.sub;

  const currentDateTime = new Date();
  const expirationDateTime = jobHelper.getExpiryDateTime(
    currentDateTime,
    2,
    'hours'
  );
  const currentUTCDateTime = jobHelper.getUTCTimestamp(currentDateTime);

  const generateChallengeTokenJob = await jobHelper.createJob(connection, {
    deviceId: device.deviceId,
    destination: constants.INVENCO_SYSTEM,
    type: constants.SYS_GENERATE_CHALLENGE_TOKEN,
    data: {},
    embargo: currentUTCDateTime,
    expiry: expirationDateTime,
    userId,
  });

  const authData = {
    token: '',
    operation: 'merchant-reset',
    params: ['vendor-cert', 'fi-keys', 'vendor-software', 'vendor-logs'],
  };
  const processAuthOperationJob = await jobHelper.createJob(connection, {
    deviceId: device.deviceId,
    destination: constants.INVENCO_SYSTEM,
    type: constants.PROCESS_AUTH_OPERATION,
    data: JSON.stringify(authData),
    embargo: currentUTCDateTime,
    expiry: expirationDateTime,
    userId,
  });

  const jobDependencyResult = await connection.execute(
    `
        INSERT INTO job_dependency
              ( job_id, dependens_on, continue_on_fail )
        VALUES 
              ( $1, $2, $3 )
        RETURNING 
              job_id,
              dependens_on AS dependent_job_id,
              continue_on_fail AS run_on_fail;
    `,
    [processAuthOperationJob.id, generateChallengeTokenJob.id, false]
  );
  const dependency = jobDependencyResult.rows[0];
  logger.info(
    `Generate Merchant Reset Job  ${generateChallengeTokenJob.id} was created by ${user.fullName}`
  );
  return {
    deviceId: device.deviceId,
    wasCreated: !!(
      generateChallengeTokenJob &&
      processAuthOperationJob &&
      dependency
    ),
    generateChallengeTokenJob,
    processAuthOperationJob,
    dependency,
  };
};

const generateBulkOperations = async (
  connection,
  bulkOperation,
  user,
  excludedDevices,
  results
) => {
  const bulkOperationId = v4();
  await connection.execute(
    `
        INSERT INTO bulk_operation 
                ( id, type, created_by, schedule, excluded_devices )
        VALUES 
                ( $1, $2, $3, $4, $5 )
        RETURNING 
                bulk_operation.id as bulkId;
    `,
    [bulkOperationId, bulkOperation, user.sub, new Date(), excludedDevices]
  );

  await Promise.all(
    results.map(resetJobs => {
      const { deviceId, generateChallengeTokenJob } = resetJobs;
      return connection.execute(
        `
            INSERT INTO bulk_operation_item 
                    ( id, bulk_operation_id, device_id, job_id, bulk_operation_status )
            VALUES  
                    ( $1, $2, $3, $4, $5 )
            RETURNING 
                    bulk_operation_item.id as bulkItemId;
        `,
        [v4(), bulkOperationId, deviceId, generateChallengeTokenJob.id, 0]
      );
    })
  );
};

const createMerchantResetJobs = async (user, devices, excludedDevices) => {
  let results = [];
  const connection = await server.db.write.getConnection();
  try {
    await connection.execute('BEGIN');

    results = await Promise.all(
      devices.map(device => createMerchantResetJob(connection, user, device))
    );

    if (!_.isEmpty(results)) {
      await generateBulkOperations(
        connection,
        constants.SYS_MERCHANT_RESET,
        user,
        excludedDevices,
        results
      );
    }
    await connection.execute('COMMIT');
    connection.done();
  } catch (err) {
    await connection.execute('ROLLBACK');
    connection.done();
    throw err;
  }
  return results;
};

module.exports = {
  getDevicesForMfaJobs,
  createMerchantResetJobs,
  getDevices,
};
