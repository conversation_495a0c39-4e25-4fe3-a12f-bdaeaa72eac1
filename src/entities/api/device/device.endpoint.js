/* eslint-disable max-len */
const Joi = require('joi');

const { server } = require('../../../../app');
const env = require('../../../../env');
const { roles, baseRoleGroup } = require('../../../../lib/app-constants');
const {
  validateHeaders,
  validateParams,
  requiresRole,
} = require('../../../../lib/pre');
const { getDevicesHandler } = require('./device.handler');
const { getDevicesRequestValidator } = require('./device.validator');

const BASE_PATH = `${env.config.base}/entities`;

/**
 * @swagger
 * /entities/internal/devices:
 *   get:
 *     tags:
 *       - entities
 *     summary: Retrieve device data
 *     parameters:
 *       - $ref: '#/parameters/SystemTokenParam'
 *       - $ref: '#/parameters/pageIndexParam'
 *       - $ref: '#/parameters/pageSizeParam'
 *       - $ref: '#/parameters/TenantIdParam'
 *       - name: fields[]
 *         in: query
 *         description: Fields to retrieve ["id", "name", "serialNumber", "deviceType", "createdAt", "updatedAt","installDate", "releaseVersion", "site.id","site.name", "site.tags.id", "site.visible","site.externalRefs.refId", "company.id"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *         default: ["id", "name", "serialNumber", "deviceType", "createdAt", "updatedAt", "installDate", "releaseVersion", "site.id","site.name", "site.tags.id", "site.visible", "site.externalRefs.refId", "company.id"]
 *       - name: filters[id][$operator]
 *         in: query
 *         description: Filter by device id "$eq, $ne, $in, $notIn"
 *         type: integer
 *         required: false
 *       - name: filters[site.id][$operator]
 *         in: query
 *         description: Filter by site id "$eq, $ne, $in, $notIn"
 *         type: string
 *         required: false
 *       - name: filters[site.tags.id][$operator]
 *         in: query
 *         description: Filter by site tag ids "$eq, $ne, $in, $notIn"
 *         type: integer
 *         required: false
 *       - name: filters[site.visible][$operator]
 *         in: query
 *         description: Filter by site visibility "$eq, $ne"
 *         type: string
 *         required: false
 *       - name: filters[site.externalRefs.refId][$operator]
 *         in: query
 *         description: Filter by external reference ids "$eq, $ne, $in, $notIn"
 *         type: string
 *         required: false
 *       - name: filters[site.externalRefs.refType][$operator]
 *         in: query
 *         description: Filter by external reference types "$eq, $ne, $in, $notIn"
 *         type: string
 *         required: false
 *       - name: sort[]
 *         in: query
 *         description: Fields to sort by ["id:asc", "id:asc"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *         default: ["id:asc"]
 *     responses:
 *       200:
 *         description: device data retrieved successfully
 *         schema:
 *           type: object
 *           properties:
 *             resultsMetadata:
 *               $ref: '#/definitions/ResultsMetadata'
 *             results:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: number
 *                   name:
 *                     type: string
 *                   serialNumber:
 *                     type: string
 *                   deviceType:
 *                     type: string
 *                   createdAt:
 *                     type: string
 *                   updatedAt:
 *                     type: string
 *                   site:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       tags:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                       visible:
 *                         type: boolean
 *                   company:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 * */
server.get(
  {
    path: `${BASE_PATH}/internal/devices`,
    version: '0.0.1',
  },
  [
    requiresRole([roles.ICS_SYSTEM]),
    validateHeaders({
      tenantid: Joi.string().guid().required(),
    }),
    validateParams(getDevicesRequestValidator, false, true),
    getDevicesHandler,
  ]
);

/**
 * @swagger
 * /entities/devices:
 *   get:
 *     tags:
 *       - entities
 *     summary: Retrieve device data
 *     parameters:
 *       - $ref: '#/parameters/AuthorizationTokenParam'
 *       - $ref: '#/parameters/pageIndexParam'
 *       - $ref: '#/parameters/pageSizeParam'
 *       - name: fields[]
 *         in: query
 *         description: Fields to retrieve ["id", "name", "serialNumber", "deviceType", "createdAt", "updatedAt","installDate","releaseVersion" , "site.id","site.name", "site.tags.id", "site.visible","company.id"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *         default: ["id", "name", "serialNumber", "deviceType", "createdAt", "updatedAt", "installDate", "releaseVersion", "site.id","site.name", "site.tags.id", "site.visible", "company.id"]
 *       - name: filters[id][$operator]
 *         in: query
 *         description: Filter by device id "$eq, $ne, $in, $notIn"
 *         type: integer
 *         required: false
 *       - name: filters[site.id][$operator]
 *         in: query
 *         description: Filter by site id "$eq, $ne, $in, $notIn"
 *         type: string
 *         required: false
 *       - name: filters[site.tags.id][$operator]
 *         in: query
 *         description: Filter by site tag ids "$eq, $ne, $in, $notIn"
 *         type: integer
 *         required: false
 *       - name: filters[site.visible][$operator]
 *         in: query
 *         description: Filter by site visibility "$eq, $ne"
 *         type: string
 *         required: false
 *       - name: filters[name][$operator]
 *         in: query
 *         description: Filter by device name "$eq, $ne, $contains, $notContains, $containsi, $notContainsi, $startsWith, $endsWith"
 *         type: string
 *         required: false
 *       - name: sort[]
 *         in: query
 *         description: Fields to sort by ["id:asc", "id:asc"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *         default: ["id:asc"]
 *     responses:
 *       200:
 *         description: device data retrieved successfully
 *         schema:
 *           type: object
 *           properties:
 *             resultsMetadata:
 *               $ref: '#/definitions/ResultsMetadata'
 *             results:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: number
 *                   name:
 *                     type: string
 *                   serialNumber:
 *                     type: string
 *                   deviceType:
 *                     type: string
 *                   createdAt:
 *                     type: string
 *                   updatedAt:
 *                     type: string
 *                   site:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       tags:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                       visible:
 *                         type: boolean
 *                   company:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 * */
server.get(
  {
    path: `${BASE_PATH}/devices`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    validateParams(getDevicesRequestValidator, false, true),
    getDevicesHandler,
  ]
);
