const { mapKeys, camelCase } = require('lodash');

const BaseRepository = require('../../lib/base.repository');
const {
  wrapQueryWithPagination,
  buildQuery,
  matchAndReplaceKey,
  prepareSortFields,
  prepareSelectFields,
} = require('../../lib/entities.helper');
const TagEntity = require('./site-tag.entity');

/**
 * @class
 */
class SiteTagRepository extends BaseRepository {
  constructor() {
    super(TagEntity);
  }

  /**
   * @param {import('../../lib/base.types').iFindOptions} param
   * @returns {Promise<import('./tag.types').iSiteTagWithCountsRes>}
   */
  async findTagWithSiteCounts({
    fields = [],
    filters,
    sort,
    pageIndex = 0,
    pageSize = 0,
  }) {
    const columnMapping = {
      ...this.entityClass.getEntityMeta().columnMapping,
      ...{ siteCount: 'COUNT(site.site_id)' },
    };
    const joins = [];
    const groupBy = [];

    if (fields.includes('siteCount')) {
      joins.push(
        ...[
          {
            type: 'LEFT',
            tableName: 'site_tag',
            on: 'site_tag.tag_id = tag.id AND site_tag.deleted = false',
          },
          {
            type: 'LEFT',
            tableName: 'site',
            on: 'site.site_id = site_tag.site_id AND site.active AND tag.company_id = site.company_id',
          },
        ]
      );
      groupBy.push(...['tag.id', 'tag.name']);
    }

    const { query, queryParams } = buildQuery({
      tableName: this.entityClass.getEntityMeta().tableName,
      fields: prepareSelectFields(fields, columnMapping),
      filters: matchAndReplaceKey(filters, columnMapping),
      joins,
      groupBy,
      sort: prepareSortFields(sort, columnMapping),
    });

    const paginatedQuery = wrapQueryWithPagination(
      query,
      queryParams,
      pageIndex,
      pageSize
    );
    const result = await this.dataContext.row(paginatedQuery, queryParams);

    if (result.results) {
      result.results = result.results.map(m =>
        mapKeys(m, (v, k) => camelCase(k))
      );
    }

    return {
      resultsMetadata: {
        totalResults: result.totalCount,
        pageIndex,
        pageSize,
      },
      results: result.results || [],
    };
  }
}

module.exports = SiteTagRepository;
