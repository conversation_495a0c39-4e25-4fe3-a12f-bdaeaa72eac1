const errorhandler = require('../../../../lib/errorhandler');
const { getSiteTagService } = require('./site-tag.service');

const getSiteTagHandler = async (req, res, next) => {
  try {
    const { user, params } = req;
    const result = await getSiteTagService(params, user);
    res.send(result);
    return next();
  } catch (err) {
    return errorhandler.onError(req, res, next)(err);
  }
};

module.exports = {
  getSiteTagHandler,
};
