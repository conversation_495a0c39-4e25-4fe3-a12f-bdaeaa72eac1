const Joi = require('joi');

const {
  buildFieldsValidator,
  buildFiltersValidator,
  buildSortValidator,
  buildPageSizeAndIndexValidator,
} = require('../../lib/base.validator');
const {
  stringOpts,
  equalityOpts,
  minPageSize,
  maxPageSize,
  minPageIndex,
} = require('../../lib/entities.constant');
const { SITE_TAG_MAX_PAGE_SIZE } = require('./site-tag.constant');

const getSitesRequestValidator = Joi.object({
  ...buildPageSizeAndIndexValidator(
    minPageSize,
    SITE_TAG_MAX_PAGE_SIZE,
    maxPageSize,
    minPageIndex
  ),
  fields: buildFieldsValidator(['tagId', 'tagName', 'siteCount']),
  filters: buildFiltersValidator({
    tagId: {
      operators: [...equalityOpts],
      validator: Joi.number().integer(),
    },
    tagName: {
      operators: [...stringOpts, ...equalityOpts],
      validator: Joi.string().regex(/^[A-Za-z0-9_ -]+$/),
    },
  }),
  sort: buildSortValidator(['tagId', 'tagName', 'siteCount'], ['tagName:asc']),
}).unknown(false);

module.exports = {
  getSitesRequestValidator,
};
