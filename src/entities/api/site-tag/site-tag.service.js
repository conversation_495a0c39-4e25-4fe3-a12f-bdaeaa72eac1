const { merge } = require('lodash');

const SiteTagRepository = require('./site-tag.repository');

const getSiteTagService = async (params, user) => {
  const repo = new SiteTagRepository();
  const userCompanyId = user.company.id;

  // Limit search to user company
  const uParams = params;
  uParams.filters = merge(
    { tagCompanyId: { $eq: userCompanyId } },
    uParams.filters
  );

  // eslint-disable-next-line no-return-await
  return await repo.findTagWithSiteCounts(uParams);
};

module.exports = {
  getSiteTagService,
};
