const { server } = require('../../../../app');
const env = require('../../../../env');
const { baseRoleGroup } = require('../../../../lib/app-constants');
const { validateParams, requiresRole } = require('../../../../lib/pre');
const { getSiteTagHandler } = require('./site-tag.handler');
const { getSitesRequestValidator } = require('./site-tag.validator');

const BASE_PATH = `${env.config.base}/entities`;

/**
 * @swagger
 * /entities/site-tags:
 *   get:
 *     tags:
 *       - entities
 *     summary: Retrieve site tag data
 *     parameters:
 *       - $ref: '#/parameters/AuthorizationTokenParam'
 *       - $ref: '#/parameters/pageIndexParam'
 *       - name: pageSize
 *         in: query
 *         description: The maximum number of records to return in a page
 *         required: false
 *         type: integer
 *         default: 40
 *       - name: fields[]
 *         in: query
 *         description: Fields to retrieve ["tagId", "tagName", "siteCount"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *       - name: filters[tagName][$operator]
 *         in: query
 *         description: Filter by tag name "$eq, $ne, $contains, $notContains, $startsWith, $endsWith"
 *         type: string
 *         required: false
 *       - name: filters[tagId][$operator]
 *         in: query
 *         description: Filter by tag ID "$eq, $ne, $contains, $notContains, $startsWith, $endsWith"
 *         type: integer
 *         required: false
 *       - name: sort[]
 *         in: query
 *         description: Fields to sort by ["tagId:asc", "tagName:asc", "siteCount:asc"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *     responses:
 *       200:
 *         description: Site tag data retrieved successfully
 *         schema:
 *           type: object
 *           properties:
 *             resultsMetadata:
 *               $ref: '#/definitions/ResultsMetadata'
 *             results:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   tagId:
 *                     type: integer
 *                   tagName:
 *                     type: string
 *                   siteCount:
 *                     type: integer
 *       400:
 *         $ref: "#/responses/BadRequest"
 *       401:
 *         $ref: "#/responses/AuthenticationFailed"
 *       403:
 *         $ref: "#/responses/Forbidden"
 *       500:
 *         $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/site-tags`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    validateParams(getSitesRequestValidator, false, true),
    getSiteTagHandler,
  ]
);
