const { server } = require('../../../../../app');
const env = require('../../../../../env');
const { validateParams } = require('../../../../../lib/pre');
const {
  getEntitySettingDefinitionHandler,
} = require('./setting-definition.handler');
const {
  getEntitySettingDefinitionsRequestValidator,
} = require('./setting-definition.validator');

const BASE_PATH = `${env.config.base}/entities`;

/**
 * @swagger
 * /entities/entity/definitions/${entityType}/feature/${featureName}:
 *   get:
 *     tags:
 *       - entities
 *     summary: Retrieve Entity Settings Definitions By Feature Name
 *     produces:
 *       - application/json
 *     parameters:
 *       - $ref: '#/parameters/AuthorizationTokenParam'
 *       - $ref: '#/parameters/pageIndexParam'
 *       - name: entityType
 *         in: path
 *         description: Enum EntityType = ['sites']
 *         required: true
 *         type: string
 *       - name: featureName
 *         in: path
 *         description: Identifier for the Entity feature
 *         required: true
 *         type: string
 *       - name: pageSize
 *         in: query
 *         description: The maximum number of records to return in a page
 *         required: false
 *         type: integer
 *         default: 40
 *       - name: fields[]
 *         in: query
 *         description: Fields to retrieve ["entitySettingsDefinitionsId", "featureName", "settingName", "schema","defaultValue", "entityType"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *       - name: filters[settingName][$operator]
 *         in: query
 *         description: Filter by device Type "$eq, $ne, $contains, $notContains, $startsWith, $endsWith"
 *         type: string
 *         required: false
 *       - name: sort[]
 *         in: query
 *         description: Fields to sort by ["settingName:asc"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *     responses:
 *       200:
 *         description: Entity Feature Definition data retrieved successfully
 *         schema:
 *           type: object
 *           properties:
 *             resultsMetadata:
 *               $ref: '#/definitions/ResultsMetadata'
 *             results:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   entitySettingsDefinitionsId:
 *                     type: integer
 *                   featureName:
 *                     type: string
 *                   settingName:
 *                     type: string
 *                   schema:
 *                     type: string
 *                   defaultValue:
 *                     type: string
 *       400:
 *         $ref: "#/responses/BadRequest"
 *       401:
 *         $ref: "#/responses/AuthenticationFailed"
 *       403:
 *         $ref: "#/responses/Forbidden"
 *       500:
 *         $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/entity/definitions/:entityType/feature/:featureName`,
    version: '0.0.1',
  },
  [
    validateParams(getEntitySettingDefinitionsRequestValidator, false, true),
    getEntitySettingDefinitionHandler,
  ]
);
