const { mapKeys, camelCase } = require('lodash');

const BaseRepository = require('../../../lib/base.repository');
const {
  wrapQueryWithPagination,
  buildQuery,
  matchAndReplaceKey,
  prepareSortFields,
  prepareSelectFields,
} = require('../../../lib/entities.helper');
const SettingDefinitionEntity = require('./setting-definition.entity');

/**
 * @class
 */
class EntitySettingDefinitionRepository extends BaseRepository {
  constructor() {
    super(SettingDefinitionEntity);
  }

  /**
   * @param {import('../../../lib/base.types').iFindOptions} param
   * @returns {Promise<import('./setting-definition.types').iEntitySettingDefinitionRes>}
   */
  async findSettingDefinition({
    fields = [],
    filters,
    sort,
    pageIndex = 0,
    pageSize = 0,
  }) {
    const columnMapping = {
      ...this.entityClass.getEntityMeta().columnMapping,
    };
    const joins = [];
    const groupBy = [];

    const { query, queryParams } = buildQuery({
      tableName: this.entityClass.getEntityMeta().tableName,
      fields: prepareSelectFields(fields, columnMapping),
      filters: matchAndReplaceKey(filters, columnMapping),
      joins,
      groupBy,
      sort: prepareSortFields(sort, columnMapping),
    });

    const paginatedQuery = wrapQueryWithPagination(
      query,
      queryParams,
      pageIndex,
      pageSize
    );

    const result = await this.dataContext.row(paginatedQuery, queryParams);

    if (result.results) {
      result.results = result.results.map(m =>
        mapKeys(m, (v, k) => camelCase(k))
      );
    }

    return {
      resultsMetadata: {
        totalResults: result.totalCount,
        pageIndex,
        pageSize,
      },
      results: result.results || [],
    };
  }

  async getEntitySettingsDefinition(settingsDefinitionsId) {
    const groupBy = [];
    const sort = [];
    const joins = [];
    const { columnMapping } = this.entityClass.getEntityMeta();
    const { query, queryParams } = buildQuery({
      tableName: this.entityClass.getEntityMeta().tableName,
      fields: prepareSelectFields(
        [
          'entitySettingsDefinitionsId',
          'entityType',
          'featureName',
          'settingName',
          'schema',
          'defaultValue',
        ],
        columnMapping
      ),
      filters: matchAndReplaceKey(
        {
          entitySettingsDefinitionsId: { $eq: settingsDefinitionsId },
        },
        columnMapping
      ),
      joins,
      groupBy,
      sort,
    });

    // eslint-disable-next-line no-return-await
    return await this.dataContext.row(query, queryParams);
  }
}

module.exports = EntitySettingDefinitionRepository;
