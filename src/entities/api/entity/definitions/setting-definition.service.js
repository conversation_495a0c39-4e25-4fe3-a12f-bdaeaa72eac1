const EntitySettingDefinitionRepository = require('./setting-definition.repository');

const getEntitySettingDefinitionService = async params => {
  const repo = new EntitySettingDefinitionRepository();

  // eslint-disable-next-line no-return-await
  return await repo.findSettingDefinition(params);
};

const getEntitySettingDefinitionById = async entitySettingsDefinitionsId => {
  const repo = new EntitySettingDefinitionRepository();

  // eslint-disable-next-line no-return-await
  return await repo.getEntitySettingsDefinition(entitySettingsDefinitionsId);
};

module.exports = {
  getEntitySettingDefinitionService,
  getEntitySettingDefinitionById,
};
