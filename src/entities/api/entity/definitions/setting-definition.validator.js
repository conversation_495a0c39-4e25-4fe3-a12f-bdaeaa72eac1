const Joi = require('joi');

const {
  baseEntityValidatorObjectFields,
  buildFieldsValidator,
  buildFiltersValidator,
  buildSortValidator,
} = require('../../../lib/base.validator');
const {
  stringOpts,
  equalityOpts,
  arrayOpts,
} = require('../../../lib/entities.constant');
const { supportedEntityTypes } = require('../settings-constants');

const getEntitySettingDefinitionsRequestValidator = Joi.object({
  ...baseEntityValidatorObjectFields,
  entityType: Joi.string().valid(...supportedEntityTypes),
  featureName: Joi.string().required(),
  fields: buildFieldsValidator(
    [
      'entitySettingsDefinitionsId',
      'entityType',
      'featureName',
      'settingName',
      'schema',
      'defaultValue',
    ],
    [
      'entitySettingsDefinitionsId',
      'entityType',
      'featureName',
      'settingName',
      'schema',
      'defaultValue',
    ]
  ),
  filters: buildFiltersValidator({
    settingName: {
      operators: [...stringOpts, ...equalityOpts, ...arrayOpts],
      validator: Joi.string(),
    },
  }),
  sort: buildSortValidator(['settingName'], ['settingName:desc']),
}).unknown(false);

module.exports = {
  getEntitySettingDefinitionsRequestValidator,
};
