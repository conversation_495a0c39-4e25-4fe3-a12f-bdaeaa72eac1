const BaseEntity = require('../../../lib/base.entity');

class EntitySettingDefinitionEntity extends BaseEntity {
  static getEntityMeta() {
    return {
      tableName: 'entity_settings_definitions',
      columnMapping: {
        entitySettingsDefinitionsId:
          'entity_settings_definitions.entity_settings_definitions_id',
        featureName: 'entity_settings_definitions.feature_name',
        settingName: 'entity_settings_definitions.setting_name',
        schema: 'entity_settings_definitions.schema',
        defaultValue: 'entity_settings_definitions.default_value',
        entityType: 'entity_settings_definitions.entity_type',
      },
    };
  }
}

module.exports = EntitySettingDefinitionEntity;
