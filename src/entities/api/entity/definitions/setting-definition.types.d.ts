/* eslint-disable import/no-unused-modules */
import { iResultsMetaData } from '../../../lib/base.types';

export interface iSettingDefinitionEntity {
  entitySettingsDefinitionsId?: number;
  entityType?: number;
  featureName?: string;
  settingName?: string;
  schema?: string;
  defaultValue?: string;
}

export interface iEntitySettingDefinitions {
  entitySettingsDefinitionsId: number;
  entityType: number;
  featureName: string;
  settingName: string;
  schema: string;
  defaultValue: string;
}
export interface iEntitySettingDefinitionRes {
  resultsMetadata: iResultsMetaData;
  results: iEntitySettingDefinitions[];
}
