const { merge } = require('lodash');

const errorhandler = require('../../../../../lib/errorhandler');
const { supportedEntityTypesInternalCode } = require('../settings-constants');
const {
  getEntitySettingDefinitionService,
} = require('./setting-definition.service');

const getEntitySettingDefinitionHandler = async (req, res, next) => {
  try {
    const { params } = req;
    const uParams = params;
    uParams.filters = merge(
      {
        featureName: { $eq: params.featureName },
        entityType: {
          $eq: supportedEntityTypesInternalCode[params.entityType],
        }, // ENUM 2 - Sites
        deleted: { $eq: false },
      },
      uParams.filters
    );
    const result = await getEntitySettingDefinitionService(uParams);
    res.send(result);
    return next();
  } catch (err) {
    return errorhandler.onError(req, res, next)(err);
  }
};

module.exports = {
  getEntitySettingDefinitionHandler,
};
