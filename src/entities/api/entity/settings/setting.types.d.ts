/* eslint-disable import/no-unused-modules */
import { iResultsMetaData } from '../../../lib/base.types';

export interface iSettingEntity {
  entitySettingsId?: number;
  entitySettingsDefinitionsId?: number;
  entityId?: string;
  entityValue?: string;
  entityType?: number;
  featureName?: string;
  settingName?: string;
}

export interface iEntitySettings {
  entitySettingsId: number;
  entitySettingsDefinitionsId: number;
  entityId: string;
  entityValue: string;
  entityType: number;
  featureName: string;
  settingName: string;
}
export interface iEntitySettingRes {
  resultsMetadata: iResultsMetaData;
  results: iEntitySettings[];
}
