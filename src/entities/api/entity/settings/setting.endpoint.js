const Joi = require('joi');
// @ts-check
const { server } = require('../../../../../app');
const env = require('../../../../../env');
const {
  requiresRole,
  validateParams,
  validateBody,
} = require('../../../../../lib/pre');
const { supportedEntityTypes } = require('../settings-constants');
const {
  getEntitySettingHandler,
  putEntitySettingHandler,
} = require('./setting.handler');
const { getEntitySettingsRequestValidator } = require('./setting.validator');

const BASE_PATH = `${env.config.base}/entities`;

/**
 * @swagger
 * /entities/entity/settings/${entityType}/${entityId}:
 *   get:
 *     tags:
 *       - entities
 *     summary: Retrieve Entity Feature Definitions By Entity Type And Entity Id
 *     produces:
 *       - application/json
 *     parameters:
 *       - $ref: '#/parameters/AuthorizationTokenParam'
 *       - $ref: '#/parameters/pageIndexParam'
 *       - name: entityType
 *         in: path
 *         description: Enum EntityType = ['sites']
 *         required: true
 *         type: string
 *       - name: entityId
 *         in: path
 *         description: Identifier for the entity id
 *         required: true
 *         type: string
 *       - name: pageSize
 *         in: query
 *         description: The maximum number of records to return in a page
 *         required: false
 *         type: integer
 *         default: 40
 *       - name: fields[]
 *         in: query
 *         description: Fields to retrieve ["entitySettingsId", "entitySettingsDefinitionsId", "entityId", "entityValue", "entityType", "featureName", "settingName"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *       - name: filters[featureName][$operator]
 *         in: query
 *         description: Filter by device Type "$eq, $ne, $contains, $notContains, $startsWith, $endsWith"
 *         type: string
 *         required: false
 *       - name: filters[settingName][$operator]
 *         in: query
 *         description: Filter by device Type "$eq, $ne, $contains, $notContains, $startsWith, $endsWith"
 *         type: string
 *         required: false
 *       - name: sort[]
 *         in: query
 *         description: Fields to sort by ["featureName:asc", "settingName:asc"]
 *         type: array
 *         items:
 *           type: string
 *         required: false
 *     responses:
 *       200:
 *         description: Entity Definition Settings data retrieved successfully
 *         schema:
 *           type: object
 *           properties:
 *             resultsMetadata:
 *               $ref: '#/definitions/ResultsMetadata'
 *             results:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   entitySettingsId:
 *                     type: integer
 *                   entitySettingsDefinitionsId:
 *                     type: integer
 *                   entityId:
 *                     type: string
 *                   entityValue:
 *                     type: string
 *                   featureName:
 *                     type: string
 *                   settingName:
 *                     type: string
 *       400:
 *         $ref: "#/responses/BadRequest"
 *       401:
 *         $ref: "#/responses/AuthenticationFailed"
 *       403:
 *         $ref: "#/responses/Forbidden"
 *       500:
 *         $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/entity/settings/:entityType/:entityId`,
    version: '0.0.1',
  },
  [
    // requiresRole(['VIEW_ENTITY_SETTINGS']),
    validateParams(getEntitySettingsRequestValidator, false, true),
    getEntitySettingHandler,
  ]
);

/**
 * @swagger
 * /entities/entity/settings/${entityType}/${entityId}:
 *   put:
 *     tags:
 *       - entities
 *     summary: Patch Entity Feature Settings By entity settings id
 *     consumes:
 *       - application/json
 *     produces:
 *       - application/json
 *     parameters:
 *       - $ref: '#/parameters/AuthorizationTokenParam'
 *       - name: entityType
 *         in: path
 *         description: Enum EntityType = ['sites']
 *         required: true
 *         type: string
 *       - name: entityId
 *         in: path
 *         description: Identifier for the entity id
 *         required: true
 *         type: string
 *       - name: Entity Settings Payload
 *         in: body
 *         required: true
 *         schema:
 *          type: object
 *          properties:
 *            settings:
 *              type: array
 *              items:
 *                type: object
 *                properties:
 *                  entitySettingsDefinitionsId:
 *                    type: number
 *                  entitySettingsId:
 *                    type: number
 *                  entityValue:
 *                    type: string
 *     responses:
 *       200:
 *         description: Entity Definition Settings data patch successfully
 *         schema:
 *           type: object
 *           properties:
 *             resultsMetadata:
 *               $ref: '#/definitions/ResultsMetadata'
 *             results:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   entitySettingsId:
 *                     type: integer
 *                   entitySettingsDefinitionsId:
 *                     type: integer
 *                   entityId:
 *                     type: string
 *                   entityValue:
 *                     type: string
 *       400:
 *         $ref: "#/responses/BadRequest"
 *       401:
 *         $ref: "#/responses/AuthenticationFailed"
 *       403:
 *         $ref: "#/responses/Forbidden"
 *       500:
 *         $ref: "#/responses/InternalServerError"
 */
server.put(
  {
    path: `${BASE_PATH}/entity/settings/:entityType/:entityId`,
    version: '0.0.1',
  },
  [
    requiresRole(['SITE_SETTINGS_EDIT']),
    validateParams({
      entityType: Joi.string().valid(...supportedEntityTypes),
      entityId: Joi.string().guid().required(),
    }),
    validateBody({
      settings: Joi.array()
        .min(1)
        .items(
          Joi.object().keys({
            entitySettingsDefinitionsId: Joi.number().required(),
            entitySettingsId: Joi.number().optional(),
            entityValue: Joi.string().allow('').allow(null).optional(),
          })
        )
        .required(),
    }),
    putEntitySettingHandler,
  ]
);
