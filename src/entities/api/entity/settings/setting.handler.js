const restify = require('restify');
// @ts-check
const { merge, isEmpty } = require('lodash');
const errorhandler = require('../../../../../lib/errorhandler');
const { supportedEntityTypesInternalCode } = require('../settings-constants');
const logger = require('../../../../../lib/logger').mainLogger();
const {
  getEntitySettingService,
  putBatchEntitySettingService,
} = require('./setting.service');
const {
  validateDuplicateSetting,
  validateUpdateSettings,
} = require('./setting.validator');

const getEntitySettingHandler = async (req, res, next) => {
  try {
    const { params } = req;
    const uParams = params;
    uParams.filters = merge(
      {
        entityId: { $eq: params.entityId },
        entityType: {
          $eq: supportedEntityTypesInternalCode[params.entityType],
        }, // ENUM 2 - Sites
        deleted: { $eq: false },
      },
      uParams.filters
    );

    const result = await getEntitySettingService(uParams);
    res.send(result);
    return next();
  } catch (err) {
    return errorhandler.onError(req, res, next)(err);
  }
};

const putEntitySettingHandler = async (req, res, next) => {
  const { params, body, user } = req;
  const { settings } = body;
  const { entityId } = params;

  const entityType = supportedEntityTypesInternalCode[params.entityType];

  try {
    const { updSettings, errors } = await validateUpdateSettings(
      entityId,
      entityType,
      settings
    );
    if (isEmpty(errors)) {
      const dupErrorEvent = await validateDuplicateSetting(
        entityId,
        updSettings
      );
      if (!dupErrorEvent) {
        const results = await putBatchEntitySettingService(
          user,
          params,
          updSettings
        );

        res.send(results);
        return next();
      }
      return next(new restify.BadRequestError(dupErrorEvent.error)); // 400 error
    }
    if (errors.length > 1)
      return next(
        new restify.BadRequestError(
          `Multiple Validation Errors! ${errors[0].error}, ${errors[1].error} ${
            errors.length > 2 ? ',...' : ''
          }`
        )
      ); // 400 error

    return next(new restify.BadRequestError(errors[0].error)); // 400 error
  } catch (err) {
    logger.error({ err }, '[putEntitySettingHandler]');
    return next(new restify.BadRequestError(err)); // 400 error
  }
};

module.exports = {
  getEntitySettingHandler,
  putEntitySettingHandler,
};
