/* eslint-disable import/no-unused-modules */
import { iResultsMetaData } from '../../../lib/base.types';

export interface iSettingHistoryEntity {
  entitySettingsHistoryId?: number;
  entitySettingsId?: number;
  entitySettingsDefinitionsId?: number;
  entityValue?: string;
  createdBy?: string;
}

export interface iEntitySettingsHistory {
  entitySettingsHistoryId: number;
  entitySettingsId: number;
  entitySettingsDefinitionsId: number;
  entityValue: string;
  createdBy: string;
}
export interface iEntitySettingHistoryRes {
  resultsMetadata: iResultsMetaData;
  results: iEntitySettingsHistory[];
}
