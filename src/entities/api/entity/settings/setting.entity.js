const BaseEntity = require('../../../lib/base.entity');

class EntitySettingEntity extends BaseEntity {
  static getEntityMeta() {
    return {
      tableName: 'entity_settings',
      columnMapping: {
        entitySettingsId: 'entity_settings.entity_settings_id',
        entitySettingsDefinitionsId:
          'entity_settings.entity_settings_definitions_id',
        entityId: 'entity_settings.entity_id',
        entityValue: 'entity_settings.entity_value',
        createdBy: 'entity_settings.created_by',
        createdDate: 'entity_settings.created_date',
        updatedBy: 'entity_settings.updated_by',
        updatedDate: 'entity_settings.updated_date',
      },
    };
  }
}

module.exports = EntitySettingEntity;
