const Joi = require('joi');
const { Validator } = require('jsonschema');
const { isEmpty } = require('lodash');

const {
  baseEntityValidatorObjectFields,
  buildFieldsValidator,
  buildFiltersValidator,
  buildSortValidator,
} = require('../../../lib/base.validator');
const {
  stringOpts,
  arrayOpts,
  equalityOpts,
} = require('../../../lib/entities.constant');
const logger = require('../../../../../lib/logger').mainLogger();
const {
  getEntitySettingDefinitionById,
} = require('../definitions/setting-definition.service');
const { supportedEntityTypes } = require('../settings-constants');
const {
  getEntitySettingByForeignKey,
  getEntitySettings,
} = require('./setting.service');

const getEntitySettingsRequestValidator = Joi.object({
  ...baseEntityValidatorObjectFields,
  entityType: Joi.string().valid(...supportedEntityTypes),
  entityId: Joi.string().required(),
  fields: buildFieldsValidator([
    'entitySettingsId',
    'entitySettingsDefinitionsId',
    'entityId',
    'entityType',
    'entityValue',
    'featureName',
    'settingName',
  ]),
  filters: buildFiltersValidator({
    featureName: {
      operators: [...stringOpts, ...arrayOpts, ...equalityOpts],
      validator: Joi.string(),
    },
    settingName: {
      operators: [...stringOpts, ...arrayOpts, ...equalityOpts],
      validator: Joi.string(),
    },
  }),
  sort: buildSortValidator(
    ['featureName', 'settingName'],
    ['featureName:asc', 'settingName:asc']
  ),
}).unknown(false);

/**
 * @private
 * @param {any} value
 * @param {object} schema
 * @returns
 */
const validateSchema = (value, schema) => {
  if (!schema) {
    return undefined;
  }

  // Remove quotes if validating against a string enum
  const validationResponse = new Validator().validate(value, schema);
  if (validationResponse.errors.length) {
    const errors = validationResponse.errors.map(
      error => `value ${error.instance} ${error.message}`
    );
    const uniqueErrors = [...new Set(errors)];
    const hasMultipleErrors = uniqueErrors.length > 1;

    const validationError = hasMultipleErrors
      ? {
          error: `Multiple errors found with value ${value}`,
          context: uniqueErrors,
          schema,
        }
      : {
          error: uniqueErrors[0],
          schema,
        };
    return validationError;
  }
  return undefined;
};

const validSettingValue = (valueStr, schemaStr) => {
  const schema = JSON.parse(schemaStr);
  const entityValue = JSON.parse(valueStr);

  return validateSchema(entityValue, schema);
};

const doValidateSetting = async (setting, entityId, entityType) => {
  const { entitySettingsId, entitySettingsDefinitionsId } = setting;

  const entityDefinition = await getEntitySettingDefinitionById(
    entitySettingsDefinitionsId
  );

  if (!entityDefinition) {
    const err = {
      error: `Invalid Entity Settings Definition Id ${entitySettingsDefinitionsId}`,
    };
    logger.error(err, '[doValidateSetting]:[entityDefinition]');
    return { updSetting: setting, errorEvent: err };
  }

  if (entityDefinition.entityType !== entityType) {
    const err = {
      error: `Invalid Entity Type! Definition Entity Type ${entityDefinition.entityType} Does Not Match Parameter ${entityType}`,
    };
    logger.error(err, '[doValidateSetting]:[entityType]');
    return { updSetting: setting, errorEvent: err };
  }

  const entitySetting = await getEntitySettings(entitySettingsId);
  if (entitySettingsId) {
    if (!entitySetting) {
      const err = {
        error: `Invalid Entity Settings Id ${entitySettingsId}`,
      };
      logger.error(err, '[doValidateSetting]:[entitySetting]');
      return { updSetting: setting, errorEvent: err };
    }

    if (entitySetting.entityId !== entityId) {
      const err = {
        error: `Invalid Entity Id! Settings Entity Id ${entitySetting.entityId} Does Not Match ${entityId}`,
      };
      logger.error(err, '[doValidateSetting]:[entityId]');
      return { updSetting: setting, errorEvent: err };
    }

    if (
      entitySetting.entitySettingsDefinitionsId !== entitySettingsDefinitionsId
    ) {
      const err = {
        error: `Invalid Entity Settings Definition Id!  Entity Settings Definition Id ${entitySetting.entitySettingsDefinitionsId} Does Not Match Parameter ${entitySettingsDefinitionsId}`,
      };
      logger.error(err, '[doValidateSetting]:[entitySettingsDefinitionsId]');
      return { updSetting: setting, errorEvent: err };
    }
  }

  const validationErrors = validSettingValue(
    setting.entityValue,
    entityDefinition.schema
  );
  if (!isEmpty(validationErrors)) {
    logger.error(validationErrors, '[doValidateSetting]:[validationErrors]');
    return { updSetting: setting, errorEvent: validationErrors };
  }
  if (entitySetting) {
    entitySetting.entityValue = setting.entityValue;
  }
  return { updSetting: entitySetting || setting };
};

const validateDuplicateSetting = async (entityId, updSettings) => {
  const insertSettings = updSettings.filter(
    setting => !setting.entitySettingsId
  );

  const checkDuplicateInsert = inputArray => {
    const uniqueInsert = new Set();
    const duplicatedElement = [];

    inputArray.forEach(elem => {
      if (!elem.entitySettingsId) {
        if (uniqueInsert.has(elem.entitySettingsDefinitionsId)) {
          duplicatedElement.push(elem);
        }
        uniqueInsert.add(elem.entitySettingsDefinitionsId);
      }
    });

    return Array.from(new Set(duplicatedElement));
  };

  const duplicates = checkDuplicateInsert(insertSettings);
  if (isEmpty(duplicates)) {
    const settingsInDB = await Promise.all(
      insertSettings.map(setting =>
        getEntitySettingByForeignKey(
          entityId,
          setting.entitySettingsDefinitionsId
        )
      )
    );
    const foundSetting = settingsInDB.filter(setting => !isEmpty(setting));
    if (isEmpty(foundSetting)) return undefined;

    const err = {
      error: 'Duplicated Entity Setting',
      data: foundSetting,
    };
    logger.error(err, '[validateDuplicateSetting]:[foundSetting]');
    return err;
  }

  const err = {
    error: 'Duplicated Entity Setting',
    data: duplicates,
  };
  logger.error(err, '[validateDuplicateSetting]:[duplicates]');
  return err;
};

const validateUpdateSettings = async (entityId, entityType, settings) => {
  const validationResult = await Promise.all(
    settings.map(setting => doValidateSetting(setting, entityId, entityType))
  );
  const errors = [];
  const updSettings = [];
  validationResult.map(result => {
    if (result.errorEvent) {
      errors.push(result.errorEvent);
    } else {
      updSettings.push(result.updSetting);
    }
    return result;
  });
  return { updSettings, errors };
};

module.exports = {
  getEntitySettingsRequestValidator,
  validateUpdateSettings,
  validateDuplicateSetting,
};
