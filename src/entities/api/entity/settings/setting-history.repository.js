const { mapKeys, camelCase } = require('lodash');
// @ts-check
// @ts-ignore

const BaseRepository = require('../../../lib/base.repository');
const {
  wrapQueryWithPagination,
  buildQuery,
  matchAndReplaceKey,
  prepareSortFields,
  prepareSelectFields,
} = require('../../../lib/entities.helper');
const EntitySettingHistoryEntity = require('./setting-history.entity');

/**
 * @class
 */
class EntitySettingHistoryRepository extends BaseRepository {
  constructor() {
    super(EntitySettingHistoryEntity);
  }

  /**
   * @param {import('../../../lib/base.types').iFindOptions} param
   * @returns {Promise<import('./setting-history.types').iEntitySettingHistoryRes>}
   */
  async findEntitySettingHistory({
    fields = [],
    filters,
    sort,
    pageIndex = 0,
    pageSize = 0,
  }) {
    const { columnMapping } = this.entityClass.getEntityMeta();
    const joins = [];
    const groupBy = [];

    const { query, queryParams } = buildQuery({
      tableName: this.entityClass.getEntityMeta().tableName,
      fields: prepareSelectFields(fields, columnMapping),
      filters: matchAndReplaceKey(filters, columnMapping),
      joins,
      groupBy,
      sort: prepareSortFields(sort, columnMapping),
    });

    const paginatedQuery = wrapQueryWithPagination(
      query,
      queryParams,
      pageIndex,
      pageSize
    );

    const result = await this.dataContext.row(paginatedQuery, queryParams);

    if (result.results) {
      result.results = result.results.map(m =>
        mapKeys(m, (v, k) => camelCase(k))
      );
    }

    return {
      resultsMetadata: {
        totalResults: result.totalCount,
        pageIndex,
        pageSize,
      },
      results: result.results || [],
    };
  }
}

module.exports = EntitySettingHistoryRepository;
