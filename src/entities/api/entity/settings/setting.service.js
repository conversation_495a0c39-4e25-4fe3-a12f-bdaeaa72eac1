const { server } = require('../../../../../app');
// @ts-check
const EntitySettingRepository = require('./setting.repository');
const EntitySettingHistoryRepository = require('./setting-history.repository');

const putEntitySetting = async (
  txConnection,
  userId,
  now,
  entityId,
  setting
) => {
  const connection = txConnection || (await server.db.write.getConnection());

  const entitySettingRepo = new EntitySettingRepository();
  const processSetting = async () => {
    if (setting.entitySettingsId) {
      const updDTO = {
        ...setting,
        updatedBy: userId,
        updatedDate: now,
      };

      // eslint-disable-next-line no-return-await
      return await entitySettingRepo.put(
        txConnection,
        updDTO,
        ['entitySettingsId'],
        [
          'entitySettingsId',
          'entitySettingsDefinitionsId',
          'entityId',
          'entityValue',
        ],
        ['entityValue', 'updatedDate', 'updatedBy']
      );
    }
    const insertDTO = {
      ...setting,
      entityId,
      createdBy: userId,
    };

    // eslint-disable-next-line no-return-await
    return await entitySettingRepo.post(txConnection, insertDTO, [
      'entitySettingsId',
      'entitySettingsDefinitionsId',
      'entityId',
      'entityValue',
    ]);
  };

  try {
    if (!txConnection) await connection.execute('BEGIN');

    const entitySetting = await processSetting();
    const { entitySettingsId, entityValue } = entitySetting;
    const entitySettingHistoryRepo = new EntitySettingHistoryRepository();
    await entitySettingHistoryRepo.post(
      txConnection,
      {
        entitySettingsId,
        entityValue,
        createdBy: userId,
      },
      []
    );
    if (!txConnection) {
      await connection.execute('COMMIT');
      connection.done();
    }
    return entitySetting;
  } catch (err) {
    if (!txConnection) {
      await connection.execute('ROLLBACK');
      connection.done();
    }
    throw err;
  }
};

const putBatchEntitySettings = async (userId, entityId, entitySettings) => {
  const now = new Date();
  const connection = await server.db.write.getConnection();

  try {
    await connection.execute('BEGIN');

    const results = await Promise.all(
      entitySettings.map(setting =>
        putEntitySetting(connection, userId, now, entityId, setting)
      )
    );
    await connection.execute('COMMIT');
    connection.done();
    return results;
  } catch (err) {
    await connection.execute('ROLLBACK');
    connection.done();
    throw err;
  }
};

const getEntitySettings = async entitySettingsId => {
  if (!entitySettingsId) {
    return undefined;
  }
  const repo = new EntitySettingRepository();

  // eslint-disable-next-line no-return-await
  return await repo.getEntitySetting(entitySettingsId);
};

const getEntitySettingByForeignKey = async (
  entitySettingsId,
  entitySettingsDefinitionsId
) => {
  if (!entitySettingsId || !entitySettingsDefinitionsId) {
    return undefined;
  }
  const repo = new EntitySettingRepository();

  // eslint-disable-next-line no-return-await
  return await repo.getEntitySettingByForeignKey(
    entitySettingsId,
    entitySettingsDefinitionsId
  );
};

const getEntitySettingService = async params => {
  const repo = new EntitySettingRepository();

  // eslint-disable-next-line no-return-await
  return await repo.findEntitySetting(params);
};

const putBatchEntitySettingService = async (user, params, entitySettings) => {
  const userId = user.sub;
  const { entityId } = params;

  // eslint-disable-next-line no-return-await
  return await putBatchEntitySettings(userId, entityId, entitySettings);
};

module.exports = {
  getEntitySettingService,
  putBatchEntitySettingService,
  getEntitySettings,
  getEntitySettingByForeignKey,
};
