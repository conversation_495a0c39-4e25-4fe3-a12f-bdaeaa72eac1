const { mapKeys, camelCase } = require('lodash');

const BaseRepository = require('../../../lib/base.repository');
const {
  wrapQueryWithPagination,
  buildQuery,
  matchAndReplaceKey,
  prepareSortFields,
  prepareSelectFields,
} = require('../../../lib/entities.helper');
const SettingDefinitionEntity = require('../definitions/setting-definition.entity');
const EntitySettingEntity = require('./setting.entity');

/**
 * @class
 */
class EntitySettingRepository extends BaseRepository {
  constructor() {
    super(EntitySettingEntity);
  }

  getColumnMapping() {
    const joins = [];
    const settingDefinitionMeta = SettingDefinitionEntity.getEntityMeta();
    const { columnMapping } = this.entityClass.getEntityMeta();

    const columnMappingSelect = {
      ...columnMapping,
      ...{
        entityType: `${settingDefinitionMeta.columnMapping.entityType}`,
        featureName: `${settingDefinitionMeta.columnMapping.featureName}`,
        settingName: `${settingDefinitionMeta.columnMapping.settingName}`,
      },
    };

    joins.push({
      type: 'INNER',
      tableName: `${settingDefinitionMeta.tableName}`,
      on: `${settingDefinitionMeta.columnMapping.entitySettingsDefinitionsId} = entity_settings.entity_settings_definitions_id`,
    });
    return { joins, columnMapping, columnMappingSelect };
  }

  /**
   * @param {import('../../../lib/base.types').iFindOptions} param
   * @returns {Promise<import('./setting.types').iEntitySettingRes>}
   */
  async findEntitySetting({
    fields = [],
    filters = [],
    sort = [],
    pageIndex = 0,
    pageSize = 0,
  }) {
    const groupBy = [];
    const { joins, columnMappingSelect } = this.getColumnMapping();

    const { query, queryParams } = buildQuery({
      tableName: this.entityClass.getEntityMeta().tableName,
      fields: prepareSelectFields(fields, columnMappingSelect),
      filters: matchAndReplaceKey(filters, columnMappingSelect),
      joins,
      groupBy,
      sort: prepareSortFields(sort, columnMappingSelect),
    });
    const paginatedQuery = wrapQueryWithPagination(
      query,
      queryParams,
      pageIndex,
      pageSize
    );
    const result = await this.dataContext.row(paginatedQuery, queryParams);

    if (result.results) {
      result.results = result.results.map(m =>
        mapKeys(m, (v, k) => camelCase(k))
      );
    }

    return {
      resultsMetadata: {
        totalResults: result.totalCount,
        pageIndex,
        pageSize,
      },
      results: result.results || [],
    };
  }

  async getEntitySetting(entitySettingsId) {
    const groupBy = [];
    const sort = [];
    const { joins, columnMappingSelect, columnMapping } =
      this.getColumnMapping();
    const { query, queryParams } = buildQuery({
      tableName: this.entityClass.getEntityMeta().tableName,
      fields: prepareSelectFields(
        [
          'entitySettingsId',
          'entitySettingsDefinitionsId',
          'entityId',
          'entityType',
          'entityValue',
          'featureName',
          'settingName',
        ],
        columnMappingSelect
      ),
      filters: matchAndReplaceKey(
        {
          entitySettingsId: { $eq: entitySettingsId },
        },
        columnMapping
      ),
      joins,
      groupBy,
      sort,
    });
    // eslint-disable-next-line no-return-await
    return await this.dataContext.row(query, queryParams);
  }

  async getEntitySettingByForeignKey(entityId, entitySettingsDefinitionsId) {
    const groupBy = [];
    const sort = [];
    const { joins, columnMappingSelect, columnMapping } =
      this.getColumnMapping();
    const { query, queryParams } = buildQuery({
      tableName: this.entityClass.getEntityMeta().tableName,
      fields: prepareSelectFields(
        [
          'entitySettingsId',
          'entitySettingsDefinitionsId',
          'entityId',
          'entityType',
          'entityValue',
          'featureName',
          'settingName',
        ],
        columnMappingSelect
      ),
      filters: matchAndReplaceKey(
        {
          entityId: { $eq: entityId },
          entitySettingsDefinitionsId: { $eq: entitySettingsDefinitionsId },
        },
        columnMapping
      ),
      joins,
      groupBy,
      sort,
    });

    // eslint-disable-next-line no-return-await
    return await this.dataContext.row(query, queryParams);
  }

  async putEntitySetting(txConnection, entitySettingDTO) {
    // eslint-disable-next-line no-return-await
    return await this.put(
      txConnection,
      entitySettingDTO,
      ['entitySettingsId'],
      ['entitySettingsId', 'entitySettingsDefinitionsId', 'entityValue'],
      ['entityValue', 'updatedDate', 'updatedBy']
    );
  }

  async insertEntitySetting(txConnection, entitySettingDTO) {
    // eslint-disable-next-line no-return-await
    return await this.post(txConnection, entitySettingDTO, [
      'entitySettingsId',
      'entitySettingsDefinitionsId',
      'entityId',
      'entityValue',
    ]);
  }
}

module.exports = EntitySettingRepository;
