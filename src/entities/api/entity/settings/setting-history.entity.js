const BaseEntity = require('../../../lib/base.entity');

class EntitySettingHistoryEntity extends BaseEntity {
  static getEntityMeta() {
    return {
      tableName: 'entity_settings_history',
      columnMapping: {
        entitySettingsHistoryId:
          'entity_settings_history.entity_settings_history_id',
        entitySettingsId: 'entity_settings_history.entity_settings_id',
        entityValue: 'entity_settings_history.entity_value',
        createdBy: 'entity_settings_history.created_by',
      },
    };
  }
}

module.exports = EntitySettingHistoryEntity;
