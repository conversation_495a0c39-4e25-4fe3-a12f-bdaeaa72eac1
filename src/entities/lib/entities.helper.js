const { snakeCase, isEmpty } = require('lodash');
const restify = require('restify');

const logger = require('../../../lib/logger').mainLogger();

const getColumnName = (entityColumnNameMapping, column) =>
  Object.keys(entityColumnNameMapping).find(
    key => entityColumnNameMapping[key] === column
  );

/* eslint-disable no-case-declarations */
const buildWhereClause = (column, values, index) => {
  const whereValues = [];
  let whereClause = '';
  let paramIndex = index;

  // eslint-disable-next-line no-restricted-syntax
  for (const [operator, value] of Object.entries(values)) {
    switch (operator) {
      case '$eq':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} = $${paramIndex}`;
        whereValues.push(value);
        paramIndex += 1;
        break;
      case '$eqi':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } LOWER(${column}) = LOWER($${paramIndex})`;
        whereValues.push(value);
        paramIndex += 1;
        break;
      case '$ne':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} != $${paramIndex}`;
        whereValues.push(value);
        paramIndex += 1;
        break;
      case '$lt':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} < $${paramIndex}`;
        whereValues.push(value);
        paramIndex += 1;
        break;
      case '$lte':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} <= $${paramIndex}`;
        whereValues.push(value);
        paramIndex += 1;
        break;
      case '$gt':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} > $${paramIndex}`;
        whereValues.push(value);
        paramIndex += 1;
        break;
      case '$gte':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} >= $${paramIndex}`;
        whereValues.push(value);
        paramIndex += 1;
        break;
      case '$in':
        let inValueArr = value;
        if (!Array.isArray(value)) {
          inValueArr = [value];
        }

        if (inValueArr.length <= 0) {
          break;
        }

        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} IN (${inValueArr
          // eslint-disable-next-line no-loop-func
          .map(() => {
            const clause = `$${paramIndex}`;
            paramIndex += 1;
            return clause;
          })
          .join(', ')})`;
        whereValues.push(...inValueArr);
        break;
      case '$notIn':
        let notInValueArr = value;
        if (!Array.isArray(value)) {
          notInValueArr = [value];
        }

        if (notInValueArr.length <= 0) {
          break;
        }
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} NOT IN (${notInValueArr
          // eslint-disable-next-line no-loop-func
          .map(() => {
            const clause = `$${paramIndex}`;
            paramIndex += 1;
            return clause;
          })
          .join(', ')})`;
        whereValues.push(...notInValueArr);
        break;
      case '$contains':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} ILIKE '%' || $${paramIndex} || '%'`;
        whereValues.push(value.replace(/[_%]/g, '\\$&'));
        paramIndex += 1;
        break;
      case '$notContains':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} NOT LIKE '%' || $${paramIndex} || '%'`;
        whereValues.push(value.replace(/[_%]/g, '\\$&'));
        paramIndex += 1;
        break;
      case '$containsi':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } LOWER(${column}) LIKE '%' || LOWER($${paramIndex}) || '%'`;
        whereValues.push(value.replace(/[_%]/g, '\\$&'));
        paramIndex += 1;
        break;
      case '$notContainsi':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } LOWER(${column}) NOT LIKE '%' || LOWER($${paramIndex}) || '%'`;
        whereValues.push(value.replace(/[_%]/g, '\\$&'));
        paramIndex += 1;
        break;
      case '$null':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} IS NULL`;
        break;
      case '$notNull':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} IS NOT NULL`;
        break;
      case '$between':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} BETWEEN $${paramIndex} AND $${paramIndex + 1}`;
        whereValues.push(value[0], value[1]);
        paramIndex += 2;
        break;
      case '$startsWith':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} LIKE $${paramIndex} || '%'`;
        whereValues.push(value);
        paramIndex += 1;
        break;
      case '$endsWith':
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } ${column} LIKE '%' || $${paramIndex}`;
        whereValues.push(value);
        paramIndex += 1;
        break;
      case '$or':
        let orSubWhereClause = '';
        let orSubIndex = paramIndex;
        const orSubWhereValues = [];
        // eslint-disable-next-line no-use-before-define, no-restricted-syntax
        for (const [orColumn, orValue] of Object.entries(orValue)) {
          const subWhere = buildWhereClause(orColumn, orValue, orSubIndex);
          orSubWhereClause += subWhere.whereClause;
          orSubWhereValues.push(...subWhere.whereValues);
          orSubIndex += subWhere.whereValues.length;
        }
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } (${orSubWhereClause.replace(/^( AND)/, '')})`;
        whereValues.push(...orSubWhereValues);
        paramIndex = orSubIndex + 1;
        break;
      case '$and':
        let andSubWhereClause = '';
        let andSubIndex = paramIndex;
        const andSubWhereValues = [];
        // eslint-disable-next-line no-use-before-define, no-restricted-syntax
        for (const [andColumn, andValue] of Object.entries(andValue)) {
          const subWhere = buildWhereClause(andColumn, andValue, andSubIndex);
          andSubWhereClause += subWhere.whereClause;
          andSubWhereValues.push(...subWhere.whereValues);
          andSubIndex += subWhere.whereValues.length;
        }
        whereClause += `${
          paramIndex === 1 ? ' WHERE' : ' AND'
        } (${andSubWhereClause.replace(/^( AND)/, '')})`;
        whereValues.push(...andSubWhereValues);
        paramIndex = orSubIndex + 1;
        break;
      default:
        throw new Error(`Invalid operator: ${operator}`);
    }
  }

  return { whereClause, whereValues };
};

const buildOrderByClause = (entityColumnNameMapping, sortArr) => {
  let query = '';
  let orderByClause = '';

  sortArr.forEach(order => {
    const [field, direction] = order.split(':');
    const columnName = getColumnName(entityColumnNameMapping, field);
    if (columnName) {
      orderByClause += `${columnName} ${direction},`;
    }
  });

  // for ( const [ key, value ] of Object.entries( sortArr ) ) {
  //     orderByClause += `${key} ${value},`;
  // }

  if (orderByClause) {
    query += ` ORDER BY ${orderByClause.slice(0, -1)}`;
  }

  return query;
};

const wrapQueryWithPagination = (
  innerQuery,
  queryParams,
  pageIndex,
  pageSize,
  tmpTables = null
) => {
  const offset = pageIndex * pageSize;
  const index = queryParams.length + 1;
  let testQuery = 'WITH ';

  if (tmpTables) {
    // eslint-disable-next-line no-restricted-syntax
    for (const tmp of tmpTables) {
      testQuery += ` ${tmp.tableName} AS (${tmp.content}),`;
    }
  }

  testQuery += `baseCTE AS (${innerQuery})        
                SELECT (SELECT COUNT(1) FROM baseCTE) AS total_count,          
                (SELECT JSON_AGG(a) FROM (SELECT * FROM baseCTE LIMIT $${index} OFFSET $${
                  index + 1
                }) AS a) AS results;`;

  queryParams.push(...[pageSize, offset]);

  return testQuery;
};

/**
 *
 * @param {Array<import('../../types').iBaseEntity>} dataArray
 * @param {typeof BaseEntity} entityClass
 * @returns {Array<typeof BaseEntity>}
 */
const convertToEntities = (dataArray, EntityClass) => {
  try {
    return (dataArray || []).map(item => {
      const entityInstance = new EntityClass(item);
      return entityInstance;
    });
  } catch (err) {
    logger.error('entities.helper.convertToEntities() error: $s', err);
    throw new Error(err);
  }
};

const prepareSortFields = (keys = [], columnMappings = {}) => {
  if (isEmpty(keys)) {
    return [];
  }
  const stripKeys = keys.map(k => k.split(':')[0]);

  const keyCounts = stripKeys.reduce((acc, curr) => {
    // eslint-disable-next-line no-param-reassign
    acc[curr] = (acc[curr] || 0) + 1;
    return acc;
  }, {});

  // eslint-disable-next-line no-restricted-syntax
  for (const [key, count] of Object.entries(keyCounts)) {
    if (count > 1) {
      throw new restify.BadRequestError({
        body: {
          code: 'BadRequestError',
          message: 'contains a duplicate value',
          context: {
            value: key,
          },
        },
      });
    }
  }

  return keys
    .map(key => {
      const [field, suffix] = key.split(':');
      const value = `${columnMappings[field]}${suffix ? ` ${suffix}` : ''}`;
      return value || null;
    })
    .filter(Boolean);
};

/**
 * Format the Select Fields if keys is Empty it will default to all fields in the column mapping
 * @param {*} keys
 * @param {*} columnMappings
 * @param {*} casingFields
 * @returns
 */
const prepareSelectFields = (
  keys = [],
  columnMappings = {},
  casingFields = true
) =>
  isEmpty(keys)
    ? Object.entries(columnMappings)
        .map(([k, v]) => {
          let q = v;
          if (v && casingFields) {
            q += ` as ${snakeCase(k)}`;
          }
          return q;
        })
        .filter(Boolean)
    : keys
        .map(key => {
          let value = columnMappings[key];
          if (value && casingFields) {
            value += ` as ${snakeCase(key)}`;
          }
          return value;
        })
        .filter(Boolean);

const matchAndReplaceKey = (object = {}, columnMappings = {}) =>
  Object.fromEntries(
    Object.entries(object)
      .map(([key, value]) => {
        const colName = columnMappings[key];
        if (colName) {
          return [colName, value];
        }

        logger.debug(
          `matchAndReplaceKey: No matching column name for ${key}, this name will be ignored.`
        );
        return null;
      })
      .filter(Boolean)
  );

const buildQuery = ({ tableName, fields, filters, joins, groupBy, sort }) => {
  let index = 1;
  let query = '';
  const queryParams = [];
  query += `SELECT ${fields} FROM ${tableName}`;

  if (joins) {
    // eslint-disable-next-line no-restricted-syntax
    for (const join of joins) {
      query += ` ${join.type} JOIN ${join.tableName} ON ${join.on}`;
    }
  }

  if (filters && Object.entries(filters).length > 0) {
    let whereClause = '';
    // eslint-disable-next-line no-restricted-syntax
    for (const [column, value] of Object.entries(filters)) {
      const { whereClause: a, whereValues: b } = buildWhereClause(
        column,
        value,
        index
      );
      whereClause += a;
      queryParams.push(...b);
      index += b.length;
    }
    query += whereClause;
  }

  if (groupBy && groupBy.length > 0) {
    query += ` GROUP BY ${groupBy.join(', ')}`;
  }

  if (sort && sort.length > 0) {
    query += ` ORDER BY ${sort.join(' ,')}`;
  }

  return { query, queryParams };
};

const buildReturnFields = (returnFields = [], columnMappings = {}) => {
  if (isEmpty(returnFields)) {
    return '';
  }
  const validFields = returnFields.map(field => {
    const colName = columnMappings[field];
    if (colName) {
      return colName.split('.')[1];
    }
    const err = `invalid object property ${field}`;
    logger.error({ err }, '[buildReturnFields]');
    throw new Error(err);
  });
  return `RETURNING ${validFields.join(', ')}`;
};

const buildPutWhereQuery = (
  object = {},
  columnMappings = {},
  whereFields = [],
  returnFields = [],
  updateFields = []
) => {
  const valueArray = [];
  if (isEmpty(whereFields)) {
    const err = `missing where fields`;
    logger.error(err);
    throw new Error(err);
  }

  const columnValues = Object.entries(object)
    .filter(key => !whereFields.includes(key[0]))
    .filter(key => isEmpty(updateFields) || updateFields.includes(key[0]))
    .map(([key, value]) => {
      const colName = columnMappings[key];
      if (colName) {
        valueArray.push(object[key]);
        return [columnMappings[key].split('.')[1], value];
      }
      const err = `invalid object property ${key}`;
      logger.error(err);
      throw new Error(err);
    });
  if (isEmpty(columnValues)) {
    const err = `No invalid object property to update`;
    logger.error(err);
    throw new Error(err);
  }
  let paramIdx = 0;
  const setValues = columnValues.map(([column]) => {
    paramIdx += 1;
    return ` ${column} = $${paramIdx}`;
  });

  const whereAndClauseValues = whereFields.map(clause => {
    valueArray.push(object[clause]);
    paramIdx += 1;
    return `${columnMappings[clause].split('.')[1]} = $${paramIdx}`;
  });
  const returnQuery = buildReturnFields(returnFields, columnMappings);

  return {
    query: ` SET ${setValues} WHERE ${whereAndClauseValues.join(
      ' AND '
    )} ${returnQuery}`,
    valueArray,
  };
};

const buildInsertIntoQuery = (
  object = {},
  columnMappings = {},
  returnFields = []
) => {
  const valueArray = [];
  const columnValues = Object.entries(object).map(([key, value]) => {
    const colName = columnMappings[key];
    if (colName) {
      valueArray.push(object[key]);
      return [columnMappings[key], value];
    }
    const err = `invalid object property ${key}`;
    logger.error(err);
    throw new Error(err);
  });
  if (isEmpty(columnValues)) {
    const err = `No invalid object property to update`;
    logger.error(err);
    throw new Error(err);
  }

  const columns = [];
  const values = [];

  columnValues.map(([key], paramIdx) => {
    columns.push(key.split('.')[1]);
    values.push(`$${paramIdx + 1}`);
    return key;
  });

  const returnQuery = buildReturnFields(returnFields, columnMappings);
  return {
    query: ` ( ${columns.join(', ')} ) VALUES ( ${values.join(
      ', '
    )} ) ${returnQuery}`,
    valueArray,
  };
};

const buildColumnKeys = entityDTOs => {
  const columnKeys = [];
  entityDTOs.forEach(entityDTO => {
    Object.entries(entityDTO).forEach(([key]) => {
      if (!columnKeys.find(k => k === key)) columnKeys.push(key);
    });
  });
  return columnKeys;
};

const buildInsertBatchIntoQuery = (
  entityDTOs = [{}],
  columnMappings = {},
  returnFields = []
) => {
  const valueArray = [];
  const objectKeys = buildColumnKeys(entityDTOs);
  const columns = [];
  objectKeys.forEach(key => {
    const colName = columnMappings[key];
    if (colName) {
      columns.push(colName.split('.')[1]);
      return;
    }
    const err = `invalid object property ${key}`;
    logger.error(err);
    throw new Error(err);
  });
  if (isEmpty(columns)) {
    const err = `No valid object property to update`;
    logger.error(err, '[buildInsertBatchIntoQuery].columns');
    throw new Error(err);
  }
  const recordSets = [];
  let paramIdx = 0;
  entityDTOs.forEach(entityDTO => {
    const valueIdxs = [];
    const columnValues = objectKeys.map(key => {
      const value = entityDTO[key] || null;
      valueArray.push(value);
      return [columnMappings[key], value];
    });
    if (isEmpty(columnValues)) {
      const err = `No valid object property to update`;
      logger.error(err, '[buildInsertBatchIntoQuery].columnValues');
      throw new Error(err);
    }
    columnValues.forEach(() => {
      paramIdx += 1;
      valueIdxs.push(`$${paramIdx}`);
    });
    recordSets.push(`( ${valueIdxs.toString()} )`);
  });

  const returnQuery = buildReturnFields(returnFields, columnMappings);
  return {
    query: ` ( ${columns.toString()} ) VALUES ${recordSets.toString()} ${returnQuery}`,
    valueArray,
  };
};

module.exports = {
  buildWhereClause,
  buildOrderByClause,
  wrapQueryWithPagination,
  convertToEntities,
  getColumnName,
  buildQuery,
  prepareSelectFields,
  prepareSortFields,
  matchAndReplaceKey,
  buildPutWhereQuery,
  buildInsertIntoQuery,
  buildInsertBatchIntoQuery,
  buildReturnFields,
};
