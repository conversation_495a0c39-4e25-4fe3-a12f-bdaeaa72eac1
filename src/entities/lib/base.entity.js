/**
 * @class BaseEntity
 */
class BaseEntity {
  /**
   *
   * @param {import("./entities.types").iBaseEntity} entityObj
   */
  constructor(entityObj) {
    this.set(entityObj);
  }

  static getEntityMeta() {
    return {};
  }

  /**
   * @returns {import("./entities.types").iBaseEntity}
   */
  get() {
    // eslint-disable-next-line no-unused-vars
    return Object.fromEntries(
      Object.entries(this).filter(([_, v]) => v != null)
    );
  }

  /**
   * @param {import("./entities.types").iBaseEntity} entityObj
   */
  set(entityObj = {}) {
    // @ts-ignore
    // const columnMapping = this.constructor.getColumnMapping();

    // Mapping will be done only in the repository.
    // eslint-disable-next-line no-restricted-syntax
    for (const [key, value] of Object.entries(entityObj)) {
      // if ( Object.keys( columnMapping ).includes( key ) ) {
      //     this[ columnMapping[ key ] ] = value;
      // }
      this[key] = value;
    }
  }
}

module.exports = BaseEntity;
