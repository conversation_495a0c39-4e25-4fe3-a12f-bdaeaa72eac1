const orderOption = {
  asc: 'asc',
  desc: 'desc',
};

const minPageIndex = 0;
const minPageSize = 1;
const maxPageSize = 40;

const gateOpts = [
  // '$or',
  // '$and'
];

const stringOpts = [
  '$contains',
  '$notContains',
  '$containsi',
  '$notContainsi',
  '$startsWith',
  '$endsWith',
];

const dateNumberOpts = [
  '$lt',
  '$lte',
  '$gt',
  '$gte',
  // '$between'
];

const arrayOpts = ['$in', '$notIn'];

const equalityOpts = [
  '$eq',
  // '$eqi',
  '$ne',
];

const nullOpts = [
  // '$null',
  // '$notNull'
];

const operators = [
  ...nullOpts,
  ...equalityOpts,
  ...arrayOpts,
  ...gateOpts,
  ...stringOpts,
  ...dateNumberOpts,
];

module.exports = {
  orderOption,
  operators,
  gateOpts,
  nullOpts,
  equalityOpts,
  stringOpts,
  dateNumberOpts,
  arrayOpts,
  minPageIndex,
  minPageSize,
  maxPageSize,
};
