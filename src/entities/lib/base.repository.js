const { mapKeys, camelCase } = require('lodash');

const logger = require('../../../lib/logger').mainLogger();
const { server } = require('../../../app');
const BaseEntity = require('./base.entity');
const {
  wrapQueryWithPagination,
  convertToEntities,
  buildQuery,
  prepareSelectFields,
  matchAndReplaceKey,
  prepareSortFields,
  buildPutWhereQuery,
  buildInsertIntoQuery,
  buildInsertBatchIntoQuery,
} = require('./entities.helper');

class BaseRepository {
  /**
   * @param {typeof BaseEntity} entityClass
   */
  constructor(entityClass = BaseEntity) {
    this.entityClass = entityClass;
    // @ts-ignore
    this.dataContext = server.db.read;
    // @ts-ignore
    this.writeContext = server.db.write;
  }

  /**
   * @function Get() Retrieves an object from the repository based on a specified ID.
   * @param {string | number} id
   * @returns {Promise<BaseEntity>}
   */
  async get(id) {
    const { tableName, primaryKey } = this.entityClass.getEntityMeta();
    const result = await this.dataContext.rows(
      `SELECT * FROM ${tableName} WHERE ${primaryKey} = $1`,
      [id]
    );
    return convertToEntities(result, this.entityClass)[0];
  }

  /**
   * @function Get() Retrieves an array of BaseEntity from the repository based on an array of IDs.
   * @param {Array<Number>} ids
   * @returns {Promise<BaseEntity[]>}
   */
  async getByIdList(ids) {
    const { tableName, primaryKey } = this.entityClass.getEntityMeta();
    const devicesFromIdsSql = `SELECT * FROM ${tableName} WHERE ${primaryKey} IN (${ids})`;

    const result = await this.dataContext.rows(devicesFromIdsSql);
    return convertToEntities(result, this.entityClass);
  }

  /**
   * @function GetAll() Retrieves all objects from the repository.
   * @returns {Promise<Array<BaseEntity>>}
   */
  async getAll() {
    const { tableName } = this.entityClass.getEntityMeta();
    const result = await this.dataContext.rows(`SELECT * FROM ${tableName}`);
    return convertToEntities(result, this.entityClass);
  }

  /**
   * @function find() - with paging
   * @param {import('./base.types').iFindOptions} param
   * @returns {Promise<import('./base.types').iResponse<BaseEntity>>}
   */
  async find({ filters, fields, pageIndex = 0, pageSize, sort }) {
    try {
      const { tableName, columnMapping } = this.entityClass.getEntityMeta();

      const { query, queryParams } = buildQuery({
        tableName,
        fields: prepareSelectFields(fields, columnMapping),
        filters: matchAndReplaceKey(filters, columnMapping),
        joins: null,
        groupBy: null,
        sort: prepareSortFields(sort, columnMapping),
      });

      const paginatedQuery = wrapQueryWithPagination(
        query,
        queryParams,
        pageIndex,
        pageSize
      );

      const result = await this.dataContext.row(paginatedQuery, queryParams);

      if (result.results) {
        result.results = result.results.map(m =>
          mapKeys(m, (v, k) => camelCase(k))
        );
      }

      const entities = convertToEntities(result.results, this.entityClass);

      return {
        resultsMetadata: {
          totalResults: result.totalCount,
          pageIndex,
          pageSize,
        },
        results: entities,
      };
    } catch (err) {
      logger.error({ err }, '[find]');
      throw err;
    }
  }

  /**
   * @function findAll() - no paging
   * @param {import('./base.types').iFindOptions} param
   * @returns {Promise<import('./base.types').iResponse<BaseEntity>>}
   */
  async findAll({ filters, fields, sort }) {
    try {
      const { tableName, columnMapping } = this.entityClass.getEntityMeta();
      logger.debug(
        { filters, fields, sort },
        '[findAll].filters, fields, sort '
      );
      const { query, queryParams } = buildQuery({
        tableName,
        fields: prepareSelectFields(fields, columnMapping),
        filters: matchAndReplaceKey(filters, columnMapping),
        joins: null,
        groupBy: null,
        sort: prepareSortFields(sort, columnMapping),
      });
      logger.debug({ query, queryParams }, '[findAll].query, queryParams ');

      const results = await server.db.read.rows(query, queryParams);
      return {
        resultsMetadata: {
          totalResults: results.length,
        },
        results,
      };
    } catch (err) {
      logger.error({ err }, '[findAll]');
      throw err;
    }
  }

  /**
   * @function put() put object from the repository.
   * @param {Object} txConnection database connection object
   * @param {Ogject} entityDTO
   * @param {Array<String>} whereFields
   * @param {Array<String>} returnFields
   * @param {Array<String>} updateFields
   * @returns {Promise<Array<BaseEntity>>}
   */
  async put(txConnection, entityDTO, whereFields, returnFields, updateFields) {
    const connection = txConnection || (await server.db.write.getConnection());
    try {
      if (!txConnection) await connection.execute('BEGIN');
      const { tableName } = this.entityClass.getEntityMeta();
      const { query, valueArray } = buildPutWhereQuery(
        entityDTO,
        this.entityClass.getEntityMeta().columnMapping,
        whereFields,
        returnFields,
        updateFields
      );
      const sql = `UPDATE ${tableName} ${query}; `;

      const updResult = await connection.execute(sql, valueArray);
      if (!txConnection) {
        await connection.execute('COMMIT');
        connection.done();
      }
      return updResult.rows[0];
    } catch (err) {
      if (!txConnection) {
        await connection.execute('ROLLBACK');
        connection.done();
      }
      logger.error({ err }, '[put]');
      throw err;
    }
  }

  /**
   * @function post() insert object from the repository.
   * @param {Object} txConnection database connection object
   * @param {BaseEntity} entityDTO insert entity object
   * @param {Array<String>} returnFields
   * @returns {Promise<Array<BaseEntity>>}
   */
  async post(txConnection, entityDTO, returnFields) {
    const connection = txConnection || (await server.db.write.getConnection());

    try {
      if (!txConnection) await connection.execute('BEGIN');
      const { tableName } = this.entityClass.getEntityMeta();
      const { query, valueArray } = buildInsertIntoQuery(
        entityDTO,
        this.entityClass.getEntityMeta().columnMapping,
        returnFields
      );

      const sql = `INSERT INTO ${tableName} ${query}; `;

      const insertResult = await txConnection.execute(sql, valueArray);
      if (!txConnection) {
        await connection.execute('COMMIT');
        connection.done();
      }
      return insertResult.rows[0];
    } catch (err) {
      logger.error({ err }, '[post]');
      throw err;
    }
  }

  /**
   * @function postBatch() insert array objects
   * @param {Object} txConnection database connection object
   * @param {Array<BaseEntity>} entityDTOs array of insert entity objects
   * @param {Array<String>} returnFields
   * @returns {Promise<Array<BaseEntity>>}
   */
  async postBatch(txConnection, entityDTOs, returnFields) {
    const connection = txConnection || (await server.db.write.getConnection());

    try {
      if (!txConnection) await connection.execute('BEGIN');
      const { tableName } = this.entityClass.getEntityMeta();
      const { query, valueArray } = buildInsertBatchIntoQuery(
        entityDTOs,
        this.entityClass.getEntityMeta().columnMapping,
        returnFields
      );

      const sql = `INSERT INTO ${tableName} ${query}; `;

      const insertResult = await txConnection.execute(sql, valueArray);
      if (!txConnection) {
        await connection.execute('COMMIT');
        connection.done();
      }
      return insertResult.rows[0];
    } catch (err) {
      logger.error({ err }, '[postBatch]');
      throw err;
    }
  }
}

module.exports = BaseRepository;
