/* eslint-disable import/no-unused-modules */
import { iBaseEntity } from './entities.types';

export enum filterOption {
  $eq = '$eq',
  $eqi = '$eqi',
  $ne = '$ne',
  $lt = '$lt',
  $lte = '$lte',
  $gt = '$gt',
  $gte = '$gte',
  $in = '$in',
  $notIn = '$notIn',
  $contains = '$contains',
  $notContains = '$notContains',
  $containsi = '$containsi',
  $notContainsi = '$notContainsi',
  $null = '$null',
  $notNull = '$notNull',
  $between = '$between',
  $startsWith = '$startsWith',
  $endsWith = '$endsWith',
  $or = '$or',
  $and = '$and',
}

export interface iFilterParam {
  [columnName: string]: filterOption;
}

export interface iFindOptions {
  filters: object;
  fields: Array<string>;
  pageIndex: number;
  pageSize: number;
  sort: Array<string>;
}

export declare class BaseRepository {
  constructor(tableName: string, primaryKey: string, entityClass: string);
}

export interface iResultsMetaData {
  totalResults: number;
  pageIndex: number;
  pageSize: number;
}

export interface iResponse<TEntity extends iBaseEntity> {
  resultsMetadata: iResultsMetaData;
  results: Array<TEntity>;
}
