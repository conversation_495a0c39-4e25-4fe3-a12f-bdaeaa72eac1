const Joi = require('joi');

const {
  minPageSize,
  maxPageSize,
  minPageIndex,
  arrayOpts,
} = require('./entities.constant');

const buildPageSizeAndIndexValidator = (
  minPageSizeParam = minPageSize,
  maxPageSizeParam = maxPageSize,
  defaultPageSizeParam = maxPageSize,
  minPageIndexParam = minPageIndex
) => ({
  pageSize: Joi.number()
    .integer()
    .min(minPageSizeParam)
    .max(maxPageSizeParam)
    .default(defaultPageSizeParam)
    .optional(),
  pageIndex: Joi.number()
    .integer()
    .min(minPageIndexParam)
    .default(minPageIndexParam)
    .optional(),
  allSites: Joi.boolean().default(true).optional(),
  tenantWise: Joi.boolean().default(false).optional(),
});

const baseEntityValidatorObjectFields = buildPageSizeAndIndexValidator();

const buildFieldsValidator = (fields, defaultFields) => {
  if (!fields || fields.length <= 0) {
    return Joi.forbidden();
  }

  return Joi.array()
    .items(Joi.string().valid(...fields))
    .optional()
    .unique()
    .default(defaultFields || fields)
    .single();
};

const buildFiltersValidator = filterOpts => {
  const validators = {};

  // eslint-disable-next-line no-restricted-syntax
  for (const [key, { operators, validator }] of Object.entries(filterOpts)) {
    const ops = operators.reduce((a, v) => {
      if (arrayOpts.includes(v)) {
        return { ...a, [v]: Joi.array().items(validator).single().optional() };
      }

      return { ...a, [v]: validator.optional() };
    }, {});
    validators[key] = Joi.object().keys(ops).optional().unknown(false);
  }

  if (Object.keys(validators).length <= 0) {
    return Joi.forbidden();
  }

  return Joi.object().keys(validators).optional().unknown(false);
};

const buildSortValidator = (
  sortFields,
  defaultSortFields,
  sortable = ['asc', 'desc', 'ASC', 'DESC']
) => {
  if (!sortFields || sortFields.length <= 0) {
    return Joi.forbidden();
  }

  const orderOptions = sortable.flatMap(sort =>
    sortFields.map(field => `${field}:${sort}`)
  );

  return Joi.array()
    .items(Joi.string().valid(...orderOptions))
    .optional()
    .unique()
    .default(defaultSortFields || [])
    .single();
};

module.exports = {
  baseEntityValidatorObjectFields,
  buildPageSizeAndIndexValidator,
  buildSortValidator,
  buildFiltersValidator,
  buildFieldsValidator,
};
