const PaymentDashboardEntity = require('../entities/api/application/dashboard/payment-dashboard.entity');
const { server } = require('../../app');
const {
  buildQuery,
  matchAndReplaceKey,
  prepareSelectFields,
} = require('../entities/lib/entities.helper');
const logger = require('../../lib/logger').mainLogger();

const getDestinationsForJobCreation = async ({
  deviceId,
  monitoringModeOn,
}) => {
  logger.info('[GetDestinationsForJobCreation] Start');

  const agentCurrentStatus = 'invenco.icsagent.metrics.active-config';
  const adaptorCurrentStatus = 'invenco.system.metrics.active-config';
  const agentFileLabel = 'invenco.icsagent.metrics.config-file-labels';
  const adaptorFileLabel = 'invenco.system.metrics.config-file-labels';

  const requiredStates = [
    agentCurrentStatus,
    adaptorCurrentStatus,
    agentFileLabel,
    adaptorFileLabel,
  ];

  const { columnMapping, tableName } = PaymentDashboardEntity.getEntityMeta();

  const { query, queryParams } = buildQuery({
    tableName,
    fields: prepareSelectFields(['state', 'status'], columnMapping),
    filters: matchAndReplaceKey(
      {
        deviceId: { $eq: deviceId },
        state: { $in: requiredStates },
      },
      columnMapping
    ),
  });

  logger.debug(
    `[GetDestinationsForJobCreation] Query: ${query}, queryParams: ${queryParams}`
  );

  const devicePaymentDashboardData = await server.db.read.rows(
    query,
    queryParams
  );

  logger.debug('[GetDestinationsForJobCreation] devicePaymentDashboardData', {
    devicePaymentDashboardData,
  });

  if (devicePaymentDashboardData.length < requiredStates.length) {
    return null;
  }

  const deviceStates = devicePaymentDashboardData.reduce(
    (acc, { state, status }) => {
      try {
        acc[state] = JSON.parse(status);
      } catch (e) {
        acc[state] = status;
      }
      return acc;
    },
    {}
  );

  const defaultStatus = 'Default';
  const intensiveStatus = 'Intensive';
  const agentDestination = 'invenco.icsagent';
  const adaptorDestination = 'invenco.system';

  const conditions = [
    {
      [agentCurrentStatus]: defaultStatus,
      [adaptorCurrentStatus]: defaultStatus,
      [agentFileLabel]: [defaultStatus, intensiveStatus],
      [adaptorFileLabel]: [defaultStatus, intensiveStatus],
      monitoringModeOn: true,
      jobs: {
        destinations: [agentDestination, adaptorDestination],
        isIntensive: true,
      },
    },
    {
      [agentCurrentStatus]: defaultStatus,
      [adaptorCurrentStatus]: defaultStatus,
      [agentFileLabel]: [defaultStatus, intensiveStatus],
      [adaptorFileLabel]: [defaultStatus, intensiveStatus],
      monitoringModeOn: false,
      jobs: {
        destinations: [agentDestination, adaptorDestination],
        isIntensive: false,
      },
    },
    {
      [agentCurrentStatus]: intensiveStatus,
      [adaptorCurrentStatus]: intensiveStatus,
      [agentFileLabel]: [defaultStatus, intensiveStatus],
      [adaptorFileLabel]: [defaultStatus, intensiveStatus],
      monitoringModeOn: false,
      jobs: {
        destinations: [agentDestination, adaptorDestination],
        isIntensive: false,
      },
    },
    {
      [agentCurrentStatus]: intensiveStatus,
      [adaptorCurrentStatus]: intensiveStatus,
      [agentFileLabel]: [defaultStatus, intensiveStatus],
      [adaptorFileLabel]: [defaultStatus, intensiveStatus],
      monitoringModeOn: true,
      jobs: {
        destinations: [agentDestination, adaptorDestination],
        isIntensive: true,
      },
    },
    {
      [agentCurrentStatus]: defaultStatus,
      [adaptorCurrentStatus]: intensiveStatus,
      [agentFileLabel]: [defaultStatus, intensiveStatus],
      [adaptorFileLabel]: [defaultStatus, intensiveStatus],
      monitoringModeOn: false,
      jobs: {
        destinations: [agentDestination, adaptorDestination],
        isIntensive: false,
      },
    },
    {
      [agentCurrentStatus]: defaultStatus,
      [adaptorCurrentStatus]: intensiveStatus,
      [agentFileLabel]: [defaultStatus, intensiveStatus],
      [adaptorFileLabel]: [defaultStatus, intensiveStatus],
      monitoringModeOn: true,
      jobs: {
        destinations: [agentDestination, adaptorDestination],
        isIntensive: true,
      },
    },
    {
      [agentCurrentStatus]: intensiveStatus,
      [adaptorCurrentStatus]: defaultStatus,
      [agentFileLabel]: [defaultStatus, intensiveStatus],
      [adaptorFileLabel]: [defaultStatus, intensiveStatus],
      monitoringModeOn: false,
      jobs: {
        destinations: [agentDestination, adaptorDestination],
        isIntensive: false,
      },
    },
    {
      [agentCurrentStatus]: intensiveStatus,
      [adaptorCurrentStatus]: defaultStatus,
      [agentFileLabel]: [defaultStatus, intensiveStatus],
      [adaptorFileLabel]: [defaultStatus, intensiveStatus],
      monitoringModeOn: true,
      jobs: {
        destinations: [agentDestination, adaptorDestination],
        isIntensive: true,
      },
    },
    {
      [agentCurrentStatus]: defaultStatus,
      [adaptorCurrentStatus]: defaultStatus,
      [agentFileLabel]: [defaultStatus, intensiveStatus],
      [adaptorFileLabel]: [defaultStatus],
      monitoringModeOn: true,
      jobs: {
        destinations: [agentDestination],
        isIntensive: true,
      },
    },
    {
      [agentCurrentStatus]: defaultStatus,
      [adaptorCurrentStatus]: defaultStatus,
      [agentFileLabel]: [defaultStatus, intensiveStatus],
      [adaptorFileLabel]: [defaultStatus],
      monitoringModeOn: false,
      jobs: {
        destinations: [agentDestination],
        isIntensive: false,
      },
    },
    {
      [agentCurrentStatus]: defaultStatus,
      [adaptorCurrentStatus]: defaultStatus,
      [agentFileLabel]: [defaultStatus],
      [adaptorFileLabel]: [defaultStatus, intensiveStatus],
      monitoringModeOn: true,
      jobs: {
        destinations: [adaptorDestination],
        isIntensive: true,
      },
    },
    {
      [agentCurrentStatus]: defaultStatus,
      [adaptorCurrentStatus]: defaultStatus,
      [agentFileLabel]: [defaultStatus],
      [adaptorFileLabel]: [defaultStatus, intensiveStatus],
      monitoringModeOn: false,
      jobs: {
        destinations: [adaptorDestination],
        isIntensive: false,
      },
    },
  ];

  const matchedCondition = conditions.find(
    condition =>
      condition[agentCurrentStatus] === deviceStates[agentCurrentStatus] &&
      condition[adaptorCurrentStatus] === deviceStates[adaptorCurrentStatus] &&
      condition.monitoringModeOn === monitoringModeOn &&
      condition[agentFileLabel].every(label =>
        deviceStates[agentFileLabel].includes(label)
      ) &&
      condition[adaptorFileLabel].every(label =>
        deviceStates[adaptorFileLabel].includes(label)
      )
  );

  logger.info(
    '[GetDestinationsForJobCreation] matchedCondition',
    matchedCondition
  );

  return matchedCondition ? matchedCondition.jobs : null;
};

module.exports = {
  getDestinationsForJobCreation,
};
