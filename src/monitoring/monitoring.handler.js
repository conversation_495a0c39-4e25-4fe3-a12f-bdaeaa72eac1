const moment = require('moment');
const restify = require('restify');
const logger = require('../../lib/logger').mainLogger();
const jobHelper = require('../../helpers/job-helper');
const { server } = require('../../app');
const { getDestinationsForJobCreation } = require('./monitoring.repository');

module.exports = {
  createMonitoringJobs: async (req, res, next) => {
    logger.info('[CreateMonitoringJobs] Start', {
      params: req.params,
      body: req.body,
    });

    const userId = req.user.sub;
    const { deviceId } = req.params;
    const { monitoringModeOn } = req.body;

    const destinationsForJobCreation = await getDestinationsForJobCreation({
      deviceId,
      monitoringModeOn,
    });

    logger.debug(
      '[CreateMonitoringJobs] destinationsForJobCreation',
      destinationsForJobCreation
    );

    if (!destinationsForJobCreation) {
      next(
        new restify.ForbiddenError('Monitoring job creation is not allowed')
      );
    }
    const connection = await server.db.write.getConnection();

    const cancelExistingMonitoringJobs = `
      UPDATE job
      SET status = $1
      WHERE device_id = $2 AND
      type = $3 AND
      status = $4
    `;

    await connection.execute(cancelExistingMonitoringJobs, [
      jobHelper.jobStatus.CANCELLED,
      deviceId,
      jobHelper.jobType.REMOTE_CONFIG,
      jobHelper.jobStatus.NEW,
    ]);

    const { destinations, isIntensive } = destinationsForJobCreation;

    const jobCreationPromises =
      destinations &&
      destinations.reduce((acc, destination) => {
        acc.push(
          jobHelper.createJob(connection, {
            deviceId,
            destination,
            type: jobHelper.jobType.REMOTE_CONFIG,
            data: {
              params: [
                'metrics.active-config',
                isIntensive ? 'Intensive' : 'Default',
              ],
            },
            embargo: moment().toISOString(),
            userId,
          })
        );

        if (isIntensive) {
          const embargo = moment().add(2, 'hour').toISOString();

          acc.push(
            jobHelper.createJob(connection, {
              deviceId,
              destination,
              type: jobHelper.jobType.REMOTE_CONFIG,
              data: { params: ['metrics.active-config', 'Default'] },
              embargo,
              userId,
            })
          );
        }

        return acc;
      }, []);

    const results = await Promise.allSettled(jobCreationPromises);

    results.forEach(result => {
      if (result.status === 'rejected') {
        logger.error(
          `Error while creating monitoring job for device ${deviceId}`
        );
      }
    });

    const jobs = results.map(result => result.value);
    connection.done();

    res.send(200, jobs);
  },
};
