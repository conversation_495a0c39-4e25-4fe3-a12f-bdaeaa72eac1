const Joi = require('joi');

const { server } = require('../../app');
const { requiresRole, requiresFeatureFlag } = require('../../lib/pre');
const { config } = require('../../env');
const { validateParams, validateBody } = require('../../lib/pre');
const handler = require('./monitoring.handler');

const BASE_PATH = `${config.base}/monitoring`;

server.post(
  {
    path: `${BASE_PATH}/:deviceId/create`,
    version: '1.0.0',
  },
  [
    requiresFeatureFlag(['MONITORING']),
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateParams({
      deviceId: Joi.number().integer().required(),
    }),
    validateBody({
      monitoringModeOn: Joi.boolean(),
    }),
    handler.createMonitoringJobs,
  ]
);
