const columnCsv = entity => Object.keys(entity).join(', ').trim();

const numberCsv = entity =>
  Object.keys(entity)
    .map((k, index) => `$${index + 1}`)
    .join(', ')
    .trim();

const values = entity => Object.keys(entity).map(k => entity[k]);

const parameterizedQuery = (query, startIndex = 0) =>
  Object.keys(query).map((key, index) => {
    const curr = startIndex + index + 1;
    return `${key} = $${curr}`;
  });

const parameterizedLikeQuery = (query, startIndex = 0) =>
  Object.keys(query).map((key, index) => {
    const curr = startIndex + index + 1;
    return `${key}::text LIKE $${curr}`;
  });

const whereFilter = (filter, startIndex = 0) => {
  const params = parameterizedLikeQuery(filter, startIndex)
    .join(' AND ')
    .trim();

  return 'WHERE '.concat(params);
};

const isColumnValid = columnName => {
  const validColumnPattern = /^[a-zA-Z0-9_]+$/; // Alphanumeric and underscores only
  return validColumnPattern.test(columnName);
};

module.exports = {
  flatEntityValues: entity => values(entity),

  buildSQLGetByFilter: (table, filter) => {
    let filterBy = '';
    let orderBy = '';
    const filterWithoutOrder = filter;
    if (filter) {
      if (filter.orderBy) {
        if (!isColumnValid(filter.orderBy))
          throw new Error('Invalid orderBy value provided.');
        orderBy = filter.orderBy;
        delete filterWithoutOrder.orderBy;
      }
      if (Object.keys(filterWithoutOrder).length)
        filterBy = whereFilter(filterWithoutOrder);
    }

    const sql = `SELECT * FROM ${table} ${filterBy} ${orderBy ? `ORDER BY ${orderBy} DESC ` : ''} LIMIT 200`;

    return sql;
  },

  buildSQLInsertEntity: (table, entity) => {
    const sql = `INSERT INTO ${table} (${columnCsv(
      entity
    )}) VALUES (${numberCsv(entity)})`;

    return sql;
  },

  buildSQLUpdateEntityByFilter: (table, entity, filter) => {
    const entityPropsLen = Object.keys(entity).length;

    const setValues = parameterizedQuery(entity).join(',');
    const useFilter = whereFilter(filter, entityPropsLen);
    const sql = `UPDATE ${table} SET ${setValues} ${useFilter}`;

    return sql;
  },

  buildSQLDeleteEntityByFiler: (table, filter) => {
    let filterBy = '';

    if (filter) {
      filterBy = whereFilter(filter);
    }

    const sql = `DELETE FROM ${table} ${filterBy}`;

    return sql;
  },
};
