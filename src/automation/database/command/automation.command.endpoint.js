const Joi = require('joi');
const restifyErrors = require('restify-errors');

const env = require('../../../../env');
const { server } = require('../../../../app');
const { roles } = require('../../../../lib/app-constants');
const {
  requiresRole,
  validateParams,
  validateBody,
} = require('../../../../lib/pre');
const errors = require('../automation.database.errors');
const handler = require('./automation.command.handler');

const BASE_PATH = `${env.config.base}/automation/db`;
const VERSION = '1.0.0';

server.post(
  {
    path: `${BASE_PATH}/:table`,
    version: VERSION,
  },
  [
    requiresRole([roles.SUPER_ADMIN]),
    validateParams({
      table: Joi.string()
        .required()
        .error(new restifyErrors.BadRequestError(errors.table.NOT_FOUND)),
    }),
    handler.insertTableEntity,
  ]
);

server.patch(
  {
    path: `${BASE_PATH}/:table`,
    version: VERSION,
  },
  [
    requiresRole([roles.SUPER_ADMIN]),
    validateParams({
      table: Joi.string()
        .required()
        .error(new restifyErrors.BadRequestError(errors.table.NOT_FOUND)),
    }),
    validateBody(
      Joi.object()
        .required()
        .error(new restifyErrors.BadRequestError(errors.filter.NOT_FOUND))
    ),
    handler.updateTableEntity,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/:table`,
    version: VERSION,
  },
  [
    requiresRole([roles.SUPER_ADMIN]),
    validateParams({
      table: Joi.string()
        .required()
        .error(new restifyErrors.BadRequestError(errors.table.NOT_FOUND)),
    }),
    validateBody(
      Joi.object()
        .required()
        .error(new restifyErrors.BadRequestError(errors.filter.NOT_FOUND))
    ),
    handler.deleteTableEntity,
  ]
);
