const restify = require('restify');
const { omit } = require('lodash');

const errorHandler = require('../../../../lib/errorhandler');
const errors = require('../automation.database.errors');
const commandService = require('./automation.command.service');
const commandAccess = require('./automation.command.access');

module.exports = {
  insertTableEntity: async (req, res, next) => {
    try {
      const { table } = req.params;
      const entity = req.body;

      if (
        !commandAccess.allowedEnvs.includes(
          process.env.ENVIRONMENT.toLowerCase()
        )
      ) {
        return next(new restify.BadRequestError(errors.table.ACCESS_DENIED));
      }

      const accessList = await commandService.getAccessList();

      if (!accessList.includes(table)) {
        return next(new restify.BadRequestError(errors.table.ACCESS_DENIED));
      }

      try {
        const result = await commandService.insertTableEntity(table, entity);

        res.send(result);

        return next();
      } catch (err) {
        return next(new restify.BadRequestError(err.message));
      }
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  updateTableEntity: async (req, res, next) => {
    try {
      const { table } = req.params;
      const filterEntity = req.body;

      const { _filter: filter } = filterEntity;
      const entity = omit(filterEntity, ['_filter']);

      if (
        !commandAccess.allowedEnvs.includes(
          process.env.ENVIRONMENT.toLowerCase()
        )
      ) {
        return next(new restify.BadRequestError(errors.table.ACCESS_DENIED));
      }

      const accessList = await commandService.getAccessList();

      if (!accessList.includes(table)) {
        return next(new restify.BadRequestError(errors.table.ACCESS_DENIED));
      }

      try {
        const result = await commandService.updateTableEntity(
          table,
          entity,
          filter
        );

        res.send(result);

        return next();
      } catch (err) {
        return next(new restify.BadRequestError(err.message));
      }
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  deleteTableEntity: async (req, res, next) => {
    try {
      const { table } = req.params;
      const filter = req.body;

      if (
        !commandAccess.allowedEnvs.includes(
          process.env.ENVIRONMENT.toLowerCase()
        )
      ) {
        return next(new restify.BadRequestError(errors.table.ACCESS_DENIED));
      }

      const accessList = await commandService.getAccessList();

      if (!accessList.includes(table)) {
        return next(new restify.BadRequestError(errors.table.ACCESS_DENIED));
      }

      const result = await commandService.deleteTableEntity(table, filter);

      res.send(result);

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
