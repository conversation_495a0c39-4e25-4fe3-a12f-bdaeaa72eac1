const errors = require('../automation.database.errors');
const commandAccess = require('./automation.command.access');
const commandRepo = require('./automation.command.repository');
const validator = require('./automation.command.validator');

module.exports = {
  getAccessList: async () => commandAccess.allowedTables,

  insertTableEntity: async (table, entity) => {
    const accessList = commandAccess.allowedTables;
    validator.validateInsertTableEntity(table, accessList);

    // eslint-disable-next-line no-return-await
    return await commandRepo.insertTableEntity(table, entity);
  },

  updateTableEntity: async (table, entity, filter) => {
    const accessList = commandAccess.allowedTables;
    validator.validateUpdateTableEntity(table, filter, accessList);

    // eslint-disable-next-line no-return-await
    return await commandRepo.updateTableEntity(table, entity, filter);
  },

  deleteTableEntity: async (table, filter) => {
    const accessList = commandAccess.allowedTables;
    validator.validateDeleteTableEntity(table, filter, accessList);

    if (!filter) {
      throw new Error(errors.table.COLUMN_NOT_FOUND);
    }

    // eslint-disable-next-line no-return-await
    return await commandRepo.deleteTableEntity(table, filter);
  },
};
