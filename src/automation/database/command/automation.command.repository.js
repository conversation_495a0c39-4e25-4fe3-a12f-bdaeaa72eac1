const { server } = require('../../../../app');
const dbHelper = require('../automation.database.helpers');

module.exports = {
  insertTableEntity: async (table, entity) => {
    const sql = dbHelper.buildSQLInsertEntity(table, entity);
    const values = dbHelper.flatEntityValues(entity);
    try {
      await server.db.write.execute(sql, values);

      return `Successfully inserted into ${table}`; // response per automation request
    } catch (err) {
      throw new Error(err.message);
    }
  },

  updateTableEntity: async (table, entity, filter) => {
    const sql = dbHelper.buildSQLUpdateEntityByFilter(table, entity, filter);

    try {
      const values = dbHelper
        .flatEntityValues(entity)
        .concat(dbHelper.flatEntityValues(filter));

      await server.db.write.execute(sql, values);

      return `Successfully updated ${table}`;
    } catch (err) {
      throw new Error(err.message);
    }
  },

  deleteTableEntity: async (table, filter) => {
    const sql = dbHelper.buildSQLDeleteEntityByFiler(table, filter);

    try {
      await server.db.write.execute(sql, dbHelper.flatEntityValues(filter));

      return `Successfully deleted from ${table}`;
    } catch (err) {
      throw new Error(err.message);
    }
  },
};
