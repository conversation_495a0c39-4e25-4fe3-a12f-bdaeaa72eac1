const errors = require('../automation.database.errors');

module.exports = {
  validateInsertTableEntity: (table, accessList) => {
    if (!table) {
      throw new Error(errors.table.NOT_FOUND);
    }

    if (!accessList.includes(table)) {
      throw new Error(errors.table.ACCESS_DENIED);
    }
  },

  validateUpdateTableEntity: (table, filter, accessList) => {
    if (!table) {
      throw new Error(errors.table.NOT_FOUND);
    }

    if (!accessList.includes(table)) {
      throw new Error(errors.table.ACCESS_DENIED);
    }

    if (!filter || !Object.keys(filter).length) {
      throw new Error(errors.filter.NOT_FOUND);
    }
  },

  validateDeleteTableEntity: (table, filter, accessList) => {
    if (!table) {
      throw new Error(errors.table.NOT_FOUND);
    }

    if (!accessList.includes(table)) {
      throw new Error(errors.table.ACCESS_DENIED);
    }

    if (!filter || !Object.keys(filter).length) {
      throw new Error(errors.filter.NOT_FOUND);
    }
  },
};
