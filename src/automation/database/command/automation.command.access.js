const allowedTables = Object.freeze([
  'device_files',
  'device_rki_key_bundle',
  'report_status_history',
  'report_api_audit',
  'report_metric_history',
  'report_alarm_history',
  'email_token',
  'company_device_keys',
  'ics_user',
  'job',
  'custom_attribute.attribute_definition',
  'custom_attribute.custom_attribute_entity_values',
]);

const allowedEnvs = Object.freeze([
  'yoda',
  'lemon',
  'martini',
  'tequila',
  'olive',
  'r2-internal',
  'nuke',
  'mint',
  'mojito',
  'brandy',
  'cherry',
  'vodka',
  'lemonade',
  'rum',
  'coke',
  'whiskey',
  'rocks',
  'gin',
  'tonic',
  'perf',
]);

module.exports = {
  allowedTables,
  allowedEnvs,
};
