const { server } = require('../../../../app');
const dbHelper = require('../automation.database.helpers');
const queryAccess = require('./automation.query.access');

module.exports = {
  getTableQueryResult: async (table, query) => {
    const sql = dbHelper.buildSQLGetByFilter(table, query);
    let values = [];

    if (query) {
      const queryWithoutOrderby = query;
      delete queryWithoutOrderby.orderBy;
      values = dbHelper.flatEntityValues(queryWithoutOrderby);
    }

    try {
      const result = await server.db.read.rows(sql, values);
      result.forEach(queryAccess.maskBlackListProperties);
      return result;
    } catch (err) {
      throw new Error(err.message);
    }
  },
};
