const Joi = require('joi');
const restifyErrors = require('restify-errors');

const env = require('../../../../env');
const { server } = require('../../../../app');
const { roles } = require('../../../../lib/app-constants');
const { requiresRole, validateParams } = require('../../../../lib/pre');
const errors = require('../automation.database.errors');
const handler = require('./automation.query.handler');

const BASE_PATH = `${env.config.base}/automation/db`;
const VERSION = '1.0.0';

server.get(
  {
    path: `${BASE_PATH}/:table`,
    version: VERSION,
  },
  [
    requiresRole([roles.SUPER_ADMIN]),
    validateParams({
      table: Joi.string()
        .required()
        .error(new restifyErrors.BadRequestError(errors.table.NOT_FOUND)),
    }),
    handler.getTableQueryResult,
  ]
);
