const queryAccess = require('./automation.query.access');
const queryRepo = require('./automation.query.repository');
const validator = require('./automation.query.validator');

module.exports = {
  getAccessList: async () => queryAccess.allowedTables,

  getTableQueryResult: async (table, query) => {
    const accessList = queryAccess.allowedTables;
    validator.validateGetTableQueryResult(table, accessList);
    // eslint-disable-next-line no-return-await
    return await queryRepo.getTableQueryResult(table, query);
  },
};
