const { intersection } = require('lodash');

const allowedTables = Object.freeze([
  'email_token',
  'prompt',
  'ics_user',
  'target',
  'site',
  'company',
  'site_tag',
  'tag',
  'company_device_keys',
  'job',
  'custom_attribute.attribute_definition',
  'custom_attribute.custom_attribute_entity_values',
  'authorization_site_group',
  'key_group',
  'user_group',
]);

const blackListProperties = Object.freeze([
  'pass',
  'password',
  'passwordHash',
  'mfaSecret',
  'ipAddress',
  'registrationKey',
  'certificate',
  'macAddress',
  'realIpAddress',
]);

const allowedEnvs = Object.freeze([
  'yoda',
  'lemon',
  'olive',
  'martini',
  'tequila',
  'r2-internal',
  'nuke',
  'mint',
  'mojito',
  'brandy',
  'cherry',
  'vodka',
  'lemonade',
  'rum',
  'coke',
  'whiskey',
  'rocks',
  'gin',
  'tonic',
  'perf',
]);

const maskBlackListProperties = entity => {
  const maskCharLength = 6;
  const charMask = '*';
  const maskStr = charMask.repeat(maskCharLength);

  const matches = intersection(Object.keys(entity), blackListProperties);

  matches.forEach(prop => {
    if (entity[prop] !== null && entity[prop] !== undefined) {
      entity[prop] = entity[prop].toString().substring(0, 3).concat(maskStr); // eslint-disable-line
    }
  });
};

module.exports = {
  allowedTables,
  allowedEnvs,
  blackListProperties,
  maskBlackListProperties,
};
