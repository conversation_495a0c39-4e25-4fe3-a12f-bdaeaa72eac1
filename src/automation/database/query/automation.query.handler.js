const restify = require('restify');

const errorHandler = require('../../../../lib/errorhandler');
const errors = require('../automation.database.errors');
const queryService = require('./automation.query.service');
const queryAccess = require('./automation.query.access');

module.exports = {
  getTableQueryResult: async (req, res, next) => {
    try {
      const { table } = req.params;
      const query = req.body;

      if (
        !queryAccess.allowedEnvs.includes(process.env.ENVIRONMENT.toLowerCase())
      ) {
        return next(new restify.BadRequestError(errors.table.ACCESS_DENIED));
      }

      const accessList = await queryService.getAccessList();

      if (!accessList.includes(table)) {
        return next(new restify.BadRequestError(errors.table.ACCESS_DENIED));
      }

      try {
        const result = await queryService.getTableQueryResult(table, query);

        res.send(result);

        return next();
      } catch (err) {
        return next(new restify.BadRequestError(err.message));
      }
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
