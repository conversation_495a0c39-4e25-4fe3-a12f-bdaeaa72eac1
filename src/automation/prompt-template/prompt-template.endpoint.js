const Joi = require('joi');

const { server } = require('../../../app');
const env = require('../../../env');
const { requiresRole, validateQuery } = require('../../../lib/pre');
const { roles } = require('../../../lib/app-constants');
const handler = require('./prompt-template.handler');

const BASE_PATH = `${env.config.base}/prompt-template`;
const VERSION = '1.0.0';

server.get(
  {
    path: BASE_PATH,
    version: VERSION,
  },
  [
    requiresRole([roles.SUPER_ADMIN]),
    validateQuery({
      name: Joi.string().min(3).max(50).optional(),
    }),
    handler.getAllPromptTemplates,
  ]
);
