const { server } = require('../../../app');

const getAllPromptTemplates = async promptTemplateName => {
  let queryString = 'SELECT id, name FROM prompt_template';
  const params = [];
  if (promptTemplateName) {
    queryString += ' WHERE name=$1';
    params.push(promptTemplateName);
  }
  const result = await server.db.read.rows(
    `${queryString} LIMIT 1000;`,
    params
  );
  return result;
};

module.exports.public = {
  getAllPromptTemplates,
};
