const errorHandler = require('../../../lib/errorhandler');
const { httpCode } = require('../../../lib/app-constants');
const repository = require('./prompt-template.repository').public;

module.exports = {
  async getAllPromptTemplates(req, res, next) {
    try {
      const promptTemplateName = req.query.name;
      const promptTemplates =
        await repository.getAllPromptTemplates(promptTemplateName);
      res.send(httpCode.OK.STATUS_CODE, promptTemplates);
      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },
};
