const errorHandler = require('../../../lib/errorhandler');
const { httpCode } = require('../../../lib/app-constants');
const repository = require('./devices.repository').public;

const deviceHandler = {
  async createDeviceFile(req, res, next) {
    const { deviceId } = req.body;
    const { timestamp } = req.body;
    const { devicePath } = req.body;
    const { contentType } = req.body;
    const { applicationId } = req.body;

    try {
      const deviceFile = await repository.createDeviceFile(
        deviceId,
        devicePath,
        contentType,
        applicationId,
        timestamp
      );

      res.send(httpCode.OK.STATUS_CODE, deviceFile);

      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },
};

module.exports = deviceHandler;
