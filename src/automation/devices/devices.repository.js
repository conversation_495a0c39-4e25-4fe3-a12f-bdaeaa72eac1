const { server } = require('../../../app');

const deviceRepository = {
  async createDeviceFile(
    deviceId,
    devicePath,
    contentType,
    applicationId,
    timestamp
  ) {
    const query = `INSERT INTO device_files (device_id, device_path, content_type, application_id, timestamp)
                       VALUES ($1, $2, $3, $4, $5)
                       RETURNING id
                       , device_id
                       , device_path
                       , content_type
                       , application_id
                       , timestamp`;

    const [deviceFile] = await server.db.write.execute(query, [
      deviceId,
      devicePath,
      contentType,
      applicationId,
      timestamp,
    ]);

    return deviceFile;
  },
};

module.exports.public = deviceRepository;
