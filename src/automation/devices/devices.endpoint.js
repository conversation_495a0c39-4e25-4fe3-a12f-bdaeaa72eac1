const Joi = require('joi');

const env = require('../../../env');
const { server } = require('../../../app');
const { roles } = require('../../../lib/app-constants');
const { requiresRole, validateBody } = require('../../../lib/pre');
const handler = require('./devices.handler');

const BASE_PATH = `${env.config.base}/devices`;
const VERSION = '1.0.0';

server.post(
  {
    path: `${BASE_PATH}/file`,
    version: VERSION,
  },
  [
    requiresRole([roles.SUPER_ADMIN]),
    validateBody({
      deviceId: Joi.number().integer().required(),
      devicePath: Joi.string().required(),
      contentType: Joi.string().required(),
      applicationId: Joi.string().required(),
      timestamp: Joi.date().required(),
    }),
    handler.createDeviceFile,
  ]
);
