const errorHandler = require('../../../lib/errorhandler');
const { httpCode } = require('../../../lib/app-constants');
const repository = require('./prompts.repository').public;

const promptHandler = {
  async getPromptIdByPromptStateIdAndPromptSetId(req, res, next) {
    const { promptSetId } = req.query;
    const { promptStateId } = req.query;

    try {
      const prompt = await repository.getPromptByPromptStateIdAndPromptSetId(
        promptStateId,
        promptSetId
      );
      res.send(httpCode.OK.STATUS_CODE, prompt.id);

      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },
};

module.exports = promptHandler;
