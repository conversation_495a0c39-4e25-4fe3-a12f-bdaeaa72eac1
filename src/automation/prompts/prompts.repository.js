const { server } = require('../../../app');

const promptRepository = {
  async getPromptByPromptStateIdAndPromptSetId(promptStateId, promptSetId) {
    const query =
      'SELECT * FROM prompt WHERE prompt_state = $1 AND prompt_set = $2 LIMIT 1';

    const prompt = await server.db.read.row(query, [
      promptStateId,
      promptSetId,
    ]);

    return prompt;
  },
};

module.exports.public = promptRepository;
