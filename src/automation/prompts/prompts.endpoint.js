const Joi = require('joi');

const env = require('../../../env');
const { server } = require('../../../app');
const { roles } = require('../../../lib/app-constants');
const { requiresRole, validateQuery } = require('../../../lib/pre');
const handler = require('./prompts.handler');

const BASE_PATH = `${env.config.base}/prompts`;
const VERSION = '1.0.0';

server.get(
  {
    path: `${BASE_PATH}/id`,
    version: VERSION,
  },
  [
    requiresRole([roles.SUPER_ADMIN]),
    validateQuery({
      promptStateId: Joi.string()
        .guid({
          version: ['uuidv4', 'uuidv5'],
        })
        .required(),
      promptSetId: Joi.string()
        .guid({
          version: ['uuidv4', 'uuidv5'],
        })
        .required(),
    }),
    handler.getPromptIdByPromptStateIdAndPromptSetId,
  ]
);
