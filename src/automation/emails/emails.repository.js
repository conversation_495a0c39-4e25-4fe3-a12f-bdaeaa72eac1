const { server } = require('../../../app');

const emailRepository = {
  async getEmailTokenByUserIdAndType(userId, type) {
    const query = `SELECT * FROM email_token WHERE user_id = $1
                    AND type = $2 ORDER BY id DESC LIMIT 1`;

    const emailToken = await server.db.read.row(query, [userId, type]);

    return emailToken;
  },

  async getEmailTokenByEmailAndType(userEmail, type) {
    const query = `SELECT * FROM email_token WHERE user_id = (SELECT id FROM ics_user WHERE email = $1)
                    AND type = $2 ORDER BY id DESC LIMIT 1`;

    const emailToken = await server.db.read.row(query, [userEmail, type]);

    return emailToken;
  },

  async updateEmailToken(emailTokenId, emailToken) {
    const query = `
            UPDATE email_token SET 
              type = $1
            , value = $2
            , expires = $3
            WHERE id = $4
        `;

    await server.db.write.execute(query, [
      emailToken.type,
      emailToken.value,
      emailToken.expires,
      emailTokenId,
    ]);

    return emailToken;
  },
};

module.exports.public = emailRepository;
