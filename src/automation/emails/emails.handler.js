const errorHandler = require('../../../lib/errorhandler');
const { httpCode } = require('../../../lib/app-constants');
const repository = require('./emails.repository').public;

const emailHandler = {
  async getEmailTokenValueByUserEmailAndType(req, res, next) {
    const { type } = req.query;
    const { userEmail } = req.query;

    try {
      const emailToken = await repository.getEmailTokenByEmailAndType(
        userEmail,
        type
      );

      res.send(httpCode.OK.STATUS_CODE, emailToken.value);

      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },

  async updateEmailTokenExpire(req, res, next) {
    const { type } = req.query;
    const { expires } = req.body;
    const { userEmail } = req.query;

    try {
      const emailToken = await repository.getEmailTokenByEmailAndType(
        userEmail,
        type
      );
      const newEmailToken = { ...emailToken, expires };

      const updatedEmailToken = await repository.updateEmailToken(
        emailToken.id,
        newEmailToken
      );
      res.send(httpCode.OK.STATUS_CODE, updatedEmailToken);

      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },
};

module.exports = emailHandler;
