const Joi = require('joi');

const env = require('../../../env');
const { server } = require('../../../app');
const constants = require('../../../lib/app-constants');
const { roles } = require('../../../lib/app-constants');
const {
  requiresRole,
  validateQuery,
  validateBody,
} = require('../../../lib/pre');
const handler = require('./emails.handler');

const BASE_PATH = `${env.config.base}/emails`;
const VERSION = '1.0.0';

const EMAIL_TOKEN_TYPES = Object.freeze(
  Object.keys(constants.emailTokenType).map(
    type => constants.emailTokenType[type]
  )
);

server.get(
  {
    path: `${BASE_PATH}/token/value`,
    version: VERSION,
  },
  [
    requiresRole([roles.SUPER_ADMIN]),
    validateQuery({
      userEmail: Joi.string().email().required(),
      type: Joi.string().valid(EMAIL_TOKEN_TYPES).required(),
    }),
    handler.getEmailTokenValueByUserEmailAndType,
  ]
);

server.patch(
  {
    path: `${BASE_PATH}/token/expire`,
    version: VERSION,
  },
  [
    requiresRole([roles.SUPER_ADMIN]),
    validateQuery({
      userEmail: Joi.string().email().required(),
      type: Joi.string().valid(EMAIL_TOKEN_TYPES).required(),
    }),
    validateBody({
      expires: Joi.date().required(),
    }),
    handler.updateEmailTokenExpire,
  ]
);
