const crypto = require('crypto');

const {
  DAY_IN_SECONDS,
  DEVICE_FILES_JOB_PARAM_SCHEMAS,
} = require('../../../lib/app-constants');
const { default: RedisClient } = require('../../../lib/redis');
const {
  gLibCompress,
  gLibDecompress,
} = require('../../../helpers/zlip-helper');
const repository = require('./schemas.repository');

/**
 * Get schema for payment dashboard by schema id, and return in a list if exist
 * @param {int} schemaId
 * @returns schema object
 */
const getDeviceFileSchema = async schemaId => {
  const redisClient = RedisClient.getInstance();
  const fileSchemaCacheValues = await redisClient.getValue(
    `${DEVICE_FILES_JOB_PARAM_SCHEMAS}:${schemaId}`
  );
  if (fileSchemaCacheValues) {
    const fileSchemaValues = await gLibDecompress(fileSchemaCacheValues);
    return JSON.parse(fileSchemaValues) || [];
  }
  const deviceFileSchema = await repository.getSchema(schemaId);
  if (deviceFileSchema) {
    const compressedValues = await gLibCompress(
      JSON.stringify(deviceFileSchema.schema)
    );
    await redisClient.saveKeyValueWithTTL(
      `${DEVICE_FILES_JOB_PARAM_SCHEMAS}:${schemaId}`,
      compressedValues,
      7 * DAY_IN_SECONDS
    );
    return deviceFileSchema.schema;
  }
  return null;
};

/**
 * Set schema for payment dashboard by name and schema, and return id
 * @param {*} schemaName
 * @param {*} schema
 * @returns id of schema
 */
const setDeviceFileSchema = async (schemaName, schema) => {
  const hash = crypto.createHash('md5').update(schemaName).digest('hex');
  const setSchemaResult = await repository.setSchema(schemaName, hash, schema);
  if (!setSchemaResult) {
    return null;
  }
  const redisClient = RedisClient.getInstance();
  await redisClient.removeKey(
    `${DEVICE_FILES_JOB_PARAM_SCHEMAS}:allSchemaNames`
  );
  await redisClient.removeKey(
    `${DEVICE_FILES_JOB_PARAM_SCHEMAS}:${setSchemaResult?.[0]?.id}`
  );
  return setSchemaResult;
};

module.exports = {
  getDeviceFileSchema,
  setDeviceFileSchema,
};
