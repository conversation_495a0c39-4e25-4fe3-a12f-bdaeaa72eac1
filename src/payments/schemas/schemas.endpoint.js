const Joi = require('joi');

const { server } = require('../../../app');
const { baseRoleGroup } = require('../../../lib/app-constants');
const {
  validateParams,
  requiresRole,
  validateBody,
} = require('../../../lib/pre');
const env = require('../../../env');

const BASE_PATH = env.config.base;
const handler = require('./schemas.handler');

/**
 * Payment Schema
 * Get payment schema for payment dashboard jobs
 * @swagger
 *   "payments/schemas/{schemaId}":
 *     get:
 *       tags:
 *         - Payment Schema
 *       summary: "Get payment schema for payment dashboard jobs"
 *       parameters:
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *         - in: path
 *           name: schemaId
 *           type: integer
 *           required: true
 *           description: schema id to get schema.
 *       responses:
 *         "200":
 *           description: "Succesful response"
 *           schema:
 *             type: object
 *             description: "return schema with success message"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "404":
 *           description: "Can't find the target schema for the given id"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/payments/schemas/:schemaId`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    validateParams({
      schemaId: Joi.number().integer().min(1).required(),
    }),
    handler.getPaymentSchema,
  ]
);

/**
 * Payment Schema
 * Post payment schema for payment dashboard jobs
 * @swagger
 *   "/internal/payments/schemas":
 *     post:
 *       tags:
 *         - Payment Schema
 *       summary: "Post payment schema for payment dashboard jobs"
 *       parameters:
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *         - in: body
 *           name: body
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: name of schema
 *               schema:
 *                 type: string
 *                 description: json schema
 *       responses:
 *         "200":
 *           description: "Successful response"
 *           schema:
 *             type: object
 *             description: "return schema id with success message"
 *             properties:
 *                 id:
 *                   type: string
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/internal/payments/schemas`,
    version: '0.0.1',
  },
  [
    requiresRole(['SUPER_ADMIN']),
    validateBody({
      name: Joi.string().required(),
      schema: Joi.object().required(),
    }),
    handler.setPaymentSchema,
  ]
);
