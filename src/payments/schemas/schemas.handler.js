const restifyErrors = require('restify-errors');

const { httpCode } = require('../../../lib/app-constants');
const errorHandler = require('../../../lib/errorhandler');
const schemaService = require('./schemas.service');

/**
 * Return schema object for payment action
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
const getPaymentSchema = async (req, res, next) => {
  const { schemaId } = req.params;

  try {
    const schema = await schemaService.getDeviceFileSchema(schemaId);

    if (!schema) {
      return next(new restifyErrors.NotFoundError('Schema not found'));
    }

    res.send(httpCode.OK.STATUS_CODE, schema);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

/**
 * Return id for schema
 * @param {*} req
 * @param {*} body
 * @param {*} next
 * @returns
 */
const setPaymentSchema = async (req, res, next) => {
  const { name, schema } = req.body;

  try {
    const result = await schemaService.setDeviceFileSchema(name, schema);
    if (!result) {
      return next(
        new restifyErrors.BadRequestError('Unable to save new schema')
      );
    }

    res.send(httpCode.OK.STATUS_CODE, result);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

module.exports = {
  getPaymentSchema,
  setPaymentSchema,
};
