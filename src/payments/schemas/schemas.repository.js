const { server } = require('../../../app');

/**
 * Get schema by id
 * @param {int} schemaId
 * @returns schema object
 */
const getSchema = async schemaId => {
  const query = `
        SELECT
            schema
        FROM
            device_files_job_param_schemas
        WHERE
            id = $1 AND deleted = false
    `;
  const result = await server.db.replica.row(query, [schemaId]);

  return result;
};

/**
 * Set schema
 * @param {*} schemaName
 * @param {*} schema
 * @returns schema object
 */
const setSchema = async (schemaName, hash, schema) => {
  const sqlQuery = `
        INSERT INTO
            device_files_job_param_schemas (name, hash, schema)
        VALUES
            ( $1, $2, $3::JSON )
        ON CONFLICT (name) 
        DO 
            UPDATE SET hash = $2, schema = $3::JSON
        RETURNING id;
    `;
  const result = await server.db.write.execute(sqlQuery, [
    schemaName,
    hash,
    JSON.stringify(schema),
  ]);
  return result;
};

module.exports = {
  getSchema,
  setSchema,
};
