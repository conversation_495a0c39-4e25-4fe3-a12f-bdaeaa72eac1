/* eslint-disable no-promise-executor-return */
const { JSONPath } = require('jsonpath-plus');
const {
  paymentAppConfigKey,
  epsAppKeyMetricsDefault,
  appKeyMetricsSeparator,
  EXTEND_PAYMENT_DASHBOARD_FEATURE_FLAG,
  appKeyMetricsFilter,
} = require('../../../lib/app-constants');
const { getValueJSON } = require('../../../helpers/db-config-helper');
const logger = require('../../../lib/logger').mainLogger();
const {
  getDevicePaymentAppsBySiteIdRepo,
  getUserAccessToSiteByIdRepo,
} = require('./dashboard.repository');
const { getDashboardPropertyStyle } = require('./dashboard.helper');

/**
 * Get payment dashboard item by Site ID
 * @param {UUID} siteId
 * @param {appKeyMetrics} appKeyMetrics must contain stateMap
 * @param {number} offset
 * @param {number} limit
 * @returns
 */
const getDevicePaymentAppsBySiteId = async (
  siteId,
  appKeyMetrics,
  // eslint-disable-next-line default-param-last
  deviceNameFilterArray = [],
  // eslint-disable-next-line default-param-last
  offset = 0,
  // eslint-disable-next-line default-param-last
  limit = null,
  applicationDashboard,
  deviceId
) => {
  if (appKeyMetrics.length) {
    // Get Device Metrics based on key metrics (determines which app runs on what device)
    const { appMetrics, totalResults, numberOfApplications } =
      await getDevicePaymentAppsBySiteIdRepo(
        siteId,
        appKeyMetrics,
        deviceNameFilterArray,
        offset,
        limit,
        deviceId
      );

    // map config label and colour indication.
    const results = (appMetrics || []).map(appMetric => {
      const {
        // eslint-disable-next-line no-shadow
        device_id: deviceId,
        device_name: deviceName,
        app_name: appName,
        metrics,
        device_health: deviceHealth,
      } = appMetric;

      const appKeyMetric = appKeyMetrics.find(akm => akm.appName === appName);

      const paymentDashboard = [];
      // eslint-disable-next-line no-restricted-syntax
      for (const [pdKEy, pdValue] of Object.entries(appKeyMetric.stateMap)) {
        if (metrics[pdKEy]) {
          const { value, timestamp } = metrics[pdKEy];
          const {
            type,
            active,
            label,
            values,
            properties,
            visibility = [],
          } = pdValue;

          // Default values
          let hasPaymentDashboard = false;
          let hasApplicationDashboard = false;

          // Check visibility and update flags
          if (visibility.length > 0) {
            hasPaymentDashboard = visibility.includes('payment_dashboard');
            hasApplicationDashboard = visibility.includes(
              'application_dashboard'
            );
          } else {
            hasPaymentDashboard = true;
          }
          if (applicationDashboard && !hasApplicationDashboard) {
            // eslint-disable-next-line no-continue
            continue;
          }
          if (!applicationDashboard && !hasPaymentDashboard) {
            // eslint-disable-next-line no-continue
            continue;
          }
          // convert JSON values to strings for the FE
          if (type === 'json') {
            try {
              const valueObject = JSON.parse(value);
              if (!properties) {
                logger.error(
                  { dottedString: pdKEy, schema: pdValue },
                  '[dashboard].[service].[getDevicePaymentAppsBySiteId] The properties object for the schema for the current dotted string is empty'
                );
              }

              properties.forEach(
                ({
                  label: jsonPropertyLabel,
                  propertyPath,
                  values: jsonPropertyValues,
                  type: jsonPropertyType,
                  active: jsonPropertyActive,
                }) => {
                  const newValue = JSONPath({
                    path: propertyPath,
                    json: valueObject,
                    wrap: false,
                  });

                  // Adding style
                  const style = getDashboardPropertyStyle({
                    propertyValues: jsonPropertyValues,
                    value: newValue,
                  });
                  paymentDashboard.push({
                    label: jsonPropertyLabel,
                    type: jsonPropertyType,
                    active: jsonPropertyActive,
                    value: newValue,
                    timestamp,
                    ...(style && { style }),
                  });
                }
              );
            } catch (error) {
              logger.error(
                { error, dottedString: pdKEy, schema: pdValue },
                '[dashboard].[service].[getDevicePaymentAppsBySiteId] Error when trying to process json values'
              );
            }
          } else {
            // Adding style
            const style = getDashboardPropertyStyle({
              propertyValues: values,
              value,
            });

            paymentDashboard.push({
              label,
              type,
              active,
              value,
              timestamp,
              ...(style && { style }),
            });
          }
        }
      }

      return {
        deviceId,
        deviceName,
        appName,
        deviceHealth,
        paymentDashboard,
      };
    });

    return {
      totalResults,
      numberOfApplications,
      results,
    };
  }
  // If appKeyMetrics are filtered and there are no app we return 0
  return {
    totalResults: 0,
    numberOfApplications: 0,
    results: [],
  };
};

/**
 * Check if user has access to the site
 * @param {UUID} userId
 * @param {UUID} siteId
 * @returns {boolean}
 */
const getUserAccessToSiteById = async (userId, siteId) =>
  // eslint-disable-next-line no-return-await
  await getUserAccessToSiteByIdRepo(userId, siteId);

/**
 * This function gets the company dashboard configuration schema for a list of apps from the ics_config table
 * @param {*} appNames array of appNames
 * @param {*} companyId company Id
 * @returns {appName: string, data: object} array of apps dashboard configuration schema
 */
const getCompanyDashboardConfigSchema = async (appNames, companyId) =>
  // eslint-disable-next-line no-return-await
  await Promise.all(
    appNames.map(
      appName =>
        // eslint-disable-next-line no-async-promise-executor
        new Promise(async (resolve, reject) => {
          try {
            return resolve({
              appName,
              data: await getValueJSON(
                `${companyId}${appKeyMetricsSeparator}${appName}`
              ),
            });
          } catch (err) {
            return reject(err);
          }
        })
    )
  );

/**
 * Search company specific config by companyId
 * @param {UUID} companyId
 * @returns {JSON<ICS_CONFIG>}
 */
const getIcsEPSConfigbyCompanyId = async (companyId, featureFlags = []) => {
  try {
    const defaultApps = featureFlags.includes(
      EXTEND_PAYMENT_DASHBOARD_FEATURE_FLAG
    )
      ? await getValueJSON(appKeyMetricsFilter)
      : epsAppKeyMetricsDefault;

    const appNames = defaultApps.map(apk => apk.appName);

    const companyConfigs = await getCompanyDashboardConfigSchema(
      appNames,
      companyId
    );

    return companyConfigs;
  } catch (err) {
    logger.error(`Getting ICS Company Configs for ${companyId} Failed`, err);
  }

  return [];
};

/**
 * Return list of allowed metrics and definition
 * @note Should search by site's company
 * @param {UUID} siteId
 * @return {appKeyMetrics}
 */
const getMetricsAllowList = async (
  companyId,
  featureFlags,
  appNameFilterArray
) => {
  const globalConfigSchema = await getValueJSON(paymentAppConfigKey);
  if (!globalConfigSchema) {
    return undefined;
  }

  const mergedConfigSchema = await (async () => {
    const mergedConfig = globalConfigSchema;
    const companyConfigSchema = await getIcsEPSConfigbyCompanyId(
      companyId,
      featureFlags
    );
    if (companyConfigSchema && companyConfigSchema.length > 0) {
      // eslint-disable-next-line no-restricted-syntax
      for (const appName of Object.keys(globalConfigSchema)) {
        const existing = companyConfigSchema.find(cc => cc.appName === appName);
        if (existing && existing.data) {
          mergedConfig[appName] = existing.data;
        }
      }
    }
    return mergedConfig;
  })();

  const defaultApps = featureFlags.includes(
    EXTEND_PAYMENT_DASHBOARD_FEATURE_FLAG
  )
    ? await getValueJSON(appKeyMetricsFilter)
    : epsAppKeyMetricsDefault;
  const appKeyMetrics = defaultApps.map(appKey => ({
    ...appKey,
    ...{ stateMap: mergedConfigSchema[appKey.appName] },
  }));

  // This regex creates an or with all the input of the user. Example (test|app1|infx-eps|xpos)
  const applpicationRegex =
    appNameFilterArray.length && new RegExp(appNameFilterArray.join('|'));
  const filteredAppKeyMetrics = appKeyMetrics.filter(
    appKey => !applpicationRegex || appKey.appName.match(applpicationRegex)
  );
  return filteredAppKeyMetrics;
};

module.exports = {
  getDevicePaymentAppsBySiteId,
  getUserAccessToSiteById,
  getIcsEPSConfigbyCompanyId,
  getMetricsAllowList,
  getCompanyDashboardConfigSchema,
};
