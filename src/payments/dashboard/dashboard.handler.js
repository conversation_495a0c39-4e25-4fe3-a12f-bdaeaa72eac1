const restifyErrors = require('restify-errors');

const errorHandler = require('../../../lib/errorhandler');
const paginationHelper = require('../../../helpers/pagination-helper');
const {
  paymentDashboardDefaultPageSize,
  paymentDashboardFilterSeparator,
} = require('../../../lib/app-constants');
const {
  getUserAccessToSiteById,
  getDevicePaymentAppsBySiteId,
  getMetricsAllowList,
} = require('./dashboard.service');

const getPaymentDashboardMetric = async (req, res, next) => {
  try {
    const userId = req.user.sub;
    const { featureFlags } = req.user.company || {};
    const { siteId } = req.params;
    const { deviceNameFilter, appNameFilter, deviceId, applicationDashboard } =
      req.query;

    const deviceNameFilterArray = deviceNameFilter
      ? deviceNameFilter.split(paymentDashboardFilterSeparator)
      : [];
    const appNameFilterArray = appNameFilter
      ? appNameFilter.split(paymentDashboardFilterSeparator)
      : [];

    const { pageIndex, pageSize, offset } =
      paginationHelper.parsePaginationParams({
        query: {
          ...req.query,
          pageSize:
            req.query.pageSize === undefined
              ? paymentDashboardDefaultPageSize
              : req.query.pageSize,
        },
      });

    const targetSite = await getUserAccessToSiteById(userId, siteId);
    if (!targetSite) {
      return next(new restifyErrors.NotFoundError('Site not found'));
    }
    const { companyId } = targetSite;

    const appKeyMetrics = await getMetricsAllowList(
      companyId,
      featureFlags,
      appNameFilterArray
    );

    if (!appKeyMetrics) {
      return next(new restifyErrors.NotFoundError('Config not found'));
    }

    const { results, totalResults, numberOfApplications } =
      await getDevicePaymentAppsBySiteId(
        siteId,
        appKeyMetrics,
        deviceNameFilterArray,
        offset,
        pageSize,
        applicationDashboard,
        deviceId
      );

    const response = {
      resultsMetadata: {
        totalResults,
        numberOfApplications,
        pageIndex,
        pageSize,
      },
      results,
    };

    res.send(response);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

module.exports = {
  getPaymentDashboardMetric,
};
