const Joi = require('joi');

const { server } = require('../../../app');
const env = require('../../../env');
const { baseRoleGroup } = require('../../../lib/app-constants');
const {
  requiresRole,
  validate<PERSON>ara<PERSON>,
  validateQuery,
} = require('../../../lib/pre');
const handler = require('./dashboard.handler');

const BASE_PATH = `${env.config.base}/payments/dashboard`;

/**
 * @swagger
 * /payments/dashboard/{siteId}:
 *     get:
 *       tags:
 *         - payment dashboard
 *       summary: Get payment dashboard summary broken down by application runs on each device.
 *       parameters:
 *         - name: siteId
 *           in: path
 *           description: Site ID
 *           required: true
 *           type: string
 *           format: uuid
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *       responses:
 *         "200":
 *           description: "Successful response"
 *           schema:
 *            type: object
 *            properties:
 *              resultsMetadata:
 *                $ref: '#/definitions/ResultsMetadata'
 *              results:
 *                type: array
 *                items:
 *                  type: object
 *                  properties:
 *                      deviceId:
 *                        type: integer
 *                        format: int32
 *                      deviceName:
 *                        type: string
 *                      appName:
 *                        type: string
 *                      deviceUnreachable:
 *                        type: boolean
 *                      paymentDashboard:
 *                        type: array
 *                        items:
 *                          type: object
 *                          properties:
 *                            label:
 *                              type: string
 *                            type:
 *                              type: string
 *                            active:
 *                              type: boolean
 *                            style:
 *                              type: string
 *                            value:
 *                              type: string
 *                            timestamp:
 *                              type: string
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "404":
 *           description: "Global Config not available or Site not found"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/:siteId`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    validateParams({
      siteId: Joi.string().guid(),
    }),
    validateQuery({
      pageSize: Joi.number().integer().min(1).optional(),
      pageIndex: Joi.number().integer().min(0).optional(),
      deviceNameFilter: Joi.string().optional(),
      appNameFilter: Joi.string().optional(),
      applicationDashboard: Joi.boolean().optional(),
      deviceId: Joi.number().integer().optional(),
    }),
    handler.getPaymentDashboardMetric,
  ]
);
