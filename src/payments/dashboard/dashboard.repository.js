const { server } = require('../../../app');

/**
 *
 * @param {uuid} siteId
 * @param {object} appKeyMetrics
 * @param {number} offset
 * @param {number} limit
 * @returns
 */
const getDevicePaymentAppsBySiteIdRepo = async (
  siteId,
  appKeyMetrics,
  deviceNameFilterArray,
  offset,
  limit,
  deviceId
) => {
  const keyMetrics = appKeyMetrics.map(appKey => appKey.state);
  const sqlParams = [siteId, keyMetrics, offset, limit];

  // Filter by device name
  let deviceNameFilter = '';
  if (deviceNameFilterArray.length) {
    // Only push this filter if there is a filter
    deviceNameFilterArray.forEach((deviceName, index) => {
      sqlParams.push(`${deviceName}`);
      deviceNameFilter += `${index === 0 ? 'AND (' : 'OR'} POSITION($${
        sqlParams.length
      } IN LOWER(t.name)) > 0`;
    });
    deviceNameFilter += ') ';
  }

  const caseClauseAppName = appKeyMetrics.map(appKey => {
    const { appName, state } = appKey;
    sqlParams.push(state, appName);
    const paramLength = sqlParams.length;
    return `WHEN state = $${paramLength - 1} THEN $${paramLength}`;
  });

  const caseClauseMetrics = appKeyMetrics.map(appKey => {
    const { appName, stateMap } = appKey;
    const stateKeys = Object.keys(stateMap);
    sqlParams.push(appName, stateKeys);

    const paramLength = sqlParams.length;
    return `WHEN app_name = $${paramLength - 1} THEN 
                json_object_agg( state, json_build_object( 'value',value, 'timestamp',timestamp ) ) 
                FILTER ( WHERE state = ANY($${paramLength}) )
        `;
  });

  let deviceIdFilter = '';
  if (deviceId) {
    sqlParams.push(deviceId);
    const paramLength = sqlParams.length;
    deviceIdFilter = `AND t.target_id = $${paramLength}`;
  }

  const sqlQuery = `
        WITH getDevices as (
            SELECT 
              t.target_id, 
              t.name, 
              mdh.health
            FROM 
              target t
              INNER JOIN mv_device_health mdh 
                ON mdh.target_id = t.target_id
            WHERE 
              t.site_id = $1 
              AND t.active 
              ${deviceIdFilter}
              ${deviceNameFilter}
        ),
        getAppNames as (
            SELECT 
              pd.device_id, 
              gdi.name, 
              (CASE ${caseClauseAppName.join(' ')} END) as app_name, 
              gdi.health
            FROM 
              payment_dashboard pd
              INNER JOIN getDevices gdi 
                ON gdi.target_id = pd.device_id
            AND 
              state = ANY ($2)
        ),
        getCombinedMetrics as (
            SELECT 
              gan.device_id,
              gan.name as device_name,
              app_name, 
              gan.health as device_health,
                (CASE ${caseClauseMetrics.join(' ')} END) as metrics
            FROM 
              getAppNames gan
              INNER JOIN payment_dashboard pd 
                ON pd.device_id = gan.device_id
            GROUP BY 
              gan.device_id,
              gan.name,
              app_name,gan.health
            ORDER BY 
              gan.health desc, 
              gan.name, 
              gan.device_id
            OFFSET 
              $3 
            LIMIT 
              $4
        )
        SELECT 
          jsonb_agg(h.*) AS app_metrics, 
          (SELECT COUNT(1) FROM getAppNames) as total_results, 
          (SELECT COUNT(1) FROM (SELECT DISTINCT app_name FROM getAppNames) as getNumberOfApplications) AS number_of_applications
        FROM 
          getCombinedMetrics h;
    `;
  // eslint-disable-next-line no-return-await
  return await server.db.read.row(sqlQuery, [...sqlParams]);
};

/**
 * Check user has access to a site
 * @param {UUID} userId
 * @param {UUID} siteId
 * @returns
 */
const getUserAccessToSiteByIdRepo = async (userId, siteId) =>
  // eslint-disable-next-line no-return-await
  await server.db.read.row(
    `
            SELECT s.site_id, s.company_id 
            FROM user_site_authorization usa
                INNER JOIN site s ON s.site_id = usa.site_id
            WHERE usa.user_id = $1
            AND usa.site_id = $2 AND s.active;
        `,
    [userId, siteId]
  );

module.exports = {
  getDevicePaymentAppsBySiteIdRepo,
  getUserAccessToSiteByIdRepo,
};
