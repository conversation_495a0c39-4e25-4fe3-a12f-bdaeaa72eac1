const restifyErrors = require('restify-errors');
const restify = require('restify');

const { httpCode } = require('../../../lib/app-constants');
const errorHandler = require('../../../lib/errorhandler');
const { isEmptyObject } = require('../../../lib/utils');
const paymentService = require('./actions.service');

/**
 * Return list of dynamic drop down for a payment action
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
const getPaymentAction = async (req, res, next) => {
  const { deviceId, appName } = req.params;

  try {
    const dropDownActions = await paymentService.getDynamicActionsList(
      appName,
      deviceId
    );

    if (!dropDownActions || !dropDownActions.length) {
      return next(
        new restifyErrors.NotFoundError('No available payment actions')
      );
    }

    res.send(httpCode.OK.STATUS_CODE, dropDownActions);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
const createPaymentJob = async (req, res, next) => {
  const deviceFileID = req.params.deviceFileId;
  try {
    const deviceFileWithSchema =
      await paymentService.getDeviceFileWithSchema(deviceFileID);

    if (!deviceFileWithSchema) {
      throw new restifyErrors.NotFoundError(
        'Device File for Device File ID not found'
      );
    }

    let userParams = { ...req.body };
    // Delete timezone from userParams, so it does not fail schema validation
    delete userParams.timezone;

    // if userParams is an empty object, make it null
    if (isEmptyObject(userParams)) {
      userParams = null;
    }

    if (userParams) {
      const { schema } = deviceFileWithSchema;
      if (!schema) {
        throw new restifyErrors.NotFoundError(
          'No schema for Device File ID to validate user params'
        );
      }
      const schemaValidationIssues = paymentService.validateUserParams(
        userParams,
        schema
      );
      if (schemaValidationIssues) {
        return next(
          new restify.BadRequestError({
            body: {
              code: 'BadRequestError',
              message: schemaValidationIssues,
            },
          })
        );
      }
    }

    await paymentService.createPaymentJob(req, {
      ...deviceFileWithSchema,
      deviceFileId: deviceFileID,
    });

    res.send(httpCode.OK.STATUS_CODE);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
const createCustomPaymentJob = async (req, res, next) => {
  const userParams = req.body;
  const { deviceFileId } = req.params;
  const userId = req.user.sub;

  try {
    const deviceFile = await paymentService.getDeviceFile(deviceFileId);
    if (!deviceFile) {
      throw new restifyErrors.NotFoundError(
        'Device File for Device File ID not found'
      );
    }
    const { deviceId, lastPulledPath } = deviceFile;

    const fileData = await paymentService.getFileData(lastPulledPath);

    const { ids } = userParams.jobData;

    const validationIssue = await paymentService.validateCustomJobRequest(
      ids,
      fileData
    );
    if (validationIssue) {
      return next(
        new restify.BadRequestError({
          body: {
            code: 'BadRequestError',
            message: validationIssue,
          },
        })
      );
    }

    await paymentService.createCustomPaymentJob(
      userId,
      deviceId,
      ids,
      fileData
    );
    res.send(httpCode.OK.STATUS_CODE);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

module.exports = {
  getPaymentAction,
  createPaymentJob,
  createCustomPaymentJob,
};
