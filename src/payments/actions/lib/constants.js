const CONSTANTS = {
  job: {
    STATUS: {
      COMPLETE: 3,
    },
    CONTENT_EXPIRES_IN_SECONDS: 900,
  },
  s3: {
    URI_PARSER_REGEX: /^[sS]3:\/\/(.*?)\/(.*)/,
  },
  JOB_DATA_VERSION: 1,
  URL_PATH_BEFORE_ID: 'fileuploadrequests',
  URL_PATH_AFTER_ID: 'upload',
  JOB_EXPIRY_HOURS: 2,
  MULTI_SELECT: 'multiselect',
  SINGLE_SELECT: 'single',
  ERROR_MESSAGE: {
    missing_key_device_file:
      'Device file key for _job_templates and _table is missing',
    selection_type_device_file: 'Selection type is incorrect',
    ids_not_found: 'ids not found',
  },
};

module.exports = CONSTANTS;
