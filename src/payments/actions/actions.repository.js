const { server } = require('../../../app');
const { getValueJSON } = require('../../../helpers/db-config-helper');
const { appActionsVisibility } = require('../../../lib/app-constants');

/**
 * Returns a set of device files where visibility is "payment/actions" for EPS and  "{app}/actions" for the rest of apps (Example "xpos/actions" for xpos)
 * @param {int} deviceId
 * @returns list of dynamic drop down
 */
const getDynamicActions = async (appName, deviceId) => {
  const appsVisibilities = await getValueJSON(appActionsVisibility);
  const visibilityType = appsVisibilities[appName] || '';
  const query = `
        WITH
        DeviceFileDetail AS (
            SELECT
                id,
                device_id,
                job_param_schema_id,
                content_type,
                jsonb_array_elements(display_properties) ::JSON ->> 'label'  "label",
                latest_upload_job,
                last_pulled_timestamp,
                last_pulled_path,
                device_path
            FROM
                device_files
            WHERE
                device_id = $1
            AND EXISTS (
                SELECT
                    1
                FROM
                    jsonb_array_elements(display_properties) AS item
                WHERE
                    item ->> 'visibility' = $2
            )
            ORDER BY
                last_pulled_timestamp desc
        ),

        DeviceFileLatest AS (
            SELECT
                DISTINCT ON (id)
                id,
                device_id,
                job_param_schema_id,
                content_type,
                label,
                latest_upload_job,
                device_path,
                last_pulled_path
            FROM
                DeviceFileDetail
            GROUP BY
                id,
                device_id,
                job_param_schema_id,
                content_type,
                label,
                latest_upload_job,
                device_path,
                last_pulled_path
        )

        SELECT
            dfl.id AS device_file_id,
            dfl.job_param_schema_id AS schema_id,
            dfl.content_type,
            dfl.label,
            dfl.device_path,
            u.id AS existing_content_retrieval_user,
            u.full_name AS existing_content_retrieval_user_name,
            fur.created AS existing_content_timestamp,
            fur.id AS existing_content_id,
            dfl.last_pulled_path AS existing_content_URL,
            j.status AS job_status
        FROM
            DeviceFileLatest dfl
        LEFT OUTER JOIN
            job j
        ON
            dfl.latest_upload_job = j.id
        LEFT OUTER JOIN
            ics_user u
        ON
            j.created_by = u.id
        LEFT OUTER JOIN
            file_upload_job fuj
        ON
            j.id = fuj.job_id
        LEFT OUTER JOIN
            file_upload_request fur
        ON
            fuj.request_id = fur.id
    `;
  const results = await server.db.read.rows(query, [deviceId, visibilityType]);
  return results;
};

/**
 * Get device file with Schema
 * @param {int} deviceFileId
 * @returns DeviceFile
 */
const getDeviceFileWithSchema = async deviceFileId =>
  // eslint-disable-next-line no-return-await
  await server.db.replica.row(
    `
        SELECT df.device_path, df.application_id, df.device_id, s.schema, t.name as device_name, st.name as site_name
        FROM device_files df
        left outer join device_files_job_param_schemas s on df.job_param_schema_id = s.id
        inner join target t on df.device_id = t.target_id 
        inner join site st on t.site_id = st.site_id
        WHERE df.id = $1
    `,
    [deviceFileId]
  );

/**
 * Get device file
 * @param {int} deviceFileId
 * @returns DeviceFile
 */
const getDeviceFile = async deviceFileId =>
  // eslint-disable-next-line no-return-await
  await server.db.read.row(
    `
        SELECT device_path, application_id, device_id, job_param_schema_id, last_pulled_path
        FROM device_files 
        WHERE id = $1
    `,
    [deviceFileId]
  );

/**
 * Get file upload request
 * @param {int} fileUploadRequestId
 * @returns DeviceFile
 */
const getFileUploadRequest = async fileUploadRequestId =>
  // eslint-disable-next-line no-return-await
  await server.db.read.row(
    `
        SELECT package_url
        FROM file_upload_request 
        WHERE id = $1
    `,
    [fileUploadRequestId]
  );

module.exports = {
  getDynamicActions,
  getDeviceFileWithSchema,
  getDeviceFile,
  getFileUploadRequest,
};
