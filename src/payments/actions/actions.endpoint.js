const Joi = require('joi');
const momentTz = require('moment-timezone');

const { server } = require('../../../app');
const {
  validateParams,
  requiresRole,
  validateBody,
} = require('../../../lib/pre');
const env = require('../../../env');
const { baseRoleGroup } = require('../../../lib/app-constants');
const handler = require('./actions.handler');

const BASE_PATH = env.config.base;

/**
 * Dynamic Action
 * Get dynamic drop down action for payment dashboard
 * @swagger
 *   "/payments/actions/{deviceId}/{appName}":
 *     get:
 *       tags:
 *         - Payment
 *       summary: "Dynamic drop down use in payment dashboard"
 *       parameters:
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *         - in: path
 *           name: deviceId
 *           type: integer
 *           required: true
 *           description: Device id to get actions.
 *         - in: path
 *           name: appName
 *           type: string
 *           description: User to make differentiate application.
 *           required: true
 *       responses:
 *         "200":
 *           description: "Successful response"
 *           schema:
 *             type: array
 *             items:
 *               type: object
 *               properties:
 *                 label:
 *                   type: string
 *                 contentType:
 *                   type: string
 *                 existingContentId:
 *                   type: string
 *                 existingContentURL:
 *                   type: string
 *                 deviceFileID:
 *                   type: string
 *                 schemaId:
 *                   type: string
 *                 existingContentTimestamp:
 *                   type: string
 *                 existingContentRetrievalUser:
 *                   type: string
 *                 existingContentRetrievalUserName:
 *                   type: string
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "404":
 *           description: "Can't find the target action for the given id"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/payments/actions/:deviceId/:appName`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    validateParams({
      deviceId: Joi.number().integer().min(1).required(),
      appName: Joi.string().optional(),
    }),
    handler.getPaymentAction,
  ]
);

/**
 * Create job for payment dashboard to retrieve request using type sys.upload
 * Jobs will be created with params if supplied in the body
 * @swagger
 *   "/payments/actions/{deviceFileId}/job":
 *     post:
 *       tags:
 *         - Payment
 *       summary: "Create job for payment dashboard to retrieve request using type sys.upload"
 *       parameters:
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *         - in: path
 *           name: deviceFileId
 *           type: integer
 *           required: true
 *           description: device file id used to create job
 *         - in: body
 *           name: body
 *           description: "Optional body payload - properties must match schema retrieved from DB"
 *           schema:
 *             type: object
 *             required:
 *              - timezone
 *             properties:
 *              timezone:
 *                type: string
 *                description: Timezone of the client(browser)
 *       responses:
 *         "200":
 *           description: "Successful response - job created"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "404":
 *           description: "Device File ID doesn't exist, or body supplied and schema doesn't exist"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/payments/actions/:deviceFileId/job`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    validateParams({
      deviceFileId: Joi.number().integer().min(1),
    }),
    validateBody({
      timezone: Joi.string()
        .valid(...momentTz.tz.names())
        .default('UTC'),
    }),
    handler.createPaymentJob,
  ]
);

/**
 * Creates a custom job for payment dashboard
 * @swagger
 *   "/payments/actions/{deviceFileId}/job/custom":
 *     post:
 *       tags:
 *         - Payment
 *       summary: "Creates a custom job for payment dashboard"
 *       parameters:
 *         - $ref: '#/parameters/AuthorizationTokenParam'
 *         - in: path
 *           name: deviceFileId
 *           type: integer
 *           required: true
 *           description: device file id to create job
 *         - in: body
 *           name: body
 *           required: true
 *           schema:
 *             type: object
 *             properties:
 *               jobData:
 *                 type: object
 *                 description: partial data of job
 *                 properties:
 *                   ids:
 *                      type: array
 *                      items:
 *                         type: string
 *                      example: ["str1", "str2", "str3"]
 *       responses:
 *         "200":
 *           description: "Successful response"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "401":
 *           $ref: "#/responses/AuthenticationFailed"
 *         "403":
 *           $ref: "#/responses/Forbidden"
 *         "404":
 *           description: "Can't find the target action"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.post(
  {
    path: `${BASE_PATH}/payments/actions/:deviceFileId/job/custom`,
    version: '0.0.1',
  },
  [
    requiresRole(baseRoleGroup.ALL),
    validateParams({
      deviceFileId: Joi.number().integer().min(1),
    }),
    validateBody({
      jobData: {
        ids: Joi.array().unique().min(1).items(Joi.string().required()),
      },
    }),
    handler.createCustomPaymentJob,
  ]
);
