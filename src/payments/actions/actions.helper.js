/* eslint-disable no-underscore-dangle */
const jobHelper = require('../../../helpers/job-helper');
const CONSTANTS = require('./lib/constants');

/**
 * Returns an array of parse S3 path.
 * eg.
 *      from - s3://bucket/path/of/key/file.json
 *      to   - ["s3://bucket/path/of/key/file.json", "bucket", "path/of/key/file.json"]
 * @param {string} s3Path
 * @returns {Array}
 */
const parseS3Uri = s3Path => {
  const parsedS3Path = s3Path.match(CONSTANTS.s3.URI_PARSER_REGEX);

  if (!parsedS3Path) {
    return null;
  }
  return parsedS3Path;
};

/**
 * Check for validation issues in the custom job request and returns any if they exist
 * @param {Array<String>} ids
 * @param {Object} fileData
 * @returns {Object}
 */
const validateCustomJobRequest = (ids, fileData) => {
  // check if _job_templates and table key is missing
  const checkFile = '_job_templates' in fileData && '_table' in fileData;

  if (!checkFile) {
    return CONSTANTS.ERROR_MESSAGE.missing_key_device_file;
  }

  // check file for selection option
  // checking if incoming ids with existing file for single or multiselect
  const selectionHandling = fileData._job_templates.find(
    item =>
      (item.handling === CONSTANTS.MULTI_SELECT && ids.length >= 1) ||
      (item.handling === CONSTANTS.SINGLE_SELECT && ids.length === 1)
  );

  if (!selectionHandling) {
    return CONSTANTS.ERROR_MESSAGE.selection_type_device_file;
  }

  // check if any of ids contain in table or not
  const deleteIdsFromFile = fileData._table.map(x => x.Delete['job.data.ids']);
  const idsNotInTable = ids.some(r => !deleteIdsFromFile.includes(r));

  if (idsNotInTable) {
    return CONSTANTS.ERROR_MESSAGE.ids_not_found;
  }

  // no validation issues
  return undefined;
};

/**
 * Builds job data and returns it as an object
 * @param {String} userId
 * @param {String} deviceId
 * @param {Array<String>} ids
 * @param {Object} fileData
 * @returns {Object}
 */
const buildCustomPaymentJobData = (userId, deviceId, ids, fileData) => {
  // check file for selection option
  // checking if incoming ids with existing file for single or multiselect
  const selectionHandling = fileData._job_templates.find(
    item =>
      (item.handling === CONSTANTS.MULTI_SELECT && ids.length >= 1) ||
      (item.handling === CONSTANTS.SINGLE_SELECT && ids.length === 1)
  );

  const type = selectionHandling['job.type'];
  const destination = selectionHandling['job.destination'];
  const ss = selectionHandling['job.data.ss'];

  const data = {
    ss,
    ids,
  };

  const jobExpiryHours = CONSTANTS.JOB_EXPIRY_HOURS;

  const now = new Date();
  const embargo = now.toISOString();
  const expiry = new Date(now.getTime());
  // set to expire in 2 hours
  expiry.setHours(now.getHours() + jobExpiryHours);

  const job = jobHelper.buildJob(
    deviceId,
    destination,
    type,
    data,
    embargo,
    expiry,
    userId
  );
  return job;
};

module.exports = {
  parseS3Uri,
  validateCustomJobRequest,
  buildCustomPaymentJobData,
};
