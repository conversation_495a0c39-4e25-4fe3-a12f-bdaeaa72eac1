const { Validator } = require('jsonschema');

const AWS = require('../../../lib/aws');
const {
  public: fileUploadRequestService,
} = require('../../file-upload/api/fileupload-request/fileupload-request.service');
const { server } = require('../../../app');
const jobHelper = require('../../../helpers/job-helper');
const {
  dbTransaction,
  fileUploadRequestedFrom,
} = require('../../../lib/app-constants');
const { isEmptyObject } = require('../../../lib/utils');
const CONSTANTS = require('./lib/constants');
const repository = require('./actions.repository');
const helper = require('./actions.helper');
const { parseS3Uri } = require('./actions.helper');

const generatePresignedUrl = async (bucket, key, options = {}) => {
  const presignedUrl = await AWS.s3GenerateGetObjectSignedURL(
    {
      Bucket: bucket,
      Key: key,
      ...options,
    },
    CONSTANTS.job.CONTENT_EXPIRES_IN_SECONDS
  );
  return presignedUrl;
};

/**
 * Returns a list of items for the Dynamic dropdown menu
 *
 * @param {int} deviceId
 * @returns list of existing drop down
 */
const getDynamicActionsList = async (appName, deviceId) => {
  const availablePaymentActions = await repository.getDynamicActions(
    appName,
    deviceId
  );

  if (!availablePaymentActions) {
    return [];
  }

  /* eslint-disable no-param-reassign */
  const enrichedActionsList = await Promise.all(
    availablePaymentActions.map(async item => {
      const isJobCompleted =
        'jobStatus' in item && item.jobStatus !== null
          ? item.jobStatus === CONSTANTS.job.STATUS.COMPLETE
          : true;

      // replace existingContentUrl with S3 preSigned URL
      if (isJobCompleted && item.existingContentUrl) {
        const parsedS3Uri = helper.parseS3Uri(item.existingContentUrl);

        item.existingContentUrl =
          parsedS3Uri && parsedS3Uri.length
            ? await generatePresignedUrl(parsedS3Uri[1], parsedS3Uri[2])
            : null;
      } else {
        // set all items with existingContentXXX to null
        Object.keys(item)
          .filter(i => i.toString().startsWith('existingContent')) // eslint-disable-line arrow-body-style
          .forEach(filteredItem => {
            item[filteredItem] = null;
          });
      }

      // if `label` is not/empty from the display_properties, use devicePath as its value
      if (!item.label || !item.label.length) {
        item.label = item.devicePath;
      }

      delete item.devicePath;
      delete item.jobStatus;

      return item;
    })
  );

  return enrichedActionsList;
};

/**
 * Get device file for device file id
 * @param {int} deviceFileId
 * @returns device file record
 */
const getDeviceFileWithSchema = async deviceFileId =>
  // eslint-disable-next-line no-return-await
  await repository.getDeviceFileWithSchema(deviceFileId);

/**
 * Validate userParams based on schema
 * @param {*} userParams
 * @param {*} schema
 * @returns validation errors if they are found
 */
const validateUserParams = (userParams, schema) => {
  const validationResponse = new Validator().validate(userParams, schema);

  if (validationResponse.errors.length) {
    return validationResponse.errors.map(e => ({
      path: e.path,
      message: e.message,
    }));
  }

  return undefined;
};

/**
 * Create job with requested param
 * @param {*} req
 * @param {*} deviceFileRecord
 * @returns create job
 */
const createPaymentJob = async (req, deviceFileRecord) => {
  let userParams = req.body;
  const userId = req.user.sub;
  const companyId = req.user.company.id;

  const { applicationId, deviceId, devicePath, deviceFileId } =
    deviceFileRecord;
  const { name: fileUploadRequestName } =
    await fileUploadRequestService.generateZipFileName({
      reqBody: {
        timezone: userParams.timezone || '',
        deviceId,
        devices: [{ id: deviceId }],
        sites: [],
        siteTags: [],
        deviceFileId,
      },
      companyId,
      userId,
      userParams,
    });

  const fileUploadRequestReference = {
    name: fileUploadRequestName,
    files: [{ path: devicePath, applicationId }],
    devices: [{ id: deviceId }],
    sites: [],
    siteTags: [],
    users: [{ id: userId }],
  };

  // Delete timezone from userParams, so it does not go into additionalJobProperties
  delete userParams.timezone;
  if (isEmptyObject(userParams)) {
    userParams = null;
  }

  const additionalJobProperties = {
    userParams,
  };

  const requestedFrom = fileUploadRequestedFrom.PAYMENT_DASHBOARD;
  return fileUploadRequestService.createFileUploadRequest(
    req.getId(),
    companyId,
    userId,
    fileUploadRequestReference,
    requestedFrom,
    additionalJobProperties
  );
};

/**
 * Get device file for device file id
 * @param {int} deviceFileId
 * @returns device file record
 */
const getDeviceFile = async deviceFileId =>
  // eslint-disable-next-line no-return-await
  await repository.getDeviceFile(deviceFileId);

/**
 * Get file upload request for file upload request id
 * @param {int} fileUploadRequestId
 * @returns file upload request
 */
const getFileUploadRequest = async fileUploadRequestId =>
  // eslint-disable-next-line no-return-await
  await repository.getFileUploadRequest(fileUploadRequestId);

/**
 * Gets file data from s3
 * @param {*} packageUrl
 * @returns File data
 */
const getFileData = async packageUrl => {
  const [, s3Bucket, s3key] = parseS3Uri(packageUrl);

  const s3Object = await AWS.downloadFromS3({
    Bucket: s3Bucket,
    Key: s3key,
  }).promise();

  const jsonString = s3Object.Body.toString();

  return JSON.parse(jsonString);
};

/**
 * Validate custom job
 * @param {*} ids
 * @param {*} fileData
 * @returns Validation issues if found
 */
const validateCustomJobRequest = async (ids, fileData) =>
  helper.validateCustomJobRequest(ids, fileData);

/**
 * Create job with custom requested param
 * @param {*} userId
 * @param {*} deviceId
 * @param {*} ids
 * @param {*} fileData
 * @returns create job
 */
const createCustomPaymentJob = async (userId, deviceId, ids, fileData) => {
  const customJobData = helper.buildCustomPaymentJobData(
    userId,
    deviceId,
    ids,
    fileData
  );

  const connection = await server.db.write.getConnection();

  try {
    await connection.execute(dbTransaction.BEGIN);
    const jobResponse = await jobHelper.createJob(connection, customJobData);
    await connection.execute(dbTransaction.COMMIT);
    return jobResponse;
  } catch (err) {
    await connection.execute(dbTransaction.ROLLBACK);
    throw err;
  } finally {
    connection.done();
  }
};

module.exports = {
  getDynamicActionsList,
  generatePresignedUrl,
  getDeviceFileWithSchema,
  validateUserParams,
  createPaymentJob,
  getDeviceFile,
  getFileUploadRequest,
  getFileData,
  validateCustomJobRequest,
  createCustomPaymentJob,
};
