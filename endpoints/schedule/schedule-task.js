const restify = require('restify');
const Joi = require('joi');
const { server } = require('../../app');
const env = require('../../env');
const { requiresRole, validateBody } = require('../../lib/pre');
const getSitesService = require('../../handlers/sites/sites-get');
const { createDevice } = require('../../handlers/device/device');
const { moveToSite } = require('../../handlers/device/device-bulkmove');
const {
  putBulkCustomAttributes,
  sendEventToEventBridgeOnCustomAttributesUpdate,
  sendEventToDeploymentReport,
} = require('../../src/custom-attributes/custom-attributes.handler');

const siteTagsHelper = require('../../helpers/site-tags-helper');
const { mainLogger } = require('../../lib/logger');

const BASE_PATH = `${env.config.base}/scheduled-task`;
const version = '1.0.0';

const logger = mainLogger();

const updateScheduledSiteTags = async (req, res, next) => {
  const { body, user, params } = req;
  const { site, deploymentType, meta } = body;

  try {
    const connection = await server.db.write.getConnection();
    try {
      await siteTagsHelper.updateSiteTags({
        companyId: user.company.id,
        siteId: params.id,
        tags: site.tags,
        deploymentType,
        connection,
        userId: user.sub,
        meta,
      });
      res.send({ message: 'success' });
      return next();
    } catch (err) {
      await connection.execute('ROLLBACK');
      throw err;
    } finally {
      connection.done();
    }
  } catch (err) {
    const message = `[scheduleTasExecute].[updateScheduledSiteTags] ${err}`;
    logger.error({ company: user.company.id }, message);
    return next(new restify.InternalServerError(err));
  }
};

/**
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
const taskExecute = async (req, res, next) => {
  const taskType = req.body.sourceType;
  switch (taskType) {
    case 'AddDevice':
      // eslint-disable-next-line no-return-await
      return await createDevice(req, res, next);
    case 'MoveDevice':
      // eslint-disable-next-line no-return-await
      return await moveToSite(req, res, next);
    case 'UpdateSiteTags':
      // eslint-disable-next-line no-return-await
      return await updateScheduledSiteTags(req, res, next);
    default:
      res.send(400, { message: 'No tasks matched.' });
      return next();
  }
};

const assignmentMW = (req, _res, next) => {
  req.user = req.body.user;
  req.params = req.body.params;
  req.body = req.body.body;

  return next();
};

server.post(
  {
    path: `${BASE_PATH}/execute`,
    version,
  },
  [requiresRole(['ICS_SYSTEM']), assignmentMW, taskExecute]
);

server.post(
  {
    path: `${BASE_PATH}/execute/custom-attributes/update`,
  },
  [
    requiresRole(['ICS_SYSTEM']),
    assignmentMW,
    putBulkCustomAttributes,
    sendEventToEventBridgeOnCustomAttributesUpdate,
    sendEventToDeploymentReport,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/sites`,
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateBody({
      siteIds: Joi.array().items(Joi.string()).required(),
    }),
    getSitesService.getAllSitesForScheduledTask,
  ]
);
