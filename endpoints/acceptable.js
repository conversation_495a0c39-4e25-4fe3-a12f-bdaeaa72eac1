const Joi = require('joi');

const { server } = require('../app');
const { validateParams, validateBody, requiresRole } = require('../lib/pre');
const acceptable = require('../handlers/acceptable');
const env = require('../env');

const BASE_PATH = `${env.config.base}/acceptable`;

server.post(
  {
    path: `${BASE_PATH}/email`,
    version: '0.0.1',
  },
  [
    validateBody({
      email: Joi.string(),
    }),
    requiresRole(['COMPANY_ADMIN', 'ANALYST', 'POWER_USER', 'SPECIALIST']),
    acceptable.checkEmail,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/password`,
    version: '0.0.1',
  },
  [
    validateBody({
      password: Joi.string(),
    }),
    acceptable.checkPassword,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/sitename`,
    version: '0.0.1',
  },
  [
    validateBody({
      siteName: Joi.string(),
    }),
    acceptable.checkSiteName,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/filename`,
    version: '0.0.1',
  },
  [
    validateBody({
      fileName: Joi.string().min(1),
    }),
    acceptable.checkFileName,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/offlinepackage/:name`,
    version: '0.0.1',
  },
  [
    validateParams({
      name: Joi.string()
        .min(3)
        .max(250)
        .regex(/^[a-zA-Z0-9._-]*$/),
    }),
    acceptable.checkOfflinePackage,
  ]
);
