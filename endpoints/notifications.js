const Joi = require('joi');

const { server } = require('../app');
const env = require('../env');
const {
  requiresRole,
  validateBody,
  validateQuery,
  validateParams,
} = require('../lib/pre');
const service = require('../handlers/notifications/notifications');
const postNotificationService = require('../handlers/notifications/notifications-create');

const BASE_PATH = `${env.config.base}/notifications/ui`;

server.get(
  {
    path: BASE_PATH,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateQuery({
      start: Joi.date().optional(),
      end: Joi.date().optional(),
      pageIndex: Joi.number().min(0).optional(),
      pageSize: Joi.number().min(-1).optional(),
    }),
    service.getNotifications,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/unreadcount`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateQuery({
      start: Joi.date().optional(),
      end: Joi.date().optional(),
    }),
    service.getUnReadCount,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/create2`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateBody({
      type: Joi.string(),
      recipientIds: Joi.array().items(Joi.string().guid()).min(1),
      timestamp: Joi.date(),
      level: Joi.string(),
      relatedEntity: Joi.object().keys({
        id: Joi.string(),
      }),
      message: Joi.string(),
    }),
    postNotificationService.createUINotification2,
  ]
);

// Deprecated (only kept to support java components that rely on this endpoint)
server.post(
  {
    path: `${BASE_PATH}/create`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateBody({
      type: Joi.string(),
      recipient: Joi.string().guid(),
      alarmId: Joi.number().optional(),
      alarmRuleId: Joi.number().optional(),
      deviceId: Joi.number().optional(),
      fileUploadRequestId: Joi.string().guid().optional(),
      rkiRequestId: Joi.string().guid().optional(),
      siteId: Joi.string().guid().optional(),
      timestamp: Joi.date(),
      level: Joi.string(),
    }),
    postNotificationService.createUINotification,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      read: Joi.boolean(),
    }),
    service.updateNotification,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/markallread`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    service.markAllRead,
  ]
);

server.del(
  {
    path: BASE_PATH,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateBody({
      end: Joi.date(),
    }),
    service.deleteNotifications,
  ]
);
