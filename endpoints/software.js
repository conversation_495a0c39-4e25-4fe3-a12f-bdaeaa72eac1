const Joi = require('joi');

const { server } = require('../app');
const { validateBody } = require('../lib/pre');
const { validateQuery } = require('../lib/pre');
const { validateParams } = require('../lib/pre');
const software = require('../handlers/software');
const { requiresRole } = require('../lib/pre');
const env = require('../env');

const BASE_PATH = `${env.config.base}/software`;

server.post(
  {
    path: `${BASE_PATH}/internal`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateBody({
      promptSetProfileName: Joi.string(),
      deviceType: Joi.string(),
      version: Joi.string().allow(null).optional().max(30),
      description: Joi.string().allow(null, '').optional().max(1000),
      prerequisiteVersion: Joi.string().allow(null).optional().max(200),
      softwareFile: Joi.string().max(100),
      softwareFileSignature: Joi.string().max(100),
      softwareFileUrl: Joi.string().max(512),
      name: Joi.string().max(100).allow(null).optional(),
      size: Joi.number(),
      type: Joi.string().valid(['software', 'media']),
      companyId: Joi.string().guid(),
      relatedEntity: Joi.string().allow(null).optional(),
      subDeviceType: Joi.string().optional(),
    }),
    software.createSofwareInternal,
  ]
);

server.get(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateQuery({
      name: Joi.string().allow('').optional(),
      deviceType: Joi.string().optional(),
      promptSetProfileName: Joi.string().optional(),
      type: Joi.string().valid('software', 'media').optional(),
      pageSize: Joi.number().min(-1).optional(),
      pageIndex: Joi.number().min(0).optional(),
      order: Joi.string().valid('name').optional(),
      subDeviceType: Joi.string().optional(),
    }),
    software.getSoftware,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER']),
    validateParams({
      id: Joi.number().integer().min(0),
    }),
    software.deleteSoftware,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER']),
    validateBody({
      deviceType: Joi.string(),
      description: Joi.string().allow(null, '').optional().max(1000),
      name: Joi.string().min(1).max(100),
      subDeviceType: Joi.string().allow(null).optional(),
      seqVersion: Joi.string().allow(null).optional(),
    }),
    software.updateSofware,
  ]
);

server.post(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER']),
    validateQuery({
      name: Joi.string().min(1).max(100),
      description: Joi.string().allow(null, '').optional().max(1000),
      type: Joi.string().valid(['software', 'media']).allow(null).optional(),
      deviceType: Joi.string().min(1),
      subDeviceType: Joi.string().optional(),
      seqVersion: Joi.string().allow(null).optional(),
    }),
    software.createSoftware,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/:id/content`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    software.getSoftwareContent,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/get-files`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateBody({
      deviceType: Joi.string(),
      type: Joi.string().valid(['software', 'media']).optional(),
      filters: Joi.object()
        .keys({
          createdBy: Joi.array().items(Joi.string().guid()).optional(),
          createdBefore: Joi.number().optional(),
          fileExtensions: Joi.array().optional(),
          fileName: Joi.string().min(0).optional(),
          isDuplicate: Joi.boolean().optional(),
        })
        .optional(),
      subDeviceType: Joi.any().optional(),
      pageSize: Joi.number().min(-1).optional(),
      pageIndex: Joi.number().min(0).optional(),
      order: Joi.string().valid(['name']).optional(),
      promptSetProfileName: Joi.string().optional(),
    }),
    software.getSoftwareByFilters,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/delete-files`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER']),
    validateBody({
      deviceType: Joi.string(),
      filters: Joi.object()
        .keys({
          createdBy: Joi.array().items(Joi.string().guid()).optional(),
          createdBefore: Joi.number().optional(),
          fileExtensions: Joi.array().optional(),
          fileName: Joi.string().optional(),
          isDuplicate: Joi.boolean().optional(),
        })
        .optional(),
      pageSize: Joi.number().min(-1).max(3000).optional(),
      include: Joi.array().optional(),
      exclude: Joi.array().optional(),
      order: Joi.string().valid(['name']).optional(),
    }),
    software.deleteMultipleSoftwares,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/delete-files/undo`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER']),
    validateBody({
      deviceType: Joi.string(),
      deleteRequestId: Joi.string().guid(),
    }),
    software.restoreSoftwares,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/delete-inactive-files`,
    version: '0.0.1',
  },
  [requiresRole(['ICS_SYSTEM']), software.deleteInactiveFiles]
);
