const { server } = require('../app');
const { requiresRole } = require('../lib/pre');
const statsService = require('../handlers/stats');
const env = require('../env');

const BASE_PATH = `${env.config.base}/stats`;

server.get(
  {
    path: `${BASE_PATH}/assets`,
    version: '0.0.1',
  },
  [requiresRole(['SUPER_ADMIN']), statsService.getStatistics]
);

server.get(
  {
    path: `${BASE_PATH}/time`,
    version: '0.0.1',
  },
  [
    (req, res, next) => {
      // Returns the current timestamp
      res.send(200, { time: +new Date() });
      return next();
    },
  ]
);
