const { server } = require('../app');
const docService = require('../handlers/docs');
const env = require('../env');

const BASE_PATH = `${env.config.base}/docs`;

server.get(
  {
    path: `${BASE_PATH}/nodejs-api.json`,
    version: '0.0.1',
  },
  [docService.getNodeJSDocs]
);

server.get(
  {
    path: `${BASE_PATH}/api.json`,
    version: '0.0.1',
  },
  [docService.getFullAPIDocs]
);

server.get(
  {
    path: `${BASE_PATH}/user-guide`,
    version: '0.0.1',
  },
  [docService.getUserGuide]
);
