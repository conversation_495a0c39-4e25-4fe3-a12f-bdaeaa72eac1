const Joi = require('joi');

const { server } = require('../app');
const { roles } = require('../lib/app-constants');
const { requiresRole } = require('../lib/pre');
const { validateParams, validateQuery, validateBody } = require('../lib/pre');
const sitegroups = require('../handlers/sitegroups');
const env = require('../env');

const BASE_PATH = `${env.config.base}/sitegroups`;

server.get(
  {
    path: BASE_PATH,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateQuery({
      company: Joi.string().guid().optional(),
    }),
    sitegroups.getSiteGroups,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [requiresRole(['COMPANY_ADMIN']), sitegroups.getSiteGroup]
);

server.get(
  {
    path: `${BASE_PATH}/internal/:id`,
    version: '0.0.1',
  },
  [requiresRole([roles.ICS_SYSTEM]), sitegroups.getSiteGroupInternal]
);

server.post(
  {
    path: BASE_PATH,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN']),
    validateBody({
      name: Joi.string().max(50),
      sites: Joi.array().items({
        id: Joi.string().guid(),
      }),
      companies: Joi.array().items({
        id: Joi.string().guid(),
      }),
    }),
    sitegroups.createSiteGroup,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN']),
    validateBody({
      name: Joi.string().max(50),
      sites: Joi.array().items({
        id: Joi.string().guid(),
      }),
      companies: Joi.array().items({
        id: Joi.string().guid(),
      }),
    }),
    validateParams({
      id: Joi.string().guid(),
    }),
    sitegroups.editSiteGroup,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [requiresRole(['COMPANY_ADMIN']), sitegroups.deleteSiteGroup]
);
