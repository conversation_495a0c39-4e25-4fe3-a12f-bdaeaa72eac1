// Endpoints for the user self service. Maps requests to the relevant function

const { server } = require('../app');
const { requiresRole } = require('../lib/pre');
const roleService = require('../handlers/roles');
const env = require('../env');

// Set the endpoints
const BASE_PATH = `${env.config.base}/roles`;

// Create the apis with the correct endpoints, calling the relevant service
server.get(
  {
    name: 'getRoles',
    path: BASE_PATH,
    version: '0.0.1',
  },
  [requiresRole(['COMPANY_ADMIN']), roleService.getRoles]
);
