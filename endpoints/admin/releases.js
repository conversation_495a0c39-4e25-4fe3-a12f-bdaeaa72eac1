const Joi = require('joi');

const env = require('../../env');
const { server } = require('../../app');
const handler = require('../../handlers/admin/releases');
const {
  requiresRole,
  validateQuery,
  validateParams,
  validateBody,
} = require('../../lib/pre');

const BASE_PATH = `${env.config.base}/admin/releases`;

server.get(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateQuery({
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    handler.getReleases,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/search`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateQuery({
      release: Joi.string().optional(),
      title: Joi.string().optional(),
      description: Joi.string().optional(),
      updatedBy: Joi.string().optional(),
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    handler.searchReleases,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/:release`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateParams({
      release: Joi.string(),
    }),
    handler.getReleaseById,
  ]
);

server.post(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateBody({
      release: Joi.string().regex(/^[0-9]{4}.[0-9]{3}.[0-9]{3}$/),
      releaseDate: Joi.date().iso(),
      title: Joi.string(),
      description: Joi.string(),
      visible: Joi.boolean(),
      updatedBy: Joi.string(),
      userGuide: Joi.string(),
    }),
    handler.createRelease,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/:release`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateBody({
      releaseDate: Joi.date().iso(),
      title: Joi.string(),
      description: Joi.string(),
      visible: Joi.boolean(),
      updatedBy: Joi.string(),
      userGuide: Joi.string(),
    }),
    handler.updateRelease,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/:release`,
    version: '0.0.1',
  },
  [requiresRole(['ICS_SYSTEM']), handler.deleteRelease]
);
