// Endpoints for the user service. Maps requests to the relevant function

const { throttle } = require('restify');
const Joi = require('joi');

const { server } = require('../app');
const { requiresRole } = require('../lib/pre');
const { validateBody } = require('../lib/pre');
const { validateQuery } = require('../lib/pre');
const userService = require('../handlers/users');
const { baseRoleGroup } = require('../lib/app-constants');
const env = require('../env');
const userHandler = require('../handlers/users/user.handler');
const { validatePersonaTypes } = require('../helpers/persona-helper');
const { validateBankPermissions } = require('../helpers/bank-helper');

const emailThrottle = env.config.emailThrottle
  ? throttle({
      ...env.config.emailThrottle,
      username: true,
      ip: false,
      xff: false,
    })
  : (req, res, next) => next();

// Set the endpoints
const BASE_PATH = `${env.config.base}/users`;

// TODO: only basic company search has been implemented
server.get(
  {
    path: BASE_PATH,
    name: 'getUsers',
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'ANALYST', 'POWER_USER', 'SPECIALIST']),
    validateQuery({
      pageIndex: Joi.number().min(0).optional(),
      pageSize: Joi.number().min(-1).optional(),
      roles: Joi.string().optional(),
      companyId: Joi.string().guid().optional(),
      pending: Joi.boolean().optional(),
      q: Joi.string().optional(),
      allRoles: Joi.boolean().optional(),
    }),
    userService.getUsers,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/search`,
    name: 'searchUsers',
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN']),
    validateBody({
      email: Joi.string().min(3).optional(),
      name: Joi.string().min(3).optional(),
    }),
    userService.search,
  ]
);

server.post(
  {
    name: 'inviteUser',
    path: `${BASE_PATH}/invite`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'SUPER_ADMIN']),
    validateBody({
      companyId: Joi.string().guid().optional(),
      email: Joi.string().email({ minDomainAtoms: 2 }),
      fullName: Joi.string(),
      roles: Joi.array().items(Joi.string()).min(1),
      groups: Joi.array().items(Joi.string()),
    }),
    validatePersonaTypes,
    validateBankPermissions,
    userService.invite,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [requiresRole(['COMPANY_ADMIN']), userService.getUserById]
);

server.put(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN']),
    validateBody({
      email: Joi.string().email({ minDomainAtoms: 2 }),
      fullName: Joi.string().min(3).max(64),
      roles: Joi.array().min(1).items(Joi.string()),
      groups: Joi.array().items(Joi.string()),
      userUnlocked: Joi.boolean().optional(),
    }),
    validatePersonaTypes,
    validateBankPermissions,
    userService.updateUserById,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [requiresRole(['COMPANY_ADMIN']), userService.archiveUserById]
);

server.del(
  {
    path: `${BASE_PATH}/:id/mfa`,
    version: '0.0.1',
  },
  [requiresRole(['COMPANY_ADMIN']), userService.clearUserMFA]
);

server.post(
  {
    path: `${BASE_PATH}/:id/pendingregistration/resend`,
    name: 'Resend user pending registration',
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN']),
    emailThrottle,
    userService.resendPendingRegistration,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/lockinactive`,
    version: '0.0.1',
  },
  [requiresRole(['ICS_SYSTEM']), userService.lockInactiveUsers]
);

server.post(
  {
    path: `${BASE_PATH}/warninactive`,
    version: '0.0.1',
  },
  [requiresRole(['ICS_SYSTEM']), userService.warnInactiveUsers]
);

server.get(
  {
    path: `${BASE_PATH}/fullname/:id`,
    version: '0.0.1',
  },
  [requiresRole(baseRoleGroup.ALL), userHandler.getUserFullNameById]
);

// To get both active and inactive users
server.get(
  {
    path: `${BASE_PATH}/all/activeinactive`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateQuery({
      pageIndex: Joi.number().min(0).optional(),
      pageSize: Joi.number().min(-1).optional(),
      roles: Joi.string().optional(),
      companyId: Joi.string().guid().optional(),
      pending: Joi.boolean().optional(),
      q: Joi.string().optional(),
      allRoles: Joi.boolean().optional(),
    }),
    userService.getAllUsers,
  ]
);
