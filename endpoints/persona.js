const { server } = require('../app');

const { requiresRole } = require('../lib/pre');
const env = require('../env');
const personaHandler = require('../handlers/persona/persona.handler');

// Set the endpoints
const BASE_PATH = `${env.config.base}/persona`;

server.get(
  {
    path: BASE_PATH,
    name: 'getPersonaList',
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'SUPER_ADMIN']),
    personaHandler.getPersonaListByCompany,
  ]
);
