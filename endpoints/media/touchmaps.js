const Joi = require('joi');
const { server } = require('../../app');
const env = require('../../env');
const pre = require('../../lib/pre');

const { validateParams } = pre;
const { validateBody } = pre;
const { requiresRole } = pre;

const touchmaps = require('../../handlers/media/touchmaps');

const BASE_PATH = `${env.config.base}/media/touchmaps`;

server.get(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER', 'MEDIA_APPROVER', 'MEDIA_DEPLOYER']),
    touchmaps.getAllTouchmaps,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    touchmaps.getTouchmap,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateBody({
      name: Joi.string(),
      areas: Joi.array().items(
        Joi.object().keys({
          shape: Joi.string().valid('rect', 'circle'),
          coords: Joi.string(),
          keyCode: Joi.string().optional(),
          softkeyId: Joi.string().optional(),
        })
      ),
    }),
    touchmaps.createTouchmap,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/:id/areas`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody(
      Joi.array().items(
        Joi.object().keys({
          shape: Joi.string().valid('rect', 'circle'),
          coords: Joi.string(),
          keyCode: Joi.string().optional(),
          softkeyId: Joi.string().optional(),
        })
      )
    ),
    touchmaps.editTouchmapAreas,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      name: Joi.string().optional(),
      description: Joi.string().optional(),
    }),
    touchmaps.editTouchmap,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    touchmaps.deleteTouchmap,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/:id/toODML`,
    version: '0.0.1',
  },
  [requiresRole(['ICS_SYSTEM', 'MEDIA_DESIGNER']), touchmaps.toODML]
);

server.get(
  {
    path: `${BASE_PATH}/:id/promptsets`,
    version: '0.0.1',
  },
  [requiresRole(['MEDIA_DESIGNER']), touchmaps.getPromptSets]
);
