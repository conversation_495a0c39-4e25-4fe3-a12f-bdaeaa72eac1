const Joi = require('joi');

const { server } = require('../../app');
const {
  requiresRole,
  requiresFeatureFlag,
  validateBody,
  validateParams,
  validateQuery,
} = require('../../lib/pre');
const dayparts = require('../../handlers/media/dayparts');
const env = require('../../env');

const BASE_PATH = `${env.config.base}/media/dayparts`;

server.get(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['MEDIA', 'PLAYLIST', 'PLAYLIST_MANAGEMENT']),
    validateParams({
      id: Joi.string().guid(),
    }),
    dayparts.getDayPartByID,
  ]
);

server.get(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['MEDIA', 'PLAYLIST', 'PLAYLIST_MANAGEMENT']),
    validateQuery({
      active: Joi.boolean().optional(),
    }),
    dayparts.getAllDayParts,
  ]
);

server.post(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'PLAYLIST_PROVIDER']),
    requiresFeatureFlag(['MEDIA', 'PLAYLIST']),
    validateBody({
      start: Joi.number().integer().greater(-1),
      end: Joi.number().integer().positive(),
      name: Joi.string().min(1),
    }),
    dayparts.createDayPart,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'PLAYLIST_PROVIDER']),
    requiresFeatureFlag(['MEDIA', 'PLAYLIST']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      start: Joi.number().integer().greater(-1),
      end: Joi.number().integer().positive(),
      name: Joi.string().min(1),
    }),
    dayparts.updateDayPart,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'PLAYLIST_PROVIDER']),
    requiresFeatureFlag(['MEDIA', 'PLAYLIST']),
    validateParams({
      id: Joi.string().guid(),
    }),
    dayparts.deleteDayPart,
  ]
);
