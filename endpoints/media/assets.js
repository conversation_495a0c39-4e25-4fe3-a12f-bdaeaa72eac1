const Joi = require('joi');

const { server } = require('../../app');
const env = require('../../env');
const constants = require('../../lib/app-constants');
const {
  requiresRole,
  requiresFeatureFlag,
  validateQuery,
  validateParams,
  validateBody,
} = require('../../lib/pre');
const assets = require('../../handlers/media/assets');

const BASE_PATH = `${env.config.base}/media`;

server.post(
  {
    path: `${BASE_PATH}/assets`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    requiresFeatureFlag(['MEDIA']),
    // Because this is a custom multipart upload, params validation is in the handler
    assets.uploadAsset,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/assets`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateBody({
      name: Joi.string().min(3),
      type: Joi.string(),
      company: Joi.string().guid(),
      width: Joi.number(),
      height: Joi.number(),
      size: Joi.number(),
      sourceUrl: Joi.string(),
      thumbnailUrl: Joi.string(),
      status: Joi.any().valid([
        constants.assetStatus.NEW,
        constants.assetStatus.PROCESSED,
      ]),
    }),
    assets.createAssetInternal,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/assets/:id/thumbnail`,
    version: '0.0.1',
  },
  [
    validateParams({
      id: Joi.string().guid(),
    }),
    assets.getAssetThumbnail,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/assets/:id/source`,
    version: '0.0.1',
  },
  [
    validateParams({
      id: Joi.string().guid(),
    }),
    assets.getAssetSource,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/assets/:id`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'ICS_SYSTEM',
      'MEDIA_DESIGNER',
      'MEDIA_APPROVER',
      'MEDIA_DEPLOYER',
    ]),
    validateParams({
      id: Joi.string().guid(),
    }),
    assets.getAsset,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/assets/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      name: Joi.string(),
    }),
    assets.updateAsset,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/assets/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    assets.deleteAsset,
  ]
);

server.patch(
  {
    path: `${BASE_PATH}/assets/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      name: Joi.string().optional(),
      type: Joi.string().optional(),
      thumbnailUrl: Joi.string().allow(null).optional(),
      sourceUrl: Joi.string().allow(null).optional(),
      package: Joi.string().guid().optional(),
      width: Joi.number().allow(null).optional(),
      height: Joi.number().allow(null).optional(),
      size: Joi.number().allow(null).optional(),
      status: Joi.any()
        .valid([
          constants.assetStatus.NEW,
          constants.assetStatus.PROCESSED,
          constants.assetStatus.ARCHIVED,
        ])
        .optional(),
    }),
    assets.updateAssetInternal,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/assets`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER', 'MEDIA_APPROVER', 'MEDIA_DEPLOYER']),
    requiresFeatureFlag(['MEDIA']),
    validateQuery({
      active: Joi.string().valid(['TRUE', 'FALSE', 'BOTH']).optional(),
      type: Joi.string().optional(),
      name: Joi.string().optional(),
      signed: Joi.boolean().optional(),
      width: Joi.number().min(0).optional(),
      height: Joi.number().min(0).optional(),
      minWidth: Joi.number().min(0).optional(),
      minHeight: Joi.number().min(0).optional(),
      maxWidth: Joi.number().min(0).optional(),
      maxHeight: Joi.number().min(0).optional(),
      uploader: Joi.string().guid().optional(),
      order: Joi.any().valid(['uploaded', 'name', 'uploader']).optional(),
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
      videoExtension: Joi.string().min(4).valid(['.bin', '.webm']).optional(),
    }),
    assets.getAssets,
  ]
);

/**
 * @swagger
 *   "/assets/{id}/pubstatus":
 *     get:
 *       tags:
 *         - Assets
 *       summary: "Assets Publish Status"
 *       description: "Get Assets Publish Status"
 *       produces:
 *         - "application/json"
 *       parameters:
 *         - in: query
 *           name: id
 *           type: string
 *           format: uuid
 *           required: true
 *       responses:
 *         "200":
 *           description: "Successful response"
 *         "400":
 *           $ref: "#/responses/BadRequest"
 *         "500":
 *           $ref: "#/responses/InternalServerError"
 */
server.get(
  {
    path: `${BASE_PATH}/assets/:id/pubstatus`,
    version: '0.0.1',
  },
  [
    validateParams({
      id: Joi.string().guid(),
    }),
    assets.getAssetsPublishStatus,
  ]
);
