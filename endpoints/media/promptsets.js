/* eslint-disable newline-per-chained-call */
const Joi = require('joi');

const { server } = require('../../app');
const {
  requiresConfig,
  requiresRole,
  requiresFeatureFlag,
  validateQuery,
  validateParams,
  validateBody,
} = require('../../lib/pre');
const constants = require('../../lib/app-constants');
const promptsets = require('../../handlers/media/promptsets');
const { config } = require('../../env');

const BASE_PATH = config.base;

server.get(
  {
    path: `${BASE_PATH}/media/promptsets`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER', 'MEDIA_APPROVER', 'MEDIA_DEPLOYER']),
    requiresFeatureFlag(['MEDIA']),
    validateQuery({
      q: Joi.string().optional(),
      state: Joi.string().optional(),
      promptSetProfileName: Joi.string().optional(),
      order: Joi.string().optional(),
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    promptsets.getAllPromptSets,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/media/promptsets/profile`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER', 'MEDIA_APPROVER', 'MEDIA_DEPLOYER']),
    requiresFeatureFlag(['MEDIA']),
    validateQuery({
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    promptsets.getAllPromptSetProfiles,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/media/promptsets`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateBody({
      template: Joi.string().guid(),
      promptSetProfileName: Joi.string(),
      name: Joi.string().min(3),
    }),
    promptsets.createPromptSet,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/media/promptsets/:id`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'MEDIA_DESIGNER',
      'MEDIA_APPROVER',
      'MEDIA_DEPLOYER',
      'ICS_SYSTEM',
    ]),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptsets.getPromptSetByID,
  ]
);

server.patch(
  {
    path: `${BASE_PATH}/media/promptsets/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      status: Joi.any().valid([
        constants.promptSetStatus.PUBLISHED,
        constants.promptSetStatus.PUBLISHING_FAILED,
        constants.promptSetStatus.PREVIEW,
        constants.promptSetStatus.PREVIEW_GENERATION_FAILED,
      ]),
    }),
    promptsets.updatePromptSetInternal,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/media/promptsets/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      name: Joi.string().min(3),
      fontColor: Joi.string().regex(/[0-9A-F]{6}$/i),
    }),
    promptsets.updatePromptSet,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/media/promptsets/:id/bg`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateQuery({
      bg: Joi.alternatives().try(
        Joi.string().guid(),
        Joi.string().regex(/[0-9A-F]{6}$/i)
      ),
      force: Joi.boolean().optional(),
    }),
    promptsets.updatePromptSetBg,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/media/promptsets/:id/font`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateQuery(
      Joi.object()
        .keys({
          fontColor: Joi.string()
            .regex(/[0-9A-F]{6}$/i)
            .optional(),
          fontFace: Joi.string()
            .max(50)
            .regex(/^[ A-Za-z0-9_@./#&+-]*$/)
            .optional(),
          fontSize: Joi.number().min(1).max(380).optional(),
          all: Joi.boolean().optional(),
        })
        .or('fontColor', 'fontFace', 'fontSize') // At least one of these keys must be in the object to be valid.
        .required()
    ),
    promptsets.updatePromptSetFont,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/media/promptsets/:id/clone`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptsets.clonePromptSet,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/media/promptsets/:id/forapproval`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      approvers: Joi.array().min(2).items({
        id: Joi.string().guid(),
      }),
    }),
    promptsets.forApprovalPromptSet,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/media/promptsets/:id/approvers`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER', 'MEDIA_APPROVER', 'MEDIA_DEPLOYER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptsets.getApprovers,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/media/promptsets/:id/approve`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_APPROVER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      mfaCode: Joi.string(),
    }),
    promptsets.approvePromptSet,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/media/promptsets/:id/reject`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_APPROVER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      mfaCode: Joi.string(),
    }),
    promptsets.rejectPromptSet,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/media/promptsets/:id/preview`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptsets.previewPromptSet,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/media/promptsets/:id/export`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptsets.exportPromptSet,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/media/promptsets/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptsets.deletePromptSet,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/media/promptsets/:promptSetId/prompts/:promptId/exception`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateBody({
      dayPart: Joi.object().keys({
        id: Joi.string().guid(),
      }),
    }),
    validateParams({
      promptSetId: Joi.string().guid(),
      promptId: Joi.string().guid(),
    }),
    promptsets.createPromptException,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/media/promptsets/:id/signpackage`,
    version: '0.0.1',
  },
  [
    requiresConfig(
      ['$.signPackage.scriptPath', '$.signPackage.user', '$.signPackage.slot'],
      config
    ),
    requiresRole(['ICS_SYSTEM']),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptsets.signPackage,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/media/promptsets/:id/fingerprint`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      secureFingerprint: Joi.string().optional().allow(''),
      nonSecureFingerprint: Joi.string().optional().allow(''),
    }),
    promptsets.saveFingerprint,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/media/promptsets/:id/children`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER', 'MEDIA_APPROVER', 'MEDIA_DEPLOYER']),
    requiresFeatureFlag(['MEDIA']),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptsets.getPromptSetChildren,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/media/promptsets/package/:id/download`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER', 'MEDIA_APPROVER', 'MEDIA_DEPLOYER']),
    requiresFeatureFlag(['MEDIA']),
    validateParams({
      id: Joi.number(),
    }),
    promptsets.getPromptSetPackage,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/media/promptsets/:id/prompts`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateBody(
      Joi.array()
        .min(1)
        .items(
          Joi.object().keys({
            promptId: Joi.string().guid(),
            elements: Joi.array().min(1),
            softkeys: Joi.array().items(
              Joi.object().keys({
                softkey: Joi.number().positive().integer(),
                label: Joi.string().optional(),
                fontColor: Joi.string()
                  .allow(null)
                  .length(6)
                  .regex(/[0-9A-F]/, { name: 'Hex Color' })
                  .optional(),
                fontSize: Joi.number()
                  .allow(null)
                  .positive()
                  .integer()
                  .optional(),
                keycode: Joi.string(),
              })
            ),
            touchmap: Joi.object()
              .keys({
                id: Joi.string().guid(),
              })
              .allow(null)
              .optional(),
            transactionState: Joi.string()
              .valid(
                null,
                constants.transactionState.FUELING,
                constants.transactionState.IDLE
              )
              .optional(),
          })
        )
    ),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptsets.updatePrompts,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/media/promptsets/import`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    requiresFeatureFlag(['MEDIA']),
    validateBody({
      type: Joi.string(),
      id: Joi.string().guid(),
    }),
    promptsets.importPromptSet,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/media/promptsets/:id/languages`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'MEDIA_DESIGNER',
      'MEDIA_APPROVER',
      'MEDIA_DEPLOYER',
      'ICS_SYSTEM',
    ]),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptsets.getPromptSetLanguages,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/media/promptsets/:id/languages`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateBody(
      Joi.array()
        .min(1)
        .items(
          Joi.object().keys({
            languageSupportId: Joi.string().guid(),
            promptSetLanguageSupport: Joi.object().keys({
              type: Joi.string().allow(null),
              size: Joi.number().integer().positive().min(1).allow(null),
              default: Joi.boolean(),
              deleted: Joi.boolean(),
            }),
          })
        )
    ),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptsets.setPromptSetLanguages,
  ]
);
