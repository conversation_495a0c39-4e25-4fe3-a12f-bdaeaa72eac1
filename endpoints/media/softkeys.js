const Joi = require('joi');

const { server } = require('../../app');
const env = require('../../env');
const {
  requiresRole,
  requiresFeatureFlag,
  validateParams,
} = require('../../lib/pre');
const softkeys = require('../../handlers/media/softkeys');

const BASE_PATH = `${env.config.base}/media/softkeys`;

server.get(
  {
    path: `${BASE_PATH}/:deviceType`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'ICS_SYSTEM',
      'COMPANY_ADMIN',
      'POWER_USER',
      'ANALYST',
      'SPECIALIST',
      'USER',
    ]),
    requiresFeatureFlag(['MEDIA']),
    validateParams({
      deviceType: Joi.string(),
    }),
    softkeys.getSoftkeysForDeviceType,
  ]
);
