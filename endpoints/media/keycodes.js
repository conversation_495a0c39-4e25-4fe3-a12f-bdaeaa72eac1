const { server } = require('../../app');
const env = require('../../env');
const { requiresRole, requiresFeatureFlag } = require('../../lib/pre');
const keycodes = require('../../handlers/media/keycodes');

const BASE_PATH = `${env.config.base}/media`;

server.get(
  {
    path: `${BASE_PATH}/keycodes`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER', 'MEDIA_APPROVER', 'MEDIA_DEPLOYER']),
    requiresFeatureFlag(['MEDIA']),
    keycodes.getKeycodes,
  ]
);
