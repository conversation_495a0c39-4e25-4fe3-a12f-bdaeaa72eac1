const Joi = require('joi');

const { server } = require('../../app');
const { requiresRole } = require('../../lib/pre');
const { validateBody } = require('../../lib/pre');
const { validateParams } = require('../../lib/pre');
const promptstates = require('../../handlers/media/promptstates');
const BASE_PATH = require('../../env').config.base;

server.get(
  {
    path: `${BASE_PATH}/media/promptstates`,
    version: '0.0.1',
  },
  [requiresRole(['ICS_SYSTEM']), promptstates.getAllStates]
);

server.post(
  {
    path: `${BASE_PATH}/media/promptstates`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateBody({
      code: Joi.string(),
      promptTemplateId: Joi.string().guid(),
    }),
    promptstates.createState,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/media/promptstates/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptstates.editState,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/media/promptstates/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateParams({
      id: Joi.string().guid(),
    }),
    promptstates.deleteState,
  ]
);
