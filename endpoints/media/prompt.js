const Joi = require('joi');

const { server } = require('../../app');
const { requiresRole, validateBody, validateQuery } = require('../../lib/pre');
const prompt = require('../../handlers/media/prompt');
const { transactionState } = require('../../lib/app-constants');
const BASE_PATH = require('../../env').config.base;

server.del(
  {
    path: `${BASE_PATH}/media/prompt/:id`,
    version: '0.0.1',
  },
  [requiresRole(['MEDIA_DESIGNER']), prompt.deletePrompt]
);

server.post(
  {
    path: `${BASE_PATH}/media/prompt/:id/toODML`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'ICS_SYSTEM',
      'MEDIA_DESIGNER',
      'MEDIA_APPROVER',
      'MEDIA_DEPLOYER',
    ]),
    validateBody(
      Joi.array().items(
        Joi.object().keys({
          id: Joi.string().optional(),
          face: Joi.string().optional(),
          path: Joi.string().optional(),
          audioPath: Joi.string().optional(),
          videoPath: Joi.string().optional(),
        })
      )
    ),
    validateQuery({
      beautify: Joi.boolean().optional(),
    }),
    prompt.toODML,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/media/prompt/:id/validate`,
    version: '0.0.1',
  },
  [requiresRole(['ICS_SYSTEM', 'MEDIA_DESIGNER']), prompt.validate]
);

server.get(
  {
    path: `${BASE_PATH}/media/prompt/:id/thumbnail`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'ICS_SYSTEM',
      'MEDIA_DESIGNER',
      'MEDIA_DEPLOYER',
      'MEDIA_APPROVER',
    ]),
    prompt.getThumbnail,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/media/prompt/:id/thumbnail`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'ICS_SYSTEM',
      'MEDIA_DESIGNER', // added to skip the auth code block(req token) in Lambda
    ]),
    prompt.setThumbnailUrl,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/media/prompt/:id/elements`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateBody(Joi.array().min(1)),
    prompt.updateElements,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/media/prompt/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateBody({
      elements: Joi.array().min(1),
      softkeys: Joi.array().items(
        Joi.object().keys({
          softkey: Joi.number().positive().integer(),
          label: Joi.string().optional(),
          fontColor: Joi.string()
            .allow(null)
            .length(6)
            .regex(/[0-9A-F]/, { name: 'Hex Color' })
            .optional(),
          fontSize: Joi.number().allow(null).positive().integer().optional(),
          keycode: Joi.string(),
        })
      ),
      touchmap: Joi.object()
        .keys({
          id: Joi.string().guid(),
        })
        .allow(null)
        .optional(),
      transactionState: Joi.string()
        .valid(null, transactionState.FUELING, transactionState.IDLE)
        .optional(),
    }),
    prompt.updatePrompt,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/media/prompt/:id/touchmap`,
    version: '0.0.1',
  },
  [requiresRole(['MEDIA_DESIGNER']), prompt.removeTouchmap]
);
