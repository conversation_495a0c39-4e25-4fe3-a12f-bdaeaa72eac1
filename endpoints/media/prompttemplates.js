const Joi = require('joi');

const { server } = require('../../app');
const {
  requiresRole,
  requiresFeatureFlag,
  validateParams,
  validateBody,
} = require('../../lib/pre');
const prompttemplates = require('../../handlers/media/prompttemplates');
const BASE_PATH = require('../../env').config.base;

server.get(
  {
    path: `${BASE_PATH}/media/prompttemplates`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'SUPER_ADMIN',
      'MEDIA_DESIGNER',
      'MEDIA_APPROVER',
      'MEDIA_DEPLOYER',
    ]),
    requiresFeatureFlag(['MEDIA']),
    prompttemplates.getAllPromptTemplates,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/media/prompttemplates/:id/states`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'SUPER_ADMIN',
      'MEDIA_DESIGNER',
      'MEDIA_APPROVER',
      'MEDIA_DEPLOYER',
    ]),
    requiresFeatureFlag(['MEDIA']),
    validateParams({
      id: Joi.string().guid(),
    }),
    prompttemplates.getPromptTemplateStateByID,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/media/prompttemplates`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateBody({
      name: Joi.string().min(3),
      defaultBg: Joi.string(),
      states: Joi.array().items(
        Joi.object().keys({
          id: Joi.string().guid(),
        })
      ),
    }),
    prompttemplates.createPromptTemplate,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/media/prompttemplates/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      name: Joi.string().min(3),
    }),
    prompttemplates.editPromptTemplate,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/media/prompttemplates/:id/company/:companyId`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateParams({
      id: Joi.string().guid(),
      companyId: Joi.string().guid(),
    }),
    prompttemplates.assignToCompany,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/media/prompttemplates/:id/company/:companyId`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateParams({
      id: Joi.string().guid(),
      companyId: Joi.string().guid(),
    }),
    prompttemplates.unassignFromCompany,
  ]
);
