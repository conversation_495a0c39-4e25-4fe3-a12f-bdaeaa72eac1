/* eslint-disable newline-per-chained-call */
const Joi = require('joi');

const { server } = require('../../app');
const { requiresRole } = require('../../lib/pre');
const { validateParams } = require('../../lib/pre');
const prompttypes = require('../../handlers/media/prompttypes');
const BASE_PATH = require('../../env').config.base;

server.get(
  {
    path: `${BASE_PATH}/media/prompttypes`,
    version: '0.0.1',
  },
  [
    requiresRole(['USER', 'POWER_USER', 'COMPANY_ADMIN', 'ICS_SYSTEM']),
    validateParams({
      state: Joi.string().guid().optional(),
    }),
    prompttypes.getPromptTypes,
  ]
);
