const Joi = require('joi');

const { server } = require('../../app');
const env = require('../../env');
const constants = require('../../lib/app-constants');
const {
  requiresRole,
  requiresFeatureFlag,
  validateQuery,
  validateParams,
  validateBody,
} = require('../../lib/pre');
const assetPackage = require('../../handlers/media/asset-package');

const BASE_PATH = `${env.config.base}/media`;

server.get(
  {
    path: `${BASE_PATH}/assetpackages/:id/assets`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER', 'MEDIA_APPROVER', 'MEDIA_DEPLOYER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    assetPackage.getAssetsInPackage,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/assetpackages/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    assetPackage.deleteAssetPackage,
  ]
);

server.patch(
  {
    path: `${BASE_PATH}/assetpackages/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      name: Joi.string().optional(),
      deviceType: Joi.string().optional(),
      signed: Joi.boolean().optional(),
      status: Joi.any()
        .valid([
          constants.assetStatus.NEW,
          constants.assetStatus.PROCESSED,
          constants.assetStatus.ERROR,
        ])
        .optional(),
    }),
    assetPackage.editAssetPackageInternal,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/assetpackages/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      name: Joi.string().optional(),
      deviceType: Joi.string().optional(),
    }),
    assetPackage.editAssetPackage,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/assetpackages/:id`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'ICS_SYSTEM',
      'MEDIA_DESIGNER',
      'MEDIA_APPROVER',
      'MEDIA_DEPLOYER',
    ]),
    validateParams({
      id: Joi.string().guid(),
    }),
    assetPackage.getAssetPackage,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/assetpackages`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER']),
    requiresFeatureFlag(['MEDIA']),
    // request validation is in the handler because we are using a custom handler
    assetPackage.uploadAssetPackage,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/assetpackages`,
    version: '0.0.1',
  },
  [
    requiresRole(['MEDIA_DESIGNER', 'MEDIA_APPROVER', 'MEDIA_DEPLOYER']),
    requiresFeatureFlag(['MEDIA']),
    validateQuery({
      name: Joi.string().optional(),
      order: Joi.any()
        .valid(['name-asc', 'name-desc', 'uploaded-asc', 'uploaded-desc'])
        .optional(),
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    assetPackage.getAssetPackages,
  ]
);
