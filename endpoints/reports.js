const Joi = require('joi');
const { server } = require('../app');
const statusReport = require('../handlers/reports/status-report');
const versionsReport = require('../handlers/reports/version-report');
const alarmHistoryReport = require('../handlers/reports/alarm-history-report');
const referenceData = require('../handlers/reports/reference-data');
const devicesReport = require('../handlers/reports/current-site-devices');
const apiAuditReport = require('../handlers/reports/api-audit-report');
const rolloutReport = require('../handlers/reports/rollout-report');
const userHistoryReport = require('../handlers/reports/user-history-report');
const sitePopReport = require('../handlers/reports/site-pop-report');
const playlistReport = require('../handlers/reports/playlist-notification-report');
const sitePopAggregator = require('../handlers/reports/site-pop-report-aggregator');
const assetsReport = require('../handlers/reports/assets-report');
const env = require('../env');
const { config } = require('../env');
const {
  validateBody,
  validateQuery,
  requiresRole,
  requiresFeatureFlag,
  requiresConfig,
  validateParams,
} = require('../lib/pre');

const BASE_PATH = `${env.config.base}/reports`;

server.post(
  {
    path: `${BASE_PATH}/status-report`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['REPORTING']),
    requiresConfig(['$.dbQueryTimeoutInSeconds'], config),
    validateBody({
      statuses: Joi.array()
        .min(1)
        .items(Joi.string().regex(/^[^|]+[|][^|]+$/g)),
      sites: Joi.array().items(Joi.string().guid()).optional(),
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    statusReport.getReportStatus,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/status-report/csv`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['REPORTING']),
    validateBody({
      statuses: Joi.array()
        .min(1)
        .items(Joi.string().regex(/^[^|]+[|][^|]+$/g)),
      sites: Joi.array().items(Joi.string().guid()).optional(),
    }),
    statusReport.getReportStatusCsv,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/version-report`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['REPORTING']),
    requiresConfig(['$.dbQueryTimeoutInSeconds'], config),
    validateBody({
      statuses: Joi.array()
        .min(1)
        .items(Joi.string().regex(/^[^|]+[|][^|]+$/g)),
      sites: Joi.array().items(Joi.string().guid()).optional(),
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
      tags: Joi.array().optional().items(Joi.string()),
    }),
    versionsReport.getVersionReport,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/version-report/csv`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['REPORTING']),
    validateBody({
      statuses: Joi.array()
        .min(1)
        .items(Joi.string().regex(/^[^|]+[|][^|]+$/g)),
      sites: Joi.array().items(Joi.string().guid()).optional(),
      tags: Joi.array().optional().items(Joi.string()),
    }),
    versionsReport.getVersionReportCsv,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/alarm-history-report`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['REPORTING']),
    requiresConfig(['$.dbQueryTimeoutInSeconds'], config),
    validateBody({
      alarms: Joi.array().min(1).items(Joi.string()),
      start: Joi.date(),
      end: Joi.date(),
      sites: Joi.array().items(Joi.string().guid()).optional(),
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    alarmHistoryReport.getReport,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/alarm-history-report/csv`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['REPORTING']),
    validateBody({
      alarms: Joi.array().min(1).items(Joi.string()),
      start: Joi.date(),
      end: Joi.date(),
      sites: Joi.array().items(Joi.string().guid()).optional(),
      type: Joi.string().optional(),
    }),
    alarmHistoryReport.getReportCSV,
  ]
);

server.get(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [requiresFeatureFlag(['REPORTING']), referenceData.getReports]
);

server.get(
  {
    path: `${BASE_PATH}/alarms`,
    version: '0.0.1',
  },
  [requiresFeatureFlag(['REPORTING']), referenceData.getReportAlarms]
);

server.get(
  {
    path: `${BASE_PATH}/statuses`,
    version: '0.0.1',
  },
  [requiresFeatureFlag(['REPORTING']), referenceData.getReportStatuses]
);

server.get(
  {
    path: `${BASE_PATH}/metrics`,
    version: '0.0.1',
  },
  [requiresFeatureFlag(['REPORTING']), referenceData.getReportMetrics]
);

server.get(
  {
    path: `${BASE_PATH}/versions`,
    version: '0.0.1',
  },
  [requiresFeatureFlag(['REPORTING']), referenceData.getReportVersions]
);

server.get(
  {
    path: `${BASE_PATH}/events`,
    version: '0.0.1',
  },
  [requiresFeatureFlag(['REPORTING']), referenceData.getReportEvents]
);

server.get(
  {
    path: `${BASE_PATH}/software`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['REPORTING']),
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateQuery({
      limit: Joi.number().min(1).optional(),
    }),
    referenceData.getSoftware,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/rollout-report`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['REPORTING']),
    requiresConfig(['$.dbQueryTimeoutInSeconds'], config),
    validateBody({
      softwarePackages: Joi.array().items(Joi.string()),
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    rolloutReport.getReport,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/rollout-report/csv`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['REPORTING']),
    validateBody({
      softwarePackages: Joi.array().items(Joi.string()),
    }),
    rolloutReport.getReportCSV,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/current-sites-devices`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['REPORTING']),
    requiresConfig(['$.dbQueryTimeoutInSeconds'], config),
    validateBody({
      sites: Joi.array().items(Joi.string().guid()).optional(),
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    devicesReport.getAssetReport,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/current-sites-devices/csv`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['REPORTING']),
    validateBody({
      sites: Joi.array().items(Joi.string().guid()).optional(),
      allConfig: Joi.boolean().optional(),
    }),
    devicesReport.getAssetReportCSV,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/api-audit`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN']),
    requiresFeatureFlag(['REPORTING']),
    requiresConfig(['$.dbQueryTimeoutInSeconds'], config),
    validateBody({
      users: Joi.array()
        .min(1)
        .items(Joi.string().guid().allow(null))
        .optional(),
      statuses: Joi.array()
        .min(1)
        .items(Joi.string().regex(/[1-5]XX/g))
        .optional(),
      base_routes: Joi.array().min(1).items(Joi.string()).optional(),
      start: Joi.date().iso(),
      end: Joi.date().iso().min(Joi.ref('start')),
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    apiAuditReport.validateDateRange,
    apiAuditReport.getReport,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/api-audit/csv`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN']),
    requiresFeatureFlag(['REPORTING']),
    validateBody({
      users: Joi.array()
        .min(1)
        .items(Joi.string().guid().allow(null))
        .optional(),
      statuses: Joi.array()
        .min(1)
        .items(Joi.string().regex(/[1-5]XX/g))
        .optional(),
      base_routes: Joi.array().min(1).items(Joi.string()).optional(),
      start: Joi.date().iso(),
      end: Joi.date().iso().min(Joi.ref('start')),
    }),
    apiAuditReport.validateDateRange,
    apiAuditReport.getReportCSV,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/user-history-report`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN']),
    requiresFeatureFlag(['REPORTING']),
    validateBody({
      user: Joi.string().optional(),
      roles: Joi.array().min(1).items(Joi.string().allow(null)).optional(),
      permissions: Joi.array()
        .min(1)
        .items(Joi.string().allow(null))
        .optional(),
      statuses: Joi.array().min(1).items(Joi.number().allow(null)).optional(),
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    userHistoryReport.getReports,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/user-history-report/csv`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN']),
    requiresFeatureFlag(['REPORTING']),
    validateBody({
      user: Joi.string().optional(),
      roles: Joi.array().min(1).items(Joi.string().allow(null)).optional(),
      statuses: Joi.array().min(1).items(Joi.number().allow(null)).optional(),
    }),
    userHistoryReport.getReportCSV,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/reaggregate/site-pop`,
    version: '0.0.1',
  },
  [
    requiresConfig(
      [
        '$.AWS.DDB.deviceDataTable',
        '$.AWS.DDB.sitePOPTable',
        '$.AWS.DDB.sitePOPTableTTLinDays',
        '$.AWS.DDB.sitePOP_DLQ',
        '$.AWS.DDB.ddbMaxTimeoutInSeconds',
        '$.AWS.DDB.ddbInitTimeoutInSeconds',
      ],
      config
    ),
    requiresRole(['SUPER_ADMIN']),
    sitePopAggregator.reaggregateSiteDailyData,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/aggregator/site-pop`,
    version: '0.0.1',
  },
  [
    requiresConfig(
      [
        '$.AWS.DDB.deviceDataTable',
        '$.AWS.DDB.sitePOPTable',
        '$.AWS.DDB.sitePOPTableTTLinDays',
        '$.AWS.DDB.sitePOP_DLQ',
        '$.AWS.DDB.ddbMaxTimeoutInSeconds',
        '$.AWS.DDB.ddbInitTimeoutInSeconds',
      ],
      config
    ),
    requiresRole(['ICS_SYSTEM']),
    sitePopAggregator.aggregateSiteDailyData,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/site/pop`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['GSTV', 'PLAYLIST', 'PLAYLIST_MANAGEMENT']),
    requiresConfig(
      ['$.AWS.DDB.sitePOPTable', '$.AWS.DDB.sitePOPTableTTLinDays'],
      config
    ),
    validateBody({
      start: Joi.date().format('YYYY-MM-DD').optional(),
      end: Joi.date().format('YYYY-MM-DD').min(Joi.ref('start')).optional(),
      siteIds: Joi.array().optional().min(1).items(Joi.string()),
      mediaNames: Joi.array().optional().min(1).items(Joi.string()),
      pageKey: Joi.object().optional(),
      pageMinSize: Joi.number().min(1).max(1000).optional(),
    }),
    sitePopReport.getReport,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/site/pop-csv`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['GSTV', 'PLAYLIST', 'PLAYLIST_MANAGEMENT']),
    requiresConfig(
      ['$.AWS.DDB.sitePOPTable', '$.AWS.DDB.sitePOPTableTTLinDays'],
      config
    ),
    validateBody({
      start: Joi.date().format('YYYY-MM-DD').optional(),
      end: Joi.date().format('YYYY-MM-DD').min(Joi.ref('start')).optional(),
      siteIds: Joi.array().optional().min(1).items(Joi.string()),
      mediaNames: Joi.array().optional().min(1).items(Joi.string()),
    }),
    sitePopReport.getReportCSV,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/playlist/notification`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['GSTV', 'REPORTING', 'PLAYLIST']),
    requiresRole(['COMPANY_ADMIN', 'PLAYLIST_PROVIDER']),
    requiresConfig(['$.AWS.DDB.deviceDataTable'], config),
    validateBody({
      start: Joi.number().integer().min(1).optional(),
      end: Joi.number().integer().min(Joi.ref('start')).optional(),
      siteIds: Joi.array().optional().min(1).items(Joi.string()),
      deviceIds: Joi.array()
        .optional()
        .min(1)
        .items(Joi.number().integer().min(1)),
    }),
    playlistReport.getReport,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/playlist/notification/csv`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['GSTV', 'REPORTING', 'PLAYLIST']),
    requiresRole(['COMPANY_ADMIN', 'PLAYLIST_PROVIDER']),
    requiresConfig(['$.AWS.DDB.deviceDataTable'], config),
    validateBody({
      start: Joi.number().integer().min(1).optional(),
      end: Joi.number().integer().min(Joi.ref('start')).optional(),
      siteIds: Joi.array().optional().min(1).items(Joi.string()),
      deviceIds: Joi.array()
        .optional()
        .min(1)
        .items(Joi.number().integer().min(1)),
    }),
    playlistReport.getReportCSV,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/assets-report/:siteId`,
    version: '0.0.1',
  },
  [
    requiresFeatureFlag(['SHELL_SUPPORT']),
    validateParams({
      siteId: Joi.string().guid(),
    }),
    assetsReport.getSiteAssetsReport,
  ]
);
