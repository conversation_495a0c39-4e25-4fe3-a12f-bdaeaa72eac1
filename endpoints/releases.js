const Joi = require('joi');

const env = require('../env');
const { server } = require('../app');
const handler = require('../handlers/releases');
const { requiresRole, validateQuery, validateParams } = require('../lib/pre');

const BASE_PATH = `${env.config.base}/releases`;

server.get(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'USER',
      'ANALYST',
      'SPECIALIST',
    ]),
    validateQuery({
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    handler.getReleases,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/search`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'USER',
      'ANALYST',
      'SPECIALIST',
    ]),
    validateQuery({
      release: Joi.string().optional(),
      title: Joi.string().optional(),
      description: Joi.string().optional(),
      updatedBy: Joi.string().optional(),
      pageSize: Joi.number().min(1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    handler.searchReleases,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/:release`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'USER',
      'ANALYST',
      'SPECIALIST',
    ]),
    validateParams({
      release: Joi.string(),
    }),
    handler.getReleaseById,
  ]
);
