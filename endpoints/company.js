const Joi = require('joi');

const { server } = require('../app');
const company = require('../handlers/company/company');
const env = require('../env');
const {
  validateParams,
  validateBody,
  requiresRole,
  validateQuery,
} = require('../lib/pre');

const BASE_PATH = `${env.config.base}/companies`;

server.post(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresRole(['SUPER_ADMIN']),
    validateBody({
      name: Joi.string().min(3).max(50),
      reference: Joi.string().optional(),
      supportEmail: Joi.string().email().optional(),
      senderEmail: Joi.string().email().optional(),
      sessionExpiryDeviceMins: Joi.number().optional(),
      sessionExpiryUserMins: Joi.number().optional(),
      promptsetPackageVersion: Joi.string().optional(),
      deletable: Joi.boolean().optional().default(false),
      featureFlags: Joi.array().items(Joi.string()).optional(),
      deviceKeys: Joi.array()
        .items({
          deviceType: Joi.string(),
          key: Joi.string(),
        })
        .optional(),
      deviceTypes: Joi.array()
        .items({
          deviceType: Joi.string(),
          displayName: Joi.string(),
        })
        .optional(),
      promptTemplates: Joi.array().items(Joi.string().guid()).optional(),
      companyRelationships: Joi.array().items(Joi.string().guid()).optional(),
      keyGroups: Joi.array()
        .items({
          name: Joi.string(),
          ref: Joi.string(),
          certificateIssuerId: Joi.number(),
        })
        .optional(),
    }),
    company.createCompany,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['SUPER_ADMIN']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      name: Joi.string().min(3).max(50).optional(),
      defaultSite: Joi.string().guid().optional(),
      reference: Joi.string().optional(),
      supportEmail: Joi.string().email().optional(),
      senderEmail: Joi.string().email().optional(),
      sessionExpiryDeviceMins: Joi.number().optional(),
      sessionExpiryUserMins: Joi.number().optional(),
      promptsetPackageVersion: Joi.string().optional(),
      featureFlags: Joi.array().items(Joi.string()).optional(),
      deviceKeys: Joi.array()
        .items({
          deviceType: Joi.string(),
          key: Joi.string(),
        })
        .optional(),
      deviceTypes: Joi.array()
        .items({
          deviceType: Joi.string(),
          displayName: Joi.string(),
        })
        .optional(),
      promptTemplates: Joi.array().items(Joi.string().guid()).optional(),
      companyRelationships: Joi.array().items(Joi.string().guid()).optional(),
      deletable: Joi.boolean().optional(),
    }),
    company.updateCompany,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['SUPER_ADMIN']),
    validateParams({
      id: Joi.string().guid(),
    }),
    company.deleteCompany,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/suppliers`,
    version: '0.0.1',
  },
  [requiresRole(['COMPANY_ADMIN']), company.getSuppliers]
);

server.get(
  {
    path: `${BASE_PATH}/consumers`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'ANALYST', 'POWER_USER', 'SPECIALIST']),
    company.getConsumers,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/self`,
    version: '0.0.1',
  },
  [company.getSelf]
);

server.get(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'FORECOURT_GATEWAY_PROVIDER', 'RKI']),
    validateParams({
      id: Joi.string().guid(),
    }),
    company.getCompany,
  ]
);

server.get(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresRole(['SUPER_ADMIN']),
    validateQuery({
      name: Joi.string().min(3).max(50).optional(),
    }),
    company.getAll,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/:id/languages`,
    version: '0.0.1',
  },
  [
    validateParams({
      id: Joi.string().guid(),
    }),
    company.getLanguages,
  ]
);
