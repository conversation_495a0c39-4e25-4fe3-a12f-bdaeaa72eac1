const Joi = require('joi');

const { server } = require('../app');
const { validateParams, validateBody, requiresRole } = require('../lib/pre');
const bridge = require('../handlers/bridge');
const env = require('../env');
const {
  validateDeviceJsonCertificate,
} = require('../helpers/validate-device-certificate-helper');

const BASE_PATH = `${env.config.base}/bridge`;

server.post({ path: `${BASE_PATH}/devices`, version: '0.0.1' }, [
  requiresRole(['BRIDGE_APP']),
  validateBody({
    name: Joi.string(),
    siteId: Joi.string().guid(),
    certificate: Joi.string(),
    isJsonCertificates: Joi.boolean().default(false).optional(),
    productTypeId: Joi.number().integer(),
    serialNumber: Joi.string().regex(/^[a-z0-9-]+$/i),
    data: Joi.string().optional(),
    description: Joi.string().optional(),
    macAddress: Joi.string().optional(),
  }),
  validateDeviceJsonCertificate('bridgeAddDevice'),
  bridge.createDevice,
]);

server.put({ path: `${BASE_PATH}/devices/:serialNumber`, version: '0.0.1' }, [
  requiresRole(['BRIDGE_APP']),
  validateParams({ serialNumber: Joi.string() }),
  validateBody({
    name: Joi.string(),
    siteId: Joi.string().guid().default(null).optional(),
    productTypeId: Joi.number().integer().default(null).optional(),
    certificate: Joi.string(),
    isJsonCertificates: Joi.boolean().default(false).optional(),
    data: Joi.string().optional(),
    description: Joi.string().optional(),
    macAddress: Joi.string().optional(),
  }),
  validateDeviceJsonCertificate('bridgeUpdateDevice'),
  bridge.putDevice,
]);

server.get({ path: `${BASE_PATH}/devices/:serialNumber`, version: '0.0.1' }, [
  requiresRole(['BRIDGE_APP']),
  validateParams({ serialNumber: Joi.string() }),
  bridge.getDevice,
]);
