const Joi = require('joi');

const { server } = require('../app');
const { requiresRole } = require('../lib/pre');
const alarms = require('../handlers/alarms');
const env = require('../env');
const { validateParams, validateBody } = require('../lib/pre');

const BASE_PATH = `${env.config.base}/alarms`;
const version = '1.0.0';

server.post(
  {
    path: `${BASE_PATH}`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'ANALYST']),
    validateBody({
      name: Joi.string().regex(/^[A-Za-z0-9- _\\s]{1,30}$/),
      rules: Joi.array().min(1).items(Joi.number()),
      notifications: Joi.array()
        .min(1)
        .items(
          Joi.object().keys({
            type: Joi.string().regex(/^[E,U]{1,1}$/),
            userId: Joi.string().guid().optional().allow(null),
            address: Joi.string()
              .email({ minDomainAtoms: 2 })
              .optional()
              .allow(null),
          })
        ),
      subjects: Joi.array()
        .items(
          Joi.object().keys({
            type: Joi.string().regex(/^[S,T]{1,1}$/),
            id: Joi.string(),
          })
        )
        .optional(),
      active: Joi.boolean().optional(),
    }),
    alarms.create,
  ]
);

server.get(
  {
    path: `${BASE_PATH}`,
    version,
  },
  [requiresRole(['COMPANY_ADMIN', 'ANALYST']), alarms.findAll]
);

server.get(
  {
    path: `${BASE_PATH}/count-per-type`,
    version,
  },
  [alarms.getCountPerType]
);

server.get(
  {
    path: `${BASE_PATH}/rules`,
    version,
  },
  [alarms.getRules]
);

server.get(
  {
    path: `${BASE_PATH}/company-rules`,
    version,
  },
  [alarms.getCompanyRules]
);

server.get(
  {
    path: `${BASE_PATH}/:id`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'ANALYST', 'ICS_SYSTEM']),
    validateParams({
      id: Joi.number().integer().min(1),
    }),
    alarms.findById,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/:id`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'ANALYST']),
    validateParams({
      id: Joi.number().integer().min(1),
    }),
    alarms.delete,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/:id`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'ANALYST']),
    validateParams({
      id: Joi.number().integer().min(0),
    }),
    validateBody({
      name: Joi.string().regex(/^[A-Za-z0-9- _\\s]{1,30}$/),
      rules: Joi.array().min(1).items(Joi.number()),
      notifications: Joi.array()
        .min(1)
        .items(
          Joi.object().keys({
            type: Joi.string().regex(/^[E,U]{1,1}$/),
            userId: Joi.string().guid().optional().allow(null),
            address: Joi.string()
              .email({ minDomainAtoms: 2 })
              .optional()
              .allow(null),
          })
        ),
      subjects: Joi.array()
        .items(
          Joi.object().keys({
            type: Joi.string().regex(/^[S,T]{1,1}$/),
            id: Joi.string(),
          })
        )
        .optional(),
      active: Joi.boolean(),
    }),
    alarms.update,
  ]
);
