const Joi = require('joi');
const { server } = require('../../app');
const env = require('../../env');
const pre = require('../../lib/pre');

const { validateQuery } = pre;
const { validateParams } = pre;
const { requiresRole } = pre;

const state = require('../../handlers/states/device-state');

const BASE_PATH = env.config.base;

server.get(
  {
    path: `${BASE_PATH}/devices/:id/alarms`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'ANALYST',
      'SPECIALIST',
      'USER',
      'ICS_SYSTEM',
    ]),
    validateParams({
      id: Joi.number().min(1),
    }),
    validateQuery({
      type: Joi.string().optional(),
    }),
    state.getDeviceAlarms,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/sites/:id/alarms`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'ANALYST',
      'SPECIALIST',
      'USER',
      'ICS_SYSTEM',
    ]),
    validateParams({
      id: Joi.string().guid(),
    }),
    state.getSiteAlarms,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/sites/:id/devices/alarms`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'ANALYST',
      'SPECIALIST',
      'USER',
      'ICS_SYSTEM',
    ]),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateQuery({
      type: Joi.string().optional(),
    }),
    state.getSiteDevicesAlarms,
  ]
);
