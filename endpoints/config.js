import { server } from '../app';
import { config } from '../env';

const GTM_KEY = config.gtmKey || '';
const GMAPS_KEY = config.gMapsKey || '';
const BASE_PATH = `${config.base}/config`;

server.get(
  {
    path: `${BASE_PATH}/public`,
    version: '0.0.1',
  },
  [
    (req, res, next) => {
      const response = {};

      if (GTM_KEY) {
        response.gtmKey = GTM_KEY;
      }
      if (GMAPS_KEY) {
        response.gMapsKey = GMAPS_KEY;
      }
      res.send({ status: 200, ...response });
      return next();
    },
  ]
);
