const Joi = require('joi');
const env = require('../env');
const { server } = require('../app');
const appConstants = require('../lib/app-constants');
const sitesService = require('../handlers/sites');
const putSiteService = require('../handlers/sites/sites-put');
const getSitesService = require('../handlers/sites/sites-get');
const postSitesService = require('../handlers/sites/sites-post');
const patchSiteService = require('../handlers/sites/sites-patch');
const getSitesByIdService = require('../handlers/sites/sites-get-id');
const deleteSiteByIdService = require('../handlers/sites/sites-delete-id');
const getSitesServiceInternal = require('../handlers/sites/sites-get-internal');
const getExternalReferencesHandler = require('../handlers/sites/sites-get-externaltypes');
const {
  checkMaintenanceFrameAndRoles,
  validateExistsScheduled,
  scheduleDeploy,
} = require('../helpers/scheduled-deployment-helper');

const {
  requiresRole,
  validateParams,
  validateQuery,
  validateBody,
} = require('../lib/pre');

const BASE_PATH = `${env.config.base}/sites`;

server.get(
  {
    path: `${BASE_PATH}/summary`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateQuery({
      status: Joi.number().optional(),
      company: Joi.string().guid().optional(),
      q: Joi.string().optional().allow(''),
      tags: Joi.string().optional(),
      siteGroups: Joi.string().optional(),
    }),
    sitesService.getSitesSummary,
  ]
);

server.get(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
      'PLAYLIST_PROVIDER',
    ]),
    validateQuery({
      statuses: Joi.array()
        .items(
          Joi.string().valid([
            'NORMAL',
            'UNKNOWN',
            'WARNING',
            'INACTIVE',
            'CRITICAL',
          ])
        )
        .optional(),
      company: Joi.string().guid().optional(),
      alarms: Joi.array().items(Joi.string()).min(1).optional(),
      q: Joi.string().optional().allow(''),
      pageIndex: Joi.number().min(0).optional(),
      pageSize: Joi.number().min(-1).optional(),
      order: Joi.string().optional(),
      tags: Joi.string().optional(),
      siteGroups: Joi.string().optional(),
      showHiddenSites: Joi.boolean().optional(),
    }),
    getSitesService.getAllSites,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/list`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
      'PLAYLIST_PROVIDER',
    ]),
    validateQuery({
      siteEvents: Joi.array()
        .items(Joi.string().valid(Object.values(appConstants.siteEvents)))
        .optional(),
      statuses: Joi.array()
        .items(
          Joi.string().valid([
            'NORMAL',
            'UNKNOWN',
            'WARNING',
            'INACTIVE',
            'CRITICAL',
          ])
        )
        .optional(),
      deviceStatuses: Joi.array()
        .items(
          Joi.string().valid([
            'OPERATIONAL',
            'UNKNOWN',
            'INACTIVE',
            'OUT_OF_SERVICE',
          ])
        )
        .optional(),
      oosFilter: Joi.array().items(Joi.string()).optional(),
      q: Joi.string().optional().allow(''),
      pageIndex: Joi.number().min(0).optional(),
      pageSize: Joi.number().min(-1).optional(),
      order: Joi.string().optional(),
      tags: Joi.string().optional(),
      showHiddenSites: Joi.boolean().optional(),
      isCSV: Joi.boolean().optional(),
    }),
    getSitesService.getSitesList,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/internal`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateQuery({
      companyId: Joi.array().items(Joi.string().guid()).optional(),
      pageIndex: Joi.number().min(0).optional(),
      pageSize: Joi.number().min(-1).optional(),
    }),
    getSitesServiceInternal.getAllSites,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/authorization`,
    version: '0.0.1',
  },
  [
    requiresRole(['FORECOURT_GATEWAY_PROVIDER']),
    validateQuery({
      sites: Joi.array().items(Joi.string().guid()).optional(),
    }),
    getSitesService.verifyAuthorization,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/external-types`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'ANALYST', 'POWER_USER']),
    getExternalReferencesHandler.getExternalReferencesTypes,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
      'PLAYLIST_PROVIDER',
    ]),
    validateParams({
      id: Joi.string().guid(),
    }),
    getSitesByIdService.getSiteById,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/:id/devices`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateParams({
      id: Joi.string().guid(),
      pageIndex: Joi.number().min(0).optional(),
      pageSize: Joi.number().min(-1).optional(),
    }),
    getSitesByIdService.getDevicesForSite,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/reference/:id`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateParams({
      id: Joi.string(),
    }),
    getSitesByIdService.getSiteByReferenceId,
  ]
);

server.post(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'ANALYST', 'POWER_USER']),
    validateBody({
      name: Joi.string().regex(/^(?!\s)(?!.*\s$)['A-Za-z0-9-&$#._,/ ]{3,100}$/),
      address: Joi.string(),
      keyGroupId: Joi.string().guid().allow(null).optional(),
      contactPhone: Joi.string().max(30).allow('').allow(null).optional(), // eslint-disable-line newline-per-chained-call
      contactEmail: Joi.string()
        .max(50)
        .email()
        .allow('')
        .allow(null)
        .optional(), // eslint-disable-line newline-per-chained-call
      formattedAddress: Joi.string(),
      latitude: Joi.number().max(90).min(-90),
      longitude: Joi.number().max(180).min(-180),
      referenceId: Joi.string().allow(null).max(2700).optional(),
      siteGroups: Joi.array()
        .length(1)
        .items(
          Joi.object({
            id: Joi.string().guid(),
            name: Joi.string().optional(),
          })
        ),
      tags: Joi.array()
        .items(Joi.object({ name: Joi.string() }))
        .required(),
      mfaCode: Joi.string().allow(null).length(6).optional(),
      timezoneId: Joi.string(),
      hours: Joi.array()
        .length(7)
        .items(
          Joi.object().keys({
            openAt: Joi.number().min(0).max(86400000),
            closeAt: Joi.number().min(0).max(86400000).min(Joi.ref('openAt')),
          })
        )
        .optional(),
      visible: Joi.boolean().optional(),
      suppressOffhoursAlarm: Joi.boolean().optional(),
      integrationId: Joi.alternatives()
        .try(Joi.string(), Joi.number().integer())
        .optional(),
      externalReferences: Joi.array()
        .items(
          Joi.object({
            referenceId: Joi.string(),
            referenceType: Joi.string().valid(
              Object.keys(appConstants.sites.ALLOWEDEXTERNALREFERENCETYPES)
            ),
          })
        )
        .unique()
        .optional(),
      disableCmAutomaticDeployments: Joi.boolean().optional(),
      disableFileDownloads: Joi.boolean().optional(),
    }),
    postSitesService.createNewSite,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'ANALYST', 'POWER_USER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      site: {
        name: Joi.string().regex(
          /^(?!\s)(?!.*\s$)['A-Za-z0-9-&$#._,/ ]{3,100}$/
        ),
        address: Joi.string(),
        keyGroupId: Joi.string().guid().allow(null).optional(),
        contactPhone: Joi.string().max(30).allow('').allow(null).optional(), // eslint-disable-line newline-per-chained-call
        contactEmail: Joi.string()
          .email()
          .max(50)
          .allow('')
          .allow(null)
          .optional(), // eslint-disable-line newline-per-chained-call
        formattedAddress: Joi.string(),
        latitude: Joi.number().max(90).min(-90),
        longitude: Joi.number().max(180).min(-180),
        referenceId: Joi.string().allow(null).max(2700).optional(), // eslint-disable-line newline-per-chained-call
        tags: Joi.array()
          .items(Joi.object({ name: Joi.string() }))
          .required(),
        mfaCode: Joi.string().allow(null).length(6).optional(),
        timezoneId: Joi.string(),
        hours: Joi.array()
          .length(7)
          .items(
            Joi.object().keys({
              openAt: Joi.number().min(0).max(86400000),
              closeAt: Joi.number().min(0).max(86400000).min(Joi.ref('openAt')),
            })
          )
          .optional(),
        visible: Joi.boolean().optional(),
        suppressOffhoursAlarm: Joi.boolean().optional(),
        externalReferences: Joi.array()
          .items(
            Joi.object({
              referenceId: Joi.string(),
              referenceType: Joi.string().valid(
                Object.keys(appConstants.sites.ALLOWEDEXTERNALREFERENCETYPES)
              ),
            })
          )
          .unique()
          .optional(),
        disableCmAutomaticDeployments: Joi.boolean().optional(),
        disableFileDownloads: Joi.boolean().optional(),
      },
      deploymentType: Joi.string()
        .valid('maintenance-window', 'immediate', 'schedule')
        .required(),
      scheduledDateTime: Joi.date().iso().optional(),
      meta: Joi.object().default(undefined).optional(),
    }),
    checkMaintenanceFrameAndRoles('UpdateSiteTags'),
    validateExistsScheduled('UpdateSiteTags'),
    scheduleDeploy('UpdateSiteTags'),
    putSiteService.putSiteById,
  ]
);

server.patch(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'ANALYST', 'POWER_USER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      site: {
        name: Joi.string()
          .regex(/^(?!\s)(?!.*\s$)['A-Za-z0-9-&$#._,/ ]{3,100}$/)
          .required(),
      },
    }),
    patchSiteService.patchSiteById,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/:id`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'ANALYST', 'POWER_USER']),
    validateParams({
      id: Joi.string().guid(),
    }),
    deleteSiteByIdService.deleteSiteById,
  ]
);
