// Endpoints for the user group service. Maps requests to the relevant function

const Joi = require('joi');

const { server } = require('../app');
const { requiresRole } = require('../lib/pre');
const { validateBody } = require('../lib/pre');
const { validateQuery } = require('../lib/pre');
const userGroupService = require('../handlers/usergroups');
const env = require('../env');

// Set the endpoints
const BASE_PATH = `${env.config.base}/usergroups`;
const ID_PATH = `${BASE_PATH}/:id`;

// Create the apis with the correct endpoints, calling the relevant service
server.get(
  {
    path: BASE_PATH,
    name: 'getUserGroups',
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'ANALYST', 'SPECIALIST']),
    validateQuery({
      name: Joi.string().optional(),
      order: Joi.any().valid(['name', 'created']).optional(),
      pageIndex: Joi.number().min(0).optional(),
      pageSize: Joi.number().min(-1).optional(),
    }),
    userGroupService.getUserGroups,
  ]
);

server.get(
  {
    path: ID_PATH,
    name: 'getUserGroup',
    version: '0.0.1',
  },
  [requiresRole(['COMPANY_ADMIN']), userGroupService.getUserGroup]
);

server.post(
  {
    name: 'createUserGroup',
    path: BASE_PATH,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN']),
    validateBody({
      name: Joi.string().max(50),
      users: Joi.array().items({
        id: Joi.string().guid(),
      }),
      sitegroups: Joi.array().items({
        id: Joi.string().guid(),
      }),
    }),
    userGroupService.createUserGroup,
  ]
);

server.put(
  {
    name: 'updateUserGroup',
    path: ID_PATH,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN']),
    validateBody({
      name: Joi.string().max(50),
      users: Joi.array().items({
        id: Joi.string().guid(),
      }),
      sitegroups: Joi.array().items({
        id: Joi.string().guid(),
      }),
    }),
    userGroupService.updateUserGroup,
  ]
);

server.del(
  {
    name: 'deleteUserGroup',
    path: ID_PATH,
    version: '0.0.1',
  },
  [requiresRole(['COMPANY_ADMIN']), userGroupService.deleteUserGroup]
);
