// Endpoints for the user self service. Maps requests to the relevant function

const Joi = require('joi');

const { server } = require('../app');
const { validateBody } = require('../lib/pre');
const selfService = require('../handlers/self');
const env = require('../env');

// Set the endpoints
const BASE_PATH = `${env.config.base}/self`;

// Create the apis with the correct endpoints, calling the relevant service
server.get(
  {
    name: 'self',
    path: BASE_PATH,
    version: '0.0.1',
  },
  [selfService.self]
);

server.post(
  {
    name: 'selfPassword',
    path: `${BASE_PATH}/password`,
    version: '0.0.1',
  },
  [
    validateBody({
      mfaCode: Joi.string(),
      password: Joi.string(),
    }),
    selfService.password,
  ]
);

server.post(
  {
    name: 'selfPasswordRest',
    path: `${BASE_PATH}/passwordReset`,
    version: '0.0.1',
  },
  [
    validateBody({
      userId: Joi.string(),
      password: Joi.string(),
    }),
    selfService.passwordReset,
  ]
);
