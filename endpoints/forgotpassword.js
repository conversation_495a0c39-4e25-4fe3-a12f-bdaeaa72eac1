// Endpoints for the forgot password service. Maps requests to the relevant function

const Joi = require('joi');
const { throttle } = require('restify');

const { server } = require('../app');
const { validateBody } = require('../lib/pre');
const forgotPasswordService = require('../handlers/forgotpassword');
const env = require('../env');

const throttleConfig =
  env.config.emailThrottle && env.config.emailThrottle.preferRawIpOverXFF
    ? { ...env.config.emailThrottle, ip: true }
    : { ...env.config.emailThrottle, xff: true };

const emailThrottle = env.config.emailThrottle
  ? throttle(throttleConfig)
  : (req, res, next) => next();

// Set the endpoints
const BASE_PATH = `${env.config.base}/forgotpassword`;

// Create the apis with the correct endpoints, calling the relevant service
server.post(
  {
    name: 'forgotPassword',
    path: BASE_PATH,
    version: '0.0.1',
  },
  [
    validateBody({
      email: Joi.string().email(),
    }),
    emailThrottle,
    forgotPasswordService.forgotPassword,
  ]
);
