const Joi = require('joi');

const { server } = require('../app');
const { validateQuery, validateParams, validateBody } = require('../lib/pre');
const offlinePackage = require('../handlers/offlinepackage');
const { requiresRole } = require('../lib/pre');
const { requiresFeatureFlag } = require('../lib/pre');
const env = require('../env');

const BASE_PATH = `${env.config.base}/offlinepackage`;
const version = '1.0.0';

// Create an offline software package
server.post(
  {
    path: `${BASE_PATH}/software`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'SPECIALIST', 'ANALYST']),
    requiresFeatureFlag(['OFFLINE_JOBS']),
    validateBody({
      name: Joi.string()
        .min(3)
        .max(250)
        .regex(/^[a-zA-Z0-9._-]*$/),
      softwareIds: Joi.array().unique().min(1).items(Joi.number().integer()),
    }),
    offlinePackage.createSoftwareDownload,
  ]
);

// Create an offline RKI package
server.post(
  {
    path: `${BASE_PATH}/rki`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'SPECIALIST', 'ANALYST']),
    requiresFeatureFlag(['OFFLINE_RKI']),
    validateBody({
      name: Joi.string()
        .min(3)
        .max(250)
        .regex(/^[a-zA-Z0-9._-]*$/),
      deviceIds: Joi.array().unique().min(1).items(Joi.number().integer()),
    }),
    offlinePackage.createRkiDownload,
  ]
);

// List offline packages
server.get(
  {
    path: `${BASE_PATH}`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'SPECIALIST',
      'ANALYST',
      'USER',
    ]),
    requiresFeatureFlag(['OFFLINE_JOBS', 'OFFLINE_RKI']),
    validateQuery({
      type: Joi.string().valid('software', 'RKI').optional(),
      status: Joi.string().valid('DONE', 'IN_PROGRESS').optional(),
      name: Joi.string().min(1).optional(),
      pageIndex: Joi.number().integer().min(0).optional(),
      pageSize: Joi.number().integer().min(1).optional(),
    }),
    offlinePackage.list,
  ]
);

// Get an offline package detail
server.get(
  {
    path: `${BASE_PATH}/:id`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'SPECIALIST',
      'ANALYST',
      'USER',
    ]),
    requiresFeatureFlag(['OFFLINE_JOBS', 'OFFLINE_RKI']),
    validateParams({
      id: Joi.string().guid(),
    }),
    offlinePackage.get,
  ]
);

// Download an offline package
server.get(
  {
    path: `${BASE_PATH}/:id/content`,
    version,
  },
  [
    validateParams({
      id: Joi.string().guid(),
    }),
    offlinePackage.download,
  ]
);

// Rename an offline package
server.put(
  {
    path: `${BASE_PATH}/:id`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'SPECIALIST', 'ANALYST']),
    requiresFeatureFlag(['OFFLINE_JOBS', 'OFFLINE_RKI']),
    validateParams({
      id: Joi.string().guid(),
    }),
    validateBody({
      name: Joi.string()
        .min(3)
        .max(250)
        .regex(/^[a-zA-Z0-9._-]*$/),
    }),
    offlinePackage.rename,
  ]
);

// Delete an offline package
server.del(
  {
    path: `${BASE_PATH}/:id`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'SPECIALIST', 'ANALYST']),
    requiresFeatureFlag(['OFFLINE_JOBS', 'OFFLINE_RKI']),
    validateParams({
      id: Joi.string().guid(),
    }),
    offlinePackage.del,
  ]
);
