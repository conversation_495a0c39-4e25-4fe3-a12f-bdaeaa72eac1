const Joi = require('joi');

const env = require('../env');
const { server } = require('../app');
const handler = require('../handlers/web-activity');
const { requiresRole } = require('../lib/pre');
const { validateBody } = require('../lib/pre');

const BASE_PATH = `${env.config.base}/web-activity`;
const version = '1.0.0';

server.post(
  {
    path: `${BASE_PATH}`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'USER',
      'ANALYST',
      'SPECIALIST',
    ]),
    validateBody({
      activityType: Joi.string(),
      objectId: Joi.string().optional(),
      command: Joi.object().optional(),
      result: Joi.string().optional(),
    }),
    handler.createWebActivity,
  ]
);
