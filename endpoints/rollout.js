const Joi = require('joi');

const { server } = require('../app');
const { requiresRole } = require('../lib/pre');
const rollout = require('../handlers/rollout');
const { config } = require('../env');
const { validateQuery } = require('../lib/pre');

const BASE_PATH = config.base;
const version = '1.0.0';

server.get(
  {
    path: `${BASE_PATH}/rollouts`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'ANALYST',
      'SPECIALIST',
      'USER',
    ]),
    validateQuery({
      name: Joi.string().optional(),
      type: Joi.string().valid(['software', 'media']).optional(),
      pageSize: Joi.number().min(-1).optional(),
      pageIndex: Joi.number().min(0).optional(),
    }),
    rollout.getRollouts,
  ]
);
