/* eslint-disable no-multi-spaces, no-console, key-spacing */

const Joi = require('joi');

const env = require('../env');
const { server } = require('../app');
const authenticationHandler = require('../handlers/authentication');
const { validateBody } = require('../lib/pre');

const authUrl = `${env.config.base}/authenticate`;
const mfaUrl = `${env.config.base}/authenticate/mfa`;

server.post({ path: authUrl, version: '0.0.1' }, [
  validateBody({
    email: Joi.string().email(),
    password: Joi.string().regex(/(.){7,100}/),
    mfaCode: Joi.string().optional(),
  }),
  authenticationHandler.authenticate,
]);

server.post({ path: mfaUrl, version: '0.0.1' }, [
  validateBody({
    email: Joi.string().email(),
    password: Joi.string().regex(/(.){7,100}/),
    mfaSecret: Joi.string().regex(/^[a-zA-Z0-9]{26}$/),
    mfaCode: Joi.string(),
  }),
  authenticationHandler.authAndSetMFA,
]);
