const Joi = require('joi');

const { server } = require('../app');
const env = require('../env');
const { config } = require('../env');
const service = require('../handlers/jobs');
const {
  requiresRole,
  requiresConfig,
  validateBody,
  validatePara<PERSON>,
  validateForm<PERSON>ields,
  requiresFeatureFlag,
  parseFormFields,
  validateFormFiles,
} = require('../lib/pre');

const BASE_PATH = `${env.config.base}/jobs`;

/**
 * GET /jobs return lists of job depends on the filters
 */
server.get(
  {
    path: `${BASE_PATH}`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'ANALYST',
      'USER',
      'SPECIALIST',
    ]),
    validateParams({
      deviceId: Joi.number().integer().allow(null).optional(),
      start: Joi.date().iso().allow(null).optional(),
      end: Joi.date().iso().allow(null).optional(),
      status: Joi.string().allow(null).optional(),
      embargoStart: Joi.date().iso().allow(null).optional(),
      embargoEnd: Joi.date().iso().allow(null).optional(),
      destination: Joi.string().allow(null).optional(),
      siteId: Joi.string().allow(null).optional(),
      types: Joi.string().allow(null).optional(),
    }),
    service.getJobs,
  ]
);

/**
 * GET /jobs/devices/deviceId returns the jobs belong to the device
 */
server.get(
  {
    path: `${BASE_PATH}/devices/:deviceId`,
    version: '0.0.1',
  },
  [
    requiresRole(['DEVICE']),
    validateParams({
      deviceId: Joi.number().integer().min(1),
    }),
    service.getDeviceJobs,
  ]
);

/**
 * GET /jobs/jobId returns the job
 */
server.get(
  {
    path: `${BASE_PATH}/:jobId`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'ANALYST',
      'USER',
      'SPECIALIST',
    ]),
    validateParams({
      jobId: Joi.string().guid(),
    }),
    service.getJob,
  ]
);

/**
 * GET /jobs/jobId/history returns the history of the job
 */
server.get(
  {
    path: `${BASE_PATH}/:jobId/history`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateParams({
      jobId: Joi.string().guid(),
    }),
    service.getJobHistory,
  ]
);

/**
 * POST /jobs/expire - expire jobs
 *
 * return total number of jobs expired
 */
server.post(
  {
    path: `${BASE_PATH}/expire`,
    version: '0.0.1',
  },
  [
    requiresRole(['ICS_SYSTEM']),
    service.expireJobs,
    service.sendEventsToEventBridgeOnBulkJobExpire,
  ]
);

/**
 * POST /jobs/jobId/cancel - cancel the job
 */
server.post(
  {
    path: `${BASE_PATH}/:jobId/cancel`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'ANALYST', 'POWER_USER', 'ICS_SYSTEM']),
    validateParams({
      jobId: Joi.string().guid(),
    }),
    validateBody({
      cancelledBy: Joi.string().allow(null).optional(),
    }),
    service.cancelJob,
    service.sendEventsToEventBridgeOnUpdateJobStatus,
  ]
);

/**
 * POST /jobs/bulkOperationID/bulkcancel
 *
 * Cancels multiple jobs related to a bulk operation
 */
server.post(
  {
    path: `${BASE_PATH}/:bulkOperationId/bulkcancel`,
    version: '0.0.1',
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'ANALYST']),
    requiresFeatureFlag(['REMOTE_MANAGEMENT']),
    validateParams({
      bulkOperationId: Joi.string().guid(),
    }),
    service.cancelBulkJobs,
    service.sendEventsToEventBridgeOnBulkJobCancel,
  ]
);

/**
 * PATCH /jobs/jobId update the job status
 */
server.patch(
  {
    path: `${BASE_PATH}/:jobId`,
    version: '0.0.1',
  },
  [
    requiresConfig(['$.maxAlarmSuspensionPeriodInMinutes'], config),
    requiresRole(['DEVICE']),
    validateParams({
      jobId: Joi.string().guid(),
    }),
    validateBody({
      message: Joi.string().allow('').allow(null).optional(),
      status: Joi.number().integer().min(0).max(5),
      data: Joi.object().allow(null).optional(),
    }),
    service.updateJobStatus,
    service.sendEventsToEventBridgeOnUpdateJobStatus,
  ]
);

/**
 * POST /jobs create job
 */
server.post(
  {
    path: BASE_PATH,
    version: '0.0.1',
  },
  [
    requiresRole([
      'ICS_SYSTEM',
      'POWER_USER',
      'COMPANY_ADMIN',
      'ANALYST',
      'SPECIALIST',
    ]),
    validateBody({
      deviceId: Joi.number().integer().min(0),
      destination: Joi.string(),
      type: Joi.string(),
      data: Joi.string().allow(null).optional(),
      embargo: Joi.date().allow(null).optional(),
      expiration: Joi.date().allow(null).optional(),
      createdBy: Joi.string().allow(null).optional(),
      dependencies: Joi.array()
        .items({
          jobId: Joi.string().guid(),

          runOnFail: Joi.boolean(),
        })
        .allow(null)
        .optional(),
    }),
    service.createJob,
  ]
);

/**
 * POST /jobs/config-management create job
 */
server.post(
  {
    path: `${BASE_PATH}/config-management`,
    version: '0.0.1',
  },
  [
    requiresRole(['CONFIG_MGMT_DEPLOY', 'ICS_SYSTEM']),
    requiresFeatureFlag(['CONFIG_MGMT']),
    parseFormFields(['devices', 'configFileDescriptor', 'createdBy']),
    validateFormFiles({
      fileContent: {
        name: Joi.string().required(),
        size: Joi.number().integer().min(1).max(5242880),
      },
    }),
    validateFormFields({
      devices: Joi.array()
        .min(1)
        .items(
          Joi.object({
            deviceId: Joi.number().integer().min(0),
            createReboot: Joi.boolean().optional(false),
            restartData: Joi.array().min(1).items(Joi.string()).optional(),
          })
        ),
      createdBy: Joi.string().allow(null).optional(),
      configFileDescriptor: {
        filename: Joi.string().required(),
        name: Joi.string().required(),
        'sig-control': Joi.string().allow(null).optional('default'),
        'sig-key': Joi.string().allow(null).optional('default'),
        'sig-type': Joi.string().valid('none', 'rsa-pss').required(),
      },
      jobExpireMins: Joi.number().integer().min(0).required(),
    }),
    service.createConfigManagementJob,
  ]
);

/**
 * POST /jobs/statuses returns job statuses
 *
 * This is logically a GET, but had to make it a post to send a body of GUIDs
 */
server.post(
  {
    path: `${BASE_PATH}/statuses`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'ANALYST',
      'USER',
      'SPECIALIST',
    ]),
    validateBody(Joi.array().items(Joi.string().guid())),
    service.getJobStatuses,
  ]
);

/**
 * POST /jobs/bulkcancel - cancel jobs sent in the body
 */
server.post(
  {
    path: `${BASE_PATH}/bulkcancel`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'CONFIG_MGMT_DEPLOY',
    ]),
    validateBody(Joi.array().items(Joi.string().guid())),
    service.bulkCancelCmJobs,
    service.sendEventsToEventBridgeOnBulkJobCancel,
  ]
);
