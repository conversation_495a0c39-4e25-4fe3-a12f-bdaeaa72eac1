const Joi = require('joi');
const { throttle } = require('restify');
const { server } = require('../app');
const { requiresRole } = require('../lib/pre');
const devices = require('../handlers/device/device');
const devicesBulkReboot = require('../handlers/device/device-bulkreboot');
const devicesBulkmove = require('../handlers/device/device-bulkmove');
const devicesAlarmRules = require('../handlers/device/device-alarmrules');
const devicesStateHistory = require('../handlers/device/device-state-history');
const deviceAuth = require('../handlers/device/device-auth');
const challengeResponse = require('../handlers/challenge-response');
const deviceMedia = require('../handlers/device/device-media');
const deviceVersions = require('../handlers/device/device-versions');
const { config } = require('../env');
const {
  validateParams,
  validateQuery,
  validateBody,
  requiresFeatureFlag,
  requiresConfig,
} = require('../lib/pre');
const env = require('../env');
const {
  validateExistsScheduled,
  checkMaintenanceFrameAndRoles,
  scheduleDeploy,
} = require('../helpers/scheduled-deployment-helper');
const {
  validateDeviceJsonCertificate,
} = require('../helpers/validate-device-certificate-helper');

const statesHistoryThrottle = env.config.statesHistoryThrottle
  ? throttle({
      ...env.config.statesHistoryThrottle,
      username: true,
      ip: false,
      xff: false,
    })
  : throttle({ burst: 10, rate: 0.2, username: true, ip: false, xff: false });

const BASE_PATH = config.base;
const version = '1.0.0';

server.put(
  {
    path: `${BASE_PATH}/devices/alarmrules`,
    version,
  },
  [
    requiresConfig(['$.maxAlarmSuspensionPeriodInMinutes'], config),
    requiresRole(['COMPANY_ADMIN', 'ANALYST']),
    validateBody(
      Joi.array()
        .min(1)
        .items({
          siteId: Joi.string().guid(),
          suspendedFrom: Joi.number().integer().min(0).allow(null),
          suspendedUntil: Joi.number().integer().min(0).allow(null),
          devices: Joi.array()
            .min(1)
            .items(Joi.number().positive().integer().min(0)),
        })
    ),
    devicesAlarmRules.updateDeviceAlarmRules,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/devices`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateQuery({
      statuses: Joi.array()
        .items(
          Joi.string().valid([
            'OPERATIONAL',
            'UNKNOWN',
            'OUT_OF_SERVICE',
            'INACTIVE',
          ])
        )
        .optional(),
      oosFilter: Joi.array().items(Joi.string()).optional(),
      searchFilter: Joi.string().default('').optional(),
      presence: Joi.string()
        .valid(['PRESENT', 'OUT_OF_INSTANCE'])
        .allow(null)
        .optional(),
      companyId: Joi.string().guid().optional(),
      deviceType: Joi.string().optional(),
      pageIndex: Joi.number().default(0).integer().min(0).optional(),
      pageSize: Joi.number().default(100).integer().min(-1).optional(),
      order: Joi.string()
        .default('')
        .valid([
          'serial-asc',
          'serial-desc',
          'name-asc',
          'name-desc',
          'status-asc',
          '',
        ])
        .optional(),
      configs: Joi.array().items(Joi.string()).optional(),
      alarms: Joi.array().items(Joi.string()).min(1).optional(),
      showHiddenDevices: Joi.boolean().optional(),
      showOnlyUnrestrictedDevices: Joi.boolean().optional(),
      isCSV: Joi.boolean().optional(),
    }),
    devices.findDevices,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/devices/check-rki`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'ANALYST', 'SPECIALIST']),
    validateQuery({
      site: Joi.string().optional(),
      keyGroupRef: Joi.string().optional(),
      companyRef: Joi.string().optional(),
      devices: Joi.any(Joi.array(), Joi.string(), Joi.number()).optional(),
      deviceSerials: Joi.any(Joi.array(), Joi.string()).optional(),
    }),
    devices.checkRKI,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/devices/:id`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateParams({
      id: Joi.number().integer().min(1),
    }),
    devices.getDeviceById,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/devices/serialnumber/:serialNumber`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateParams({
      serialNumber: Joi.string().regex(/^[A-Za-z0-9-]{1,128}$/),
    }),
    validateQuery({
      serviceRecipientId: Joi.string().guid().optional(),
    }),
    devices.getDeviceBySerialNumber,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/devices/serialnumber/:serialNumber/certificate`,
    version,
  },
  [
    requiresRole(['ICS_SYSTEM']),
    validateParams({
      serialNumber: Joi.string().regex(/^[A-Za-z0-9-]{1,128}$/),
    }),
    validateBody({
      certificate: Joi.string().required(),
      isJsonCertificates: Joi.boolean().default(false).optional(),
    }),
    validateDeviceJsonCertificate('UpdateDeviceCertificate'),
    devices.updateDeviceCertificate,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/devices`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'ANALYST',
      'SPECIALIST',
      'BRIDGE_APP',
    ]),
    validateBody({
      name: Joi.string().required(),
      description: Joi.string().optional(),
      siteId: Joi.string().required(),
      deviceType: Joi.string().required(),
      serialNumber: Joi.string()
        .regex(/^[A-Za-z0-9-]{1,128}$/)
        .required(),
      macAddress: Joi.string().optional(),
      certificate: Joi.string().optional(),
      isJsonCertificates: Joi.boolean().default(false).optional(),
      data: Joi.string().optional(),
      keyGroupId: Joi.string().optional(),
      keyGroupRef: Joi.string().optional(),
      productTypeId: Joi.number().integer().optional(),
      deploymentType: Joi.string()
        .default('maintenance-window')
        .valid('maintenance-window', 'immediate', 'schedule')
        .optional(),
      scheduledDateTime: Joi.date().iso().optional(),
      meta: Joi.object().default(undefined).optional(),
    }),
    checkMaintenanceFrameAndRoles('AddDevice'),
    validateExistsScheduled('AddDevice'),
    scheduleDeploy('AddDevice'),
    validateDeviceJsonCertificate('AddDevice'),
    devices.createDevice,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/devices/:id`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'ANALYST', 'SPECIALIST']),
    validateParams({
      id: Joi.number().integer().min(1),
    }),
    validateBody({
      siteId: Joi.string().guid().optional(),
      name: Joi.string().min(1).max(128).optional(),
      description: Joi.string().max(1000).allow(null).allow('').optional(),
      deviceType: Joi.string().optional(),
      configUpdate: Joi.object().optional(),
      deploymentType: Joi.string()
        .default('maintenance-window')
        .valid('maintenance-window', 'immediate', 'schedule')
        .optional(),
      scheduledDateTime: Joi.date().iso().optional(),
      serialNumber: Joi.any().when('deploymentType', {
        is: 'schedule',
        then: Joi.string()
          .regex(/^[a-z0-9-]+$/i)
          .required(),
        otherwise: Joi.string()
          .regex(/^[a-z0-9-]+$/i)
          .optional(),
      }),
      meta: Joi.object().default(undefined).optional(),
      terminalLocation: Joi.string().valid('Forecourt', 'Backcourt').optional(),
    }),
    checkMaintenanceFrameAndRoles('MoveDevice'),
    validateExistsScheduled('MoveDevice'),
    scheduleDeploy('MoveDevice'),
    devices.updateDevice,
  ]
);

server.del(
  {
    path: `${BASE_PATH}/devices/:id`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'ANALYST', 'SPECIALIST']),
    validateParams({
      id: Joi.number().integer().min(1),
    }),
    devices.deactivateDevice,
  ]
);

/*

  THIS ENDPOINT IS DEPRECATED FROM api-nodejs AND MOVED TO ics-device REPOSITORY
  https://github.com/Invenco-Cloud-Systems-ICS/ics-device

*/
server.put(
  {
    path: `${BASE_PATH}/devices/:id/data`,
    version,
  },
  [
    requiresConfig(
      [
        '$.AWS.kinesis.region',
        '$.AWS.kinesis.deadLetterQueue',
        '$.AWS.S3.deviceDataBucket',
        '$.AWS.kinesis.dataStream',
      ],
      config
    ),
    requiresRole(['DEVICE']),
    validateParams({
      id: Joi.number().integer().min(0),
    }),
    validateBody(
      Joi.array().items({
        dv: Joi.any().required(),
        ds: Joi.any().required(),
        dn: Joi.any().required(),
        ts: Joi.any().required(),
      })
    ),
    devices.ingestDeviceData,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/devices/:id/self`,
    version,
  },
  [
    requiresRole(['DEVICE']),
    validateParams({
      id: Joi.number().integer().min(0),
    }),
    devices.getSelf,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/sites/devices/summary`,
    version,
  },
  [
    requiresRole([
      'USER',
      'POWER_USER',
      'COMPANY_ADMIN',
      'ANALYST',
      'SPECIALIST',
    ]),
    validateQuery({
      type: Joi.string().optional(),
      presence: Joi.string().valid(['PRESENT', 'OUT_OF_INSTANCE']).optional(),
      q: Joi.string().optional().allow(''),
      tags: Joi.string().optional(),
      siteGroups: Joi.string().optional(),
      promptSetProfile: Joi.string().optional(),
      isFileDownload: Joi.boolean().optional(),
      applyStrictMatchTags: Joi.boolean().optional(),
      excludeRestrictedDeviceTypes: Joi.boolean().optional(),
    }),
    devices.getSiteDevicesSummary,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/sites/:id/devices/alarmrules`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateParams({
      id: Joi.string().guid(),
    }),
    devicesAlarmRules.getDeviceAlarmRulesForSite,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/devices/:id/alarmrules`,
    version,
  },
  [
    requiresRole([
      'USER',
      'POWER_USER',
      'COMPANY_ADMIN',
      'ANALYST',
      'SPECIALIST',
    ]),
    validateParams({
      id: Joi.number().integer().min(1),
    }),
    devicesAlarmRules.getDeviceAlarmRules,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/devices/movetosite`,
    version,
  },
  [
    requiresRole([
      'ICS_SYSTEM',
      'POWER_USER',
      'COMPANY_ADMIN',
      'ANALYST',
      'SPECIALIST',
    ]),
    validateBody({
      siteId: Joi.string().guid(),
      devices: Joi.array()
        .min(1)
        .items(
          Joi.object({
            id: Joi.number().optional(),
            serialNumber: Joi.string().optional(),
          })
        ),
      deploymentType: Joi.string()
        .valid('maintenance-window', 'immediate', 'schedule')
        .required(),
      scheduledDateTime: Joi.date().iso().optional(),
      meta: Joi.object().default(undefined).optional(),
    }),
    checkMaintenanceFrameAndRoles('MoveDevice'),
    validateExistsScheduled('MoveDevice'),
    scheduleDeploy('MoveDevice'),
    devicesBulkmove.moveToSite,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/devices/movetocompany`,
    version,
  },
  [
    requiresRole([
      'ICS_SYSTEM',
      'POWER_USER',
      'COMPANY_ADMIN',
      'ANALYST',
      'SPECIALIST',
    ]),
    validateBody(
      Joi.array()
        .min(1)
        .items(
          Joi.object().keys({
            companyRef: Joi.string(),
            serialNumber: Joi.string(),
          })
        )
    ),
    devicesBulkmove.moveToCompany,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/devices/:id/challenge-response`,
    version,
  },
  [
    requiresConfig(
      [
        '$.tamperChallenge.scriptPath',
        '$.tamperChallenge.key',
        '$.tamperChallenge.user',
        '$.tamperChallenge.slot',
      ],
      config
    ),
    requiresRole(['TAMPER_CLEAR', 'FACTORY_RESET']),
    validateParams({
      id: Joi.number().positive().integer().allow(0),
    }),
    validateBody({
      requestedBy: Joi.string(),
      challenge: Joi.string().regex(/^[0-9]{8}$/),
      serialNumber: Joi.string().regex(/^[a-z0-9-]+$/i),
      mfaCode: Joi.string(),
      operation: Joi.string().valid(['TAMPER_CLEAR', 'FACTORY_RESET']),
    }),
    challengeResponse.requestChallengeResponse,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/devices/backup`,
    version,
  },
  [requiresRole(['ICS_SYSTEM']), devices.backupConfig]
);

server.post(
  {
    path: `${BASE_PATH}/devices/:id/swap`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'ANALYST', 'SPECIALIST']),
    requiresFeatureFlag(['DEVICES_SWAP_OUT']),
    validateBody({
      oldDeviceName: Joi.string(),
      newDevice: Joi.object().keys({
        id: Joi.number(),
        name: Joi.string(),
      }),
    }),
    devices.swap,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/devices/:id/rollouts/:rolloutId`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'ANALYST',
      'SPECIALIST',
      'USER',
    ]),
    devices.getRolloutsById,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/devices/:id/reboot`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'ANALYST']),
    validateParams({
      id: Joi.number().positive().integer(),
    }),
    devices.rebootDevice,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/devices/reboot`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'ANALYST']),
    requiresFeatureFlag(['REMOTE_MANAGEMENT']),
    validateBody({
      dryRun: Joi.boolean(),
      devices: Joi.array().optional().items(
        Joi.string() // device serial nums
      ),
      sites: Joi.array().optional().items(
        Joi.string().guid() // site ids
      ),
      tags: Joi.array().optional().items(
        Joi.number().positive().integer() // tag ids
      ),
      siteGroups: Joi.array().optional().items(
        Joi.string().guid() // site group ids
      ),
      schedule: Joi.date().timestamp('unix').optional(), // datetime in epoch/unix time format
    }),
    devicesBulkReboot.rebootDevices,
  ]
);

server.put(
  {
    path: `${BASE_PATH}/devices/:id/recommission`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'SPECIALIST', 'ANALYST']),
    validateParams({
      id: Joi.number().positive().integer(),
    }),
    deviceAuth.recommissionDevice,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/devices/recommission`,
    version,
  },
  [
    requiresRole(['COMPANY_ADMIN', 'POWER_USER', 'ANALYST']),
    requiresFeatureFlag(['REMOTE_MANAGEMENT']),
    validateBody({
      dryRun: Joi.boolean(),
      devices: Joi.array().optional().items(
        Joi.string() // device serial nums
      ),
      sites: Joi.array().optional().items(
        Joi.string().guid() // site ids
      ),
      tags: Joi.array().optional().items(
        Joi.number().positive().integer() // tag ids
      ),
      siteGroups: Joi.array().optional().items(
        Joi.string().guid() // site group ids
      ),
    }),
    deviceAuth.recommissionDevices,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/devices/register`,
    version,
  },
  [deviceAuth.registerDevice]
);

server.post(
  {
    path: `${BASE_PATH}/devices/authenticate`,
    version,
  },
  [
    validateBody({
      deviceId: Joi.number().integer().min(0),
      secretKey: Joi.string().required(),
    }),
    deviceAuth.authenticate,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/devices/bulkoperations/summary`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'SPECIALIST',
      'ANALYST',
      'USER',
    ]),
    requiresFeatureFlag(['REMOTE_MANAGEMENT']),
    validateParams({
      pageIndex: Joi.number().min(0).optional(),
      pageSize: Joi.number().min(-1).optional(),
    }),
    devices.getBulkOperationsSummary,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/devices/bulkoperations/:bulkoperationid/history`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'POWER_USER',
      'SPECIALIST',
      'ANALYST',
      'USER',
    ]),
    requiresFeatureFlag(['REMOTE_MANAGEMENT']),
    validateParams({
      bulkoperationid: Joi.string().guid(),
    }),
    devices.getBulkOperationsHistory,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/devices/:id/media/stats`,
    version,
  },
  [
    requiresConfig(['$.AWS.DDB.deviceDataTable', '$.AWS.DDB.region'], config),
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'USER',
      'POWER_USER',
      'SPECIALIST',
      'PLAYLIST_PROVIDER',
    ]),
    requiresFeatureFlag(['GSTV', 'PLAYLIST']),
    validateParams({
      id: Joi.number().integer().min(1),
    }),
    deviceMedia.getDeviceMediaStats,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/devices/:id/versions`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'USER',
      'POWER_USER',
      'SPECIALIST',
    ]),
    validateParams({
      id: Joi.number().integer().min(1),
    }),
    deviceVersions.getDeviceVersions,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/devices/:id/states/history`,
    version,
  },
  [
    requiresConfig(['$.AWS.DDB.deviceDataTable', '$.AWS.DDB.region'], config),
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'USER',
      'POWER_USER',
      'SPECIALIST',
    ]),
    validateParams({
      id: Joi.number().integer().min(1),
    }),
    validateQuery({
      start: Joi.date().optional(),
      end: Joi.date().min(Joi.ref('start')).optional(),
      levels: Joi.array()
        .min(1)
        .items(
          Joi.string().valid([
            'trace',
            'debug',
            'info',
            'notice',
            'warn',
            'error',
            'critical',
            'fatal',
          ])
        )
        .optional(),
      q: Joi.string().optional(),
      advanced: Joi.boolean().default(false).optional(),
      pageKey: Joi.object().optional(),
      pageMinSize: Joi.number().min(1).max(1000).optional(),
    }),
    statesHistoryThrottle,
    devicesStateHistory.getDeviceStateHistory,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/device-available-memory`,
    version,
  },
  [
    validateQuery({
      deviceType: Joi.string().optional(),
    }),
    devices.getDeviceAvailableMemory,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/devices/sync-keygroup-to-site`,
    version,
  },
  [
    requiresRole(['RKI']),
    validateBody({
      siteId: Joi.string().guid(),
      devices: Joi.array()
        .min(1)
        .items(
          Joi.object({
            id: Joi.number().integer().min(0),
            keyGroupId: Joi.string().guid().allow(null),
          })
        ),
    }),
    devices.syncKeygroupToSite,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/devices/health/update`,
    version: '0.0.1',
  },
  [requiresRole(['ICS_SYSTEM']), devices.updateDevicesHealth]
);

server.post(
  {
    path: `${BASE_PATH}/devices/filters`,
    version,
  },
  [
    requiresRole([
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
    ]),
    validateBody({
      serialNos: Joi.array()
        .items(
          Joi.string()
            .trim()
            .regex(/^[a-zA-Z0-9_-]*$/)
        )
        .optional(),
      deviceNames: Joi.array()
        .items(Joi.string().regex(/^[a-zA-Z0-9_-]*$/))
        .optional(),
      deviceTypes: Joi.array()
        .items(
          Joi.object({
            type: Joi.string()
              .trim()
              .regex(/^[a-zA-Z0-9_-]+$/) // Validation for the type (device type)
              .required(), // type is required
            screenSize: Joi.number() // screen_Size can be a number or null
              .integer()
              .allow(null)
              .optional(), // screen_Size is optional and can be null
          })
        )
        .optional(),
      releases: Joi.array()
        .items(
          Joi.string().regex(
            /^R(\d+(\.\d+){0,2})?$|^\d+(\.\d+){0,2}$|^\[R\d+(\.\d+){0,2}\]$|^R$/
          )
        )
        .optional(),
      operator: Joi.any().valid(['=', '>', '<', '>=', '<=']).optional(),
      status: Joi.array()
        .items(
          Joi.string().valid([
            'OPERATIONAL',
            'UNKNOWN',
            'OUT_OF_SERVICE',
            'INACTIVE',
          ])
        )
        .optional(),
      location: Joi.array()
        .items(
          Joi.string()
            .trim()
            // eslint-disable-next-line no-useless-escape
            .regex(/^[A-Za-z0-9-&$#._,\/ ]{3,200}$/)
        )
        .optional(),
    }),
    devices.filterDevice,
  ]
);
