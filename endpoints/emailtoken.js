// Endpoints for the user service. Maps requests to the relevant function

const Joi = require('joi');

const { server } = require('../app');
const { validateBody } = require('../lib/pre');
const emailTokenService = require('../handlers/emailtoken');
const env = require('../env');

// Set the endpoints
const BASE_PATH = `${env.config.base}/emailtoken`;

// Create the apis with the correct endpoints, calling the relevant service
server.get(
  {
    path: `${BASE_PATH}/register`,
    name: 'getRegister',
    version: '0.0.1',
  },
  [emailTokenService.getRegister]
);

server.post(
  {
    path: `${BASE_PATH}/register`,
    name: 'postRegister',
    version: '0.0.1',
  },
  [
    validateBody({
      token: Joi.string(),
      password: Joi.string(),
      mfaSecret: Joi.string(),
      mfaCode: Joi.string(),
    }),
    emailTokenService.postRegister,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/forgotpassword`,
    name: 'postForgotPassword',
    version: '0.0.1',
  },
  [
    validateBody({
      token: Joi.string(),
      password: Joi.string(),
      mfaCode: Joi.string(),
    }),
    emailTokenService.forgotPassword,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/passwordValidity`,
    name: 'postPasswordValidity',
    version: '0.0.1',
  },
  [
    validateBody({
      token: Joi.string(),
      password: Joi.string(),
    }),
    emailTokenService.passwordValidity,
  ]
);

server.post(
  {
    path: `${BASE_PATH}/verifyemail`,
    name: 'postVerifyEmail',
    version: '0.0.1',
  },
  [
    validateBody({
      token: Joi.string(),
    }),
    emailTokenService.verifyEmail,
  ]
);

server.get(
  {
    path: `${BASE_PATH}/exists`,
    name: 'getEmailTokenExists',
    version: '0.0.1',
  },
  emailTokenService.exists
);
