const Joi = require('joi');
const { server } = require('../app');
const { requiresRole, validateQuery } = require('../lib/pre');
const env = require('../env');
const service = require('../handlers/device/device-types');

const BASE_PATH = env.config.base;

server.get(
  {
    path: `${BASE_PATH}/devicetypes`,
    version: '0.0.1',
  },
  [
    requiresRole([
      'USER',
      'SPECIALIST',
      'POWER_USER',
      'ANALYST',
      'COMPANY_ADMIN',
      'RKI',
    ]),
    validateQuery({
      serviceRecipientId: Joi.string().guid().optional(),
    }),
    service.getDeviceTypes,
  ]
);
