const { server } = require('../app');

server.get(
  {
    path: '/health',
    version: '0.0.1',
  },
  [
    (req, res, next) => {
      const [start, startNano] = process.hrtime();

      setTimeout(() => {
        const [end, endNano] = process.hrtime([start, startNano]);
        const resp = {
          tick: `${end > 0 ? `${end}s ` : ''}${endNano / 1000000}ms`,
          version: process.env.npm_package_version || '0.0.1',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
        };
        res.send(200, resp);

        return next();
      }, 0).unref();
    },
  ]
);
