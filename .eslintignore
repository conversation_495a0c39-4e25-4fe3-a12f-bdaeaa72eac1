# files to ignore
.git-blame-ignore-revs
.gitignore
.gitmodules
.prettierignore
.eslintignore
.eslintrc.js
.prettierrc.js
.nvmrc
.npmrc
package.json
package-lock.json
.ebextensions
polaris.yml
src/media-mgmt/promptstates/promptstate.spec.ts
/docs/nodejs-api.yml
/handlers/device/device-data-ingestion-rules.json
/handlers/device/device-state.config.json
/helpers/device-types.json
/tests/integration/resources/tokens/bp/roles/FUEL_PRICE_MGMT_VIEW.json
Dockerfile

# folders to ignore
coverage/*
dist/*
/build
.idea/*

# extensions to be excluded
*.sh
*.log
*.pem
*.pub
*.sql
*.xml
*.bin
*.bat
*.pdf
*.opts
*.properties 
*.map
*.zsh
*.ejs
*.md
*.json
*.html
*.webm
*.jpg
*.bmp
*.gif
*.png
*.ext
*.ttf
*.otf
*.jbs