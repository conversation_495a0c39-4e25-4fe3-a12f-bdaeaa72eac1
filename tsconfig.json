{
  "compilerOptions": {
    "target": "es2022" /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */,
    "module": "commonjs" /* Specify what module code is generated. */,
    "resolveJsonModule": true /* Enable importing .json files. */,
    "allowJs": true /* Allow JavaScript files to be a part of your program. Use the 'checkJS' option to get errors from these files. */,
    "checkJs": false /* Enable error reporting in type-checked JavaScript files. */,
    "declaration": false /* Generate .d.ts files from TypeScript and JavaScript files in your project. */,
    "sourceMap": true /* Create source map files for emitted JavaScript files. */,
    "outDir": "./dist" /* Specify an output folder for all emitted files. */,
    "esModuleInterop": true /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */,
    "forceConsistentCasingInFileNames": true /* Ensure that casing is correct in imports. */,
    "strict": true /* Enable all strict type-checking options. */,
    "alwaysStrict": false,
    "skipLibCheck": true /* Skip type checking all .d.ts files. */
  },
  "include": ["**/*.*", "**/*.json"],
  "exclude": [
    "node_modules",
    "**/*.spec.js",
    "build",
    "coverage",
    "temp-build-dir",
    "dist"
  ]
}
