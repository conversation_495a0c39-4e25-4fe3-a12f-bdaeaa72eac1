const errors = require('restify-errors');
const co = require('co');
const _ = require('lodash');

const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');
const deviceHelper = require('../helpers/device-helper');

const deviceRules = [1, 2, 3, 4, 5, 6, 7, 8, 10, 13, 16, 18];
const systemRules = [11, 12];

function getAlarm(id, companyId) {
  const params = [id];
  if (companyId) {
    params.push(companyId);
  }
  return server.db.read.row(
    `
                SELECT a.name, a.id, a.active,
                  json_agg( DISTINCT ar.rule_id ) AS rules,
                  COALESCE( json_agg( DISTINCT jsonb_build_object( 'type', an.type, 'address', an.address, 'userId', an.user_id ) ) FILTER (WHERE an.type IS NOT NULL), '[]' ) AS notifications,
                  COALESCE ( json_agg( DISTINCT jsonb_build_object( 'type', asb.type, 'id', asb.subject_id ) ) FILTER (WHERE asb.id IS NOT NULL), '[]') AS subjects
                FROM ics_alarm.alarm a
                    LEFT JOIN ics_alarm.alarm_notification an ON a.id = an.alarm_id
                    LEFT JOIN ics_alarm.alarm_rule ar ON a.id = ar.alarm_id
                    LEFT JOIN ics_alarm.alarm_subject asb ON a.id = asb.alarm_id
                WHERE ${companyId ? ' a.company_id = $2 AND ' : ''}
                a.id = $1 AND a.deleted = false
                GROUP BY a.name, a.id, a.active;
                `,
    params
  );
}

function calculateItemsToAddAndRemove(
  existingArray,
  newArray,
  itemsToRemove,
  itemsToAdd,
  itemsEqual
) {
  const newItems = [];
  newItems.push(...newArray);
  for (
    let existingItemsIndex = 0;
    existingItemsIndex < existingArray.length;
    existingItemsIndex++
  ) {
    let found = false;
    for (
      let newItemsIndex = 0;
      newItemsIndex < newItems.length;
      newItemsIndex++
    ) {
      if (
        itemsEqual(existingArray[existingItemsIndex], newItems[newItemsIndex])
      ) {
        newItems.splice(newItemsIndex, 1);
        found = true;
        break;
      }
    }
    if (!found) {
      itemsToRemove.push(existingArray[existingItemsIndex]);
    }
  }
  itemsToAdd.push(...newItems);
}

function checkSubjectsValid(alarm, user) {
  return co(function* execute() {
    let valid = true;

    if (alarm.rules.filter(rule => deviceRules.includes(rule)).length > 0) {
      if (!alarm.subjects || alarm.subjects.length === 0) {
        valid = false;
      } else {
        const siteSubjects = alarm.subjects.filter(
          subject => subject.type === 'S'
        );
        const siteIds = siteSubjects.map(subject => subject.id);
        const siteResult = yield server.db.read.rows(
          `
                SELECT count(*) FROM user_site_authorization
                WHERE user_id = $1
                AND site_id = ANY ( $2 )
                `,
          [user.sub, siteIds]
        );
        if (
          siteResult &&
          siteResult[0] &&
          siteResult[0].count < siteIds.length
        ) {
          valid = false;
        }

        const tagSubjects = alarm.subjects.filter(
          subject => subject.type === 'T'
        );
        const tagIds = tagSubjects.map(subject => subject.id);

        const tagResult = yield server.db.read.rows(
          `
                SELECT count(*) FROM tag
                WHERE company_id = $1
                AND id = ANY ( $2 )
                `,
          [user.company.id, tagIds]
        );
        if (tagResult && tagResult[0] && tagResult[0].count < tagIds.length) {
          valid = false;
        }
      }
    }
    return valid;
  });
}

function checkNotificationsValid(alarm, userCompanyId) {
  return co(function* execute() {
    const userIds = [];
    for (let index = 0; index < alarm.notifications.length; index++) {
      if (
        (alarm.notifications[index].type === 'E' &&
          !alarm.notifications[index].address) ||
        (alarm.notifications[index].type === 'U' &&
          !alarm.notifications[index].userId)
      ) {
        return false;
      }
      if (alarm.notifications[index].userId) {
        userIds.push(alarm.notifications[index].userId);
      }
    }

    if (userIds.length > 0) {
      const result = yield server.db.read.rows(
        `
                    SELECT count(*) FROM ics_user
                    WHERE company_id = $1
                    AND id = ANY ( $2 )
                    `,
        [userCompanyId, userIds]
      );
      if (result && result[0] && result[0].count < userIds.length) {
        return false;
      }
    }
    return true;
  });
}

function* checkRulesValid(alarm) {
  let hasSystemRules = false;
  let hasDeviceRules = false;

  alarm.rules.forEach(rule => {
    if (deviceRules.includes(rule)) {
      hasDeviceRules = true;
    }
    if (systemRules.includes(rule)) {
      hasSystemRules = true;
    }
  });
  if (hasSystemRules && hasDeviceRules) {
    return false;
  }
  if (alarm.rules && alarm.rules.length > 0) {
    const result = yield server.db.read.rows(
      `
                SELECT count(*) FROM ics_alarm.rule
                WHERE id = ANY ( $1 )
                `,
      [alarm.rules]
    );
    if (result && result[0] && result[0].count < alarm.rules.length) {
      return false;
    }
  }
  return true;
}

module.exports = {
  deviceRules,
  systemRules,
  findAll(req, res, next) {
    return co(function* execute() {
      const companyId = req.user.company.id;
      const alarms = yield server.db.read.rows(
        `
                SELECT a.name, a.id, a.active,
                  json_agg( DISTINCT ar.rule_id ) AS rules,
                  COALESCE( json_agg( DISTINCT jsonb_build_object( 'type', an.type, 'address', an.address, 'userId', an.user_id ) ) FILTER (WHERE an.type IS NOT NULL), '[]' ) AS notifications,
                  COALESCE ( json_agg( DISTINCT jsonb_build_object( 'type', asb.type, 'id', asb.subject_id ) ) FILTER (WHERE asb.id IS NOT NULL), '[]') AS subjects
                FROM ics_alarm.alarm a
                    LEFT JOIN ics_alarm.alarm_notification an ON a.id = an.alarm_id
                    LEFT JOIN ics_alarm.alarm_rule ar ON a.id = ar.alarm_id
                    LEFT JOIN ics_alarm.alarm_subject asb ON a.id = asb.alarm_id
                WHERE a.company_id = $1
                AND a.deleted = false
                GROUP BY a.name, a.id;
                `,
        [companyId]
      );
      res.send(200, alarms);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  findById(req, res, next) {
    return co(function* execute() {
      const companyId = req.user.company ? req.user.company.id : undefined;
      const { id } = req.params;
      const alarm = yield getAlarm(id, companyId);

      if (!alarm) {
        req.log.warn(`Cannot find alarm with id ${id}`);
        return next(
          new errors.NotFoundError(`Cannot find alarm with id ${id}`)
        );
      }
      res.send(200, alarm);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  update: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;
      const { id } = req.params;

      const payload = req.body;

      if (!(yield checkRulesValid(payload))) {
        return next(
          new errors.BadRequestError(
            'At least 1 rule is not valid or does not exist'
          )
        );
      }
      if (!(yield checkSubjectsValid(payload, req.user))) {
        return next(
          new errors.BadRequestError(
            'At least 1 subject has to be specified for device alarms'
          )
        );
      }
      if (!(yield checkNotificationsValid(payload, companyId))) {
        return next(
          new errors.BadRequestError(
            'At least 1 notification is not valid ( email notifications need "address" property, UI notifications need userId property )'
          )
        );
      }

      const alarm = yield getAlarm(id, companyId);

      if (!alarm) {
        req.log.warn(`Cannot find alarm with id ${id}`);
        return next(
          new errors.NotFoundError(`Cannot find alarm with id ${id}`)
        );
      }

      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');
        if (payload.active !== alarm.active || payload.name !== alarm.name) {
          yield connection.execute(
            `
                        UPDATE ics_alarm.alarm SET active = $1, name = $2 WHERE id = $3
                    `,
            [payload.active, payload.name, id]
          );
        }

        const rulesToRemove = [];
        const rulesToAdd = [];
        calculateItemsToAddAndRemove(
          alarm.rules,
          payload.rules,
          rulesToRemove,
          rulesToAdd,
          (a, b) => a === b
        );

        if (rulesToRemove.length > 0) {
          yield connection.execute(
            'DELETE FROM ics_alarm.alarm_rule WHERE alarm_id = $1 AND rule_id = ANY($2::int[])',
            [id, rulesToRemove]
          );
        }
        if (rulesToAdd.length > 0) {
          let insertQuery =
            'INSERT INTO ics_alarm.alarm_rule ( alarm_id, rule_id ) VALUES ';
          for (let index = 0; index < rulesToAdd.length; index++) {
            insertQuery += `($1, $${index + 2}),`;
          }
          insertQuery = insertQuery.substr(0, insertQuery.length - 1);
          yield connection.execute(insertQuery, [id, ...rulesToAdd]);
        }

        const notificationsToRemove = [];
        const notificationsToAdd = [];
        calculateItemsToAddAndRemove(
          alarm.notifications,
          payload.notifications,
          notificationsToRemove,
          notificationsToAdd,
          (a, b) => {
            if (a.type === b.type) {
              if (a.type === 'E') {
                if (a.address === b.address) {
                  return true;
                }
                return false;
              }
              if (a.type === 'U') {
                if (a.userId === b.userId) {
                  return true;
                }
              }
            }
            return false;
          }
        );

        if (notificationsToRemove.length > 0) {
          const emailsToRemove = notificationsToRemove
            .filter(item => item.address !== null)
            .map(item => item.address);
          const usersToRemove = notificationsToRemove
            .filter(item => item.userId !== null)
            .map(item => item.userId);
          yield connection.execute(
            `
                     DELETE FROM ics_alarm.alarm_notification WHERE alarm_id = $1 AND ( address = ANY($2::text[]) OR user_id = ANY($3::text[]) )`,
            [id, emailsToRemove, usersToRemove]
          );
        }
        if (notificationsToAdd.length > 0) {
          let insertQuery =
            'INSERT INTO ics_alarm.alarm_notification ( alarm_id, type, address, user_id ) VALUES ';
          const params = [];
          for (let index = 0; index < notificationsToAdd.length; index++) {
            if (notificationsToAdd[index].type === 'E') {
              insertQuery += `($1, 'E', $${index + 2}, null),`;
              params.push(notificationsToAdd[index].address);
            }
            if (notificationsToAdd[index].type === 'U') {
              insertQuery += `($1, 'U', null, $${index + 2}),`;
              params.push(notificationsToAdd[index].userId);
            }
          }
          insertQuery = insertQuery.substr(0, insertQuery.length - 1);
          yield connection.execute(insertQuery, [id, ...params]);
        }

        const subjectsToRemove = [];
        const subjectsToAdd = [];
        calculateItemsToAddAndRemove(
          alarm.subjects,
          payload.subjects,
          subjectsToRemove,
          subjectsToAdd,
          (a, b) => {
            if (a.type === b.type && a.id === b.id) {
              return true;
            }
            return false;
          }
        );
        if (subjectsToRemove.length > 0) {
          const subjectIdsToRemove = subjectsToRemove.map(item => item.id);
          yield connection.execute(
            `
                     DELETE FROM ics_alarm.alarm_subject WHERE alarm_id = $1 AND subject_id = ANY($2)`,
            [id, subjectIdsToRemove]
          );
        }
        if (subjectsToAdd.length > 0) {
          let insertQuery =
            'INSERT INTO ics_alarm.alarm_subject ( alarm_id, type, subject_id ) VALUES ';
          const params = [];
          let idx = 2;
          for (let index = 0; index < subjectsToAdd.length; index++) {
            insertQuery += `($1, $${idx}, $${idx + 1}),`;
            params.push(subjectsToAdd[index].type);
            params.push(subjectsToAdd[index].id);
            idx += 2;
          }
          insertQuery = insertQuery.substr(0, insertQuery.length - 1);
          yield connection.execute(insertQuery, [id, ...params]);
        }
        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        throw err;
      }
      payload.id = id;
      res.send(200, payload);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  delete: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;
      const { id } = req.params;
      const alarm = yield getAlarm(id, companyId);

      if (!alarm) {
        req.log.warn(`Cannot find alarm with id ${id}`);
        return next(
          new errors.NotFoundError(`Cannot find alarm with id ${id}`)
        );
      }

      try {
        yield server.db.write.execute(
          'UPDATE ics_alarm.alarm SET deleted = true WHERE id = $1',
          [id]
        );
      } catch (err) {
        req.log.error({ err });
      }
      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  create: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;
      let id;
      let active;
      const notifications = [];
      const subjects = [];
      const payload = req.body;

      if (!(yield checkRulesValid(payload))) {
        return next(
          new errors.BadRequestError(
            'At least 1 rule is not valid or does not exist'
          )
        );
      }
      if (!(yield checkSubjectsValid(payload, req.user))) {
        return next(
          new errors.BadRequestError(
            'At least 1 subject has to be specified for device alarms'
          )
        );
      }
      if (!(yield checkNotificationsValid(payload, companyId))) {
        return next(
          new errors.BadRequestError(
            'At least 1 notification is not valid ( email notifications need "address" property, UI notifications need userId property )'
          )
        );
      }

      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');
        let insertProperties = ' name, company_id, deleted ';
        let insertValues = ' $1, $2, false ';
        const insertParams = [payload.name, companyId];
        // eslint-disable-next-line no-prototype-builtins
        if (payload.hasOwnProperty('active')) {
          insertProperties += ', active ';
          insertValues += ', $3';
          insertParams.push(payload.active);
        }

        const insertResult = yield connection.execute(
          `
                    INSERT INTO ics_alarm.alarm ( ${insertProperties} ) VALUES ( ${insertValues} ) RETURNING id, active
                `,
          insertParams
        );
        if (
          !insertResult ||
          !insertResult.rows ||
          !insertResult.rows.length ||
          insertResult.rows.length !== 1
        ) {
          req.log.warn(
            `Cannot creare alarm with payload ${JSON.stringify(payload)}`
          );
          return next(
            new errors.InternalServerError(
              `Cannot insert alarm with payload ${JSON.stringify(
                payload
              )} to DB`
            )
          );
        }
        id = insertResult.rows[0].id;
        active = insertResult.rows[0].active;

        const rulesToAdd = payload.rules;

        if (rulesToAdd.length > 0) {
          let insertQuery =
            'INSERT INTO ics_alarm.alarm_rule ( alarm_id, rule_id ) VALUES ';
          for (let index = 0; index < rulesToAdd.length; index++) {
            insertQuery += `($1, $${index + 2}),`;
          }
          insertQuery = insertQuery.substr(0, insertQuery.length - 1);
          yield connection.execute(insertQuery, [id, ...rulesToAdd]);
        }

        const notificationsToAdd = payload.notifications;

        if (notificationsToAdd.length > 0) {
          let insertQuery =
            'INSERT INTO ics_alarm.alarm_notification ( alarm_id, type, address, user_id ) VALUES ';
          const params = [];
          for (let index = 0; index < notificationsToAdd.length; index++) {
            if (notificationsToAdd[index].type === 'E') {
              insertQuery += `($1, 'E', $${index + 2}, null),`;
              params.push(notificationsToAdd[index].address);
              notifications.push({
                type: 'E',
                address: notificationsToAdd[index].address,
              });
            }
            if (notificationsToAdd[index].type === 'U') {
              insertQuery += `($1, 'U', null, $${index + 2}),`;
              params.push(notificationsToAdd[index].userId);
              notifications.push({
                type: 'U',
                userId: notificationsToAdd[index].userId,
              });
            }
          }
          insertQuery = insertQuery.substr(0, insertQuery.length - 1);
          yield connection.execute(insertQuery, [id, ...params]);
        }

        const subjectsToAdd = payload.subjects;
        if (subjectsToAdd.length > 0) {
          let insertQuery =
            'INSERT INTO ics_alarm.alarm_subject ( alarm_id, type, subject_id ) VALUES ';
          const params = [];
          let idx = 2;
          for (let index = 0; index < subjectsToAdd.length; index++) {
            insertQuery += `($1, $${idx}, $${idx + 1}),`;
            params.push(subjectsToAdd[index].type);
            params.push(subjectsToAdd[index].id);
            subjects.push({
              id: subjectsToAdd[index].id,
              type: subjectsToAdd[index].type,
            });
            idx += 2;
          }
          insertQuery = insertQuery.substr(0, insertQuery.length - 1);
          yield connection.execute(insertQuery, [id, ...params]);
        }
        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        throw err;
      }

      const response = {
        id,
        active,
        name: payload.name,
        notifications,
        rules: payload.rules,
        subjects,
      };

      res.send(200, response);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getCountPerType: async (req, res, next) => {
    const userId = req.user.sub;

    try {
      const sql = `
                SELECT COUNT(a.alarm_type), r.code
                FROM ics_alarm."rule" r
                LEFT JOIN (
                    SELECT DISTINCT s.site_id, states.device_id,
                        CASE
                            WHEN states.code LIKE '%removal_tamper%'
                                THEN 'removal_device_tampered'
                            WHEN states.code LIKE '%destructive_tamper%'
                                THEN 'destructive_device_tampered'
                            WHEN states.code LIKE 'version_downgrade%'
                                THEN 'version_downgrade'
                            ELSE states.code
                        END as "alarm_type"
                    FROM (
                        SELECT sa.code, sa.site_id,
                            NULL AS device_id
                        FROM site_alarms sa
                        WHERE
                            sa.status
                        UNION
                        SELECT da.code, da.site_id,
                            da.device_id
                        FROM device_alarms da
                        JOIN target t ON
                            t.target_id = da.device_id
                            AND t.site_id = da.site_id
                        WHERE
                            da.status
                        AND t.active
                        AND t.delete_timestamp IS NULL
                    ) states
                    JOIN public.site s ON states.site_id = s.site_id
                    JOIN public.user_site_authorization usa ON usa.site_id = states.site_id
                    WHERE s.active AND s.visible = true
                    AND usa.user_id = $1
                ) a
                ON r.code = a.alarm_type
                WHERE 
                    r.type IN ('DEVICE','SITE')                
                AND r.code NOT IN ('excessive_bad_card_reads') -- to disable excessive_bad_card_reads (special case)
                GROUP BY a.alarm_type, r.code
            `;

      const result = await server.db.read.rows(sql, [userId]);
      res.send(200, result);

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getRules: async (req, res, next) => {
    try {
      const sql = `
                SELECT
                    r.id,
                    r.name,
                    r.description,
                    r.code,
                    r.type
                FROM ics_alarm."rule" r
                WHERE r.code != 'excessive_bad_card_reads'
                ORDER BY r.name ASC
            `;

      const result = await server.db.read.rows(sql);
      res.send(200, result);

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getCompanyRules: async (req, res, next) => {
    const FORECOURT_ALARM = 'forecourt_alarm';
    const companyId = req.user.company.id;
    let alarmTypes = ['SYSTEM'];
    const applicationTypes = ['FUELPOS', 'E1-100-EPS', 'IPT', 'vOPT'];
    try {
      const companyAccess = await server.db.read.rows(`
                SELECT device_type
                FROM company_product_type
                WHERE company = '${companyId}'
            `);
      const deviceAccess = _.find(
        companyAccess,
        access =>
          deviceHelper.isG6100orG6200(access.deviceType) ||
          deviceHelper.isG7100orG6300(access.deviceType)
      );
      const applicationAccess = _.find(
        companyAccess,
        access =>
          applicationTypes.includes(access.deviceType) ||
          deviceHelper.isG7100orG6300(access.deviceType)
      );
      if (deviceAccess) alarmTypes = alarmTypes.concat(['DEVICE', 'SITE']);
      if (applicationAccess) alarmTypes = alarmTypes.concat(['APP']);
      const forecourtAccess = _.find(
        companyAccess,
        access =>
          access.deviceType.startsWith('C1') ||
          deviceHelper.isG7100orG6300(access.deviceType)
      );

      let result = await server.db.read.rows(
        `
                SELECT
                    r.id,
                    r.name,
                    r.description,
                    r.code,
                    r.type,
                    r.show_description
                FROM ics_alarm."rule" r
                WHERE r.type = ANY($1)
                AND r.code != 'excessive_bad_card_reads'
                AND r.active
                ORDER BY r.name ASC
            `,
        [alarmTypes]
      );

      if (!forecourtAccess) {
        result = _.filter(result, item => item.code !== FORECOURT_ALARM);
      }

      res.send(200, result);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
