const _ = require('lodash');

const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');
const paginationHelper = require('../helpers/pagination-helper');

module.exports = {
  getRollouts: async (req, res, next) => {
    const userId = req.user.sub;
    const companyId = req.user.company.id;

    const params = [userId, companyId];

    let name;
    if (req.query.name) {
      params.push(`%${req.query.name.toLowerCase()}%`);
      name = `AND LOWER(r.name) LIKE $${params.length}`;
    }

    let type;
    if (req.query.type) {
      params.push(req.query.type);
      type = `AND r.type = $${params.length}`;
    }

    const pageParams = paginationHelper.parsePaginationParams(req);

    try {
      const joinBasicInfo = `
                JOIN ics_user uc ON r.created_by = uc.id
                JOIN target_release tr ON r.release_id = tr.release_id
                JOIN target t ON tr.target_id = t.target_id
                JOIN user_site_authorization usa ON t.site_id = usa.site_id
            `;

      const joinReleaseInfo = `
                JOIN ics_user uc ON r.created_by = uc.id
                JOIN company c ON uc.company_id = c.id
                JOIN software sw ON r.software_id = sw.software_id
                LEFT JOIN deploy_policy dp ON r.deploy_policy_id = dp.id
                LEFT JOIN ics_user ca ON r.cancelled_by = ca.id
                LEFT JOIN company cac ON ca.company_id = cac.id
                LEFT JOIN prompt_set ps ON ps.id = (sw.related_entity)::UUID
            `;

      const where = `
                r.deleted IS NOT TRUE AND
                usa.user_id = $1 AND
                (
                    uc.company_id = $2 OR
                    uc.company_id IN (
                        SELECT cr.company_id
                        FROM company_relationship cr
                        WHERE
                            cr.allowed_company_id = $2
                    )
                )
                ${type || ''}
                ${name || ''}
            `;

      const resultReleases = await server.db.read.rows(
        `
                SELECT id, count (*) OVER()
                FROM (
                    SELECT distinct(r.release_id ) AS id, r.schedule_start_time
                    FROM release r
                    ${joinBasicInfo}
                    WHERE ${where}
                    ORDER BY
                      r.schedule_start_time DESC
                ) AS tmp
                LIMIT ${pageParams.pageSize}
                OFFSET ${pageParams.offset};
            `,
        params
      );

      let releaseIds = [];
      let totalCount = 0;

      if (resultReleases.length) {
        releaseIds = _.map(resultReleases, release => release.id);
        totalCount = resultReleases[0].count;
      }

      const [resultData, resultInfo] = await Promise.all([
        server.db.read.rows(
          `
                    WITH releases AS (
                        SELECT r.release_id                                     AS id,
                            r.name,
                            r.description,
                            r.cancelled,
                            r.deleted,
                            r.type,
                            r.retry,
                            r.install_window,
                            r.install_frequency,
                            array_agg(DISTINCT tr.download_job)                       AS download_jobs,
                            array_agg(DISTINCT tr.install_job)                        AS install_jobs,
                            array_agg(DISTINCT tr.target_release_id)                  AS target_releases,
                            EXTRACT(EPOCH FROM r.schedule_start_time) * 1000 AS start_time,
                            EXTRACT(EPOCH FROM r.schedule_end_time) * 1000   AS end_time
                        FROM release r
                            ${joinBasicInfo}
                        WHERE r.release_id = ANY ($1)
                        GROUP BY r.release_id
                    )
                    SELECT *,
                    (
                        SELECT COUNT(j.id)
                        FROM job j
                        WHERE j.id = ANY (r.download_jobs)
                        AND j.status = 3 -- complete
                    )   AS deployments_downloaded_count,
                    (
                        SELECT json_build_object(
                            'total', COUNT(tr.release_id),
                            'cancelled', COUNT(CASE WHEN (j2.status = 5 AND j3.status != ALL ('{3,4}')) OR j3.status = ANY ('{5}') THEN j2.id END ),
                            'failed', COUNT(CASE WHEN (j2.status = 4 AND j3.status != 5) OR j3.status = 4 THEN j2.id END ),
                            'pendingDownload', COUNT(CASE WHEN ((j2.status = 0 OR j2.status = 2) AND j3.status = 0) THEN j2.id END ),
                            'pendingInstall', COUNT(CASE WHEN (j2.status = 3 ) AND (j3.status = ANY ('{0,2}')) THEN j2.id END ),
                            'completed', COUNT(CASE WHEN j3.status = 3 THEN j2.id END )
                            )
                        FROM target_release tr
                                INNER JOIN job j2 on tr.download_job = j2.id
                                LEFT JOIN job j3 on tr.install_job = j3.id
                        WHERE tr.target_release_id = ANY (r.target_releases)
                        GROUP BY tr.release_id
                    ) as file_download_status,
                    (
                    SELECT json_object_agg(tmp.status, tmp.count)
                    FROM (
                                SELECT j.status, COUNT(j.status)
                                FROM job j
                                WHERE j.id = ANY (r.install_jobs)
                                GROUP BY j.status
                            ) AS tmp
                    ) as status
                    FROM releases r;
                `,
          [releaseIds]
        ),

        server.db.read.rows(
          `
                    SELECT
                       r.release_id AS id,
                       CASE
                         WHEN r.cancelled THEN json_build_object(
                             'id', ca.id,
                             'fullName', ca.full_name,
                             'company', json_build_object(
                                 'id', cac.id,
                                 'name', cac.name
                               )
                           )
                         ELSE NULL
                         END        AS cancelled_by,
                
                       json_build_object(
                           'id', uc.id,
                           'fullName', uc.full_name,
                           'company', json_build_object(
                               'id', c.id,
                               'name', c.name
                             )
                         )          AS created_by,
                
                       json_build_object(
                           'id', dp.id,
                           'description', dp.description,
                           'name', dp.name
                         )          AS deployment_policy,
                
                       json_build_object(
                           'id', sw.software_id,
                           'name', sw.name,
                           'relatedEntity', sw.related_entity,
                           'softwareFile', sw.software_file
                         )          AS software,
                
                       CASE
                         WHEN r.type = 'media' THEN json_build_object(
                             'status', ps.status,
                             'version', ps.version
                           )
                         END        AS media
                    FROM release r
                    ${joinReleaseInfo}
                    WHERE
                        r.release_id = ANY ( $1 )
                `,
          [releaseIds]
        ),
      ]);

      /* eslint-disable no-param-reassign */
      const results = _.map(resultData, result => {
        result.deploymentsCancelCount =
          result.status[server.constants.job.STATUS.CANCELLED] || 0;
        result.deploymentsCompleteCount =
          result.status[server.constants.job.STATUS.COMPLETED] || 0;
        result.deploymentsFailCount =
          result.status[server.constants.job.STATUS.FAILED] || 0;
        result.deploymentsInProgressCount =
          result.status[server.constants.job.STATUS.IN_PROGRESS] || 0;

        result.deploymentsPendingDownloadCount =
          result.fileDownloadStatus.pendingDownload;
        result.deploymentsPendingInstallCount =
          result.fileDownloadStatus.pendingInstall;
        result.fileDownloadCancelCount = result.fileDownloadStatus.cancelled;
        result.fileDownloadFailCount = result.fileDownloadStatus.failed;
        result.fileDownloadTotalCount = result.fileDownloadStatus.total;

        result.totalDeploymentsCount =
          (result.status[server.constants.job.STATUS.NEW] || 0) +
          (result.status[server.constants.job.STATUS.ACCEPTED] || 0) +
          (result.status[server.constants.job.STATUS.IN_PROGRESS] || 0) +
          (result.status[server.constants.job.STATUS.COMPLETED] || 0) +
          (result.status[server.constants.job.STATUS.CANCELLED] || 0) +
          (result.status[server.constants.job.STATUS.FAILED] || 0);

        delete result.status;

        delete result.downloadJobs;
        delete result.installJobs;
        delete result.fileDownloadStatus;
        delete result.targetReleases;

        const info = _.find(resultInfo, { id: result.id });

        return _.assign({}, info, result);
      });
      /* eslint-enable */

      res.send({
        resultsMetadata: {
          totalResults: totalCount,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results,
      });
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
