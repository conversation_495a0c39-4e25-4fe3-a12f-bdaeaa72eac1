const url = require('url');
const stream = require('stream');
const crypto = require('crypto');
const errors = require('restify-errors');
const restify = require('restify');
const co = require('co');
const formidable = require('formidable');

const uuid = require('uuid/v4');
const AWS = require('../lib/aws');
const paginationHelper = require('../helpers/pagination-helper');
const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');
const env = require('../env');
const subDeviceTypeMap = require('../helpers/sub-devicetype.json');
const {
  getDeletionLimit,
  addApiAuditLog,
} = require('../helpers/software-helper');

const { config } = env;

const stringUtils = require('../lib/string-utils');
const { mainLogger } = require('../lib/logger');

const logger = mainLogger();

const FILE_RESTORE_TIMEOUT = '15 min';

module.exports = {
  createSoftware: (req, res, next) => {
    const form = new formidable.IncomingForm();
    return co(function* execute() {
      const userId = req.user.sub;
      const userName = req.user.fullName;

      const companyId = req.user.company.id;
      const { deviceType, subDeviceType, seqVersion } = req.params;

      const deviceTypeQuery = `
                SELECT device_type
                FROM company_product_type pt
                WHERE
                    company = $1 AND
                    device_type = $2;  
            `;
      const companyDeviceType = yield server.db.read.row(deviceTypeQuery, [
        companyId,
        deviceType,
      ]);
      if (!companyDeviceType) {
        return next(
          new errors.BadRequestError(`Invalid product type name ${deviceType}.`)
        );
      }

      if (subDeviceType && !subDeviceTypeMap[subDeviceType]) {
        return next(
          new errors.BadRequestError(
            `Invalid sub product type name ${subDeviceType}.`
          )
        );
      }

      // eslint-disable-next-line consistent-return
      form.onPart = part => {
        if (!part.filename) {
          // let formidable handle all non-file parts
          form.handlePart(part);
        } else {
          if (part.filename.length > 100) {
            return next(
              new errors.BadRequestError(
                'File name is too long (max 100 characters).'
              )
            );
          }

          let sha256;
          let size = 0;

          const softwareType = req.params.type || 'software';
          const hash = crypto.createHash('sha256');
          const s3stream = new stream.PassThrough();
          const cryptoStream = new stream.PassThrough();

          cryptoStream.on('data', data => {
            if (data) {
              size += data.length;
              hash.update(data);
            }
          });

          cryptoStream.on('end', () => {
            sha256 = hash.digest('hex');
          });

          part.pipe(s3stream);
          part.pipe(cryptoStream);
          s3stream.on('error', errorHandler.onError(req, res, next));

          return co(function* executeInternal() {
            // Check if software with same name already exists (we don't allow duplicate names)
            const query = `
                            SELECT s.software_id
                            FROM software s
                            WHERE
                                LOWER(s.name) = LOWER($1) AND
                                s.company_id = $2 AND
                                active = TRUE;
                        `;
            const duplicateNameSoftware = yield server.db.read.row(query, [
              req.params.name,
              companyId,
            ]);
            if (
              duplicateNameSoftware &&
              duplicateNameSoftware.softwareId !== req.params.id
            ) {
              req.log.info(
                `Software with name ${req.params.name} already exists.`
              );
              return next(
                new errors.BadRequestError(
                  `Software with name ${req.params.name} already exists.`
                )
              );
            }

            // Get display name
            const displayNameQuery = `
                            SELECT display_name AS display_name
                            FROM company_product_type
                            WHERE
                                device_type = $1 AND
                                company = $2;
                        `;
            const displayName = yield server.db.read.row(displayNameQuery, [
              req.params.deviceType,
              companyId,
            ]);

            // Create new software entry
            // TODO: Remove string templating
            const insertQuery = `
                            INSERT INTO software (
                                device_type, version, description, prerequisite_version, software_file, name,
                                type, company_id, related_entity, date_time_created, uploaded_by, active, software_type_id , sub_device_type, seq_version
                            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
                            RETURNING
                                description,
                                json_build_object( 
                                    'id', device_type, 
                                    'name', '${displayName.displayName}'
                                ) AS device_type,
                                software_id as id, name, related_entity, software_file, type,
                                date_time_created AS uploaded_date,
                                sub_device_type, seq_version
                        `;

            const created = new Date().toISOString();
            let insertResult = yield server.db.write.execute(insertQuery, [
              req.params.deviceType,
              null,
              req.params.description,
              null,
              part.filename,
              req.params.name,
              softwareType,
              companyId,
              null,
              created,
              userId,
              false,
              1,
              subDeviceType,
              seqVersion,
            ]);

            const { 0: firstRow } = insertResult;
            insertResult = firstRow;
            const params = {
              Key: `Software Release/${insertResult.id}/${part.filename}`,
              Body: s3stream,
              ACL: 'public-read',
              // FIXME: refactor our configs so it's by functionality (software instead of offlinePackage)
              Bucket: config.offlinePackage.s3SoftwareBucket,
            };

            yield AWS.uploadToS3(
              params.Bucket,
              params.Key,
              params.Body,
              params.ACL
            );
            const apiUrl = url.parse(config.url.api);

            // NOTE: ICS-2816, dont use S3.upload.Location because its inconsistent with spaces in URLs (>5MB = Software+Release, <5MB = Software%20Release)
            const softwareFileUrl = `${apiUrl.protocol}//${apiUrl.host}/Software%20Release/${insertResult.id}/${part.filename}`;

            yield server.db.write.execute(
              `
                            UPDATE software
                            SET
                                active = TRUE,
                                software_file_url = $1,
                                size = $2,
                                software_file_signature = $3
                            WHERE software_id = $4;
                        `,
              [softwareFileUrl, size, sha256, insertResult.id]
            );

            insertResult.softwareFileUrl = softwareFileUrl;
            insertResult.size = size;
            insertResult.uploadedBy = {
              id: userId,
              name: userName,
            };
            res.send(insertResult, 200);
            return next();
          }).catch(errorHandler.onError(req, res, next));
        }
      };

      form.parse(req);
      return true;
    }).catch(errorHandler.onError(req, res, next));
  },

  updateSofware: (req, res, next) =>
    co(function* execute() {
      const userId = req.user.sub;
      const companyId = req.user.company.id;
      const { deviceType, subDeviceType, seqVersion } = req.body;

      const deviceTypeQuery = `
                SELECT device_type
                FROM company_product_type
                WHERE
                    company = $1 AND 
                    device_type = $2;
            `;

      const companyDeviceType = yield server.db.read.row(deviceTypeQuery, [
        companyId,
        deviceType,
      ]);
      if (!companyDeviceType) {
        return next(
          new errors.BadRequestError(`Invalid product type name ${deviceType}.`)
        );
      }

      if (subDeviceType && !subDeviceTypeMap[subDeviceType]) {
        return next(
          new errors.BadRequestError(
            `Invalid sub product type name ${subDeviceType}.`
          )
        );
      }

      const query = `
                SELECT software_id
                FROM software
                WHERE
                    LOWER(name) = LOWER($1) AND
                    company_id = $2 AND
                    active = TRUE;
            `;
      const duplicateNameSoftware = yield server.db.read.row(query, [
        req.body.name,
        companyId,
      ]);
      if (
        duplicateNameSoftware &&
        duplicateNameSoftware.softwareId.toString() !== req.params.id
      ) {
        req.log.info(`Software with name ${req.body.name} already exists.`);
        return next(
          new errors.BadRequestError(
            `Software with name ${req.body.name} already exists.`
          )
        );
      }

      const queryParams = [req.params.id, companyId];
      let paramIndex = queryParams.length;

      let updateQuery = 'UPDATE software SET';

      if (req.body.name) {
        updateQuery = `${updateQuery} name = $${(paramIndex += 1)},`;
        queryParams.push(req.body.name);
      }

      if (req.body.description || req.body.description === '') {
        updateQuery = `${updateQuery} description = $${(paramIndex += 1)},`;
        queryParams.push(req.body.description);
      }

      if (req.body.deviceType) {
        updateQuery = `${updateQuery} device_type = $${(paramIndex += 1)},`;
        queryParams.push(req.body.deviceType);
      }

      if (subDeviceType || subDeviceType === null) {
        updateQuery = `${updateQuery} sub_device_type = $${(paramIndex += 1)},`;
        queryParams.push(subDeviceType);
      }
      if (seqVersion || seqVersion === null) {
        updateQuery = `${updateQuery} seq_version = $${(paramIndex += 1)},`;
        queryParams.push(seqVersion);
      }

      updateQuery = `
                ${updateQuery}
                updated_by = $${(paramIndex += 1)},
                updated_date = NOW()
            `;
      queryParams.push(userId);

      updateQuery = updateQuery.substring(0, updateQuery.length - 1);
      updateQuery = `
                ${updateQuery}
                 WHERE
                    software_id = $1 AND
                    company_id = $2 AND
                    active = TRUE;
            `;

      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');

        const response = yield connection.execute(updateQuery, queryParams);
        if (response.rowCount === 0) {
          yield connection.execute('ROLLBACK');
          req.log.info(`Software with id ${req.params.id} does not exists.`);
          return next(
            new errors.NotFoundError(
              `Software with id ${req.params.id} does not exists.`
            )
          );
        }

        yield connection.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        throw err;
      } finally {
        connection.done();
      }

      const updatedSoftwareQuery = `
                SELECT
                    s.description,
                    json_build_object(
                        'id', cpt.device_type,
                        'name', cpt.display_name
                    )                                   as device_type,
                    s.software_id                       as id,
                    s.seq_version,
                    s.name,
                    s.related_entity,
                    s.size,
                    s.software_file,
                    s.type,
                    s.date_time_created                 as uploaded_date,
                    json_build_object(
                        'id', cr.id,
                        'name', cr.full_name
                    )                                   as uploaded_by,
                    s.updated_date,
                    json_build_object(
                        'id', md.id,
                        'name', md.full_name
                    )                                   as updated_by,
                    s.sub_device_type
                FROM software s
                    JOIN company_product_type cpt ON (
                        s.company_id = cpt.company AND
                        s.device_type = cpt.device_type
                    )
                    LEFT JOIN ics_user cr ON s.uploaded_by = cr.id
                    LEFT JOIN ics_user md ON s.updated_by = md.id
                WHERE
                    s.software_id = $1 AND
                    s.active = TRUE;
            `;

      const updatedSoftware = yield server.db.read.row(updatedSoftwareQuery, [
        req.params.id,
      ]);
      res.send(200, updatedSoftware);
      return next();
    }),

  deleteSoftware: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;
      const query = `
                SELECT s.software_id, j.*
                FROM software s
                LEFT JOIN target_release tr ON tr.software_id = s.software_id
                LEFT JOIN job j ON tr.install_job = j.id
                WHERE
                    s.software_id = $1 AND
                    s.company_id = $2 AND
                    s.active = TRUE;
            `;

      const softwareToDelete = yield server.db.read.rows(query, [
        req.params.id,
        companyId,
      ]);

      if (softwareToDelete.length === 0) {
        req.log.info(`Cannot find software with id ${req.params.id}`);
        return next(
          new errors.NotFoundError(
            `Cannot find software with id ${req.params.id}`
          )
        );
      }

      for (let i = 0; i < softwareToDelete.length; i++) {
        const item = softwareToDelete[i];
        if (item.status != null && item.status < 3) {
          return next(
            new errors.ConflictError(
              'Software cannot be deleted because it is currently in use in a software rollout'
            )
          );
        }
      }

      const connection = yield server.db.write.getConnection();
      try {
        // begin transaction
        yield connection.execute('BEGIN');

        yield connection.execute(
          `
                    UPDATE software
                    SET active = false
                    WHERE software_id = $1;
                `,
          [req.params.id]
        );

        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        throw err;
      }
      res.send(204);
      return next();
    }),

  getSoftware: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;
      const pageParams = paginationHelper.parsePaginationParams(req);

      const { deviceType, subDeviceType, promptSetProfileName } = req.params;
      const { name } = req.params;
      const { type } = req.params;
      const { order } = req.params;

      const queryParams = [companyId];

      const from = ` 
                FROM software s
                JOIN company_product_type cpt ON ( 
                    s.company_id = cpt.company AND 
                    s.device_type = cpt.device_type
                )
                LEFT JOIN ics_user cr ON s.uploaded_by = cr.id
                LEFT JOIN ics_user md ON s.updated_by = md.id
            `;
      let where = ` WHERE
                s.company_id = $1 AND
                active = TRUE
            `;

      let paramIndex = 2;

      if (deviceType) {
        where += ` AND s.device_type = $${paramIndex}`;
        queryParams.push(deviceType);
        paramIndex += 1;
      }
      if (promptSetProfileName) {
        where += ` AND s.prompt_set_profile_name = $${paramIndex}`;
        queryParams.push(promptSetProfileName);
        paramIndex += 1;
      }
      if (name) {
        where += ` AND lower(s.name) LIKE lower($${paramIndex})`;
        queryParams.push(`%${name}%`);
        paramIndex += 1;
      }
      if (type) {
        where += ` AND s.type = $${paramIndex}`;
        queryParams.push(type);
        paramIndex += 1;
      }
      if (subDeviceType) {
        where += ` AND s.sub_device_type = $${paramIndex}`;
        queryParams.push(subDeviceType);
        paramIndex += 1;
      }

      const countQuery = `SELECT COUNT(1) ${from} ${where}`;
      const totalObj = yield server.db.read.row(countQuery, queryParams);

      let orderBy = 'date_time_created';
      let orderDirection = 'desc';
      if (order && order === 'name') {
        orderBy = 'name';
        orderDirection = 'asc';
      }

      // NOTE: Related Entity is only inflated for media types, add more cases when a new entity relation is added
      const query = `
                SELECT
                    s.description, s.name, s.software_file, s.size, s.type, s.sub_device_type, s.seq_version,
                    json_build_object(
                        'id', cpt.device_type,
                        'name', cpt.display_name
                    )                                   as device_type,
                    s.software_id                       as id,
                    s.date_time_created                   as uploaded_date,
                    json_build_object(
                        'id', cr.id,
                        'name', cr.full_name
                    )                                   as uploaded_by,
                    s.updated_date,
                    json_build_object(
                        'id', md.id,
                        'name', md.full_name
                    )                                   as updated_by,
                    CASE 
                        WHEN ( s.type = 'media' )
                        THEN ( row_to_json( ( 
                            SELECT d FROM (
                                SELECT id, status, version
                                FROM prompt_set 
                                WHERE
                                    id = s.related_entity::uuid
                            ) d ) 
                        ) )
                    END                                     as related_entity,
                    s.prompt_set_profile_name
                ${from}
                ${where}
                ORDER BY ${orderBy} ${orderDirection}
                LIMIT ${pageParams.pageSize}
                OFFSET ${pageParams.offset};
            `;

      const result = yield server.db.read.rows(query, queryParams);

      const output = {
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results: result,
      };

      res.send(output);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * create new software record
   */
  createSofwareInternal: (req, res, next) =>
    co(function* execute() {
      const userId = req.user.sub;
      const userName = req.user.fullName;

      const software = req.body;

      const { subDeviceType } = software;

      if (subDeviceType && !subDeviceTypeMap[subDeviceType]) {
        return next(
          new errors.BadRequestError(
            `Invalid sub product type name ${subDeviceType}.`
          )
        );
      }

      // Create new software entry
      const query = `
                INSERT INTO software (
                    device_type,
                    version,
                    description,
                    prerequisite_version,
                    software_file,
                    software_file_signature,
                    software_file_url,
                    name,
                    size,
                    type,
                    company_id,
                    related_entity,
                    date_time_created,
                    uploaded_by,
                    active,
                    software_type_id,
                    sub_device_type,
                    prompt_set_profile_name,
                    seq_version
                )
                VALUES ( $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW(), $13, $14, $15, $16, $17, $18)
                RETURNING *;
            `;

      req.log.info({ software }, 'Creating software record');

      const connection = yield server.db.write.getConnection();

      // START: TO BE REMOVED WHEN OBSOLETE COLUMN software_type_id ARE REMOVED FROM DB
      let softwareTypeId;
      switch (software.type) {
        case 'software':
          softwareTypeId = 1;
          break;
        case 'media':
          softwareTypeId = 2;
          break; // using 2 as it is not being currently used and software_type table is going to be soon removed
        default:
          break;
      }
      // END: TO BE REMOVED WHEN OBSOLETE COLUMN software_type_id ARE REMOVED FROM DB

      let result;
      try {
        yield connection.execute('BEGIN');
        result = yield connection.execute(query, [
          software.deviceType,
          software.version,
          software.description,
          software.prerequisiteVersion,
          software.softwareFile,
          software.softwareFileSignature,
          software.softwareFileUrl,
          software.name,
          software.size,
          software.type,
          software.companyId,
          software.relatedEntity,
          userId,
          true,
          softwareTypeId,
          software.subDeviceType,
          software.promptSetProfileName,
          software.seqVersion,
        ]);
        const { 0: firstRow } = result.rows;
        result = firstRow;
        result.uploadedBy = {
          id: userId,
          name: userName,
        };

        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        throw err;
      }
      res.send(200, result);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getSoftwareContent: (req, res, next) => {
    const softwareId = req.params.id;
    const companyId = req.user.company.id;

    return co(function* execute() {
      const software = yield server.db.read.row(
        `
                SELECT * FROM software
                WHERE
                    software_id = $1 AND
                    company_id = $2 AND
                    active = TRUE;
            `,
        [softwareId, companyId]
      );

      if (!software) {
        return next(new restify.NotFoundError('Software not found'));
      }

      const s3Key = `Software Release/${software.softwareId}/${software.softwareFile}`;

      req.log.info(
        `Attempting to download software content for key: ${s3Key}.`
      );

      res.setHeader(
        'Content-Disposition',
        `attachment; filename="${software.softwareFile}"`
      );
      res.setHeader('File-Name', software.softwareFile);

      return AWS.downloadFromS3({ Key: s3Key })
        .on('httpHeaders', (statusCode, headers) => {
          if (statusCode === 200) {
            res.setHeader('Content-Length', headers['content-length']);
            res.setHeader('Content-Type', headers['content-type']);
            res.setHeader('Last-Modified', headers['last-modified']);
          }
        })
        .createReadStream()
        .on('error', err => {
          req.log.error({ err });

          if (err.code === 'NoSuchKey') {
            return next(
              new restify.NotFoundError('The specified key does not exist.')
            );
          }
          return next(
            new restify.InternalServerError(
              'Could not download software content.'
            )
          );
        })
        .on('end', () => next())
        .pipe(res);
    }).catch(errorHandler.onError(req, res, next));
  },

  getSoftwareByFilters: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;
      const {
        deviceType,
        type,
        filters,
        subDeviceType,
        pageSize,
        pageIndex,
        order,
        promptSetProfileName,
      } = req.body;

      req.query = { pageSize, pageIndex };
      const pageParams = paginationHelper.parsePaginationParams(req);

      const from = ` 
                    FROM software s
                    JOIN company_product_type cpt ON ( 
                        s.company_id = cpt.company AND 
                        s.device_type = cpt.device_type
                    )
                    LEFT JOIN ics_user cr ON s.uploaded_by = cr.id
                    LEFT JOIN ics_user md ON s.updated_by = md.id
                `;

      let where = ` WHERE
                    s.device_type = $1 AND
                    s.company_id = $2 AND
                    active = TRUE
                `;

      const queryParams = [deviceType, companyId];
      let paramIndex = 3;

      if (type) {
        where += ` AND s.type = $${paramIndex}`;
        queryParams.push(type);
        paramIndex += 1;
      }
      if (promptSetProfileName) {
        where += ` AND s.prompt_set_profile_name = $${paramIndex}`;
        queryParams.push(promptSetProfileName);
        paramIndex += 1;
      }

      if (filters) {
        if (filters.createdBy && filters.createdBy.length > 0) {
          where += ` AND s.uploaded_by = ANY($${paramIndex})`;
          queryParams.push(filters.createdBy);
          paramIndex += 1;
        }

        if (filters.createdBefore) {
          where += ` AND s.date_time_created < NOW() - $${paramIndex}::text::interval`;
          queryParams.push(`${filters.createdBefore} days`);
          paramIndex += 1;
        }

        if (filters.fileName) {
          where += ` AND LOWER(s.name) like LOWER($${paramIndex})`;
          queryParams.push(`%${filters.fileName}%`);
          paramIndex += 1;
        }

        if (filters.isDuplicate) {
          where += ` AND s.software_file in 
                    (SELECT software_file from software 
                    WHERE device_type=$1 
                    AND active=TRUE 
                    AND company_id=$2 
                    GROUP BY software_file 
                    HAVING COUNT(software_file) > 1) 
                    `;
        }

        if (filters.fileExtensions && filters.fileExtensions.length > 0) {
          const fileExtCond = [];
          filters.fileExtensions.forEach(ext => {
            fileExtCond.push(`s.software_file LIKE $${paramIndex}`);
            queryParams.push(`%.${ext}`);
            paramIndex += 1;
          });

          where += ` AND (${fileExtCond.join(` OR `)})`;
        }
        if (subDeviceType) {
          where += ` AND s.sub_device_type = $${paramIndex}`;
          queryParams.push(subDeviceType);
          paramIndex += 1;
        }
      }

      const countQuery = `SELECT COUNT(1) ${from} ${where}`;
      const totalObj = yield server.db.read.row(countQuery, queryParams);

      let orderBy = 'date_time_created';
      let orderDirection = 'desc';
      if (order && order === 'name') {
        orderBy = 'name';
        orderDirection = 'asc';
      }

      // NOTE: Related Entity is only inflated for media types, add more cases when a new entity relation is added
      const query = `
                SELECT
                    s.description, s.name, s.software_file, s.size, s.type, s.seq_version,
                    json_build_object(
                        'id', cpt.device_type,
                        'name', cpt.display_name,
                        'subDeviceType', s.sub_device_type
                    )                                   as device_type,
                    s.software_id                       as id,
                    s.date_time_created                   as uploaded_date,
                    json_build_object(
                        'id', cr.id,
                        'name', cr.full_name
                    )                                   as uploaded_by,
                    s.updated_date,
                    json_build_object(
                        'id', md.id,
                        'name', md.full_name
                    )                                   as updated_by,
                    CASE 
                        WHEN ( s.type = 'media' )
                        THEN ( row_to_json( ( 
                            SELECT d FROM (
                                SELECT id, status, version
                                FROM prompt_set 
                                WHERE
                                    id = s.related_entity::uuid
                            ) d ) 
                        ) )
                    END                                     as related_entity,
                  s.prompt_set_profile_name
                ${from}
                ${where}
                ORDER BY ${orderBy} ${orderDirection}
                LIMIT ${pageParams.pageSize}
                OFFSET ${pageParams.offset};
            `;
      const maxFileDeleteLimit = yield getDeletionLimit(companyId);
      const result = yield server.db.read.rows(query, queryParams);
      const output = {
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
          maxFileDeleteLimit,
        },
        results: result,
      };

      res.send(output);

      addApiAuditLog({ req, response: output, statusCode: 200 });

      return next();
    }),

  deleteMultipleSoftwares: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;
      const { deviceType, filters, type, include, exclude, order } = req.body;
      let { pageSize } = req.body;
      const userId = req.user.sub;
      const deleteRequestId = uuid();

      const maxFileDeleteLimit = yield getDeletionLimit(companyId);

      if (pageSize) {
        if (pageSize > maxFileDeleteLimit) {
          req.log.info(`Number of Files selected exceeds the maximum limit`);
          addApiAuditLog({
            req,
            response: {
              code: 'RangeNotSatisfiable',
              message: 'Number of Files selected exceeds the maximum limit',
            },
            statusCode: 416,
          });
          return next(
            new errors.RangeNotSatisfiableError(
              `Number of Files selected exceeds the maximum limit`
            )
          );
        }

        if (pageSize === -1) {
          pageSize = maxFileDeleteLimit;
        }
      } else {
        pageSize = maxFileDeleteLimit;
      }

      let query = `SELECT * FROM software s 
                    LEFT JOIN target_release tr ON tr.software_id = s.software_id
                    LEFT JOIN job j ON tr.install_job = j.id`;

      let where = ` WHERE s.device_type = $1 
                  AND s.active = TRUE 
                  AND s.company_id = $2
                  `;

      const queryParams = [deviceType, companyId];
      let paramIndex = 3;

      if (type) {
        where += ` AND s.type = $${paramIndex}`;
        queryParams.push(type);
        paramIndex += 1;
      }

      if (filters) {
        if (filters.createdBy && filters.createdBy.length > 0) {
          where += ` AND s.uploaded_by = ANY($${paramIndex})`;
          queryParams.push(filters.createdBy);
          paramIndex += 1;
        }

        if (filters.createdBefore) {
          where += ` AND s.date_time_created < NOW() - $${paramIndex}::text::interval`;
          queryParams.push(`${filters.createdBefore} days`);
          paramIndex += 1;
        }

        if (filters.fileName) {
          where += ` AND LOWER(s.name) like LOWER($${paramIndex})`;
          queryParams.push(`%${filters.fileName}%`);
          paramIndex += 1;
        }

        if (filters.isDuplicate) {
          where += ` AND s.software_file in 
                    (SELECT software_file from software 
                    WHERE device_type=$1 
                    AND active=TRUE 
                    AND company_id=$2 
                    GROUP BY software_file 
                    HAVING COUNT(software_file) > 1) 
                    `;
        }

        if (filters.fileExtensions && filters.fileExtensions.length > 0) {
          const fileExtCond = [];
          filters.fileExtensions.forEach(ext => {
            fileExtCond.push(`s.software_file LIKE $${paramIndex}`);
            queryParams.push(`%.${ext}`);
            paramIndex += 1;
          });

          where += ` AND (${fileExtCond.join(` OR `)})`;
        }
      }

      if (include && include.length > 0) {
        where += ` AND s.software_id = ANY($${paramIndex})`;
        queryParams.push(include);
        paramIndex += 1;
      }

      if (exclude && exclude.length > 0) {
        where += ` AND NOT (s.software_id = ANY($${paramIndex}))`;
        queryParams.push(exclude);
        paramIndex += 1;
      }

      let orderBy = 'date_time_created';
      let orderDirection = 'desc';
      if (order && order === 'name') {
        orderBy = 'name';
        orderDirection = 'asc';
      }

      query = `${query} 
               ${where}
               ORDER BY ${orderBy} ${orderDirection}
               LIMIT ${pageSize}
               OFFSET 0`;

      const softwaresToDelete = yield server.db.read.rows(query, queryParams);

      if (softwaresToDelete.length === 0) {
        req.log.info(`Cannot find any softwares with ids provided`);
        addApiAuditLog({
          req,
          response: {
            code: 'NotFound',
            message: 'Cannot find any softwares with ids provided',
          },
          statusCode: 404,
        });
        return next(
          new errors.NotFoundError(
            `Cannot find any softwares with ids provided`
          )
        );
      }

      for (let i = 0; i < softwaresToDelete.length; i++) {
        const item = softwaresToDelete[i];
        if (item.status != null && item.status < 3) {
          addApiAuditLog({
            req,
            response: {
              code: 'Conflict',
              message: `Software ${item.name} cannot be deleted because it is currently in use in a software rollout`,
            },
            statusCode: 409,
          });
          return next(
            new errors.ConflictError(
              `Software ${item.name} cannot be deleted because it is currently in use in a software rollout`
            )
          );
        }
      }

      const connection = yield server.db.write.getConnection();
      try {
        // begin transaction
        yield connection.execute('BEGIN');

        let updateQuery = `
                          UPDATE software
                          SET active = FALSE , updated_date = NOW(), updated_by = $${paramIndex},`;
        paramIndex += 1;
        queryParams.push(userId);

        updateQuery += `delete_request_id = $${paramIndex}
                          WHERE software_id in 
                          (SELECT s.software_id from software s ${where}
                          ORDER BY ${orderBy} ${orderDirection}
                          LIMIT ${pageSize}
                          OFFSET 0)
                      `;

        paramIndex += 1;
        queryParams.push(deleteRequestId);

        yield connection.execute(updateQuery, queryParams);
        yield connection.execute('COMMIT');
        connection.done();

        res.send({ deleteRequestId });

        addApiAuditLog({ req, response: { deleteRequestId }, statusCode: 200 });

        return next();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        addApiAuditLog({ req, response: err, statusCode: 500 });
        throw err;
      }
    }),

  restoreSoftwares: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;
      const { deviceType, deleteRequestId } = req.body;
      // let { pageSize } = req.body;
      const userId = req.user.sub;

      const connection = yield server.db.write.getConnection();
      try {
        // begin transaction
        yield connection.execute('BEGIN');

        const query = `UPDATE software
                      SET active = TRUE,
                      updated_date = NOW(), updated_by = $4
                      WHERE device_type = $1
                      AND company_id = $2
                      AND delete_request_id = $3
                      AND active = FALSE
                      AND updated_by = $4
                      AND updated_date > NOW() - $5::text::interval`;

        const queryParams = [
          deviceType,
          companyId,
          deleteRequestId,
          userId,
          FILE_RESTORE_TIMEOUT,
        ];

        const restoredFiles = yield connection.execute(query, queryParams);
        yield connection.execute('COMMIT');
        connection.done();

        let msg = '';

        if (restoredFiles.rowCount && restoredFiles.rowCount > 0) {
          msg = `${restoredFiles.rowCount} files were restored.`;
        } else {
          msg = 'No files were restored.';
        }

        res.send(msg);

        addApiAuditLog({ req, response: msg, statusCode: 200 });

        return next();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        addApiAuditLog({ req, response: err, statusCode: 500 });
        throw err;
      }
    }),

  deleteInactiveFiles: (req, res, next) => {
    co(function* execute() {
      try {
        const query = `SELECT * FROM software s
                     WHERE s.active = FALSE
                     AND s.updated_date > NOW() - INTERVAL '1 day'
                     AND s.delete_request_id IS NOT NULL
                     AND s.software_file_url IS NOT NULL`;

        const softwaresToDelete = yield server.db.read.rows(query, []);

        if (softwaresToDelete.length > 0) {
          const fileKeys = [];
          const softwareIds = [];

          for (let i = 0; i < softwaresToDelete.length; i++) {
            const s3Key = decodeURI(
              stringUtils.getPathName(softwaresToDelete[i].softwareFileUrl)
            );
            fileKeys.push({ Key: s3Key });
            softwareIds.push(softwaresToDelete[i].softwareId);
          }

          const deleteS3Resp = yield AWS.deleteS3Files(
            config.offlinePackage.s3SoftwareBucket,
            fileKeys
          );

          const message = `[fileLibraryDeleteTask] Files deleted from S3}`;
          logger.info(fileKeys, message);

          res.send(JSON.stringify({ deleteS3Resp, fileKeys }));
          return next();
        }

        logger.info(`[fileLibraryDeleteTask]: No files to delete`);
        res.send(404, 'No files to delete');
        return next();
      } catch (err) {
        logger.error(`[fileLibraryDeleteTask].[error] ${err}`);
        throw err;
      }
    });
  },
};
