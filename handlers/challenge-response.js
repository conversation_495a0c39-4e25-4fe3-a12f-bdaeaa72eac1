/* eslint-disable no-param-reassign */
/* eslint-disable no-promise-executor-return */
const childProcess = require('child_process');
const restify = require('restify');

const env = require('../env');
const totp = require('../lib/totp');
const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');
const usersHelper = require('../helpers/users-helper');
const mfaJobConstants = require('../src/entities/api/device/mfa/jobs/mfa-jobs.constant');

const validFRAOperations = {
  FACTORY_RESET: 'FACTORY_RESET',
  [mfaJobConstants.MERCHANT_RESET]: mfaJobConstants.MERCHANT_RESET,
};

const generateChallengeResponse = (
  key,
  user,
  slot,
  serialNumber,
  challenge,
  operation
) => {
  const command = validFRAOperations[operation] ? 'fra' : 'rta';

  return new Promise((resolve, reject) =>
    childProcess.exec(
      `${env.config.tamperChallenge.scriptPath} ${key} ${user} ${slot} ${serialNumber} ${challenge} ${command}`,
      (error, stdout, stderr) => {
        if (stdout) {
          stdout = stdout.trim();
        }

        if (error) {
          error.stdout = stdout;
          error.stderr = stderr;
          return reject(error);
        }

        const ERROR_SIGNATURE = 'Err:';
        if (stdout.indexOf(ERROR_SIGNATURE) === 0) {
          return reject(stdout);
        }

        const SUCCESS_SIGNATURE_REGEX = new RegExp(
          `^${serialNumber} [0-9]{8}$`,
          'g'
        );
        if (!SUCCESS_SIGNATURE_REGEX.test(stdout)) {
          return reject(stdout);
        }

        return resolve(stdout);
      }
    )
  ).then(stdout => {
    const parts = stdout.split(' ');
    if (parts.length > 1) {
      return parts[1];
    }
    return null;
  });
};

const sanitiseHSMError = (err, key, user) => {
  const sanitised = '<sanitised>';

  const keyRegex = new RegExp(key, 'g');
  const userPasswordRegex = new RegExp(user, 'g');

  const deepSanitize = object => {
    Object.getOwnPropertyNames(object).forEach(property => {
      if (object[property] instanceof Object) {
        deepSanitize(object[property]);
      } else if (typeof object[property] === 'string') {
        object[property] = object[property]
          .replace(keyRegex, sanitised)
          .replace(userPasswordRegex, sanitised);
      }
    });
  };

  return deepSanitize(err);
};

module.exports = {
  generateChallengeResponse,
  async requestChallengeResponse(req, res, next) {
    try {
      const tamperChallengeConfig = env.config.tamperChallenge;

      const userId = req.user.sub;

      const deviceId = req.params.id;
      if (!deviceId) {
        return next(
          new restify.errors.BadRequestError(
            'Unmet Requirements or Invalid Parameters'
          )
        ); // 400
      }

      const { challenge } = req.body;
      const serialNumber = req.body.serialNumber.toUpperCase();
      const { requestedBy } = req.body;
      const { mfaCode } = req.body;
      const { operation } = req.body;

      const mfaSecret = await usersHelper.getUserMfaSecretById(userId);
      const isMfaCodeValid = await totp.validateTotp(req, mfaSecret, mfaCode);
      if (!isMfaCodeValid) {
        return next(
          new restify.errors.ForbiddenError(
            `MFA code ${mfaCode} validation failed`
          )
        ); // 403
      }

      const hasUserRole = req.user.hasRole(operation);
      if (!hasUserRole) {
        return next(new restify.errors.ForbiddenError('No Permission')); // 403
      }

      const device = await server.db.read.row(
        `
                SELECT t.name, t.serial_number, ff.feature_flag
                    FROM target t
                    JOIN user_site_authorization a ON a.site_id = t.site_id
                    LEFT JOIN device_feature_flag ff ON
                        ff.device_type = t.device_type AND
                        ff.feature_flag = $1
                WHERE
                    t.target_id = $2 AND
                    a.user_id = $3;
            `,
        [operation, deviceId, userId]
      );

      if (!device) {
        return next(new restify.errors.NotFoundError('Device not found')); // 404
      }

      if (!device.featureFlag) {
        return next(
          new restify.errors.ConflictError(
            'Device does not support tamper clear / factory reset challenge code'
          )
        ); // 409
      }

      req.log.info(`Generating challenge response for device ${deviceId}`);

      let responsePayload = null;
      let challengeResponseCode = null;

      try {
        try {
          challengeResponseCode = await generateChallengeResponse(
            tamperChallengeConfig.key,
            tamperChallengeConfig.user,
            tamperChallengeConfig.slot,
            serialNumber,
            challenge,
            operation
          );
        } catch (err) {
          sanitiseHSMError(
            err,
            tamperChallengeConfig.key,
            tamperChallengeConfig.user
          );
          req.log.error(err);
          return next(
            new restify.errors.InternalServerError(
              'An error occurred while generating the challenge response'
            )
          );
        } finally {
          /* tamper_challenge_response table is deprecated */
          // responsePayload = ( await server.db.write.execute( `
          //     INSERT INTO tamper_challenge_response ( id, device_id, requested_by, created, created_by, challenge, response )
          //     VALUES ( $1, $2, $3, NOW(), $4, $5, $6 )
          //     RETURNING id, requested_by, created, challenge, response;
          // `, [ uuid(), deviceId, requestedBy, userId, challenge, challengeResponseCode ] ) )[ 0 ];

          responsePayload = {
            requestedBy,
            challenge,
            response: challengeResponseCode,
            device: {
              id: deviceId,
              name: device.name,
              serialNumber: device.serialNumber,
            },
            createdBy: {
              id: userId,
              fullName: req.user.fullName,
              email: req.user.email,
            },
            targetSerialNumber: serialNumber,
            operation,
          };
        }

        if (!challengeResponseCode) {
          return next(
            new restify.errors.InternalServerError(
              'Unable to generate challenge response'
            )
          );
        }
      } catch (err) {
        req.log.error({ err });
        return next(new restify.errors.InternalServerError(err));
      }

      req.log.info(`Challenge response generated for device ${deviceId}`);
      res.send(200, responsePayload);
    } catch (err) {
      errorHandler.onError(req, res, next);
    }
    return next();
  },
};
