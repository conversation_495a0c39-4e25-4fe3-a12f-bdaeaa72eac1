// Implemented as per specs
// DSD-XXXXX-S1-R05-SpecificationAdditionalStateandErrorReporting.pdf

const restify = require('restify');

const AWS = require('../../lib/aws');
const { server } = require('../../app');
const { config } = require('../../env');
const deviceHelper = require('../../helpers/device-helper');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');

let stateConfig;

function evaluateTemplate(data) {
  const result = data;
  result.msg = result.mt;

  if (!result.mt) return result;

  const r = /\{([^}]+)\}/g;
  const placeholders = result.mt.match(r);
  placeholders.forEach(placeholder => {
    r.lastIndex = 0;
    const prop = r.exec(placeholder)[1];
    result.msg = result.msg.replace(placeholder, result[prop] || '');
  });

  return result;
}

function normaliseData(data) {
  const result = data;

  const levelMap = {
    st: 'notice',
    ev: 'info',
    mt: 'info',
  };

  if (!result.lv) {
    result.lv = levelMap[result.dt] || 'info';
  }

  const dtMap = {
    st: '{k} updated: {v}',
    ev: '{k}: {v}',
  };

  const dnMap = {
    counter: '{k}: {v} times in {ap} minutes',
    gauge: '{k}: {v} over {ap} minutes',
  };

  if (dtMap[result.dt]) {
    result.mt = dtMap[result.dt];
  } else {
    const dnMapKeys = Object.keys(dnMap);
    for (let i = 0; i < dnMapKeys.length; i++) {
      if (result.dn && result.dn.includes(dnMapKeys[i])) {
        result.mt = dnMap[dnMapKeys[i]];
        break;
      }
    }
  }

  result.v = result.dv;
  result.k = result.k || [result.ds, result.dn].join('.');
  result.fn = result.fn || [result.ds, result.dn].join('.');

  return result;
}

function filterLevels(levels) {
  return data => levels.includes(data.lv);
}

function filterQuery(q, advanced) {
  return data => {
    if (advanced) {
      return (
        [data.ds, data.dn].join('.').toLowerCase().includes(q) ||
        String(data.dv).toLowerCase().includes(q) ||
        data.msg.toLowerCase().includes(q)
      );
    }

    return data.msg.toLowerCase().includes(q);
  };
}

function processRules(c) {
  return data => {
    if (!c.rules) {
      throw new Error('No rules found in device state config');
    }

    const rule = c.rules[[data.ds, data.dn].join('.')];
    const { vmaps } = c;

    if (!rule) return data;

    const result = data;
    const { dv } = result;

    Object.keys(rule).forEach(prop => {
      if (prop[0] !== '#') {
        result[prop] = rule[prop];
      } else {
        switch (prop) {
          case '#vmap': {
            const vmap = rule[prop];
            const type = typeof vmap;

            if (type === 'string') {
              if (vmaps[vmap] && vmaps[vmap][dv]) {
                Object.assign(result, vmaps[vmap][dv]);
              }
            } else if (type === 'object') {
              if (vmap[dv]) {
                Object.assign(result, vmap[dv]);
              }
            }

            break;
          }
          case '#nmap': {
            const nmap = rule[prop];
            const type = typeof nmap;

            const intDv = parseInt(result.dv, 10) || null;

            if (type === 'string') {
              if (vmaps[nmap] && vmaps[nmap][intDv]) {
                Object.assign(result, vmaps[nmap][intDv]);
              }
            } else if (type === 'object') {
              if (nmap[intDv]) {
                Object.assign(result, nmap[intDv]);
              }
            }

            break;
          }
          case '#vmatch': {
            const vmatch = rule[prop];

            Object.keys(vmatch).forEach(exp => {
              const r = new RegExp(exp);

              if (r.test(dv)) {
                Object.assign(result, vmatch[exp]);
              }
            });

            break;
          }
          case '#nrange': {
            const nrange = rule[prop];

            Object.keys(nrange).forEach(range => {
              const low = range.split(',')[0] || 0;
              const high = range.split(',')[1] || 0;

              if (dv >= low && dv < high) {
                Object.assign(result, nrange[range]);
              }
            });

            break;
          }
          default:
            break;
        }
      }
    });

    return result;
  };
}

module.exports = {
  getDeviceStateHistory: async (req, res, next) => {
    try {
      // ICS-6064, ICS-7031
      // TODO: if 'DN_IGNORE_LIST' keeps growing, we should think about making it configurable.
      const DN_IGNORE_LIST = ['uploadable-files', 'device-offline'];
      const pagination = paginationHelper.parsePaginationParamsDDB(req);

      const userId = req.user.sub;
      const deviceId = req.params.id;

      const { levels } = req.query;
      const { start } = req.query;
      const { end } = req.query;
      const q = req.query.q || undefined;
      const advanced = req.query.advanced || false;

      const site = await server.db.read.row(
        `
                SELECT
                    s.site_id
                FROM target d
                    JOIN site s ON s.site_id = d.site_id 
                    JOIN user_site_authorization usa ON s.site_id = usa.site_id
                WHERE 
                    d.target_id = $1 AND
                    d.active AND 
                    s.active AND
                    usa.user_id = $2;
            `,
        [deviceId, userId]
      );

      if (!site) {
        return next(new restify.NotFoundError('Device not found'));
      }

      const params = {
        ExpressionAttributeValues: {
          ':deviceId': deviceId.toString(),
        },
        KeyConditionExpression: `
                    deviceId = :deviceId AND 
                    ${
                      end
                        ? 'ts_SK BETWEEN :ts_start_SK AND :ts_end_SK'
                        : 'ts_SK > :ts_start_SK'
                    }
                `,
        ProjectionExpression: 'ts, dt, ds, dn, dv, ap, lv, fn',
        TableName: config.AWS.DDB.deviceDataTable,
        ScanIndexForward: false,
        Limit: pagination.pageMinSize,
      };

      const last24Hours = Date.now() - 86400000;
      params.ExpressionAttributeValues[':ts_start_SK'] = start
        ? `${Math.max(last24Hours, new Date(start).getTime())}`
        : `${last24Hours}`;

      if (end) {
        params.ExpressionAttributeValues[':ts_end_SK'] = `${new Date(
          end
        ).getTime()}`;
      }

      if (pagination.pageKey) {
        params.ExclusiveStartKey = pagination.pageKey;
      }

      if (Array.isArray(DN_IGNORE_LIST) && DN_IGNORE_LIST.length > 0) {
        let dnIgnoreList = '';
        DN_IGNORE_LIST.forEach((elem, index) => {
          params.ExpressionAttributeValues[`:dn_to_be_ignored${index}`] = elem;
          dnIgnoreList += `:dn_to_be_ignored${index}`;
          if (index !== DN_IGNORE_LIST.length - 1) {
            dnIgnoreList += ', ';
          }
        });
        params.FilterExpression = `NOT (dn IN (${dnIgnoreList}))`;
      }

      if (!stateConfig) {
        stateConfig = deviceHelper.getDeviceStateHistoryConfig();
      }

      const queryResult = [];
      let data;

      do {
        // eslint-disable-next-line no-await-in-loop
        data = await AWS.DDBDC.query(params);

        if (data.LastEvaluatedKey) {
          params.ExclusiveStartKey = data.LastEvaluatedKey;
        }

        let dataProcessed = data.Items.map(normaliseData)
          .map(processRules(stateConfig))
          .map(evaluateTemplate);

        if (levels) {
          dataProcessed = dataProcessed.filter(filterLevels(levels));
        }

        if (q) {
          dataProcessed = dataProcessed.filter(filterQuery(q, advanced));
        }

        queryResult.push(...dataProcessed);
      } while (
        data.LastEvaluatedKey &&
        queryResult.length < pagination.pageMinSize
      );

      res.send({
        results: queryResult,
        resultsMetadata: {
          pageKey: data.LastEvaluatedKey,
          pageMinSize: pagination.pageMinSize,
        },
      });

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
