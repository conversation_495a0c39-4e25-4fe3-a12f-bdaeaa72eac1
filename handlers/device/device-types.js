const restify = require('restify');
const { server } = require('../../app');
const companyHelper = require('../../helpers/company-helper');

module.exports = {
  getDeviceTypes: async (req, res, next) => {
    const { serviceRecipientId } = req.query;
    const companyId = req.user.company.id;

    if (serviceRecipientId && serviceRecipientId !== companyId) {
      const canManage = await companyHelper.canManageServiceRecipient({
        serviceManagerId: companyId,
        serviceRecipientId,
      });
      if (!canManage) {
        return next(
          new restify.errors.ForbiddenError('Permission denied to access')
        );
      }
    }
    const queryCompanyId = serviceRecipientId || req.user.company.id;
    res.send(
      await server.db.read.rows(
        `
                SELECT
                  t.device_type as id,
                  c.display_name as name,
                  t.screen_width,
                  t.screen_height,
                  array_remove(array_agg(dff.feature_flag), NULL) as featureFlags,
                  t.scope
                FROM product_type t
                  INNER JOIN company_product_type c ON t.device_type = c.device_type
                  LEFT JOIN device_feature_flag dff ON t.device_type = dff.device_type
                WHERE t.active = TRUE 
                  AND c.company = $1
                GROUP BY t.device_type, c.display_name, t.screen_width, t.screen_height, t.priority_order, t.scope
                ORDER BY priority_order;
            `,
        [queryCompanyId]
      )
    );

    return next();
  },
};
