const restify = require('restify');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const { config } = require('../../env');
const AWS = require('../../lib/aws');

module.exports = {
  getDeviceMediaStats: async (req, res, next) => {
    try {
      const deviceId = req.params.id;
      const userId = req.user.sub;

      const site = await server.db.read.row(
        `
                    SELECT
                        s.site_id
                    FROM target d
                        JOIN site s ON s.site_id = d.site_id 
                        JOIN user_site_authorization usa ON s.site_id = usa.site_id
                    WHERE 
                    d.target_id = $1 AND
                    d.active AND 
                    s.active AND
                    usa.user_id = $2;
                 `,
        [deviceId, userId]
      );
      if (!site) {
        return next(new restify.NotFoundError('Device not found'));
      }

      const queryResult = [];
      // let count = 0;
      const params = {
        ExpressionAttributeValues: {
          ':deviceId': deviceId.toString(),
          ':event_ts_GSI_SK': `gstv-pop#${Date.now() - 86400000}`,
        },
        KeyConditionExpression:
          'deviceId = :deviceId AND event_ts_GSI_SK > :event_ts_GSI_SK',
        ProjectionExpression: 'dv',
        TableName: config.AWS.DDB.deviceDataTable,
        IndexName: 'deviceId-event_ts_GSI_SK-index',
        ScanIndexForward: false,
        Limit: 1000,
      };
      req.log.debug(`device-media: params = ${JSON.stringify(params)}`);
      let data;
      do {
        // eslint-disable-next-line no-await-in-loop
        data = await AWS.DDBDC.query(params);
        if (typeof data.LastEvaluatedKey !== 'undefined') {
          params.ExclusiveStartKey = data.LastEvaluatedKey;
        }
        queryResult.push(...data.Items);
      } while (typeof data.LastEvaluatedKey !== 'undefined');

      /* eslint-disable no-param-reassign, no-unsafe-finally */
      const reducedResult = queryResult.reduce((acc, queryResultItem) => {
        try {
          queryResultItem.dv = JSON.parse(queryResultItem.dv);
          if (
            queryResultItem.dv.filename &&
            queryResultItem.dv.startTimestamp &&
            queryResultItem.dv.endTimestamp
          ) {
            const start = Date.parse(queryResultItem.dv.startTimestamp);
            const end = Date.parse(queryResultItem.dv.endTimestamp);
            const playTime =
              Number.isNaN(start) || Number.isNaN(end) ? 0 : end - start;
            if (
              Object.prototype.hasOwnProperty.call(
                acc,
                queryResultItem.dv.filename
              )
            ) {
              acc[queryResultItem.dv.filename].count += 1;
              if (
                queryResultItem.dv.startTimestamp >
                acc[queryResultItem.dv.filename].lastPlayed
              ) {
                acc[queryResultItem.dv.filename].lastPlayed =
                  queryResultItem.dv.startTimestamp;
              }
              acc[queryResultItem.dv.filename].totalPlayTime += playTime;
            } else {
              acc[queryResultItem.dv.filename] = {
                count: 1,
                lastPlayed: queryResultItem.dv.startTimestamp,
                totalPlayTime: playTime,
              };
            }
          }
        } catch (err) {
          req.log.error(
            `Error '${err}' occurred in device-media: queryResultItem = ${JSON.stringify(
              queryResultItem
            )}`
          );
        } finally {
          return acc;
        }
      }, {});
      /* eslint-enable no-param-reassign, no-unsafe-finally */

      const result = [];
      const keys = Object.keys(reducedResult);
      for (let i = 0; i < keys.length; i++) {
        result.push({
          name: keys[i],
          count: reducedResult[keys[i]].count,
          totalPlayTime: reducedResult[keys[i]].totalPlayTime,
          lastPlayed: reducedResult[keys[i]].lastPlayed,
        });
      }

      res.send(result);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
