const _ = require('lodash');
const co = require('co');
const restify = require('restify');
const uuid = require('uuid/v4');

const hashPassword = require('../../lib/hash-password');
const jwt = require('../../lib/jwt');
const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const ipAddressHelper = require('../../helpers/ip-address-helper');
const deviceHelper = require('../../helpers/device-helper');

const FIND_DEVICES_SELECT = `
    SELECT
        t.target_id as device_id,
        t.serial_number,
        t.name as device_name,
        t.site_id,
        s.name as site_name,
        t.presence,
        get_device_health( target_id ) as status `;

function prepareFrom(hasTags, hasSiteGroups) {
  let from = `
        FROM target t
        JOIN site s ON s.site_id = t.site_id
        JOIN company c ON c.id = s.company_id
        LEFT JOIN company_product_type cpt ON cpt.device_type = t.device_type
        JOIN user_site_authorization u ON u.site_id = s.site_id`;

  if (hasTags) {
    from = `${from} 
        JOIN site_tag st ON st.site_id = s.site_id AND st.deleted = false
        JOIN tag tg ON tg.id = st.tag_id`;
  }

  if (hasSiteGroups) {
    from = `${from} 
        JOIN authorization_site_group_site asgs ON asgs.site_id=s.site_id 
        JOIN authorization_site_group asg ON asg.id=asgs.authorization_site_group_id`;
  }

  return from;
}

function prepareWhere({ userId, companyId, devices, sites, tags, siteGroups }) {
  const params = [];
  params.push(userId);
  let where = `
        WHERE t.active IS true
        AND t.delete_timestamp IS NULL
        AND t.presence != 'OUT_OF_INSTANCE'
        AND u.user_id = $${params.length} `;

  params.push(companyId);
  where = `${where} AND cpt.company = $${params.length} `;

  if (devices && devices.length > 0) {
    params.push(devices);
    // device serial numbers should not be case sensitive
    where = `${where} AND LOWER(t.serial_number) = ANY( $${params.length} ) `;
  }

  if (sites && sites.length > 0) {
    params.push(sites);
    where = `${where} AND s.site_id = ANY( $${params.length}::uuid[] ) `;
  }

  if (tags && tags.length > 0) {
    params.push(tags);
    where = `${where} AND tg.id = ANY( $${params.length} ) `;
  }

  if (siteGroups && siteGroups.length > 0) {
    params.push(siteGroups);
    where = `${where} AND asg.id = ANY( $${params.length}::uuid[] ) `;
  }

  return { params, where };
}

const FIND_DEVICES_GROUPBY = `
    GROUP BY t.target_id,
        t.name,
        t.serial_number,
        t.site_id,
        s.name,
        t.presence;`;

module.exports = {
  recommissionDevice: (req, res, next) =>
    co(function* execute() {
      const deviceId = req.params.id;
      const userId = req.user.sub;
      let result;
      const connection = yield server.db.write.getConnection();

      try {
        yield connection.execute('BEGIN');
        const { 0: firstRow } = (yield connection.execute(
          `
                UPDATE target
                SET
                    last_registered = NULL,
                    password        = NULL,
                    status          = 0,
                    last_contact    = NULL,
                    registration_window_end = NOW() + INTERVAL '20 years',
                    last_edited_date = NOW(),
                    updated_by = $1
                WHERE target.target_id IN (SELECT target_id
                            FROM target t
                                JOIN site s ON s.site_id = t.site_id
                                JOIN user_site_authorization usa
                                ON usa.site_id = s.site_id AND usa.user_id = $1
                            WHERE t.target_id = $2
                                AND t.active = TRUE AND t.delete_timestamp IS NULL AND t.presence != 'OUT_OF_INSTANCE')
                RETURNING registration_window_end AS window_end;
            `,
          [userId, deviceId]
        )).rows;
        result = firstRow;

        if (!result) {
          yield connection.execute('ROLLBACK');
          return next(
            new restify.errors.NotFoundError(
              `Cannot find device with id ${deviceId}`
            )
          );
        }

        yield deviceHelper.markDeviceStatesForDeletion([deviceId]);
        yield connection.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        return next(new restify.InternalServerError(err));
      } finally {
        connection.done();
      }

      res.send(result);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  recommissionDevices: async (req, res, next) => {
    const userId = req.user.sub;
    const companyId = req.user.company.id;
    /*
        {
            "dryRun": "boolean, default true",
            "devices": ["array of device serials"],
            "sites": ["array of site ids"],
            "tags": ["array of tag ids"],
            "siteGroups": ["array of sitegroups"]
        }
        */
    const { dryRun } = req.body;
    const { devices } = req.body;
    const { sites } = req.body;
    const { tags } = req.body;
    const { siteGroups } = req.body;

    try {
      // - return 400 if any of the devices/sites/tags/siteGroups filters array is empty or is undefined
      if (!devices && !sites && !tags && !siteGroups) {
        return next(
          new restify.errors.BadRequestError(
            'Filter arrays must contain at least 1 item.'
          )
        );
      }

      const hasFilters =
        (devices && devices.length > 0) ||
        (sites && sites.length > 0) ||
        (tags && tags.length > 0) ||
        (siteGroups && siteGroups.length > 0);
      if (!hasFilters) {
        return next(
          new restify.errors.BadRequestError(
            'Filter arrays must contain at least 1 item.'
          )
        );
      }

      const fromClause = prepareFrom(
        tags && tags.length > 0,
        siteGroups && siteGroups.length > 0
      );

      // device serial numbers should not be case sensitive
      const devicesLowerCased = devices
        ? devices.map(value => value.toLowerCase())
        : [];

      const whereClause = prepareWhere({
        userId,
        companyId,
        devices: devicesLowerCased,
        sites,
        tags,
        siteGroups,
      });

      const sql = `
                ${FIND_DEVICES_SELECT}
                ${fromClause}
                ${whereClause.where}
                ${FIND_DEVICES_GROUPBY}`;

      let result = null;

      // 1. get all the devices (filtered by devices, sites, tags and/or siteGroups)
      const devicesToRecommission = await server.db.read.rows(
        sql,
        whereClause.params
      );

      // 2. get the valid and excluded devices
      const validDevices = _.map(devicesToRecommission, item =>
        item.serialNumber.toUpperCase()
      );
      // to maintain the letter case of the items in excludedDevices (to display in UI)
      const excludedDevices = _.filter(
        devices,
        item => validDevices.indexOf(item.toUpperCase()) === -1
      );

      let bulkOperationId = null;

      // - return 404 if no device is found based on the parameters
      if (devicesToRecommission.length <= 0) {
        // Can't find the target device based on the specified filters
        return next(new restify.errors.NotFoundError('No devices found.'));
      }

      if (!dryRun) {
        if (devicesToRecommission.length > 0) {
          // if dryRun is set to TRUE, skip steps 3-5
          //   3. get the device ids from devicesToRecommission and put to an array
          const devicesArray = _.map(devicesToRecommission, 'deviceId');

          //   4. UPDATE the registration_window_end, ... values of the target devices from target table
          const connection = await server.db.write.getConnection();

          try {
            await connection.execute('BEGIN');
            const { 0: firstRow } = (
              await connection.execute(
                `
                            UPDATE target
                            SET
                                last_registered = NULL,
                                password        = NULL,
                                status          = 0,
                                last_contact    = NULL,
                                registration_window_end = NOW() + INTERVAL '20 years'
                            WHERE target.target_id = ANY( $1 )
                            RETURNING registration_window_end AS window_end;
                        `,
                [devicesArray]
              )
            ).rows;
            result = firstRow;

            // site state will be updated by alarm worker in the next cycle

            // INSERT INTO bulk_operation table
            bulkOperationId = uuid();
            await connection.execute(
              `
                            INSERT INTO bulk_operation ( id, type, created_by, schedule, excluded_devices )
                            VALUES ( $1, $2, $3, $4, $5 );
                        `,
              [
                bulkOperationId,
                'sys.recommission',
                userId,
                new Date(),
                excludedDevices,
              ]
            );

            let bulkOperationItemId = '';
            await Promise.all(
              devicesArray.map(deviceId => {
                bulkOperationItemId = uuid();
                return connection.execute(
                  `
                                    INSERT INTO bulk_operation_item ( id, bulk_operation_id, device_id, job_id, bulk_operation_status )
                                    VALUES ( $1, $2, $3, $4, $5 );
                                `,
                  [bulkOperationItemId, bulkOperationId, deviceId, null, 2]
                ); // default value for bulk_operation_status is 2=in-progress
              })
            );
            await deviceHelper.markDeviceStatesForDeletion(devicesArray);
            await connection.execute('COMMIT');
          } catch (err) {
            req.log.error({ err });
            await connection.execute('ROLLBACK');
            return next(new restify.InternalServerError(err));
          } finally {
            connection.done();
          }
        }
      }

      // 6. if successfully sent, return count of devices affected
      res.send(200, {
        validDevices,
        excludedDevices,
        result,
        bulkOperationId,
      });
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  registerDevice: async (req, res, next) => {
    const { registrationKey } = req.body;
    const secretKey = hashPassword.generatePlainPassword(30);

    try {
      const connection = await server.db.write.getConnection();
      let data = null;

      try {
        await connection.execute('BEGIN');

        const hashedPassword = await hashPassword.createHash(secretKey);

        const registrationResult =
          (
            await connection.execute(
              `
                    UPDATE target SET
                        password = $2,
                        registration_window_end = NULL,
                        last_registered = NOW(),
                        last_contact = NOW(),
                        ip_address = $3
                    WHERE 
                        registration_key = $1 AND
                        registration_window_end IS NOT NULL AND 
                        registration_window_end > NOW() AND 
                        active AND 
                        delete_timestamp IS NULL 
                    RETURNING target_id
                `,
              [
                registrationKey,
                hashedPassword,
                ipAddressHelper.getIPFromHeader(req),
              ]
            )
          ).rows || [];

        if (registrationResult.length === 0) {
          data = new restify.errors.NotFoundError();
        } else {
          // update value of bulk_operation_status and date_updated columns of
          // bulk_operation_item table to update the status of the bulk recommission record
          const bulkOperationItem =
            (
              await connection.execute(
                `
                        UPDATE bulk_operation_item SET
                            date_updated = NOW(),
                            bulk_operation_status = 3
                        WHERE
                            bulk_operation_status = 2 AND
                            device_id = $1 AND
                            date_updated IS NULL AND
                            job_id IS NULL
                        RETURNING id AS item_id, bulk_operation_id AS id
                    `,
                [registrationResult[0].targetId]
              )
            ).rows || [];

          data = {
            deviceId: registrationResult[0].targetId,
            secretKey,
            bulkOperation: bulkOperationItem[0],
          };
        }
        await connection.execute('COMMIT');

        res.send(data);

        return next();
      } catch (err) {
        req.log.error({ err });
        await connection.execute('ROLLBACK');
        return next(new restify.InternalServerError(err));
      } finally {
        connection.done();
      }
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  authenticate: (req, res, next) =>
    co(function* execute() {
      const { deviceId } = req.body;
      const { secretKey } = req.body;

      const deviceDetails = yield server.db.read.row(
        `SELECT
                t.password,
                s.company_id
            FROM target t
                JOIN site s ON t.site_id = s.site_id
            WHERE t.target_id = $1 AND t.active = TRUE`,
        [deviceId]
      );

      if (!deviceDetails) {
        res.send(401, 'Authentication failed');
        return next();
      }

      const passwordHash = deviceDetails.password;
      const { companyId } = deviceDetails;

      if (
        !passwordHash ||
        !(yield hashPassword.verify(secretKey, passwordHash))
      ) {
        res.send(401, 'Authentication failed');
        return next();
      }

      yield server.db.write.execute(
        'UPDATE target SET last_contact = NOW(), ip_address = $1 WHERE target_id = $2',
        [ipAddressHelper.getIPFromHeader(req), deviceId]
      );

      // TODO: Marcelo - How can I test that this still works ?
      const token = jwt.createDeviceToken(deviceId, companyId);
      res.send({ token });
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
