[{"deviceType": "G6-400", "dottedString": "invenco.system.g7opt.sdc-channel-status", "dv": "down"}, {"deviceType": "G6-500", "dottedString": "invenco.system.g7opt.sdc-channel-status", "dv": "down", "subPlatform": "ipt"}, {"deviceType": "G6-500", "dottedString": "invenco.system.g7opt.sdc-channel-status", "dv": "down", "subPlatform": "opt", "dualDisplayEnabled": "0"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.scc-channel-status", "dv": "down", "sysContactlessModule": "upc-config"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.scc-channel-status", "dv": "down", "sysContactlessModule": "upc-default"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.scc-tamper-mode", "dv": "safe", "sysContactlessModule": "upc-config"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.scc-tamper-mode", "dv": "safe", "sysContactlessModule": "upc-default"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.scc-tamper-status", "dv": "safe", "sysContactlessModule": "upc-config"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.scc-tamper-status", "dv": "safe", "sysContactlessModule": "upc-default"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.scc-tamper-status", "dv": "tampered", "sysContactlessModule": "upc-config"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.scc-tamper-status", "dv": "tampered", "sysContactlessModule": "upc-default"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.upc-channel-status", "dv": "down", "sysContactlessModule": "scc-config"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.upc-channel-status", "dv": "down", "sysContactlessModule": "scc-default"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.upc-tamper-status", "dv": "separated", "sysContactlessModule": "scc-default"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.upc-tamper-status", "dv": "separated", "sysContactlessModule": "scc-config"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.upc-tamper-status", "dv": "tampered", "sysContactlessModule": "scc-default"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.upc-tamper-status", "dv": "tampered", "sysContactlessModule": "scc-config"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.upc-tamper-mode", "dv": "safe", "sysContactlessModule": "scc-config"}, {"deviceType": "G7-100", "dottedString": "invenco.system.g7opt.upc-tamper-mode", "dv": "safe", "sysContactlessModule": "scc-default"}]