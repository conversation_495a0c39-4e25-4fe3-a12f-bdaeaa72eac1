const restify = require('restify');
const _ = require('lodash');
const env = require('../../env');

const deviceHelper = require('../../helpers/device-helper');
const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');

const enableBluefin = env.config.bluefin && env.config.bluefin.enable;
const BLUEFIN_DEVICE_RECORD_SELECT = enableBluefin
  ? `, c.default_site as from_default_site, s.company_id as current_company,json_object_agg(bf.key, bf.value) FILTER (WHERE bf.key IS NOT NULL) AS bluefin_config_data`
  : '';

const BLUEFIN_DEVICE_RECORD_JOIN = enableBluefin
  ? `LEFT JOIN site s ON s.site_id = t.site_id   
      LEFT JOIN company c on c.default_site  = t.site_id 
      LEFT JOIN bluefin_config bf ON bf.target_id = t.target_id AND bf.key IS NOT NULL`
  : '';

const BLUEFIN_DEVICE_RECORD_GROUPBY = enableBluefin
  ? `GROUP BY 
                        t.*, t.target_id ,
                        c.default_site, 
                        s.company_id`
  : '';

const BLUEFIN_DEVICE_QUERY_GROUPBY = enableBluefin
  ? `GROUP BY 
                    t.target_id,
                    t.serial_number, 
                    t.device_type, 
                    t.site_id, 
                    t.key_group_ref, 
                    t.presence, 
                    t.deployment_type,
                    s.company_id,
                    c.default_site`
  : '';
const BLUEFIN_COMPANY_FLAG_JOIN = enableBluefin
  ? `LEFT JOIN company_feature_flag cff on cff.company = s.company_id and cff.feature_flag = 'BLUEFIN'`
  : '';
const BLUEFIN_COMPANY_FLAG_SELECT = enableBluefin
  ? `, cff.feature_flag as bluefin_flag, s.bluefin_location_id as location_id, c.kif, c.default_site as to_default_site`
  : '';

const BLUEFIN_COMPANY_KIF_JOIN = enableBluefin
  ? `LEFT JOIN company c on c.id = s.company_id `
  : '';

const BLUEFIN_COMPANY_FLAG_GROUP_BY = enableBluefin ? `, cff.feature_flag` : '';

module.exports = {
  moveToSite: async (req, res, next) => {
    try {
      const isSystemUser = req.user.roles.includes('ICS_SYSTEM');
      const userId = req.user.sub;
      const { deploymentType, siteId, devices, meta } = req.body;
      const siteParams = [siteId];
      const errorMessages = [];
      const deviceTypes = [];
      const activeDevices = [];
      const devicesToMove = [];
      const moveResult = { succeeded: [], failed: [], ignored: [] };
      const action = 'moveToSite';

      if (!isSystemUser) {
        siteParams.push(userId);
      }

      const newSite = await server.db.read.row(
        `
                SELECT s.company_id, s.site_id, s.timezone_id, s.name,
                       k.key_group_ref
                       ${BLUEFIN_COMPANY_FLAG_SELECT}
                FROM site s
                ${
                  isSystemUser
                    ? ''
                    : 'JOIN user_site_authorization a ON a.site_id = s.site_id'
                }
                LEFT JOIN key_group k ON k.key_group_id = s.key_group_id
                ${BLUEFIN_COMPANY_FLAG_JOIN} ${BLUEFIN_COMPANY_KIF_JOIN}
                WHERE
                    s.active AND
                ${
                  isSystemUser
                    ? 's.site_id = $1'
                    : 'a.site_id = $1 AND a.user_id = $2;'
                }
            `,
        siteParams
      );

      if (!newSite) {
        return next(
          new restify.NotFoundError(
            `Site (${siteId}) not found or user has no permission`
          )
        );
      }

      for (let i = 0; i < devices.length; i++) {
        const device = devices[i];

        if (!device.id && !device.serialNumber) {
          return next(
            new restify.BadRequestError(
              'Either device id or serialNumber must be provided'
            )
          );
        }

        const deviceParams = [
          req.body.siteId,
          device.serialNumber ? device.serialNumber.toUpperCase() : device.id,
        ];
        if (!isSystemUser) {
          deviceParams.push(req.user.sub);
        }
        // eslint-disable-next-line no-await-in-loop
        const deviceRecord = await server.db.read.row(
          `
                    SELECT t.presence, t.serial_number, t.device_type, t.site_id, t.target_id, t.key_group_ref
                          ${BLUEFIN_DEVICE_RECORD_SELECT}
                    FROM target t
                    ${BLUEFIN_DEVICE_RECORD_JOIN}
                        ${isSystemUser ? '' : ', user_site_authorization a'}
                    WHERE
                        t.active = TRUE AND
                        ( t.presence != 'OUT_OF_INSTANCE' OR
                        ( t.presence = 'OUT_OF_INSTANCE' AND t.site_id = $1 ) ) AND
                        ${
                          device.serialNumber
                            ? 't.serial_number = $2'
                            : 't.target_id = $2'
                        }
                        ${
                          isSystemUser
                            ? ''
                            : 'AND a.site_id = t.site_id AND a.user_id = $3'
                        }
                   ${BLUEFIN_DEVICE_RECORD_GROUPBY}
                `,
          deviceParams
        );

        if (!deviceRecord) {
          errorMessages.push(
            `Device (${
              device.serialNumber ? device.serialNumber : device.id
            }) not found or user has no permission`
          );
        } else if (deviceRecord.presence !== 'OUT_OF_INSTANCE') {
          req.log.info(`Adding device ${deviceRecord.serialNumber} to list`);
          activeDevices.push(deviceRecord);
          if (!deviceTypes.includes(deviceRecord.deviceType)) {
            deviceTypes.push(deviceRecord.deviceType);
          }
        }

        if (
          newSite.bluefinFlag &&
          !deviceRecord.fromDefaultSite &&
          newSite.siteId !== newSite.toDefaultSite
        ) {
          return next(
            new restify.BadRequestError(
              `Device can only be moved to the default site`
            )
          );
        }
      }

      const companyProductTypes = (
        await server.db.read.rows(
          `
                SELECT cpt.device_type
                FROM company_product_type cpt
                WHERE cpt.company = $1;
            `,
          [newSite.companyId]
        )
      ).map(item => item.deviceType);

      for (let i = 0; i < deviceTypes.length; i++) {
        let deviceType = deviceTypes[i];

        if (!companyProductTypes.includes(deviceType)) {
          if (!isSystemUser) {
            // eslint-disable-next-line no-await-in-loop
            const originCompanyProductType = await server.db.read.row(
              `
                            SELECT cpt.display_name
                            FROM company_product_type cpt
                            WHERE
                                cpt.company = $1 AND
                                cpt.device_type = $2;
                        `,
              [req.user.company.id, deviceType]
            );

            if (
              originCompanyProductType &&
              originCompanyProductType.displayName
            ) {
              deviceType = originCompanyProductType.displayName;
            }
          }

          return next(
            new restify.UnprocessableEntityError(
              `Device type ${deviceType} is not supported by destination site owner.`
            )
          );
        }
      }

      if (errorMessages.length) {
        res.send(404, { msg: errorMessages });
        return next();
      }

      for (let i = 0; i < activeDevices.length; i++) {
        if (activeDevices[i].siteId !== newSite.siteId) {
          devicesToMove.push({
            id: activeDevices[i].targetId.toString(),
            serialNumber: activeDevices[i].serialNumber,
            keyGroupRef: activeDevices[i].keyGroupRef,
            companyId: newSite.companyId,
            fromSiteId: activeDevices[i].siteId,
            toSiteId: newSite.siteId,
            deploymentType,
            meta,
            timezoneId: newSite.timezoneId,
            currentDeviceCompany: activeDevices[i].currentCompany,
            fromDefaultSite: activeDevices[i].fromDefaultSite,
            bluefinFlag: newSite.bluefinFlag,
            bluefinConfigData: activeDevices[i].bluefinConfigData,
            siteName: newSite.name,
            locationId: newSite.locationId,
            toDefaultSite: newSite.toDefaultSite,
            kif: newSite.kif,
          });
        } else {
          moveResult.ignored.push(activeDevices[i].targetId.toString());
        }
      }

      if (devicesToMove.length > 0) {
        Object.assign(
          moveResult,
          await deviceHelper.moveDevices(devicesToMove, userId, action)
        );
      }
      return res.send(200, moveResult);
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },

  moveToCompany: async (req, res, next) => {
    try {
      const isSystemUser = req.user.roles.includes('ICS_SYSTEM');
      const userId = req.user.sub;
      const payload = req.body;
      const deviceSerialNumbers = [];
      const companyRefs = [];
      const devicesToMove = [];
      const moveResult = { succeeded: [], failed: [], ignored: [] };
      const action = 'moveToCompany';

      for (let i = 0; i < payload.length; i++) {
        const { companyRef } = payload[i];
        const serialNumber = payload[i].serialNumber.toUpperCase();

        if (!companyRefs.includes(companyRef)) {
          companyRefs.push(companyRef);
        }

        if (!deviceSerialNumbers.includes(serialNumber)) {
          deviceSerialNumbers.push(serialNumber);
        } else {
          return next(
            new restify.errors.BadRequestError(
              `Duplication of serial number ${serialNumber} detected`
            )
          );
        }
      }

      const companyParams = [companyRefs];
      if (!isSystemUser) {
        companyParams.push(userId);
      }
      const queriedCompanies = await server.db.read.rows(
        `
                SELECT c.id, c.name, c.reference, s.site_id, s.timezone_id, k.key_group_ref, s.name as site_name,
                    array_remove(array_agg(cpt.device_type), NULL) as device_types
                    ${BLUEFIN_COMPANY_FLAG_SELECT}
                FROM company c
                    JOIN site s ON s.site_id = c.default_site
                    ${
                      isSystemUser
                        ? ''
                        : `
                        JOIN user_site_authorization a ON
                            a.site_id = s.site_id AND
                            a.user_id = $2
                    `
                    }
                    LEFT JOIN key_group k ON k.key_group_id = s.key_group_id
                    LEFT JOIN company_product_type cpt ON cpt.company = c.id
                    ${BLUEFIN_COMPANY_FLAG_JOIN}
                WHERE
                    c.reference = ANY( $1 ) GROUP BY c.id, c.name, c.reference, s.site_id, k.key_group_ref ${BLUEFIN_COMPANY_FLAG_GROUP_BY} ;
            `,
        companyParams
      );

      if (queriedCompanies.length !== companyRefs.length) {
        // Haven't found one or more companies
        const queriedCompanyRefs = _.map(queriedCompanies, 'reference');
        const difference = _.difference(companyRefs, queriedCompanyRefs);

        if (difference.length > 0) {
          return next(
            new restify.errors.BadRequestError(
              `Company ${difference[0]} stock site not found`
            )
          );
        }
      }

      const deviceParams = [deviceSerialNumbers];
      if (!isSystemUser) {
        deviceParams.push(req.user.sub);
      }
      const queriedDevices = await server.db.read.rows(
        `
                SELECT t.target_id, t.serial_number, t.device_type, t.site_id, t.key_group_ref, t.presence, t.deployment_type
                ${BLUEFIN_DEVICE_RECORD_SELECT}
                FROM target t
                ${BLUEFIN_DEVICE_RECORD_JOIN}
                    ${
                      isSystemUser
                        ? ''
                        : `
                        JOIN user_site_authorization a ON
                            a.site_id = t.site_id AND
                            a.user_id = $2`
                    }
                WHERE
                    t.serial_number = ANY( $1 ) AND
                    t.active = TRUE
                    ${BLUEFIN_DEVICE_QUERY_GROUPBY}
            `,
        deviceParams
      );

      if (queriedDevices.length !== deviceSerialNumbers.length) {
        // Haven't found one or more devices
        const queriedSerialNumber = _.map(queriedDevices, 'serialNumber');
        const difference = _.difference(
          deviceSerialNumbers,
          queriedSerialNumber
        );

        if (difference.length > 0) {
          return next(
            new restify.errors.BadRequestError(
              `Device with serial number ${difference[0]} not found`
            )
          );
        }
      }

      for (let i = 0; i < payload.length; i++) {
        const data = payload[i];

        const company = queriedCompanies.find(
          c => c.reference === data.companyRef
        );
        const device = queriedDevices.find(
          d => d.serialNumber === data.serialNumber.toUpperCase()
        );

        const deviceIsOutOfInstance = device.presence === 'OUT_OF_INSTANCE';
        const deviceIsAtDestinationSite = device.siteId === company.siteId;

        if (deviceIsOutOfInstance && !deviceIsAtDestinationSite) {
          return next(
            new restify.errors.BadRequestError(
              `Device ${device.serialNumber} is out-of-instance`
            )
          );
        }

        if (!company.deviceTypes.includes(device.deviceType)) {
          let { deviceType } = device;

          if (!isSystemUser) {
            // eslint-disable-next-line no-await-in-loop
            const originCompanyProductType = await server.db.read.row(
              `
                            SELECT cpt.display_name
                            FROM company_product_type cpt
                            WHERE
                                cpt.company = $1 AND
                                cpt.device_type = $2;
                        `,
              [req.user.company.id, deviceType]
            );

            if (
              originCompanyProductType &&
              originCompanyProductType.displayName
            ) {
              deviceType = originCompanyProductType.displayName;
            }
          }

          return next(
            new restify.UnprocessableEntityError(
              `Device type ${deviceType} is not supported by destination site owner ${company.reference}.`
            )
          );
        }

        if (device.siteId !== company.siteId) {
          // Device has moved
          req.log.info(`Prepare to move device ${device.serialNumber}
                        from company ${data.companyRef} old site ${device.siteId}
                        to new company ${company.name} stock site ${company.siteId}`);
          devicesToMove.push({
            id: device.targetId.toString(),
            serialNumber: device.serialNumber,
            keyGroupRef: device.keyGroupRef,
            companyId: company.id,
            fromSiteId: device.siteId,
            toSiteId: company.siteId,
            deploymentType: device.deploymentType,
            timezoneId: company.timezoneId,
            currentDeviceCompany: device.currentCompany,
            fromDefaultSite: device.fromDefaultSite,
            bluefinFlag: company.bluefinFlag,
            bluefinConfigData: device.bluefinConfigData,
            siteName: company.siteName,
            locationId: company.locationId,
            kif: company.kif,
            toDefaultSite: company.toDefaultSite,
          });
        } else {
          moveResult.ignored.push(device.targetId.toString());
        }
      }
      if (devicesToMove.length > 0) {
        Object.assign(
          moveResult,
          await deviceHelper.moveDevices(devicesToMove, userId, action)
        );
      }
      res.send(200, moveResult);
      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },
};
