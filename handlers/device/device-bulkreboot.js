const _ = require('lodash');
const restify = require('restify');
const moment = require('moment');
const uuid = require('uuid/v4');

const AWS = require('../../lib/aws');
const env = require('../../env');
const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const logger = require('../../lib/logger').mainLogger();

const FIND_DEVICES_SELECT = `
    SELECT
        t.target_id as device_id,
        t.serial_number,
        t.name as device_name,
        t.site_id,
        s.name as site_name,
        t.presence,
        get_device_health( target_id ) as status `;

function prepareFrom(hasTags, hasSiteGroups) {
  let from = `
        FROM target t
        JOIN site s ON s.site_id = t.site_id
        JOIN company c ON c.id = s.company_id
        LEFT JOIN company_product_type cpt ON cpt.device_type = t.device_type
        JOIN user_site_authorization u ON u.site_id = s.site_id`;

  if (hasTags) {
    from = `${from} 
        JOIN site_tag st ON st.site_id = s.site_id AND st.deleted = false
        JOIN tag tg ON tg.id = st.tag_id`;
  }

  if (hasSiteGroups) {
    from = `${from} 
        JOIN authorization_site_group_site asgs ON asgs.site_id=s.site_id 
        JOIN authorization_site_group asg ON asg.id=asgs.authorization_site_group_id`;
  }

  return from;
}

function prepareWhere({
  userId /* required */,
  companyId /* required */,
  devices,
  sites,
  tags,
  siteGroups,
}) {
  const params = [];
  params.push(userId);
  let where = `
        WHERE t.active IS true 
        AND u.user_id = $${params.length} `;

  params.push(companyId);
  where = `${where} AND cpt.company = $${params.length} `;

  if (devices && devices.length > 0) {
    params.push(devices);
    // device serial numbers should not be case sensitive
    where = `${where} AND LOWER(t.serial_number) = ANY( $${params.length} ) `;
  }

  if (sites && sites.length > 0) {
    params.push(sites);
    where = `${where} AND s.site_id = ANY( $${params.length}::uuid[] ) `;
  }

  if (tags && tags.length > 0) {
    params.push(tags);
    where = `${where} AND tg.id = ANY( $${params.length} ) `;
  }

  if (siteGroups && siteGroups.length > 0) {
    params.push(siteGroups);
    where = `${where} AND asg.id = ANY( $${params.length}::uuid[] ) `;
  }

  return { params, where };
}

const FIND_DEVICES_GROUPBY = `
    GROUP BY t.target_id,
        t.name,
        t.serial_number,
        t.site_id,
        s.name,
        t.presence
    ORDER BY t.target_id;`;

module.exports = {
  rebootDevices: async (req, res, next) => {
    const userId = req.user.sub;
    const companyId = req.user.company.id;
    /*
        {
            "dryRun": "boolean, default true",
            "devices": ["array of device serials"],
            "sites": ["array of site ids"],
            "tags": ["array of tag ids"],
            "siteGroups": ["array of sitegroups"],
            "schedule": "datetime in epoch/unix time format"
        }
        */
    const { dryRun } = req.body;
    const { devices } = req.body;
    const { sites } = req.body;
    const { tags } = req.body;
    const { siteGroups } = req.body;
    const schedule = !req.body.schedule
      ? null
      : new Date(parseInt(req.body.schedule, 10));

    try {
      // - return 400 if any of the devices/sites/tags/siteGroups filters array is empty or is undefined
      if (!devices && !sites && !tags && !siteGroups) {
        return next(
          new restify.errors.BadRequestError(
            'Filter arrays must contain at least 1 item.'
          )
        );
      }

      const hasFilters =
        (devices && devices.length > 0) ||
        (sites && sites.length > 0) ||
        (tags && tags.length > 0) ||
        (siteGroups && siteGroups.length > 0);
      if (!hasFilters) {
        return next(
          new restify.errors.BadRequestError(
            'Filter arrays must contain at least 1 item.'
          )
        );
      }

      const fromClause = prepareFrom(
        tags && tags.length > 0,
        siteGroups && siteGroups.length > 0
      );

      // device serial numbers should not be case sensitive
      const devicesLowerCased = devices
        ? devices.map(value => value.toLowerCase())
        : [];

      const whereClause = prepareWhere({
        userId,
        companyId,
        devices: devicesLowerCased,
        sites,
        tags,
        siteGroups,
      });

      const sql = `
                ${FIND_DEVICES_SELECT}
                ${fromClause}
                ${whereClause.where}
                ${FIND_DEVICES_GROUPBY}`;

      // 1. get all the devices (filtered by sites, tags and/or siteGroups)
      const devicesToReboot = await server.db.read.rows(
        sql,
        whereClause.params
      );

      // 2. get the count of all devices
      const totalDevices = devicesToReboot.length;

      // - return 404 if no device is found based on the parameters
      if (totalDevices <= 0) {
        // Can't find the target device based on the specified filters
        return next(new restify.errors.NotFoundError('No devices found.'));
      }

      server.log.info(`Sending job messages to reboot ${totalDevices} devices`);

      // - return 400 if schedule timestamp is before the current time's timestamp
      const dateNow = new Date();
      if (schedule && dateNow.getTime() > schedule.getTime()) {
        return next(
          new restify.errors.BadRequestError(
            'Schedule timestamp value must be ahead of the current timestamp.'
          )
        );
      }

      const validDevices = _.map(devicesToReboot, item =>
        item.serialNumber.toUpperCase()
      );
      // to maintain the letter case of the items in excludedDevices (to display in UI)
      const excludedDevices = _.filter(
        devices,
        item => validDevices.indexOf(item.toUpperCase()) === -1
      );

      if (!dryRun) {
        // if dryRun is set to TRUE, skip steps 3-5
        //   3. group devices by 10
        //   4. prepare the job message params
        //   5. use AWS.sendSqsMessageBatch() to send the batch of reboot job messages to SQS job queue
        const connection = await server.db.write.getConnection();
        try {
          if (totalDevices > 0) {
            await connection.execute('BEGIN');
            // INSERT INTO bulk_operation table
            const bulkOperationId = uuid();
            const scheduledAt = !schedule ? new Date() : schedule; // if immediately invoked reboot job, record the current date
            await connection.execute(
              `
                            INSERT INTO bulk_operation ( id, type, created_by, schedule, excluded_devices )
                            VALUES ( $1, $2, $3, $4, $5 );
                        `,
              [
                bulkOperationId,
                'sys.reboot',
                userId,
                scheduledAt,
                excludedDevices,
              ]
            );

            // Prepare data for bulk insert into bulk_operation_items
            const batchSize = env.config.bulkDeviceDataInsertionBatchSize;
            logger.info(`batchSize- ${batchSize}`);
            const deviceChunks = _.chunk(devicesToReboot, batchSize);

            // eslint-disable-next-line no-restricted-syntax
            for (const chunk of deviceChunks) {
              const params = [];
              const values = _.map(chunk, (device, index) => {
                const bulkOperationItemId = uuid();
                params.push(
                  bulkOperationItemId,
                  bulkOperationId,
                  device.deviceId,
                  null
                );

                return `($${index * 4 + 1}, $${index * 4 + 2}, $${index * 4 + 3}, $${index * 4 + 4})`;
              });

              const query = `
                INSERT INTO bulk_operation_item (id, bulk_operation_id, device_id, job_id)
                VALUES ${values.join(', ')}`;

              // eslint-disable-next-line no-await-in-loop
              await connection.execute(query, params);
            }
            await connection.execute('COMMIT');
            await Promise.all(
              _.chunk(devicesToReboot, 10).map(batchOfDevices => {
                const msgParams = {
                  QueueUrl: env.config.jobs.queue.url,
                  Entries: batchOfDevices.map((device, idx) => {
                    const MessageAttributes = {
                      'device-id': {
                        DataType: 'String',
                        StringValue: `${device.deviceId}`,
                      },
                      destination: {
                        DataType: 'String',
                        StringValue: 'invenco.system',
                      },
                      'user-id': {
                        DataType: 'String',
                        StringValue: userId,
                      },
                      expiry: {
                        DataType: 'String',
                        StringValue: `${+moment().add(2, 'hours')}`,
                      },
                      type: {
                        DataType: 'String',
                        StringValue: 'sys.reboot',
                      },
                      'bulk-operation-id': {
                        DataType: 'String',
                        StringValue: bulkOperationId,
                      },
                    };
                    if (schedule) {
                      MessageAttributes.embargo = {
                        DataType: 'String',
                        StringValue: schedule.toISOString(),
                      };
                      const expiryDate = new Date(schedule);
                      expiryDate.setHours(expiryDate.getHours() + 2); // add 2hrs expiry time based on embargo time
                      MessageAttributes.expiry = {
                        DataType: 'String',
                        StringValue: `${expiryDate.getTime()}`,
                      };
                    }
                    return {
                      Id: `${idx}`,
                      MessageAttributes,
                      MessageBody: JSON.stringify({}),
                    };
                  }),
                };

                return AWS.sendSqsMessageBatch(msgParams);
              })
            );
          }
        } catch (err) {
          await connection.execute('ROLLBACK');
          return next(new restify.errors.InternalServerError(err));
        } finally {
          connection.done();
        }
      }

      // 6. if successfully sent,
      // return array of device serial numbers of valid devices/sent reboot jobs
      // return array of device serial numbers excluded from reboot job
      res.send(200, {
        validDevices,
        excludedDevices,
      });
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
