{"dataIngestionRules": {"validCompanies": {"invenco": "invenco", "ncr": "ncr", "igl": "igl"}, "regex": "^[a-z0-9_\\\\.-]+$", "translations": {"invenco.system.payments.security.decryption-error": "igl.eps.security.alert-decryption-error", "invenco.system.payments.metric.msg-type.advice-epay-approved.counter.sum": "igl.eps.epay.msg-type-advice-approved.counter.sum", "invenco.system.payments.metric.msg-type.advice-fdi-approved.counter.sum": "igl.eps.fdi.msg-type-advice-approved.counter.sum", "invenco.system.payments.metric.msg-type.advice-paymark-approved.counter.sum": "igl.eps.paymark.msg-type-advice-approved.counter.sum", "invenco.system.payments.metric.msg-type.advice-pdb-approved.counter.sum": "igl.eps.pdb.msg-type-advice-approved.counter.sum", "invenco.system.payments.metric.msg-type.advice-petron-approved.counter.sum": "igl.eps.petron.msg-type-advice-approved.counter.sum", "invenco.system.payments.metric.msg-type.create-request-centrapay-approved.counter.sum": "igl.eps.centrapay.msg-type-create-request-approved.counter.sum", "invenco.system.payments.metric.msg-type.fileupdate-paymark-approved.counter.sum": "igl.eps.paymark.msg-type-fileupdate-approved.counter.sum", "invenco.system.payments.metric.msg-type.logon-fdi-approved.counter.sum": "igl.eps.fdi.msg-type-logon-approved.counter.sum", "invenco.system.payments.metric.msg-type.logon-paymark-approved.counter.sum": "igl.eps.paymark.msg-type-logon-approved.counter.sum", "invenco.system.payments.metric.msg-type.logon-pdb-approved.counter.sum": "igl.eps.pdb.msg-type-logon-approved.counter.sum", "invenco.system.payments.metric.msg-type.logon-petron-approved.counter.sum": "igl.eps.petron.msg-type-logon-approved.counter.sum", "invenco.system.payments.metric.msg-type.postauth-fdi-approved.counter.sum": "igl.eps.fdi.msg-type-postauth-approved.counter.sum", "invenco.system.payments.metric.msg-type.postauth-pdb-approved.counter.sum": "igl.eps.pdb.msg-type-postauth-approved.counter.sum", "invenco.system.payments.metric.msg-type.preauth-epay-approved.counter.sum": "igl.eps.epay.msg-type-preauth-approved.counter.sum", "invenco.system.payments.metric.msg-type.preauth-fdi-approved.counter.sum": "igl.eps.fdi.msg-type-preauth-approved.counter.sum", "invenco.system.payments.metric.msg-type.preauth-paymark-approved.counter.sum": "igl.eps.paymark.msg-type-preauth-approved.counter.sum", "invenco.system.payments.metric.msg-type.preauth-paymark-declined.counter.sum": "igl.eps.paymark.msg-type-preauth-declined.counter.sum", "invenco.system.payments.metric.msg-type.preauth-pdb-approved.counter.sum": "igl.eps.pdb.msg-type-preauth-approved.counter.sum", "invenco.system.payments.metric.msg-type.preauth-petron-approved.counter.sum": "igl.eps.petron.msg-type-preauth-approved.counter.sum", "invenco.system.payments.metric.msg-type.refund-centrapay-approved.counter.sum": "igl.eps.centrapay.msg-type-refund-approved.counter.sum", "invenco.system.payments.metric.msg-type.reversal-fdi-approved.counter.sum": "igl.eps.fdi.msg-type-reversal-approved.counter.sum", "invenco.system.payments.metric.msg-type.reversal-paymark-approved.counter.sum": "igl.eps.paymark.msg-type-reversal-approved.counter.sum", "invenco.system.payments.metric.msg-type.reversal-petron-approved.counter.sum": "igl.eps.petron.msg-type-reversal-approved.counter.sum", "invenco.system.payments.metric.msg-type.settlement-fdi-approved.counter.sum": "igl.eps.fdi.msg-type-settlement-approved.counter.sum", "invenco.system.payments.metric.msg-type.settlement-paymark-approved.counter.sum": "igl.eps.paymark.msg-type-settlement-approved.counter.sum", "invenco.system.payments.metric.msg-type.settlement-paymark-declined.counter.sum": "igl.eps.paymark.msg-type-settlement-declined.counter.sum", "invenco.system.payments.metric.msg-type.tcupload-pdb-approved.counter.sum": "igl.eps.pdb.msg-type-tcupload-approved.counter.sum", "invenco.system.payments.metric.msg-type.tcupload-petron-approved.counter.sum": "igl.eps.petron.msg-type-tcupload-approved.counter.sum", "invenco.system.payments.metric.msg-type.void-centrapay-approved.counter.sum": "igl.eps.centrapay.msg-type-void-approved.counter.sum", "invenco.system.payments.metric.trans-type.centrapay-paid.counter.sum": "igl.eps.centrapay.trans-type-paid.counter.sum", "invenco.system.payments.metric.trans-type.contact-icc-fdi-advice.counter.sum": "igl.eps.fdi.trans-type-contact-icc-advice.counter.sum", "invenco.system.payments.metric.trans-type.contact-icc-paymark-advice.counter.sum": "igl.eps.paymark.trans-type-contact-icc-advice.counter.sum", "invenco.system.payments.metric.trans-type.contact-icc-pdb-advice.counter.sum": "igl.eps.pdb.trans-type-contact-icc-advice.counter.sum", "invenco.system.payments.metric.trans-type.contact-icc-petron-advice.counter.sum": "igl.eps.petron.trans-type-contact-icc-advice.counter.sum", "invenco.system.payments.metric.trans-type.contactless-icc-fdi-advice.counter.sum": "igl.eps.fdi.trans-type-contactless-icc-advice.counter.sum", "invenco.system.payments.metric.trans-type.contactless-icc-paymark-advice.counter.sum": "igl.eps.paymark.trans-type-contactless-icc-advice.counter.sum", "invenco.system.payments.metric.trans-type.contactless-icc-pdb-post.counter.sum": "igl.eps.pdb.trans-type-contactless-icc-post.counter.sum", "invenco.system.payments.metric.trans-type.contactless-icc-petron-advice.counter.sum": "igl.eps.petron.trans-type-contactless-icc-advice.counter.sum", "invenco.system.payments.metric.trans-type.fall-back-paymark-advice.counter.sum": "igl.eps.paymark.trans-type-fall-back-advice.counter.sum", "invenco.system.payments.metric.trans-type.mag-strip-epay-advice.counter.sum": "igl.eps.epay.trans-type-mag-strip-advice.counter.sum", "invenco.system.payments.metric.trans-type.mag-strip-fdi-advice.counter.sum": "igl.eps.fdi.trans-type-mag-strip-advice.counter.sum", "invenco.system.payments.metric.trans-type.mag-strip-paymark-advice.counter.sum": "igl.eps.paymark.trans-type-mag-strip-advice.counter.sum", "invenco.system.payments.g7opt.transaction-amount.counter.sum": "igl.eps.main.transaction-amount.counter.sum", "invenco.system.payments.g7opt.transaction-count.counter.sum": "igl.eps.main.transaction-count.counter.sum", "invenco.system.payments.eps.reversal-stalled": "igl.eps.main.alert-reversal-stalled", "invenco.system.payments.eps.saf-clear": "igl.eps.main.alert-saf-clear", "invenco.system.payments.eps.saf-reversal-invalid": "igl.eps.main.alert-saf-reversal-invalid", "invenco.system.payments.eps.saf-stalled": "igl.eps.main.alert-saf-stalled", "invenco.system.payments.eps.settlement-failed": "igl.eps.main.alert-settlement-failed", "invenco.system.payments.eps.terminal-disconnected": "igl.eps.main.alert-terminal-disconnected", "invenco.system.payments.eps.terminal-offline": "igl.eps.main.alert-terminal-offline", "invenco.system.payments.eps.transaction-not-allowed": "igl.eps.main.alert-transaction-not-allowed", "invenco.system.payments.eps.transaction-stalled": "igl.eps.main.alert-transaction-stalled", "invenco.system.payments.communications.dfd-host-exception": "igl.fuelpos.comms.paymentalert-dfd-host-exception", "invenco.system.payments.communications.no-log-server": "igl.eps.comms.alert-no-log-server", "invenco.system.payments.alert.alert.device-offline": "igl.eps.main.paymentalert-device-offline", "invenco.system.payments.alert.alert.memory-high-usage": "igl.eps.system.paymentalert-memory-high-usage", "invenco.system.payments.alert.alert.no-settlement": "igl.eps.main.paymentalert-no-settlement", "invenco.system.payments.alert.alert.no-txn": "igl.eps.main.paymentalert-no-txn", "invenco.system.payments.alert.alert.stuck-saf": "igl.eps.main.paymentalert-stuck-saf", "invenco.system.payments.alert.api-error-500": "igl.fuelpos.main.paymentalert-api-error-500", "invenco.system.payments.alert.device-offline": "igl.eps.main.paymentalert-device-offline", "invenco.system.payments.alert.invalid-delivery-completion": "igl.fuelpos.main.paymentalert-invalid-delivery-completion", "invenco.system.payments.alert.no-settlement": "igl.eps.main.paymentalert-no-settlement", "invenco.system.payments.alert.no-txn": "igl.eps.main.paymentalert-no-txn", "invenco.system.payments.alert.price-change-plugin": "igl.eps.main.paymentalert-price-change-plugin", "invenco.system.payments.alert.price-sign-change": "igl.eps.main.paymentalert-price-sign-change", "invenco.system.payments.alert.pump-over-run": "igl.eps.main.paymentalert-pump-over-run", "invenco.system.payments.alert.stuck-saf": "igl.eps.main.paymentalert-stuck-saf", "invenco.system.payments.alert.tranxactor-advice-declined": "igl.eps.main.paymentalert-tranxactor-advice-declined", "invenco.system.payments.alert.tranxactor-duplicate-transactionID": "igl.eps.main.paymentalert-tranxactor-duplicate-transactionID", "invenco.system.payments.bos-offline": "igl.fuelpos.comms.paymentalert-bos-offline", "invenco.system.payments.dfd-host-exception": "igl.fuelpos.comms.paymentalert-dfd-host-exception", "invenco.system.payments.EFT disconnected": "igl.fuelpos.comms.paymentalert-eft-disconnected", "invenco.system.payments.PluginConnection": "igl.fuelpos.main.pluginconnection", "invenco.system.payments.PluginService": "igl.fuelpos.main.pluginservice", "invenco.system.payments.pricecontrollerservice": "igl.fuelpos.main.pricecontrollerservice", "invenco.system.payments.saf-clear": "igl.eps.main.alert-saf-clear", "invenco.system.payments.saf-stalled": "igl.eps.main.alert-saf-stalled", "invenco.system.payments.settlement-failed": "igl.eps.main.alert-settlement-failed", "invenco.system.payments.settlement-faileded": "igl.eps.main.alert-settlement-failed", "invenco.system.payments.terminal-offline": "igl.eps.main.alert-terminal-offline", "invenco.system.payments.transaction-not-allowed": "igl.eps.main.alert-transaction-not-allowed", "invenco.system.payments.transaction-stalled": "igl.eps.main.alert-transaction-stalled", "invenco.system.ipt.alert-ipt": "igl.eps.main.alert-alert-ipt", "invenco.system.ipt.transaction-amount.counter.sum": "igl.eps.main.transaction-amount.counter.sum", "invenco.system.ipt.transaction-count.counter.sum": "igl.eps.main.transaction-count.counter.sum", "invenco.system.g7-100.icsadaptor-ver": "invenco.system.main.icsadaptor-ver", "invenco.system.g7-100.icsagent-ver": "invenco.icsagent.main.icsagent-ver", "invenco.system.g6opt.alert.stuck-saf": "igl.eps.main.paymentalert-stuck-saf", "invenco.system.g6-300.icsadaptor-ver": "invenco.system.main.icsadaptor-ver", "invenco.system.g6-300.icsagent-ver": "invenco.icsagent.main.icsagent-ver", "invenco.system.g6-400.icsadaptor-ver": "invenco.system.main.icsadaptor-ver", "invenco.system.g6-400.icsagent-ver": "invenco.icsagent.main.icsagent-ver", "invenco.system.g6-200.icsadaptor-ver": "invenco.system.main.icsadaptor-ver", "invenco.system.g6-200.icsagent-ver": "invenco.icsagent.main.icsagent-ver", "invenco.system.g6-500.icsadaptor-ver": "invenco.system.main.icsadaptor-ver", "invenco.system.g6-500.icsagent-ver": "invenco.icsagent.main.icsagent-ver", "invenco.system.fuelpos.fuelpos-ver": "igl.fuelpos.main.fuelpos-ver", "invenco.system.fuelpos.opt-platform-ver": "igl.fuelpos.main.opt-platform-ver", "invenco.system.e1-100-eps.e1-100-eps-ver": "igl.eps.main.eps-ver", "invenco.system.e1-100-eps.opt-platform-ver": "igl.eps.main.opt-platform-ver", "invenco.system.c1-100.alert.fcc-client": "igl.fcc.main.forecourtalert-fcc-client", "invenco.system.c1-100.alert.fcc-dispenser": "igl.fcc.main.forecourtalert-fcc-dispenser", "invenco.system.c1-100.alert.fcc-log": "igl.fcc.main.forecourtalert-fcc-log", "invenco.system.c1-100.alert.fcc-status": "igl.fcc.main.forecourtalert-fcc-status", "invenco.system.c1-100.alert.mtprod": "igl.fcc.main.forecourtalert-mtprod", "invenco.system.c1-100.icsagent-ver": "igl.fcc.main.icsagent-ver", "infx.tbr.tal.channel-failures.counter.sum": "igl.infx-pti.tal.channel-failures.counter.sum", "infx.tbr.prompt.last-lookup-failure": "igl.infx-pti.prompt.last-lookup-failure", "infx.tbr.prompt.lookup-failures.counter.sum": "igl.infx-pti.prompt.lookup-failures.counter.sum", "infx.tbr.pos.channel": "igl.infx-pti.pos.channel", "infx.tbr.pos.channel-failures.counter.sum": "igl.infx-pti.pos.channel-failures.counter.sum", "infx.tbr.pinkey.keysetid": "igl.infx-pti.pinkey.keysetid", "emvadaptersecondary.prompt.last-lookup-failure": "igl.emvadaptersecondary.prompt.last-lookup-failure", "emvadapter.prompt.last-lookup-failure": "igl.emvadapter.prompt.last-lookup-failure", "emvadapter.prompt.lookup-failures.counter.sum": "igl.emvadapter.prompt.lookup-failures.counter.sum", "emvadapter.prompt.see-attendant": "igl.emvadapter.prompt.see-attendant", "emvadapter.pos.message": "igl.emvadapter.pos.message", "emvadapter.emvmodule.handler": "igl.emvadapter.emv.handler"}}}