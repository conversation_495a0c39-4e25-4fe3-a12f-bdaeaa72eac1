const restify = require('restify');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');

module.exports = {
  getDeviceVersions: async (req, res, next) => {
    try {
      const deviceId = req.params.id;
      const userId = req.user.sub;

      const deviceVersions = await server.db.read.rows(
        'SELECT * FROM device_versions_lookup'
      );

      const where = {};

      const releaseVersionData = await server.db.read.row(
        `SELECT release_version FROM target WHERE target_id = ${deviceId}`
      );
      if (!releaseVersionData) {
        return next(new restify.NotFoundError(`Device ${deviceId} not found.`));
      }
      let { releaseVersion } = releaseVersionData;
      // eslint-disable-next-line no-continue
      const re = /\[(.*)\]/i;
      releaseVersion =
        releaseVersion && releaseVersion.match(re)
          ? releaseVersion.match(re)[1]
          : releaseVersion;

      for (let i = 0; i < deviceVersions.length; i++) {
        const item = deviceVersions[i];
        const targetRelease = item.releaseVersion
          ? item.releaseVersion.split('-')
          : null;

        if (targetRelease && targetRelease.length) {
          /* eslint-disable no-continue */
          if (releaseVersion < targetRelease[0]) continue;
          if (targetRelease.length === 2 && releaseVersion > targetRelease[1])
            continue;
          /* eslint-enable */
        }

        // TODO (for performance improvement):
        // - replace SIMILAR TO with Regular Expressions or LIKE instead
        // see (https://dba.stackexchange.com/questions/10694/pattern-matching-with-like-similar-to-or-regular-expressions-in-postgresql)
        if (item.exclude === null) {
          if (where[item.category] === undefined) {
            where[item.category] =
              ` WHERE ( state SIMILAR TO '${item.include}') `;
          } else {
            where[item.category] +=
              ` OR ( state SIMILAR TO '${item.include}') `;
          }
        } else if (where[item.category] === undefined) {
          where[item.category] =
            ` WHERE ( state SIMILAR TO '${item.include}' AND state NOT SIMILAR TO '${item.exclude}') `;
        } else {
          where[item.category] +=
            ` OR ( state SIMILAR TO '${item.include}' AND state NOT SIMILAR TO '${item.exclude}') `;
        }
      }
      let categories = '';
      Object.keys(where).forEach(category => {
        categories += `'${category}', ( SELECT array_agg(json_build_object( state, value )) FROM device_versions_auth ${where[category]} ),`;
      });
      categories = categories.slice(0, -1);
      const query = `
                WITH device_versions_auth AS (
                    SELECT
                        d.target_id AS device_id,
                        ds.state AS state,
                        ds.value AS value
                    FROM target d
                    LEFT JOIN device_versions ds ON d.target_id = ds.device_id
                    JOIN site s ON s.site_id = d.site_id
                    JOIN user_site_authorization usa ON s.site_id = usa.site_id
                    WHERE
                        d.target_id = $1 AND
                        d.active AND
                        s.active AND
                        usa.user_id = $2 
                )
                SELECT json_build_object( 'device_id', (SELECT device_id FROM device_versions_auth LIMIT 1), ${categories} ) AS result`;
      const states = await server.db.read.row(query, [deviceId, userId]);

      if (!states || !states.result || !states.result.device_id) {
        return next(
          new restify.NotFoundError(
            `States for device id ${deviceId} not found.`
          )
        );
      }
      delete states.result.device_id;

      // device can return '[not set]' string instead of null for 'exceptions' metric (but not for any other);
      // have to replace it with null for consistency
      if (Array.isArray(states.result.exceptions)) {
        states.result.exceptions = states.result.exceptions.filter(
          item => Object.values(item)[0] !== '[not set]'
        );
        if (states.result.exceptions.length === 0) {
          states.result.exceptions = null;
        }
      }

      res.send(states.result);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
