const co = require('co');
const restify = require('restify');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const AWS = require('../../lib/aws');
const deviceHelper = require('../../helpers/device-helper');

module.exports = {
  exportDevicesEnhanced: (req, res, next) =>
    co(function* execute() {
      let syncStatus = 'Device Export Status: ';
      const deviceSyncConfig = yield server.db.read.rows(`
                SELECT company_id,
                       source_site_id,
                       destination_site_id,
                       destination_queue_url
                FROM device_sync_config
                WHERE active = TRUE
            ;`);

      if (deviceSyncConfig.length < 1) {
        req.log.debug('No devices to be exported');
        res.send('No devices to be exported');
        return next();
      }

      // eslint-disable-next-line no-restricted-syntax
      for (const config of deviceSyncConfig) {
        const sourceCompany = config.companyId;
        const sourceSite = config.sourceSiteId;
        const destinationQueue = config.destinationQueueUrl;
        const destinationSite = config.destinationSiteId;

        const devices = yield server.db.read.rows(
          `
                    SELECT *
                    FROM target
                    WHERE (presence = 'PRESENT' OR presence = 'UPDATE')
                      AND active = TRUE
                      AND site_id = $1
                    LIMIT 10
                  ;`,
          [sourceSite]
        );

        const noOfDevices = devices.length;
        if (noOfDevices < 1) {
          req.log.debug(`No devices to be exported for ${sourceCompany}`);
          // logged by lambda cron
          syncStatus += `\n No devices to be exported for ${sourceCompany}`;
          // Skip to next iteration
          continue; // eslint-disable-line no-continue
        }

        const deviceIds = [];
        const siteIds = [];
        const devicesSerials = [];
        const messages = [];
        // eslint-disable-next-line no-restricted-syntax
        for (const device of devices) {
          devicesSerials.push(device.serialNumber.toString());
          deviceIds.push(device.targetId);
          siteIds.push(device.siteId);

          const message = {
            DelaySeconds: 0,
            MessageBody: JSON.stringify(device),
            Id: device.targetId.toString(),
            MessageAttributes: {
              type: {
                DataType: 'String',
                StringValue: 'DEVICE_IMPORT',
              },
              destinationSite: {
                DataType: 'String',
                StringValue: destinationSite.toString(),
              },
            },
          };

          if (device.presence === 'UPDATE') {
            message.MessageAttributes.updateFields = {
              DataType: 'String',
              StringValue: 'certificate, isJsonCertificates, keyGroupRef',
            };
          }

          messages.push(message);
        }

        /*
         * Getting a new connection on each iteration,
         * as export fail for one environment, shouldn't
         * affect others
         */
        const connection = yield server.db.write.getConnection();
        try {
          // Start transaction
          yield connection.execute('BEGIN');

          // Update the presence of 'active' devices and reset device status
          yield connection.execute(
            `
                      UPDATE target
                      SET presence = 'OUT_OF_INSTANCE',
                          password = NULL,
                          registration_window_end = NOW(),
                          status = 0
                      WHERE serial_number = ANY($1)
                        AND active = TRUE
                      ;`,
            [devicesSerials]
          );

          yield AWS.sendSqsMessageBatch({
            Entries: messages,
            QueueUrl: destinationQueue,
          });

          yield deviceHelper.markDeviceStatesForDeletion(deviceIds);
          yield connection.execute('COMMIT');
          // Logged by lambda cron
          syncStatus += `\n Successfully exported ${noOfDevices} device(s) for ${sourceCompany}`;
        } catch (err) {
          const errorMessage = `Failed to export devices: ${devicesSerials.toString()} for ${sourceCompany}`;
          req.log.error(
            { error: err },
            `[DeviceSync].[ExportDevices] ${errorMessage}.`
          );
          yield connection.execute('ROLLBACK');
          return next(new restify.errors.InternalServerError(errorMessage));
        } finally {
          connection.done();
        }
      }

      req.log.info(`[DeviceSync].[ExportDevices] ${syncStatus}`);
      res.send(syncStatus);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
