{"rules": {"invenco.emvapp.app.restarted.counter.sum": {"k": "EMVApp started", "lv": "notice"}, "invenco.emvapp.pinkey.keysetid": {"k": "Debit PIN keyset ID"}, "invenco.emvapp.pinkey.ksn": {"k": "Debit PIN key KSN"}, "invenco.emvapp.pinkey.status": {"mt": "Debit PIN {v}", "#nmap": {"0": {"v": "Keys not ready", "lv": "warn"}, "1": {"v": "Keys ready", "lv": "info"}, "2": {"v": "Keys exhausted", "lv": "critical"}}}, "invenco.emvapp.pos.channel-status": {"mt": "{em}TLS connection {v}", "#nmap": "up_down_number"}, "invenco.emvapp.prompt.last-lookup-failure": {"lv": "error", "mt": "Prompt missing: {v}"}, "invenco.emvapp.tal.channel-failures.counter.sum": {"k": "SocketTAL connection lost", "lv": "error"}, "invenco.emvapp.txn.state": {"mt": "Transaction state: {v}", "#nmap": {"0": {"v": "Device offline", "lv": "warn"}, "1": {"v": "<PERSON>ce idle"}, "2": {"v": "Pre-fueling"}, "3": {"v": "Fueling"}, "4": {"v": "Post-fueling"}, "5": {"v": "Site closed"}}}, "invenco.icp.app.restarted.counter.sum": {"k": "ICP started", "lv": "notice"}, "invenco.system.cfg.aud-volume-master": {"k": "Master volume"}, "invenco.system.cfg.mgt-pci-reboot-time": {"k": "PCI reboot time"}, "invenco.system.cfg.net-configurationservice-ip": {"k": "Configuration service IP"}, "invenco.system.cfg.net-configurationservice-port": {"k": "Configuration service port"}, "invenco.system.cfg.net-controller-ip": {"k": "Controller IP"}, "invenco.system.cfg.net-controller-port": {"k": "Controller port"}, "invenco.system.cfg.net-dhcp-enabled": {"k": "DHCP"}, "invenco.system.cfg.net-dns": {"k": "DNS"}, "invenco.system.cfg.net-gateway": {"k": "Gateway address"}, "invenco.system.cfg.net-ip-addr": {"k": "Device IP address"}, "invenco.system.cfg.net-netmask": {"k": "Netmask"}, "invenco.system.cfg.net-terminal-id": {"k": "Terminal ID"}, "invenco.system.cfg.rtc-date-time-local": {"k": "Local time on device", "lv": "info"}, "invenco.system.client.invenco-adaptor-ver": {"lv": "info"}, "invenco.system.client.invenco-emvapp-ver": {"lv": "info"}, "invenco.system.client.invenco-icp-app-enabled": {"mt": "Syncronised media : {v}", "#nmap": "enabled_disabled"}, "invenco.system.client.invenco-icp-peer-cast-mode": {"mt": "Syncronised media mode : {v}", "#nmap": {"0": {"v": "broadcast"}, "1": {"v": "unicast"}}}, "invenco.system.client.invenco-icp-peer-election-state": {"mt": "Media sync master elections : {v}", "#nmap": {"0": {"v": "<PERSON><PERSON> waiting for election", "lv": "warn"}, "1": {"v": "<PERSON><PERSON> is media slave"}, "2": {"v": "<PERSON><PERSON> is media sync master"}, "3": {"v": "<PERSON><PERSON> is participating in election"}, "4": {"v": "Connection to media LAN lost", "lv": "error"}, "5": {"v": "Connection to media LAN unstable", "lv": "error"}}}, "invenco.system.client.invenco-icp-peer-master": {"mt": "Device {v} is media sync master"}, "invenco.system.client.invenco-icp-peer-num-peers": {"k": "Syncronised media peers"}, "invenco.system.client.invenco-icp-playlist-guid": {"k": "New active playlist"}, "invenco.system.client.invenco-icp-playlist-last-update": {"k": "Active playlist updated (UTC)", "#vmatch": {"failure": {"lv": "error"}}}, "invenco.system.client.invenco-icp-playlist-siteid": {"k": "Active playlist Site ID"}, "invenco.system.client.invenco-icp-playlist-updated": {"k": "Last playlist update operation", "#vmatch": {"failure": {"lv": "error"}}}, "invenco.system.client.invenco-icp-ver": {"lv": "info"}, "invenco.system.client.invenco-serviceapp-ver": {"lv": "info"}, "invenco.system.g6opt.barcode-hw-status": {"mt": "Barcode {v}", "#vmap": {"online": {"v": "connected"}, "offline": {"v": "disconnected", "lv": "warn"}}}, "invenco.system.g6opt.barcode-status": {"mt": "Barcode {v}", "#vmap": {"offline": {"v": "offline", "lv": "warn"}}}, "invenco.system.g6opt.barcode-ver-app": {"lv": "info"}, "invenco.system.g6opt.barcode-ver-hw": {"lv": "info"}, "invenco.system.g6opt.barcode-ver-mxs": {"lv": "info"}, "invenco.system.g6opt.ca-dyn-freespace-flash.gauge.last": {"k": "Current available memory FLASH (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-flash.gauge.max": {"k": "Max available memory FLASH (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-flash.gauge.mean": {"k": "Average available memory FLASH (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-flash.gauge.min": {"k": "Minimum available memory FLASH (kB)", "#nmatch": {",10000": {"lv": "critical"}, "10000,20000": {"lv": "warn"}, "20000,": {"lv": "info"}}}, "invenco.system.g6opt.ca-dyn-freespace-install.gauge.last": {"k": "Current available memory INSTALL (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-install.gauge.max": {"k": "Max available memory INSTALL (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-install.gauge.mean": {"k": "Average available memory INSTALL (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-install.gauge.min": {"k": "Minimum available memory INSTALL (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-ram.gauge.last": {"k": "Current available RAM (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-ram.gauge.max": {"k": "Max available RAM (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-ram.gauge.mean": {"k": "Average available RAM (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-ram.gauge.min": {"k": "Minimum available RAM (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-ram-health": {"mt": "Available RAM {v}", "#vmap": {"low": {"lv": "warn"}, "critical": {"lv": "warn"}}}, "invenco.system.g6opt.ca-dyn-freespace-upload.gauge.last": {"k": "Current available memory UPLOAD (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-upload.gauge.max": {"k": "Max available memory UPLOAD (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-upload.gauge.mean": {"k": "Average available memory UPLOAD (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-upload.gauge.min": {"k": "Minimum available memory UPLOAD (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-usb.gauge.last": {"k": "Current available memory USB (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-usb.gauge.max": {"k": "Max available memory USB (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-usb.gauge.mean": {"k": "Average available memory USB (kB)"}, "invenco.system.g6opt.ca-dyn-freespace-usb.gauge.min": {"k": "Minimum available memory USB (kB)"}, "invenco.system.g6opt.ca-status": {"mt": "Control agent {v}", "#vmap": {"offline": {"lv": "error"}}}, "invenco.system.g6opt.ca-ver-app": {"lv": "info"}, "invenco.system.g6opt.ca-verchecker": {"lv": "info"}, "invenco.system.g6opt.ca-ver-hw": {"lv": "info"}, "invenco.system.g6opt.ca-ver-mxs": {"lv": "info"}, "invenco.system.g6opt.emv-fault": {"mt": "EMV {v}", "#nmap": {"0": {"v": "OK"}, "1": {"v": "error", "lv": "warn"}}}, "invenco.system.g6opt.emv-status": {"mt": "EMV status {v}", "#vmap": {"offline": {"lv": "warn"}}}, "invenco.system.g6opt.emv-ver-app": {"lv": "info"}, "invenco.system.g6opt.emv-ver-emvk": {"lv": "info"}, "invenco.system.g6opt.emv-ver-mxs": {"lv": "info"}, "invenco.system.g6opt.env-kernel-type": {"k": "Device model"}, "invenco.system.g6opt.env-ver-kernel": {"lv": "info"}, "invenco.system.g6opt.env-ver-lib": {"lv": "info"}, "invenco.system.g6opt.env-ver-rootfs": {"lv": "info"}, "invenco.system.g6opt.env-ver-sandbox": {"lv": "info"}, "invenco.system.g6opt.env-ver-utils": {"lv": "info"}, "invenco.system.g6opt.java-ver-diag": {"lv": "info"}, "invenco.system.g6opt.java-ver-jvm": {"lv": "info"}, "invenco.system.g6opt.java-ver-sdk": {"lv": "info"}, "invenco.system.g6opt.java-ver-tal": {"lv": "info"}, "invenco.system.g6opt.omr-dns-servers-list": {"k": "List of DNS Servers"}, "invenco.system.g6opt.omr-gw-default": {"k": "Default gateway"}, "invenco.system.g6opt.omr-local-ip": {"k": "Device IP address"}, "invenco.system.g6opt.omr-local-mac": {"k": "MAC address"}, "invenco.system.g6opt.omr-local-netmask": {"k": "Netmask"}, "invenco.system.g6opt.omr-server-ip": {"k": "Primary server IP address"}, "invenco.system.g6opt.omr-server-ip-enabled": {"mt": "Primary server in use: {v}"}, "invenco.system.g6opt.omr-server-ip-sec": {"k": "Secondary server IP address", "#vmap": {"notset": {"lv": "warn"}}}, "invenco.system.g6opt.omr-server-port": {"k": "Server port"}, "invenco.system.g6opt.omr-status": {"mt": "OMR {v}", "#vmap": {"offline": {"lv": "warn"}}}, "invenco.system.g6opt.omr-ver-app": {"lv": "info"}, "invenco.system.g6opt.p2pe-cpat-index": {"k": "P2PE CPAT index"}, "invenco.system.g6opt.p2pe-cpat-name": {"k": "P2PE CPAT name"}, "invenco.system.g6opt.p2pe-cpat-ver": {"k": "P2PE CPAT format version"}, "invenco.system.g6opt.p2pe-key-dukpt": {"k": "P2PE DUKPT key"}, "invenco.system.g6opt.p2pe-pkkey-list": {"k": "List of P2PE certificates"}, "invenco.system.g6opt.pcd-status": {"mt": "Contactless reader {v}", "#vmap": {"offline": {"lv": "warn"}}}, "invenco.system.g6opt.pcd-ver-app": {"lv": "info"}, "invenco.system.g6opt.pcd-ver-chipset": {"lv": "info"}, "invenco.system.g6opt.pcd-ver-cpf": {"lv": "info"}, "invenco.system.g6opt.pcd-ver-emvl1": {"lv": "info"}, "invenco.system.g6opt.pcd-ver-hw": {"lv": "info"}, "invenco.system.g6opt.pcd-ver-kernels-dp": {"lv": "info"}, "invenco.system.g6opt.pcd-ver-kernels-ep": {"lv": "info"}, "invenco.system.g6opt.pcd-ver-kernels-pp": {"lv": "info"}, "invenco.system.g6opt.pcd-ver-kernels-pw": {"lv": "info"}, "invenco.system.g6opt.pcd-ver-kernels-xp": {"lv": "info"}, "invenco.system.g6opt.pcd-ver-mxs": {"lv": "info"}, "invenco.system.g6opt.prd-ver-app": {"lv": "info"}, "invenco.system.g6opt.prn-cutter-err": {"mt": "Printer cutter {v}", "#nmap": {"0": {"v": "OK"}, "1": {"v": "error", "lv": "warn"}}}, "invenco.system.g6opt.prn-head-temp-over": {"mt": "Print head {v}", "#nmap": {"0": {"v": "temperature OK"}, "1": {"v": "over temperature", "lv": "warn"}}}, "invenco.system.g6opt.prn-paper-calibrated": {"mt": "Printer paper {v}", "#nmap": {"0": {"v": "not calibrated", "lv": "warn"}, "1": {"v": "calibrated"}}}, "invenco.system.g6opt.prn-paper-level.gauge.last": {"k": "Current paper level (%)"}, "invenco.system.g6opt.prn-paper-level.gauge.max": {"k": "Max paper level (%)"}, "invenco.system.g6opt.prn-paper-level.gauge.mean": {"k": "Average paper level (%)"}, "invenco.system.g6opt.prn-paper-level.gauge.min": {"k": "Minimum paper level (%)"}, "invenco.system.g6opt.prn-paper-low": {"mt": "Printer paper {v}", "#nmap": {"0": {"v": "OK"}, "1": {"v": "low", "lv": "warn"}}}, "invenco.system.g6opt.prn-paper-out": {"mt": "Printer paper {v}", "#nmap": {"0": {"v": "OK"}, "1": {"v": "out", "lv": "warn"}}}, "invenco.system.g6opt.prn-status": {"mt": "Printer {v}", "#vmap": {"offline": {"lv": "warn"}}}, "invenco.system.g6opt.prn-ver-app": {"lv": "info"}, "invenco.system.g6opt.prn-ver-hw": {"lv": "info"}, "invenco.system.g6opt.prn-ver-loader": {"lv": "info"}, "invenco.system.g6opt.prn-ver-stamp": {"lv": "info"}, "invenco.system.g6opt.sdc-vendorassets-fingerprint": {"mt": "Vendor assets: {v}", "#vmap": {"notset": {"lv": "warn"}}}, "invenco.system.g6opt.sdu-exception-integrity": {"mt": "SDU integrity {v}", "#nmap": {"0": {"v": "OK"}, "1": {"v": "error", "lv": "error"}}}, "invenco.system.g6opt.sdu-status": {"mt": "SDU {v}", "#vmap": "online_offline"}, "invenco.system.g6opt.sdu-ver-app": {"lv": "info"}, "invenco.system.g6opt.sdu-ver-insecure": {"lv": "info"}, "invenco.system.g6opt.sdu-ver-mxs": {"lv": "info"}, "invenco.system.g6opt.sdu-ver-secure": {"lv": "info"}, "invenco.system.g6opt.serial-ver-app": {"lv": "info"}, "invenco.system.g6opt.serial-ver-loader": {"lv": "info"}, "invenco.system.g6opt.serial-ver-mxs": {"lv": "info"}, "invenco.system.g6opt.tallies-ca-restarted.counter.sum": {"k": "Firmware Control Agent restarted"}, "invenco.system.g6opt.tallies-prn-present": {"mt": "Printer {v}", "#vmap": "connected_disconnected"}, "invenco.system.g6opt.tallies-serial-rs485-present": {"mt": "RS485 {v}", "#vmap": "connected_disconnected"}, "invenco.system.g6opt.upc-battery-voltage.gauge.last": {"k": "Current battery voltage (mV)"}, "invenco.system.g6opt.upc-battery-voltage.gauge.max": {"k": "Max battery voltage (mV)"}, "invenco.system.g6opt.upc-battery-voltage.gauge.mean": {"k": "Average battery voltage (mV)"}, "invenco.system.g6opt.upc-battery-voltage.gauge.min": {"k": "Minimum battery voltage (mV)"}, "invenco.system.g6opt.upc-exception-battery-exhausted": {"mt": "Battery {v}", "#nmap": {"0": {"v": "OK"}, "1": {"v": "exhausted", "lv": "warn"}}}, "invenco.system.g6opt.upc-exception-battery-low": {"mt": "Battery level {v}", "#nmap": {"0": {"v": "OK"}, "1": {"v": "low", "lv": "warn"}}}, "invenco.system.g6opt.upc-exception-bpklost": {"mt": "BPK {v}", "#nmap": {"0": {"v": "OK"}, "1": {"v": "lost", "lv": "warn"}}}, "invenco.system.g6opt.upc-exception-integrity-anchor": {"mt": "Integrity anchor {v}", "#nmap": "error_warn"}, "invenco.system.g6opt.upc-exception-integrity-certificate": {"mt": "Integrity certificate {v}", "#nmap": "error_warn"}, "invenco.system.g6opt.upc-exception-integrity-flash": {"mt": "Integrity flash {v}", "#nmap": "error_warn"}, "invenco.system.g6opt.upc-exception-integrity-model": {"mt": "Integrity model {v}", "#nmap": "error_warn"}, "invenco.system.g6opt.upc-exception-integrity-nvcipher": {"mt": "Integrity nvcipher {v}", "#nmap": "error_warn"}, "invenco.system.g6opt.upc-exception-integrity-nvclear": {"mt": "Integrity nvclear {v}", "#nmap": "error_warn"}, "invenco.system.g6opt.upc-exception-integrity-ram": {"mt": "Integrity RAM {v}", "#nmap": "error_warn"}, "invenco.system.g6opt.upc-exception-nvinitfail": {"mt": "NVinit  {v}", "#nmap": "error_warn"}, "invenco.system.g6opt.upc-exception-tamperremoved": {"mt": "Tamper {v}", "#nmap": {"0": {"v": "OK"}, "1": {"v": "removed", "lv": "error"}}}, "invenco.system.g6opt.upc-exception-tempsensor-initfail": {"mt": "Temp sensor initialisation {v}", "#nmap": {"0": {"v": "OK"}, "1": {"v": "failed", "lv": "warn"}}}, "invenco.system.g6opt.upc-exception-usip-phase": {"mt": "USIP phase {v}", "#nmap": "error_warn"}, "invenco.system.g6opt.upc-exception-version-mismatch": {"mt": "UPC version {v}", "#nmap": {"0": {"v": "OK"}, "1": {"v": "mismatch", "lv": "warn"}}}, "invenco.system.g6opt.upc-lasttamper-event": {"mt": "Last tamper event: {v}"}, "invenco.system.g6opt.upc-sn": {"k": "UPC serial number"}, "invenco.system.g6opt.upc-status": {"mt": "{em}UPC {v}", "#vmap": "online_offline"}, "invenco.system.g6opt.upc-tamper-mon-ver": {"k": "Tamper monitor version", "lv": "info"}, "invenco.system.g6opt.upc-ver-app": {"lv": "info"}, "invenco.system.g6opt.upc-ver-emvl1": {"lv": "info"}, "invenco.system.g6opt.upc-ver-hal": {"lv": "info"}, "invenco.system.g6opt.upc-ver-hw": {"lv": "info"}, "invenco.system.g6opt.upc-ver-loader": {"lv": "info"}, "invenco.system.g6opt.upc-ver-mxs": {"lv": "info"}, "invenco.system.g6opt.upc-ver-offlinepin": {"lv": "info"}, "invenco.system.g6opt.upc-ver-spi": {"lv": "info"}, "invenco.system.g6opt.upc-ver-ucl": {"lv": "info"}, "invenco.system.g7opt.apc-barcodefw-ver": {"lv": "info"}, "invenco.system.g7opt.apc-display-ver": {"lv": "info"}, "invenco.system.g7opt.apc-init-ver": {"lv": "info"}, "invenco.system.g7opt.apc-printer-condition": {"k": "Printer condition"}, "invenco.system.g7opt.apc-printer-headcoverstatus": {"mt": "Print head cover {v}", "#vmap": {"open": {"v": "open", "lv": "warn"}, "close": {"v": "closed"}}}, "invenco.system.g7opt.apc-printer-model": {"mt": "Printer model: {v}", "#vmap": {"unknown": {"lv": "warn"}}}, "invenco.system.g7opt.apc-printer-modeldetails": {"k": "Printer model details"}, "invenco.system.g7opt.apc-printer-offline": {"mt": "Printer {v}", "#vmap": {"true": {"v": "offline", "lv": "warn"}, "false": {"v": "online"}}}, "invenco.system.g7opt.apc-printer-pprjammed": {"mt": "Printer paper {v}", "#vmap": {"true": {"v": "jammed", "lv": "warn"}, "false": {"v": "OK"}}}, "invenco.system.g7opt.apc-printer-pprlevel": {"mt": "Printer paper {v}", "#vmap": {"low": {"v": "low", "lv": "warn"}, "out": {"v": "out", "lv": "warn"}}}, "invenco.system.g7opt.apc-printer-prnpprlen": {"lv": "info", "mt": "Printer paper length: {v}"}, "invenco.system.g7opt.apc-printer-queueavailable": {"k": "Printer queue status", "lv": "info"}, "invenco.system.g7opt.apc-printer-queuemax": {"k": "Printer queue max", "lv": "info"}, "invenco.system.g7opt.apc-printer-queueused": {"k": "Printer queue used", "lv": "info"}, "invenco.system.g7opt.apc-printer-receipt": {"k": "Printer receipt"}, "invenco.system.g7opt.apc-printer-status": {"mt": "Printer status: {v}", "#vmap": {"error - printer communication error": {"lv": "warn"}}}, "invenco.system.g7opt.apc-printer-temp": {"mt": "Printer {v}", "#vmap": {"normal": {"v": "temperature OK"}, "overheated": {"lv": "warn"}}}, "invenco.system.g7opt.apc-printer-ver": {"lv": "info"}, "invenco.system.g7opt.apc-safe-ver": {"lv": "info"}, "invenco.system.g7opt.apc-system-restarted": {"k": "APC system started"}, "invenco.system.g7opt.apc-terminal-gateway": {"k": "Device gateway address"}, "invenco.system.g7opt.apc-terminal-ip": {"k": "Device IP address"}, "invenco.system.g7opt.apc-terminal-mac": {"k": "Device MAC address"}, "invenco.system.g7opt.apc-terminal-netmask": {"k": "<PERSON><PERSON> netmask"}, "invenco.system.g7opt.apc-uboot-ver": {"lv": "info"}, "invenco.system.g7opt.apc-udp-ver": {"lv": "info"}, "invenco.system.g7opt.p2pe-cpat-index": {"k": "P2PE CPAT index"}, "invenco.system.g7opt.p2pe-cpat-name": {"k": "P2PE CPAT name"}, "invenco.system.g7opt.p2pe-cpat-status": {"k": "P2PE CPAT status"}, "invenco.system.g7opt.p2pe-cpat-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-application-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-bootmonitor-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-hw-serialnumber": {"k": "SDC serial number"}, "invenco.system.g7opt.sdc-hw-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-init-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-kernel-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-reboot-timer": {"k": "SDC reboot timer", "lv": "info"}, "invenco.system.g7opt.sdc-root-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-safekernel-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-saferoot-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-softkey-adakey1": {"lv": "info", "mt": "Akey adakey1 {v}"}, "invenco.system.g7opt.sdc-softkey-adakey2": {"lv": "info", "mt": "Akey adakey2 {v}"}, "invenco.system.g7opt.sdc-softkey-adakey3": {"lv": "info", "mt": "Akey adakey3 {v}"}, "invenco.system.g7opt.sdc-softkey-adakey4": {"lv": "info", "mt": "Akey adakey4 {v}"}, "invenco.system.g7opt.sdc-softkey-adakey5": {"lv": "info", "mt": "Akey adakey5 {v}"}, "invenco.system.g7opt.sdc-softkey-adakey6": {"lv": "info", "mt": "Akey adakey6 {v}"}, "invenco.system.g7opt.sdc-softkey-adakey7": {"lv": "info", "mt": "Akey adakey7 {v}"}, "invenco.system.g7opt.sdc-softkey-adakey8": {"lv": "info", "mt": "<PERSON>key adakey8 {v}"}, "invenco.system.g7opt.sdc-softkey-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-sysassets-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-system-restarted.counter.sum": {"k": "SDC system started"}, "invenco.system.g7opt.sdc-tamper-mode": {"mt": "SDC tamper mode: {v}", "#vmap": {"safe": {"lv": "warn"}}}, "invenco.system.g7opt.sdc-tamper-status": {"lv": "info"}, "invenco.system.g7opt.sdc-tamper-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-touchscreenfrm-ver": {"lv": "info"}, "invenco.system.g7opt.sdc-vendorassets-ver": {"lv": "info"}, "invenco.system.g7opt.upc-application-ver": {"lv": "info"}, "invenco.system.g7opt.upc-bootmonitor-ver": {"lv": "info"}, "invenco.system.g7opt.upc-cep-ver": {"lv": "info"}, "invenco.system.g7opt.upc-channel-status": {"mt": "{em}UPC connection {v}", "#vmap": "up_down_value"}, "invenco.system.g7opt.upc-contactlessemvl1-chksum": {"k": "Contactless EMV L1 checksum"}, "invenco.system.g7opt.upc-contactlessemvl1-ver": {"lv": "info"}, "invenco.system.g7opt.upc-cpattable-ver": {"lv": "info"}, "invenco.system.g7opt.upc-dataentry-mode": {"k": "UPC data entry mode"}, "invenco.system.g7opt.upc-dpas-ver": {"lv": "info"}, "invenco.system.g7opt.upc-emvcfg-ver": {"lv": "info"}, "invenco.system.g7opt.upc-emvkernel-chksum": {"k": "EMV kernel checksum"}, "invenco.system.g7opt.upc-emvkernel-ver": {"lv": "info"}, "invenco.system.g7opt.upc-emvl1-ver": {"lv": "info"}, "invenco.system.g7opt.upc-entrypoint-ver": {"lv": "info"}, "invenco.system.g7opt.upc-express-ver": {"lv": "info"}, "invenco.system.g7opt.upc-hw-serialnumber": {"k": "UPC serial number"}, "invenco.system.g7opt.upc-hw-ver": {"lv": "info"}, "invenco.system.g7opt.upc-init-ver": {"lv": "info"}, "invenco.system.g7opt.upc-kernel-ver": {"lv": "info"}, "invenco.system.g7opt.upc-l2contactless-ver": {"lv": "info"}, "invenco.system.g7opt.upc-l2contact-ver": {"lv": "info"}, "invenco.system.g7opt.upc-offlinepin-chksum": {"k": "Offline PIN checksum"}, "invenco.system.g7opt.upc-offlinepin-ver": {"lv": "info"}, "invenco.system.g7opt.upc-p2pe-ver": {"lv": "info"}, "invenco.system.g7opt.upc-paypass-ver": {"lv": "info"}, "invenco.system.g7opt.upc-paywave-ver": {"lv": "info"}, "invenco.system.g7opt.upc-reboot-timer": {"k": "UPC reboot timer", "lv": "info"}, "invenco.system.g7opt.upc-root-ver": {"lv": "info"}, "invenco.system.g7opt.upc-safekernel-ver": {"lv": "info"}, "invenco.system.g7opt.upc-saferoot-ver": {"lv": "info"}, "invenco.system.g7opt.upc-sam-ver": {"lv": "info"}, "invenco.system.g7opt.upc-sysassets-ver": {"lv": "info"}, "invenco.system.g7opt.upc-system-restarted.counter.sum": {"k": "UPC system started"}, "invenco.system.g7opt.upc-tamper-mode": {"mt": "UPC tamper mode: {v}", "#vmap": {"safe": {"lv": "warn"}}}, "invenco.system.g7opt.upc-tamper-status": {"mt": "{em}UPC tamper status: {v}", "#vmap": {"good": {"v": "good"}, "tampered": {"v": "tampered (destructive)", "lv": "error", "em": "OUT OF SERVICE"}, "separated": {"v": "tampered (removal)", "lv": "error", "em": "OUT OF SERVICE"}}}, "invenco.system.g7opt.upc-tamper-ver": {"lv": "info"}, "invenco.system.g7opt.upc-trmdata-chksum": {"k": "UPC TRMdata checksum"}, "invenco.system.ped.keys-present": {"k": "PED key status"}, "invenco.system.prt.condition": {"k": "Printer status"}, "invenco.system.prt.model": {"k": "Printer model"}, "invenco.system.prt.paper-jammed": {"k": "Paper jam status"}, "invenco.system.prt.paper-level": {"k": "Paper level status"}, "invenco.system.rtc.date-time": {"k": "RTC time", "lv": "info"}, "invenco.system.rtc1.date-time": {"k": "RTC1 time", "lv": "info"}, "invenco.system.rtc2.date-time": {"k": "RTC2 time", "lv": "info"}, "invenco.system.rtc3.date-time": {"k": "RTC3 time", "lv": "info"}}, "vmaps": {"up_down_number": {"0": {"v": "lost", "em": "OUT OF SERVICE: ", "lv": "warn"}, "1": {"v": "OK"}, "2": {"v": "error", "em": "OUT OF SERVICE: ", "lv": "error"}}, "up_down_value": {"up": {"v": "up"}, "down": {"v": "lost", "lv": "warn", "em": "OUT OF SERVICE"}}, "online_offline": {"online": {"v": "connected"}, "offline": {"v": "disconnected", "lv": "warn", "em": "OUT OF SERVICE"}}, "enabled_disabled": {"0": {"v": "disabled"}, "1": {"v": "enabled"}}, "connected_disconnected": {"true": {"v": "connected"}, "false": {"v": "disconnected", "lv": "warn"}}, "error_warn": {"0": {"v": "OK"}, "1": {"v": "error", "lv": "warn"}}}}