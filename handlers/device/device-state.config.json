[{"name": "sccSerialNumber", "states": ["invenco.system.g7opt.scc-hw-serialnumber"], "deviceTypes": ["G7-100"]}, {"name": "sysContactlessModule", "states": ["invenco.system.g7opt.sys-contactless-module"], "deviceTypes": ["G7-100"]}, {"name": "sccChannelStatus", "states": ["invenco.system.g7opt.scc-channel-status"], "deviceTypes": ["G7-100"]}, {"name": "sysSecurelinkUPCSCC", "states": ["invenco.system.g7opt.sys-securelink-upc-scc"], "deviceTypes": ["G7-100"]}, {"name": "apcSerialNumber", "states": ["invenco.system.g7opt.apc-hw-serialnumber"], "deviceTypes": ["G7-100", "G6-300", "G6-400", "G6-500", "OMNIA", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "sdcSerialNumber", "states": ["invenco.system.g7opt.sdc-hw-serialnumber"], "deviceTypes": ["G7-100", "G6-300", "G6-400", "G6-500", "OMNIA", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "printerModel", "states": ["invenco.system.g7opt.apc-printer-model"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "printerModel", "states": ["invenco.system.g7opt.apc-printer-model"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "printerModel", "states": ["invenco.system.cfg.prt-model"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "printerPortType", "states": ["invenco.system.cfg.prt-port-type"], "deviceTypes": ["G6-300", "G7-100"]}, {"name": "printerPaperWidth", "states": ["invenco.system.cfg.prt-paper-width"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "printerMarginX", "states": ["invenco.system.cfg.prt-margin-x"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "printerMarginY", "states": ["invenco.system.cfg.prt-margin-y"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "printerCutOffset", "states": ["invenco.system.cfg.prt-cut-offset"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "isUnicast", "states": ["invenco.system.client.invenco-icp-peer-cast-mode"], "deviceTypes": ["G6-200", "G7-100"]}, {"name": "terminalId", "states": ["invenco.system.cfg.net-terminal-id", "invenco.contentplayer.media-player.terminal-id"], "deviceTypes": ["G6-100", "G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "terminalLocation", "states": ["invenco.system.cfg.net-terminal-location"], "deviceTypes": ["G6-100", "G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "terminalIpAddress", "states": ["invenco.system.cfg.net-ip-addr"], "deviceTypes": ["G6-100", "G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "terminalType", "states": ["invenco.system.g6opt.env-kernel-type"], "deviceTypes": ["G6-100", "G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "dateTime", "states": ["invenco.system.cfg.rtc-date-time-local"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "isMaster", "states": ["invenco.system.client.invenco-icp-peer-master"], "deviceTypes": ["G6-200", "G7-100", "G6-300"]}, {"name": "temperature", "states": ["invenco.system.g7opt.upc-tamper-temperature.gauge.last"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "memory", "states": ["invenco.system.g7opt.apc-system-freeram.gauge.last", "invenco.system.g6opt.ca-dyn-freespace-ram.gauge.mean"], "deviceTypes": ["G6-100", "G6-200", "G7-100"]}, {"name": "pciRebootTime", "states": ["invenco.system.cfg.mgt-pci-reboot-time"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "controllerIp", "states": ["invenco.system.cfg.net-controller-ip"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "controllerPort", "states": ["invenco.system.cfg.net-controller-port"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "networkMode", "states": ["invenco.system.cfg.net-dhcp-enabled"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "networkMask", "states": ["invenco.system.cfg.net-netmask"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "networkGateway", "states": ["invenco.system.cfg.net-gateway"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "dns", "states": ["invenco.system.cfg.net-dns"], "deviceTypes": ["G6-100", "G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "syslog", "states": ["invenco.system.cfg.net-log-server"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "ntpServerIpAddress", "states": ["invenco.system.cfg.net-ntp-server"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "ntpPool", "states": ["invenco.system.cfg.net-ntp-pool"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "configurationServiceIpAddress", "states": ["invenco.system.cfg.net-configurationservice-ip"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "configurationServicePort", "states": ["invenco.system.cfg.net-configurationservice-port"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "timeZone", "states": ["invenco.system.cfg.rtc-time-zone"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "masterVolume", "states": ["invenco.system.cfg.aud-volume-master"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "audioOutputChannel", "states": ["invenco.system.cfg.aud-outchannel"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "auxiliary", "states": ["invenco.system.cfg.net-terminal-rank"], "deviceTypes": ["G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "certificateModule", "states": ["invenco.system.g7opt.upc-certificate-module", "invenco.system.g6opt.upc-pki-certs-loaded"], "deviceTypes": ["G6-200", "G7-100"]}, {"name": "pkiKeyLoaded", "states": ["invenco.system.g7opt.upc-pki-certs-loaded", "invenco.system.g6opt.upc-pki-sysmke-loaded"], "deviceTypes": ["G6-200", "G7-100"]}, {"name": "pkiExpiry", "states": ["invenco.system.g7opt.upc-certificate-module-notafter", "invenco.system.g6opt.upc-pki-certs-loaded"], "deviceTypes": ["G6-200", "G7-100"]}, {"name": "ksnMismatched", "states": ["invenco.emvapp.pinkey.ksn"], "deviceTypes": ["G6-200", "G7-100"]}, {"name": "apcMode", "states": ["invenco.system.cfg.mgt-device-type"], "deviceTypes": ["G6-200", "G6-300", "G6-400", "G6-500", "OMNIA", "G7-100", "EDGE", "WIN-SERVER", "LINUX-SERVER"]}, {"name": "ada<PERSON><PERSON>", "states": ["invenco.system.g7opt.sys-aux-keypad"], "deviceTypes": ["G7-100"]}, {"name": "accessibilityMode", "states": ["invenco.system.cfg.lcd-accessibility-mode"], "deviceTypes": ["G6-400", "G6-500"]}, {"name": "subPlatform", "states": ["invenco.system.g7opt.sys-subplatform"], "deviceTypes": ["G6-500"]}, {"name": "displayBrightness2", "states": ["invenco.system.cfg.lcd-brightness-2"], "deviceTypes": ["G6-500"]}, {"name": "dualDisplayEnabled", "states": ["invenco.system.cfg.mgt-dual-display-enabled"], "deviceTypes": ["G6-500"]}, {"name": "gstvScreen", "states": ["invenco.system.cfg.lcd-gstv-screen"], "deviceTypes": ["G6-500"]}, {"name": "udcSerialNumber", "states": ["invenco.system.g7opt.sys-serialnumber-udc"], "deviceTypes": ["G6-500"]}, {"name": "udcVersion", "states": ["invenco.system.g7opt.opt-fw-udc-ver"], "deviceTypes": ["G6-500"]}]