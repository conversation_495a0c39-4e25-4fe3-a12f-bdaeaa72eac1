const restify = require('restify');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const deviceHelper = require('../../helpers/device-helper');
const { config } = require('../../env');

module.exports = {
  updateDeviceAlarmRules: async (req, res, next) => {
    try {
      const userId = req.user.sub;
      let response;
      const conn = await server.db.write.getConnection();
      try {
        await conn.execute('BEGIN');
        const resultPromises = req.body.map(async item => {
          const payload = item;

          if (
            (payload.suspendedUntil == null && payload.suspendedFrom != null) ||
            (payload.suspendedUntil != null && payload.suspendedFrom == null)
          ) {
            return next(
              new restify.errors.BadRequestError(
                'Properties "suspended_from" and "suspended_until" have to have the same type (both null or both integer)'
              )
            );
          }

          if (payload.suspendedUntil < payload.suspendedFrom) {
            return next(
              new restify.errors.BadRequestError(
                'Specified alarm suspension window start time is before end time.'
              )
            );
          }

          if (
            payload.suspendedUntil - payload.suspendedFrom >
            config.maxAlarmSuspensionPeriodInMinutes * 60000
          ) {
            return next(
              new restify.errors.BadRequestError(
                `Specified alarm suspension window exceeds maximum allow value of ${config.maxAlarmSuspensionPeriodInMinutes} minutes.`
              )
            );
          }

          const authorizedDevicesForUser = await conn.execute(
            `
                SELECT target_id FROM user_site_authorization usa
                JOIN site s ON s.site_id = usa.site_id
                JOIN target t ON t.site_id = s.site_id
                WHERE usa.user_id = $1 AND s.site_id = $2 AND t.active = true AND t.target_id = ANY ($3)
            `,
            [userId, payload.siteId, payload.devices]
          );

          if (
            !authorizedDevicesForUser ||
            authorizedDevicesForUser.rowCount !== payload.devices.length
          ) {
            return next(
              new restify.errors.NotFoundError(
                `Site [${payload.siteId}] or devices [${payload.devices}] specified in the request not found or user is not authorized to access them.`
              )
            );
          }

          let indexes = '';

          for (let i = 4; i < payload.devices.length + 4; i++) {
            indexes = indexes.concat(` ($1, $2, $3, $${i}),`);
          }
          indexes = indexes.substring(0, indexes.length - 1);

          await conn.execute(
            `INSERT INTO ics_alarm.alarm_rules_settings (site_id, suspended_from, suspended_until, device_id)
                VALUES ${indexes}
                ON CONFLICT (device_id)
                DO UPDATE SET
                site_id = EXCLUDED.site_id,
                suspended_from = EXCLUDED.suspended_from,
                suspended_until = EXCLUDED.suspended_until;`,
            [
              payload.siteId,
              payload.suspendedFrom
                ? new Date(parseInt(payload.suspendedFrom, 10)).toISOString()
                : null,
              payload.suspendedUntil
                ? new Date(parseInt(payload.suspendedUntil, 10)).toISOString()
                : null,
              ...payload.devices,
            ]
          );

          const now = Date.now();
          const suspended =
            payload.suspendedFrom < now && payload.suspendedUntil > now;
          return payload.devices.map(device => ({
            deviceId: device,
            siteId: payload.siteId,
            suspended,
            suspendedFrom: payload.suspendedFrom,
            suspendedUntil: payload.suspendedUntil,
          }));
        });
        const result = await Promise.all(resultPromises);
        response = [].concat.apply([], result); // eslint-disable-line prefer-spread
        await conn.execute('COMMIT');
      } catch (err) {
        await conn.execute('ROLLBACK');
        return next(new restify.errors.InternalServerError(err));
      } finally {
        conn.done();
      }
      res.send(response);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  getDeviceAlarmRulesForSite: async (req, res, next) => {
    try {
      const siteId = req.params.id;
      const userId = req.user.sub;

      const site = await server.db.read.row(
        `
                    SELECT
                        s.site_id
                    FROM site s
                        JOIN user_site_authorization usa ON s.site_id = usa.site_id
                    WHERE 
                    s.site_id = $1 AND 
                    s.active AND 
                    usa.user_id = $2;
                 `,
        [siteId, userId]
      );
      if (!site) {
        return next(new restify.NotFoundError('Site not found'));
      }

      const devices = await server.db.read.rows(
        `
                SELECT 
                    target_id
                FROM target
                WHERE site_id = $1 AND
                    active IS true;
            `,
        [siteId]
      );

      /*
                suspended property should be calculated only based on user defined suspension window
                and ignore suspended_by_device_until
             */
      const ruleSettings = await server.db.read.rows(
        `
                SELECT
                    device_id,
                    site_id,
                    CASE WHEN (suspended_from IS NULL OR suspended_until IS NULL) THEN FALSE
                    ELSE (SELECT (NOW() >= suspended_from) AND (NOW() <= suspended_until)) 
                    END AS suspended,
                    extract(epoch from date_trunc('milliseconds', suspended_from) ) * 1000 AS suspended_from,
                    extract(epoch from date_trunc('milliseconds', suspended_until) ) * 1000  AS suspended_until,
                    extract(epoch from date_trunc('milliseconds', suspended_by_device_until) ) * 1000  AS suspended_by_device_until

                FROM ics_alarm.alarm_rules_settings 
                WHERE site_id = $1
            `,
        [siteId]
      );

      const alarmRules = devices.map(device => {
        const alarmRule = {
          siteId,
          deviceId: device.targetId,
          suspended: false,
          suspendedFrom: null,
          suspendedUntil: null,
          suspendedByDeviceUntil: null,
        };
        const deviceRule = ruleSettings.find(
          rule => rule.deviceId === device.targetId
        );
        if (deviceRule) {
          alarmRule.suspended = deviceRule.suspended;
          alarmRule.suspendedFrom = deviceRule.suspendedFrom;
          alarmRule.suspendedUntil = deviceRule.suspendedUntil;
          alarmRule.suspendedByDeviceUntil = deviceRule.suspendedByDeviceUntil;
        }
        return alarmRule;
      });

      res.send(alarmRules);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  getDeviceAlarmRules: async (req, res, next) => {
    try {
      const deviceId = req.params.id;
      const userId = req.user.sub;
      const companyId = req.user.company.id;

      const device = await deviceHelper.queryDeviceById(
        userId,
        deviceId,
        companyId
      );
      if (!device) {
        return next(
          new restify.errors.NotFoundError(
            `Device (id: ${deviceId}) is not found`
          )
        );
      }
      /*
                suspended property should be calculated only based on user defined suspension window
                and ignore suspended_by_device_until
             */
      const alarmRule = await server.db.read.row(
        `
                SELECT
                    device_id,
                    site_id,
                    CASE WHEN (suspended_from IS NULL OR suspended_until IS NULL) THEN FALSE
                    ELSE (SELECT (NOW() >= suspended_from) AND (NOW() <= suspended_until)) 
                    END AS suspended,
                    extract(epoch from date_trunc('milliseconds', suspended_from) ) * 1000 AS suspended_from,
                    extract(epoch from date_trunc('milliseconds', suspended_until) ) * 1000  AS suspended_until,
                    extract(epoch from date_trunc('milliseconds', suspended_by_device_until) ) * 1000  AS suspended_by_device_until
                FROM ics_alarm.alarm_rules_settings 
                WHERE device_id = $1
                `,
        [deviceId]
      );

      if (alarmRule != null) {
        res.send(alarmRule);
      } else {
        res.send({
          deviceId: device.id,
          siteId: device.siteId,
          suspended: false,
          suspendedFrom: null,
          suspendedUntil: null,
          suspendedByDeviceUntil: null,
        });
      }
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
