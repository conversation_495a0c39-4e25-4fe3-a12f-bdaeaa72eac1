/* eslint-disable no-useless-escape */
const async = require('async');
const _ = require('lodash');
const co = require('co');
const moment = require('moment');
const timezone = require('moment-timezone');
const restify = require('restify');
const restifyErrors = require('restify-errors');
const NodeCache = require('node-cache');
const textEncoding = require('text-encoding');
const QueryStream = require('pg-query-stream');
const stringify = require('csv-stringify');
const transform = require('stream-transform');
const newrelic = require('newrelic');
const env = require('../../env');
const AWS = require('../../lib/aws');
const { server } = require('../../app');
const constants = require('../../lib/app-constants');
const rkiHelper = require('../../helpers/rki-helper');
const errorHandler = require('../../lib/errorhandler');
const deviceHelper = require('../../helpers/device-helper');
const ipAddressHelper = require('../../helpers/ip-address-helper');
const paginationHelper = require('../../helpers/pagination-helper');
const keyRequestHelper = require('../../helpers/key-request-helper');
const companyHelper = require('../../helpers/company-helper');
const sitesHelper = require('../../helpers/sites-helper');
const alarmsHelper = require('../../helpers/alarms-helper');
const uploaderHelper = require('../../helpers/uploader-helper');
const deviceTypeMap = require('../../helpers/device-types.json');
const KafkaStreamHelper = require('../../helpers/kafkaStreamHelper');
const eventStreamHelper = require('../../helpers/eventStream-helper');
const {
  screenSizeToEnum,
  enumToScreenSize,
} = require('../../helpers/screensize-enum.json');
const deviceTypeScreenSize = require('../../helpers/devicetype-screensize.json');
const {
  RESTRICTED_DEVICE_TYPES,
} = require('../../src/entities/api/device/device.constant');

const krdStates =
  require('../../src/rki/lib/key-request-device.statemachine').states;
const krsHandler = require('../../src/rki/api/krs/krs.handler');
const jobHelper = require('../../helpers/job-helper');
const logger = require('../../lib/logger').mainLogger();
const {
  sendEventProcessDevicesChange,
} = require('../../helpers/process-device-change-events');
const {
  deviceOnboardingAdapter,
} = require('../../helpers/deviceOnboarding-helper');
const {
  parseDeviceBuild,
  updateBluefinConfig,
} = require('../../helpers/bluefin-helper');
const deviceDataIngestionRules = require('./device-data-ingestion-rules.json');
const ignoreDottedStringForDevice = require('./ignore-dotted-for-specific-device.json');

const { invalidRKIDevices } = env;
const { KEY_REQUEST_SESSION_STATUS } = keyRequestHelper;

// Cache for device feature flag.
//   Structure: { device_type: [ features... ] }
const deviceFeatureCache = new NodeCache({
  stdTTL: 60,
});

const deviceDataFullNames = {};
const enableBluefin = env.config.bluefin && env.config.bluefin.enable;

const BLUEFIN_DEVICE_SELECT = enableBluefin
  ? `, s.company_id as current_company, c.default_site as from_default_site, cff.feature_flag as bluefin_feat_config,
        json_object_agg(bf.key, bf.value) FILTER (WHERE bf.key IS NOT NULL) AS bluefin_config_data`
  : '';

const BLUEFIN_DEVICE_QUERY_ROOT = enableBluefin
  ? `LEFT JOIN company c ON c.default_site  = t.site_id
        LEFT JOIN company_feature_flag cff ON cff.company = c.id and cff.feature_flag = 'BLUEFIN_CONFIG'
        LEFT JOIN bluefin_config bf ON bf.target_id = t.target_id`
  : '';

const BLUEFIN_DEVICE_GROUP_BY = enableBluefin
  ? `, s.company_id, c.default_site , cff.feature_flag`
  : '';

const BLUEFIN_SITE_JOIN = enableBluefin
  ? ` LEFT JOIN company_feature_flag cff on cff.company = s.company_id and  cff.feature_flag = 'BLUEFIN'
      LEFT JOIN company c on c.id = s.company_id`
  : '';

const BLUEFIN_SITE_SELECT = enableBluefin
  ? `,cff.feature_flag as bluefin_flag, s.bluefin_location_id as location_id, c.default_site as to_default_site, c.kif`
  : '';

const FIND_SINGLE_DEVICE_SELECT = `
    SELECT
        t.target_id as id, t.site_id, s.name as site_name,s.formatted_address as site_address, t.last_registered, t.last_contact, t.name,
        t.description, t.serial_number, t.key_group_ref, kg.key_group_id, kg.certificate_issuer_id, t.presence, get_device_health( t.target_id ) as status,
        t.gateway_address, t.mac_address, t.subnet_mask, t.release_version,
        json_build_object(
            'suspendedByDeviceUntil', extract(epoch from date_trunc('milliseconds', ars.suspended_by_device_until) ) * 1000,
            'suspendedFrom', extract(epoch from date_trunc('milliseconds', ars.suspended_from) ) * 1000,
            'suspendedUntil', extract(epoch from date_trunc('milliseconds', ars.suspended_until) ) * 1000,
            'suspended', coalesce(NOW() >= ars.suspended_from AND NOW() <= ars.suspended_until, false)
        ) as alarm_rules_settings,
        coalesce(t.real_ip_address, t.ip_address) as ip_address,
        json_build_object(
            'id', t.device_type,
            'name', cpt.display_name,
            'screenSize', t.screen_size
        ) as device_type,
        json_build_object(
            'id', t.prompt_set,
            'name', ps.name,
            'version', ps.version
        ) as prompt_set,
        kg.ksn_prefix as ksn,
        gp.playlist_json ->> 'guid' AS playlist_id,
        t.last_rki AS last_successful_rki,
        s.key_group_id AS site_keygroup_id,
        (
          SELECT COALESCE(dh.timestamp, rah.triggered_timestamp)
            FROM report_alarm_history rah 
            LEFT JOIN ics_state._device_health dh 
            ON rah.device_id = dh.device_id
            WHERE rah.device_id = t.target_id
            AND rah.alarm_type = (
                CASE GET_DEVICE_HEALTH(t.target_id)
                WHEN 'OUT_OF_SERVICE' THEN 'device_out_of_service'
                WHEN 'UNKNOWN' THEN 'device_unreachable' END
            )
            AND rah.remedy_timestamp IS NULL
            AND dh.is_critical
            ORDER BY rah.triggered_timestamp DESC NULLS LAST
            LIMIT 1
        ) AS status_alarm_ts,
        (
            SELECT JSON_AGG(
                JSON_BUILD_OBJECT(
                    'state', dh.name,
                    'value', dh.value
                ) )
            FROM ics_state._device_health dh
            WHERE t.target_id = dh.device_id
            AND t.active
            AND get_device_health(t.target_id) = 'OUT_OF_SERVICE'
            AND dh.name != 'heartbeat'
            AND dh.is_critical
        ) as oos_conditions
`;

const FIND_SINGLE_DEVICE_QUERY_ROOT = `
    FROM target t
        JOIN LATERAL get_device_health(t.target_id) device_health ON TRUE
        JOIN site s ON s.site_id = t.site_id
        JOIN user_site_authorization u ON u.site_id = s.site_id
        JOIN company_product_type cpt ON cpt.device_type = t.device_type
        LEFT JOIN prompt_set ps ON t.prompt_set = ps.id
        LEFT JOIN ics_alarm.alarm_rules_settings ars ON ars.device_id = t.target_id
        LEFT JOIN key_group kg ON t.key_group_ref = kg.key_group_ref AND kg.company_id = s.company_id
        LEFT JOIN gstv_playlist gp ON
            gp.site_id = t.site_id AND
            gp.updated_on >= NOW() - INTERVAL '24 hours'
        ${BLUEFIN_DEVICE_QUERY_ROOT}
    WHERE
        t.active IS true`;

const FIND_SINGLE_DEVICE_QUERY = `${FIND_SINGLE_DEVICE_SELECT}  ${BLUEFIN_DEVICE_SELECT} ${FIND_SINGLE_DEVICE_QUERY_ROOT}`;

const FIND_DEVICE_FEATURE_FLAG = 'SELECT * FROM device_feature_flag';

const FIND_DEVICE_BY_SERIAL_NUMBER_FOR_SYSTEM_USER = `
    SELECT * FROM target t
    WHERE t.active IS true AND t.serial_number = $1
`;
const COUNT_DEVICE_QUERY = `
SELECT COUNT(DISTINCT(t.*))
FROM target t
    JOIN site s ON s.site_id = t.site_id
    JOIN user_site_authorization u ON u.site_id = s.site_id
    JOIN company_product_type cpt ON cpt.device_type = t.device_type
    JOIN LATERAL get_device_health(t.target_id) device_health ON TRUE
     WHERE
        t.active IS true `;
function deduplicateRecords(records) {
  // Grouped records with same ds, dn and ts
  const groupedItems = records.reduce((obj, item) => {
    const data = item.Data;
    const key = `${data.ds}:${data.dn}:${data.ts}`;

    obj[key] = obj[key] || []; // eslint-disable-line no-param-reassign
    obj[key].push(item);

    return obj;
  }, {});

  const updatedRecords = [];
  // Per each group (duplicates), extract item ts and increment it by index
  /* eslint-disable array-callback-return */
  Object.keys(groupedItems).map(key => {
    const duplicated = groupedItems[key];

    duplicated.forEach((_element, index) => {
      const item = duplicated[index];
      const data = item.Data;

      data.ts += index;
      updatedRecords.push({
        Data: JSON.stringify(data),
        PartitionKey: item.PartitionKey,
      });
    });
  });

  return updatedRecords;
}

function appendParams(
  sqlStr,
  paramList,
  searchFilter,
  presence,
  statuses,
  companyFilter,
  userId,
  companyId,
  deviceType,
  alarms,
  showHiddenDevices,
  oosFilter
) {
  let queryStr = `${sqlStr} WHERE t.active`;

  if (!userId) {
    throw new restify.errors.UnauthorizedError('No userId present');
  }
  paramList.push(userId);
  queryStr = `${queryStr} AND u.user_id = $${paramList.length}`;
  paramList.push(companyId);
  queryStr = `${queryStr} AND cpt.company = $${paramList.length}`;

  if (companyFilter) {
    paramList.push(companyFilter);
    queryStr = `${queryStr} AND s.id = $${paramList.length}`;
  }

  if (!showHiddenDevices) {
    queryStr = `${queryStr} AND s.visible`;
  }

  if (deviceType) {
    // add support for multiple device types
    paramList.push(deviceType.split(','));
    queryStr = `${queryStr} AND t.device_type = ANY( $${paramList.length} )`;
  }

  if (searchFilter) {
    const q = `%${searchFilter.replace(/([%_])/g, '\\$1').toLowerCase()}%`;
    paramList.push(q);
    queryStr = `${queryStr} AND (
            LOWER(t.name) LIKE $${paramList.length} OR
            LOWER(t.device_type) LIKE $${paramList.length} OR
            LOWER(t.serial_number) LIKE $${paramList.length} OR
            LOWER(t.description) LIKE $${paramList.length} OR
            LOWER(t.name) LIKE $${paramList.length} OR
            CASE
                WHEN t.real_ip_address IS NULL THEN LOWER(t.ip_address) LIKE $${paramList.length}
                ELSE LOWER(t.real_ip_address) LIKE $${paramList.length}
            END
            )`;
  }

  if (presence) {
    paramList.push(presence);
    queryStr = `${queryStr} AND t.presence = $${paramList.length} `;
  }

  // Add OUT_OF_SERVICE status filter when OOS condition filter is added.
  if (oosFilter && Array.isArray(oosFilter)) {
    if (statuses && Array.isArray(statuses)) {
      if (!statuses.includes('OUT_OF_SERVICE')) {
        statuses.push('OUT_OF_SERVICE');
      }
    } else {
      // In case of statue parameter is empty.
      // eslint-disable-next-line no-param-reassign
      statuses = ['OUT_OF_SERVICE'];
    }
  }

  if (statuses && Array.isArray(statuses) && statuses.length) {
    paramList.push(statuses);
    const condition = `device_health = ANY( $${paramList.length} )`;
    queryStr = `${queryStr} AND ${condition} `;
  }

  // OOS category filter
  // TODO (OPT Health Phase 2): use exact value/s for invenco.system.g6opt.upc-lasttamper-event instead of != 'none'
  if (oosFilter && Array.isArray(oosFilter) && oosFilter.length) {
    const oosMetrics = deviceHelper.getOOSConditionMetrics(oosFilter);
    const oosCategoryFilters = [];
    let hasG6UPCTamper = false;
    // eslint-disable-next-line no-restricted-syntax
    for (const item of oosMetrics) {
      // eslint-disable-next-line no-restricted-syntax
      for (const [k, v] of Object.entries(item)) {
        if (k !== 'invenco.system.g6opt.upc-lasttamper-event') {
          oosCategoryFilters.push(`( dh.name = '${k}' AND dh.value = '${v}' )`);
        } else {
          hasG6UPCTamper = true;
        }
      }
    }

    if (hasG6UPCTamper) {
      oosCategoryFilters.push(
        "( dh.name = 'invenco.system.g6opt.upc-lasttamper-event' AND dh.value != 'none' )"
      );
    }

    const condition = `
            AND (
                device_health != 'OUT_OF_SERVICE' OR
                ( device_health = 'OUT_OF_SERVICE' AND
                    t.target_id IN (
                        SELECT dh.device_id FROM ics_state._device_health dh
                        WHERE dh.device_id = t.target_id AND
                        (
                            ${oosCategoryFilters.join(' OR ')}
                        )
                    )
                )
            )
        `;
    queryStr += `${oosCategoryFilters.length > 0 ? condition : ''} `;
  }

  if (alarms && alarms.length) {
    const newAlarms = alarmsHelper.updateAlarmParams(alarms);
    paramList.push(newAlarms);
    queryStr = `${queryStr}
        AND da.status
        AND da.code LIKE ANY( $${paramList.length} )`;
  }

  return { queryStr, paramList };
}

function loadDevicesFeatureFlagMap() {
  return co(function* run() {
    const stat = deviceFeatureCache.getStats();
    if (!stat.keys) {
      const deviceFeatures = yield server.db.read.rows(
        FIND_DEVICE_FEATURE_FLAG
      );
      const featureMap = {};
      deviceFeatures.forEach(feature => {
        const features = featureMap[feature.deviceType];
        if (_.isArray(features)) {
          features.push(feature.featureFlag);
        } else {
          featureMap[feature.deviceType] = [feature.featureFlag];
        }
      });

      Object.keys(featureMap).forEach(key => {
        deviceFeatureCache.set(key, featureMap[key]);
      });
    }
  });
}

function getKeyGroupBySiteId(siteId) {
  const FIND_KEY_GROUP_BY_SITE_ID = `
        SELECT kg.key_group_id, kg.key_group_name, kg.key_group_ref
        FROM key_group kg
            LEFT JOIN site s ON s.key_group_id = kg.key_group_id
        WHERE s.site_id = $1`;

  return co(function* execute() {
    return yield server.db.read.row(FIND_KEY_GROUP_BY_SITE_ID, [siteId]);
  });
}

function getKeyGroupById(keyGroupId, companyId) {
  const FIND_KEY_GROUP_BY_ID = `
            SELECT * FROM key_group kg
            WHERE kg.id = $1 AND company_id = $2;`;

  return co(function* execute() {
    return yield server.db.read.row(FIND_KEY_GROUP_BY_ID, [
      keyGroupId,
      companyId,
    ]);
  });
}

async function queryDeviceById(userId, deviceId, companyId) {
  const getDeviceByQuery = `${FIND_SINGLE_DEVICE_QUERY} AND
        t.target_id = $1 AND
        u.user_id = $2 AND
        cpt.company = $3
    GROUP BY
        t.target_id, s.name, s.formatted_address, cpt.display_name, ps.name, ps.version, ars.suspended_by_device_until,
        ars.suspended_from, ars.suspended_until, kg.ksn_prefix, gp.playlist_json ->> 'guid',
        s.key_group_id, kg.key_group_id, kg.certificate_issuer_id ${BLUEFIN_DEVICE_GROUP_BY}
    `;

  const device = await server.db.read.row(getDeviceByQuery, [
    deviceId,
    userId,
    companyId,
  ]);

  if (device) {
    device.statusStr = device.status;
    device.status = deviceHelper.translateDeviceHealthToNumber(device.status);
    device.config = await deviceHelper.getDeviceConfigs(device);
    if (device.oosConditions && device.oosConditions.length) {
      device.oosConditions = device.oosConditions
        .map(item => {
          const match = deviceHelper.getOOSCategoryAndCondition({
            name: item.state,
            value: item.value,
          });
          if (match) {
            const { category, condition } = match;
            return { category, condition };
          }
          return undefined;
        })
        .filter(Boolean);
    }
  }

  return device;
}

async function queryDeviceBySerialNumber(userId, serialNumber, companyId) {
  const getDeviceByQuery = `${FIND_SINGLE_DEVICE_QUERY} AND
        t.serial_number = $1 AND
        u.user_id = $2 AND
        cpt.company = $3 AND
        s.company_id = $3
    GROUP BY
        t.target_id, s.name, s.formatted_address, cpt.display_name, ps.name, ps.version, ars.suspended_by_device_until,
        ars.suspended_from, ars.suspended_until, kg.ksn_prefix, gp.playlist_json ->> 'guid',
        s.key_group_id, kg.key_group_id, s.company_id, kg.certificate_issuer_id ${BLUEFIN_DEVICE_GROUP_BY} 
    `;

  logger.info(
    { getDeviceByQuery, serialNumber, userId, companyId },
    'queryDeviceBySerialNumber SQL Query'
  );
  const device = await server.db.read.row(getDeviceByQuery, [
    serialNumber,
    userId,
    companyId,
  ]);
  if (device) {
    device.statusStr = device.status;
    device.status = deviceHelper.translateDeviceHealthToNumber(device.status);
    device.certificateIssuerCodes =
      await deviceHelper.getCertificateIssuerCodes(device.id);
    if (device.oosConditions && device.oosConditions.length) {
      device.oosConditions = device.oosConditions
        .map(item => {
          const match = deviceHelper.getOOSCategoryAndCondition({
            name: item.state,
            value: item.value,
          });
          if (match) {
            const { category, condition } = match;
            return { category, condition };
          }
          return undefined;
        })
        .filter(Boolean);
    }
  }

  return device;
}

async function queryMainAuxiliaryDevice(
  deviceId,
  terminalId,
  terminalRank,
  userId,
  companyId,
  siteId,
  ip,
  configData
) {
  // Following ADDITIONAL_JOIN_TABLE_CLAUSE and IP_CLAUSE are added to the main SQL when search only for auxiliary device.
  let ADDITIONAL_JOIN_TABLE_CLAUSE = '';
  let IP_CLAUSE = '';
  if (terminalRank && terminalRank === 'aux') {
    ADDITIONAL_JOIN_TABLE_CLAUSE =
      'JOIN device_states as ds3 ON ds3.device_id = t.target_id';
    IP_CLAUSE =
      "AND ds3.state = 'invenco.system.cfg.net-ip-addr' and ds3.value = $7";
  }

  const findMainAuxiliaryDeviceQuery = `${FIND_SINGLE_DEVICE_SELECT}
        FROM target t
            JOIN site s ON s.site_id = t.site_id
            JOIN user_site_authorization u ON u.site_id = s.site_id
            JOIN company_product_type cpt ON cpt.device_type = t.device_type
            LEFT JOIN prompt_set ps ON t.prompt_set = ps.id
            LEFT JOIN ics_alarm.alarm_rules_settings ars ON ars.device_id = t.target_id
            LEFT JOIN key_group kg ON t.key_group_ref = kg.key_group_ref AND kg.company_id = s.company_id
            LEFT JOIN gstv_playlist gp ON
                gp.site_id = t.site_id AND
                gp.updated_on >= NOW() - INTERVAL '24 hours'
            JOIN device_states as ds1 ON ds1.device_id = t.target_id
            JOIN device_states as ds2 ON ds2.device_id = t.target_id
            ${ADDITIONAL_JOIN_TABLE_CLAUSE}
        WHERE
            ( ds1.state = 'invenco.system.cfg.net-terminal-id' and ds1.value = $1 and ds1.device_id != $2) AND
            ds2.state = 'invenco.system.cfg.net-terminal-rank' and ds2.value = $3 AND
            t.active IS true AND
            u.user_id = $4 AND
            cpt.company = $5 AND
            t.site_id = $6
        ${IP_CLAUSE}
        GROUP BY
            t.target_id, s.name, s.formatted_address, cpt.display_name, ps.name, ps.version, ars.suspended_by_device_until,
            ars.suspended_from, ars.suspended_until, kg.ksn_prefix, gp.playlist_json ->> 'guid',
            s.key_group_id, kg.key_group_id, kg.certificate_issuer_id
        LIMIT 1;
    `;

  const device =
    terminalRank === 'aux'
      ? await server.db.replica.row(findMainAuxiliaryDeviceQuery, [
          terminalId,
          deviceId,
          terminalRank,
          userId,
          companyId,
          siteId,
          ip,
        ])
      : await server.db.replica.row(findMainAuxiliaryDeviceQuery, [
          terminalId,
          deviceId,
          terminalRank,
          userId,
          companyId,
          siteId,
        ]);

  if (device) {
    device.status = deviceHelper.translateDeviceHealthToNumber(device.status);

    device.configData = configData;
    if (device.oosConditions && device.oosConditions.length) {
      device.oosConditions = device.oosConditions
        .map(item => {
          const match = deviceHelper.getOOSCategoryAndCondition({
            name: item.state,
            value: item.value,
          });
          if (match) {
            const { category, condition } = match;
            return { category, condition };
          }
          return undefined;
        })
        .filter(Boolean);
    }
  }

  return device;
}

async function isSyncSite(siteId) {
  return !!(await server.db.read.row(
    `
        SELECT 1
        FROM device_sync_config dsc
        WHERE dsc.source_site_id = $1
        LIMIT 1
    `,
    [siteId]
  ));
}

function transformCSVResult(result) {
  const data = result;

  // for location
  if (data.site_address) {
    data.location = data.site_address;
  }

  // for device type
  if (data.device_type) {
    data.device_type = data.device_type.name;
  }

  // for status alarm ts
  if (data.status_alarm_ts) {
    data.status_alarm_ts = moment(data.status_alarm_ts).utc().format();
  }

  // for OOS category and  condition
  if (data.oos_conditions && data.oos_conditions.length) {
    data.oos_category_condition = data.oos_conditions
      .map(item => {
        const match = deviceHelper.getOOSCategoryAndCondition({
          name: item.state,
          value: item.value,
        });
        if (match) {
          const { category, condition } = match;
          return `${category} / ${condition}`;
        }
        return undefined;
      })
      .filter(Boolean)
      .join();
  }

  return data;
}

const toDottedString = deviceData => `${deviceData.ds}.${deviceData.dn}`;
const currentPriceData = dottedString =>
  dottedString === 'igl.infx-fps.app.current-prices-v1';
const scheduledPriceData = dottedString =>
  dottedString === 'igl.infx-fps.app.scheduled-prices-v1';
const updatePriceReportData = dottedString =>
  dottedString === 'igl.infx-fps.app.update-report-v1';
const updateDeviceSyncReport = dottedString =>
  dottedString === 'igl.infx-fps.app.device-sync-report-v1';

const eventDetailType = deviceData => {
  const dottedString = toDottedString(deviceData);
  if (scheduledPriceData(dottedString)) {
    return (
      env.config.AWS.eventBus.fuelPriceService.rules.updateScheduledPrice ||
      'fps_update_scheduled_prices'
    );
  }
  if (
    updatePriceReportData(dottedString) ||
    updateDeviceSyncReport(dottedString)
  ) {
    return (
      env.config.AWS.eventBus.fuelPriceService.rules.updatePriceReport ||
      'fps_update_price_report'
    );
  }

  return (
    env.config.AWS.eventBus.fuelPriceService.rules.updateCurrentPrice ||
    'fps_update_current_prices'
  );
};

const okToSendFuelPriceServiceEvents = deviceData => {
  const dottedString = toDottedString(deviceData);
  return (
    env.config.AWS.eventBus &&
    env.config.AWS.eventBus.fuelPriceService &&
    (currentPriceData(dottedString) ||
      updatePriceReportData(dottedString) ||
      updateDeviceSyncReport(dottedString) ||
      scheduledPriceData(dottedString))
  );
};

const sendFuelPriceEventsToEventBridge = async ({ device, deviceData }) => {
  if (!okToSendFuelPriceServiceEvents(deviceData)) {
    return Promise.resolve();
  }
  const eventBridgeParams = {
    data: {
      dottedString: toDottedString(deviceData),
      tenantId: device.companyId,
      siteId: device.siteId,
      deviceId: device.deviceId,
      fuelPricePayload: deviceData.dv,
      priceDate: new Date(deviceData.ts).toISOString(),
    },
    detailType: eventDetailType(deviceData),
    source: constants.eventBusSources.FPS_UPDATE_FUEL_PRICES,
  };

  return AWS.sendEventsToEventBridge(eventBridgeParams);
};

const doSendLatestFuelPriceDataEvent = ({
  fuelPriceRecords,
  qCalls,
  device,
}) => {
  const latestFuelPriceRecords = [];
  logger.info({ fuelPriceRecords }, 'doSendLatestFuelPriceDataEvent');
  fuelPriceRecords.forEach((deviceData, idx) => {
    let { ts } = deviceData;
    const dottedString = toDottedString(deviceData);
    if (
      updatePriceReportData(dottedString) ||
      updateDeviceSyncReport(dottedString)
    ) {
      ts += idx;
    }
    if (
      _.isEmpty(latestFuelPriceRecords) ||
      updatePriceReportData(dottedString) ||
      updateDeviceSyncReport(dottedString)
    ) {
      latestFuelPriceRecords.push({ ...deviceData, ts });
    } else {
      const latestPriceDataIdx = latestFuelPriceRecords.findIndex(
        lfpr =>
          lfpr.companyId === deviceData.companyId &&
          lfpr.siteId === deviceData.siteId &&
          lfpr.deviceId === deviceData.deviceId &&
          lfpr.ds === deviceData.ds &&
          lfpr.dn === deviceData.dn
      );
      if (latestPriceDataIdx === -1) {
        latestFuelPriceRecords.push({ ...deviceData });
      } else if (
        deviceData.ts >= latestFuelPriceRecords[latestPriceDataIdx].ts
      ) {
        latestFuelPriceRecords[latestPriceDataIdx] = { ...deviceData };
      }
    }
  });
  logger.info(
    { fuelPriceRecords, latestFuelPriceRecords },
    'doSendLatestFuelPriceDataEvent'
  );
  latestFuelPriceRecords.forEach(deviceData => {
    qCalls.push(sendFuelPriceEventsToEventBridge({ device, deviceData }));
  });
};

const convertToBooleanValue = (device, configurationKey) => {
  const configuration = device.configData[configurationKey];
  if (configuration) {
    if (configuration.value === '') configuration.value = 'false';
    else configuration.value = configuration.value === '1' ? 'true' : 'false';

    if (configuration.pending)
      configuration.pending = configuration.pending === '1' ? 'true' : 'false';
  }
};

function buildScreenSizeQuery(refererType, type, subDeviceType, idx) {
  let where = '';
  let param = null;
  let enumValue = null;
  let isInvalidSubDeviceType = false;

  if (refererType === 'software' && type === 'G7-100' && subDeviceType) {
    enumValue = screenSizeToEnum[deviceTypeScreenSize[subDeviceType]];
    isInvalidSubDeviceType = !enumValue;
  } else if (refererType === 'media') {
    enumValue = screenSizeToEnum[deviceTypeScreenSize[type]];
  }
  if (enumValue) {
    where += ` AND (t.screen_size = $${idx} OR t.screen_size IS NULL)`;
    param = enumValue;
  }
  if (isInvalidSubDeviceType) {
    where += ` AND t.screen_size = $${idx}`;
    param = -1;
  }
  return { where, param };
}

function configureAccessibilityMode(device) {
  const cfgAccessibilityMode = device.configData['cfg.lcd-accessibility-mode'];

  if (cfgAccessibilityMode) {
    if (
      device.config.accessibilityMode === null &&
      cfgAccessibilityMode.value === ''
    ) {
      cfgAccessibilityMode.value = 'true';
    } else {
      cfgAccessibilityMode.value =
        cfgAccessibilityMode.value === '1' ? 'true' : 'false';
    }

    if (cfgAccessibilityMode.pending) {
      cfgAccessibilityMode.pending =
        cfgAccessibilityMode.pending === '1' ? 'true' : 'false';
    }
  }

  // Default make enable.
  if (device.config.accessibilityMode === null && !cfgAccessibilityMode) {
    // eslint-disable-next-line no-param-reassign
    device.configData['cfg.lcd-accessibility-mode'] = {
      value: 'true',
    };
  }
}

function configureAdaPuck(device) {
  let adaPuckConfig = null;
  try {
    if (device.configData && device.configData['g7opt.sys-aux-keypad']) {
      adaPuckConfig = JSON.parse(
        deviceHelper.handleSlashes(
          device.configData['g7opt.sys-aux-keypad'].value
        )
      );
    }
    if (
      adaPuckConfig &&
      adaPuckConfig.board &&
      Number.isInteger(adaPuckConfig.board.id) &&
      typeof adaPuckConfig.connected === 'boolean'
    ) {
      const { board, connected } = adaPuckConfig;
      const connectedStatus = connected ? 'Connected' : 'Disconnected';
      const deviceDetails = board.id === 0 ? ' (Basic)' : ' (Advanced)';
      // eslint-disable-next-line no-param-reassign
      device.configData['g7opt.sys-aux-keypad'].value =
        connectedStatus + deviceDetails;
    } else {
      // eslint-disable-next-line no-param-reassign
      delete device.configData['g7opt.sys-aux-keypad'];
    }
  } catch (error) {
    // eslint-disable-next-line no-param-reassign
    delete device.configData['g7opt.sys-aux-keypad'];
    logger.error(error, 'configureAdaPuck');
  }
}

/*

  THIS FUNCTION IS DEPRECATED FROM api-nodejs AND MOVED TO ics-device REPOSITORY
  https://github.com/Invenco-Cloud-Systems-ICS/ics-device

*/
module.exports = {
  ingestDeviceData(req, res, next) {
    const deviceId = req.params.id;
    logger.error(
      `[IngestDeviceData] DeviceID: ${deviceId}-- DeviceBody-- ${JSON.stringify(req.body)}`
    );

    if (req.user.sub !== deviceId) {
      return next(new restify.ForbiddenError('No permission'));
    }

    // ICS-2749 Incoming requests with empty bodies return 400
    if (!req.body) {
      return next(new restify.errors.BadRequestError('No payload'));
    }
    req.log.debug(
      {
        ingestData: req.body,
      },
      '[Device].[IngestData] Data Request body for ingestion data.'
    );
    const bodyBeforeNormalisation = Array.isArray(req.body)
      ? req.body
      : [req.body];
    let body = deviceHelper.getNormalisedDeviceData(
      bodyBeforeNormalisation,
      deviceId,
      deviceDataFullNames,
      env.config.normaliseDottedStrings,
      deviceDataIngestionRules
    );

    const deviceTime = req.headers['ics-device-current-time'];
    const receivedTime = new Date().toISOString();
    const encoder = new textEncoding.TextEncoder('utf-8');

    return co(function* execute() {
      const device = yield server.db.read.row(
        `
                SELECT d.target_id as device_id,
                 d.serial_number as device_serial,
                 p.device_type,
                 d.site_id,
                 s.name as site_name,
                 s.company_id,
                 c.data_delivery_stream,
                 s.timezone_id,
                 e.reference_id as gvr_site_id,
                 d.fuel_position,
                 array_to_string(array_agg(t.tag_id), ',') as tag_ids
                FROM target d
                    LEFT JOIN site_tag t on t.site_id = d.site_id AND t.deleted = false
                    JOIN product_type p on d.device_type = p.device_type
                    LEFT JOIN site s on d.site_id = s.site_id
                    LEFT JOIN site_external_references e on e.site_id = s.site_id AND e.reference_type = 'GVR' AND e.deleted = FALSE
                    LEFT JOIN company c on c.id = s.company_id
                WHERE d.target_id = $1 AND d.active IS true AND d.presence = 'PRESENT' AND d.last_registered IS NOT NULL
                GROUP BY d.target_id, d.serial_number, p.device_type, d.site_id, s.company_id, s.name, s.timezone_id, c.data_delivery_stream, e.reference_id;`,
        [deviceId]
      );
      if (!device) {
        return next(
          new restify.NotFoundError(`Device with id ${deviceId} not found`)
        );
      }

      const { subPlatform, dualDisplayEnabled, sysContactlessModule } =
        yield deviceHelper.getDeviceConfigs(device, [
          'subPlatform',
          'dualDisplayEnabled',
          'sysContactlessModule',
        ]);

      const attributeData = yield server.db.read.rows(
        `
        SELECT caev.attribute_definition_id, 
        ad.attribute_name as attribute_name ,
        caev.attribute_override_value as attribute_id
        FROM custom_attribute.custom_attribute_entity_values caev
        INNER JOIN custom_attribute.attribute_definition ad on
        caev.attribute_definition_id = ad.attribute_definition_id
        WHERE caev.entity_id = $1
        AND (ad.attribute_name = 'Tandem ID' OR ad.attribute_name = 'Merchant ID')
        AND not caev.deleted AND not caev.is_staged;`,
        [device.siteId]
      );
      req.log.debug(
        ` ${JSON.stringify(attributeData)} [Device].[IngestData] customattribute data`
      );
      body = yield Promise.all(
        body.map(async item => {
          // eslint-disable-next-line no-param-reassign
          item.dv =
            typeof item.dv === 'string' ? item.dv : JSON.stringify(item.dv);
          // check for missing certificates
          const { deviceType, companyId } = device;
          if (
            (deviceType === 'G6-300' || deviceType === 'G7-100') &&
            item.dn === 'job-sys.generate-csr'
          ) {
            const companyFeatureFlags =
              await companyHelper.getAllFeatureFlagsByCompanyIdInCache(
                companyId
              );
            const hasJBZBundleFeature = companyFeatureFlags.includes(
              constants.featureFlags.SEQ3_SEQ5_CERT_HANDLER
            );
            logger.error(
              `[JbzFeatureFlag] SEQ3-SEQ5: ${JSON.stringify(hasJBZBundleFeature)}`
            );
            if (hasJBZBundleFeature) {
              deviceHelper.startJBZBundlingToLoadCertificates(
                deviceId,
                item.dv
              );
            } else {
              logger.error(
                `${constants.featureFlags.SEQ3_SEQ5_CERT_HANDLER} feature flag not found for the company : ${companyId}`
              );
            }
          }

          return item;
        })
      );

      // Filter the body after processing
      body = body.filter(
        item =>
          !ignoreDottedStringForDevice.some(filterValue => {
            const isDeviceStateMatching =
              toDottedString(item) === filterValue.dottedString &&
              item.dv === filterValue.dv &&
              device.deviceType === filterValue.deviceType;

            const isSubPlatformMatching =
              !filterValue.subPlatform ||
              filterValue.subPlatform === subPlatform;
            const isDualDisplayMatching =
              !filterValue.dualDisplayEnabled ||
              filterValue.dualDisplayEnabled === dualDisplayEnabled;
            const isSysContactlessModuleMatching =
              !filterValue.sysContactlessModule ||
              filterValue.sysContactlessModule === sysContactlessModule;

            return (
              isDeviceStateMatching &&
              isSubPlatformMatching &&
              isDualDisplayMatching &&
              isSysContactlessModuleMatching
            );
          })
      );

      const payload = JSON.stringify(body);
      let qCalls = [];

      if (
        env.config.AWS.sqs &&
        env.config.AWS.sqs.urls &&
        env.config.AWS.sqs.urls.length > 0
      ) {
        qCalls = env.config.AWS.sqs.urls.map(sqsUrl => {
          const params = {
            MessageBody: payload,
            QueueUrl: sqsUrl,
            DelaySeconds: 0,
            MessageAttributes: {
              'device-id': {
                DataType: 'String',
                StringValue: device.deviceId.toString(),
              },
              'device-type': {
                DataType: 'String',
                StringValue: device.deviceType.toString(),
              },
              'received-time': {
                DataType: 'String',
                StringValue: receivedTime,
              },
              'site-id': {
                DataType: 'String',
                StringValue: device.siteId.toString(),
              },
            },
          };
          if (device.tagIds) {
            params.MessageAttributes['tag-ids'] = {
              DataType: 'String',
              StringValue: device.tagIds.toString(),
            };
          }

          if (deviceTime) {
            params.MessageAttributes['device-time'] = {
              DataType: 'String',
              StringValue: deviceTime,
            };
          }

          if (device.gvrSiteId) {
            params.MessageAttributes['gvr-site-id'] = {
              DataType: 'String',
              StringValue: device.gvrSiteId,
            };
            params.MessageAttributes['fuel-position'] = {
              DataType: 'Integer',
              Value: device.fuelPosition,
            };
          }

          // Check if message bigger than 256KB
          const payloadSize = encoder.encode(payload).length;
          if (payloadSize < 240000) {
            return AWS.sendSqsMessage(params);
          }

          req.log.info(
            `[Device].[IngestData] Message exceeded supported limit of 256KB. Size ${payloadSize}. Uploading to S3 directly.`
          );
          return uploaderHelper
            .uploadLargePayloadToS3(req, device, sqsUrl)
            .then(newParams => AWS.sendSqsMessage(newParams));
        });
      }

      const fuelPriceRecords = [];
      const records = body.map(item => {
        const deviceData = item;
        // Use stringify instead of toString so object values will be properly converted to string
        // Check if already in string so object strings will not be stringified again
        deviceData.dv =
          typeof deviceData.dv === 'string'
            ? deviceData.dv
            : JSON.stringify(deviceData.dv);
        deviceData.deviceId = device.deviceId.toString();
        deviceData.siteId = device.siteId.toString();
        deviceData.siteName = device.siteName;
        deviceData.deviceSerial = device.deviceSerial;
        deviceData.deviceType = device.deviceType;
        deviceData.companyId = device.companyId;
        deviceData.merchantId = null;
        deviceData.tandemId = null;

        if (attributeData && attributeData.length > 0) {
          attributeData.forEach(attribute => {
            if (attribute.attributeName === 'Merchant ID') {
              deviceData.merchantId = attribute.attributeId || null;
            } else if (attribute.attributeName === 'Tandem ID') {
              deviceData.tandemId = attribute.attributeId || null;
            }
          });
        }
        req.log.debug(
          ` ${deviceData.merchantId} [Device].[IngestData] deviceData.merchantId data.`
        );
        if (deviceData.ap) {
          deviceData.ap = deviceData.ap.toString();
        }

        let { ts } = deviceData; // original timestamp as ISO8601 format
        const time = new Date(deviceData.ts);

        if (Number.isNaN(time.getTime())) {
          req.log.error(
            { deviceId: deviceData.deviceId, invalidDataTs: deviceData.ts },
            `[Device].[IngestData] Device ${deviceData.deviceId} send a message with invalid ts ${deviceData.ts}.`
          );

          deviceData.ts = Date.now();
          ts = new Date(deviceData.ts).toISOString(); // if malformed use current ISO timestamp
        } else {
          deviceData.ts = time.getTime();
        }

        if (
          device.timezoneId &&
          timezone.tz.names().includes(device.timezoneId)
        ) {
          // convert to site local timezone for aggregation
          const timeInUTC = timezone.tz(deviceData.ts, 'UTC');
          deviceData.timeInSiteTZ = timeInUTC
            .clone()
            .tz(device.timezoneId)
            .format();
        } else {
          deviceData.timeInSiteTZ = ts; // if no valid timezone defined for site use UTC
        }

        if (device.tagIds) {
          deviceData.tagIds = device.tagIds;
        }
        if (deviceTime) {
          deviceData.deviceTime = deviceTime;
        }
        if (receivedTime) {
          deviceData.receivedTime = receivedTime;
        }

        const getFuelPositionData = originalValue => {
          if (
            deviceData.dt !== 'st' ||
            deviceData.ds !== 'invenco.system.cfg' ||
            deviceData.dn !== 'net-terminal-id'
          ) {
            return originalValue;
          }
          // eslint-disable-next-line radix
          const newFuelPosition = parseInt(deviceData.dv, 0) || 0;
          if (newFuelPosition < 0 || newFuelPosition > 32767) {
            return 0;
          }
          return newFuelPosition;
        };

        const fuelPosition = getFuelPositionData(device.fuelPosition);
        if (fuelPosition !== device.fuelPosition) {
          device.fuelPosition = fuelPosition;
          deviceHelper.updateFuelPosition(
            deviceData.deviceId,
            device.fuelPosition,
            req
          );
        }

        if (device.gvrSiteId) {
          deviceData.gvrSiteId = device.gvrSiteId;
          deviceData.fuelPosition = device.fuelPosition;
        }
        if (okToSendFuelPriceServiceEvents(deviceData)) {
          fuelPriceRecords.push(deviceData);
        }

        return {
          Data: deviceData,
          PartitionKey: deviceData.deviceId,
        };
      });

      if (!_.isEmpty(fuelPriceRecords)) {
        doSendLatestFuelPriceDataEvent({ fuelPriceRecords, qCalls, device });
      }

      if (env.config.AWS.kinesis) {
        // We need to remove duplicates based on ds, dn, ts properties
        // because it might cause exceptions in DynamoDB.
        //
        // To fix this we need to de-duplicate records based on similar ds, dn and ts values
        // by incrementing ts (ddb sortkey) for duplicate items by 1 ms difference between items
        const updatedRecords = deduplicateRecords(records);
        let index = 0;
        // Process records by batches of 500 to avoid exceeding Kinesis Write size limit (1mb per shard)
        while (index < updatedRecords.length) {
          const items = updatedRecords.slice(index, index + 500);

          qCalls.push(
            AWS.kinesisPutRecords({
              Records: items,
              StreamName: env.config.AWS.kinesis.dataStream,
            })
          );

          if (
            env.config.AWS.kinesis.gvrIntegrationEnabled &&
            env.config.AWS.kinesis.gvrIntegrationDataStream &&
            device.gvrSiteId
          ) {
            qCalls.push(
              AWS.kinesisPutRecords({
                Records: items,
                StreamName: env.config.AWS.kinesis.gvrIntegrationDataStream,
              })
            );
          }

          if (env.config.AWS.eventstream && env.config.AWS.eventstream.enable) {
            req.log.debug(
              { configEventStream: env.config.AWS.eventstream },
              `[Device].[IngestData] checking eventStream Service.`
            );
            eventStreamHelper.putToEventStream(device, items);
          }

          if (
            env.config.AWS.kafka &&
            env.config.AWS.kafka.enable &&
            device.gvrSiteId
          ) {
            const isDataFeedEnabled =
              yield deviceOnboardingAdapter.isFeatureEnabled(
                device.deviceType,
                'telemetry_data_feed'
              );
            if (isDataFeedEnabled)
              KafkaStreamHelper.putDataToKafkaStream(device, items);
          }

          if (device.dataDeliveryStream) {
            qCalls.push(
              AWS.firehosePutRecords({
                Records: items.map(elm => ({ Data: elm.Data })),
                DeliveryStreamName: device.dataDeliveryStream,
              })
            );
          } else {
            req.log.debug(
              `[Device].[IngestData] Data Delivery Stream not found for this company ${device.companyId}. Skipped.`
            );
          }

          index += 500;
        }
      }

      Promise.all(qCalls)
        .then(() => {
          if (!res.headersSent) {
            res.send(204);
          }
          return next();
        })
        .catch(error => {
          req.log.error(
            { error },
            `[Device].[IngestData] failed to send the request to Kinesis or Kafka: ${error.message}.`
          );
          return next(
            new restify.InternalServerError(
              'Failed to send the request to AWS SQS or Kinesis'
            )
          );
        });

      return true;
    }).catch(error => {
      req.log.error(
        { error },
        `[Device].[IngestData] general processing error: ${error.message}.`
      );
      res.send(500, error);
    });
  },

  async getSelf(req, res, next) {
    const deviceId = req.params.id;

    if (req.user.sub !== deviceId) {
      return next(new restify.ForbiddenError('No permission'));
    }

    try {
      try {
        await server.db.write.execute(
          `
                    UPDATE target set
                        last_contact = current_timestamp,
                        ip_address = $2
                    where target_id = $1
                `,
          [deviceId, ipAddressHelper.getIPFromHeader(req)]
        );
      } catch (error) {
        req.log.error(
          { error },
          `[Device].[getSelf] error on update last contact: ${error.message}.`
        );
      }

      const device = await server.db.read.row(
        `
                SELECT
                    d.target_id as device_id,
                    d.device_type,
                    d.serial_number,
                    d.site_id,
                    s.name as site_name,
                    s.timezone_id as timezone,
                    (SELECT COUNT(*) FROM target WHERE site_id = d.site_id) as site_device_count,
                    s.company_id,
                    (SELECT name FROM company WHERE id = s.company_id) as company_name
                FROM target d
                INNER JOIN site s on d.site_id = s.site_id
                WHERE d.target_id = $1
                  AND d.active
            `,
        [deviceId]
      );
      if (!device) {
        return next(
          new restify.NotFoundError(`Device with id ${deviceId} not found`)
        );
      }

      res.send(200, {
        id: device.deviceId,
        serial: device.serialNumber,
        type: device.deviceType,
        site: {
          id: device.siteId,
          name: device.siteName,
          timezone: device.timezone,
          deviceCount: device.siteDeviceCount,
          company: {
            id: device.companyId,
            name: device.companyName,
          },
        },
      });
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getSiteDevicesSummary(req, res, next) {
    return co(function* execute() {
      const userId = req.user.sub;
      const {
        promptSetProfile,
        refererType,
        tags,
        type,
        siteGroups,
        subDeviceType,
        applyStrictMatchTags,
        seqVersion,
        excludeRestrictedDeviceTypes,
      } = req.query;
      const query = req.query.q;
      const params = [userId];
      let idx = 2;
      let where = '';
      if (type || excludeRestrictedDeviceTypes) {
        const rawTypes = [];
        let productTypes;

        if (type) {
          const types = type.split(',');
          productTypes = yield server.db.read.rows(
            `
                    SELECT
                        device_type
                    FROM product_type
                    WHERE
                    device_type = ANY( $1 )
                `,
            [types]
          );
        }

        if (excludeRestrictedDeviceTypes) {
          const regex = new RegExp(RESTRICTED_DEVICE_TYPES.join('|'), 'i');
          if (type) {
            productTypes = productTypes.filter(
              productType => !regex.test(productType.deviceType)
            );
          }
          if (!type) {
            const allProductTypes = yield server.db.read.rows(
              `
                    SELECT
                        device_type
                    FROM product_type
                    `
            );
            productTypes = allProductTypes.filter(
              productType => !regex.test(productType.deviceType)
            );
          }
        }

        productTypes.forEach(productType => {
          const mappedDeviceType =
            deviceTypeMap[productType.deviceType] || productType.deviceType;
          if (!rawTypes[mappedDeviceType]) {
            rawTypes.push(mappedDeviceType);
          }
        });

        // add support for multiple device types
        where = `${where} AND t.device_type = ANY( $${idx} ) `;
        params.push(rawTypes);
        idx += 1;

        if (deviceTypeScreenSize[type] || deviceTypeScreenSize[subDeviceType]) {
          const screenSizeQuery = buildScreenSizeQuery(
            refererType,
            type,
            subDeviceType,
            idx
          );
          where += screenSizeQuery.where;
          if (screenSizeQuery.param !== null) {
            params.push(screenSizeQuery.param);
            idx += 1;
          }
        }
      }

      const { presence } = req.query;
      if (presence) {
        where = `${where} AND t.presence = $${idx}`;
        params.push(presence);
        idx += 1;
      }

      // The search will either contain a query or tags or site groups

      if (query && query.length > 0) {
        // add an escape character '\' prefixing the '%' and '_' special characters
        // to sanitize the query parameter value
        const q = `%${query.replace(/([%_])/g, '\\$1').toLowerCase()}%`;
        where = `
                    ${where} AND LOWER(s.name) LIKE $${idx} `;
        params.push(q);
        idx += 1;
      }
      let strictMatchTagsHavingClause = ' ';

      if (tags && tags.length > 0) {
        const splitTags = tags.split(',');
        const tagsParamsLength = splitTags.length;
        where = `${where} AND tag.name = ANY( $${idx} ) `;
        params.push(splitTags);
        if (applyStrictMatchTags === 'true') {
          strictMatchTagsHavingClause = `Having COUNT(DISTINCT tag.name) = ${tagsParamsLength}`;
        }
        idx += 1;
      }

      if (siteGroups && siteGroups.length > 0) {
        where = `${where} AND asg.name = ANY( $${idx} ) `;
        params.push(siteGroups.split(','));
        idx += 1;
      }
      if (seqVersion && seqVersion !== 'null' && seqVersion !== null) {
        const versionNumber = seqVersion.slice(-1);
        const releaseVersion = `R${versionNumber}`;
        where = `${where} AND (t.release_version <= $${idx} OR t.release_version LIKE $${idx + 1} OR t.release_version is NULL)`;
        params.push(releaseVersion);
        params.push(`${releaseVersion}%`);
        idx += 2;
      }

      let mediaSelect = ' ';
      let mediaWhere = ' ';
      let mediaGroupBy = ' ';
      if (type && screenSizeToEnum[deviceTypeScreenSize[type]]) {
        mediaSelect = 'cpt.device_type as deviceType,';
        mediaWhere =
          'LEFT JOIN company_product_type cpt ON cpt.device_type = t.device_type';
        mediaGroupBy = ', cpt.device_type';
      }

      const SQL = `
              WITH tag AS (
                SELECT id, name FROM tag
              ),
              auth_site_group AS (
                SELECT id, name
                FROM authorization_site_group
              )
              SELECT
                t.target_id,
                t.screen_size,
                t.release_version,
                t.name as target_name,
                t.serial_number,
                t.ip_address,
                s.site_id,
                s.name as site_name,
                s.disable_software_downloads as disable_file_downloads,
                ${mediaSelect}
                COALESCE(json_agg(DISTINCT tag.*) FILTER (WHERE tag.id IS NOT NULL), '[]') as tags,
                COALESCE(json_agg(DISTINCT asg.*) FILTER (WHERE asg.id IS NOT NULL), '[]') AS "siteGroups"
              FROM target t
              JOIN site s ON s.site_id = t.site_id
              JOIN user_site_authorization u ON u.site_id = t.site_id
              LEFT JOIN site_tag st ON st.site_id = s.site_id AND st.deleted = false
              LEFT JOIN tag ON tag.id = st.tag_id
              LEFT JOIN authorization_site_group_site asgs ON asgs.site_id = s.site_id
              LEFT JOIN auth_site_group asg ON asg.id = asgs.authorization_site_group_id
              ${mediaWhere}
              WHERE
                u.user_id = $1 AND
                s.active = TRUE AND
                t.active = TRUE
                ${where}
              GROUP BY
                t.target_id, s.site_id${mediaGroupBy}
              ${strictMatchTagsHavingClause}
              ORDER BY
                s.name, s.site_id, t.name, t.target_id
            `;

      const isFileDownload = req.query.isFileDownload === 'true';
      let deviceSummary = yield server.db.read.rows(SQL, params);
      let siteId;
      let site;

      deviceSummary = deviceSummary.reduce((reduced, device) => {
        if (siteId !== device.siteId) {
          siteId = device.siteId;
          site = {
            id: device.siteId,
            name: device.siteName,
            tags: device.tags,
            siteGroups: device.siteGroups,
            devices: [],
          };
          if (isFileDownload) {
            site.disableFileDownloads = device.disableFileDownloads;
          }
          reduced.push(site);
        }

        site.devices.push({
          id: device.targetId,
          name: device.targetName,
          serialNumber: device.serialNumber,
          screenSize: enumToScreenSize[device.screenSize] || null,
          deviceType: device.devicetype,
          releaseVersion: device.releaseVersion,
        });

        return reduced;
      }, []);

      if (promptSetProfile) {
        const result = yield deviceHelper.validateDevices(
          deviceSummary,
          promptSetProfile
        );
        deviceSummary = result.deviceSummary;
      }

      res.send(deviceSummary);

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  findDevices(req, res, next) {
    const {
      searchFilter,
      presence,
      companyId: companyFilter,
      pageIndex: pageIndexStr,
      pageSize: pageSizeStr,
      order,
      statuses,
      deviceType,
      configs,
      alarms,
      oosFilter,
      showHiddenDevices: showHiddenDevicesStr,
      isCSV,
      showOnlyUnrestrictedDevices,
    } = req.query;

    const userId = req.user.sub;
    const companyId = req.user.company.id;
    const pageIndex = _.parseInt(pageIndexStr) || 0;
    const pageSize = _.parseInt(pageSizeStr) || 100;
    const showHiddenDevices =
      showHiddenDevicesStr === undefined
        ? false
        : JSON.parse(showHiddenDevicesStr);

    return co(function* run() {
      let FIND_COUNT_DEVICE_QUERY = `
                SELECT COUNT(DISTINCT(t.*))
                FROM target t
                    JOIN site s ON s.site_id = t.site_id
                    JOIN user_site_authorization u ON u.site_id = s.site_id
                    JOIN company_product_type cpt ON cpt.device_type = t.device_type
                    JOIN LATERAL get_device_health(t.target_id) device_health ON TRUE
                    `;

      const ALARMS_QUERY =
        'JOIN device_alarms da ON t.target_id = da.device_id AND t.site_id = da.site_id';

      if (alarms && alarms.length) {
        FIND_COUNT_DEVICE_QUERY = `${FIND_COUNT_DEVICE_QUERY} ${ALARMS_QUERY}`;
      }

      const { queryStr, paramList } = appendParams(
        FIND_COUNT_DEVICE_QUERY,
        [],
        searchFilter,
        presence,
        statuses,
        companyFilter,
        userId,
        companyId,
        deviceType,
        alarms,
        showHiddenDevices,
        oosFilter
      );

      const { count } = yield server.db.read.row(queryStr, paramList);

      let results = [];

      let FIND_ALL_DEVICE_QUERY = `
                SELECT
                    t.target_id as id, t.site_id, s.name as site_name, t.last_registered, t.last_contact, t.name,
                    t.description, t.serial_number, t.key_group_ref, t.presence, device_health as status,
                    t.gateway_address, t.mac_address, t.subnet_mask, t.release_version,
                    json_build_object(
                        'suspendedByDeviceUntil', extract(epoch from date_trunc('milliseconds', ars.suspended_by_device_until) ) * 1000,
                        'suspendedFrom', extract(epoch from date_trunc('milliseconds', ars.suspended_from) ) * 1000,
                        'suspendedUntil', extract(epoch from date_trunc('milliseconds', ars.suspended_until) ) * 1000,
                        'suspended', CASE WHEN (ars.suspended_from IS NULL OR ars.suspended_until IS NULL) THEN FALSE
                        ELSE (SELECT (NOW() >= ars.suspended_from) AND (NOW() <= ars.suspended_until))
                        END
                    ) as alarm_rules_settings,
                    CASE
                        WHEN t.real_ip_address IS NOT NULL THEN t.real_ip_address
                        ELSE t.ip_address
                    END as ip_address,
                    json_build_object(
                        'id', t.device_type,
                        'name', cpt.display_name,
                        'screenSize', t.screen_size
                    ) as device_type,
                    kg.ksn_prefix as ksn,
                    json_build_object(
                        'type', ar.value,
                        'status', auxs.value
                    ) as aux_info,
                    (
                        SELECT COALESCE(dh.timestamp, rah.triggered_timestamp)
                        FROM report_alarm_history rah
                        LEFT JOIN ics_state._device_health dh
                        ON rah.device_id = dh.device_id
                        WHERE rah.device_id = t.target_id
                        AND rah.alarm_type = (
                            CASE GET_DEVICE_HEALTH(t.target_id)
                            WHEN 'OUT_OF_SERVICE' THEN 'device_out_of_service'
                            WHEN 'UNKNOWN' THEN 'device_unreachable' END
                        )
                        AND rah.remedy_timestamp IS NULL
                        AND dh.is_critical
                        ORDER BY rah.triggered_timestamp DESC NULLS LAST
                        LIMIT 1
                    ) AS status_alarm_ts,
                    s.formatted_address as site_address,
                    (
                        SELECT JSON_AGG(
                            JSON_BUILD_OBJECT(
                                'state', dh.name,
                                'value', dh.value
                            ) )
                        FROM ics_state._device_health dh
                        WHERE t.target_id = dh.device_id AND device_health = 'OUT_OF_SERVICE'
                        AND dh.name != 'heartbeat'
                        AND dh.is_critical
                    ) as oos_conditions
                FROM target t
                    JOIN LATERAL get_device_health(t.target_id) device_health ON TRUE
                    JOIN site s ON s.site_id = t.site_id
                    JOIN user_site_authorization u ON u.site_id = s.site_id
                    JOIN company_product_type cpt ON cpt.device_type = t.device_type
                    LEFT JOIN ics_alarm.alarm_rules_settings ars ON ars.device_id = t.target_id
                    LEFT JOIN key_group kg ON t.key_group_ref = kg.key_group_ref AND kg.company_id = s.company_id
                    LEFT JOIN LATERAL (
                        SELECT d.value
                        FROM device_states d
                        WHERE state = 'invenco.system.client.invenco-emulation-aux-status' AND d.device_id = t.target_id
                    ) auxs on true
                    LEFT JOIN LATERAL (
                        SELECT d.value
                        FROM device_states d
                        WHERE state = 'invenco.system.cfg.net-terminal-rank' AND d.device_id = t.target_id
                    ) ar on true
            `;

      if (alarms && alarms.length) {
        FIND_ALL_DEVICE_QUERY = `${FIND_ALL_DEVICE_QUERY} ${ALARMS_QUERY}`;
      }

      const findAllDevicesConfig = appendParams(
        FIND_ALL_DEVICE_QUERY,
        [],
        searchFilter,
        presence,
        statuses,
        companyFilter,
        req.user.sub,
        companyId,
        deviceType,
        alarms,
        showHiddenDevices,
        oosFilter
      );

      let findAllDevicesQuery = findAllDevicesConfig.queryStr;
      const findAllDevicesParams = findAllDevicesConfig.paramList;

      const orderDefault = 'status desc, t.name';
      const orderMap = {
        'serial-asc': 't.serial_number',
        'serial-desc': 't.serial_number DESC',
        'name-desc': 's.name DESC, t.name DESC',
        'status-asc': 'status, t.name',
      };

      findAllDevicesQuery = `
                ${findAllDevicesQuery}
                GROUP BY
                    t.target_id, device_health, s.name, cpt.display_name, ars.suspended_by_device_until,
                    ars.suspended_from, ars.suspended_until, kg.ksn_prefix, ar.value, auxs.value,
                    s.formatted_address
                ORDER BY
                    ${orderMap[order] || orderDefault}`;

      if (!isCSV) {
        if (count > 0) {
          if (pageSize > 0) {
            const offset = pageIndex * pageSize;
            findAllDevicesQuery = `
                        ${findAllDevicesQuery}
                            LIMIT $${findAllDevicesParams.length + 1}
                            OFFSET $${findAllDevicesParams.length + 2}`;
            findAllDevicesParams.push(pageSize, offset);
          }

          const devices = yield server.db.read.rows(
            findAllDevicesQuery,
            findAllDevicesParams
          );

          yield loadDevicesFeatureFlagMap();

          /* eslint-disable no-param-reassign */
          results = yield devices.map(async device => {
            if (configs && configs.length > 0) {
              device.config = await deviceHelper.getDeviceConfigs(
                device,
                configs
              );
            }

            device.deviceType.screenSize =
              enumToScreenSize[device.deviceType.screenSize] || null;
            device.statusStr = device.status;
            device.status = deviceHelper.translateDeviceHealthToNumber(
              device.status
            );
            device.featureFlags = deviceFeatureCache.get(device.id);
            if (device.oosConditions && device.oosConditions.length) {
              const oosCategories = new Map();
              device.oosConditions = device.oosConditions
                .map(item => {
                  const match = deviceHelper.getOOSCategoryAndCondition({
                    name: item.state,
                    value: item.value,
                  });
                  if (match) {
                    const { category, condition } = match;
                    // eslint-disable-next-line no-unused-expressions
                    !oosCategories.has(category)
                      ? oosCategories.set(category, [condition])
                      : oosCategories.set(category, [
                          ...oosCategories.get(category),
                          condition,
                        ]);

                    return { category, condition };
                  }
                  return undefined;
                })
                .filter(Boolean);
              device.oosCategories = Array.from(oosCategories);
            }
            // device.releaseVersion = releaseVersionHelper.getReleaseVersion( device );
            return device;
          });
          /* eslint-enable no-param-reassign */
        }

        if (showOnlyUnrestrictedDevices) {
          const regex = new RegExp(RESTRICTED_DEVICE_TYPES.join('|'), 'i');
          results = results.filter(device => !regex.test(device.deviceType.id));
        }

        const data = {
          resultsMetadata: {
            totalResults: count,
            pageIndex,
            pageSize,
          },
          results,
        };

        res.send(data);
      } else {
        // export to CSV
        const csvOptions = {
          delimiter: ',',
          endLine: '\n',
          columns: [
            'id',
            'name',
            'serial_number',
            'status',
            'location',
            'status_alarm_ts',
            'device_type',
            'release_version',
            'oos_category_condition',
          ],
          escapeChar: '"',
          enclosedChar: '"',
          header: true,
        };

        const connection = yield server.db.read.getConnection();
        const query = new QueryStream(
          findAllDevicesQuery,
          findAllDevicesParams
        );

        res.writeHead(200, {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename=devices-list.csv',
        });

        const stream = connection.client.query(query);
        stream.on('end', connection.done);
        const stringifier = stringify(csvOptions);
        stream
          .pipe(transform(data => transformCSVResult(data)))
          .pipe(stringifier)
          .pipe(res);
      }

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  async getDeviceById(req, res, next) {
    try {
      const userId = req.user.sub;
      const deviceId = req.params.id;
      const companyId = req.user.company.id;
      const device = await queryDeviceById(userId, deviceId, companyId);

      const flagInstance = await server.appFeatureFlag.getInstance();

      if (!device) {
        return next(
          new restify.errors.NotFoundError(
            `Device (id: ${deviceId}) is not found`
          )
        );
      }

      // MED-342 User can see prompt, remove prompt if company doesn't have MEDIA flag
      if (!req.user.company.featureFlags.includes('MEDIA')) {
        delete device.promptSet;
      }

      const { configSchema, configForm, bluefinDefaultConfig } =
        await deviceHelper.getDeviceConfigSchemaAndForm();

      device.configSchema = configSchema;
      device.configForm = configForm;
      device.deviceType.screenSize =
        enumToScreenSize[device.deviceType.screenSize] || null;
      const canChangeConfig = await deviceHelper.canEditConfig(
        device.id,
        req.user.roles,
        req.user.company.featureFlags
      );

      Object.keys(device.configSchema).forEach(k => {
        if (canChangeConfig(k)) {
          device.configSchema[k].readonly =
            device.configSchema[k].readonly && true;
        } else {
          device.configSchema[k].readonly = true;
        }
      });

      device.configData = {
        ...(await deviceHelper.getLatestConfigData(device.id, configSchema)),
        ...(await deviceHelper.fetchConfigsFromTarget(device.id, configSchema)),
      };

      // Auxiliary Device Support
      if (flagInstance.verifyFeatureFlag('ADA').isEnabled) {
        const terminalRank = device.configData['cfg.net-terminal-rank']
          ? device.configData['cfg.net-terminal-rank'].value
          : null;
        const auxIp = device.configData['cfg.net-aux-ip']
          ? device.configData['cfg.net-aux-ip'].value
          : null;

        if (/(main)/i.test(terminalRank) && auxIp) {
          device.auxDevice = await queryMainAuxiliaryDevice(
            device.id,
            device.config.terminalId,
            'aux',
            userId,
            companyId,
            device.siteId,
            auxIp,
            device.configData
          );
        } else if (/(aux)/i.test(terminalRank)) {
          const mainDevice = await queryMainAuxiliaryDevice(
            device.id,
            device.config.terminalId,
            'main',
            userId,
            companyId,
            device.siteId,
            null,
            device.configData
          );
          if (
            mainDevice &&
            device.ipAddress === mainDevice.configData['cfg.net-aux-ip'].value
          ) {
            device.mainDevice = mainDevice;
          }
        }
      }

      if ('dualDisplayEnabled' in device.config)
        convertToBooleanValue(device, 'cfg.mgt-dual-display-enabled');
      if (
        'subPlatform' in device.config &&
        device.config.subPlatform !== 'opt'
      ) {
        delete device.config.accessibilityMode;
      }

      if ('accessibilityMode' in device.config) {
        configureAccessibilityMode(device);
      }

      if ('adaPuck' in device.config) {
        configureAdaPuck(device);
        if (!device.configData['g7opt.sys-aux-keypad']) {
          delete device.config.adaPuck;
        }
      }

      if (device.bluefinFeatConfig && device.fromDefaultSite) {
        const deviceBuild = parseDeviceBuild(device.bluefinConfigData);
        device.configData['bfc.device-build'] = {
          value: deviceBuild,
          all: bluefinDefaultConfig.deviceBuild
            ? bluefinDefaultConfig.deviceBuild
            : [],
        };
      } else {
        delete device.configSchema['bfc.device-build'];
      }

      delete device.bluefinFeatConfig;
      delete device.currentCompany;
      delete device.bluefinConfigData;

      // add inFlight property to the device object
      res.send(await sitesHelper.updateDeviceInflight(device));

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getDeviceBySerialNumber(req, res, next) {
    const userId = req.user.sub;
    const { serialNumber } = req.params;
    const { serviceRecipientId } = req.query;
    const companyId = req.user.company.id;

    return co(function* run() {
      if (serviceRecipientId && serviceRecipientId !== companyId) {
        const canManage = yield companyHelper.canManageServiceRecipient({
          serviceManagerId: companyId,
          serviceRecipientId,
        });
        if (!canManage) {
          return next(
            new restify.errors.ForbiddenError('Permission denied to access')
          );
        }
      }
      const device = yield queryDeviceBySerialNumber(
        userId,
        serialNumber,
        serviceRecipientId || companyId
      );
      if (!device) {
        return next(
          new restify.errors.NotFoundError(
            `Device (serialNumber: ${serialNumber}) is not found`
          )
        );
      }

      // MED-342 User can see prompt, remove prompt if company doesn't have MEDIA flag
      if (!req.user.company.featureFlags.includes('MEDIA')) {
        delete device.prompt_set;
      }

      res.send(device);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  createDevice: async (req, res, next) => {
    const userId = req.user.sub;
    const companyId = req.user.company.id;
    const device = req.body;
    const isBridgeApp = false;
    return co(function* run() {
      const windowEnd = moment();
      if (isBridgeApp) {
        // TODO: create device by bridge app
        return next(
          new restify.errors.NotImplementedError(
            'Create device by bridge app is not implemented'
          )
        );
      }
      req.log.info('create device - %s', JSON.stringify(device));

      windowEnd.add(20, 'years');
      req.log.debug(`window registration will end at ${windowEnd.format()}`);

      const site = yield server.db.read.row(
        `
                SELECT
                    s.site_id,
                    s.company_id
                FROM site s
                JOIN user_site_authorization usa ON s.site_id = usa.site_id
                WHERE
                    s.site_id = $1 AND
                    s.active AND
                    usa.user_id = $2;
            `,
        [device.siteId, userId]
      );
      if (!site) {
        return next(
          new restify.errors.UnauthorizedError(
            `No permission to access Site (${device.siteId})`
          )
        );
      }

      const serialNumber = device.serialNumber.toUpperCase();
      const count = yield server.db.read.row(
        `
                SELECT
                    COUNT(*)
                FROM target t
                WHERE
                t.active AND
                t.serial_number = $1
            `,
        [serialNumber]
      );
      if (count.count > 0) {
        return next(
          new restify.errors.ConflictError(
            `Another device found with serial number (${serialNumber})`
          )
        );
      }

      const DEVICE_TYPE_QUERY = `
                SELECT
                    cpt.display_name
                FROM company_product_type cpt
                WHERE
                    cpt.device_type = $1 AND
                    cpt.company = $2;
            `;
      let deviceType = yield server.db.read.row(DEVICE_TYPE_QUERY, [
        device.deviceType,
        companyId,
      ]);
      if (!deviceType) {
        return next(
          new restify.errors.BadRequestError(
            `Device Type (id: ${device.deviceType}) not found`
          )
        );
      }

      deviceType = yield server.db.read.row(DEVICE_TYPE_QUERY, [
        device.deviceType,
        site.companyId,
      ]);
      if (!deviceType) {
        return next(
          new restify.errors.BadRequestError(
            `Destination company does not have access to device type (id: ${device.deviceType})`
          )
        );
      }

      let { keyGroupRef } = device;
      let deviceKeyGroup;
      if (!device.keyGroupRef && device.keyGroupId) {
        deviceKeyGroup = yield getKeyGroupById(
          device.keyGroupId,
          site.companyId
        );
        if (deviceKeyGroup) {
          keyGroupRef = deviceKeyGroup.keyGroupRef;
        }
      }

      const INSERT_DEVICE = `
                INSERT INTO target (site_id, password, version, last_registered, registration_key,
                    name, description, serial_number, active, registration_window_end,
                    certificate, is_json_certificates, mac_address, data, status, presence, key_group_ref, device_type, deployment_type, updated_by,meta)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19,$20,$21)
                RETURNING target_id;`;

      const FIND_NEW_INSERTED_DEVICE = `
                SELECT
                    t.target_id as id, t.name, t.description, t.site_id, get_device_health( t.target_id ) as status,
                    t.presence, t.key_group_ref, t.serial_number, t.last_registered, t.last_contact, s.name as site_name,
                    json_build_object(
                        'id', cpt.device_type,
                        'name', cpt.display_name
                    ) as device_type
                FROM target t
                LEFT JOIN site s ON t.site_id = s.site_id
                LEFT JOIN company_product_type cpt ON
                    cpt.company = s.company_id AND
                    cpt.device_type = t.device_type
                WHERE
                    t.target_id = $1;
            `;

      const conn = yield server.db.write.getConnection();
      let insertResult;
      try {
        yield conn.execute('BEGIN');

        insertResult = yield conn.execute(INSERT_DEVICE, [
          device.siteId,
          device.password,
          constants.devices.VERSION,
          device.lastRegistered,
          serialNumber,
          device.name,
          device.description,
          serialNumber,
          true,
          windowEnd,
          device.certificate,
          device.isJsonCertificates,
          device.macAddress,
          device.data,
          constants.devices.STATUS.INACTIVE,
          constants.devices.PRESENCE.PRESENT,
          keyGroupRef,
          device.deviceType,
          device.deploymentType ? device.deploymentType : 'maintenance-window',
          userId,
          device.meta ? device.meta : null,
        ]);

        // send 'MOVE_DEVICE' action message to refresh destination site
        deviceHelper
          .sendMoveDeviceMessage([
            {
              companyId: site.companyId,
              deviceId: insertResult.rows[0].targetId.toString(),
              toSiteId: device.siteId,
            },
          ])
          .catch(err => {
            req.log.error(
              'deviceHelper.sendMoveDeviceMessage() Error: %s',
              err
            );
          });

        // INSERT THIS CREATED TARGET ID INTO DEVICE_PROFILE TABLE
        yield conn.execute(
          `INSERT INTO device_profile (target_id) VALUES ($1)`,
          [insertResult.rows[0].targetId]
        );
        yield conn.execute('COMMIT');
      } catch (err) {
        yield conn.execute('ROLLBACK');
        return next(new restify.errors.InternalServerError(err));
      } finally {
        conn.done();
      }

      if (!insertResult.rows.length) {
        return next(
          new restify.errors.InternalServerError('Device creation failed')
        );
      }

      const result = yield server.db.read.row(FIND_NEW_INSERTED_DEVICE, [
        insertResult.rows[0].targetId,
      ]);
      result.status = deviceHelper.translateDeviceHealthToNumber(result.status);

      const siteKeyGroup = yield getKeyGroupBySiteId(device.siteId);

      if (siteKeyGroup && siteKeyGroup.keyGroupId) {
        // if device has been created in a site with key group assigned to it, trigger the creation of an automatic RKI retrieve request
        req.log.info(
          'auto rki enabled and the site has a key group %s!',
          siteKeyGroup.keyGroupRef
        );
        yield rkiHelper.createAutomaticRkiRequestSession(
          userId,
          [result.id],
          device.siteId,
          rkiHelper.RKI_REASON.CREATE
        );
      }

      req.log.info(
        'Device id [%s] with serial number %s is created by %s',
        result.id,
        result.serialNumber,
        userId
      );

      res.send(result);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  deactivateDevice(req, res, next) {
    const userId = req.user.sub;
    const deviceId = req.params.id;
    const usersCompany = req.user.company.id;

    return co(function* run() {
      const device = yield server.db.read.row(
        `
                  SELECT
                      t.target_id, t.site_id, t.serial_number, t.key_group_ref, s.company_id
                  FROM company_feature_flag cff, target t
                        JOIN site s ON t.site_id = s.site_id
                        JOIN user_site_authorization u ON s.site_id = u.site_id
                        LEFT JOIN company c ON s.company_id = c.id
                        LEFT JOIN company_product_type cpt ON s.company_id = cpt.company AND t.device_type = cpt.device_type
                  WHERE
                      t.active IS TRUE AND
                      cff.feature_flag = 'PROVISION_DEVICES' AND
                      t.target_id = $1 AND
                      u.user_id = $2 AND
                      cff.company = $3
              ;`,
        [deviceId, userId, usersCompany]
      );

      res.resourceOwner = device ? device.companyId : null; // eslint-disable-line no-param-reassign

      if (!device) {
        return next(
          new restify.errors.NotFoundError(
            `Device (id: ${deviceId}) is not found`
          )
        );
      }

      const conn = yield server.db.write.getConnection();
      try {
        yield conn.execute('BEGIN');
        // Switch active to false
        yield conn.execute(
          `
                    UPDATE target
                    SET active = false, delete_timestamp = $1, last_edited_date = $1, updated_by = $2
                    WHERE target_id = $3`,
          [new Date(), userId, deviceId]
        );

        // once the device is deactivated it does not make sense to keep ui notifications for it
        const { notificationCount } = yield conn.execute(
          `
                    WITH effected AS (
                        DELETE FROM notification
                        WHERE related_entity ->> 'type' = 'device'
                          AND related_entity ->> 'id' = $1
                        RETURNING 1
                    )
                    SELECT COUNT(*) AS notification_count FROM effected;`,
          [deviceId]
        );
        req.log.info(
          `${notificationCount} outstanding uiNotifications for device id ${deviceId} deleted`
        );

        // cancel all outstanding jobs for the device being deleted
        const cancelJobResults = yield conn.execute(
          `
            UPDATE job
            SET status = (SELECT id FROM job_status WHERE name = 'Cancelled')
            WHERE device_id = $1 AND (status = 0 OR status = 1 OR status = 2)
            RETURNING id, status, type, destination;
          `,
          [deviceId]
        );

        const cancelledJobs = cancelJobResults ? cancelJobResults.rows : [];
        req.log.info(
          `${cancelledJobs.length} outstanding jobs for device id ${deviceId} cancelled`
        );

        // TMS-4019 delete all outstanding deployment for the device being deleted
        const { targetReleaseCount } = yield conn.execute(
          `
                    WITH effected AS (
                        DELETE FROM target_release
                        WHERE target_id = $1
                        RETURNING 1
                    )
                    SELECT COUNT(*) AS target_release_count FROM effected;`,
          [deviceId]
        );
        req.log.info(
          `${targetReleaseCount} device are released for device id ${deviceId}`
        );

        yield deviceHelper.markDeviceStatesForDeletion([deviceId]);

        yield conn.execute('COMMIT');
        if (jobHelper.okToSendFpsJobStatus() && !_.isEmpty(cancelledJobs)) {
          const fpsJobs = cancelledJobs
            .filter(job => jobHelper.isFpsJobs(job))
            .map(job => ({
              jobDestination: job.destination,
              jobType: job.type,
              jobId: job.id,
              jobStatus: job.status,
            }));
          yield jobHelper.sendFpsJobStatusEventsToEventBridge(fpsJobs);
        }
        yield sendEventProcessDevicesChange([deviceId]);
      } catch (err) {
        yield conn.execute('ROLLBACK');
        return next(new restify.errors.InternalServerError(err));
      } finally {
        conn.done();
      }

      server.log.info(
        'de-activating device id %s completed by user (id: %s)',
        deviceId,
        userId
      );
      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  updateDevice(req, res, next) {
    const userId = req.user.sub;
    const companyId = req.user.company.id;
    const deviceId = req.params.id;
    const updatePayload = req.body;

    return co(function* run() {
      const device = yield queryDeviceById(userId, deviceId, companyId);
      let site;

      if (!device) {
        return next(
          new restify.errors.NotFoundError(`Device (id: ${deviceId}) not found`)
        );
      }

      const isChangingTerminalLocation =
        ['Forecourt', 'Backcourt'].includes(updatePayload.terminalLocation) &&
        updatePayload.terminalLocation !== device.config.terminalLocation;

      const isChangingDeviceType =
        updatePayload.deviceType &&
        updatePayload.deviceType !== device.deviceType.id;
      if (isChangingDeviceType) {
        // Check user has access to this device type
        const deviceType = yield server.db.read.row(
          `
                    SELECT
                        cpt.display_name
                    FROM company_product_type cpt
                    WHERE
                        cpt.device_type = $1 AND
                        cpt.company = $2;
                `,
          [updatePayload.deviceType, companyId]
        );
        if (!deviceType) {
          return next(
            new restify.errors.BadRequestError(
              `Device Type (id: ${updatePayload.deviceType}) not found`
            )
          );
        }
      }

      const isMovingSite =
        updatePayload.siteId && device.siteId !== updatePayload.siteId;
      if (isMovingSite) {
        // Check user is allowed to access this site
        site = yield server.db.read.row(
          `
                    SELECT
                        s.site_id,
                        s.name,
                        s.company_id,
                        s.address_json,
                        s.timezone_id,
                        s.date_updated
                        ${BLUEFIN_SITE_SELECT}
                    FROM site s
                    JOIN user_site_authorization usa ON s.site_id = usa.site_id
                    ${BLUEFIN_SITE_JOIN}
                    WHERE
                        s.site_id = $1 AND
                        s.active AND
                        usa.user_id = $2;
                `,
          [updatePayload.siteId, userId]
        );

        if (!site || !site.siteId) {
          return next(
            new restify.errors.BadRequestError(
              `Site (id: ${updatePayload.siteId}) not found`
            )
          );
        }

        if (
          site.bluefinFlag &&
          !device.fromDefaultSite &&
          site.siteId !== site.toDefaultSite
        ) {
          return next(
            new restify.BadRequestError(
              `Device can only be moved to the default site`
            )
          );
        }

        const isChangingCompany = site.companyId !== companyId;
        if (isChangingCompany) {
          // Check destination company has access to the deviceType
          const deviceType = yield server.db.read.row(
            `
                        SELECT
                            cpt.display_name
                        FROM company_product_type cpt
                        WHERE
                            cpt.device_type = $1 AND
                            cpt.company = $2;
                    `,
            [updatePayload.deviceType, site.companyId]
          );
          if (!deviceType) {
            return next(
              new restify.errors.BadRequestError(
                `Destination company does not have access to device type (id: ${updatePayload.deviceType})`
              )
            );
          }
        }
      }

      if (!_.isEmpty(updatePayload.configUpdate)) {
        const canChangeConfig = yield deviceHelper.canEditConfig(
          device.id,
          req.user.roles,
          req.user.company.featureFlags
        );
        // check if some configs is not editable to the user or device does not support config edits
        const someConfigsNotEditable = Object.keys(
          updatePayload.configUpdate
        ).some(k => !canChangeConfig(k));
        if (someConfigsNotEditable) {
          return next(
            new restify.errors.BadRequestError(
              `User is not allowed to change config or device does not support config changes (id: ${device.id}) `
            )
          );
        }
      }

      const conn = yield server.db.write.getConnection();
      try {
        yield conn.execute('BEGIN');
        req.log.info('updating device %s', device.id);

        const columns = [];
        const params = [];
        let targetName = updatePayload.name;
        let targetDescription = updatePayload.description;

        if (
          updatePayload.configUpdate &&
          !_.isEmpty(updatePayload.configUpdate['db.target.name'])
        ) {
          targetName = updatePayload.configUpdate['db.target.name'];
        }

        if (targetName && targetName !== device.name) {
          columns.push('name');
          params.push(targetName);
        }

        if (
          updatePayload.configUpdate &&
          !_.isEmpty(updatePayload.configUpdate['db.target.description'])
        ) {
          targetDescription =
            updatePayload.configUpdate['db.target.description'];
        }

        if (
          (targetDescription !== null || targetDescription !== undefined) &&
          targetDescription !== device.description
        ) {
          columns.push('description');
          params.push(targetDescription);
        }

        if (isChangingDeviceType) {
          columns.push('device_type');
          params.push(updatePayload.deviceType);
        }

        if (updatePayload.deploymentType) {
          columns.push('deployment_type');
          params.push(updatePayload.deploymentType);
        }

        // TODO check if terminal location is changing require target table to be updated
        columns.push('last_edited_date');
        params.push('now()');

        if (userId) {
          params.push(userId);
          columns.push('updated_by');
        }
        if (updatePayload.meta) {
          params.push(updatePayload.meta);
          columns.push('meta');
        }

        params.push(device.id);

        if (columns.length) {
          yield conn.execute(
            `
                        UPDATE target SET
                        ${columns.map(
                          (column, index) => `${column} = $${index + 1}`
                        )}
                        WHERE
                            target_id = $${columns.length + 1};
                    `,
            params
          );
        }

        if (
          updatePayload.configUpdate &&
          'cfg.mgt-dual-display-enabled' in updatePayload.configUpdate
        ) {
          updatePayload.configUpdate['cfg.mgt-dual-display-enabled'] =
            updatePayload.configUpdate['cfg.mgt-dual-display-enabled'] ? 1 : 0;
        }

        if (
          updatePayload.configUpdate &&
          'cfg.lcd-accessibility-mode' in updatePayload.configUpdate
        ) {
          updatePayload.configUpdate['cfg.lcd-accessibility-mode'] =
            updatePayload.configUpdate['cfg.lcd-accessibility-mode'] ? 1 : 0;
        }

        let bluefinConfigPayload = null;

        if (!_.isEmpty(updatePayload.configUpdate)) {
          // remove config updates that starts with `db.target` as this update goes to target table
          Object.keys(updatePayload.configUpdate).forEach(key => {
            if (key.startsWith('db.target')) {
              delete updatePayload.configUpdate[key];
            }
            if (key.startsWith('bfc.')) {
              bluefinConfigPayload = { [key]: updatePayload.configUpdate[key] };
              delete updatePayload.configUpdate[key];
            }
          });

          yield deviceHelper.createSysconfigJob(
            conn,
            userId,
            device.id,
            updatePayload.configUpdate
          );
        }

        if (bluefinConfigPayload && enableBluefin) {
          const deviceBuildPayload = [
            {
              key: 'deviceBuild',
              value: bluefinConfigPayload['bfc.device-build'],
              id: device.id,
            },
          ];
          yield updateBluefinConfig(deviceBuildPayload);
        }

        yield conn.execute('COMMIT');

        if (isMovingSite) {
          const action = 'moveToSite';
          const moveResult = yield deviceHelper.moveDevices(
            [
              {
                id: device.id,
                serialNumber: device.serialNumber,
                keyGroupRef: device.keyGroupRef,
                companyId: site.companyId,
                fromSiteId: device.siteId,
                toSiteId: updatePayload.siteId,
                deploymentType: updatePayload.deploymentType,
                meta: updatePayload.meta,
                timezoneId: site.timezoneId,
                currentDeviceCompany: device.currentCompany,
                fromDefaultSite: device.fromDefaultSite,
                bluefinFlag: site.bluefinFlag,
                bluefinConfigData: device.bluefinConfigData,
                siteName: site.name,
                locationId: site.locationId,
                toDefaultSite: site.toDefaultSite,
                kif: site.kif,
              },
            ],
            userId,
            action
          );
          if (moveResult.failed.length > 0) {
            throw new Error('Device move failed');
          }
        }

        if (isChangingTerminalLocation) {
          const canChangeConfig = yield deviceHelper.canEditConfig(
            device.id,
            req.user.roles,
            req.user.company.featureFlags
          );
          const canChangeTerminalLocation = canChangeConfig(
            'invenco.system.cfg.net-terminal-location'
          );
          if (!canChangeTerminalLocation) {
            return next(
              new restify.errors.BadRequestError(
                `User is not allowed to change terminal location (id: ${device.id})`
              )
            );
          }
          if (canChangeTerminalLocation) {
            const timestamp = moment().format('YYYY-MM-DD HH:mm:ss.SSS Z');
            const result = yield deviceHelper.updateDeviceTerminalLocation(
              device.config,
              device.id,
              device.siteId,
              'invenco.system.cfg.net-terminal-location',
              updatePayload.terminalLocation,
              timestamp
            );
            if (result.failed.length > 0) {
              throw new Error(
                `Device terminal location update failed on device id: ${device.id}`
              );
            }
          }
        }
      } catch (err) {
        yield conn.execute(constants.dbTransaction.ROLLBACK);
        return next(new restify.errors.InternalServerError(err));
      } finally {
        conn.done();
      }

      const newDevice = yield queryDeviceById(userId, deviceId, companyId);
      res.send(newDevice);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  backupConfig(req, res, next) {
    return co(function* execute() {
      // select all devices where active and not offline
      const deviceFilesToBackup = yield server.db.read.rows(
        `SELECT
                    df.id,
                    df.device_id,
                    df.device_path,
                    df.application_id,
                    df.writeable,
                    tgt.presence,
                    df.last_pulled_path,
                    df.last_pulled_timestamp
                FROM device_files df
                    INNER JOIN target tgt on tgt.target_id = df.device_id
                    INNER JOIN site s on tgt.site_id = s.site_id
                WHERE
                    tgt.last_contact >= current_timestamp - interval '2 hours' AND
                    df.writeable AND
                    tgt.active = true AND
                    tgt.presence = 'PRESENT' AND
                    coalesce(df.last_pulled_hash, '') != df.current_hash AND
                    s.is_production AND
                    s.timezone_id is not null AND
                    extract( hour from timezone( s.timezone_id, current_timestamp ) ) = 3 AND
                    exists ( select 1 from company_feature_flag cff where cff.company = s.company_id and cff.feature_flag = 'DEVICES_SWAP_OUT') AND
                    coalesce(df.last_pulled_timestamp, '-infinity'::timestamptz) <= df.timestamp
                    `
      );

      // create a file upload request job (call it backup)
      const fileUploadRequest = yield server.db.write.execute(
        `
                INSERT INTO file_upload_request( id, name, created, completed, created_by )
                VALUES
                    ( uuid_generate_v4(), 'AUTO_BACKUP', NOW(),  NOW(), $1 )
                RETURNING id
            `,
        [req.user.sub]
      );

      _.chunk(deviceFilesToBackup, 10).map(batchFilesToSend => {
        // get sqs things
        server.log.info(
          `Sending ${batchFilesToSend.length} backup job messages`
        );
        return AWS.sendSqsMessageBatch({
          QueueUrl: env.config.jobs.queue.url,
          Entries: batchFilesToSend.map(batchFile => {
            const urlEncodedQuery = encodeURI(
              `app=${batchFile.applicationId}&filepath=${batchFile.devicePath}`
            );
            return {
              Id: `${batchFile.id}`,
              MessageAttributes: {
                'device-id': {
                  DataType: 'String',
                  StringValue: `${batchFile.deviceId}`,
                },
                destination: {
                  DataType: 'String',
                  StringValue: batchFile.applicationId,
                },
                'user-id': {
                  DataType: 'String',
                  StringValue: req.user.sub,
                },
                expiry: {
                  DataType: 'String',
                  StringValue: `${+moment().add(2, 'hours')}`,
                },
                type: {
                  DataType: 'String',
                  StringValue: 'sys.upload',
                },
              },
              MessageBody: JSON.stringify({
                sourcePath: batchFile.devicePath,
                uploadDestination: `${env.config.url.api}/fileuploadrequests/${fileUploadRequest[0].id}/upload?${urlEncodedQuery}`,
                version: 1,
              }),
            };
          }),
        });
      });
      res.send(200);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  swap(req, res, next) {
    return co(function* execute() {
      const oldDeviceId = req.params.id;
      // eslint-disable-next-line eqeqeq
      if (oldDeviceId == req.body.newDevice.id) {
        return next(
          new restify.errors.BadRequestError('Cannot swap device to itself')
        );
      }

      function* loadDevice(deviceId) {
        return yield server.db.read.row(
          `
                    SELECT t.target_id as id, t.name, t.site_id, t.device_type, c.id as "companyId"
                    FROM target t
                        JOIN site s ON s.site_id = t.site_id
                        JOIN user_site_authorization u on u.site_id = s.site_id
                        JOIN company c on c.id = s.company_id
                    WHERE
                        t.target_id = $1 AND
                        t.active = true AND
                        u.user_id = $2`,
          [deviceId, req.user.sub]
        );
      }

      const oldDevice = yield loadDevice(oldDeviceId);
      if (!oldDevice) {
        return next(
          new restify.errors.NotFoundError(`Device ${oldDeviceId} not found`)
        );
      }
      const newDevice = yield loadDevice(req.body.newDevice.id);
      if (!newDevice) {
        return next(
          new restify.errors.NotFoundError(
            `Device ${req.body.newDevice.id} not found`
          )
        );
      }

      if (oldDevice.companyId !== newDevice.companyId) {
        return next(
          new restify.errors.BadRequestError(
            'Cannot swap device that belongs to another company'
          )
        );
      }

      if (oldDevice.deviceType !== newDevice.deviceType) {
        return next(
          new restify.errors.BadRequestError(
            'Cannot swap devices of different types'
          )
        );
      }

      // select all files from this device
      const deviceFiles = yield server.db.read.rows(
        `
                SELECT id, device_path, last_pulled_path, last_pulled_hash, application_id
                FROM device_files
                WHERE
                    writeable = true AND
                    last_pulled_path IS NOT NULL AND
                    device_id = $1
            `,
        [oldDevice.id]
      );

      const conn = yield server.db.write.getConnection();
      try {
        yield conn.execute('BEGIN');

        // swap both devices
        const setSiteAndName = (deviceId, newSiteId, newName) =>
          conn.execute(
            `UPDATE target t
                            SET site_id = $1, name = $2
                        WHERE
                            target_id = $3`,
            [newSiteId, newName, deviceId]
          );

        yield setSiteAndName(
          newDevice.id,
          oldDevice.siteId,
          req.body.newDevice.name
        );
        yield setSiteAndName(
          oldDevice.id,
          newDevice.siteId,
          req.body.oldDeviceName
        );

        // create file records for each device file
        const files = (yield Promise.all(
          deviceFiles.map(deviceFile =>
            conn.execute(
              `
                        INSERT INTO file(
                            id, customer_id, application_id, description, device_id, file_url, file_name, file_signature, created, created_by )
                        VALUES
                            ( uuid_generate_v1mc(), $1, $2, $3, $4, $5, $6, $7, NOW(), $8 )
                        RETURNING
                            id, application_id, file_url, file_name, file_signature;
                    `,
              [
                req.user.company.id,
                'invenco.system',
                'Device Config Swap',
                newDevice.id,
                deviceFile.lastPulledPath,
                deviceFile.devicePath,
                deviceFile.lastPulledHash,
                req.user.email,
              ]
            )
          )
        )).map(queryResult => queryResult.rows[0]);

        // create job message
        server.log.info(
          `Swapping ${deviceFiles.length} config files from Device: ${oldDevice.id} to Device: ${newDevice.id}`
        );
        yield Promise.all(
          files.map(file =>
            AWS.sendSqsMessage({
              QueueUrl: env.config.jobs.queue.url,
              MessageAttributes: {
                'device-id': {
                  DataType: 'String',
                  StringValue: newDevice.id.toString(),
                },
                'user-id': { DataType: 'String', StringValue: req.user.sub },
                destination: {
                  DataType: 'String',
                  StringValue: 'invenco.system',
                },
                type: { DataType: 'String', StringValue: 'sys.install.file' },
              },
              MessageBody: JSON.stringify({
                fileURI: `${env.config.url.api}/files/${file.id}/content`,
                fileHash: file.fileSignature,
                fileName: file.fileName,
                fileHashAlg: 'sha256',
                version: 1,
              }),
            })
          )
        );

        yield conn.execute('COMMIT');

        yield sendEventProcessDevicesChange([oldDevice.id, newDevice.id]);
        res.send(200, { filesCount: deviceFiles.length });
      } catch (err) {
        yield conn.execute('ROLLBACK');
        return next(new restify.errors.InternalServerError(err));
      } finally {
        conn.done();
      }

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  getRolloutsById(req, res, next) {
    const { rolloutId, id: deviceId } = req.params;
    return co(function* execute() {
      const device = yield queryDeviceById(
        req.user.sub,
        deviceId,
        req.user.company.id
      );
      if (!device) {
        return next(
          new restify.errors.NotFoundError(`Device ${deviceId} not found`)
        );
      }

      const rolloutJobHistory = yield server.db.read.rows(
        `
                SELECT
                    tr.target_id as device_id,
                    r.release_id,
                    s.name as software_name,
                    dj.data as job_data,
                    djsh.job_id as job_id,
                    djsh.id as id,
                    djsh.status as status,
                    djsh.message as message,
                    djsh.created_at as created,
                    'download' as job_type
                FROM target_release tr
                    JOIN release r ON r.release_id = tr.release_id
                    JOIN software s ON s.software_id = tr.software_id
                    JOIN job dj ON dj.id = tr.download_job
                    JOIN job_status_history djsh ON djsh.job_id = dj.id
                WHERE
                    tr.target_id = $1 AND
                    tr.release_id = $2
                UNION
                SELECT
                    tr.target_id as device_id,
                    r.release_id,
                    s.name as software_name,
                    ij.data as job_data,
                    ijsh.job_id as job_id,
                    ijsh.id as id,
                    ijsh.status as status,
                    ijsh.message as message,
                    ijsh.created_at as created,
                    'install' as job_type
                FROM target_release tr
                    JOIN release r ON r.release_id = tr.release_id
                    JOIN software s ON s.software_id = tr.software_id
                    JOIN job ij ON ij.id = tr.install_job
                    JOIN job_status_history ijsh ON ijsh.job_id = ij.id
                WHERE
                    tr.target_id = $1 AND
                    tr.release_id = $2;
            `,
        [deviceId, rolloutId]
      );

      if (!(rolloutJobHistory && rolloutJobHistory.length)) {
        return next(
          new restify.errors.NotFoundError(`Rollout ${rolloutId} not found`)
        );
      }
      const installJobs = _.chain(rolloutJobHistory)
        .filter({ jobType: 'install' })
        .sortBy(['created'])
        .value();
      const downloadJobs = _.chain(rolloutJobHistory)
        .filter({ jobType: 'download' })
        .sortBy(['created'])
        .value();

      const rollout = {
        softwareName: null,
        downloadJob: {},
        installJob: {},
      };

      if (installJobs && installJobs.length) {
        const { jobData, jobId, status, softwareName } =
          installJobs[installJobs.length - 1];
        const jobs = {
          data: jobData,
          id: jobId,
          status,
          history: installJobs,
        };
        rollout.installJob = jobs;
        rollout.softwareName = softwareName;
      }

      if (downloadJobs && downloadJobs.length) {
        const { jobData, jobId, status, softwareName } =
          downloadJobs[downloadJobs.length - 1];
        const jobs = {
          data: jobData,
          id: jobId,
          status,
          history: downloadJobs,
        };
        rollout.downloadJob = jobs;
        rollout.softwareName = softwareName;
      }

      res.send(rollout);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  rebootDevice: (req, res, next) => {
    const deviceId = req.params.id;
    const userId = req.user.sub;
    const companyId = req.user.company.id;

    return co(function* execute() {
      const device = yield queryDeviceById(userId, deviceId, companyId);
      if (!device) {
        // should allow only active devices and devices that belongs to the user's company
        return next(
          new restify.errors.NotFoundError(
            `Device (id: ${deviceId}) is not found`
          )
        );
      }
      // should allow to reboot ONLINE devices only
      if (device.status !== constants.devices.STATUS.OPERATIONAL) {
        return next(
          new restify.errors.ConflictError(
            `Device (id: ${deviceId}) is not online`
          )
        );
      }

      server.log.info(
        `Sending job message to reboot device with id: ${deviceId}`
      );

      const params = {
        MessageBody: '{}',
        QueueUrl: env.config.jobs.queue.url,
        DelaySeconds: 0,
        MessageAttributes: {
          'device-id': {
            DataType: 'String',
            StringValue: `${deviceId}`,
          },
          destination: {
            DataType: 'String',
            StringValue: 'invenco.system',
          },
          'user-id': {
            DataType: 'String',
            StringValue: userId,
          },
          expiry: {
            DataType: 'String',
            StringValue: `${+moment().add(2, 'hours')}`,
          },
          type: {
            DataType: 'String',
            StringValue: 'sys.reboot',
          },
        },
      };

      try {
        yield AWS.sendSqsMessage(params);
        res.send(200);
      } catch (err) {
        return next(new restify.errors.InternalServerError(err));
      }

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  getBulkOperationsSummary: async (req, res, next) => {
    const companyId = req.user.company.id;
    const companyName = req.user.company.name;
    const paginationParams = paginationHelper.parsePaginationParams(req);

    try {
      const sqlWith = `
      WITH 
      totals AS (
          SELECT 
            bo.id as bulk_operation_id1,
            boi.id,
            boi.bulk_operation_id,
            bo.type,
            bo.date_created,
            bo.created_by,
            u.full_name AS user_name,
            u.company_id,
            bo.schedule,
            CASE
              WHEN bo.type = 'sys.reboot'                  THEN j.status
              WHEN bo.type = 'sys.recommission'            THEN boi.bulk_operation_status
              WHEN bo.type = 'sys.merchant-reset'          THEN boi.bulk_operation_status END AS status
            FROM bulk_operation bo 
              INNER JOIN bulk_operation_item boi ON bo.id = boi.bulk_operation_id 
              INNER JOIN ics_user u ON bo.created_by = u.id
              LEFT JOIN job j ON boi.job_id = j.id
            WHERE u.company_id = $1
            ),`;

      const sqlGroupData = `GroupData AS (
            SELECT
                bulk_operation_id1,
                COUNT(*) AS total_devices,
                SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) AS total_in_progress,
                SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) AS total_cancelled,
                SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) AS total_failed,
                SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) AS total_completed
            FROM totals
            GROUP BY bulk_operation_id1
          )`;

      const sqlSelect = `                
            SELECT DISTINCT
                t.bulk_operation_id1 AS bulk_operation_id,
                t.type AS bulk_operation_type,
                t.date_created,
                t.created_by,
                t.user_name,
                t.company_id,
                t.schedule,
                gd.total_devices,
                gd.total_in_progress,
                gd.total_cancelled,
                gd.total_failed,
                gd.total_completed
            FROM
                totals t
            INNER JOIN GroupData gd ON
                t.bulk_operation_id1 = gd.bulk_operation_id1
            `;

      const sqlFromWhere = `
            FROM bulk_operation bo
                JOIN ics_user u ON bo.created_by = u.id
            WHERE u.company_id = $1
            `;

      let sqlResult = `
            ${sqlWith}
            ${sqlGroupData}
            ${sqlSelect}
            ORDER BY t.schedule DESC
            `;

      // count by bulkOperationIds since we are grouping the results by bulkOperationIds
      const sqlSelectCount = `
            SELECT
                COUNT(DISTINCT bo.id)
            `;
      const sqlCount = `
            ${sqlSelectCount}
            ${sqlFromWhere}
            `;

      const paramsArr = [companyId];

      if (paginationParams.pageSize > 0) {
        sqlResult = `${sqlResult}
                LIMIT $2
                OFFSET $3`;
        paramsArr.push(paginationParams.pageSize, paginationParams.offset);
      }

      const result = await server.db.replica.rows(sqlResult, paramsArr);
      const resultCount = await server.db.replica.row(sqlCount, [companyId]);
      let bulkOperations = [];

      if (!result || result.length > 0) {
        bulkOperations = result.map(item => ({
          id: item.bulkOperationId,
          type: item.bulkOperationType,
          scheduledAt: item.schedule,
          createdBy: {
            id: item.createdBy,
            name: item.userName,
          },
          company: {
            id: item.companyId,
            name: companyName,
          },
          totalDevices: item.totalDevices,
          totalInProgress: item.totalInProgress,
          totalCancelled: item.totalCancelled,
          totalFailed: item.totalFailed,
          totalCompleted: item.totalCompleted,
        }));
      }

      const data = {
        resultsMetadata: {
          totalResults: resultCount.count,
          pageIndex: paginationParams.pageIndex,
          pageSize: paginationParams.pageSize,
        },
        results: bulkOperations,
      };

      /* bulkOperations object schema:
            [
                {
                    id,
                    type,
                    totalDevices,
                    totalInProgress,
                    totalCancelled,
                    totalFailed,
                    totalCompleted,
                    scheduledAt,
                    createdBy: {
                        id,
                        name
                    },
                    company: {
                        id,
                        name
                    },
                    devices: [{
                        id,
                        serialNumber,
                        name,
                        currentStatus: {
                            code,
                            name
                        },
                        site: {
                            id,
                            name
                        }
                    }]
                }
            ]
             */
      res.send(data);

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getBulkOperationsHistory: async (req, res, next) => {
    const companyId = req.user.company.id;
    const bulkOperationId = req.params.bulkoperationid;

    try {
      const sqlResult = `
            SELECT
                boi.device_id,
                t.name AS device_name,
                t.serial_number,
                j.id AS job_id,
                CASE
                    WHEN bo.type = 'sys.reboot' THEN
                        j.status
                    WHEN bo.type = 'sys.recommission' THEN
                        boi.bulk_operation_status
                    WHEN bo.type = 'sys.merchant-reset' THEN
                        boi.bulk_operation_status
                END AS status,
                json_build_object('id', s.site_id, 'name', s.name) AS "site",
                COALESCE(
                    json_agg(
                        json_build_object(
                            'id', jsh.id,
                            'status', jsh.status,
                            'remark', jsh.message,
                            'timestamp', jsh.created_at
                        )
                    ORDER BY jsh.created_at) FILTER (WHERE jsh.id IS NOT NULL), '[]'
                ) AS history
            FROM bulk_operation bo
            JOIN bulk_operation_item boi ON bo.id = boi.bulk_operation_id
            JOIN target t ON boi.device_id = t.target_id
            JOIN site s ON t.site_id = s.site_id
            JOIN ics_user u ON bo.created_by = u.id
            LEFT JOIN job j ON boi.job_id = j.id
            LEFT JOIN job_status_history jsh ON j.id = jsh.job_id
            WHERE
                u.company_id = $1 AND bo.id = $2
            GROUP BY bo.id, boi.id, j.id, t.serial_number, t.name, s.site_id, s.name
            ORDER BY s.name, t.name
            `;

      const result = await server.db.read.rows(sqlResult, [
        companyId,
        bulkOperationId,
      ]);

      /* result schema:
            [
                {
                    deviceId: 0, // integer
                    deviceName: "",
                    serialNumber: "string",
                    jobId: "uuid",
                    status: 0,
                    site: {
                        id: "uuid",
                        name: "string"
                    }
                    history: [{
                        id: 0, // integer
                        status: 0, // integer
                        remark: "",
                        timestamp: ""
                    }]
                }
            ]
             */
      res.send(result);

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  updateDeviceCertificate: async (req, res, next) => {
    try {
      const { serialNumber } = req.params;
      const { certificate, isJsonCertificates } = req.body;

      const device = await server.db.read.row(
        FIND_DEVICE_BY_SERIAL_NUMBER_FOR_SYSTEM_USER,
        [serialNumber]
      );

      if (!device) {
        return next(
          new restify.errors.NotFoundError(
            `Device (serialNumber: ${serialNumber}) is not found`
          )
        );
      }

      const syncSiteFlag = await isSyncSite(device.siteId);
      const updatedDevice = await server.db.write.execute(
        `
                UPDATE target
                SET certificate = $1, is_json_certificates = $2, presence = $3,  date_updated = now()
                WHERE target_id = $4
                RETURNING target_id AS id, certificate, is_json_certificates, presence;
                `,
        [
          certificate,
          isJsonCertificates,
          syncSiteFlag && device.presence === 'OUT_OF_INSTANCE'
            ? 'UPDATE'
            : device.presence,
          device.targetId,
        ]
      );

      return res.send(updatedDevice[0]);
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getDeviceAvailableMemory: async (req, res, next) => {
    try {
      const { deviceType } = req.query;

      let selectQuery =
        'SELECT device_type, mbytes FROM device_available_memory';
      let result;
      if (!deviceType) {
        // returns an array
        result = await server.db.read.rows(selectQuery);
      } else {
        selectQuery = `${selectQuery} WHERE device_type = $1`;
        // returns an object
        result = await server.db.read.row(selectQuery, [deviceType]);

        if (!result) {
          return next(
            new restify.errors.NotFoundError(
              `Device avalable memory for device type: ${deviceType} is not found`
            )
          );
        }
      }

      res.send(200, result);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  checkRKI: async (req, res, next) => {
    const { site, devices, deviceSerials, companyRef, keyGroupRef } = req.query;
    const isSerialUsed = !!deviceSerials;
    const isCompanyRefUsed = !!companyRef;

    let devicesList = [];

    if (Array.isArray(devices) || Array.isArray(deviceSerials)) {
      devicesList = Array.from(isSerialUsed ? deviceSerials : devices);
    } else {
      devicesList = [isSerialUsed ? deviceSerials : devices];
    }

    const userId = req.user.sub;
    const companyId = req.user.company.id;
    const { approval, retrieving, ready, installing } = krdStates;
    const pendingKRDStatusList = [approval, retrieving, ready, installing];
    const pendingKRSStatusList = [
      KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
      KEY_REQUEST_SESSION_STATUS.APPROVED,
      KEY_REQUEST_SESSION_STATUS.IN_PROGRESS,
      KEY_REQUEST_SESSION_STATUS.PENDING_SECOND_AUTHORIZATION,
    ];

    const asyncTasks = [];
    const result = { devicesToRKI: [], deviceType: [] };

    try {
      await new Promise((resolve, reject) => {
        devicesList.forEach(elem => {
          asyncTasks.push(async () => {
            let device;
            let company;

            if (isSerialUsed) {
              device = await queryDeviceBySerialNumber(
                userId,
                elem.toString().toUpperCase(),
                companyId
              );
            } else {
              device = await deviceHelper.queryDeviceById(
                userId,
                elem,
                companyId
              );
            }

            if (isCompanyRefUsed) {
              company = await companyHelper.getCompanyByReference(
                companyRef,
                userId
              );
              if (!company || !company.siteId) {
                // if company reference is used and there is no company/default site, then skip the process
                return;
              }
            }

            const destinationKeyGroup = keyGroupRef
              ? { keyGroupRef }
              : await sitesHelper.getKeyGroupBySiteId(
                  isCompanyRefUsed ? company.siteId : site
                );

            if (!device || !destinationKeyGroup) {
              // if destination site doesn't have key group, then RKI is not necessary.
              return;
            }

            const isInvalidRKIDevices = invalidRKIDevices.includes(
              device.deviceType.id
            );

            const deviceId = device.id;
            let pendingKrds =
              (await keyRequestHelper.getKeyRequestDeviceByDeviceId(
                deviceId,
                pendingKRDStatusList
              )) || [];
            pendingKrds = pendingKrds.filter(
              krd =>
                pendingKRSStatusList.indexOf(krd.keyRequestSessionStatus) > -1
            );
            const isPendingRequest =
              pendingKrds.findIndex(
                krd => krd.keyGroupRef === destinationKeyGroup.keyGroupRef
              ) > -1;

            result.deviceType.push(device.deviceType.id);

            if (
              !isPendingRequest &&
              device.keyGroupRef !== destinationKeyGroup.keyGroupRef &&
              !isInvalidRKIDevices
            ) {
              result.devicesToRKI.push(parseInt(deviceId, 10));
            }
          });
        });

        // eslint-disable-next-line no-promise-executor-return
        return async.parallelLimit(asyncTasks, 10, err => {
          if (err) reject(err);
          resolve();
        });
      });

      res.send(200, result);
    } catch (err) {
      errorHandler.onError(req, res, next)(err);
    }
  },

  syncKeygroupToSite: async (req, res, next) => {
    try {
      const { siteId, devices } = req.body;
      const userId = req.user.sub;
      const reason = rkiHelper.RKI_REASON.KEYGROUP_CHANGE;

      // check if site key group ref exists
      const siteKeyGroup = await sitesHelper.getKeyGroupBySiteId(siteId);

      if (!siteKeyGroup) {
        return next(
          new restify.errors.NotFoundError(
            `Site's (${siteId}) key group not found`
          )
        );
      }

      const devicesWithInFlight = await sitesHelper.getKRSStatusDevice(devices);

      // get only devices w/c doesn't have in-flight RKI request and have keygroup mismatch with its current site
      const filteredDevices = devicesWithInFlight.filter(
        device =>
          !device.inFlight && siteKeyGroup.keyGroupId !== device.keyGroupId
      );

      const deviceIds = filteredDevices.map(device => device.id);

      req.log.info(
        `Devices' [${deviceIds}] key group are being synched to site: ${siteId}`
      );

      // create the automated RKI request
      const result = await krsHandler.createRKIRequestSession(
        null,
        deviceIds,
        siteKeyGroup.keyGroupRef,
        siteKeyGroup.issuerCode,
        reason,
        userId,
        siteId
      );

      if (result && result.error) {
        const err = restifyErrors.makeErrFromCode(
          result.error.code,
          result.error.message
        );
        return next(err);
      }

      res.send(200, result);
      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },

  updateDevicesHealth: async (req, res, next) => {
    try {
      const active = await server.db.read.row(`
                SELECT pid FROM pg_stat_activity
                WHERE query ~* '^[\r|\s]*REFRESH MATERIALIZED VIEW CONCURRENTLY mv_device_health;.*'
                LIMIT 1
            `);

      if (active) {
        req.log.warn(
          `Skipping refresh mv_device_health, Already running with (PID: ${active.pid}).`
        );

        // Report to NewRelic for refresh materialized view skip.
        if (env.config.enableNewRelic) {
          newrelic.recordCustomEvent('DeviceHealthUpdateSkip', {
            pid: active.pid,
          });
        }
      } else {
        req.log.info(
          '[Device].[UpdateDevicesHealth] Execute mv_device_health refresh.'
        );
        await server.db.write.execute(
          'REFRESH MATERIALIZED VIEW CONCURRENTLY mv_device_health;'
        );
      }
      res.send(204);
      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },

  // eslint-disable-next-line consistent-return
  filterDevice: async (req, res, next) => {
    try {
      const userId = req.user.sub;
      const companyId = req.user.company.id;
      const pageIndex = _.parseInt(req.body.pageIndex) || 0;
      const pageSize = _.parseInt(req.body.pageSize) || 100;
      const {
        serialNos,
        deviceNames,
        deviceTypes,
        releases,
        operator,
        status,
        location,
      } = req.body;

      const allowedKeys = [
        'serialNos',
        'deviceNames',
        'deviceTypes',
        'releases',
        'operator',
        'status',
        'location',
        'pageSize',
        'pageIndex',
      ];

      const receivedKeys = Object.keys(req.body);

      // Check if all received keys are allowed
      const invalidKeys = receivedKeys.filter(
        key => !allowedKeys.includes(key)
      );

      if (invalidKeys.length > 0) {
        return next(
          new restify.errors.BadRequestError(
            `Invalid parameters received ${invalidKeys}`
          )
        );
      }
      const conditions = [];
      let results = [];
      const queryParams = [];

      if (serialNos && serialNos.length > 0) {
        const serialNumbers = serialNos.map(serial => `'${serial}'`).join(',');
        conditions.push(`t.serial_number IN (${serialNumbers})`);
      }
      if (deviceNames && deviceNames.length > 0) {
        const names = deviceNames.map(name => `'${name}'`).join(',');
        conditions.push(`t.name IN (${names})`);
      }
      if (deviceTypes && deviceTypes.length > 0) {
        const typeConditions = deviceTypes.map(device => {
          const { type, screenSize } = device;
          // Check if the device has a screen_Size, otherwise only filter by type
          if (screenSize !== null && screenSize !== undefined) {
            const screenSizeMap = {
              8: 2,
              12: 4,
              15: 5,
            };
            const mappedScreenSize = screenSizeMap[screenSize] || screenSize;
            return `(t.device_type = '${type.toUpperCase()}' AND t.screen_size = ${mappedScreenSize})`;
            // eslint-disable-next-line no-else-return
          } else {
            return `t.device_type = '${type.toUpperCase()}'`;
          }
        });

        // Join the conditions for each device type
        conditions.push(`(${typeConditions.join(' OR ')})`);
      }
      if (releases && operator === undefined) {
        return next(
          new restify.errors.NotFoundError(`Please specify the operator`)
        );
      }
      if (operator && releases === undefined) {
        return next(
          new restify.errors.NotFoundError(`Please specify the release version`)
        );
        // eslint-disable-next-line no-else-return
      } else if (releases && operator !== '=') {
        if (releases.length === 1) {
          conditions.push(`t.release_version ${operator} '${releases}'`);
        } else {
          return next(
            new restify.errors.NotFoundError(
              `Only single value is allowed for Operator (${operator})`
            )
          );
        }
      } else if (releases && operator === '=') {
        const deviceVer = releases.map(relV => `'${relV}'`).join(',');
        conditions.push(`t.release_version IN (${deviceVer})`);
      }

      if (status && status.length > 0) {
        const oos = status.map(oosF => `'${oosF}'`).join(',');
        conditions.push(`device_health IN (${oos})`);
      }
      if (location && location.length > 0) {
        const locationConditions = location
          .map(loc => {
            const [namePart, addressPart] = loc.split(',', 2);

            const nameCondition = namePart
              ? `s.name LIKE '%${namePart.trim()}%'`
              : '';
            const addressCondition = addressPart
              ? `s.formatted_address LIKE '%${addressPart.trim()}%'`
              : '';

            return addressPart
              ? `(${nameCondition} AND ${addressCondition})`
              : `(${nameCondition})`;
          })
          .join(' OR ');

        conditions.push(`(${locationConditions})`);
      }
      let query = `${FIND_SINGLE_DEVICE_QUERY}`;

      if (conditions.length > 0) {
        query += ` AND (${conditions.join(' AND ')})`;
      }

      const sqlSelectCount = `${COUNT_DEVICE_QUERY} AND (${conditions.join(' AND ')}) AND u.user_id = $1 AND cpt.company = $2`;
      query += ` AND u.user_id = $1 AND cpt.company = $2
                GROUP BY t.target_id, s.name, s.formatted_address, cpt.display_name, ps.name, ps.version, 
                ars.suspended_by_device_until, ars.suspended_from, ars.suspended_until, 
                kg.ksn_prefix, gp.playlist_json ->> 'guid', s.key_group_id, kg.key_group_id, kg.certificate_issuer_id ORDER BY t.created desc`;

      return co(function* run() {
        queryParams.push(userId, companyId);
        const { count } = yield server.db.read.row(sqlSelectCount, queryParams);

        if (count > 0) {
          if (pageSize > 0) {
            const offset = pageIndex * pageSize;
            query = `
                        ${query}
                            LIMIT $${queryParams.length + 1}
                            OFFSET $${queryParams.length + 2}`;
            queryParams.push(pageSize, offset);
          }

          const devices = yield server.db.read.rows(query, queryParams);

          if (devices.length === 0) {
            return next(
              new restify.errors.NotFoundError(
                `Device with this criteria not found`
              )
            );
          }
          /* eslint-disable no-param-reassign */
          results = yield Promise.all(
            devices.map(async device => {
              device.deviceType.screenSize =
                enumToScreenSize[device.deviceType.screenSize] || null;
              device.statusStr = device.status;
              device.status = deviceHelper.translateDeviceHealthToNumber(
                device.status
              );
              device.featureFlags = deviceFeatureCache.get(device.id);

              if (device.oosConditions && device.oosConditions.length) {
                const oosCategories = new Map();
                device.oosConditions = device.oosConditions
                  .map(item => {
                    const match = deviceHelper.getOOSCategoryAndCondition({
                      name: item.state,
                      value: item.value,
                    });
                    if (match) {
                      const { category, condition } = match;
                      // eslint-disable-next-line no-unused-expressions
                      !oosCategories.has(category)
                        ? oosCategories.set(category, [condition])
                        : oosCategories.set(category, [
                            ...oosCategories.get(category),
                            condition,
                          ]);
                      return { category, condition };
                    }
                    return undefined;
                  })
                  .filter(Boolean);
                device.oosCategories = Array.from(oosCategories);
              }
              return device;
            })
          );
        }
        const data = {
          resultsMetadata: {
            totalResults: count,
            // eslint-disable-next-line object-shorthand
            pageIndex: pageIndex,
            // eslint-disable-next-line object-shorthand
            pageSize: pageSize,
          },
          results,
        };

        res.send(data);
        return next();
      }).catch(errorHandler.onError(req, res, next));
    } catch (error) {
      errorHandler.onError(req, res, next);
    }
  },
};

module.exports.private = {
  deviceDataFullNames,
};
