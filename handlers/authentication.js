const restify = require('restify');
const co = require('co');

const { config } = require('../env');
const { server } = require('../app');
const hashPassword = require('../lib/hash-password');
const totp = require('../lib/totp');
const errorHandler = require('../lib/errorhandler');
const authHelper = require('../helpers/auth-helper');

const LoginFailedTypes = {
  WRONG_PASSWORD: 0,
  WRONG_EMAIL: 1,
  USER_INACTIVE: 2,
};

function recordAuthHistory(userId, type, success) {
  return co(function* execute() {
    const query = `
            INSERT INTO auth_history
                ( user_id, type, timestamp, success )
            VALUES
                ( $1, $2, NOW(), $3 );
        `;

    yield server.db.write.execute(query, [userId, type, success]);
  }).catch(err => {
    // eslint-disable-line no-unused-vars
    server.log.warn({ err }, 'Recording auth history failed');
    // Swallow any errors here and let authentication flow continue
  });
}

/**
 * Common function to get a user by email
 */
function getUserByEmail(email) {
  return co(function* execute() {
    const user = yield server.db.read.row(
      `
            WITH company AS (
                SELECT
                  c.id,
                  c.name,
                  COALESCE(json_agg(cf.feature_flag)
                    FILTER (WHERE cf.feature_flag IS NOT NULL), '[]') AS "featureFlags",
                  c.session_expiry_user_mins AS "sessionExpiryUserMins"
                FROM company c
                LEFT JOIN company_feature_flag cf ON c.id = cf.company
                GROUP BY c.id
            )
            SELECT
              u.id,
              u.email,
              u.full_name,
              u.password_hash,
              u.mfa_secret,
              u.email_verified,
              u.status,
              u.type,
              u.created,
              row_to_json(c.*) AS company,
              array(
                SELECT r.role_name AS name
                FROM role as r
                JOIN user_role ur ON ur.user_id = u.id AND ur.role_id = r.role_id
              ) as roles
            FROM ics_user u
            LEFT JOIN company c on c.id = u.company_id
            WHERE
              u.email = LOWER($1) AND
              u.status = 1;
        `,
      [email]
    );

    return user;
  }).catch(err => {
    server.log.error(err);
    throw err;
  });
}

/**
 * Validate user and verify password
 */
function validateUser(user, email, password) {
  return co(function* execute() {
    // If there's no user or user is inactive or user not email verifed
    if (!user) {
      server.log.debug(`User with email ${email} not found`);
      return false;
    }

    if (user.type !== 'SYSTEM' && !user.emailVerified) {
      server.log.debug({ user }, 'Email not verified');
      return false;
    }

    // validate password
    if (!(yield hashPassword.verify(password, user.passwordHash))) {
      server.log.debug({ user }, 'Invalid password');
      recordAuthHistory(user.id, 'email-password', false);
      return false;
    }

    return true;
  }).catch(err => {
    server.log.error(err);
    throw err;
  });
}

async function fullValidateUser(user, email, password) {
  try {
    if (!user) {
      server.log.debug(`User with email ${email} not found`);
      const users = await server.db.read.rows(
        `
                SELECT
                  u.id
                FROM ics_user u
                WHERE
                  u.email = LOWER($1) AND
                  u.status = 2;
            `,
        [email]
      );
      if (users.length) {
        return LoginFailedTypes.USER_INACTIVE;
      }
      return LoginFailedTypes.WRONG_EMAIL;
    }

    if (user.type !== 'SYSTEM' && !user.emailVerified) {
      server.log.debug({ user }, 'Email not verified');
      return LoginFailedTypes.WRONG_EMAIL;
    }

    // validate password
    if (!(await hashPassword.verify(password, user.passwordHash))) {
      server.log.debug({ user }, 'Invalid password');
      recordAuthHistory(user.id, 'email-password', false);
      return LoginFailedTypes.WRONG_PASSWORD;
    }

    return true;
  } catch (err) {
    server.log.error(err);
    throw err;
  }
}

module.exports = {
  authenticate: (req, res, next) =>
    co(function* execute() {
      const { email } = req.body;
      const { password } = req.body;
      const { mfaCode } = req.body;

      req.log.info(`[Authn] Start authenticate user: ${email}.`);

      const user = yield getUserByEmail(email);

      if (user) {
        req._userId = user.id; // eslint-disable-line no-param-reassign, no-underscore-dangle
        req._companyId = user.company ? user.company.id : null; // eslint-disable-line no-param-reassign, no-underscore-dangle
      }

      const isValid = yield fullValidateUser(user, email, password);
      if (isValid !== true) {
        switch (isValid) {
          case LoginFailedTypes.USER_INACTIVE:
            return next(new restify.ForbiddenError('User is blocked'));
          case LoginFailedTypes.WRONG_EMAIL:
          case LoginFailedTypes.WRONG_PASSWORD:
          default:
            return next(new restify.UnauthorizedError('Authentication failed'));
        }
      }

      req.user = user; // eslint-disable-line no-param-reassign

      if (user.type === 'SYSTEM') {
        req.log.info(`[Authn] System user logged in: ${email}.`);
        res.send(200, yield authHelper.createAuthResp(user));
        return next();
      }

      req.log.info(`[Authn] User email and password was valid: ${email}.`);
      if (!user.mfaSecret) {
        req.log.info(`[Authn] User email  MFA is not configured: ${email}.`);

        const mfaTotp = yield totp.createTotp(req);

        res.send(202, {
          issuer: config.auth.issuer,
          secret: {
            ascii: mfaTotp.ascii,
            base32: mfaTotp.base32,
            hex: mfaTotp.hex,
          },
          otpURL: mfaTotp.otpauth_url,
          qrCodeData: mfaTotp.image.data,
        });

        return next();
      }

      if (user.mfaSecret && (!mfaCode || !mfaCode.length)) {
        req.log.info(
          `[Authn] Password was validated, mfaCode is not provided. User: ${email}.`
        );
        yield recordAuthHistory(user.id, 'email-password-no-mfa', true);

        res.send(204);
        return next();
      }

      const isCodeValid = yield totp.validateTotp(req, user.mfaSecret, mfaCode);
      if (isCodeValid === false) {
        req.log.info(
          `[Authn] MFA code validation failed. MFA code: ${mfaCode}. User: ${email}.`
        );
        yield recordAuthHistory(user.id, 'email-password-mfa', false);

        return next(
          new restify.errors.NotAcceptableError('MFA code validation failed')
        );
      }

      yield recordAuthHistory(user.id, 'email-password-mfa', true);
      const flagInstance = yield server.appFeatureFlag.getInstance();
      const flags = flagInstance.fetchAllFeatureFlags();
      res.send(200, yield authHelper.createAuthResp(user, flags));
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  authAndSetMFA: (req, res, next) =>
    co(function* execute() {
      const mfaReq = req.body;
      req.log.info(`Setup MFA for user ${mfaReq.email}`);

      const user = yield getUserByEmail(mfaReq.email);

      if (user) {
        req._userId = user.id; // eslint-disable-line no-param-reassign, no-underscore-dangle
        req._companyId = user.company ? user.company.id : null; // eslint-disable-line no-param-reassign, no-underscore-dangle
      }

      const isValid = yield validateUser(user, mfaReq.email, mfaReq.password);
      if (!isValid) {
        return next(new restify.UnauthorizedError('Authentication failed'));
      }

      req.user = user; // eslint-disable-line no-param-reassign

      if (user.mfaSecret && user.mfaSecret !== '') {
        yield recordAuthHistory(user.id, 'set-mfa-no-secret', false);
        return next(new restify.ForbiddenError('MFA code already set'));
      }

      const isCodeValid = yield totp.validateTotp(
        req,
        mfaReq.mfaSecret,
        mfaReq.mfaCode
      );
      if (!isCodeValid) {
        req.log.info('MFA code validation failed');
        yield recordAuthHistory(user.id, 'set-mfa-bad-code', false);
        return next(
          new restify.errors.NotAcceptableError('MFA code validation failed')
        );
      }

      yield server.db.write.execute(
        `
                UPDATE ics_user SET
                    mfa_secret = $1
                WHERE
                    id = $2
                `,
        [mfaReq.mfaSecret, user.id]
      );

      yield recordAuthHistory(user.id, 'set-mfa', true);
      res.send(200, yield authHelper.createAuthResp(user));
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  validateUser,
};
