/* eslint-disable no-promise-executor-return */
/* eslint-disable no-param-reassign */
const childProcess = require('child_process');
const crypto = require('crypto');
const { URL } = require('url');
const moment = require('moment');
const uuid = require('uuid/v4');
const restify = require('restify');
const _ = require('lodash');
const { backOff } = require('exponential-backoff');
const { default: RedisClient } = require('../lib/redis');
const { server } = require('../app');
const {
  createJobDependencyBatch,
  createJobsBatch,
  selectRebootJobs,
  updateRebootJobsBatch,
} = require('../src/rollout/models/deployment.model');
const jobHelper = require('../helpers/job-helper');
const env = require('../env');
const {
  signingKeyTypes,
  DAY_IN_SECONDS,
  isUnitTest,
} = require('../lib/app-constants');
const logger = require('../lib/logger').mainLogger();

const cloudHsmConfig = env.config.cloudhsm || {};

const createConfigManagementRebootJob = async (
  rebootDeviceIds,
  embargoTime,
  configJobUpdateExpiryDate,
  user,
  rebootDate,
  conn,
  configUpdateJobMap,
  rebootJobs
) => {
  server.log.info(`[CreateRebootJob] Create new config management reboot jobs`);

  const rebootDeviceCount = rebootDeviceIds.length;

  const rebootJob = {
    // eslint-disable-next-line no-unused-vars
    jobId: [...Array(rebootDeviceCount)].map(i => uuid()),
    deviceId: rebootDeviceIds,
    destination: Array(rebootDeviceCount).fill('invenco.system'),
    type: Array(rebootDeviceCount).fill(jobHelper.jobType.REBOOT),
    data: Array(rebootDeviceCount).fill({}),
    status: Array(rebootDeviceCount).fill(jobHelper.jobStatus.NEW),
    embargo: Array(rebootDeviceCount).fill(embargoTime),
    expiry: Array(rebootDeviceCount).fill(configJobUpdateExpiryDate.toDate()),
    createdBy: Array(rebootDeviceCount).fill(user),
    createdOn: Array(rebootDeviceCount).fill(rebootDate),
    message: Array(rebootDeviceCount).fill('new job'),
  };

  rebootJobs.push(
    ...[...Array(rebootDeviceCount).keys()].map(i => ({
      deviceId: rebootJob.deviceId[i],
      jobId: rebootJob.jobId[i],
      type: jobHelper.jobType.REBOOT,
    }))
  );

  await createJobsBatch(conn, Object.values(rebootJob));

  const dependencyJob = {
    jobId: rebootJob.jobId,
    dependsOn: rebootJob.deviceId.map(deviceId =>
      configUpdateJobMap.get(deviceId)
    ),
    continueOnFail: Array(rebootDeviceCount).fill(false),
  };
  await createJobDependencyBatch(conn, Object.values(dependencyJob));
};

async function createConfigUpdateJob(
  conn,
  fileHash,
  s3FileKey,
  configFileDescriptor,
  fileSize,
  deviceIdToSignatureMappings,
  user,
  deviceIds,
  rebootDeviceIds,
  restartDevices,
  jobExpireMins
) {
  const {
    rebootExpiryMin = 20,
    rebootEmbargoMin = 10,
    rebootQueryLimitTime = 4,
    rebootJobExpiryMins,
    MAX_CM_REBOOT_EMBARGO_TIME = 60,
  } = env.config.configManagementJobs;

  const now = Date.now();
  const nowDate = new Date(now);

  const configJobUpdateExpiryDate = moment().add(jobExpireMins, 'minute');

  const apiUrl = new URL(env.config.url.api);
  const configFileURL = `${apiUrl.protocol}//${apiUrl.host}/${s3FileKey}`;

  const getConfigJobPayload = deviceId => ({
    messageVersion: 1,
    fileHash,
    fileHashAlg: 'sha256',
    fileURI: configFileURL,
    configName: configFileDescriptor.name,
    storageName: configFileDescriptor.filename,
    fileSize,
    fileSig: deviceIdToSignatureMappings
      ? deviceIdToSignatureMappings.find(map => map.deviceId === deviceId)
          .signature
      : null,
    fileSigAlg: configFileDescriptor['sig-type'],
    fileSigKey: deviceIdToSignatureMappings
      ? deviceIdToSignatureMappings.find(map => map.deviceId === deviceId).key
      : null,
  });

  const deviceCount = deviceIds.length;

  const configUpdateJob = {
    // eslint-disable-next-line no-unused-vars
    jobId: [...Array(deviceCount)].map(i => uuid()),
    deviceId: deviceIds,
    destination: Array(deviceCount).fill('invenco.system'),
    type: Array(deviceCount).fill(jobHelper.jobType.CONFIG_UPDATE),
    data: deviceIds.map(getConfigJobPayload),
    status: Array(deviceCount).fill(jobHelper.jobStatus.NEW),
    embargo: Array(deviceCount).fill(nowDate),
    expiry: Array(deviceCount).fill(configJobUpdateExpiryDate),
    createdBy: Array(deviceCount).fill(user),
    createdOn: Array(deviceCount).fill(nowDate),
    message: Array(deviceCount).fill('new config update job'),
  };
  await createJobsBatch(conn, Object.values(configUpdateJob));

  const rebootNow = now + 5 * 1000; // Make sure the reboot job created date is after update job 5 second
  const rebootDate = new Date(rebootNow);

  const rebootJobs = [];
  let restartJob = [];
  const restartDeviceCount = restartDevices.length;
  const configUpdateJobMap = new Map(
    configUpdateJob.jobId.map((jobId, i) => [
      configUpdateJob.deviceId[i],
      jobId,
    ])
  );

  if (!_.isEmpty(rebootDeviceIds)) {
    server.log.info(`Create rebootJob for devices ${rebootDeviceIds.join()}`);
    const rebootJobExpiryDate = new Date(rebootNow);

    rebootJobExpiryDate.setMinutes(
      configJobUpdateExpiryDate.minutes() + rebootJobExpiryMins
    );

    const embargoTime = moment().add(rebootEmbargoMin, 'minutes');

    const timeBeforeNow = moment()
      .subtract(rebootQueryLimitTime, 'hour')
      .toDate();

    const thresholdEmbargo = moment(embargoTime)
      .add(MAX_CM_REBOOT_EMBARGO_TIME, 'minutes')
      .toDate();

    const selectParams = [
      `{${rebootDeviceIds.join(',')}}`,
      'sys.reboot',
      jobHelper.jobStatus.NEW,
      timeBeforeNow,
      thresholdEmbargo,
    ];

    const { rows: existingRebootJobs } = await selectRebootJobs(
      conn,
      selectParams
    );

    if (_.isEmpty(existingRebootJobs)) {
      // create new reboot jobs
      await createConfigManagementRebootJob(
        rebootDeviceIds,
        embargoTime,
        configJobUpdateExpiryDate,
        user,
        rebootDate,
        conn,
        configUpdateJobMap,
        rebootJobs
      );
    } else {
      server.log.info(
        `[CreateRebootJob] We found some existing reboot jobs for provided devices`,
        existingRebootJobs
      );

      const newExpiryTime = moment(embargoTime).add(rebootExpiryMin, 'minutes');

      server.log.info(
        `[CreateRebootJob] New expiry time for reboot jobs is ${newExpiryTime} on top of embargo time ${embargoTime}`
      );

      const updatedJobs = existingRebootJobs.filter(job =>
        newExpiryTime.isBefore(job.expiry)
      );

      const updatedJobIds = updatedJobs.map(job => job.id);
      let updatedDeviceIds;
      if (!_.isEmpty(updatedJobIds)) {
        server.log.info(
          `[CreateRebootJob] Reboot jobs which have enough time before expiry: ${updatedJobIds}`
        );

        const updateParams = [embargoTime, `{${updatedJobIds.join(',')}}`];
        await updateRebootJobsBatch(conn, updateParams);
        updatedDeviceIds = updatedJobs.map(job => job.deviceId);
        rebootJobs.push(
          ...updatedJobs.map(job => ({
            deviceId: job.deviceId,
            jobId: job.id,
            type: jobHelper.jobType.REBOOT,
          }))
        );
      }
      const deviceIdsForReboot = _.difference(
        rebootDeviceIds,
        updatedDeviceIds
      );

      if (!_.isEmpty(deviceIdsForReboot)) {
        server.log.info(
          `[CreateRebootJob] Reboot jobs which do not have enough time before expiry and need to be created: ${deviceIdsForReboot}`
        );
        // create new reboot jobs
        await createConfigManagementRebootJob(
          deviceIdsForReboot,
          embargoTime,
          configJobUpdateExpiryDate,
          user,
          rebootDate,
          conn,
          configUpdateJobMap,
          rebootJobs
        );
      }
    }
  }

  if (!_.isEmpty(restartDevices)) {
    const restartDeviceIds = restartDevices.map(device => device.deviceId);

    server.log.info(
      `Create restartJob for devices ${restartDeviceIds.join(',')}`
    );

    restartJob = {
      // eslint-disable-next-line no-unused-vars
      jobId: [...Array(restartDeviceCount)].map(i => uuid()),
      deviceId: restartDeviceIds,
      destination: Array(restartDeviceCount).fill('invenco.system'),
      type: Array(restartDeviceCount).fill(jobHelper.jobType.RESTART),
      data: restartDevices.map(device => ({
        applications: device.restartData,
      })),
      status: Array(restartDeviceCount).fill(jobHelper.jobStatus.NEW),
      embargo: Array(restartDeviceCount).fill(new Date()),
      expiry: Array(restartDeviceCount).fill(configJobUpdateExpiryDate),
      createdBy: Array(restartDeviceCount).fill(user),
      createdOn: Array(restartDeviceCount).fill(new Date()),
      message: Array(restartDeviceCount).fill('new restart job'),
    };
    await createJobsBatch(conn, Object.values(restartJob));
    const dependencyJob = {
      jobId: restartJob.jobId,
      dependsOn: restartJob.deviceId.map(deviceId =>
        configUpdateJobMap.get(deviceId)
      ),
      continueOnFail: Array(restartDeviceCount).fill(false),
    };
    await createJobDependencyBatch(conn, Object.values(dependencyJob));
  }

  return {
    configJobs: [...Array(deviceCount).keys()].map(i => ({
      deviceId: configUpdateJob?.deviceId[i],
      jobId: configUpdateJob?.jobId[i],
      type: configUpdateJob?.type[i],
    })),
    rebootJobs,
    restartJobs: [...Array(restartDeviceCount).keys()].map(i => ({
      deviceId: restartJob?.deviceId[i],
      jobId: restartJob?.jobId[i],
      type: restartJob?.type[i],
      data: restartJob?.data[i]?.applications,
    })),
  };
}

function generateRedisKey(args) {
  const { key, slot, deviceType, fileHash, companyId } = args;
  const concanatedArgs = `${deviceType}-${slot}-${key}-${fileHash}`;
  const md5Hash = crypto.createHash('md5').update(concanatedArgs).digest('hex');
  return `sgn:{${companyId}}:${md5Hash}`;
}

async function generateConfigHashSignatureKeyMappings(args) {
  const keyToSignatureMapping = {};
  try {
    const redisClient = RedisClient.getInstance();
    const { keys, slot, deviceType, fileHash } = args;
    const redisData = await Promise.all(
      keys.map(async key => {
        try {
          const cachedSignature = await redisClient.getValue(
            generateRedisKey({ ...args, key })
          );
          return {
            key,
            signature: cachedSignature,
          };
        } catch (err) {
          logger.info(
            `[generateConfigHashSignatureKeyMappings] Redis read could not happen ${err}, will fetch from HSM`
          );
          return {
            key,
            signature: null,
          };
        }
      })
    );

    const hsmSignatures = await Promise.all(
      redisData
        .filter(keyData => !keyData.signature)
        .map(async keyData => {
          const { key } = keyData;
          const signatureFromHSM = async () =>
            // eslint-disable-next-line no-return-await
            await backOff(
              async () =>
                generateConfigHashSignature(key, slot, deviceType, fileHash),
              {
                jitter: 'full',
                maxDelay: cloudHsmConfig.maxRetryMs || 20000,
                numOfAttempts: cloudHsmConfig.numOfAttempts || 20,
                retry: (err, attemptNumber) => {
                  logger.warn(
                    `[generateConfigHashSignature] Retrying to fetch signature from HSM for #${attemptNumber} time, received error from HSM =>  ${err}`
                  );
                  // As long as it does not exceed numOfAttempts it will keep retrying
                  return true;
                },
              }
            );

          const signature = await signatureFromHSM().catch(error => {
            logger.error(
              `Retry limit exceeded, for current attempt with ${error}`
            );
            throw new Error('HSM: error, could not fetch the signature');
          });

          try {
            await redisClient.saveKeyValueWithTTL(
              generateRedisKey({ ...args, key }),
              signature,
              cloudHsmConfig.cacheExpiryInSeconds || 5 * DAY_IN_SECONDS
            );
          } catch (err) {
            logger.info(
              `[generateConfigHashSignatureKeyMappings] Redis save could not happen due to ${err}`
            );
          }
          return {
            key,
            signature,
          };
        })
    );

    // merge signatures to single object
    redisData.forEach(keyData => {
      if (keyData.signature) {
        keyToSignatureMapping[keyData.key] = keyData.signature;
      }
    });

    hsmSignatures.forEach(keyData => {
      keyToSignatureMapping[keyData.key] = keyData.signature;
    });

    // check if any signature is empty , if it is throw error
    let isAnySignatureMissing = false;
    // eslint-disable-next-line no-restricted-syntax
    for (const key of Object.keys(keyToSignatureMapping)) {
      if (!keyToSignatureMapping[key]) {
        logger.error(`Signature not found for key , ${key} `);
        isAnySignatureMissing = true;
      }
    }

    if (isAnySignatureMissing) {
      throw new Error('One or more signatures could not be found');
    }
  } catch (error) {
    server.log.error(`[generateConfigHashSignatureKeyMappings] : ${error}`);
    throw error;
  }

  return keyToSignatureMapping;
}

function generateConfigHashSignature(key, slot, device, hash) {
  if (isUnitTest) return 'mockedSignature';

  return new Promise((resolve, reject) => {
    const { scriptPath } = env.config.signConfig;
    const command = `${scriptPath} ${key} ${slot} ${device} ${hash}`;
    return childProcess.exec(command, (error, stdout, stderr) => {
      if (stdout) {
        stdout = stdout.trim();
      }

      if (error) {
        error.stdout = stdout;
        error.stderr = stderr;
        return reject(error);
      }

      const ERROR_SIGNATURE = 'Err:';
      if (stdout.indexOf(ERROR_SIGNATURE) === 0) {
        return reject(stdout);
      }

      return resolve(stdout);
    });
  });
}

async function getSignatureMappings(req, companyId, deviceIds, fileHash) {
  const targetToKeyMappings = await server.db.read.rows(
    `
              select t.target_id, t.device_type, cdk.key
              from target t
              left join company_device_keys cdk
                  on t.device_type = cdk.device_type
                         and cdk.company = $1::uuid
                         and cdk.key_type = $2::text
              where t.target_id = ANY( $3::int[] )
          `,
    [companyId, signingKeyTypes.VENDOR, deviceIds]
  );

  const deviceIdsWithoutKeys = targetToKeyMappings
    .filter(k => !k.key)
    .map(k => k.targetId);
  if (deviceIdsWithoutKeys.length) {
    req.log.error(
      `Devices with the following IDs do not have vendor keys: ${deviceIdsWithoutKeys.join()}`
    );
    throw new restify.BadRequestError('Invalid vendor keys');
  }

  const uniqueKeys = _.uniq(targetToKeyMappings.map(k => k.key));

  const { slot, deviceType } = env.config.signConfig;
  const keyToSignatureMap = await generateConfigHashSignatureKeyMappings({
    keys: uniqueKeys,
    slot,
    deviceType,
    fileHash,
    companyId,
  });

  return targetToKeyMappings.map(k => ({
    deviceId: k.targetId,
    signature: keyToSignatureMap[k.key],
    key: k.key,
  }));
}

module.exports = {
  createConfigUpdateJob,
  getSignatureMappings,
  generateConfigHashSignatureKeyMappings,
  generateRedisKey,
};
