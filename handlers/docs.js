const url = require('url');
const fs = require('fs');
const restify = require('restify');
const co = require('co');
const rp = require('request-promise');
const merge = require('deepmerge');
const _ = require('lodash');
const stringify = require('json-stable-stringify');
const yaml = require('js-yaml');
const swaggerJSDoc = require('swagger-jsdoc');

const { config } = require('../env');
const AWS = require('../lib/aws');
const errorHandler = require('../lib/errorhandler');
const { server } = require('../app');

const NODE_SWAGGER = yaml.safeLoad(
  fs.readFileSync(`${__dirname}/../docs/nodejs-api.yml`, 'utf8')
);

function downloadWithAuth(targetUrl, authHeadder) {
  const options = {
    uri: targetUrl,
    headers: { Authorization: authHeadder },
    method: 'GET',
    json: true,
  };

  return rp(options)
    .then(data => data)
    .catch(e => {
      throw e;
    });
}

function sendOrderedResponse(res, input) {
  const output = stringify(input); // deterministic stringify

  // Raw Node response to avoid being stringified by Restify
  res.writeHead(200, {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(output),
  });
  res.write(output);
  return res.end();
}

module.exports = {
  /**
   * Return JSON swagger docs for services hosted by NodeJS
   */
  getNodeJSDocs: (req, res) => sendOrderedResponse(res, NODE_SWAGGER),

  getFullAPIDocs: (req, res, next) =>
    co(function* execute() {
      let playlistSwagger = {};
      if (config.url.playlistApi && config.url.playlistSwagger) {
        try {
          playlistSwagger = yield downloadWithAuth(
            config.url.playlistApi + config.url.playlistSwagger,
            req.headers.authorization
          ).catch(err => {
            throw new Error(
              `Failed to load Playlist Swagger file: ${err.message}`
            );
          });

          if (typeof playlistSwagger === 'string') {
            try {
              playlistSwagger = JSON.parse(playlistSwagger);
            } catch (err) {
              throw new Error(
                `Failed to parse Playlist Swagger file: ${err.message}`
              );
            }
          }
        } catch (err) {
          return next(new restify.InternalServerError(err.message));
        }
      }

      const srcFolderSwagger = swaggerJSDoc({
        swaggerDefinition: {
          info: {},
        },
        apis: ['./src/**/*.endpoint.js'],
      });

      // Merge the swagger docs (to get full API)
      const fullSwagger = merge.all(
        [playlistSwagger, srcFolderSwagger, NODE_SWAGGER],
        {
          arrayMerge: (dest, src) => _.union(dest, src), // merge arrays removing duplicates
        }
      );

      // Update host url for the current environment
      fullSwagger.host = url.parse(config.url.api).hostname;

      fullSwagger.tags = _.sortBy(fullSwagger.tags, ['name']);

      return sendOrderedResponse(res, fullSwagger);
    }).catch(errorHandler.onError(req, res, next)),

  getUserGuide: (req, res, next) => {
    if (
      !config.docs ||
      !config.docs.userGuide ||
      !config.docs.userGuide.bucket
    ) {
      return next(
        new restify.errors.InternalServerError('Missing docs config')
      );
    }

    return co(function* execute() {
      const latestRelease = yield server.db.read.row(`
                SELECT release, user_guide FROM ics_releases WHERE visible = true
                ORDER BY release_date DESC LIMIT 1
            `);
      server.log.info(
        `Latest release/guide: ${latestRelease.release}, ${latestRelease.userGuide}`
      );

      if (!latestRelease.userGuide || latestRelease.userGuide === '') {
        return next(
          new restify.NotFoundError('Latest release has no user guide')
        );
      }

      try {
        const params = {
          Bucket: config.docs.userGuide.bucket,
          Key: latestRelease.userGuide,
        };
        // first, make sure object is accessible
        yield AWS.downloadMetadataFromS3(params);
        // then passthrough the stream
        AWS.downloadFromS3(params).createReadStream().pipe(res);
      } catch (err) {
        server.log.error(err);
        return next(new restify.NotFoundError('User Guide file not found'));
      }

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
};
