const restify = require('restify');
const co = require('co');

const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');
const hasher = require('../lib/hash-password');
const totp = require('../lib/totp');
const jwt = require('../lib/jwt');
const { config } = require('../env');
const constants = require('../lib/app-constants');
const usersHelper = require('../helpers/users-helper');
const userChangeHelper = require('../helpers/user-change-helper');
const passwordHelper = require('../helpers/password-helper');
const emailTokenHelper = require('../helpers/email-token-helper');
const authenticateUserRepository = require('../src/authentication/authenticate-user.repository');
const { refreshMVUserSiteAuthorization } = require('../helpers/mv-helper');

const getUserByTokenForgotPassword = `SELECT u.id, u.mfa_secret, u.failed_login_attempts, ( u.email_verified IS NOT NULL ) AS email_verified
    FROM email_token et
    JOIN ics_user u ON u.id = et.user_id
    WHERE et.value = $1
    AND et.type = '${constants.emailTokenType.FORGOT_PASSWORD}'
    AND et.expires > NOW()`;

/**
 * Function to verify a users email
 *
 * NOTE: You'll need to handle a transaction if required
 */
function updateUserEmailVerified(connection, userId) {
  return co(function* execute() {
    const now = new Date();

    yield connection.execute(
      `
                        UPDATE ics_user SET
                            email_verified = $2
                        WHERE
                            id = $1;
                    `,
      [userId, now]
    );

    // Update the user change tables to record the change
    yield userChangeHelper.recordUserChange(
      userId,
      'User Verify Email',
      userId,
      [{ fieldName: 'email_verified', fromValue: null, toValue: now }],
      connection
    );
  }).catch(err => {
    throw err;
  });
}

module.exports = {
  getRegister: (req, res, next) =>
    // query token to get email_token object
    co(function* execute() {
      if (!req.params.token) {
        return next(new restify.BadRequestError('Token not found'));
      }

      const userEmail = yield server.db.read.row(
        `SELECT u.id, u.status, u.email, u.full_name, et.type FROM ics_user as u, email_token as et
                    WHERE et.value = $1 AND et.user_id = u.id AND et.expires > now();`,
        [req.params.token]
      );

      if (!userEmail) {
        req.log.info(`Unable to find token ${req.params.token}`);
        return next(new restify.NotFoundError('Token not found'));
      }

      if (userEmail.type !== 'registration-invite') {
        req.log.info(`Invalid email type ${userEmail.type}`);
        return next(new restify.NotFoundError('Token not found'));
      }

      if (userEmail.status !== server.constants.userStatus.PENDING) {
        req.log.info(`User is no longer pending ${userEmail.status}`);
        return next(new restify.NotFoundError('Token not found'));
      }

      req.user = { email: userEmail.email }; // eslint-disable-line no-param-reassign
      const mfaTotp = yield totp.createTotp(req);

      res.send({
        email: userEmail.email,
        fullName: userEmail.fullName,
        mfa: {
          issuer: config.auth.issuer,
          secret: {
            ascii: mfaTotp.ascii,
            base32: mfaTotp.base32,
            hex: mfaTotp.hex,
          },
          otpURL: mfaTotp.otpauth_url,
          qrCodeData: mfaTotp.image.data,
        },
      });
      return next();
    }).catch(errorHandler.onError(req, res, next)),
  postRegister: (req, res, next) =>
    co(function* execute() {
      const userCreds = req.body;

      const userEmail = yield server.db.read.row(
        `SELECT u.id, u.email, u.full_name, u.status, u.password_hash, u.mfa_secret, u.company_id, et.type FROM ics_user as u, email_token as et
                    WHERE et.value = $1 AND et.user_id = u.id AND et.expires > now();`,
        [userCreds.token]
      );

      if (!userEmail) {
        req.log.info(`Unable to find token ${req.body.token}`);
        return next(new restify.NotFoundError('Token not found'));
      }

      if (userEmail.type !== 'registration-invite') {
        req.log.info(`Invalid email type ${userEmail.type}`);
        return next(new restify.NotFoundError('Token not found'));
      }

      if (userEmail.status !== server.constants.userStatus.PENDING) {
        req.log.info(`User is no longer pending ${userEmail.status}`);
        return next(new restify.NotFoundError('Token not found'));
      }

      // verify that the password is valid
      const hashedPassword = yield hasher.createHash(userCreds.password);

      req.log.info(userCreds.mfaSecret.length, 'User Creds');
      if (
        !(yield totp.validateTotp(req, userCreds.mfaSecret, userCreds.mfaCode))
      ) {
        req.log.info('MFA token did not match');
        return next(new restify.ConflictError('Invalid password or token'));
      }

      // save the user
      yield server.db.write.execute(
        `
                UPDATE ics_user
                SET mfa_secret = $2, status = $3, password_hash = $4, email_verified = now()
                WHERE ics_user.id = $1`,
        [
          userEmail.id,
          userCreds.mfaSecret,
          constants.userStatus.ACTIVE,
          hashedPassword,
        ]
      );

      // Update User Change Log
      try {
        userChangeHelper.recordUserChange(
          userEmail.id,
          'User Register',
          userEmail.id,
          [
            {
              fieldName: 'mfa_secret',
              fromValue: null,
              toValue: userCreds.mfaSecret,
            },
            {
              fieldName: 'password_hash',
              fromValue: null,
              toValue: hashedPassword,
            },
            {
              fieldName: 'status',
              fromValue: null,
              toValue: server.constants.userStatus.ACTIVE,
            },
            {
              fieldName: 'email_verified',
              fromValue: null,
              toValue: new Date(),
            }, // TODO make this more accurate
          ]
        );
      } catch (err) {
        // Swallow errors when recording user changes
        req.log.warn(err);
      }
      refreshMVUserSiteAuthorization('emailTokenRegister');

      const tokenUser = yield usersHelper.getJwtTokenUser(userEmail.id, [0, 1]);
      const flagInstance = yield server.appFeatureFlag.getInstance();
      const flags = flagInstance.fetchAllFeatureFlags();
      req.log.info({ tokenUser }, 'fullUser');
      // TODO: Marcelo - Why is this returning a token ? (after registrer they need to setup a new password they need an initial token to do that)
      res.send({
        token: jwt.createToken(tokenUser),
        flags,
      });
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Function to reset a user password based on the passed token, password and mfaCode.
   * Will validate the passed values and if ok, add to db and return a token to the user
   */
  // TODO: Marcelo - This flow is no longer valid
  forgotPassword: (req, res, next) =>
    co(function* execute() {
      const creds = req.body;
      // Get user from token if exists, type is forgot-password and that it is still valid (not expired)
      const user = yield server.db.read.row(getUserByTokenForgotPassword, [
        creds.token,
      ]);

      // Validate if its a valid forgot password email token
      if (!user) {
        req.log
          .info(`Email token was not found or expired or not ${constants.emailTokenType.FORGOT_PASSWORD}:
                                ${req.body.token}`);
        return next(
          new restify.NotFoundError('Email token was not found or expired')
        ); //
      }

      // If user can reset then validate with the mfa
      if (!(yield totp.validateTotp(req, user.mfaSecret, creds.mfaCode))) {
        req.log.info('MFA token did not match');
        return next(new restify.ForbiddenError('Invalid MFA code'));
      }

      // Validate new password
      if (!(yield passwordHelper.isPasswordAcceptable(req, creds.password))) {
        req.log.info('Password requirements not met');
        return next(new restify.ConflictError('Password requirements not met')); // 409
      }

      req.log.info('Valid password reset, attempting update in database');

      // Get the old password hash
      const oldPassHash = yield usersHelper.getUserPasswordHashById(user.id);

      // Get a connection for transaction control
      const connection = yield server.db.write.getConnection();
      try {
        // Start transaction
        yield connection.execute('BEGIN');

        // Hash the password
        const passwordHash = yield hasher.createHash(creds.password);

        // Update the new password in the database
        yield usersHelper.updateUserPassword(connection, user.id, passwordHash);

        // Update the user change tables to show a password reset
        yield userChangeHelper.recordUserChange(
          user.id,
          'User Reset Password (email)',
          user.id,
          [
            {
              fieldName: 'password_hash',
              fromValue: oldPassHash,
              toValue: passwordHash,
            },
          ],
          connection
        );

        // Reset failed_login_attempt and last_locked
        if (user.failedLoginAttempts > 0) {
          yield authenticateUserRepository.updateUserLoginData(
            user.id,
            0,
            null
          );
        }
        // Delete token from db
        yield emailTokenHelper.deleteEmailToken(connection, creds.token);

        // If the email is not verified, then verify it
        if (!user.emailVerified)
          yield updateUserEmailVerified(connection, user.id);

        // Commit transaction
        yield connection.execute('COMMIT');
        connection.done();

        req.log.info(
          `Password reset token saved to database for user ${user.id}`
        );
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        return next(new restify.InternalServerError(err));
      }

      req.log.info('Valid password reset successfully');

      // Return a valid jwt token
      const tokenUser = yield usersHelper.getJwtTokenUser(user.id, [0, 1]);
      req.log.info({ tokenUser }, 'tokenUser');
      res.send({
        token: jwt.createToken(tokenUser),
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Validates whether the provided password is different from the user's previous 4 passwords.
   * If the password is valid, returns the success message.
   * Otherwise, returns an error message indicating the password is invalid.
   */
  passwordValidity: (req, res, next) =>
    co(function* execute() {
      const creds = req.body;
      // Get user from token if exists, type is forgot-password and that it is still valid (not expired)
      const user = yield server.db.read.row(getUserByTokenForgotPassword, [
        creds.token,
      ]);
      if (!user) {
        req.log.warn('User not found or token is invalid');
        return next(
          new restify.NotFoundError('User not found or invalid token')
        );
      }
      yield passwordHelper.validatePassword(req, user.id, creds.password);
      res.send(200, { message: 'Password is valid' });
      return next(false);
    }).catch(err => {
      if (err instanceof restify.ConflictError) {
        return next(err);
      }
      req.log.error('Error occurred:', err);
      return next(new restify.InternalServerError('An error occurred'));
    }),

  verifyEmail: (req, res, next) =>
    co(function* execute() {
      const { token } = req.body;
      const type = constants.emailTokenType.VERIFY_EMAIL;

      // Find the existing user for the token
      const query = `
                SELECT
                    u.id,
                    ( u.email_verified IS NOT NULL ) AS email_verified
                FROM
                    email_token t
                    JOIN ics_user u ON u.id = t.user_id
                WHERE
                    t.value = $1 AND
                    t.type = $2 AND
                    t.expires > NOW();
            `;

      const user = yield server.db.read.row(query, [token, type]);
      if (!user || !user.id) {
        // User not found - token invalid
        return next(
          new restify.NotFoundError('Email token was not found or expired')
        ); //
      }

      const conn = yield server.db.write.getConnection();
      try {
        yield conn.execute('BEGIN');

        // If the email is not verified, then verify it
        if (!user.emailVerified) yield updateUserEmailVerified(conn, user.id);

        // Delete the token so it cannot be reused
        yield conn.execute(
          `
                    DELETE FROM email_token
                    WHERE
                        value = $1 AND
                        type = $2;
                `,
          [token, type]
        );

        yield conn.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield conn.execute('ROLLBACK');
        return next(new restify.InternalServerError(err));
      } finally {
        conn.done();
      }

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  exists: (req, res, next) =>
    co(function* execute() {
      const { token } = req.params;
      const { type } = req.params;
      // eslint-disable-next-line no-unused-expressions
      (yield emailTokenHelper.emailTokenExists(token, type))
        ? res.send(204)
        : res.send(404);

      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
