const co = require('co');
const restify = require('restify');

const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');
const paginationHelper = require('../helpers/pagination-helper');

module.exports = {
  getReleases: (req, res, next) =>
    co(function* execute() {
      const pageParams = paginationHelper.parsePaginationParams(req);

      const results = yield server.db.read.rows(
        `
                SELECT
                    release,
                    release_date,
                    title,
                    description,
                    updated_by,
                    date_created,
                    date_updated
                FROM ics_releases
                WHERE visible = TRUE
                ORDER BY release DESC
                LIMIT $1
                OFFSET $2;
            `,
        [pageParams.pageSize, pageParams.offset]
      );

      const totalObj = yield server.db.read.row(
        'SELECT COUNT(1) FROM ics_releases WHERE visible = TRUE;',
        []
      );

      res.send({
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results,
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getReleaseById: (req, res, next) =>
    co(function* execute() {
      const { release } = req.params;

      const result = yield server.db.read.row(
        `
                SELECT release,
                    release_date,
                    title,
                    description,
                    updated_by,
                    date_created,
                    date_updated
                FROM ics_releases
                WHERE
                    release = $1 AND
                    visible = TRUE;
            `,
        [release]
      );

      if (!result) {
        return next(new restify.errors.NotFoundError('Release not found'));
      }

      res.send(result);

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  searchReleases: (req, res, next) =>
    co(function* execute() {
      const pageParams = paginationHelper.parsePaginationParams(req);

      const { release } = req.query;
      const { title } = req.query;
      const { description } = req.query;
      const { updatedBy } = req.query;

      let where = '';
      let index = 0;
      const params = [];

      if (release) {
        where = `${where} release LIKE $${(index += 1)}`;
        params.push(`%${release}%`);
      }

      if (title) {
        if (params.length) {
          where = `${where} OR`;
        }
        where += ` ${where} LOWER(title) LIKE $${(index += 1)}`;
        params.push(`%${title.toLowerCase()}%`);
      }

      if (description) {
        if (params.length) {
          where = `${where} OR`;
        }
        where += ` ${where} LOWER(description) LIKE $${(index += 1)}`;
        params.push(`%${description.toLowerCase()}%`);
      }

      if (updatedBy) {
        if (params.length) {
          where = `${where} OR`;
        }
        where += ` ${where} LOWER(updated_by) LIKE $${(index += 1)}`;
        params.push(`%${updatedBy.toLowerCase()}%`);
      }

      if (params.length) {
        where = `AND (${where})`;
      }

      const results = yield server.db.read.rows(
        `
                SELECT
                    release,
                    release_date,
                    title,
                    description,
                    updated_by,
                    date_created,
                    date_updated
                FROM ics_releases
                WHERE
                    visible = TRUE
                    ${where}
                ORDER BY release DESC
                LIMIT $${(index += 1)}
                OFFSET $${(index += 1)};
            `,
        [...params, pageParams.pageSize, pageParams.offset]
      );

      const totalObj = yield server.db.read.row(
        `
                SELECT COUNT(1)
                FROM ics_releases
                WHERE
                    visible = TRUE
                    ${where};
            `,
        params
      );

      res.send({
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results,
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
