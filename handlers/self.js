const restify = require('restify');
const co = require('co');

const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');
const hasher = require('../lib/hash-password');
const totp = require('../lib/totp');
const usersHelper = require('../helpers/users-helper');
const userChangeHelper = require('../helpers/user-change-helper');
const passwordHelper = require('../helpers/password-helper');

module.exports = {
  /**
   * Function to get the logged in user details. User is obtained from the token
   */
  self: (req, res, next) =>
    co(function* execute() {
      // Get the user from the db
      const user = (yield usersHelper.getFullUser([req.user.sub]))[0];

      if (!user) {
        req.log.warn(`User ${req.user.sub} not found`);
        return next(new restify.NotFoundError('User not found'));
      }
      // Remove the unneeded fields
      delete user.status;

      // Return the user object
      res.send(user);

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Function to change a password of a logged in user.
   */
  password: (req, res, next) =>
    co(function* execute() {
      const creds = req.body;
      const userId = req.user.sub;

      const mfaSecret = yield usersHelper.getUserMfaSecretById(userId);

      // If user can reset then validate with the mfa
      if (!(yield totp.validateTotp(req, mfaSecret, creds.mfaCode))) {
        req.log.info('MFA token did not match');
        return next(new restify.ForbiddenError('Invalid token')); // 403
      }

      // Validate new password
      if (!(yield passwordHelper.isPasswordAcceptable(req, creds.password))) {
        req.log.info('Password requirements not met');
        return next(new restify.ConflictError('Password requirements not met')); // 409
      }
      req.log.info('Valid password reset, attempting update in database');

      // Get a connection for transaction control
      const connection = yield server.db.write.getConnection();
      try {
        // Start transaction
        yield connection.execute('BEGIN');

        // Get the old password hash
        const oldPassHash = yield usersHelper.getUserPasswordHashById(userId);

        // Hash the new password
        const passwordHash = yield hasher.createHash(creds.password);

        // Update the new password in the database
        yield usersHelper.updateUserPassword(connection, userId, passwordHash);

        // Update the user change tables to show a password change
        yield userChangeHelper.recordUserChange(
          userId,
          'User Change Password',
          userId,
          [
            {
              fieldName: 'password_hash',
              fromValue: oldPassHash,
              toValue: passwordHash,
            },
          ],
          connection
        );

        // Commit transaction
        yield connection.execute('COMMIT');
        req.log.info(`Password reset saved to database for user ${userId}`);
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        return next(new restify.InternalServerError(err));
      } finally {
        connection.done();
      }

      req.log.info('Valid password reset successfully');

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  passwordReset: (req, res, next) =>
    co(function* execute() {
      const creds = req.body;
      const userId = req.user.sub;
      yield passwordHelper.validatePassword(req, userId, creds.password);
      res.send(200, { message: 'Password is valid' });
      return next(false);
    }).catch(err => {
      if (err instanceof restify.ConflictError) {
        return next(err);
      }
      req.log.error('Error occurred:', err);
      return next(new restify.InternalServerError('An error occurred'));
    }),
};
