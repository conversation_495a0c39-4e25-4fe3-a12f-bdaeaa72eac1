const restify = require('restify');

const errorHandler = require('../../lib/errorhandler');
const personaService = require('./persona.service');

/**
 * Return list of languages for a company
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
const getPersonaListByCompany = async (req, res, next) => {
  const companyId = req.user.company.id;
  try {
    if (!companyId) {
      return next(new restify.errors.BadRequestError('Company id is null'));
    }

    const personaList = await personaService.getPersonaListByCompany(companyId);
    res.send(200, personaList);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

module.exports = {
  getPersonaListByCompany,
};
