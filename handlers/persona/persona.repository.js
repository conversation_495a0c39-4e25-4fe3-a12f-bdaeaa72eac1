const { server } = require('../../app');

/**
 * Retrieves the list of personas for a given company.
 * @param {UUID} companyId
 * @returns {Promise<Array<{tenant_id: string, persona: string, roles: string[]}>>}
 */
const getPersonaListByCompany = async companyId => {
  const personaListResult = await server.db.read.rows(
    `
         SELECT 
              p.name,
              ARRAY_AGG(DISTINCT r.role_name) AS roles
          FROM persona p
          LEFT JOIN persona_role pr ON pr.persona_id = p.id
          LEFT JOIN role r ON pr.role_id = r.role_id
          LEFT JOIN persona_tenant pt
              ON pt.persona_id = p.id AND p.is_applicable_to_all_tenants = false
          WHERE
              p.is_applicable_to_all_tenants = true OR pt.persona_id IS NOT null
              and pt.tenant_id = $1
          GROUP BY p.name;`,
    [companyId]
  );

  let personaList = { tenantId: companyId };

  if (!personaListResult || personaListResult.length === 0) {
    personaList = {
      ...personaList,
      personas: [{ persona: 'Custom', roles: [] }],
    };
  } else {
    personaList = {
      ...personaList,
      personas: personaListResult.map(row => {
        let roles = [];
        if (row.roles && row.roles.length > 0 && row.roles[0] !== null) {
          roles = row.roles;
        }
        return {
          persona: row.name,
          roles,
        };
      }),
    };
  }

  if (
    personaList.personas.find(persona => persona.persona === 'Custom') ===
    undefined
  ) {
    personaList.personas.push({ persona: 'Custom', roles: [] });
  }

  return personaList;
};

module.exports = {
  getPersonaListByCompany,
};
