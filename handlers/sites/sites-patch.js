const restify = require('restify');
const restifyErrors = require('restify-errors');
const { server } = require('../../app');
const sitesHelper = require('../../helpers/sites-helper');
const {
  getSiteAuthSiteGroupIds,
  submitSiteGroupUpdateEventEvent,
} = require('../sitegroups');
const { sendEventOnUpdateSite } = require('./sites-replication-events');

const patchSiteById = async (req, res, next) => {
  try {
    const { site } = req.body;
    const siteId = req.params.id;
    const { user } = req;
    const userId = user.sub;
    const companyId = user.company.id;
    const currentSite = await server.db.read.row(
      `
          SELECT s.*, kg.key_group_ref
          FROM site s
          INNER JOIN user_site_authorization u ON u.site_id = s.site_id AND u.user_id = $2
          LEFT JOIN key_group kg ON kg.key_group_id = s.key_group_id
          WHERE s.site_id = $1 AND s.active = true
          ;`,
      [siteId, userId]
    );
    if (!currentSite) {
      return next(new restify.NotFoundError('Site not found'));
    }
    /*
     * Site with same name already exists check
     */
    let similarSite = null;
    if (currentSite.name !== site.name) {
      similarSite = await server.db.read.row(
        `
          SELECT site_id
          FROM site
          WHERE lower(name) = $1 AND company_id = $2 AND active = TRUE
        `,
        [site.name.toLowerCase(), currentSite.companyId]
      ); // should be based on the origin company
      if (similarSite) {
        req.log.info('Site with same name already exists');
        return next(new restify.BadRequestError('Site with same name exists'));
      }
    }
    const authorizationSiteGroups = await getSiteAuthSiteGroupIds(siteId);
    const connection = await server.db.write.getConnection();
    try {
      await connection.execute('BEGIN');
      const sqlQuery = `
          UPDATE site
          SET name = $1
          WHERE site_id = $2
      `;
      await connection.execute(sqlQuery, [site.name, siteId]);
      await connection.execute('COMMIT');
    } catch (e) {
      await connection.execute('ROLLBACK');
    } finally {
      await connection.done();
    }
    const updatedSite = await sitesHelper.getSiteById(
      siteId,
      userId,
      companyId
    );
    await sendEventOnUpdateSite(siteId);
    if (authorizationSiteGroups.length) {
      await submitSiteGroupUpdateEventEvent(
        authorizationSiteGroups,
        site.companyId
      );
    }
    return res.send(updatedSite);
  } catch (e) {
    req.log.info(e);
    return next(new restifyErrors.BadRequestError('Error in updateSiteName'));
  }
};
module.exports = {
  patchSiteById,
};
