const restify = require('restify');
const co = require('co');
const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const sitesHelper = require('../../helpers/sites-helper');
const usersHelper = require('../../helpers/users-helper');
const rkiHelper = require('../../helpers/rki-helper');
const siteTagsHelper = require('../../helpers/site-tags-helper');
const deviceHelper = require('../../helpers/device-helper');
const totp = require('../../lib/totp');
const {
  getSiteExternalReferences,
  updateSiteExternalReferences,
  insertSiteExternalReferences,
  sendEventsToEventBridgeOnSiteUpdate,
  resolveExternalReferenceType,
} = require('../../helpers/site-external-references-helper');
const logger = require('../../lib/logger').mainLogger();
const { ACTIONS } = require('../../lib/app-constants');
const {
  getSiteAuthSiteGroupIds,
  submitSiteGroupUpdateEventEvent,
} = require('../sitegroups');
const { processLocationEvent } = require('../../helpers/bluefin-helper');
const { sendEventOnUpdateSite } = require('./sites-replication-events');

module.exports = {
  putSiteById: (req, res, next) =>
    co(function* execute() {
      const { site, deploymentType, meta } = req.body;
      const siteId = req.params.id;
      const { user } = req;
      const userId = user.sub;
      const companyId = user.company.id;

      if (site.timezoneId && site.timezoneId !== '') {
        let validTimezones = yield server.db.read.rows(
          'SELECT name FROM pg_timezone_names()'
        );
        validTimezones = validTimezones.map(r => r.name);
        if (!validTimezones.includes(site.timezoneId)) {
          return next(new restify.BadRequestError('Invalid timezoneId'));
        }
      }

      // Check if site exists and user is authorized to access it
      // ICS-2187 user in relationship should be able to update the site
      const currentSite = yield server.db.read.row(
        `
                SELECT s.*, kg.key_group_ref, sef.reference_id as gvr_site_id
                FROM site s
                INNER JOIN user_site_authorization u ON u.site_id = s.site_id AND u.user_id = $2
                LEFT JOIN key_group kg ON kg.key_group_id = s.key_group_id
                LEFT JOIN site_external_references sef on s.site_id = sef.site_id AND sef.reference_type = 'GVR' AND sef.deleted = false
                WHERE s.site_id = $1 AND s.active = true
              ;`,
        [siteId, userId]
      );

      if (!currentSite) {
        return next(new restify.NotFoundError('Site not found'));
      }

      req.log.info(`Site current key group - ${currentSite.keyGroupId}`);

      let similarSite = null;

      /*
       * Site with same name already exists check
       */
      if (currentSite.name !== site.name) {
        similarSite = yield server.db.read.row(
          `
                    SELECT site_id
                    FROM site
                    WHERE lower(name) = $1 AND company_id = $2 AND active = TRUE
                  `,
          [site.name.toLowerCase(), currentSite.companyId]
        ); // should be based on the origin company
        if (similarSite) {
          req.log.info('Site with same name already exists');
          return next(
            new restify.BadRequestError('Site with same name exists')
          );
        }
      }

      /*
       *  Separate check for referenceId, than above, as another site
       *  with same name and same referenceId could exist
       */
      if (
        site.referenceId !== null &&
        currentSite.referenceId !== site.referenceId
      ) {
        // ICS-1859 Check reference Id with company id
        similarSite = yield server.db.read.row(
          `
                    SELECT site_id
                    FROM site
                    WHERE reference_id = $1 AND
                        company_id = $2 AND active = true;
                `,
          [site.referenceId, currentSite.companyId]
        );
        if (similarSite) {
          req.log.info('Site with same reference id already exists');
          return next(
            new restify.BadRequestError('Site with same reference id exists')
          );
        }
      }

      /*
       *  Check if any of the site external references already exist
       */
      if (site.externalReferences && site.externalReferences.length) {
        const references = yield getSiteExternalReferences(
          siteId,
          site.externalReferences
        );

        if (references.length) {
          return next(
            new restify.BadRequestError(
              `Site with the references exist ${references
                .map(row => row.referenceType)
                .join(',')}`
            )
          );
        }
      }

      // sitegroup isn't sent during update site

      /*
       * Check for MFA and RKI role
       * if keygroup id is sent & is different than previous, even if it's null
       */
      if (site.keyGroupId && site.keyGroupId !== currentSite.keyGroupId) {
        // User must have RKI role
        if (user.hasRole('RKI')) {
          const mfaSecret = yield usersHelper.getUserMfaSecretById(userId);
          // Validate the MFA
          if (!(yield totp.validateTotp(req, mfaSecret, site.mfaCode))) {
            req.log.info('MFA is not valid');
            return next(new restify.NotAcceptableError('MFA is not valid'));
          }
        } else {
          req.log.info(
            'Only users with RKI role are allowed to assign keygroup'
          );
          return next(
            new restify.NotAcceptableError(
              'Only users with RKI role are allowed to assign keygroup'
            )
          );
        }

        /*
         * If keygroup is not null, check
         * keygroup exists & must be from the same company as site
         */
        if (site.keyGroupId) {
          const keyGroupsCompany = yield server.db.read.row(
            `
                        SELECT company_id
                        FROM key_group
                        WHERE key_group_id = $1
                    `,
            [site.keyGroupId]
          );

          if (keyGroupsCompany.companyId !== user.company.id) {
            return next(
              new restify.ForbiddenError('Unable to access keygroup')
            );
          }
        }
      }
      const authorizationSiteGroups = yield getSiteAuthSiteGroupIds(siteId);

      try {
        // Site update
        const connection = yield server.db.write.getConnection();

        try {
          yield connection.execute('BEGIN');

          const suppressOffhoursAlarmValue =
            site.suppressOffhoursAlarm === undefined
              ? false
              : site.suppressOffhoursAlarm;

          const params = [
            site.address,
            site.contactEmail,
            site.contactPhone,
            site.formattedAddress,
            site.latitude,
            site.longitude,
            site.name,
            site.referenceId,
            site.timezoneId,
            site.keyGroupId,
            site.visible,
            suppressOffhoursAlarmValue,
            siteId,
            site.disableCmAutomaticDeployments !== undefined
              ? site.disableCmAutomaticDeployments
              : false,
            site.disableFileDownloads !== undefined
              ? site.disableFileDownloads
              : false,
          ];

          if (site.hours) {
            params.push(JSON.stringify(site.hours));
          }

          yield connection.execute(
            `
                        UPDATE site
                        SET address_json = $1,
                            contact_email = $2,
                            phone_number = $3,
                            formatted_address = $4,
                            latitude = $5,
                            longitude = $6,
                            name = $7,
                            reference_id = $8,
                            timezone_id = $9,
                            key_group_id = $10,
                            visible = $11,
                            suppress_offhours_alarm = $12,
                            disable_cm_automatic_deployment = $14,
                            disable_software_downloads = $15
                            ${site.hours ? ', hours = $16' : ''}
                        WHERE site_id = $13
                        `,
            params
          );

          yield siteTagsHelper.updateSiteTags({
            companyId: user.company.id,
            siteId,
            tags: site.tags,
            deploymentType,
            connection,
            userId,
            meta,
          });

          /*
           * Update Site External References
           */
          yield updateSiteExternalReferences(
            siteId,
            userId,
            connection,
            companyId
          );
          if (site.externalReferences) {
            yield insertSiteExternalReferences(
              siteId,
              site.externalReferences,
              userId,
              connection,
              companyId
            );
          }
          // Commit transaction
          yield connection.execute('COMMIT');
          const externalSiteReferencesUpdated = resolveExternalReferenceType(
            site.externalReferences
          );
          if (externalSiteReferencesUpdated) {
            yield sendEventsToEventBridgeOnSiteUpdate(
              siteId,
              externalSiteReferencesUpdated.referenceId,
              companyId,
              ACTIONS.UPDATE,
              externalSiteReferencesUpdated.referenceType
            );
          }
        } catch (err) {
          req.log.error({
            err,
          });
          yield connection.execute('ROLLBACK');
          throw err;
        } finally {
          connection.done();
        }

        try {
          const devicesToRKI = [];

          const keyGroup = yield server.db.read.row(
            `
                            SELECT key_group_ref
                            FROM key_group
                            where key_group_id = $1
                            ;`,
            [site.keyGroupId]
          );

          site.keyGroupRef = keyGroup.keyGroupRef;

          if (
            site.keyGroupRef &&
            site.keyGroupRef !== currentSite.keyGroupRef
          ) {
            const targets = yield server.db.read.rows(
              `
                                SELECT target_id,
                                     key_group_ref,
                                     active
                                FROM target
                                WHERE site_id = $1 AND active = true;`,
              [siteId]
            );

            yield Promise.all(
              targets.map(target =>
                deviceHelper
                  .cleanUpKeyRequestDevice({ id: target.targetId }, keyGroup)
                  .then(results => {
                    let isRkiRequired =
                      target.keyGroupRef !== keyGroup.keyGroupRef;
                    if (results.pending.length) {
                      isRkiRequired = false;
                    }
                    if (isRkiRequired) {
                      devicesToRKI.push(target.targetId);
                    }
                  })
                  .catch(err => {
                    throw err;
                  })
              )
            );

            if (devicesToRKI.length) {
              yield rkiHelper.createAutomaticRkiRequestSession(
                userId,
                devicesToRKI,
                siteId,
                rkiHelper.RKI_REASON.KEYGROUP_CHANGE
              );
            }
          }
        } catch (e) {
          logger.error(
            'rkiHelper.createAutomaticRkiRequestSession() Error: %s',
            e
          );
        }
      } catch (err) {
        return next(new restify.InternalServerError(err));
      }

      const updatedSite = yield sitesHelper.getSiteById(
        siteId,
        userId,
        companyId
      );

      yield sendEventOnUpdateSite(siteId);

      if (authorizationSiteGroups.length) {
        yield submitSiteGroupUpdateEventEvent(
          authorizationSiteGroups,
          site.companyId
        );
      }

      yield processLocationEvent(
        updatedSite,
        'updateLocation',
        currentSite.gvrSiteId
      );

      res.send(updatedSite);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
