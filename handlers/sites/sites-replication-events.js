const AWS = require('../../lib/aws');
const env = require('../../env');
const { eventBusSources } = require('../../lib/app-constants');
const logger = require('../../lib/logger').mainLogger();
const { server } = require('../../app');

const mapToEventData = site =>
  Object.fromEntries(
    Object.entries(site).map(([k, v]) => {
      switch (k) {
        case 'deleteTimestamp':
          return ['isDeleted', !!site[k]];
        default:
          return [k, v];
      }
    })
  );
const isSiteReplication = () =>
  env.config.AWS.eventBus && env.config.AWS.eventBus.fuelPriceService;

const getSite = async siteId =>
  // eslint-disable-next-line no-return-await
  await server.db.read.row(
    `
    SELECT 
      s.site_id, 
      s.name as site_name, 
      s.company_id as tenant_id,
      c.name as tenant_name, 
      s.active as is_active, 
      s.delete_timestamp, 
      s.date_updated as updated_date,
      s.created as created_date
    FROM site s
    LEFT OUTER JOIN company c ON c.id = s.company_id
    WHERE site_id = $1;
    `,
    [siteId]
  );

const sendEventOnUpdateSite = async siteId => {
  if (!isSiteReplication()) return;
  const site = await getSite(siteId);
  logger.info(
    { site, env: env.config.AWS.eventBus },
    '[site-replication].sendEventOnUpdateSite.start'
  );
  if (site) {
    const eventBridgeParams = {
      data: mapToEventData(site),
      detailType:
        env.config.AWS.eventBus.fuelPriceService.rules.updateSite ||
        'fps_update_sites',
      source: eventBusSources.SITE_REPLICATION,
    };

    const res = await AWS.sendEventsToEventBridge(eventBridgeParams);
    logger.info(
      { eventResponse: res },
      '[site-replication].sendEventOnUpdateSite.completed'
    );
  }
};

const sendEventOnDeleteSite = async deletedSiteId => {
  if (!isSiteReplication()) return;
  const deletedSite = await getSite(deletedSiteId);
  logger.info(
    { deletedSite, env: env.config.AWS.eventBus },
    '[site-replication].sendEventOnDeleteSite.start'
  );

  if (deletedSite) {
    const eventBridgeParams = {
      data: mapToEventData(deletedSite),
      detailType:
        env.config.AWS.eventBus.fuelPriceService.rules.updateSite ||
        'fps_update_sites',
      source: eventBusSources.SITE_REPLICATION,
    };

    const res = await AWS.sendEventsToEventBridge(eventBridgeParams);
    logger.info(
      { eventResponse: res },
      '[site-replication].sendEventOnDeleteSite.complete'
    );
  }
};

module.exports = {
  sendEventOnUpdateSite,
  sendEventOnDeleteSite,
  mapToEventData,
};
