const co = require('co');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');
const siteHelper = require('../../helpers/sites-helper');

module.exports = {
  getAllSites: (req, res, next) =>
    co(function* execute() {
      const { companyId } = req.query;
      const paginationParams = paginationHelper.parsePaginationParams(req);

      const params = [];
      let whereBlock = `
                WHERE
                    s.active
            `;
      if (companyId) {
        params.push(companyId);
        whereBlock = `${whereBlock}
                    AND s.company_id = ANY( $${params.length} )
                `;
      }

      const withBlock = `
                    WITH company AS (
                      SELECT
                          id,
                          name
                      FROM company
                    ),
                    key_group AS (
                      SELECT
                          key_group_id              AS "id",
                          key_group_name            AS "name",
                          key_group_ref             AS "ref",
                          company_id,
                          row_to_json( c.* )::jsonb AS "owner"
                      FROM key_group
                      JOIN company c ON c.id = key_group.company_id
                      GROUP BY c.*, key_group_id, company_id
                    ),
                    tag AS (
                      SELECT
                          id,
                          name,
                          company_id AS "companyId"
                      FROM tag
                    )
                  `;

      const fromBlock = `
                FROM site s
                    JOIN company c                  ON s.company_id = c.id
                    LEFT JOIN site_tag st           ON st.site_id = s.site_id AND st.deleted = false
                    LEFT JOIN tag t                 ON t.id = st.tag_id 
                    LEFT JOIN key_group k           ON k.id = s.key_group_id
            `;

      const totalObj = yield server.db.read.row(
        `
                    ${withBlock}
                    SELECT COUNT(DISTINCT( s.* ))
                    ${fromBlock}
                    ${whereBlock};
                `,
        params
      );

      const selectBlock = `
                SELECT
                    s.address_json                      AS "address",
                    s.contact_email,
                    s.phone_number                      AS "contactPhone",
                    s.formatted_address,
                    s.site_id                           AS "id",
                    s.latitude::FLOAT,
                    s.longitude::FLOAT,
                    s.name,
                    s.reference_id,
                    get_site_health( s.site_id ) as status,
                    s.timezone_id,
                    s.visible,
                    row_to_json( c.* )                  AS "owner",
                    CASE 
                        WHEN COUNT(k) = 0
                        THEN NULL
                        ELSE json_build_object( 
                            'id' , k.id, 
                            'name', k.name, 
                            'owner', k.owner, 
                            'ref', k.ref
                        ) END                           AS "keyGroup",
                    json_agg( DISTINCT t.* )            AS "tags"
              `;
      const getSitesQuery = `
                    ${withBlock}
                    ${selectBlock}
                    ${fromBlock}
                    ${whereBlock}
                    GROUP BY s.site_id, c.*, k.id, k.name, k.owner, k.ref
                    ORDER BY s.name
                    LIMIT ${paginationParams.pageSize}
                    OFFSET ${paginationParams.offset};
                  `;

      const results = yield server.db.read.rows(getSitesQuery, params);
      if (results && results.length) {
        results.forEach(r => {
          // eslint-disable-next-line no-param-reassign
          r.status = siteHelper.translateSiteHealthToNumber(r.status);
        });
      }

      res.send(200, {
        results,
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: paginationParams.pageIndex,
          pageSize: paginationParams.pageSize,
        },
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
