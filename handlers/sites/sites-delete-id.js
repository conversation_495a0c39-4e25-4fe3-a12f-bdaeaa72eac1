const restify = require('restify');
const { server } = require('../../app');
const {
  submitSiteGroupUpdateEventEvent,
  getSiteAuthSiteGroupIds,
} = require('../sitegroups');
const { refreshMVUserSiteAuthorization } = require('../../helpers/mv-helper');
const { sendEventOnDeleteSite } = require('./sites-replication-events');

const getSite = async (siteId, userId) =>
  // eslint-disable-next-line no-return-await
  await server.db.read.row(
    `
              SELECT s.site_id, s.company_id, count( t.target_id ) AS "targetCount"
              FROM site s
              LEFT OUTER JOIN target t ON s.site_id = t.site_id AND t.active = TRUE
              JOIN user_site_authorization usa ON usa.site_id = s.site_id AND usa.user_id = $2
              WHERE s.site_id = $1 AND s.active = TRUE
              GROUP BY s.site_id;
          `,
    [siteId, userId]
  );

module.exports = {
  deleteSiteById: async (req, res, next) => {
    const siteId = req.params.id;
    const userId = req.user.sub;

    const site = await getSite(siteId, userId);

    res.resourceOwner = site ? site.companyId : null; // eslint-disable-line no-param-reassign

    if (!site || !site.siteId) {
      return next(new restify.NotFoundError('Site not found')); // 404
    }

    if (site.targetCount) {
      return next(new restify.NotAcceptableError('Site not empty')); // 406
    }

    // ICS-2214 Cannot delete default site
    const defaultSite = await server.db.read.row(
      `
                SELECT default_site, name
                FROM company
                WHERE default_site = $1;
                `,
      [siteId]
    );

    if (defaultSite) {
      return next(
        new restify.NotAcceptableError(
          `Cannot delete company ${defaultSite.name} default site`
        )
      );
    }

    const authorizationSiteGroups = await getSiteAuthSiteGroupIds(siteId);

    const connection = await server.db.write.getConnection();

    try {
      await connection.execute('BEGIN');

      // Remove authorization
      await connection.execute(
        `
                  DELETE FROM authorization_site_group_site
                  WHERE site_id = $1
                ;`,
        [siteId]
      );

      // Set site active to false
      await connection.execute(
        `
                  UPDATE site
                  SET active = false,
                      delete_timestamp = NOW(),
                      date_updated = NOW()
                  WHERE site_id = $1
              ;`,
        [siteId]
      );

      // delete all references to deleted site in ics_alarms schema
      const deleteResult = await connection.execute(
        `
                  DELETE FROM ics_alarm.alarm_subject
                  WHERE subject_id = $1
                ;`,
        [siteId]
      );

      if (deleteResult && deleteResult.rowCount > 0) {
        // disable all alarms that don't have subscription subjects anymore after site is deleted
        await connection.execute(`
                      UPDATE ics_alarm.alarm a SET active = false
                      WHERE ( SELECT COUNT(1) FROM ics_alarm.alarm_subject asb WHERE asb.alarm_id = a.id ) = 0
                    ;`);
      }
      await refreshMVUserSiteAuthorization('deleteSite');
      // Commit transaction
      await connection.execute('COMMIT');
      await sendEventOnDeleteSite(siteId);
      connection.done();
    } catch (err) {
      req.log.error({ err });
      await connection.execute('ROLLBACK');
      connection.done();
      return next(new restify.InternalServerError(err));
    }

    if (authorizationSiteGroups.length) {
      await submitSiteGroupUpdateEventEvent(
        authorizationSiteGroups,
        site.companyId
      );
    }

    res.send(204);
    return next();
  },
};
