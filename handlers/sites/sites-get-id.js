const co = require('co');
const restify = require('restify');

const sitesHelper = require('../../helpers/sites-helper');
const deviceHelper = require('../../helpers/device-helper');
const errorHandler = require('../../lib/errorhandler');
const { server } = require('../../app');
const { enumToScreenSize } = require('../../helpers/screensize-enum.json');

function getLimitClause(req) {
  const pageSize =
    req.query.pageSize > 0 ? parseInt(req.query.pageSize, 10) : 0;
  if (pageSize > 0) {
    const pageIndex = req.query.pageIndex
      ? parseInt(req.query.pageIndex, 10)
      : 0;
    const offset = pageSize * pageIndex;
    return `LIMIT ${pageSize} OFFSET ${offset}`;
  }
  return '';
}

module.exports = {
  getSiteById: (req, res, next) =>
    co(function* execute() {
      const siteId = req.params.id;
      const userId = req.user.sub;
      const companyId = req.user.company.id;

      const site = yield sitesHelper.getSiteById(siteId, userId, companyId);
      if (!site) {
        return next(new restify.NotFoundError('Site not found')); // 404
      }
      res.send(site);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getDevicesForSite: (req, res, next) =>
    co(function* execute() {
      const siteId = req.params.id;
      const userId = req.user.sub;
      const companyId = req.user.company.id;
      const { featureFlags } = req.user.company || {};
      // Pagination support
      const limitClause = getLimitClause(req);
      const site = yield server.db.read.row(
        `
                    SELECT
                        s.site_id
                    FROM site s
                        JOIN user_site_authorization usa ON s.site_id = usa.site_id
                    WHERE 
                    s.site_id = $1 AND 
                    s.active AND 
                    usa.user_id = $2;
                 `,
        [siteId, userId]
      );

      if (!site) {
        return next(new restify.NotFoundError('Site not found'));
      }

      const devices = yield server.db.read.rows(
        `
                SELECT
                    t.target_id                     AS id, t.site_id,
                    s.name                          AS site_name,
                    s.key_group_id                  AS site_keygroup,                    
                    t.last_registered,
                    t.last_contact,
                    t.name,
                    CASE
                        WHEN t.real_ip_address IS NOT NULL THEN t.real_ip_address
                        ELSE t.ip_address
                    END                             AS ip_address,
                    t.gateway_address,
                    t.description,
                    t.serial_number,
                    kg.key_group_id ,
                    t.key_group_ref,
                    t.presence,
                    get_device_health( t.target_id ) AS status,
                    t.release_version               AS release_version,
                    json_build_object(
                        'id', t.device_type,
                        'name', cpt.display_name,
                        'screenSize', t.screen_size
                    )                               AS device_type,
                    drkb.key_certs IS NOT NULL      AS has_RKI,
                    ds.states                       AS device_states,
                    (
                        SELECT COALESCE(dh.timestamp, rah.triggered_timestamp)
                        FROM report_alarm_history rah
                        LEFT JOIN ics_state._device_health dh
                        ON rah.device_id = dh.device_id
                        WHERE rah.device_id = t.target_id
                        AND rah.alarm_type = (
                            CASE GET_DEVICE_HEALTH(t.target_id)
                            WHEN 'OUT_OF_SERVICE' THEN 'device_out_of_service'
                            WHEN 'UNKNOWN' THEN 'device_unreachable' END
                        )
                        AND rah.remedy_timestamp IS NULL
                        AND dh.is_critical
                        ORDER BY rah.triggered_timestamp DESC NULLS LAST
                        LIMIT 1
                    ) AS status_alarm_ts,
                    (
                        SELECT JSON_AGG(
                            JSON_BUILD_OBJECT(
                                'state', dh.name,
                                'value', dh.value
                            ) )
                        FROM ics_state._device_health dh
                        WHERE t.target_id = dh.device_id
                        AND t.active
                        AND get_device_health(t.target_id) = 'OUT_OF_SERVICE'
                        AND dh.name != 'heartbeat'
                        AND dh.is_critical
                    ) as oos_conditions
                FROM target t
                    JOIN site s ON t.site_id = s.site_id
                    JOIN company c ON s.company_id = c.id
                    LEFT JOIN key_group kg on s.company_id=kg.company_id and t.key_group_ref=kg.key_group_ref
                    LEFT JOIN company_product_type cpt ON t.device_type = cpt.device_type and cpt.company = $1
                    LEFT JOIN device_rki_key_bundle drkb ON drkb.device_id = t.target_id AND
                        drkb.date_created = (SELECT MAX(date_created) FROM device_rki_key_bundle WHERE device_id = t.target_id)                
                    LEFT JOIN (
                        SELECT dst.device_id, json_object_agg(dst.state, dst.value) AS states
                        FROM device_states dst
                        WHERE dst.site_id = $2
                        GROUP BY dst.device_id
                    ) ds ON t.target_id = ds.device_id
                WHERE
                    t.site_id = $2 AND
                    t.active = true
                ORDER BY status desc, t.name
                ${limitClause};
             `,
        [req.user.company.id, siteId]
      );

      for (let i = 0; i < devices.length; i++) {
        const device = devices[i];
        device.deviceType.screenSize =
          enumToScreenSize[device.deviceType.screenSize] || null;

        // Check company product type display name
        if (!device.deviceType.name) {
          device.deviceType.name = device.deviceType.id;
        }

        device.statusStr = device.status;
        device.status = deviceHelper.translateDeviceHealthToNumber(
          device.status
        );

        const masterDevice = device.deviceStates
          ? device.deviceStates['invenco.system.client.invenco-icp-peer-master']
          : null;
        device.isMaster = device.serialNumber === masterDevice;

        const terminalId1 =
          (device.deviceStates &&
            device.deviceStates['invenco.system.cfg.net-terminal-id']) ||
          null;
        const terminalId2 =
          (device.deviceStates &&
            device.deviceStates[
              'invenco.contentplayer.media-player.terminal-id'
            ]) ||
          null;
        device.terminalId = terminalId1 || terminalId2;

        device.isAuxiliary =
          (device.deviceStates &&
            device.deviceStates['invenco.system.cfg.net-terminal-rank'] ===
              'aux') ||
          false;
        device.auxStatus =
          (device.deviceStates &&
            device.deviceStates[
              'invenco.system.client.invenco-emulation-aux-status'
            ]) ||
          null;

        if (device.oosConditions && device.oosConditions.length) {
          const oosCategories = new Map();
          device.oosConditions = device.oosConditions
            .map(item => {
              const match = deviceHelper.getOOSCategoryAndCondition({
                name: item.state,
                value: item.value,
              });
              if (match) {
                const { category, condition } = match;
                // eslint-disable-next-line no-unused-expressions
                !oosCategories.has(category)
                  ? oosCategories.set(category, [condition])
                  : oosCategories.set(category, [
                      ...oosCategories.get(category),
                      condition,
                    ]);

                return { category, condition };
              }
              return undefined;
            })
            .filter(Boolean);
          device.oosCategories = Array.from(oosCategories);
        }

        delete device.deviceStates;
      }
      const devicesWithAppVersions = devices.length
        ? yield sitesHelper.getApplicationVersionOnDevice({
            companyId,
            featureFlags,
            devices,
          })
        : devices;
      const results = yield sitesHelper.getKRSStatusDevice(
        devicesWithAppVersions
      );
      res.send(results);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getSiteByReferenceId: (req, res, next) =>
    co(function* execute() {
      const referenceId = req.params.id;
      const userId = req.user.sub;
      const companyId = req.user.company.id;

      const tmp = yield server.db.read.row(
        `
                    SELECT site_id
                    FROM site s
                    WHERE s.reference_id = $1 AND
                        s.company_id = $2;
                `,
        [referenceId, companyId]
      );
      if (!tmp) {
        return next(new restify.NotFoundError('Site not found')); // 404
      }

      const { siteId } = tmp;
      const site = yield sitesHelper.getSiteById(siteId, userId, companyId);
      if (!site) {
        return next(new restify.NotFoundError('Site not found')); // 404
      }

      res.send(site);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
