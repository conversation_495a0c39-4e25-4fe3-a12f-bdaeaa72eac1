const restify = require('restify');
const appConstants = require('../../lib/app-constants');

const getExternalReferencesTypes = (req, res, next) => {
  const companyId = req.user.company.id;

  if (!companyId) {
    return next(new restify.errors.BadRequestError('Company id is null'));
  }

  res.send(200, {
    allowedExternalReferenceTypes: Object.keys(
      appConstants.sites.ALLOWEDEXTERNALREFERENCETYPES
    ),
  });
  return next();
};

module.exports = {
  getExternalReferencesTypes,
};
