/* eslint-disable max-len */
const moment = require('moment');
const QueryStream = require('pg-query-stream');
const stringify = require('csv-stringify');
const transform = require('stream-transform');
const appConstants = require('../../lib/app-constants');

const { server } = require('../../app');
const siteHelper = require('../../helpers/sites-helper');
const deviceHelper = require('../../helpers/device-helper');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');
const alarmsHelper = require('../../helpers/alarms-helper');
const {
  objectKeysToCamelCase,
} = require('../../src/custom-attributes/custom-attributes.helper');

/**
 * @param {any} item
 * @param {Array<any>} params
 * @returns {string}
 */
function addParam(item, params) {
  params.push(item);
  return `$${params.length}`;
}

function prepareWhere({
  user,
  query,
  tags,
  company,
  companyId,
  statuses,
  deviceStatuses,
  oosFilter,
  alarms,
  siteGroups,
  showHiddenSites,
  tagsForList,
  isForSiteList,
}) {
  const params = [];

  params.push(user);
  const userId = isForSiteList
    ? `AND EXISTS (
            SELECT 1
            FROM user_site_authorization usa
            WHERE usa.site_id = s.site_id
            AND usa.user_id = $${params.length}
        )`
    : `AND u.user_id = $${params.length}`;
  let where = `WHERE s.active ${userId}`;

  // The search will either contain a query or tags or site groups

  if (query && query.length > 0) {
    const q = `%${query.replace(/([%_])/g, '\\$1').toLowerCase()}%`;
    params.push(q);
    where = `
            ${where} AND (LOWER(s.name) LIKE $${params.length}
            OR LOWER(s.phone_number) LIKE $${params.length}
            OR LOWER(s.formatted_address) LIKE $${params.length}
            OR LOWER(s.contact_email) LIKE $${params.length}) `;
  }

  if (tags && tags.length > 0) {
    tags = tags.split(','); // eslint-disable-line
    params.push(tags);
    where = `${where} AND t.name = ANY( $${params.length} ) `;
  }

  if (tagsForList && tagsForList.length > 0) {
    tagsForList = tagsForList.split(','); // eslint-disable-line
    params.push(tagsForList);
    where = `${where} AND EXISTS (
            SELECT 1
            FROM site_tag st
            LEFT JOIN tag t ON t.id = st.tag_id
            WHERE st.site_id = s.site_id
            AND st.deleted = false
            AND t.name = ANY( $${params.length} )`;
    params.push(companyId);
    where = `${where} AND t.company_id = $${params.length} ) `;
  }

  if (siteGroups && siteGroups.length > 0) {
    siteGroups = siteGroups.split(','); // eslint-disable-line
    params.push(siteGroups);
    where = `${where} AND asg.name = ANY( $${params.length} ) `;
  }

  if (company) {
    // Filter sites by query param `company`
    params.push(company);
    where = `${where} AND s.company_id = $${params.length} `;
  }

  if (statuses && Array.isArray(statuses) && statuses.length) {
    params.push(statuses);
    const condition = isForSiteList
      ? `gsh = ANY( $${params.length} )`
      : `get_site_health( s.site_id ) = ANY( $${params.length} )`;
    where = `${where} AND ${condition} `;
  }

  // device status filter
  if (
    deviceStatuses &&
    Array.isArray(deviceStatuses) &&
    deviceStatuses.length
  ) {
    params.push(deviceStatuses);
    const condition = `EXISTS (
          SELECT 1
          FROM target t2
          WHERE t2.active
          AND t2.site_id = s.site_id
          AND get_device_health(t2.target_id) = ANY( $${params.length} )
        )`;
    where = `${where} AND ${condition} `;
  }

  // OOS category filter
  // TODO (OPT Health Phase 2): use exact value/s for invenco.system.g6opt.upc-lasttamper-event instead of != 'none'
  if (oosFilter && Array.isArray(oosFilter) && oosFilter.length) {
    const oosMetrics = deviceHelper.getOOSConditionMetrics(oosFilter);
    const oosCategoryFilters = [];
    let hasG6UPCTamper = false;
    // eslint-disable-next-line no-restricted-syntax
    for (const item of oosMetrics) {
      // eslint-disable-next-line no-restricted-syntax
      for (const [k, v] of Object.entries(item)) {
        if (k !== 'invenco.system.g6opt.upc-lasttamper-event') {
          oosCategoryFilters.push(`( dh.name = '${k}' AND dh.value = '${v}' )`);
        } else {
          hasG6UPCTamper = true;
        }
      }
    }
    if (hasG6UPCTamper) {
      oosCategoryFilters.push(
        "( dh.name = 'invenco.system.g6opt.upc-lasttamper-event' AND dh.value != 'none' )"
      );
    }

    const condition = `AND EXISTS (
            SELECT 1
            FROM target t3
            INNER JOIN ics_state._device_health dh ON t3.target_id = dh.device_id
            WHERE t3.active
            AND t3.site_id = s.site_id
            AND get_device_health(t3.target_id) = 'OUT_OF_SERVICE'
            AND (
                ${oosCategoryFilters.join(' OR ')}
            )
        )`;
    where = `${where} ${oosCategoryFilters ? condition : ''} `;
  }

  if (alarms && alarms.length) {
    const newAlarms = alarmsHelper.updateAlarmParams(alarms);
    params.push(newAlarms);
    where = `${where} AND
            EXISTS (
                SELECT active_alarms.code, active_alarms.site_id, active_alarms.modified
                FROM active_alarms
                WHERE
                    active_alarms.site_id = s.site_id AND
                    active_alarms.code LIKE ANY($${params.length})
            )
        `;
  }

  if (!showHiddenSites) {
    where = `${where} AND s.visible`;
  }

  return { params, where };
}

function prepareAll({
  user,
  query,
  companyId,
  statuses,
  deviceStatuses,
  siteEvents,
  oosFilter,
  showHiddenSites,
  tagsForList,
  searchCustomAttributes,
  limit,
  offset,
  order,
  isCSV,
  applyStrictMatchTags,
}) {
  const params = [];

  const userIdParam = addParam(user, params);

  let hiddenFilter = '';
  if (!showHiddenSites) {
    hiddenFilter = 'WHERE s.visible = true';
  }

  let tagsForListFilter = '';
  if (tagsForList && tagsForList.length > 0) {
    if (applyStrictMatchTags === true || applyStrictMatchTags === 'true') {
      tagsForListFilter = `AND site_id IN (
        SELECT st.site_id
        FROM site_tag st
        LEFT JOIN tag t ON t.id = st.tag_id
        WHERE st.deleted = false
        AND t.name = ANY( ${addParam(tagsForList.split(','), params)} )
        AND t.company_id = ${addParam(companyId, params)}
        GROUP BY st.site_id
        HAVING COUNT(DISTINCT t.name) = ${tagsForList.split(',').length}
    )`;
    } else {
      tagsForListFilter = `AND EXISTS (
        SELECT 1
        FROM site_tag st
        LEFT JOIN tag t ON t.id = st.tag_id
        WHERE st.site_id = usa.site_id
        AND st.deleted = false
        AND t.name = ANY( ${addParam(tagsForList.split(','), params)} )
        AND t.company_id = ${addParam(companyId, params)})`;
    }
  }

  let statusFilter = '';
  if (statuses && Array.isArray(statuses) && statuses.length) {
    statusFilter = `WHERE gsh = ANY( ${addParam(statuses, params)} ) `;
  }

  let deviceStatusFilter = '';
  if (
    deviceStatuses &&
    Array.isArray(deviceStatuses) &&
    deviceStatuses.length
  ) {
    deviceStatusFilter = `AND EXISTS (
          SELECT 1
          FROM target t2
          WHERE t2.active
          AND t2.site_id = usa.site_id
          AND get_device_health(t2.target_id) = ANY( ${addParam(
            deviceStatuses,
            params
          )} )
        ) `;
  }

  // TODO (OPT Health Phase 2): use exact value/s for invenco.system.g6opt.upc-lasttamper-event instead of != 'none'
  let oosCategoryFilter = '';
  if (oosFilter && Array.isArray(oosFilter) && oosFilter.length) {
    const oosMetrics = deviceHelper.getOOSConditionMetrics(oosFilter);
    const oosCategoryFilters = [];
    let hasG6UPCTamper = false;
    // eslint-disable-next-line no-restricted-syntax
    for (const item of oosMetrics) {
      // eslint-disable-next-line no-restricted-syntax
      for (const [k, v] of Object.entries(item)) {
        if (k !== 'invenco.system.g6opt.upc-lasttamper-event') {
          oosCategoryFilters.push(`( dh.name = '${k}' AND dh.value = '${v}' )`);
        } else {
          hasG6UPCTamper = true;
        }
      }
    }
    if (hasG6UPCTamper) {
      oosCategoryFilters.push(
        "( dh.name = 'invenco.system.g6opt.upc-lasttamper-event' AND dh.value != 'none' )"
      );
    }

    if (oosCategoryFilters.length) {
      oosCategoryFilter = `AND EXISTS (
                SELECT 1
                FROM target t3
                INNER JOIN ics_state._device_health dh ON t3.target_id = dh.device_id
                WHERE t3.active
                AND t3.site_id = usa.site_id
                AND get_device_health(t3.target_id) = 'OUT_OF_SERVICE'
                AND (
                    ${oosCategoryFilters.join(' OR ')}
                )
            )`;
    }
  }

  let queryLikeFilter = '';
  let searchableAttributeWithBlock = '';
  let searchableAttributesUnionPortion = '';
  if (query && query.length > 0) {
    const likeMatchParam = addParam(
      `%${query.replace(/([%_])/g, '\\$1').toLowerCase()}%`,
      params
    );
    queryLikeFilter = `
            AND (LOWER(s.name) LIKE ${likeMatchParam}
            OR LOWER(s.phone_number) LIKE ${likeMatchParam}
            OR LOWER(s.formatted_address) LIKE ${likeMatchParam}
            OR LOWER(s.contact_email) LIKE ${likeMatchParam}) `;

    if (searchCustomAttributes) {
      const exactMatchParam = addParam(
        query.replace(/([%_])/g, '\\$1'),
        params
      );
      const companyIdParam = addParam(companyId, params);
      searchableAttributeWithBlock = `
                     SearchableAttributes
                         AS
                         (SELECT attribute_definition_id
                          FROM custom_attribute.attribute_definition ad
                          WHERE company_id = ${companyIdParam}
                            AND entity_type = 2
                            AND deleted = false
                            AND searchable_attribute = true),`;

      searchableAttributesUnionPortion = `      
                  
                          UNION
                
                          SELECT ua.Site_id
                          FROM UserAuthorized ua
                                   INNER JOIN custom_attribute.custom_attribute_entity_values cev
                                              ON ua.site_id = cev.entity_id
                          WHERE cev.attribute_override_value = ${exactMatchParam}
                            AND cev.deleted = false
                            AND cev.attribute_definition_id IN (SELECT attribute_definition_id FROM SearchableAttributes)`;
    }
  }

  const siteEventFilters = [];
  const siteEventsMap = {
    [appConstants.siteEvents.NEW_SITE]:
      "sh.created > NOW() - INTERVAL '1 week' AND COALESCE(tt.total_devices, 0)::INTEGER = 0",
  };

  if (siteEvents && siteEvents.length) {
    siteEvents.forEach(event => {
      const mappedEvent = siteEventsMap[event];
      if (mappedEvent) {
        siteEventFilters.push(mappedEvent);
      }
    });
  }

  const withBlock = `with UserAuthorized
                         AS
                         (SELECT site_id
                          FROM user_site_authorization usa
                          WHERE usa.user_id = ${userIdParam}
                          ${tagsForListFilter}
                          ${deviceStatusFilter}
                          ${oosCategoryFilter}
                          ),
                     ${searchableAttributeWithBlock}
                     SiteFilter
                         AS
                         (SELECT s.Site_id
                          FROM UserAuthorized ua
                                   INNER JOIN site s
                                              ON ua.site_id = s.site_id
                          WHERE (
                              s.active = true
                              ${queryLikeFilter}
                          )                
                          ${searchableAttributesUnionPortion}
                          ),
                     SiteDetails
                         AS
                         (SELECT s.site_id AS id,
                                 s.name,
                                 s.formatted_address,
                                 s.visible,
                                 s.created,
                                 JSONB_BUILD_OBJECT(
                                         'id',
                                         c.id,
                                         'name',
                                         c.name
                                     )     AS "owner"
                          FROM SiteFilter ua
                                   INNER JOIN site s
                                              ON ua.site_id = s.site_id
                                   INNER JOIN company c
                                              ON s.company_id = c.id
                          
                          ${hiddenFilter}
                         ),
                     SiteHealth
                         AS
                         (SELECT s.id,
                                 name,
                                 formatted_address,
                                 gsh     as status,
                                 visible,
                                 owner,
                                 s.created,
                                 CASE
                                     WHEN gsh = 'CRITICAL' THEN (SELECT rah.triggered_timestamp
                                                                 FROM report_alarm_history rah
                                                                 WHERE rah.site_id = s.id
                                                                   AND rah.alarm_type = 'site_critical'
                                                                   AND rah.remedy_timestamp IS NULL
                                                                   AND rah.device_id IS NULL
                                                                 ORDER BY rah.triggered_timestamp DESC NULLS LAST
                                                                 LIMIT 1)
                                     ELSE NULL
                                     END AS critical_alarm_ts
                          FROM SiteDetails s
                                   INNER JOIN LATERAL get_site_health(s.id) gsh on TRUE
                          ${statusFilter}),
                     TargetDetails
                         AS
                         (SELECT JSONB_OBJECT_AGG(cc.health, cc.count) FILTER (
                             WHERE cc.health IS NOT NULL
                             ) as           device_counts,
                                 cc.id,
                                 SUM(count) total_devices
                          FROM (SELECT s.id,
                                       mv_dh.health,
                                       COUNT(t.target_id) AS count
                                FROM SiteDetails s
                                         INNER JOIN target t
                                                    ON s.id = t.site_id
                                         INNER JOIN mv_device_health mv_dh
                                                    ON t.target_id = mv_dh.target_id
                                WHERE t.active
                                GROUP BY mv_dh.health, s.id) cc
                          GROUP BY cc.id),
                     AllSites
                         AS (SELECT sh.id,
                                    sh.name,
                                    sh.formatted_address,
                                    sh.status,
                                    sh.visible,
                                    sh.owner,
                                    sh.created,
                                    (SELECT COALESCE(json_agg(t.*) FILTER (WHERE t.id IS NOT NULL), '[]')
                                     FROM site_tag st
                                              LEFT JOIN tag t
                                                        ON t.id = st.tag_id 
                                     WHERE st.site_id = sh.id AND st.deleted = false)             as "tags",
                                    sh.critical_alarm_ts,
                                    COALESCE(tt.total_devices, 0)::INTEGER as total_devices,
                                    tt.device_counts
                             FROM SiteHealth sh
                                      LEFT JOIN TargetDetails tt
                                                ON sh.id = tt.id
                            ${
                              siteEventFilters.length
                                ? `WHERE ${siteEventFilters.join(' AND ')}`
                                : ''
                            }
                             ORDER BY ${order})`;

  let selectFromBlock = '';
  if (isCSV) {
    selectFromBlock = `
            SELECT * FROM AllSites`;
  } else {
    const limitParam = addParam(limit, params);
    const offsetParam = addParam(offset, params);

    selectFromBlock = `
            SELECT  (SELECT COUNT(1) FROM AllSites)                                           AS total_count,
                    (SELECT JSON_AGG(a) FROM (SELECT * FROM AllSites LIMIT ${limitParam} OFFSET ${offsetParam}) AS a) AS results`;
  }

  const completeQuery = `
        ${withBlock}
        ${selectFromBlock}
    `;

  return { completeQuery, params };
}

function transformCSVResult(result) {
  const data = result;

  // for company name
  if (data.owner) {
    data.company_name = data.owner.name;
  }

  // for site tags
  if (data.tags) {
    data.tag_names = data.tags.map(tag => tag.name).join();
  }

  // for location
  if (data.formatted_address) {
    data.location = data.formatted_address;
  }

  // for critical alarm ts
  if (data.critical_alarm_ts) {
    data.critical_alarm_ts = moment(data.critical_alarm_ts).utc().format();
  }

  // for counts
  if (data.total_devices) {
    data.total_devices = data.total_devices || 0;
  }
  if (data.device_counts) {
    data.operational_devices = data.device_counts.OPERATIONAL || 0;
    data.inactive_devices = data.device_counts.INACTIVE || 0;
    data.unknown_devices = data.device_counts.UNKNOWN || 0;
    data.oos_devices = data.device_counts.OUT_OF_SERVICE || 0;
  }

  delete data.device_counts;

  return data;
}

module.exports = {
  verifyAuthorization: async (req, res, next) => {
    const userId = req.user.sub;
    const { sites } = req.query;

    try {
      const params = [userId];

      if (sites && sites.length) {
        params.push(sites);
      }

      const result = await server.db.read.rows(
        `
                SELECT site_id FROM user_site_authorization
                WHERE
                    user_id = $1
                    ${sites && sites.length ? 'AND site_id = ANY($2)' : ''};
            `,
        params
      );

      const sitesFound = result.map(r => r.siteId);

      res.send(sitesFound);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  /**
   * Get all sites visible to the user and filter by tags
   */
  getAllSites: async (req, res, next) => {
    try {
      let order = req.query.order || 'name-asc';
      const { alarms } = req.query;
      const { tags } = req.query;
      const pagingParams = paginationHelper.parsePaginationParams(req);

      switch (order) {
        case 'id-asc':
          order = 's.site_id';
          break;
        case 'id-desc':
          order = 's.site_id desc';
          break;
        case 'address-asc':
          order = 's.formatted_address';
          break;
        case 'address-desc':
          order = 's.formatted_address desc';
          break;
        case 'status-asc':
          order = 'status, s.name';
          break;
        case 'status-desc':
          order = 'status desc, s.name';
          break;
        case 'name-desc':
          order = 's.name desc';
          break;
        default:
          order = 's.name';
          break;
      }

      const whereClause = prepareWhere({
        user: req.user.sub,
        query: req.query.q,
        tags: req.query.tags,
        company: req.query.company,
        statuses: req.query.statuses,
        deviceStatuses: null,
        oosFilter: null,
        alarms: req.query.alarms,
        siteGroups: req.query.siteGroups,
        // show only visible sites by default
        showHiddenSites:
          req.query.showHiddenSites === undefined
            ? false
            : JSON.parse(req.query.showHiddenSites),
      });

      let withBlock = `
                WITH company AS (
                  SELECT
                      id,
                      name,
                      default_site
                  FROM company
                ),
                key_group AS (
                  SELECT
                      key_group_id AS "id",
                      key_group_name AS "name",
                      key_group_ref AS "ref",
                      company_id,
                      json_build_object(
                          'id', c.id,
                          'name', c.name
                      )::jsonb AS "owner"
                  FROM key_group
                  JOIN company c on c.id=key_group.company_id
                  GROUP BY c.id, c.name, key_group_id, company_id
                ),
                tag AS (
                  SELECT
                      id,
                      name,
                      company_id AS "companyId"
                  FROM tag
                ),
                auth_site_group AS (
                    SELECT
                        id,
                        name,
                        company_id AS "companyId"
                    FROM authorization_site_group
                )
              `;

      if (alarms && alarms.length) {
        withBlock += `
                , active_alarms AS (
                    SELECT sa.code, sa.site_id, sa.modified,
                        NULL AS device_id,
                        NULL AS device_name,
                        NULL AS device_serial_number
                        FROM site_alarms sa
                    WHERE sa.status
                
                UNION
   
                    SELECT da.code, da.site_id, da.modified,
                        t.target_id     AS device_id,
                        t.name          AS device_name,
                        t.serial_number AS device_serial_number
                    FROM device_alarms da
                    JOIN target t ON t.target_id = da.device_id
                    WHERE da.status AND t.active AND t.delete_timestamp IS NULL
                )
            `;
      }

      const selectBlock = `
                SELECT
                    s.address_json       AS "address",
                    s.contact_email,
                    s.phone_number       AS "contactPhone",
                    s.formatted_address,
                    s.site_id            AS "id",
                    s.latitude::FLOAT,
                    s.longitude::FLOAT,
                    s.name,
                    s.reference_id,
                    get_site_health( s.site_id ) as status,
                    s.timezone_id,
                    s.visible,
                    json_build_object(
                        'id', c.id,
                        'name', c.name
                    )::jsonb AS "owner",
                    CASE
                        WHEN s.site_id = c.default_site THEN TRUE
                        ELSE FALSE
                    END as "isDefault",
                    CASE WHEN COUNT(k) = 0 then NULL ELSE json_build_object( 'id' , k.id, 'name', k.name, 'owner', k.owner, 'ref', k.ref ) END AS "keyGroup",
                    COALESCE(json_agg(t.*) FILTER (WHERE t.id IS NOT NULL), '[]') AS "tags",
                    COALESCE(json_agg(asg.*) FILTER (WHERE asg.id IS NOT NULL), '[]') AS "siteGroups"
            `;

      const fromBlock = `
                FROM site s
                JOIN company c ON s.company_id=c.id
                JOIN user_site_authorization u on u.site_id = s.site_id
                LEFT JOIN key_group k ON k.id=s.key_group_id
                LEFT JOIN site_tag st ON st.site_id=s.site_id AND st.deleted = false
                LEFT JOIN tag t ON t.id=st.tag_id
                LEFT JOIN authorization_site_group_site asgs ON asgs.site_id=s.site_id
                LEFT JOIN auth_site_group asg ON asg.id=asgs.authorization_site_group_id
            `;

      const totalObj = await server.db.read.row(
        `
                ${withBlock}
                SELECT COUNT(DISTINCT( s.* ))
                ${fromBlock}
                ${whereClause.where}
                `,
        whereClause.params
      );

      const getSitesQuery = `
                ${withBlock}
                ${selectBlock}
                ${fromBlock}
                ${whereClause.where}
                GROUP BY s.site_id, c.id, c.name, c.default_site, k.id, k.name, k.owner, k.ref
                ORDER BY ${order}
              `;

      let results = await server.db.read.rows(
        getSitesQuery,
        whereClause.params
      );

      if (results && results.length) {
        results.forEach(r => {
          /* eslint-disable no-param-reassign */
          r.statusStr = r.status;
          r.status = siteHelper.translateSiteHealthToNumber(r.status);
          /* eslint-enable no-param-reassign */
        });
      }

      // ICS-2195 Users can see their company tag only
      results = siteHelper.filterSiteTagByCompany(req.user.company.id, results);
      results = siteHelper.filterSiteGroupByCompany(
        req.user.company.id,
        results
      );

      if (tags && tags.length > 0) {
        // ICS-4016 Searching site using tags shows only tag searched in results even if sites has other tags
        results = await siteHelper.getTagsForSitesByCompanyId(
          req.user.company.id,
          results
        );
      }

      res.send(200, {
        results,
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pagingParams.pageIndex,
          pageSize: pagingParams.pageSize,
        },
      });

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getSitesList: async (req, res, next) => {
    try {
      let order = req.query.order || 'name-asc';
      const { tags } = req.query;
      const isCSV = req.query.isCSV === 'true';
      const pagingParams = paginationHelper.parsePaginationParams(req);

      const searchCustomAttributes =
        req.user.company.featureFlags.includes('SHELL_SUPPORT');

      switch (order) {
        case 'id-asc':
          order = 'sh.id';
          break;
        case 'id-desc':
          order = 'sh.id desc';
          break;
        case 'address-asc':
          order = 'sh.formatted_address';
          break;
        case 'address-desc':
          order = 'sh.formatted_address desc';
          break;
        case 'status-asc':
          order = 'sh.status, sh.name';
          break;
        case 'status-desc':
          order = 'sh.status desc, sh.name';
          break;
        case 'name-desc':
          order = 'sh.name desc';
          break;
        default:
          order = 'sh.name';
          break;
      }

      const { completeQuery, params } = prepareAll({
        user: req.user.sub,
        companyId: req.user.company.id,
        query: req.query.q,
        applyStrictMatchTags: req.query?.applyStrictMatchTags,
        tagsForList: tags,
        statuses: req.query.statuses,
        deviceStatuses: req.query.deviceStatuses,
        oosFilter: req.query.oosFilter,
        siteEvents: req.query.siteEvents,
        // show only visible sites by default
        showHiddenSites:
          req.query.showHiddenSites === undefined
            ? false
            : JSON.parse(req.query.showHiddenSites),
        searchCustomAttributes,
        limit: pagingParams.pageSize,
        offset: pagingParams.offset,
        order,
        isCSV,
      });

      if (!isCSV) {
        const result = await server.db.read.row(completeQuery, params);

        const { totalCount } = result;
        const results = result.results
          ? result.results.map(objectKeysToCamelCase)
          : [];

        if (results && results.length) {
          results.forEach(r => {
            /* eslint-disable no-param-reassign */
            r.counts = {
              totalDevices: r.totalDevices || 0,
              operationalDevices:
                (r.deviceCounts && r.deviceCounts.OPERATIONAL) || 0,
              inactiveDevices: (r.deviceCounts && r.deviceCounts.INACTIVE) || 0,
              unknownDevices: (r.deviceCounts && r.deviceCounts.UNKNOWN) || 0,
              oosDevices:
                (r.deviceCounts && r.deviceCounts.OUT_OF_SERVICE) || 0,
            };
            r.statusStr = r.status;
            r.status = siteHelper.translateSiteHealthToNumber(r.status);
            r.tags = siteHelper.filterTagsByCompany(
              req.user.company.id,
              r.tags
            );

            delete r.totalDevices;
            delete r.deviceCounts;
            /* eslint-enable no-param-reassign */
          });
        }

        res.send(200, {
          results,
          resultsMetadata: {
            totalResults: totalCount,
            pageIndex: pagingParams.pageIndex,
            pageSize: pagingParams.pageSize,
          },
        });
      } else {
        // export to CSV
        const csvOptions = {
          delimiter: ',',
          endLine: '\n',
          columns: [
            'id',
            'name',
            'location',
            'status',
            'critical_alarm_ts',
            'company_name',
            'tag_names',
            'total_devices',
            'operational_devices',
            'inactive_devices',
            'unknown_devices',
            'oos_devices',
          ],
          escapeChar: '"',
          enclosedChar: '"',
          header: true,
        };

        const connection = await server.db.read.getConnection();
        const query = new QueryStream(completeQuery, params);

        res.writeHead(200, {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename=sites-list.csv',
        });

        const stream = connection.client.query(query);
        stream.on('end', connection.done);
        const stringifier = stringify(csvOptions);
        stream
          .pipe(transform(data => transformCSVResult(data)))
          .pipe(stringifier)
          .pipe(res);
      }

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  /**
   * Get all the sites for ScheduledTask
   */
  getAllSitesForScheduledTask: async (req, res, next) => {
    try {
      const results = await server.db.read.rows(
        `select site_id as id, name as "siteName", formatted_address as "formattedAddress" from site where site_id = ANY($1)`,
        [req.body.siteIds]
      );
      res.send(200, {
        results,
      });
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
