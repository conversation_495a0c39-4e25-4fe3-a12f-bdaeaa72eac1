const restify = require('restify');
const uuid = require('uuid/v4');
const { server } = require('../../app');
const totp = require('../../lib/totp');
const usersHelper = require('../../helpers/users-helper');
const {
  getSiteExternalReferences,
  insertSiteExternalReferences,
  sendEventsToEventBridgeOnSiteUpdate,
  resolveExternalReferenceType,
} = require('../../helpers/site-external-references-helper');
const siteTagsHelper = require('../../helpers/site-tags-helper');
const { ACTIONS } = require('../../lib/app-constants');
const { submitSiteGroupUpdateEventEvent } = require('../sitegroups');
const { refreshMVUserSiteAuthorization } = require('../../helpers/mv-helper');
const { processLocationEvent } = require('../../helpers/bluefin-helper');
const sitesHelper = require('../../helpers/sites-helper');
const { sendEventOnUpdateSite } = require('./sites-replication-events');

module.exports = {
  createNewSite: async (req, res, next) => {
    const site = { ...req.body };
    const { user } = req;
    const userId = user.sub;
    const companyId = user.company.id;

    if (site.timezoneId && site.timezoneId !== '') {
      let validTimezones = await server.db.read.rows(
        'SELECT name FROM pg_timezone_names()'
      );
      validTimezones = validTimezones.map(r => r.name);
      if (!validTimezones.includes(site.timezoneId)) {
        return next(new restify.BadRequestError('Invalid timezoneId'));
      }
    }

    /*
     * Check if site with same name (case insensitive)
     * or referenceId already exists
     */
    const existingSite = await server.db.read.row(
      `
          SELECT site_id, integration_id
          FROM site
          WHERE ( ( LOWER(name) = $1 AND company_id = $2)
          OR
          ( reference_id = $3 AND company_id = $2 )
          OR
          ( integration_id = $4 AND company_id = $2 AND $4 IS NOT NULL )
          ) AND active = true LIMIT 1
          ;`,
      [site.name.toLowerCase(), companyId, site.referenceId, site.integrationId]
    );

    if (existingSite) {
      if (
        site.integrationId &&
        existingSite.integrationId &&
        site.integrationId === existingSite.integrationId
      ) {
        return next(
          new restify.BadRequestError('IntegrationId already exists')
        );
      }
      return next(
        new restify.BadRequestError('Site name or reference already exists')
      );
    }

    /*
     *  Check if any of the site external references already exist
     */
    if (site.externalReferences && site.externalReferences.length) {
      const references = await getSiteExternalReferences(
        null,
        site.externalReferences
      );

      if (references.length) {
        return next(
          new restify.BadRequestError(
            `Site with the references exist ${references
              .map(row => row.referenceType)
              .join(',')}`
          )
        );
      }
    }

    /*
     * Validate sitegroup exists, user is allowed to see that sitegroup and if there is exactly one
     * Joi confirms that only one sitegroup is sent in the body
     */
    let userSiteGroupAuth = '';
    if (req.user.hasRole('COMPANY_ADMIN')) {
      userSiteGroupAuth = await server.db.read.row(
        `
                  SELECT asg.id
                  FROM authorization_site_group asg
                  WHERE asg.id = $1 AND
                        asg.company_id = $2
              ;`,
        [site.siteGroups[0].id, req.user.company.id]
      );
    } else {
      userSiteGroupAuth = await server.db.read.row(
        `
                SELECT ugasg.user_group_id
                FROM user_group_authorization_site_group ugasg
                JOIN user_group_user ugu ON ugu.user_group_id = ugasg.user_group_id
                JOIN authorization_site_group asg ON asg.id = ugasg.authorization_site_group_id
                WHERE ugasg.authorization_site_group_id = $1 AND ugu.user_id = $2 AND asg.company_id = $3
              ;`,
        [site.siteGroups[0].id, userId, req.user.company.id]
      );
    }

    if (!userSiteGroupAuth) {
      req.log.info("User isn't allowed to view this sitegroup");
      return next(new restify.NotFoundError('Sitegroup not found'));
    }

    if (site.keyGroupId) {
      if (user.hasRole('RKI')) {
        const mfaSecret = await usersHelper.getUserMfaSecretById(userId);
        // If Key Group Id is assigned and mfa code is received, validate the mfa
        if (!(await totp.validateTotp(req, mfaSecret, site.mfaCode))) {
          req.log.info('MFA is not valid');
          return next(new restify.NotAcceptableError('MFA is not valid')); // 406
        }

        /*
         * If keygroup is not null, check
         * keygroup exists & must be from the same company as site
         */
        if (site.keyGroupId) {
          const keyGroupsCompany = await server.db.read.row(
            `
                    SELECT company_id
                    FROM key_group
                    WHERE key_group_id = $1
                  `,
            [site.keyGroupId]
          );

          if (keyGroupsCompany.companyId !== user.company.id) {
            return next(
              new restify.ForbiddenError('Unable to access keygroup')
            );
          }
        }
      } else {
        req.log.info('Only users with RKI role are allowed to assign keygroup');
        return next(
          new restify.NotAcceptableError(
            'Only users with RKI role are allowed to assign keygroup'
          )
        );
      }
    }

    const connection = await server.db.write.getConnection();

    try {
      site.id = uuid();
      site.status = 0; // default status
      const suppressOffhoursAlarmValue =
        site.suppressOffhoursAlarm === undefined
          ? false
          : site.suppressOffhoursAlarm;

      const params = [
        site.id,
        site.address,
        site.contactEmail,
        site.contactPhone,
        site.formattedAddress,
        site.keyGroupId,
        site.latitude,
        site.longitude,
        site.name,
        user.company.id,
        site.referenceId,
        true,
        '127.0.0.1',
        site.status,
        0,
        site.timezoneId,
        site.visible === undefined ? true : site.visible,
        suppressOffhoursAlarmValue,
        site.disableCmAutomaticDeployments !== undefined
          ? site.disableCmAutomaticDeployments
          : false,
        site.disableFileDownloads !== undefined
          ? site.disableFileDownloads
          : false,
      ];

      let hoursParamId;
      if (site.hours) {
        params.push(JSON.stringify(site.hours));
        hoursParamId = params.length;
      }

      let integrationIdParamId;
      if (site.integrationId) {
        params.push(site.integrationId);
        integrationIdParamId = params.length;
      }
      await connection.execute('BEGIN');
      await connection.execute(
        `
        INSERT INTO site (
          site_id,
          address_json,
          contact_email,
          phone_number,
          formatted_address,
          key_group_id,
          latitude,
          longitude,
          name,
          company_id,
          reference_id,
          active,
          ip_address,
          status,
          max_upload_rate_kb,
          created,
          timezone_id,
          visible,
          suppress_offhours_alarm,
          disable_cm_automatic_deployment,
          disable_software_downloads
          ${site.hours ? ', hours' : ''}
          ${site.integrationId ? ', integration_id' : ''}
        )
        VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
          $11, $12, $13, $14, $15, NOW(), $16, $17,
          $18, $19, $20 ${site.hours ? `, $${hoursParamId}` : ''}  ${
            site.integrationId ? `, $${integrationIdParamId}` : ''
          }
        );`,
        params
      );

      await siteTagsHelper.saveSiteTags(
        user.company.id,
        site.id,
        site.tags,
        userId,
        connection
      );

      if (site.externalReferences) {
        await insertSiteExternalReferences(
          site.id,
          site.externalReferences,
          userId,
          connection,
          companyId
        );
      }
      // Assigning site to sitegroup
      if (site.siteGroups.length) {
        await connection.execute(
          `
                      INSERT INTO authorization_site_group_site (
                        authorization_site_group_id,
                        site_id
                      )
                      VALUES ( $1, $2 );
                  `,
          [site.siteGroups[0].id, site.id]
        );
      }

      // Commit transaction
      await connection.execute('COMMIT');

      await sendEventOnUpdateSite(site.id);

      const externalSiteReferencesCreated = resolveExternalReferenceType(
        site.externalReferences
      );
      if (externalSiteReferencesCreated) {
        await sendEventsToEventBridgeOnSiteUpdate(
          site.id,
          externalSiteReferencesCreated.referenceId,
          companyId,
          ACTIONS.CREATE,
          externalSiteReferencesCreated.referenceType
        );
      }
    } catch (err) {
      req.log.error({
        err,
      });
      await connection.execute('ROLLBACK');
      return next(new restify.InternalServerError(err));
    } finally {
      await connection.done();
    }

    await refreshMVUserSiteAuthorization('createSite');

    const newSite = await sitesHelper.getSiteById(site.id, userId, companyId);

    if (!newSite) {
      // Unable to insert site - `should` never happen
      return next(
        new restify.errors.InternalServerError('Failed to insert site')
      );
    }

    if (site.siteGroups.length) {
      await submitSiteGroupUpdateEventEvent(
        [{ id: site.siteGroups[0].id }],
        companyId
      );
    }

    await processLocationEvent(newSite, 'createLocation');
    res.send(newSite);
    return next();
  },
};
