const co = require('co');
const _ = require('lodash');
const errors = require('restify-errors');

const { server } = require('../app');
const AWS = require('../lib/aws');
const mailer = require('../lib/mailer');
const { config } = require('../env');
const { offlinePackage } = require('../lib/app-constants');
const errorHandler = require('../lib/errorhandler');
const offlinePackageHelper = require('../helpers/offlinepackage-helper');
const mailerHelper = require('../helpers/mailer-helper');

const ADD_OFFLINE_PACKAGE = `INSERT INTO offline_package(id, company_id, name, type, created_by, date_created )
VALUES (uuid_generate_v1mc(), $1, $2, $3, $4, NOW())
RETURNING id, company_id, name, type, file_size, date_created AS created_on, status`;

const GET_OFFLINE_PACKAGE_BY_ID = `SELECT op.id, op.company_id, op.name, op.type, file_size, checksum, op.status, 
json_build_object(
    'id', u.id,
    'email', u.email,
    'fullName', u.full_name
) as created_by,
date_created
FROM offline_package op
LEFT JOIN ics_user u ON created_by = u.id
WHERE op.id = $1 AND
op.company_id = $2 AND
op.active = TRUE`;

const CREATE_NOTIFICATION = `INSERT INTO notification
    ( id, user_id, created, read, message, related_entity, type, level )
VALUES
    ( uuid_generate_v1mc(), $1, NOW(), $2, $3, $4, $5, $6 )`;

module.exports = {
  createSoftwareDownload(req, res, next) {
    const companyId = req.user.company.id;
    const userName = req.user.fullName;
    const userEmail = req.user.email;
    const userId = req.user.sub;
    const { name } = req.body;
    const { softwareIds } = req.body;

    let offlinePackageItem;

    co(function* run() {
      const duplicatedName = yield server.db.read.rows(
        `
                SELECT id, name FROM offline_package
                WHERE
                    LOWER(name) = $1 AND
                    company_id = $2 AND
                    active IS TRUE;
            `,
        [name.toLowerCase(), companyId]
      );
      if (duplicatedName.length) {
        return next(
          new errors.BadRequestError(
            `Offline package with name ${name} already exists`
          )
        );
      }

      // Validate software ids
      const softwareList = yield offlinePackageHelper.getSoftwareByIdBatch(
        softwareIds,
        companyId
      );

      const missingSoftwareIds = _.difference(
        softwareIds,
        softwareList.map(software => software.id)
      );
      if (!_.isEmpty(missingSoftwareIds)) {
        return next(
          new errors.ForbiddenError(
            `Software id ${missingSoftwareIds.join(', ')} ${
              missingSoftwareIds.length === 1 ? 'does' : 'do'
            } not exist or not accessible.`
          )
        );
      }

      const totalFileSize = softwareList.reduce(
        (sum, software) => sum + software.size,
        0
      );
      if (totalFileSize >= config.offlinePackage.offlinePackageMaxFileSize) {
        return next(
          new errors.UnprocessableEntityError(
            `The total file size is over ${
              config.offlinePackage.offlinePackageMaxFileSize / 1024 / 1024
            }G `
          )
        );
      }

      // save the offline package
      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');
        const offlinePackageResult = yield connection.execute(
          ADD_OFFLINE_PACKAGE,
          [companyId, name, offlinePackage.TYPE.SOFTWARE, userId]
        );
        const { 0: firstRow } = offlinePackageResult.rows;
        offlinePackageItem = firstRow;
        req.log.info('Added offline package');

        const softwareTemplate = softwareList
          .map((software, index) => `($${index * 2 + 1}, $${index * 2 + 2})`)
          .join(',');
        const softwareParams = softwareList.map(software => [
          software.id,
          offlinePackageItem.id,
        ]);

        // Add offline_package_software items
        yield connection.execute(
          `INSERT INTO offline_package_software(software_id, offline_package_id) VALUES ${softwareTemplate}`,
          _.flatten(softwareParams)
        );
        req.log.info('Added offline package software');

        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        throw err;
      }

      offlinePackageItem.softwareFiles = softwareList;
      offlinePackageItem.createdBy = {
        id: userId,
        email: userEmail,
        name: userName,
      };

      // Create archive in a next event loop, not blocking the response
      global.setImmediate(
        offlinePackageHelper.createSoftwarePackage,
        softwareList,
        offlinePackageItem,
        companyId
      );

      // add offline software package
      res.send(201, offlinePackageItem);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  createRkiDownload(req, res, next) {
    let companyId;
    const { name } = req.body;
    const { deviceIds } = req.body;
    const userName = req.user.fullName;
    const userEmail = req.user.email;
    const userCompanyId = req.user.company.id;
    const userId = req.user.sub;
    let offlinePackageItem;
    const notification = {
      message: '',
      path: '/remote/packages',
    };
    let senderEmail;

    co(function* run() {
      // verify that all devices belong to the same company
      const devicesOwners = yield server.db.read.rows(
        `
                SELECT DISTINCT s.company_id FROM target t JOIN site s ON t.site_id = s.site_id WHERE t.target_id = ANY($1);
            `,
        [deviceIds]
      );

      if (devicesOwners && devicesOwners.length !== 1) {
        return next(
          new errors.BadRequestError(
            'All devices have to belong to same owner.'
          )
        );
      }

      companyId = devicesOwners[0].companyId;
      const duplicatedName = yield server.db.read.rows(
        `
                SELECT id, name FROM offline_package
                WHERE
                    LOWER(name) = $1 AND
                    company_id = $2;
            `,
        [name.toLowerCase(), companyId]
      );
      if (duplicatedName.length) {
        return next(
          new errors.BadRequestError(
            `Offline package with name ${name} already exists`
          )
        );
      }

      // Validate device ids
      const devices = yield offlinePackageHelper.getDeviceByIdBatch(
        deviceIds,
        companyId
      );
      const missingDeviceIds = _.difference(
        deviceIds,
        devices.map(device => device.id)
      );
      if (!_.isEmpty(missingDeviceIds)) {
        return next(
          new errors.ForbiddenError(
            `Device id ${missingDeviceIds.join(', ')} ${
              missingDeviceIds.length === 1 ? 'does' : 'do'
            } not exist, out of instance or not accessible.`
          )
        );
      }

      const invalidDevices = devices.filter(device => !device.keyCerts);
      if (invalidDevices) {
        const invalidDeviceIds = invalidDevices.map(device => device.id);
        if (!_.isEmpty(invalidDeviceIds)) {
          return next(
            new errors.BadRequestError(
              `${
                invalidDeviceIds.length === 1 ? 'Device' : 'Devices'
              } with id ${invalidDeviceIds.join(', ')} ${
                invalidDeviceIds.length === 1 ? 'is' : 'are'
              } not valid for this operation.`
            )
          );
        }
      }
      senderEmail = (yield mailerHelper.getSenderEmailsByCompanyId(companyId))
        .senderEmail;
      // save the offline package
      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');
        const offlinePackageResult = yield connection.execute(
          ADD_OFFLINE_PACKAGE,
          [userCompanyId, name, offlinePackage.TYPE.RKI, userId]
        );
        const { 0: firstRow } = offlinePackageResult.rows;
        offlinePackageItem = firstRow;
        req.log.info('Added offline package');

        const rkiTemplate = devices
          .map(
            (device, index) =>
              `($${index * 3 + 1}, $${index * 3 + 2}, $${index * 3 + 3})`
          )
          .join(',');
        const rkiParams = devices.map(device => [
          device.id,
          offlinePackageItem.id,
          device.keyBundleId,
        ]);

        // Add offline_package_rki items
        yield connection.execute(
          `INSERT INTO offline_package_rki(device_id, offline_package_id, device_rki_key_bundle_id) VALUES ${rkiTemplate}`,
          _.flatten(rkiParams)
        );
        req.log.info('Added offline package RKI');

        // Create RKI package
        const rkiPackage = yield offlinePackageHelper.createRkiPackage(
          devices,
          offlinePackageItem.id
        );
        yield connection.execute(
          `UPDATE offline_package
                    SET file_size = $1, checksum = $2, status = 'DONE'
                    WHERE id = $3`,
          [rkiPackage.fileSize, rkiPackage.checksum, offlinePackageItem.id]
        );
        offlinePackageItem.checksum = rkiPackage.checksum;
        offlinePackageItem.fileSize = rkiPackage.fileSize;

        // Create success notification
        const relatedEntity = {
          type: 'rkiPackage',
          id: offlinePackageItem.id,
        };

        notification.message = `Offline package ${name} is ready for download.`;
        yield connection.execute(CREATE_NOTIFICATION, [
          userId,
          false,
          notification.message,
          relatedEntity,
          offlinePackage.NOTIFICATION_TYPE,
          'SUCCESS',
        ]);

        // Send offlinePackage email notification
        yield mailer.sendNotification(
          { to: userEmail, from: senderEmail },
          notification,
          notification.message
        );

        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });

        yield connection.execute('ROLLBACK');
        connection.done();

        // save the offline package
        const conn = yield server.db.write.getConnection();

        try {
          // Create failure notification
          const relatedEntity = {
            type: 'rkiPackage',
            id: offlinePackageItem.id,
          };

          notification.message = `Failed to create offline package ${name}.`;
          yield conn.execute(CREATE_NOTIFICATION, [
            userId,
            false,
            notification.message,
            relatedEntity,
            offlinePackage.NOTIFICATION_TYPE,
            'DANGER',
          ]);

          yield mailer.sendNotification(
            { to: userEmail, from: senderEmail },
            notification,
            notification.message
          );
          yield connection.execute('COMMIT');
          connection.done();
        } catch (err2) {
          req.log.error({ err2 });
          yield connection.execute('ROLLBACK');
          connection.done();
        }

        throw err;
      }

      offlinePackageItem.devices = devices;
      offlinePackageItem.createdBy = {
        id: userId,
        email: userEmail,
        name: userName,
      };
      offlinePackageItem.status = 'DONE';

      // add offline rki package
      res.send(201, offlinePackageItem);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  list(req, res, next) {
    const companyId = req.user.company.id;
    const pageIndex = req.params.pageIndex || 0;
    const pageSize = req.params.pageSize || 100;
    const { status } = req.params;
    let { type } = req.params;
    const { name } = req.params;
    const totalParams = [companyId];

    if (
      req.user.company &&
      !req.user.company.featureFlags.includes('OFFLINE_RKI')
    ) {
      if (type === 'RKI' || type === 'rki') {
        return next(new errors.ForbiddenError('Unauthorized User'));
      }
      if (!type) {
        type = 'software';
      }
    }

    return co(function* run() {
      let totalWhereClause = ' WHERE active = TRUE AND op.company_id = $1';
      if (status) {
        totalWhereClause += ` AND op.status = $${totalParams.length + 1}`;
        totalParams.push(status);
      }
      if (type) {
        totalWhereClause += ` AND op.type = $${totalParams.length + 1}`;
        totalParams.push(type);
      }
      if (name) {
        totalWhereClause += ` AND op.name LIKE $${totalParams.length + 1}`;
        totalParams.push(`%${name}%`);
      }

      const listParams = _.concat(totalParams, [
        pageSize,
        pageIndex * pageSize,
      ]);
      const listWhereClause = `${totalWhereClause}
            ORDER BY date_created DESC
            LIMIT $${totalParams.length + 1}
            OFFSET $${totalParams.length + 2}`;

      const total = yield server.db.read.row(
        `SELECT COUNT(*) FROM offline_package op ${totalWhereClause}`,
        totalParams
      );
      const offlinePackages = yield server.db.read.rows(
        `SELECT op.id, op.company_id, op.name, op.type, file_size, checksum, op.status,
                json_build_object(
                    'id', u.id,
                    'email', u.email,
                    'fullName', u.full_name
                ) as created_by, 
                date_created
                FROM offline_package op
                LEFT JOIN ics_user u ON created_by = u.id ${listWhereClause}`,
        listParams
      );
      const paginatedOfflinePackages = {
        results: offlinePackages,
        resultsMetadata: {
          pageIndex,
          pageSize,
          total: total.count,
        },
      };
      res.send(200, paginatedOfflinePackages);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  get(req, res, next) {
    const companyId = req.user.company.id;
    const offlinePackageId = req.params.id;

    co(function* run() {
      const offlinePackageEntry = yield server.db.read.row(
        GET_OFFLINE_PACKAGE_BY_ID,
        [offlinePackageId, companyId]
      );
      if (!offlinePackageEntry) {
        return next(
          new errors.NotFoundError(
            `Offline package with id ${offlinePackageId} does not exist`
          )
        );
      }

      if (offlinePackageEntry.type === offlinePackage.TYPE.SOFTWARE) {
        offlinePackageEntry.softwareFiles = yield server.db.read.rows(
          `SELECT s.name, s.software_id, software_file, s.type, 
                    json_build_object(
                        'id', s.device_type,
                        'name', cpt.display_name
                    ) as device_type,
                    s.size
                    FROM software s
                    LEFT JOIN company_product_type cpt ON 
                        cpt.company = s.company_id AND
                        cpt.device_type = s.device_type
                    LEFT JOIN offline_package_software ops ON
                        ops.software_id = s.software_id
                    WHERE ops.offline_package_id = $1`,
          [offlinePackageId]
        );
      } else if (offlinePackageEntry.type === offlinePackage.TYPE.RKI) {
        if (
          req.user.company &&
          !req.user.company.featureFlags.includes('OFFLINE_RKI')
        ) {
          return next(new errors.ForbiddenError('Unauthorized User'));
        }

        offlinePackageEntry.devices = yield server.db.read.rows(
          `
                    SELECT
                      t.target_id, t.name, t.serial_number, t.presence,
                      s.name AS site_name, s.site_id AS site_id,
                      json_build_object (
                        'id', t.device_type,
                        'name', cpt.display_name
                      ) AS device_type,
                      json_build_object (
                        'id', kg.key_group_id,
                        'name', kg.key_group_name,
                        'ref', kg.key_group_ref,
                        'owner', json_build_object(
                          'id', c.id,
                          'name', c.name
                        )
                      ) AS key_group,
                    CASE 
                      WHEN s.company_id != c.id THEN true
                      WHEN t.key_group_ref != kg.key_group_ref THEN true
                      WHEN t.presence = 'OUT_OF_INSTANCE' THEN true
                      ELSE false
                    END AS moved_since_creation
                    FROM offline_package_rki opr
                      LEFT JOIN device_rki_key_bundle drkb ON drkb.id = opr.device_rki_key_bundle_id
                      JOIN target t ON t.target_id = opr.device_id
                      JOIN site s ON s.site_id = t.site_id
                      LEFT JOIN key_group kg ON kg.key_group_id = drkb.key_group_id
                      JOIN company c ON c.id = kg.company_id
                      LEFT JOIN company_product_type cpt ON
                        cpt.company = s.company_id AND
                        cpt.device_type = t.device_type
                    WHERE
                      opr.offline_package_id = $1;
                `,
          [offlinePackageId]
        );
      }

      res.send(200, offlinePackageEntry);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  download(req, res, next) {
    const offlinePackageId = req.params.id;

    co(function* run() {
      const offlinePackageEntry = yield server.db.read.row(
        `
                SELECT op.id, op.name, op.type FROM offline_package op
                WHERE
                    op.id = $1 AND
                    op.active = TRUE AND
                    op.status = 'DONE';
            `,
        [offlinePackageId]
      );

      if (!offlinePackageEntry) {
        return next(
          new errors.NotFoundError(
            `Offline package with id ${offlinePackageId} does not exist or not ready for download`
          )
        );
      }

      req.log.info(
        `Attempting to download offline package content for key: ${offlinePackageId}.`
      );

      res.setHeader(
        'Content-Disposition',
        `attachment; filename="${offlinePackageEntry.name}.zip"`
      );
      res.setHeader('File-Name', `${offlinePackageEntry.name}.zip`);

      return AWS.downloadFromS3({
        Bucket: config.offlinePackage.s3OfflinePackageBucket,
        Key: offlinePackageId,
      })
        .on('httpHeaders', (statusCode, headers) => {
          if (statusCode === 200) {
            res.setHeader('Content-Length', headers['content-length']);
            res.setHeader('Content-Type', headers['content-type']);
            res.setHeader('Last-Modified', headers['last-modified']);
          }
        })
        .createReadStream()
        .on('error', err => {
          req.log.error({ err });

          if (err.code === 'NoSuchKey') {
            return next(
              new errors.NotFoundError('The specified key does not exist.')
            );
          }
          return next(
            new errors.InternalServerError(
              'Could not download offline package content.'
            )
          );
        })
        .on('end', () => next())
        .pipe(res);
    }).catch(errorHandler.onError(req, res, next));
  },

  rename(req, res, next) {
    const companyId = req.user.company.id;
    const offlinePackageId = req.params.id;
    const { name } = req.body;

    co(function* run() {
      const duplicatedName = yield server.db.read.rows(
        `
                SELECT id, name FROM offline_package
                WHERE
                    LOWER(name) = $1 AND
                    company_id = $2; 
            `,
        [name.toLowerCase(), companyId]
      );
      if (duplicatedName.length) {
        return next(
          new errors.BadRequestError(
            `Offline package with name ${name} already exists`
          )
        );
      }

      const connection = yield server.db.write.getConnection();
      try {
        let excludeRKIpackages = '';
        if (
          req.user.company &&
          !req.user.company.featureFlags.includes('OFFLINE_RKI')
        ) {
          excludeRKIpackages = " AND op.type <> 'RKI'";
        }

        yield connection.execute('BEGIN');

        yield connection.execute(
          `UPDATE offline_package AS op
                    SET name = $1
                    WHERE op.id = $2 AND
                    op.company_id = $3 AND
                    op.active = TRUE ${excludeRKIpackages}`,
          [name, offlinePackageId, companyId]
        );

        yield connection.execute('COMMIT');

        const offlinePackageEntry = yield server.db.read.row(
          GET_OFFLINE_PACKAGE_BY_ID + excludeRKIpackages,
          [offlinePackageId, companyId]
        );
        if (!offlinePackageEntry) {
          return next(
            new errors.NotFoundError(
              `Offline package with id ${offlinePackageId} does not exist`
            )
          );
        }

        res.send(200, offlinePackageEntry);
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        throw err;
      }

      // add offline software package
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  del(req, res, next) {
    const companyId = req.user.company.id;
    const offlinePackageId = req.params.id;

    co(function* run() {
      const offlinePackageEntry = yield server.db.read.row(
        GET_OFFLINE_PACKAGE_BY_ID,
        [offlinePackageId, companyId]
      );
      if (!offlinePackageEntry) {
        return next(
          new errors.NotFoundError(
            `Offline package with id ${offlinePackageId} does not exist`
          )
        );
      }
      if (
        offlinePackageEntry.type === 'RKI' &&
        req.user.company &&
        !req.user.company.featureFlags.includes('OFFLINE_RKI')
      ) {
        return next(new errors.ForbiddenError('Unauthorized User'));
      }

      const connection = yield server.db.write.getConnection();

      try {
        yield connection.execute('BEGIN');
        const offlinePackageResult = yield connection.execute(
          `UPDATE offline_package
                    SET active = FALSE, date_deleted = NOW()
                    WHERE id = $1 AND
                    company_id = $2 AND
                    active = TRUE`,
          [offlinePackageId, companyId]
        );

        yield connection.execute('COMMIT');
        connection.done();
        res.send(204, offlinePackageResult);
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        throw err;
      }

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
};
