const co = require('co');
const Joi = require('joi');

const { server } = require('../app');
const constants = require('../lib/app-constants');
const errorHandler = require('../lib/errorhandler');
const { config } = require('../env');

const passwordRegex = config.passwordRegex || constants.passwordRegex;

module.exports = {
  /**
   * Check whether the specified email can be used for a new account
   */
  checkEmail: (req, res, next) =>
    co(function* execute() {
      const { email } = req.body;

      // Check email format
      try {
        Joi.assert(email, Joi.string().email());
      } catch (err) {
        res.send(false);
        return next();
      }

      // Check email not used
      const query = `
                SELECT COUNT(*)
                FROM ics_user
                WHERE status != 2 AND email = LOWER($1);
            `;
      const result = yield server.db.read.row(query, [req.body.email]);

      res.send(result.count === 0);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Check whether the specified password can be used for a new account
   */
  checkPassword: (req, res, next) =>
    co(function* execute() {
      const { password } = req.body;

      // Check password format
      try {
        Joi.assert(password, Joi.string().min(12).max(100));
        Joi.assert(password, Joi.string().regex(new RegExp(passwordRegex)));
      } catch (err) {
        res.send(false);
        return next();
      }

      // Check password not banned
      const query = `
                SELECT COUNT(*)
                FROM banned_password
                WHERE password = $1;
            `;
      const result = yield server.db.read.row(query, [password]);

      res.send(result.count === 0);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Check whether the specified site name can be used for a new site
   */
  checkSiteName: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;
      const siteName = req.body.siteName.toLowerCase();

      // Check site name length format
      try {
        Joi.assert(siteName, Joi.string().regex(/^[^\\]{3,100}$/)); // eslint-disable-line space-in-parens
      } catch (err) {
        res.send(false);
        return next();
      }

      // Check name not used by this company before
      const query = `
                SELECT COUNT(*) FROM site
                WHERE company_id = $1 AND LOWER(name) = $2 AND active = true
            `;
      const result = yield server.db.read.row(query, [companyId, siteName]);

      res.send(result.count === 0);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Check whether the specified file name can be used for a new file upload
   */
  checkFileName: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;
      const fileName = req.body.fileName.toLowerCase();

      // Check if a file with the same name already exists
      const query = `
              SELECT COUNT(*) FROM software
              WHERE company_id = $1 AND LOWER(name) = $2 AND active = true
            `;

      const result = yield server.db.read.row(query, [companyId, fileName]);

      res.send(result.count === 0);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  checkOfflinePackage: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;
      const name = req.params.name.toLowerCase();

      const result = yield server.db.read.row(
        `
              SELECT COUNT(*) FROM offline_package
              WHERE 
                company_id = $1 AND 
                LOWER(name) = $2 AND
                active IS TRUE;
            `,
        [companyId, name]
      );

      res.send(result.count === 0);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
