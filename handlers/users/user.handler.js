const restify = require('restify');

const errorHandler = require('../../lib/errorhandler');
const userService = require('./user.service');

/**
 * Return list of languages for a company
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
const getUserFullNameById = async (req, res, next) => {
  const companyId = req.user.company.id;
  const userId = req.params.id;
  try {
    if (!companyId || !userId) {
      return next(
        new restify.errors.BadRequestError('Company id or user id is null')
      );
    }

    const userName = await userService.getUserFullNameById(companyId, userId);

    if (!userName) {
      return next(new restify.NotFoundError('user not found'));
    }
    res.send(200, userName);
    return next();
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};
module.exports = {
  getUserFullNameById,
};
