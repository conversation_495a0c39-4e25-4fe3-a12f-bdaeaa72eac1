const { server } = require('../../app');

/**
 * Get user full name by id.
 * @param {UUID} companyId
 * @param {UUID} userId
 * @returns fullname
 */
const getUserFullNameById = async (companyId, userId) =>
  // eslint-disable-next-line no-return-await
  await server.db.read.row(
    `
         SELECT u.full_name
                FROM ics_user u
                WHERE u.id = $1 
                AND u.company_id = $2`,
    [userId, companyId]
  );

module.exports = {
  getUserFullNameById,
};
