const errors = require('restify-errors');
const uuid = require('uuid/v4');
const validateUUID = require('uuid-validate');
const co = require('co');
const _ = require('lodash');
const { server } = require('../app');
const env = require('../env');
const aws = require('../lib/aws');

const errorHandler = require('../lib/errorhandler');
const { getSiteIdsByAuthSiteGroupId } = require('../helpers/sitegroups-helper');
const { refreshMVUserSiteAuthorization } = require('../helpers/mv-helper');

function* getUserSiteGroups(userId, companyId, targetCompanyId) {
  // Get site groups which user has access to
  return yield server.db.read.rows(
    `
        SELECT
          asg.id,
          asg.name,
          (
            SELECT row_to_json(_) FROM (SELECT c.id, c.name) as _
          ) as owner,
          (
            SELECT COUNT(*) as site_count
              FROM authorization_site_group_site as asgs
              JOIN site ON site.site_id = asgs.site_id AND site.active != false
            WHERE asgs.authorization_site_group_id = asg.id
          )
        FROM user_group_user ugu
          JOIN user_group_authorization_site_group ugasg ON ugasg.user_group_id = ugu.user_group_id
          JOIN authorization_site_group asg ON asg.id = ugasg.authorization_site_group_id
          JOIN company c ON c.id = asg.company_id
        WHERE
          asg.company_id = $1 AND
          ugu.user_id = $2;`,
    [targetCompanyId || companyId, userId]
  );
}

function* getCompanyAdminSiteGroups(companyId, targetCompanyId) {
  const params = [companyId];
  if (targetCompanyId) {
    params.push(targetCompanyId);
  }

  // Get sitegroups shared with company
  const sharedSiteGroups = yield server.db.read.rows(
    `
        SELECT
          asg.id,
          asg.name,
          (
            SELECT row_to_json(_) FROM (SELECT c.id, c.name) as _
          ) as owner,
          (
            SELECT COUNT(*) as site_count
            FROM authorization_site_group_site as asgs
            JOIN site ON site.site_id = asgs.site_id AND site.active != false
            WHERE asgs.authorization_site_group_id = asg.id
          )
        FROM
          authorization_site_group as asg
          JOIN authorization_site_group_company asgc ON asg.id = asgc.authorization_site_group_id
          JOIN company c ON c.id = asg.company_id
        WHERE
          asgc.company_id = $1
          ${targetCompanyId ? 'AND asg.company_id = $2;' : ';'}`,
    params
  );

  if (targetCompanyId && targetCompanyId !== companyId) {
    return sharedSiteGroups;
  }

  // Get sitegroups owned by company
  const ownSiteGroups = yield server.db.read.rows(
    `
        SELECT
          asg.id,
          asg.name,
          (
            SELECT row_to_json(_) FROM (SELECT c.id, c.name) as _
          ) as owner,
          (
            SELECT COUNT(*) as site_count
            FROM authorization_site_group_site as asgs
            JOIN site ON site.site_id = asgs.site_id AND site.active != false
            WHERE asgs.authorization_site_group_id = asg.id
          )
        FROM
          authorization_site_group as asg
          JOIN company c ON c.id = asg.company_id
        WHERE
          asg.company_id = $1;`,
    [companyId]
  );

  return [...ownSiteGroups, ...sharedSiteGroups];
}

const getSiteAuthSiteGroupIds = async siteId => {
  const results = await server.db.read.rows(
    `
              SELECT authorization_site_group_id AS id 
              FROM authorization_site_group_site
              WHERE site_id = $1
            ;`,
    [siteId]
  );
  if (results.length) {
    return results;
  }
  return [];
};

const sendEventsToEventBridge = async ({
  siteGroupId,
  type,
  tenantId,
  siteIds,
}) => {
  const eventBridgeParams = {
    data: {
      siteGroupId,
      type,
      tenantId,
      siteIds,
    },
    detailType: env.config.AWS.eventBus.renditions.rules.updateSiteGroup,
    source: 'handlers/sitegroups.js',
  };

  await aws.sendEventsToEventBridge(eventBridgeParams);
};

const sendSiteGroupEventToEventBrige = async ({
  siteGroupId,
  type,
  tenantId,
  siteIds,
}) => {
  await sendEventsToEventBridge({
    siteGroupId,
    type,
    tenantId,
    siteIds,
  });
};

const submitSiteGroupUpdateEventEvent = async (
  authorizationSiteGroups,
  companyId
) => {
  const authSiteGroupPromises = authorizationSiteGroups.map(authSiteGroup =>
    getSiteIdsByAuthSiteGroupId(authSiteGroup.id)
  );

  const authSiteGroupSiteIds = await Promise.allSettled(authSiteGroupPromises);
  if (authSiteGroupSiteIds.length) {
    const promises = authorizationSiteGroups.map((authSiteGroup, idx) => {
      const siteIds = authSiteGroupSiteIds[idx].value;

      return sendSiteGroupEventToEventBrige({
        siteGroupId: authSiteGroup.id,
        tenantId: companyId,
        siteIds: siteIds && siteIds.length ? siteIds.map(({ id }) => id) : [],
        type: 'update',
      });
    });
    await Promise.allSettled(promises);
  }
};

module.exports = {
  getSiteAuthSiteGroupIds,
  submitSiteGroupUpdateEventEvent,
  getSiteGroups: (req, res, next) =>
    co(function* execute() {
      const userId = req.user.sub;
      const companyId = req.user.company.id;
      const targetCompanyId = req.query.company;

      const userRoles = req.user.getRoles(); // eslint-disable-line dot-notation
      const isAdmin = _.find(userRoles, uRole => uRole === 'COMPANY_ADMIN');

      let siteGroups = [];
      if (isAdmin) {
        siteGroups = yield getCompanyAdminSiteGroups(
          companyId,
          targetCompanyId
        );
      } else {
        siteGroups = yield getUserSiteGroups(
          userId,
          companyId,
          targetCompanyId
        );
      }

      res.send(siteGroups);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getSiteGroup: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;
      const siteGroupId = req.params.id;

      if (!validateUUID(siteGroupId)) {
        req.log.info(`Invalid sitegroup id: ${siteGroupId}`);
        return next(new errors.NotFoundError('Cannot find sitegroup'));
      }

      const siteGroup = yield server.db.read.row(
        `SELECT id, name, company_id
                               FROM authorization_site_group
                               WHERE authorization_site_group.id = $1;`,
        [siteGroupId]
      );

      if (!siteGroup) {
        req.log.info(`Cannot find sitegroup: ${siteGroupId}`);
        return next(new errors.NotFoundError('Cannot find site group'));
      }

      if (siteGroup.companyId !== companyId) {
        req.log.info(
          `Company ${companyId} is not allowed to view Company ${siteGroup.companyId}`
        );
        return next(
          new errors.ForbiddenError('Not authorized to view sitegroups')
        );
      }

      siteGroup.owner = req.user.company;

      const sites = yield server.db.read.rows(
        `
                            SELECT site.site_id as id, site.name, site.address_json as address
                               FROM site
                               JOIN authorization_site_group_site asgs ON site.site_id = asgs.site_id
                               WHERE asgs.authorization_site_group_id = $1 AND site.active != false;`,
        [siteGroupId]
      );
      siteGroup.sites = sites;

      const companies = yield server.db.read.rows(
        `SELECT company.id, company.name
                               FROM company
                               JOIN authorization_site_group_company asgc ON company.id = asgc.company_id
                               WHERE asgc.authorization_site_group_id = $1;`,
        [siteGroupId]
      );
      siteGroup.companies = companies;

      req.log.info({ siteGroup }, 'Found sitegroup');

      res.send(siteGroup);

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getSiteGroupInternal: async (req, res, next) => {
    try {
      const siteGroupId = req.params.id;

      if (!validateUUID(siteGroupId)) {
        req.log.info(`Invalid sitegroup id: ${siteGroupId}`);
        return next(new errors.NotFoundError('Cannot find sitegroup'));
      }

      const siteGroup = await server.db.read.row(
        `SELECT id, name, company_id
                                   FROM authorization_site_group
                                   WHERE authorization_site_group.id = $1;`,
        [siteGroupId]
      );

      if (!siteGroup) {
        req.log.info(`Cannot find sitegroup: ${siteGroupId}`);
        return next(new errors.NotFoundError('Cannot find site group'));
      }

      siteGroup.sites = await server.db.read.rows(
        `
                                SELECT site.site_id as id, site.name, site.address_json as address
                                   FROM site
                                   JOIN authorization_site_group_site asgs ON site.site_id = asgs.site_id
                                   WHERE asgs.authorization_site_group_id = $1 AND site.active != false;`,
        [siteGroupId]
      );

      req.log.info({ siteGroup }, 'Found sitegroup');
      res.send(siteGroup);
      return next();
    } catch (e) {
      return errorHandler.onError(req, res, next)(e);
    }
  },
  createSiteGroup: (req, res, next) =>
    co(function* execute() {
      const siteGroup = req.body;
      const sites = [];
      const companies = [];

      req.log.info({ siteGroup }, 'Creating sitegroup ');

      // get my company
      const companyId = req.user.company.id;

      // verify that sitegroup name is unique
      const exists = yield server.db.read.row(
        `
                SELECT count(name) FROM authorization_site_group
                WHERE
                    LOWER(name) = LOWER($1) AND
                    company_id = $2;
            `,
        [siteGroup.name, companyId]
      );

      if (exists.count) {
        req.log.info(`Sitegroup name ${siteGroup.name} already exists`);
        return next(new errors.ConflictError('Sitegroup name must be unique'));
      }

      const seenSites = [];
      // verify that all current sites belong to my company + Check for duplicates
      // eslint-disable-next-line no-restricted-syntax
      for (const siteBody of siteGroup.sites) {
        // Check for duplicates
        if (seenSites[siteBody.id]) {
          return next(
            new errors.BadRequestError('Sites must contain unique objects')
          );
        }
        seenSites[siteBody.id] = true;

        req.log.info(`Searching sites ${siteBody.id}`);

        const site = yield server.db.read.row(
          `SELECT site_id as id, name, address_json as address
                                 FROM site
                                 WHERE site.company_id = $1
                                    AND site.site_id = $2;`,
          [companyId, siteBody.id]
        );
        if (!site) {
          return next(
            new errors.NotFoundError(`Cannot find site ${siteBody.id}.`)
          );
        }

        sites.push(site);
      }

      siteGroup.sites = sites;

      const seenCompanies = [];
      // verify that all companies have a relationship to my company + Check for duplicates
      // eslint-disable-next-line no-restricted-syntax
      for (const companyBody of siteGroup.companies) {
        // Check for duplicates
        if (seenCompanies[companyBody.id]) {
          return next(
            new errors.BadRequestError('Companies must contain unique objects')
          );
        }
        seenCompanies[companyBody.id] = true;

        req.log.info(`Searching companies ${companyBody.id}`);

        if (companyBody.id === companyId) {
          return next(
            new errors.ConflictError('Cannot add company to own sitegroup')
          );
        }

        const company = yield server.db.read.row(
          `SELECT id, name
                                  FROM company
                                  JOIN company_relationship cr ON cr.allowed_company_id = company.id
                                    AND cr.company_id = $1
                                  WHERE company.id = $2;`,
          [companyId, companyBody.id]
        );
        if (!company) {
          return next(
            new errors.NotFoundError(`Cannot find company ${companyBody.id}.`)
          );
        }

        companies.push(company);
      }
      siteGroup.companies = companies;

      // save the site group ( initiate a transaction )
      const connection = yield server.db.write.getConnection();
      try {
        // begin transaction
        yield connection.execute('BEGIN');

        // create a site-group
        const result = yield connection.execute(
          `INSERT INTO authorization_site_group( id, name, company_id, created )
                          VALUES ( $1, $2, $3, now() )
                          RETURNING id, name, company_id;`,
          [uuid(), siteGroup.name, companyId]
        );
        const newSiteGroup = result.rows[0];
        req.log.info({ newSiteGroup }, 'Created site group');
        siteGroup.id = newSiteGroup.id;
        siteGroup.owner = req.user.company;

        // relate site-group with the companies
        // eslint-disable-next-line no-restricted-syntax
        for (const site of sites) {
          req.log.info({ site }, 'Connecting site');
          yield connection.execute(
            `INSERT INTO
                          authorization_site_group_site( authorization_site_group_id, site_id )
                          VALUES ($1, $2);`,
            [newSiteGroup.id, site.id]
          );
        }

        // relate site-group with the sites
        // eslint-disable-next-line no-restricted-syntax
        for (const company of companies) {
          req.log.info({ company }, 'Connecting company');
          yield connection.execute(
            `INSERT INTO
                           authorization_site_group_company( authorization_site_group_id, company_id )
                           VALUES ($1, $2);`,
            [newSiteGroup.id, company.id]
          );
        }
        refreshMVUserSiteAuthorization('createSiteGroup');

        // commit transaction
        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        throw err;
      }

      // add this sitegroup
      res.send(200, siteGroup);

      const siteGroupId = siteGroup.id;
      const siteIds = yield getSiteIdsByAuthSiteGroupId(siteGroupId);

      yield sendEventsToEventBridge({
        siteGroupId,
        tenantId: companyId,
        siteIds: siteIds && siteIds.length ? siteIds.map(({ id }) => id) : [],
        type: 'create',
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  editSiteGroup: (req, res, next) =>
    co(function* execute() {
      const siteGroupId = req.params.id;
      const siteGroup = req.body;
      siteGroup.id = siteGroupId;

      const sites = [];
      const companies = [];

      req.log.info({ siteGroup }, 'Editing sitegroup');

      const companyId = req.user.company.id;

      // verify that sitegroup name is unique excluding the sitegroup currently being edited
      const exists = yield server.db.read.row(
        `
                SELECT count(name) FROM authorization_site_group
                WHERE
                    id != $1 AND
                    LOWER(name) = LOWER($2) AND
                    company_id = $3;
            `,
        [siteGroupId, siteGroup.name, req.user.company.id]
      );

      if (exists.count) {
        req.log.info(`Sitegroup name ${siteGroup.name} already exists`);
        return next(new errors.ConflictError('Sitegroup name must be unique'));
      }

      // verify that this is a real sitegroup
      const siteGroupFromDB = yield server.db.read.row(
        `SELECT id FROM authorization_site_group
                                      WHERE id = $1 AND company_id = $2`,
        [siteGroupId, companyId]
      );
      if (!siteGroupFromDB) {
        req.log.info(`Cannot find sitegroup ${siteGroupId}`);
        return next(new errors.NotFoundError('Cannot find sitegroup'));
      }

      // verify that all current sites belong to my company
      // eslint-disable-next-line no-restricted-syntax
      for (const siteBody of siteGroup.sites) {
        req.log.info(`Searching sites ${siteBody.id}`);

        const site = yield server.db.read.row(
          `SELECT site_id, name, company_id
                                 FROM site
                                 WHERE site.company_id = $1
                                    AND site.site_id = $2;`,
          [companyId, siteBody.id]
        );
        if (!site) {
          return next(
            new errors.NotFoundError(`Cannot find site ${siteBody.id}.`)
          );
        }

        sites.push(site);
      }
      siteGroup.sites = sites;

      // verify that all companies have a relationship to my company
      // eslint-disable-next-line no-restricted-syntax
      for (const companyBody of siteGroup.companies) {
        req.log.info(`Searching companies ${companyBody.id}`);

        if (companyBody.id === companyId) {
          return next(
            new errors.ConflictError('Cannot add company to own sitegroup')
          );
        }

        const company = yield server.db.read.row(
          `SELECT id, name
                                  FROM company
                                  JOIN company_relationship cr ON cr.allowed_company_id = company.id
                                    AND cr.company_id = $1
                                  WHERE company.id = $2;`,
          [companyId, companyBody.id]
        );
        if (!company) {
          return next(
            new errors.NotFoundError(`Cannot find company ${companyBody.id}.`)
          );
        }

        companies.push(company);
      }
      siteGroup.companies = companies;

      // EDIT
      const connection = yield server.db.write.getConnection();
      try {
        // begin transaction
        yield connection.execute('BEGIN');

        // create a site-group
        const result = yield connection.execute(
          `UPDATE authorization_site_group
                          SET name = $1
                          WHERE authorization_site_group.id = $2
                          RETURNING id, name, company_id;`,
          [siteGroup.name, siteGroupFromDB.id]
        );
        const editedSiteGroup = result.rows[0];
        req.log.info({ editedSiteGroup }, 'Updated site group');

        // delete all site-group connections for sites
        yield connection.execute(
          `DELETE FROM authorization_site_group_site
                          WHERE authorization_site_group_id = $1;`,
          [editedSiteGroup.id]
        );

        // delete all site-group connections for companies
        yield connection.execute(
          `DELETE FROM authorization_site_group_company
                          WHERE authorization_site_group_id = $1;`,
          [editedSiteGroup.id]
        );

        // create new site-group relationships to sites
        // eslint-disable-next-line no-restricted-syntax
        for (const site of sites) {
          req.log.info({ site }, 'Connecting site');
          yield connection.execute(
            `INSERT INTO
                          authorization_site_group_site( authorization_site_group_id, site_id )
                          VALUES ($1, $2);`,
            [editedSiteGroup.id, site.siteId]
          );
        }

        // create new site-group relationships to companies
        // eslint-disable-next-line no-restricted-syntax
        for (const company of companies) {
          req.log.info({ company }, 'Connecting company');
          yield connection.execute(
            `INSERT INTO
                           authorization_site_group_company( authorization_site_group_id, company_id )
                           VALUES ($1, $2);`,
            [editedSiteGroup.id, company.id]
          );
        }

        refreshMVUserSiteAuthorization('updateSiteGroup');

        // commit transaction
        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        throw err;
      }

      res.send(200, siteGroup);

      const siteIds = yield getSiteIdsByAuthSiteGroupId(siteGroupId);

      yield sendEventsToEventBridge({
        siteGroupId,
        tenantId: companyId,
        siteIds: siteIds && siteIds.length ? siteIds.map(({ id }) => id) : [],
        type: 'update',
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  deleteSiteGroup: (req, res, next) =>
    co(function* execute() {
      const siteGroupId = req.params.id;
      req.log.info(`Deleting sitegroup ${siteGroupId}`);

      const companyId = req.user.company.id;

      // verify that this is a real sitegroup
      const siteGroupFromDB = yield server.db.read.row(
        `SELECT id FROM authorization_site_group
                                      WHERE id = $1 AND company_id = $2;`,
        [siteGroupId, companyId]
      );
      if (!siteGroupFromDB) {
        req.log.info(`Cannot find sitegroup ${siteGroupId}`);
        return next(new errors.NotFoundError('Cannot find sitegroup'));
      }

      // verify that I have no sites
      const sites = yield server.db.read.rows(
        `SELECT site_id FROM authorization_site_group_site
                                      WHERE authorization_site_group_id = $1;`,
        [siteGroupId]
      );
      if (sites.length > 0) {
        req.log.info(
          `Trying to delete but sitegroup ${siteGroupId} has ${sites.length} sites`
        );
        return next(new errors.ConflictError('SiteGroup must contain 0 sites'));
      }

      // begin delete transaction
      const connection = yield server.db.write.getConnection();
      try {
        // begin transaction
        yield connection.execute('BEGIN');

        // delete all site-group connections for sites
        yield connection.execute(
          `DELETE FROM authorization_site_group_site
                          WHERE authorization_site_group_id = $1;`,
          [siteGroupId]
        );

        // delete all site-group connections for companies
        yield connection.execute(
          `DELETE FROM authorization_site_group_company
                          WHERE authorization_site_group_id = $1;`,
          [siteGroupId]
        );

        // delete all site-group connections for teams
        yield connection.execute(
          `DELETE FROM user_group_authorization_site_group
                          WHERE authorization_site_group_id = $1`,
          [siteGroupId]
        );

        // delete this sitegroup
        yield connection.execute(
          `DELETE FROM authorization_site_group
                          WHERE id = $1;`,
          [siteGroupId]
        );

        refreshMVUserSiteAuthorization('deleteSiteGroup');

        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        throw err;
      }
      res.send(204);

      yield sendEventsToEventBridge({
        siteGroupId,
        tenantId: companyId,
        siteIds: [],
        type: 'delete',
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
