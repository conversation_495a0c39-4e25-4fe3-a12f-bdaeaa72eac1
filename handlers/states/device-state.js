const co = require('co');
const restify = require('restify');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');

function isDeviceVisibleToUser(deviceId, sub) {
  return co(function* execute() {
    const deviceVisibleToUserQuery = `
                SELECT *
                FROM user_site_authorization usa
                  LEFT JOIN target t ON usa.site_id = t.site_id
                WHERE t.target_id = $1 AND usa.user_id = $2`;
    const deviceVisibleToUser = yield server.db.read.row(
      deviceVisibleToUserQuery,
      [deviceId, sub]
    );
    return deviceVisibleToUser;
  }).catch(err => {
    throw err;
  });
}

function isSiteVisibleToUser(siteId, sub) {
  return co(function* execute() {
    const siteVisibleToUserQuery = `
                SELECT *
                FROM user_site_authorization WHERE site_id = $1 AND user_id = $2`;
    const siteVisibleToUser = yield server.db.read.row(siteVisibleToUserQuery, [
      siteId,
      sub,
    ]);
    return siteVisibleToUser;
  }).catch(err => {
    throw err;
  });
}

module.exports = {
  getDeviceAlarms: (req, res, next) =>
    co(function* execute() {
      const isSystemUser = req.user.roles.includes('ICS_SYSTEM');
      const deviceId = req.params.id;
      const queryParams = [];
      queryParams.push(deviceId);

      if (!isSystemUser) {
        const deviceVisibleToUser = yield isDeviceVisibleToUser(
          deviceId,
          req.user.sub
        );
        if (!deviceVisibleToUser) {
          return next(
            new restify.NotFoundError(`Device with id ${deviceId} not found`)
          );
        }
      }

      const query = `
                SELECT
                  device_id,
                  site_id,
                  code,
                  component,
                  modified,
                  status
                FROM device_alarms
                WHERE device_id = $1
            `;
      const result = yield server.db.read.rows(query, queryParams);
      res.send(result);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
  getSiteAlarms: (req, res, next) =>
    co(function* execute() {
      const isSystemUser = req.user.roles.includes('ICS_SYSTEM');
      const siteId = req.params.id;
      const { state } = req.params;

      if (!isSystemUser) {
        const siteVisibleToUser = yield isSiteVisibleToUser(
          siteId,
          req.user.sub
        );
        if (!siteVisibleToUser) {
          return next(
            new restify.NotFoundError(`Site with id ${siteId} not found`)
          );
        }
      }

      const query = `
                SELECT
                  site_id,
                  code,
                  modified,
                  status
                FROM site_alarms
                WHERE site_id = $1
            `;
      const params = [siteId];
      let result;
      if (state) {
        params.push(state);
        result = yield server.db.read.row(query, params);
        if (result === null) {
          return next(
            new restify.NotFoundError(
              `Cannot find state ${state} for site ${siteId}`
            )
          );
        }
      } else {
        result = yield server.db.read.rows(query, params);
      }

      res.send(result);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
  getSiteDevicesAlarms: (req, res, next) =>
    co(function* execute() {
      const isSystemUser = req.user.roles.includes('ICS_SYSTEM');
      const siteId = req.params.id;
      const queryParams = [];
      queryParams.push(siteId);

      if (!isSystemUser) {
        const siteVisibleToUser = yield isSiteVisibleToUser(
          siteId,
          req.user.sub
        );
        if (!siteVisibleToUser) {
          return next(
            new restify.NotFoundError(`Site with id ${siteId} not found`)
          );
        }
      }

      let query = `
                SELECT
                  device_id,
                  site_id,
                  code,
                  component,
                  modified,
                  status
                FROM device_alarms
                WHERE site_id = $1
            `;
      query += ' ORDER BY device_id';
      const result = yield server.db.read.rows(query, queryParams);
      res.send(result);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
