const url = require('url');
const fs = require('fs');
const { Readable } = require('stream');
const _ = require('lodash');
const co = require('co');
const restify = require('restify');

const env = require('../env');

const { config } = env;
const { server } = require('../app');
const aws = require('../lib/aws');
const jobHelper = require('../helpers/job-helper');
const gstvHelper = require('../helpers/gstv-helper');
const errorHandler = require('../lib/errorhandler');
const usersHelper = require('../helpers/users-helper');
const keyRequestHelper = require('../helpers/key-request-helper');
const ipAddressHelper = require('../helpers/ip-address-helper');
const companyHelper = require('../helpers/company-helper');
const deviceHeartBeatHelper = require('../helpers/deviceHeartBeatHelper');
const mfaJobConstants = require('../src/entities/api/device/mfa/jobs/mfa-jobs.constant');
const {
  public: fileuploadRequestService,
} = require('../src/file-upload/api/fileupload-request/fileupload-request.service');
const { dbTransaction } = require('../lib/app-constants');
const logger = require('../lib/logger').mainLogger();
const { fetchCertificates } = require('../helpers/device-helper');
const {
  deviceOnboardingAdapter,
} = require('../helpers/deviceOnboarding-helper');
const {
  generateChallengeResponse,
  sanitiseHSMError,
} = require('./challenge-response');
const {
  createConfigUpdateJob,
  getSignatureMappings,
} = require('./jobs.service');

const hasIcsSystemRole = req => {
  const userRoles = req.user.getRoles();
  return userRoles.includes('ICS_SYSTEM');
};

const findDependensJob = async (connection, jobId, continueOnFail) => {
  const dependency = await connection.execute(
    `
            SELECT jd.job_id
            FROM job_Dependency jd
            WHERE
                jd.dependens_on = $1 AND
                jd.continue_on_fail = $2`,
    [jobId, continueOnFail]
  );

  return dependency.rows[0];
};

const getJob = async (jobId, inConnection) => {
  let connection = inConnection;
  let needToClose = false;
  try {
    if (!connection) {
      connection = await server.db.read.getConnection();
      needToClose = true;
    }

    const query = `
      SELECT DISTINCT
          j.id, j.device_id, j.destination, j.type, j.status, j.expiry AS expiration,
          j.embargo, j.data, j.created_on, u.full_name AS created_by, u.company_id
      FROM job j
      JOIN ics_user u ON j.created_by = u.id
      WHERE
          j.id = $1
    `;

    const job = await connection.execute(query, [jobId]);
    if (needToClose) {
      connection.done();
    }

    return job.rows[0];
  } catch (err) {
    if (needToClose) {
      connection.done();
    }
    throw err;
  }
};

const getJobWithHistory = async (jobId, inConnection) => {
  let connection = inConnection;
  let needToClose = false;
  try {
    if (!connection) {
      connection = await server.db.read.getConnection();
      needToClose = true;
    }

    const query = `
      SELECT
        j.id,
        j.device_id,
        j.destination,
        j.type,
        j.status,
        j.expiry AS expiration,
        j.embargo,
        j.data,
        j.created_on,
        j.updated,
        u.full_name AS created_by,
        CASE
          WHEN j.status = $2 THEN (SELECT jsh.message FROM job_status_history jsh WHERE jsh.job_id  = j.id ORDER by jsh.created_at desc LIMIT 1)
          ELSE ''
        END as message,
        s.name AS job_status_name
      FROM
        public.job j
        JOIN public.ics_user u ON j.created_by = u.id
        JOIN public.job_status s ON j.status = s.id
      WHERE
        j.id = $1 AND
        j.type IN ('sys.configfile-update', 'sys.reboot', 'sys.restart')
      LIMIT 1;
    `;

    const job = await connection.execute(query, [
      jobId,
      jobHelper.jobStatus.FAILED,
    ]);

    if (needToClose) {
      connection.done();
    }

    return job.rows[0];
  } catch (err) {
    if (needToClose) {
      connection.done();
    }
    throw err;
  }
};

const getDependentJob = async (connection, jobId, continueOnFail) => {
  const dependency = await findDependensJob(connection, jobId, continueOnFail);
  if (dependency) {
    // eslint-disable-next-line no-return-await
    return await getJob(dependency.jobId, connection);
  }
  return undefined;
};

const updateJobAndInsertHistory = async (
  connection,
  job,
  status,
  historyMessage,
  data,
  fpsJobStatusEventList
) => {
  const now = new Date();
  await connection.execute(
    `
            UPDATE job
            SET status = $1, updated = $2
            WHERE id = $3 `,
    [status, now, job.id]
  );

  if (jobHelper.isFpsJobs(job) && jobHelper.okToSendFpsJobStatus()) {
    fpsJobStatusEventList.push({
      failReason: historyMessage || null,
      jobDestination: job.destination,
      jobType: job.type,
      jobId: job.id,
      jobStatus: status,
      updatedBy: null,
    });
  }
  if (
    job.type === 'playlist.update' &&
    env.config.enablePlaylistStatusHistoryToFirehose
  ) {
    const playlistJobHistory = [
      {
        jobId: job.id,
        status,
        message: historyMessage,
        createdAt: new Date().toISOString(),
      },
    ];
    logger.debug(playlistJobHistory, `putting playlist job to firehose`);
    jobHelper.sendPlaylistStatusHistoryToFirehose(playlistJobHistory);
  } else {
    await connection.execute(
      `
            INSERT INTO job_status_history
                (job_id, status, message, created_at, data)
            VALUES ( $1, $2, $3, $4, $5);`,
      [job.id, status, historyMessage, now, data]
    );
  }
};

const updateJobDataStatusAndInsertHistory = async (
  connection,
  jobId,
  jobData,
  status,
  historyMessage,
  data
) => {
  const now = new Date();

  await connection.execute(
    `
            UPDATE job
            SET status = $1, updated = $2, data = $3
            WHERE id = $4 `,
    [status, now, jobData, jobId]
  );

  await connection.execute(
    `
            INSERT INTO job_status_history
                (job_id, status, message, created_at, data)
            VALUES ( $1, $2, $3, $4, $5);`,
    [jobId, status, historyMessage, now, data]
  );
};

const cancelThisJob = async ({
  req,
  connection,
  job,
  userName,
  message,
  fpsJobStatusEventList,
}) => {
  // Update the job
  await connection.execute(
    `
            UPDATE job
            SET
                status = $1,
                updated = now()
            WHERE
                id = $2`,
    [jobHelper.jobStatus.CANCELLED, job.id]
  );

  if (
    fpsJobStatusEventList &&
    jobHelper.isFpsJobs(job) &&
    jobHelper.okToSendFpsJobStatus()
  ) {
    fpsJobStatusEventList.push({
      jobDestination: job.destination,
      failReason: message,
      jobType: job.type,
      jobId: job.id,
      jobStatus: jobHelper.jobStatus.CANCELLED,
      updatedBy: userName,
    });
  }

  if (job.bulkOperationItemId)
    await connection.execute(
      `
            UPDATE bulk_operation_item
            SET
                bulk_operation_status = $1,
                date_updated = now()
            WHERE
                id = $2`,
      [jobHelper.jobStatus.CANCELLED, job.bulkOperationItemId]
    );

  // Insert into job status history
  await connection.execute(
    `
            INSERT INTO job_status_history
                (job_id, status, message, created_at)
            VALUES ( $1, $2, $3, now() );`,
    [job.id, jobHelper.jobStatus.CANCELLED, message]
  );

  // Send GSTV notification
  if (job.type === jobHelper.jobType.PLAYLIST_UPDATE && env.config.GSTV) {
    const queue = _.filter(
      env.config.GSTV.dataUpload,
      x => x.queueType === 'reporting'
    );
    if (!_.isEmpty(queue) && queue[0].url) {
      await gstvHelper.sendGstvNotificationIfJobIsTerminal(
        connection,
        queue,
        job,
        5
      );
    } else {
      req.log.warn(
        `Did not send GSTV notification for job ${job.id} due to 'reporting' queue not specified`
      );
    }
  }

  // Cancel dependencies
  const dependency = await findDependensJob(connection, job.id, false);
  if (dependency) {
    const jobdDepend = await getJob(dependency.jobId, connection);
    await cancelThisJob({
      req,
      connection,
      job: jobdDepend,
      userName,
      message: `Cancelled by dependency job ${job.id}`,
      fpsJobStatusEventList,
    });
  }
};

const updateThisJob = async (
  req,
  connection,
  job,
  newStatus,
  message,
  data,
  fpsJobStatusEventList
) => {
  await updateJobAndInsertHistory(
    connection,
    job,
    newStatus,
    message,
    data,
    fpsJobStatusEventList
  );

  // Send GSTV notification - update job status no longer sending gstv message

  if (job.type === jobHelper.jobType.FUTUREX_KEYLOAD_RKI) {
    await keyRequestHelper.updateKeyRequestAndSendNotifications(
      req,
      connection,
      job,
      newStatus,
      message
    );
  }

  // Cascade dependencies for Fail and Cancel only!!
  if (
    newStatus === jobHelper.jobStatus.FAILED ||
    newStatus === jobHelper.jobStatus.CANCELLED
  ) {
    const dependency = await findDependensJob(connection, job.id, false);
    if (dependency !== undefined) {
      const jobdDepend = await getJob(dependency.jobId, connection);
      await updateThisJob(
        req,
        connection,
        jobdDepend,
        newStatus,
        `Updated by dependency job ${job.id}`,
        data,
        fpsJobStatusEventList
      );
    }
  }
};

const getCancelableJobsWithUser = async ({
  bulkOperationId,
  jobIds,
  user,
  jobStatusFilter,
}) => {
  let cancellableJobs;

  if (bulkOperationId) {
    // Get jobs by bulk id with site authorization
    cancellableJobs = await server.db.read.rows(
      `SELECT
          j.id,
          j.type,
          j.destination,
          j.device_id,
          j.status,
          j.updated,
          js.name AS job_status_name
      FROM bulk_operation bo
          JOIN bulk_operation_item boi ON boi.bulk_operation_id = bo.id
          JOIN job j ON j.id = boi.job_id
          JOIN job_status js ON j.status = js.id
          JOIN target t ON j.device_id = t.target_id
          JOIN site s ON s.site_id = t.site_id
          JOIN user_site_authorization usa ON usa.site_id = s.site_id
      WHERE bo.id = $1
          AND usa.user_id = $2
          AND j.status <= $3
      `,
      [bulkOperationId, user.sub, jobStatusFilter]
    );
  }

  if (jobIds) {
    const companyId = user.company.id;
    const jobType = 'sys.configfile-update';
    cancellableJobs = await server.db.read.rows(
      `SELECT
              j.id,
              j.type,
              j.destination,
              j.device_id,
              j.status,
              j.updated,
              js.name AS job_status_name
          FROM job j
              INNER JOIN target t on j.device_id = t.target_id
              INNER JOIN site s on s.site_id = t.site_id
              INNER JOIN job_status js ON j.status = js.id
          WHERE s.company_id = $1
              AND j.id = ANY ($2::uuid[])
              AND j.type = $3
              AND j.status = $4
        `,
      [companyId, jobIds, jobType, jobStatusFilter]
    );
  }

  const fullUser = (await usersHelper.getFullUser([user.sub]))[0];

  return {
    user: fullUser,
    cancellableJobs,
  };
};

/**
 * Trigger and update to job ref by jobId if all upload request is completed
 *
 * @param {string} jobId
 * @returns {Promise<void>}
 */
const finalizeUploadRequest = async jobId => {
  const batch = await server.db.read.row(
    `
            select
                fur.id,
                (fur.completed isnull and not fur.cancelled and not fur.packaging_in_progress ) package,
                c.jobs,
                c.completed
            from file_upload_request fur
            inner join lateral (
                select
                    count(*) filter ( where j.status >= 3 ) completed,
                    count(*) jobs
                from job j
                inner join file_upload_job fuj on j.id = fuj.job_id
                where fuj.request_id = fur.id
            ) c on true
            where fur.id = (
                select distinct fr.id
                from file_upload_request fr
                inner join file_upload_job fj on fr.id = fj.request_id
                where fj.job_id = $1
            )
        `,
    [jobId]
  );

  // TODO: It can be null?! might be related to move/sync...?
  if (batch) {
    server.log.info(
      `FileUploadRequest(${batch.id}) progress ${batch.completed}/${batch.jobs}`
    );
  }

  // Only notify java about completed and yet to be packaged requests, with jobs that have progressed to completion...
  if (
    batch &&
    batch.package &&
    batch.jobs > 0 &&
    batch.jobs === batch.completed
  ) {
    try {
      await fileuploadRequestService.packageFiles();
    } catch (err) {
      server.log.error(
        `FileUploadRequest(${batch.id}) error: ${err.message}\n\n${err.stack}`
      );
    }
  }
};

const getDeviceFiles = async (jobId, inConnection) => {
  let connection = inConnection;
  let needToClose = false;

  try {
    if (!connection) {
      connection = await server.db.read.getConnection();
      needToClose = true;
    }

    const query = `
      SELECT df.*, fuj.request_id
      FROM device_files df
      JOIN file_upload_job fuj ON
   	  df.latest_upload_job = fuj.job_id 
      WHERE
          df.latest_upload_job = $1
    `;

    const deviceFiles = await connection.execute(query, [jobId]);

    logger.info(`log from getDeviceFiles ${JSON.stringify(deviceFiles)}`);

    if (needToClose) {
      connection.done();
    }

    if (!deviceFiles.rows || deviceFiles.rows.length === 0) {
      throw new Error(`No device files found for job ID: ${jobId}`);
    }

    return deviceFiles.rows;
  } catch (err) {
    logger.info(`comming here down error ${err}`);
    if (needToClose && connection) {
      try {
        connection.done();
      } catch (closeErr) {
        logger.error(`Error releasing connection: ${closeErr.message}`);
      }
    }
    logger.error('Error fetching device files:', err);
    throw err;
  }
};

const getProcessAuthTokenJob = async (connection, newStatus, job) => {
  const dependentJob = await getDependentJob(connection, job.id, false);
  if (
    dependentJob &&
    dependentJob.destination === mfaJobConstants.INVENCO_SYSTEM &&
    dependentJob.type === mfaJobConstants.PROCESS_AUTH_OPERATION
  ) {
    return dependentJob;
  }

  return undefined;
};

const updateProcessAuthTokenJob = async (
  connection,
  processAuthTokenJob,
  challengeRes,
  fpsJobStatusEventList
) => {
  let authResponseCode = null;
  const tamperChallengeConfig = env.config.tamperChallenge;
  const device = await server.db.read.row(
    `
              SELECT
                  t.target_id as device_id, t.serial_number as serial_number
              FROM target t
              WHERE t.target_id = $1;
    `,
    [processAuthTokenJob.deviceId]
  );
  if (!device) {
    throw new Error(`Device Not Found for Id: ${processAuthTokenJob.deviceId}`);
  }

  try {
    authResponseCode = await generateChallengeResponse(
      tamperChallengeConfig.key,
      tamperChallengeConfig.user,
      tamperChallengeConfig.slot,
      device.serialNumber,
      challengeRes.token,
      mfaJobConstants.MERCHANT_RESET
    );
  } catch (err) {
    sanitiseHSMError(
      err,
      tamperChallengeConfig.key,
      tamperChallengeConfig.user
    );
    throw err;
  } finally {
    const authTokenData = {
      token: authResponseCode,
    };
    const jobData = JSON.parse(processAuthTokenJob.data);
    jobData.token = authTokenData.token;

    updateJobDataStatusAndInsertHistory(
      connection,
      processAuthTokenJob.id,
      JSON.stringify(jobData),
      processAuthTokenJob.status,
      'Process Auth Token Generated',
      authTokenData,
      fpsJobStatusEventList
    );
  }
};
const getJobNewBulkStatus = (status, job) => {
  if (job.type === mfaJobConstants.SYS_GENERATE_CHALLENGE_TOKEN) {
    switch (status) {
      case jobHelper.jobStatus.ACCEPT:
      case jobHelper.jobStatus.IN_PROGRESS:
        return jobHelper.jobStatus.IN_PROGRESS;
      case jobHelper.jobStatus.CANCELLED:
      case jobHelper.jobStatus.FAILED:
        return status;
      default:
        break;
    }
  } else if (job.type === mfaJobConstants.PROCESS_AUTH_OPERATION) {
    switch (status) {
      case jobHelper.jobStatus.COMPLETE:
      case jobHelper.jobStatus.CANCELLED:
      case jobHelper.jobStatus.FAILED:
        return status;
      default:
        break;
    }
  }
  return undefined;
};

const getJobDependency = async job =>
  // eslint-disable-next-line no-return-await
  await server.db.read.rows(
    `     SELECT
               jd.dependens_on, j.status, jd.continue_on_fail
          FROM job_dependency jd
          JOIN job j ON jd.dependens_on = j.id
          WHERE
                jd.job_id = $1
    `,
    [job.id]
  );

const updateBulkOperationStatus = async (connection, newStatus, job) => {
  const bulkStatus = getJobNewBulkStatus(newStatus, job);
  if (bulkStatus) {
    let bulkJobId = job.id;
    if (job.type === mfaJobConstants.PROCESS_AUTH_OPERATION) {
      const jobsDepended = await getJobDependency(job);
      if (!_.isEmpty(jobsDepended)) {
        bulkJobId = jobsDepended[0].dependensOn;
      }
    }
    // update bulk_operation_item with new status
    await connection.execute(
      `
              UPDATE bulk_operation_item
              SET
                  date_updated = NOW(),
                  bulk_operation_status = $3
              WHERE
                  device_id = $1 AND
                  job_id = $2
          `,
      [job.deviceId, bulkJobId, bulkStatus]
    );
  }
};

const removeS3BucketPrefix = path => {
  try {
    const s3Bucket = config.fileUpload.bucket;
    const s3BucketPrefix = `s3://${s3Bucket}/`;
    if (!path) {
      throw new Error('Path is missing.');
    }
    return path.replace(s3BucketPrefix, '');
  } catch (error) {
    logger.error('Error in removeS3BucketPrefix:', error);
    throw error;
  }
};
const publishJobToSqs = ({
  tenantId,
  job,
  queueType = 'updateDeploymentStatusQueue',
}) => {
  const {
    id: jobId,
    deviceId,
    type: jobType,
    status,
    updated: updatedAt,
    message,
  } = job;

  const messageGroupId = jobId;

  const queueMessage = {
    tenantId,
    jobId,
    deviceId,
    jobType,
    status,
    updatedAt,
    ...(message && { message }),
  };

  return aws.publishSqsMessage(queueMessage, messageGroupId, queueType);
};

module.exports = {
  updateAlarmRulesSettings: async (connection, job, newStatus) => {
    let response = null;
    if (job.type === jobHelper.jobType.ROLLOUT_INSTALL) {
      let value = null;
      if (newStatus === jobHelper.jobStatus.IN_PROGRESS) {
        response = true; // SUSPEND ALARMS
        value = `NOW() + INTERVAL '${config.deviceInitiatedAlarmSuspensionTimeoutInMinutes} MINUTES'`;
      }
      if (
        newStatus === jobHelper.jobStatus.COMPLETE ||
        newStatus === jobHelper.jobStatus.FAILED
      ) {
        response = false; // RESUME ALARMS
        value = 'null';
      }
      const query = `
                INSERT INTO ics_alarm.alarm_rules_settings
                (
                    site_id,
                    suspended_by_device_until,
                    device_id
                )
                VALUES
                (
                      (
                          SELECT site_id FROM target
                          WHERE
                              target_id = $1 AND active = true AND delete_timestamp IS NULL
                      ),
                      ${value},
                      $1
                 )
                ON CONFLICT (device_id)
                DO UPDATE SET
                site_id = ( SELECT site_id FROM target WHERE target_id = $1 AND active = true AND delete_timestamp IS NULL ),
                suspended_by_device_until = ${value};`;

      await server.db.write.execute(query, [job.deviceId]);
    }
    return response; // DO NOTHING
  },
  createJob: (req, res, next) => {
    const {
      deviceId,
      createdBy,
      destination,
      type,
      data,
      dependencies,
      embargo: embargoTime,
      expiration: expiry,
    } = req.body;
    const userId = createdBy || req.user.sub;
    const userRoles = req.user.getRoles();

    const embargo = embargoTime || new Date(Date.now()).toISOString();
    let job = null;

    return co(function* execute() {
      req.log.info(
        { deviceId, userId, jobType: type },
        `[Jobs].[CreateJob] Creating job for deviceId: ${deviceId}.`
      );

      // Check role
      let isIcsUser = false;
      // eslint-disable-next-line no-restricted-syntax
      for (const role of userRoles) {
        if (role === 'ICS_SYSTEM') {
          isIcsUser = true;
          break;
        }
      }

      if (!isIcsUser) {
        const device = yield server.db.read.row(
          `
                    SELECT
                        d.target_id as device_id, s.company_id
                    FROM target d
                    JOIN site s ON d.site_id = s.site_id
                    JOIN user_site_authorization usa ON d.site_id = usa.site_id
                    WHERE
                        d.target_id = $1 AND d.active IS true AND usa.user_id = $2;`,
          [deviceId, userId]
        );

        res.resourceOwner = device ? device.companyId : null; // eslint-disable-line no-param-reassign

        if (!device) {
          return next(
            new restify.NotFoundError(`Device with id ${deviceId} not found`)
          );
        }
      }

      // Save Jobs
      // Get a connection for transaction control
      const connection = yield server.db.write.getConnection();
      try {
        // Start transaction
        yield connection.execute('BEGIN');

        // Create the job
        job = yield jobHelper.createJob(connection, {
          deviceId,
          destination,
          type,
          data,
          embargo,
          expiry,
          userId,
        });

        // Insert into job dependencies
        if (dependencies) {
          const depend = [];
          for (let i = 0; i < dependencies.length; i++) {
            const dependencyDto = dependencies[i];
            const jobDependOn = yield connection.execute(
              'SELECT j FROM job j WHERE j.id = $1;',
              [dependencyDto.jobId]
            );
            const jobDepended = jobDependOn.rows[0];

            if (!jobDepended) {
              throw new restify.NotFoundError(
                `Job ${dependencyDto.jobId} not found`
              );
            }

            const jobDependencyResult = yield connection.execute(
              `
                            INSERT INTO job_dependency
                                ( job_id, dependens_on, continue_on_fail )
                            VALUES ( $1, $2, $3 )
                            RETURNING
                                dependens_on AS job_id, continue_on_fail AS run_on_fail;`,
              [job.id, dependencyDto.jobId, dependencyDto.runOnFail]
            );

            const dependency = jobDependencyResult.rows[0];
            depend.push(dependency);
          }

          // Add into dependencies array
          job.dependencies = depend;
        }

        // Commit transaction
        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error(
          { error: err, deviceId, userId, jobType: type },
          `[Jobs].[CreateJob] error: ${err.message}.`
        );
        yield connection.execute('ROLLBACK');
        connection.done();
        throw err;
      }

      req.log.info(
        { deviceId, userId, jobType: type, jobId: job.id },
        `[Jobs].[CreateJob] Job ${job.id} is created by ${req.user.fullName}.`
      );
      res.send(job);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
  getJobs: (req, res, next) => {
    const { deviceId } = req.params;
    const startTime = req.params.start;
    const endTime = req.params.end;
    const companyId = req.user.company.id;
    const statuses = [];
    const { embargoStart, embargoEnd, destination, siteId } = req.params;

    if (req.params.status) {
      const tokens = req.params.status.split(',');
      if (tokens) {
        for (let i = 0; i < tokens.length; i++) {
          const status = parseInt(tokens[i], 10);
          if (Number.isNaN(status)) {
            return next(
              new restify.ConflictError(
                'Invalid status value; Not array of Integers'
              )
            );
          }
          statuses.push(status);
        }
      }
    }

    const types = req.params.types ? req.params.types.split(',') : [];

    return co(function* execute() {
      let query = `
                WITH t_company AS (SELECT cr.company_id
                    FROM company_relationship cr
                    WHERE cr.allowed_company_id = $1
                    UNION
                    SELECT $1)
                SELECT DISTINCT
                    j.id,
                    j.device_id,
                    j.destination,
                    j.type,
                    j.status,
                    j.expiry as expiration,
                    j.embargo,
                    j.data,
                    j.created_on,
                     CASE
                        WHEN u.type = 'SYSTEM' THEN 'SYSTEM_USER'
                        ELSE u.full_name
                     END as created_by
                FROM job j
                JOIN target t ON j.device_id = t.target_id
                JOIN site s ON t.site_id = s.site_id
                JOIN user_site_authorization usa ON s.site_id = usa.site_id
                JOIN ics_user u ON u.id = j.created_by
                JOIN t_company tc ON tc.company_id = u.company_id OR u.company_id is null
                WHERE 1=1
            `;

      let numberOfParam = 1;
      const params = [companyId];

      if (deviceId) {
        numberOfParam += 1;
        query = `${query} AND j.device_id = $${numberOfParam}`;
        params.push(deviceId);
      }

      if (startTime) {
        numberOfParam += 1;
        query = `${query} AND j.created_on >= $${numberOfParam}`;
        params.push(startTime);
      }

      if (endTime) {
        numberOfParam += 1;
        query = `${query} AND j.created_on <= $${numberOfParam}`;
        params.push(endTime);
      }

      if (!_.isEmpty(statuses)) {
        numberOfParam += 1;
        query = `${query} AND j.status = ANY($${numberOfParam})`;
        params.push(statuses);
      }

      if (embargoStart) {
        numberOfParam += 1;
        query = `${query} AND j.embargo >= $${numberOfParam}`;
        params.push(embargoStart);
      }

      if (embargoEnd) {
        numberOfParam += 1;
        query = `${query} AND j.embargo <= $${numberOfParam}`;
        params.push(embargoEnd);
      }

      if (destination) {
        numberOfParam += 1;
        query = `${query} AND j.destination = $${numberOfParam}`;
        params.push(destination);
      }

      if (siteId) {
        numberOfParam += 1;
        query = `${query} AND s.site_id = $${numberOfParam}`;
        params.push(siteId);
      }

      if (!_.isEmpty(types)) {
        numberOfParam += 1;
        query = `${query} AND j.type = ANY($${numberOfParam})`;
        params.push(types);
      }

      query += ' ORDER BY j.created_on';
      let jobs = yield server.db.read.rows(query, params);

      jobs = _.filter(jobs, j => j.type !== jobHelper.jobType.PLAYLIST_UPDATE);

      res.send(jobs);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
  getJob: async (req, res, next) => {
    try {
      const { jobId } = req.params;
      const job = await getJob(jobId);

      if (!job) {
        req.log.debug(`Cannot find job id ${jobId}`);
        return next(new restify.NotFoundError(`Job ${jobId} not found`));
      }

      if (!(await jobHelper.checkJobUserPermission(req.user.sub, jobId))) {
        return next(new restify.NotFoundError(`Cannot find Job ${jobId}`));
      }

      res.send(job);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  getJobHistory: (req, res, next) => {
    const { jobId } = req.params;

    return co(function* execute() {
      const job = yield getJob(jobId);

      if (!job) {
        req.log.debug(`Cannot find job id ${jobId}`);
        return next(new restify.NotFoundError(`Job ${jobId} not found`));
      }

      if (!(yield jobHelper.checkJobUserPermission(req.user.sub, jobId))) {
        return next(new restify.NotFoundError(`Cannot find Job ${jobId}`));
      }

      const history = yield server.db.read.rows(
        `       SELECT
                    jsh.job_id,
                    jsh.status,
                    jsh.message,
                    jsh.created_at as changed,
                    jsh.data
                FROM
                    job_status_history jsh
                WHERE
                    jsh.job_id = $1
                ORDER BY
                    jsh.id
        `,
        [jobId]
      );

      res.send(history);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
  expireJobs: (req, res, next) =>
    co(function* execute() {
      const enablePlaylistStatusHistoryToFirehose =
        env?.config?.enablePlaylistStatusHistoryToFirehose;
      const condition = enablePlaylistStatusHistoryToFirehose
        ? `WHERE ej.type != 'playlist.update'`
        : '';
      const jobs = yield server.db.write.rows(
        `
            WITH expired_jobs AS (
                UPDATE job SET
                    status = ${jobHelper.jobStatus.FAILED},
                    updated = current_timestamp
                WHERE status < ${jobHelper.jobStatus.COMPLETE}
                    AND coalesce(expiry, 'infinity'::timestamptz) <= current_timestamp
                RETURNING id, status, updated, expiry, type, device_id, data, destination
            ),
            ejsh AS (
                INSERT INTO job_status_history (job_id, status, message, created_at, data)
                    SELECT ej.id, ej.status, 'Failed due to expiry', ej.updated, ej.data
                    FROM expired_jobs ej ${condition}
                RETURNING id, job_id
            )
            SELECT
                ej.id,
                ej.device_id,
                ej.type,
                ej.status,
                ej.destination,
                ej.expiry as expiration,
                ej.updated
            FROM expired_jobs ej
        `
      );

      req.expiredJobs = jobs;
      if (jobHelper.okToSendFpsJobStatus() && !_.isEmpty(jobs)) {
        const fpsJobs = jobs
          .filter(job => jobHelper.isFpsJobs(job))
          .map(job => ({
            jobDestination: job.destination,
            failReason: JSON.stringify({
              code: -1,
              message: 'failed due to expired',
            }),
            jobType: job.type,
            jobId: job.id,
            jobStatus: job.status,
          }));
        if (!_.isEmpty(fpsJobs)) {
          server.log.info(
            {
              payloadCount: fpsJobs.length,
            },
            `[jobs].[expireJobs] calling sendFpsJobStatusEventsToEventBridge`
          );
          yield jobHelper.sendFpsJobStatusEventsToEventBridge(fpsJobs);
        }
      }

      const filteredJobs = jobs.filter(({ type }) =>
        [
          jobHelper.jobType.PLAYLIST_UPDATE,
          jobHelper.jobType.FUTUREX_KEYLOAD_RKI,
        ].includes(type)
      );
      if (filteredJobs.length && enablePlaylistStatusHistoryToFirehose) {
        const playlistJob = [];
        filteredJobs.forEach(job => {
          if (job.type === jobHelper.jobType.PLAYLIST_UPDATE) {
            playlistJob.push({
              jobId: job.id,
              status: job.status,
              message: 'Failed due to expiry',
              createdAt: new Date().toISOString(),
            });
          }
        });
        logger.debug(playlistJob, `putting playlist job to firehose`);
        jobHelper.sendPlaylistStatusHistoryToFirehose(playlistJob);
      }

      if (!_.isEmpty(filteredJobs)) {
        res.send(200, 0);
        return next();
      }
      // NOTE: Below code is sitting here since last 4 years which will never execute because of above condition...
      let totalExpired = 0;
      const connection = yield server.db.write.getConnection();

      while (filteredJobs.length) {
        // Start transaction
        yield connection.execute('BEGIN');
        const job = filteredJobs.shift();

        try {
          // Send GSTV notification
          if (
            job.type === jobHelper.jobType.PLAYLIST_UPDATE &&
            env.config.GSTV
          ) {
            const queue = _.filter(
              env.config.GSTV.dataUpload,
              x => x.queueType === 'reporting'
            );
            if (queue && queue[0].url) {
              req.log.info(
                `Sending GSTV jod id ${job.id} notification for expiry`
              );
              yield gstvHelper.sendGstvNotificationIfJobIsTerminal(
                connection,
                queue,
                job,
                jobHelper.jobStatus.FAILED
              );
            } else {
              req.log.warn(
                `Did not send GSTV notification for job ${job.id} due to 'reporting' queue not specified`
              );
            }
          }

          if (job.type === jobHelper.jobType.FUTUREX_KEYLOAD_RKI) {
            yield keyRequestHelper.updateKeyRequestAndSendNotifications(
              req,
              connection,
              job,
              jobHelper.jobStatus.FAILED,
              'Failed due to expiry'
            );
          }

          totalExpired += 1;
          // Commit transaction
          yield connection.execute('COMMIT');
        } catch (err) {
          req.log.error('Error on Notification - Error: %o, Job: %o', err, job);
          yield connection.execute('ROLLBACK');
        }
      }

      if (connection) {
        connection.done();
      }

      res.send(200, totalExpired);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
  cancelJob: (req, res, next) => {
    const { jobId } = req.params;

    return co(function* execute() {
      const job = yield getJob(jobId);
      if (!job) {
        req.log.debug(`Cannot find job id ${jobId}`);
        return next(new restify.NotFoundError(`Job ${jobId} not found`));
      }
      if (
        !hasIcsSystemRole(req) &&
        !(yield jobHelper.checkJobUserPermission(req.user.sub, jobId))
      ) {
        return next(new restify.NotFoundError(`Cannot find Job ${jobId}`));
      }
      if (job.status > jobHelper.jobStatus.ACCEPT) {
        return next(
          new restify.ConflictError(
            `Cannot cancel job with status ${job.statusName}`
          )
        );
      }

      // Get the user from the db
      const { cancelledBy } = req.body;
      const user = (yield usersHelper.getFullUser([req.user.sub]))[0];
      const userName = cancelledBy || user.fullName;
      const message = `Job cancelled by ${userName}`;
      const connection = yield server.db.write.getConnection();
      const fpsJobStatusEventList = [];
      try {
        // Start transaction
        yield connection.execute('BEGIN');
        yield cancelThisJob({
          req,
          connection,
          job,
          userName,
          message,
          fpsJobStatusEventList,
        });
        // Commit transaction
        yield connection.execute('COMMIT');
        if (
          jobHelper.okToSendFpsJobStatus() &&
          !_.isEmpty(fpsJobStatusEventList)
        ) {
          server.log.info(
            {
              payloadCount: fpsJobStatusEventList.length,
            },
            `[jobs].[cancelJob] calling sendFpsJobStatusEventsToEventBridge`
          );
          yield jobHelper.sendFpsJobStatusEventsToEventBridge(
            fpsJobStatusEventList
          );
        }
      } catch (err) {
        req.log.error(err);
        yield connection.execute('ROLLBACK');
        throw err;
      } finally {
        if (connection) {
          connection.done();
        }
      }

      req.log.info(`Job ${jobId} cancellation is completed by ${userName}`);
      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
  getDeviceJobs: async (req, res, next) => {
    try {
      const { deviceId } = req.params;
      let jobs = [];

      if (req.user.sub !== deviceId) {
        req.log.info(
          `[JobHandler].[GetDeviceJobs] no permission for device ${req.user.sub} to change device ${deviceId}.`
        );
        return next(new restify.ForbiddenError('No permission'));
      }
      const { companyId } = req.user;

      const companyFeatureFlags =
        await companyHelper.getAllFeatureFlagsByCompanyIdInCache(companyId);
      const preferHTTPS = companyFeatureFlags.includes('PREFER_HTTPS') || false;
      req.log.info(
        `[JobHandler].[GetDeviceJobs] get all companyFeatureFlags  ${companyFeatureFlags} prefer Https: ${preferHTTPS}.`
      );

      // update last contact time
      try {
        await server.db.write.execute(
          'UPDATE target set last_contact = now(), ip_address = $2 where target_id = $1',
          [deviceId, ipAddressHelper.getIPFromHeader(req)]
        );
      } catch (err) {
        req.log.error(
          { error: err },
          `[JobHandler].[GetDeviceJobs] error: ${err.message}`
        );
      }

      const device1 = await server.db.read.row(
        ` SELECT
                  d.target_id as device_id,
                  p.device_type,
                  d.site_id,
                  s.company_id,
                  s.timezone_id,
                  e.reference_id as gvr_site_id,
                  d.fuel_position,
                  array_to_string(array_agg(t.tag_id), ',') as tag_ids
      FROM
                  target d
                  LEFT JOIN site_tag t
                        ON t.site_id = d.site_id AND t.deleted = false
                  JOIN product_type p
                        ON d.device_type = p.device_type
                  LEFT JOIN site s
                        ON d.site_id = s.site_id
                  LEFT JOIN site_external_references e
                        ON e.site_id = s.site_id
                        AND e.reference_type = 'GVR'
                        AND e.deleted = FALSE
                  LEFT JOIN company c
                        ON c.id = s.company_id
      WHERE
                  d.target_id = $1
                  AND d.active IS true
                  AND d.last_registered IS NOT NULL
      GROUP BY
                  d.target_id,
                  p.device_type,
                  d.site_id,
                  s.company_id,
                  s.timezone_id,
                  e.reference_id`,
        [deviceId]
      );

      if (!device1) {
        return next(
          new restify.NotFoundError(`Device with id ${deviceId} not found`)
        );
      }
      logger.debug(
        `[JobHandler].[GetDeviceJobs] Device Id: ${device1.deviceId} GVR Id: ${device1.gvrSiteId}. Checking Jobs..`
      );

      if (
        env.config.AWS.kafka &&
        env.config.AWS.kafka.enable &&
        device1.gvrSiteId
      ) {
        const isDataFeedEnabled =
          await deviceOnboardingAdapter.isFeatureEnabled(
            device1.deviceType,
            'telemetry_data_feed'
          );

        if (isDataFeedEnabled)
          deviceHeartBeatHelper.addDeviceHeartBeat(device1);
      }

      const query = `
        SELECT
             j.id,
             j.device_id,
             j.destination,
             j.type,
             j.data
        FROM
            job j
        WHERE
            j.device_id = $1 AND
            j.status = 0 AND
            ( j.embargo IS NULL OR j.embargo <= CURRENT_TIMESTAMP ) AND
            ( j.expiry IS NULL OR j.expiry >= CURRENT_TIMESTAMP )
        ORDER BY
            j.embargo ASC;
      `;

      jobs = await server.db.read.rows(query, [deviceId]);

      // inflate jobs data
      // eslint-disable-next-line no-restricted-syntax
      for (const job of jobs) {
        const parsedUrl = url.parse(job.data);

        if (parsedUrl.protocol === 's3:') {
          try {
            // eslint-disable-next-line no-await-in-loop
            const s3Result = await aws
              .downloadFromS3({
                Bucket: parsedUrl.hostname,
                Key: parsedUrl.path.slice(1),
              })
              .promise();
            job.data = s3Result.Body.toString('utf-8');
          } catch (err) {
            req.log.warn(
              { error: err },
              `[JobHandler].[GetDeviceJobs] Failed trying to download device job from S3: ${err.message}.`
            );
          }
        }
      }

      for (let k = 0; k < jobs.length; k++) {
        const job = jobs[k];
        // Filter dependency
        // eslint-disable-next-line no-await-in-loop
        const jobsDepended = await getJobDependency(job);

        if (jobsDepended) {
          for (let j = 0; j < jobsDepended.length; j++) {
            const jobDepend = jobsDepended[j];
            if (
              jobDepend.status < jobHelper.jobStatus.COMPLETE ||
              (jobDepend.status === jobHelper.jobStatus.FAILED &&
                !jobDepend.continueOnFail)
            ) {
              // Remove from jobs
              if (k !== -1) {
                jobs.splice(k, 1);
              }
              k -= 1;
              break;
            }
          }
        }
      }

      for (let i = 0; i < jobs.length; i++) {
        const job = jobs[i];
        try {
          if (preferHTTPS && config.urlRewriteJobTypes.includes(job.type)) {
            let parsedData = null;
            parsedData = JSON.parse(job.data);
            job.data = JSON.stringify(parsedData);
            jobHelper.modifyJobDataProtocolHttpToHttps(job);
          }
        } catch (err) {
          req.log.warn(
            { error: err },
            `[JobHandler].[GetDeviceJobs] Failed trying to parse device job data on preferHTTPS: ${err.message}.`
          );
        }
      }

      const host = `${req.headers['x-forwarded-proto']}://${req.headers.host}`;
      let isHostWhitelisted = false;

      if (env.config.url.whitelistedHosts) {
        isHostWhitelisted = env.config.url.whitelistedHosts.includes(host);
      }

      if (isHostWhitelisted) {
        for (let i = 0; i < jobs.length; i++) {
          const job = jobs[i];
          let parsedData = null;
          try {
            parsedData = JSON.parse(job.data);
            if (parsedData && parsedData.fileUrl) {
              parsedData.fileUrl = parsedData.fileUrl.replace(
                env.config.url.api,
                host
              );
              job.data = JSON.stringify(parsedData);
            }
          } catch (err) {
            req.log.warn(
              { error: err },
              `[JobHandler].[GetDeviceJobs] Failed trying to parse device job data: ${err.message}.`
            );
          }
        }
      }

      res.send(jobs);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  updateJobStatus: async (req, res, next) => {
    try {
      const { status: newStatus, message, data } = req.body;
      const { jobId } = req.params;

      req.log.info(
        `[Jobs].[UpdateJobStatus] Job ${jobId} update is started with new status ${newStatus}.`
      );

      if (
        newStatus === jobHelper.jobStatus.NEW ||
        newStatus === jobHelper.jobStatus.CANCELLED
      ) {
        req.log.info(
          '[Jobs].[UpdateJobStatus] Cannot update job status to new or cancelled.'
        );
        return next(
          new restify.ConflictError(
            `Cannot update job status to be ${newStatus}`
          )
        );
      }

      const job = await getJob(jobId);

      if (!job) {
        req.log.info(
          `[Jobs].[UpdateJobStatus] Cannot find for job id ${jobId}.`
        );
        return next(new restify.NotFoundError(`Job ${jobId} not found`));
      }

      if (job.status === newStatus && job.type === 'playlist.update') {
        res.send(204);
        return next();
      }

      const { deviceId } = job;

      if (parseInt(req.user.sub, 10) !== deviceId) {
        req.log.info(
          `[Jobs].[UpdateJobStatus] no permission for device ${req.user.sub} to change device ${deviceId}.`
        );
        return next(new restify.ForbiddenError('No permission'));
      }

      if (
        job.status !== undefined &&
        job.status === jobHelper.jobStatus.CANCELLED
      ) {
        req.log.info('[Jobs].[UpdateJobStatus] Job already cancelled.');
        return next(
          new restify.ConflictError(`Job ${jobId} has been cancelled`)
        );
      }

      // Expiry only accept COMPLETE or FAILED
      const now = new Date();

      if (job.expiration && now > job.expiration) {
        if (
          newStatus !== jobHelper.jobStatus.COMPLETE &&
          newStatus !== jobHelper.jobStatus.FAILED
        ) {
          req.log.info(
            `[Jobs].[UpdateJobStatus] Job ${jobId} already expired and try to update to ${newStatus}.`
          );
          return next(
            new restify.ConflictError(
              `Job ${jobId} expired, cannot update status to ${newStatus}`
            )
          );
        }
      }

      // Update job
      const connection = await server.db.write.getConnection();
      const fpsJobStatusEventList = [];

      try {
        // Start transaction
        await connection.execute('BEGIN');
        if (
          newStatus === jobHelper.jobStatus.COMPLETE &&
          job.destination === mfaJobConstants.INVENCO_SYSTEM &&
          job.type === mfaJobConstants.SYS_GENERATE_CHALLENGE_TOKEN
        ) {
          const processAuthTokenJob = await getProcessAuthTokenJob(
            connection,
            newStatus,
            job
          );

          if (processAuthTokenJob) {
            await updateProcessAuthTokenJob(
              connection,
              processAuthTokenJob,
              data,
              fpsJobStatusEventList
            );
          }
        }

        if (
          job.destination === mfaJobConstants.INVENCO_SYSTEM &&
          (job.type === mfaJobConstants.SYS_GENERATE_CHALLENGE_TOKEN ||
            job.type === mfaJobConstants.PROCESS_AUTH_OPERATION)
        ) {
          await updateBulkOperationStatus(connection, newStatus, job);
        }

        if (
          newStatus === jobHelper.jobStatus.COMPLETE &&
          job.destination === mfaJobConstants.INVENCO_SYSTEM &&
          job.type === jobHelper.jobType.GENERATE_CSR
        ) {
          logger.error(
            `[updateJobStatus] fetching certificates for ${deviceId}`
          );
          fetchCertificates(req, data, deviceId, jobId);
        }

        await updateThisJob(
          req,
          connection,
          job,
          newStatus,
          message,
          data,
          fpsJobStatusEventList
        );

        if (job.type === jobHelper.jobType.ROLLOUT_INSTALL) {
          await module.exports.updateAlarmRulesSettings(
            connection,
            job,
            newStatus
          );
        }

        await connection.execute('COMMIT');
        const companyFeatureFlags =
          await companyHelper.getAllFeatureFlagsByCompanyIdInCache(
            job.companyId
          );
        const enablePullLogFeature =
          companyFeatureFlags.includes('ICS_PULL_LOG_ENABLE') || false;
        logger.info(
          `Logger for printing job and requiredFreatureFlag: ${enablePullLogFeature} ${JSON.stringify(companyFeatureFlags)} ${JSON.stringify(job)}`
        );

        if (
          job.type === jobHelper.jobType.FILE_UPLOAD &&
          newStatus === jobHelper.jobStatus.COMPLETE
        ) {
          if (job.id && enablePullLogFeature) {
            const deviceFilesDetails = await getDeviceFiles(job.id);

            logger.info(
              `Device files details: ${JSON.stringify(deviceFilesDetails)}`
            );

            if (
              !deviceFilesDetails[0] ||
              !deviceFilesDetails[0].lastPulledPath ||
              !deviceFilesDetails[0].requestId
            ) {
              throw new Error(
                'Missing required device file details (lastPulledPath or requestId).'
              );
            }

            const deviceFilesPath = removeS3BucketPrefix(
              deviceFilesDetails[0].lastPulledPath
            );

            const lambdaPayload = {
              eventType: 's3-file-upload',
              source: 'data-processor',
              timestamp: new Date().toISOString(),
              furid: deviceFilesDetails[0].requestId,
              key: deviceFilesPath,
            };

            const lambdaParams = {
              FunctionName: `ics-lambda-dataprocessors_pull_log-${process.env.ENVIRONMENT}`,
              InvocationType: 'Event',
              Payload: JSON.stringify(lambdaPayload),
            };

            logger.info(
              `Lambda invocation params: ${JSON.stringify(lambdaParams)}`
            );

            try {
              aws.invokeLambda(lambdaParams);
              logger.info('Lambda invoked successfully:');
            } catch (error) {
              logger.error('Error invoking Lambda pull log:', error);
            }
          } else {
            logger.info('Coming to finalizeUploadRequest part');
            await finalizeUploadRequest(job.id);
          }
        }

        server.log.info(
          {
            payloadCount: fpsJobStatusEventList.length,
          },
          `[Jobs].[UpdateJobStatus] checking valid to send.`
        );
        if (
          jobHelper.okToSendFpsJobStatus() &&
          !_.isEmpty(fpsJobStatusEventList)
        ) {
          server.log.info(
            {
              payloadCount: fpsJobStatusEventList.length,
            },
            `[Jobs].[UpdateJobStatus] calling sendFpsJobStatusEventsToEventBridge.`
          );
          await jobHelper.sendFpsJobStatusEventsToEventBridge(
            fpsJobStatusEventList
          );
        }
      } catch (err) {
        await connection.execute('ROLLBACK');
        throw err;
      } finally {
        if (connection) {
          connection.done();
        }
      }

      req.log.info(
        { jobId, deviceId },
        `[Jobs].[UpdateJobStatus] Job ${jobId} update is completed by ${deviceId}.`
      );
      res.send(204);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  cancelBulkJobs: (req, res, next) =>
    co(function* execute() {
      const { bulkOperationId } = req.params;
      const { user, cancellableJobs } = yield getCancelableJobsWithUser({
        user: req.user,
        bulkOperationId,
        jobStatusFilter: jobHelper.jobStatus.ACCEPT,
      });

      if (cancellableJobs.length < 1) {
        return next(
          new restify.NotFoundError(
            `No jobs found for bulk operation id ${bulkOperationId}`
          )
        );
      }

      const message = `Job cancelled by ${user.fullName}`;
      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');
        yield Promise.all(
          cancellableJobs.map(job =>
            cancelThisJob({
              req,
              connection,
              job,
              userName: user.fullName,
              message,
            })
          )
        );
        yield connection.execute('COMMIT');
      } catch (err) {
        req.log.error(err);
        yield connection.execute('ROLLBACK');
        throw err;
      } finally {
        connection.done();
      }

      const jobIds = _.map(cancellableJobs, 'id');

      res.send(jobIds);

      return next();
    }).catch(errorHandler.onError(req, res, next)),
  createConfigManagementJob: async (req, res, next) => {
    let filePath;

    try {
      const { isHsmSignatureEnabled = true } = env.config;
      filePath = req.files.fileContent.path;

      const { devices, configFileDescriptor, createdBy, jobExpireMins } =
        req.params;
      const { hash: fileHash, size: fileSize } = req.files.fileContent;
      const fileBuffer = req.params.fileContent;
      const deviceIds = [...new Set(devices.map(device => device.deviceId))];
      const rebootDeviceIds = devices
        .filter(device => device.createReboot)
        .map(device => device.deviceId);
      const restartDevices = devices.filter(device => device.restartData);

      const companyId = req.user.company.id;
      const existingDeviceIds = (
        await server.db.read.rows(
          `     SELECT t.target_id
                FROM target t
                INNER JOIN site s on t.site_id = s.site_id
                WHERE s.company_id = $1::uuid AND target_id = ANY( $2::int[] )
          `,
          [companyId, deviceIds]
        )
      ).map(device => device.targetId);

      const nonExistingDeviceIds = deviceIds.filter(
        deviceId => !existingDeviceIds.includes(deviceId)
      );
      if (!_.isEmpty(nonExistingDeviceIds))
        return next(
          new restify.ForbiddenError(
            `Device IDs do not belong to current company: ${nonExistingDeviceIds}`
          )
        );

      const { s3Bucket } = env.config.configManagementJobs;
      const s3fileKey = `config-files/${companyId}/${fileHash}`;
      await aws.uploadToS3(
        s3Bucket,
        s3fileKey,
        Readable.from(fileBuffer),
        'public-read'
      );

      let deviceIdToSignatureMappings = null;
      if (
        isHsmSignatureEnabled &&
        configFileDescriptor['sig-type'] &&
        configFileDescriptor['sig-type'] === 'rsa-pss'
      ) {
        deviceIdToSignatureMappings = await getSignatureMappings(
          req,
          companyId,
          deviceIds,
          fileHash
        );
      }

      let connection;
      let result;

      try {
        connection = await server.db.write.getConnection();
        await connection.execute(dbTransaction.BEGIN);
        result = await createConfigUpdateJob(
          connection,
          fileHash,
          s3fileKey,
          configFileDescriptor,
          fileSize,
          deviceIdToSignatureMappings,
          createdBy || req.user.sub,
          deviceIds,
          rebootDeviceIds,
          restartDevices,
          jobExpireMins
        );

        const jobTypes = ['configJobs', 'rebootJobs', 'restartJobs'];

        jobTypes.forEach(category => {
          result[category].forEach(item => {
            if (!item.jobId) {
              logger.error(
                `For device id = ${item.deviceId} in category = ${category}, Got no job Id`
              );
            }
          });
        });

        await connection.execute(dbTransaction.COMMIT);
      } catch (err) {
        connection.execute(dbTransaction.ROLLBACK);
        logger.error('error', JSON.stringify(err));
        throw err;
      } finally {
        connection.done();
      }

      res.send(result);
      return next();
    } catch (e) {
      return errorHandler.onError(req, res, next)(e);
    } finally {
      if (filePath) {
        fs.unlinkSync(filePath);
      }
    }
  },
  getJobStatuses: async (req, res, next) => {
    try {
      const companyId = req.user.company.id;
      const jobIds = req.body;
      const jobType = 'sys.configfile-update';

      // Only return Job Statuses which are not 'New' (0)
      const jobStatuses = await server.db.read.rows(
        `       SELECT j.id, js.name
                FROM job j
                    INNER JOIN job_status js on j.status = js.id
                    INNER JOIN target t on j.device_id = t.target_id
                    INNER JOIN site s on s.site_id = t.site_id
                WHERE s.company_id = $1
                    AND j.id = ANY ($2::uuid[])
                    AND j.type = $3
                    AND j.status > 0
        `,
        [companyId, jobIds, jobType]
      );
      const jobEnumStatuses = jobStatuses.map(jobStatus => ({
        jobId: jobStatus.id,
        jobStatus: jobStatus.name,
      }));
      res.send(jobEnumStatuses);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  bulkCancelCmJobs: async (req, res, next) => {
    try {
      const { user, cancellableJobs } = await getCancelableJobsWithUser({
        user: req.user,
        jobIds: req.body,
        jobStatusFilter: jobHelper.jobStatus.NEW,
      });

      if (cancellableJobs.length < 1) {
        return next(
          new restify.NotFoundError(
            'There are no Cancellable Jobs for the supplied Job IDs'
          )
        );
      }

      const message = `Job cancelled by ${user.fullName}`;
      const connection = await server.db.write.getConnection();
      try {
        await connection.execute('BEGIN');
        await Promise.all(
          cancellableJobs.map(job =>
            cancelThisJob({
              req,
              connection,
              job,
              userName: user.fullName,
              message,
            })
          )
        );
        await connection.execute('COMMIT');
      } catch (err) {
        req.log.error(err);
        await connection.execute('ROLLBACK');
        throw err;
      } finally {
        connection.done();
      }
      res.send(204);

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  sendEventsToEventBridgeOnUpdateJobStatus: async (req, _res, next) => {
    const { jobId } = req.params;
    const tenantId = req.user.companyId;
    const job = await getJobWithHistory(jobId);

    if (!job) {
      req.log.info(
        { jobId },
        `[Jobs].[SendEventsToEventBridge] can't find job id: ${jobId}.`
      );
      return next();
    }

    const {
      status,
      deviceId,
      updated,
      message,
      jobStatusName,
      type: jobType,
    } = job;
    const eventBridgeParamsFactory = ({ detailType, source }) => ({
      data: {
        tenantId,
        jobId,
        deviceId,
        jobType,
        status,
        updatedAt: updated,
        jobStatusName,
        message,
      },
      detailType,
      source,
    });

    await publishJobToSqs({ tenantId, job });

    await aws.sendEventsToEventBridge([
      eventBridgeParamsFactory({
        detailType:
          env.config.AWS.eventBus.renditions.rules.updateDeploymentStatus,
        source: 'handlers/jobs/sendEventsToEventBridgeOnUpdateJobStatus',
      }),
    ]);

    return next();
  },
  sendEventsToEventBridgeOnBulkJobCancel: async (req, res, next) => {
    const { cancellableJobs } = await getCancelableJobsWithUser({
      user: req.user,
      bulkOperationId: req.params.bulkOperationId,
      jobIds: req.body,
      jobStatusFilter: jobHelper.jobStatus.CANCELLED,
    });

    const tenantId = req.user.company.id;

    const sqsMessages = cancellableJobs.map(job =>
      publishJobToSqs({ tenantId, job })
    );

    const sendEventsPromises = cancellableJobs.map(
      ({ id, deviceId, status, updated, type: jobType }) => {
        const eventBridgeParamsFactory = ({ detailType, source }) => ({
          data: {
            tenantId,
            jobId: id,
            jobType,
            deviceId,
            status,
            updatedAt: updated,
          },
          detailType,
          source,
        });

        return aws.sendEventsToEventBridge([
          eventBridgeParamsFactory({
            detailType:
              env.config.AWS.eventBus.renditions.rules.updateDeploymentStatus,
            source: 'handlers/jobs/sendEventsToEventBridgeOnBulkJobCancel',
          }),
        ]);
      }
    );

    await Promise.all(sqsMessages);

    await Promise.all(sendEventsPromises);

    return next();
  },
  sendEventsToEventBridgeOnBulkJobExpire: async (req, res, next) => {
    const { expiredJobs = [] } = req;
    const tenantId = req.user.company.id;

    const sqsMessages = expiredJobs.map(job =>
      publishJobToSqs({ tenantId, job })
    );

    const sendEventsPromises = expiredJobs.map(
      ({ id, deviceId, status, updated, type: jobType }) => {
        const eventBridgeParamsFactory = ({ detailType, source }) => ({
          data: {
            tenantId,
            jobId: id,
            deviceId,
            jobType,
            status,
            updatedAt: updated,
            message: 'Failed due to expiry',
          },
          detailType,
          source,
        });

        return aws.sendEventsToEventBridge([
          eventBridgeParamsFactory({
            detailType:
              env.config.AWS.eventBus.renditions.rules.updateDeploymentStatus,
            source: 'handlers/jobs/sendEventsToEventBridgeOnBulkJobExpire',
          }),
        ]);
      }
    );

    await Promise.all(sqsMessages);

    await Promise.all(sendEventsPromises);

    return next();
  },
};
