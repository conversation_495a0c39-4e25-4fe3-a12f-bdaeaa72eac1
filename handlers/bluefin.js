const logger = require('../lib/logger').mainLogger();
const {
  updateBlueFinHubLocation,
} = require('../helpers/bluefinConsumer-helper');

const handleBlueFinHubLocation = async payload => {
  try {
    const decodedValue = payload.kafkaPayload?.message?.value?.toString();
    const messageObj = decodedValue ? JSON.parse(decodedValue) : {};

    await updateBlueFinHubLocation(payload.dbConnection, messageObj);
  } catch (error) {
    const errorMessage = `[bluefin].[handleBlueFinHubLocation] Error processing payload: ${error.message}`;
    logger.error({ error }, errorMessage);
    throw new Error(errorMessage);
  }
};

module.exports = {
  handleBlueFinHubLocation,
};
