/* eslint-disable no-plusplus */
// User group service to perform all the operations on the database in regards to the api calls

const util = require('util');
const restify = require('restify');
const co = require('co');
const uuid = require('uuid/v4');
const validateUUID = require('uuid-validate');
const _ = require('lodash');
const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');
const env = require('../env');
const aws = require('../lib/aws');
const constants = require('../lib/app-constants');
const logger = require('../lib/logger').mainLogger();
const paginationHelper = require('../helpers/pagination-helper');
// Common queries

const queryMyUserGroup =
  'select ug.id, ug.name, ug.company_id from user_group ug where ug.id = $1;';

const queryMyUsers = `select distinct ugu.user_id as id
  from user_group ug
  join user_group_user ugu on ugu.user_group_id = ug.id
  where ug.id = $1;`;

const queryMyUsersId = `select distinct ugu.user_id as id
  from user_group_user ugu
  where ugu.user_group_id = $1;`;

const queryMySiteGroups = `select distinct asg.id, asg.name
  from user_group ug
  join user_group_authorization_site_group ugasg on ugasg.user_group_id = ug.id
  join authorization_site_group asg on asg.id = ugasg.authorization_site_group_id
  where ug.id = $1`;

const countMyUserGroupUsers = `SELECT ug.id, ug.name,
    ( SELECT DISTINCT COUNT(ugu.user_id) as userCount FROM
    user_group_user ugu WHERE ugu.user_group_id = ug.id
    )
  FROM user_group ug
  WHERE ug.id = $1 AND ug.company_id = $2;`;

// Query to check the count of user groups/teams a user is part of
const queryUserGroupCount = `
  SELECT COUNT(ugu.user_id) AS user_group_count, user_id 
  FROM user_group_user ugu 
  WHERE user_id = ANY ($1) 
  AND ugu.user_id NOT IN (
    SELECT user_id 
    FROM user_role ur 
    INNER JOIN role r ON ur.role_id = r.role_id 
    WHERE role_name = 'COMPANY_ADMIN'
  )
  GROUP BY user_id HAVING COUNT(user_id) = 1;`;

const queryUsersTeamsCount = `SELECT COUNT(ugu.user_id) AS user_group_count, user_id AS id
  FROM user_group_user ugu 
  WHERE user_id = ANY ($1)
  AND ugu.user_id NOT IN (
    SELECT user_id 
    FROM user_role ur 
    INNER JOIN role r ON ur.role_id = r.role_id 
    WHERE role_name = 'COMPANY_ADMIN'
  )
  GROUP BY user_id;`;
// Inserts

const insertUserGroupUser =
  'INSERT INTO user_group_user (user_group_id, user_id) values %s RETURNING user_id as id;';

const insertUserGroupAuthorizationSiteGroup = `INSERT INTO user_group_authorization_site_group (user_group_id, authorization_site_group_id) values %s
  RETURNING authorization_site_group_id as id;`;

// Deletes
const deleteUserGroup = 'DELETE FROM user_group WHERE id = $1;';

const deleteUserGroupUser =
  'DELETE FROM user_group_user WHERE user_group_id = $1;';

const deleteMultipleUserGroupUser = `DELETE FROM user_group_user
  WHERE user_group_id = $1
  AND user_id IN (%s)`;

const deleteUserGroupAuthorizationSiteGroup = `DELETE FROM user_group_authorization_site_group
  WHERE user_group_id = $1;`;

const deleteMultipleUserGroupAuthorizationSiteGroup = `DELETE FROM user_group_authorization_site_group
  WHERE user_group_id = $1
  AND authorization_site_group_id IN (%s)`;

// Updates

const updateUserGroup =
  'UPDATE user_group SET name = $1 WHERE id = $2 RETURNING id, name;';

const queryUserGroup = `SELECT count(1) FROM user_group WHERE name=$1 AND company_id=$2`;

const sendEventsToEventBridge = ({
  userGroupId,
  userIds,
  authorizationSiteGroupIds,
  type,
  source,
}) => {
  const eventBridgeParams = {
    data: {
      userIds,
      userGroupId,
      authorizationSiteGroupIds,
      type,
    },
    detailType: env.config.AWS.eventBus.renditions.rules.updateUserGroup,
    source: `handlers/usergroups.js/fn_${source}`,
  };

  aws.sendEventsToEventBridge(eventBridgeParams);
};

/**
 * Common function to get a user group based on it's id
 */
function* getUserGroupById(userGroupId, req) {
  // Get the user group
  const result = yield server.db.read.row(queryMyUserGroup, [userGroupId]);

  // If there's no user group, there's no need to proceed
  if (!result || result.companyId !== req.user.company.id) {
    return null;
  }
  const users = yield server.db.read.rows(queryMyUsers, [userGroupId]);
  const allUserIds = users.map(user => user.id);

  const usersWithTeams = yield server.db.read.rows(queryUsersTeamsCount, [
    allUserIds,
  ]);
  const userIdsWithTeams = usersWithTeams.map(user => user.id);

  const companyAdminIds = _.difference(allUserIds, userIdsWithTeams);

  const teamUsers = usersWithTeams.map(user => ({
    ...user,
    isAdmin: false,
  }));

  const companyAdmins = companyAdminIds.map(id => ({
    id,
    isAdmin: true,
  }));

  const finalUsers = [...teamUsers, ...companyAdmins];
  const sitegroups = yield server.db.read.rows(queryMySiteGroups, [
    userGroupId,
  ]);

  return { id: result.id, name: result.name, users: finalUsers, sitegroups };
}

// Validate that you have access to this sitegroup
function* validateSitegroupAccess(companyId, sitegroups) {
  const allowedSitegroups = yield server.db.read.rows(
    `SELECT distinct(id)
      FROM authorization_site_group as asg
      LEFT OUTER JOIN authorization_site_group_company asgc ON asg.id = asgc.authorization_site_group_id
        WHERE asgc.company_id=$1 OR asg.company_id=$1;`,
    [companyId]
  );

  const oddSitegroup = _.differenceBy(
    sitegroups,
    allowedSitegroups,
    data => data.id
  );

  if (oddSitegroup.length > 0) {
    return false;
  }
  return true;
}

// Validate the user belongs to any other group or not
async function userGroupCountsCheck(users) {
  const userIds = users.map(user => user.id);
  const result = await server.db.read.rows(queryUserGroupCount, [userIds]);
  return result?.length;
}

// Validate user belongs to my company

function* validateUser(users, company) {
  const userIds = users.map(user => user.id);

  const companyUsers = yield server.db.read.rows(
    `
      SELECT id
      FROM ics_user
      WHERE company_id = $1
      AND id = ANY($2)
      ;
    `,
    [company, userIds]
  );

  return companyUsers.length === users.length;
}

// Common function for materialized view refresh to update `user_site_authorization`.
function refreshUserSiteAuthorization() {
  return co(function* refreshUserSiteAuthorizationGen() {
    const connection = yield server.db.write.getConnection();
    try {
      yield connection.execute(
        'REFRESH MATERIALIZED VIEW CONCURRENTLY user_site_authorization;'
      );
    } catch (error) {
      logger.error({ error }, 'Failed to refresh materialized view');
    } finally {
      connection.done();
    }
  }).catch(error => {
    logger.error({ error }, 'Error during materialized view refresh');
  });
}

module.exports = {
  /**
   * Gets all user groups from the database from the company that the user belongs to.
   * Includes a count of the users in the object
   */
  getUserGroups: (req, res, next) =>
    co(function* execute() {
      req.log.info('Retrieving all user groups for a company');

      const companyId = req.user.company.id;
      const companyAdmin = req.user.roles.includes('COMPANY_ADMIN');
      const { username } = req;
      const { name, order } = req.params;
      req.query.pageSize = req.query.pageSize || '-1';
      const pageParams = paginationHelper.parsePaginationParams(req);

      const filterData = [`${companyId}`];
      let queryStr = ` FROM user_group ug ${companyAdmin ? '' : 'INNER JOIN user_group_user ugu ON ugu.user_group_id = ug.id'} WHERE ug.company_id = $${filterData.length}`;

      if (!companyAdmin) {
        filterData.push(`${username}`);
        queryStr += ` AND ugu.user_id = $${filterData.length}`;
      }

      if (name) {
        filterData.push(`%${name}%`);
        queryStr += ` AND LOWER(ug.name) LIKE LOWER($${filterData.length})`;
      }
      let orderByStr = '';
      if (order) {
        orderByStr += ` ORDER BY ${order} ${order === 'created' ? 'DESC' : ''}`;
      }

      const baseQuery = `
        SELECT ug.id, ug.name,
          (SELECT DISTINCT COUNT(ugu.user_id) as userCount FROM user_group_user ugu WHERE ugu.user_group_id = ug.id)
        `;

      const userGroups = yield server.db.read.rows(
        `${baseQuery}${queryStr}
          ${orderByStr}
          LIMIT ${pageParams.pageSize}
          OFFSET ${pageParams.offset};`,
        [...filterData]
      );

      const { count } = yield server.db.read.row(
        `
                      SELECT COUNT(DISTINCT ug.id) 
                          ${queryStr}
                  `,
        [...filterData]
      );

      req.log.debug({ userGroups }, 'Found groups');

      const data = {
        resultsMetadata: {
          totalResults: count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results: userGroups,
      };
      res.send(data);

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Gets a specific user group from the database, via the passed in parameter
   */
  getUserGroup: (req, res, next) =>
    co(function* execute() {
      const userGroupId = req.params.id;

      req.log.info('Validating UUID');

      if (!validateUUID(userGroupId)) {
        return next(new restify.NotFoundError('Cannot find usergroup'));
      }

      req.log.info('Retrieving user group');

      // Return the result in the correct object construct by calling the get user group function
      const userGroup = yield getUserGroupById(userGroupId, req, next);
      if (!userGroup) {
        return next(new restify.NotFoundError('Cannot find usergroup'));
      }

      res.send(userGroup);
      return next();
    }),

  /**
   * Adds a user group with given users and site groups if given
   */
  createUserGroup: (req, res, next) => {
    const id = uuid();
    const object = req.body;

    return co(function* execute() {
      req.log.info('Creating user group');

      let userGroup = '';
      let users = [];
      let sitegroups = [];

      // Get the company id of the user
      const companyId = req.user.company.id;
      if (_.isEmpty(object.users)) {
        return next(
          new restify.BadRequestError(
            'Please add at least one member to your team before creating it.'
          )
        );
      }
      // Validate if users are present in my company
      if (!(yield validateUser(object.users, companyId))) {
        return next(new restify.NotFoundError('User not found'));
      }

      // Validate that you have access to this sitegroup
      if (
        !(yield validateSitegroupAccess(companyId, object.sitegroups, next))
      ) {
        return next(new restify.NotFoundError('Sitegroup not found'));
      }
      const duplicateCheck = yield server.db.read.row(queryUserGroup, [
        object.name.trim(),
        companyId,
      ]);
      if (duplicateCheck.count >= 1) {
        return next(
          new restify.BadRequestError(
            `User group '${object.name}' already exists`
          )
        );
      }
      // Get a connection for transaction control
      const connection = yield server.db.write.getConnection();
      try {
        req.log.info('Beginning Transaction');

        // Start transaction
        yield connection.execute('BEGIN');

        // Create the user group
        req.log.info([id, object.name, companyId], 'Requesting...');
        const result = yield connection.execute(
          `INSERT INTO user_group (id, name, company_id, created)
                    VALUES ( $1, $2, $3, now()) RETURNING id, name;`,
          [id, object.name, companyId]
        );

        const { 0: firstRow } = result.rows;
        userGroup = firstRow;
        req.log.info(userGroup);

        // If there's no user group, there's no need to proceed
        if (!userGroup) {
          yield connection.execute('ROLLBACK');
          connection.done();
          req.log.error('User group could not be created, rollback performed');
          return next(
            new restify.InternalServerError('User group could not be created')
          );
        }
        // Insert the users into the user group user table
        if (object.users && object.users.length) {
          const values = [];
          let count = 1;
          const array = [];
          // Create the sql query based on the users
          object.users.forEach(user => {
            values.push(` ($${count++}, $${count++} ) `);
            array.push(userGroup.id);
            array.push(user.id);
          });
          const userGroupInsert = util.format(
            `INSERT INTO user_group_user (user_group_id, user_id)
                        values %s RETURNING user_id as id;`,
            _.join(values, ',')
          );
          const result2 = yield connection.execute(userGroupInsert, array);
          users = result2.rows;
        } else {
          users = [];
        }

        // Insert the authorize site groups into the user group authorization site group table
        if (object.sitegroups && object.sitegroups.length) {
          const values = [];
          let count = 1;
          const array = [];
          // Create the sql query based on all the sitegroups
          object.sitegroups.forEach(sitegroupsObject => {
            values.push(` ($${count++}, $${count++} ) `);
            array.push(userGroup.id);
            array.push(sitegroupsObject.id);
          });
          const userGroupAuthorizationSiteGroupInsert = util.format(
            `INSERT INTO user_group_authorization_site_group (user_group_id, authorization_site_group_id)
                        VALUES %s RETURNING authorization_site_group_id as id;`,
            _.join(values, ',')
          );
          const result3 = yield connection.execute(
            userGroupAuthorizationSiteGroupInsert,
            array
          );
          sitegroups = result3.rows;
        } else {
          sitegroups = [];
        }

        // commit transaction
        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        return next(new restify.InternalServerError(err));
      }

      userGroup.users = users;
      userGroup.sitegroups = sitegroups;
      // Return the result in the correct object construct
      res.send(userGroup);

      refreshUserSiteAuthorization();

      sendEventsToEventBridge({
        userIds: userGroup.users.map(user => user.id),
        userGroupId: id,
        authorizationSiteGroupIds: userGroup.sitegroups.map(
          sitegroup => sitegroup.id
        ),
        type: constants.ACTIONS.CREATE,
        source: 'createUserGroup',
      });

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  /**
   * This will update a user group. It will add and remove users and site groups as well.
   */
  updateUserGroup: (req, res, next) => {
    const userGroupId = req.params.id;

    if (!validateUUID(userGroupId)) {
      return next(new restify.NotFoundError('Cannot find usergroup'));
    }

    const userGroup = req.body;
    if (_.isEmpty(userGroup.users)) {
      return next(
        new restify.BadRequestError(
          'Please add at least one member to your team before updating it.'
        )
      );
    }
    return co(function* execute() {
      req.log.info('Updating user group');

      // Check if the user group exists and if it does how many users are in the group
      const result = yield server.db.read.row(countMyUserGroupUsers, [
        userGroupId,
        req.user.company.id,
      ]);

      if (!result) {
        return next(new restify.NotFoundError('User group does not exist'));
      }

      if (
        !(yield validateSitegroupAccess(
          req.user.company.id,
          userGroup.sitegroups,
          next
        ))
      ) {
        return next(new restify.NotFoundError('Sitegroup not found'));
      }

      const selectQuery = `${queryUserGroup} AND id<>$3;`;
      const duplicateCheck = yield server.db.read.row(selectQuery, [
        userGroup.name.trim(),
        req.user.company.id,
        userGroup.id,
      ]);
      if (duplicateCheck.count >= 1) {
        return next(
          new restify.BadRequestError(
            `User group '${userGroup.name}' already exists`
          )
        );
      }
      // If the user group exists then we can continue to try and update

      // Get a connection for transaction control
      const connection = yield server.db.write.getConnection();
      try {
        // Start transaction
        yield connection.execute('BEGIN');
        // Update the user group table name
        yield connection.execute(updateUserGroup, [
          userGroup.name,
          userGroupId,
        ]);

        // Update the user group user table

        // Get the existing user ids
        const currentUsers = yield server.db.read.rows(queryMyUsersId, [
          userGroupId,
        ]);

        // Remove the unwanted users
        const toRemove = _.differenceBy(currentUsers, userGroup.users, 'id');

        const usergroupCheck = yield userGroupCountsCheck(toRemove);

        if (usergroupCheck) {
          yield connection.execute('ROLLBACK');
          connection.done();
          return next(
            new restify.ConflictError(
              `User can't be removed as some of the users are not part of any other team.`
            )
          );
        }

        if (toRemove.length > 0) {
          const del = [];
          const ids = [];
          let count = 2;
          ids.push(userGroupId);
          toRemove.forEach(x => {
            del.push(` $${count++} `);
            ids.push(x.id);
          });
          const query = util.format(
            deleteMultipleUserGroupUser,
            _.join(del, ',')
          );
          yield connection.execute(query, ids);
        }

        // Add the new users
        const toAdd = _.differenceBy(userGroup.users, currentUsers, 'id');

        if (toAdd.length > 0) {
          const add = [];
          const ids = [];
          let count = 1;
          toAdd.forEach(x => {
            add.push(` ($${count++}, $${count++} ) `);
            ids.push(userGroupId);
            ids.push(x.id);
          });
          const query = util.format(insertUserGroupUser, _.join(add, ','));
          yield connection.execute(query, ids);
        }

        // Update the user group authorization site group table

        // Get the existing authorization site group ids
        const currentSiteGroups = yield server.db.read.rows(queryMySiteGroups, [
          userGroupId,
        ]);

        // Remove the unwanted users
        const toRemoveSites = _.differenceBy(
          currentSiteGroups,
          userGroup.sitegroups,
          'id'
        );

        if (toRemoveSites.length > 0) {
          const del = [];
          let count = 2;
          const ids = [];
          ids.push(userGroupId);
          // Create the sql query based on all the sitegroups
          toRemoveSites.forEach(sitegroupsObject => {
            del.push(`$${count++}`);
            ids.push(sitegroupsObject.id);
          });
          const query = util.format(
            deleteMultipleUserGroupAuthorizationSiteGroup,
            _.join(del, ',')
          );
          yield connection.execute(query, ids);
        }

        // Add the new sites
        const toAddSites = _.differenceBy(
          userGroup.sitegroups,
          currentSiteGroups,
          'id'
        );

        if (toAddSites.length > 0) {
          const add = [];
          let count = 1;
          const ids = [];
          // Create the sql query based on all the sitegroups
          toAddSites.forEach(sitegroupsObject => {
            add.push(` ($${count++}, $${count++} ) `);
            ids.push(userGroupId);
            ids.push(sitegroupsObject.id);
          });
          const query = util.format(
            insertUserGroupAuthorizationSiteGroup,
            _.join(add, ',')
          );
          yield connection.execute(query, ids);
        }

        // commit transaction
        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        return next(new restify.InternalServerError(err));
      }

      // Return the result in the correct object construct by calling the get user group function
      const response = yield getUserGroupById(userGroupId, req, next);
      if (!response) {
        return next(
          new restify.InternalServerError(
            'Cannot find usergroup after updating it'
          )
        );
      }

      res.send(response);

      refreshUserSiteAuthorization();

      sendEventsToEventBridge({
        userIds: response.users.map(user => user.id),
        userGroupId,
        authorizationSiteGroupIds: response.sitegroups.map(
          sitegroup => sitegroup.id
        ),
        type: constants.ACTIONS.UPDATE,
        source: 'updateUserGroup',
      });

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  /**
   * This will delete a user group if there are no users within it. Else responds with a 403
   */
  deleteUserGroup: (req, res, next) => {
    const userGroupId = req.params.id;

    if (!validateUUID(userGroupId)) {
      return next(new restify.NotFoundError('Cannot find usergroup'));
    }

    return co(function* execute() {
      req.log.info('Deleting user group');

      // Check if the user group exists and if it does how many users are in the group
      const result = yield server.db.read.row(countMyUserGroupUsers, [
        userGroupId,
        req.user.company.id,
      ]);

      if (!result) {
        req.log.info('User group does not exist');
        return next(new restify.NotFoundError('User group does not exist'));
      }

      // Check if the user is part of any other teams/user groups or not
      const currentUsers = yield server.db.read.rows(queryMyUsersId, [
        userGroupId,
      ]);

      const usergroupCheck = yield userGroupCountsCheck(currentUsers);

      if (usergroupCheck) {
        return next(
          new restify.ConflictError(
            `Team can't be deleted as some of the users are not part of any other team.`
          )
        );
      }

      // Get a connection for transaction control
      const connection = yield server.db.write.getConnection();
      try {
        // Start transaction
        yield connection.execute('BEGIN');

        // Delete user group user entries
        yield connection.execute(deleteUserGroupUser, [userGroupId]);

        // Delete user group authorization site group
        yield connection.execute(deleteUserGroupAuthorizationSiteGroup, [
          userGroupId,
        ]);

        // Delete user group
        yield connection.execute(deleteUserGroup, [userGroupId]);

        // commit transaction
        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        return next(new restify.InternalServerError(err));
      }

      // Send success
      res.send(204, 'Deleted');

      refreshUserSiteAuthorization();

      sendEventsToEventBridge({
        userIds: [],
        userGroupId,
        authorizationSiteGroupIds: [],
        type: constants.ACTIONS.DELETE,
        source: 'deleteUserGroup',
      });

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
};
