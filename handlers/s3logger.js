const _ = require('lodash');
const co = require('co');
const restify = require('restify');

const AWS = require('../lib/aws');
const { server } = require('../app');
const { config } = require('../env');
const errorHandler = require('../lib/errorhandler');
const dbConfigHelper = require('../helpers/db-config-helper');

const S3LOGGER_LAST_SYNCED = 's3logger_last_synced';

function safeToString(obj) {
  try {
    return JSON.stringify(obj);
  } catch (err) {
    try {
      return JSON.stringify(String(obj));
    } catch (err2) {
      return JSON.stringify('Error serializing log line');
    }
  }
}

function uploadToS3(logs, companyId, bucket) {
  // Prepare file buffer
  const payload = Buffer.from(safeToString(logs));

  const now = new Date();
  const day = now.getUTCDate();
  const month = now.getUTCMonth() + 1;
  const year = now.getUTCFullYear();
  const filename = `${Math.round(now.getTime() / 1000)}.txt`; // UNIX epoch

  const key = `api-audit/${companyId}/${year}/${month}/${day}/${filename}`;
  const acl = 'private';

  // Upload file
  return AWS.uploadToS3(bucket, key, payload, acl);
}

module.exports = {
  sync: (req, res, next) =>
    co(function* execute() {
      if (!config.s3logger) {
        return next(
          new restify.InternalServerError('Missing S3Logger configuration')
        );
      }

      const { chunkSize } = config.s3logger;
      const { defaultBucket } = config.s3logger;
      const companyBuckets = config.s3logger.companies;

      let lastSynced = yield dbConfigHelper.getValue(S3LOGGER_LAST_SYNCED);

      const conn = yield server.db.write.getConnection();
      try {
        yield conn.execute('BEGIN');

        const now = new Date().toISOString();
        let results = [];

        do {
          req.log.debug('S3Logger: querying report_api_audit');

          results = (yield conn.execute(
            `
                        SELECT * FROM report_api_audit
                        WHERE
                            timestamp > $1 AND
                            timestamp <= $2
                        ORDER BY timestamp
                        LIMIT ${chunkSize};
                    `,
            [lastSynced, now]
          )).rows;

          req.log.debug(`S3Logger: ${results.length} results found`);
          if (results.length) {
            const lastRecord = results[results.length - 1];
            lastSynced = lastRecord.timestamp;
            req.log.debug(`S3Logger: ${lastSynced} last timestamp`);
          }

          req.log.debug('S3Logger: grouping records by companyId');
          const companyLogs = _.groupBy(results, 'companyId');

          // eslint-disable-next-line guard-for-in, no-restricted-syntax
          for (const companyId in companyLogs) {
            const logs = companyLogs[companyId];

            let bucket = defaultBucket;
            if (companyBuckets) {
              const companyBucket = companyBuckets.find(
                company => company.id === companyId
              );

              if (companyBucket) {
                bucket = companyBucket.bucket;
              }
            }

            req.log.debug(
              `S3Logger: uploading logs for companyId ${companyId}`
            );
            yield uploadToS3(logs, companyId, bucket);
            req.log.debug('S3Logger: uploaded');
          }
        } while (results.length);

        yield dbConfigHelper.setValue(S3LOGGER_LAST_SYNCED, now, conn);
        req.log.debug('S3Logger: process end');

        yield conn.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield conn.execute('ROLLBACK');
        return next(new restify.InternalServerError(err));
      } finally {
        conn.done();
      }

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
