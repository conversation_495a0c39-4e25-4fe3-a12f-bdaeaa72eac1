const co = require('co');

const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');

module.exports = {
  getTags: (req, res, next) =>
    co(function* execute() {
      const userId = req.user.sub;
      const { params } = req;
      const companyId = req.user.company.id;
      let tags;
      if (params && params.legacy) {
        tags = yield server.db.read.rows(
          `SELECT tag.name as name,tag.id as id,COUNT(site.site_id) as site_count 
                FROM tag LEFT JOIN site_tag ON site_tag.tag_id = tag.id AND site_tag.deleted = false 
            LEFT JOIN site ON site.site_id = site_tag.site_id AND site.active AND tag.company_id = site.company_id
           WHERE tag.company_id = $1 GROUP BY tag.id, tag.name ORDER BY tag.name asc 
        `,
          [companyId]
        );
      } else {
        tags = yield server.db.read.rows(
          `
            SELECT
                MAX(t.id) AS id,
                t.name,
                COUNT(a.site_id) AS site_count
            FROM tag t
                LEFT JOIN site_tag st ON st.tag_id = t.id AND st.deleted = false
                LEFT JOIN site s ON s.site_id = st.site_id AND s.active
                LEFT JOIN user_site_authorization a ON
                    a.site_id = s.site_id AND
                    a.user_id = $2
            WHERE
                t.company_id = $1
            GROUP BY t.name
            ORDER BY site_count DESC, t.name ASC;
        `,
          [companyId, userId]
        );
      }
      res.send(tags);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
