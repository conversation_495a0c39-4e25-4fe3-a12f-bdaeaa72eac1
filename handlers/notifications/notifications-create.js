const co = require('co');
const restify = require('restify');

const errorHandler = require('../../lib/errorhandler');
const { server } = require('../../app');
const mailer = require('../../lib/mailer');
const mailerHelper = require('../../helpers/mailer-helper');

function getDeviceRow(id) {
  return co(function* execute() {
    const device = yield server.db.read.row(
      `
          SELECT name, serial_number
          FROM target
          WHERE target_id = $1
      ;`,
      [id]
    );

    return device;
  });
}

function getSiteName(id) {
  return co(function* execute() {
    const siteName = yield server.db.read.row(
      `
          SELECT name
          FROM site
          WHERE site_id = $1
        ;`,
      [id]
    );

    return siteName;
  });
}

function getFileUploadRequestRow(id) {
  return co(function* execute() {
    const file = yield server.db.read.row(
      `
          SELECT name, package_url
          FROM file_upload_request
          WHERE id = $1
        ;`,
      [id]
    );

    return file;
  });
}

function getRkiRequestRow(id) {
  return co(function* execute() {
    const rkiRequest = yield server.db.read.row(
      `
          SELECT name, full_name AS "creator"
          FROM key_request_session
          LEFT JOIN ics_user ON ics_user.id = key_request_session.request_user_id
          WHERE key_request_session_id = $1;
      ;`,
      [id]
    );

    return rkiRequest;
  });
}

function getRecipientById(id) {
  return co(function* execute() {
    const user = yield server.db.read.row(
      `
        SELECT email, company_id
        FROM ics_user
        WHERE id = $1
      `,
      [id]
    );

    return user;
  });
}

module.exports = {
  createUINotification2: (req, res, next) =>
    co(function* execute() {
      const { recipientIds } = req.body;
      const { timestamp } = req.body;
      const { level } = req.body;
      const notificationType = req.body.type;
      const { relatedEntity } = req.body;
      const { message } = req.body;

      // make sure relatedEntities are unique
      if (new Set(recipientIds).size !== recipientIds.length) {
        return next(
          new restify.BadRequestError('Found duplicates in recipients list')
        );
      }

      // verify if all users exist
      const users = yield server.db.read.rows(
        `
                SELECT id, company_id FROM ics_user
                WHERE id = ANY($1::uuid[])`,
        [recipientIds]
      );
      if (users.length !== recipientIds.length) {
        return next(new restify.NotFoundError('Recipient not found'));
      }

      // verify if all relatedEntity are of the same company
      if (!users.every(user => user.companyId === users[0].companyId)) {
        return next(
          new restify.ConflictError('Recipient must all be of the same company')
        );
      }

      const conn = yield server.db.write.getConnection();
      try {
        yield conn.execute('BEGIN');

        yield Promise.all(
          recipientIds.map(recepientId =>
            conn.execute(
              `INSERT INTO notification
                            ( id, user_id, created, read, message, related_entity, type, level )
                        VALUES
                            ( uuid_generate_v1mc(), $1, $2, $3, $4, $5, $6, $7 );
                        `,
              [
                recepientId,
                timestamp,
                false,
                message,
                relatedEntity,
                notificationType,
                level,
              ]
            )
          )
        );

        yield conn.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield conn.execute('ROLLBACK');
        throw err;
      } finally {
        conn.done();
      }

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  createUINotification: (req, res, next) =>
    co(function* execute() {
      const uuid = require('uuid/v4'); //eslint-disable-line
      const id = uuid();
      const notificationType = req.body.type;
      const { recipient } = req.body;
      const { timestamp } = req.body;
      const { level } = req.body;
      const relatedEntity = {
        type: '',
        id: '',
      };
      const email = {
        message: '',
        path: '/notifications',
      };
      let message = '';

      // Check if payload contains all required values for different notification type
      if (notificationType === 'alarm') {
        if (!req.body.alarmRuleId) {
          return next(new restify.BadRequestError('Bad entity payload'));
        }
        if (req.body.alarmRuleId !== 9) {
          if (!req.body.deviceId) {
            return next(new restify.BadRequestError('Bad entity payload'));
          }
        }

        if (!req.body.siteId) {
          return next(new restify.BadRequestError('Bad entity payload'));
        }
      }

      if (notificationType === 'file-upload') {
        if (!req.body.fileUploadRequestId) {
          return next(new restify.BadRequestError('Bad entity payload'));
        }
      }

      const rkiRegExp = /rki-*/g;

      if (notificationType.match(rkiRegExp) !== null) {
        if (!req.body.rkiRequestId) {
          return next(new restify.BadRequestError('Bad entity payload'));
        }
        relatedEntity.type = 'rkiRequest';
        relatedEntity.id = req.body.rkiRequestId;
        email.path = '/rki';
      }

      if (
        notificationType === 'rki-device-success' ||
        notificationType === 'rki-device-failed'
      ) {
        if (!req.body.deviceId) {
          return next(new restify.BadRequestError('Bad entity payload'));
        }
      }

      // Update notificaiton message based on notification type
      switch (notificationType) {
        case 'alarm': {
          const siteName = yield getSiteName(req.body.siteId);
          if (req.body.alarmRuleId !== 9) {
            const device = yield getDeviceRow(req.body.deviceId);
            message = device.name;
            if (
              ([2, '2', 5, '5', 6, '6'].includes(req.body.alarmRuleId) &&
                level === 'INFO') ||
              [3, '3', 4, '4'].includes(req.body.alarmRuleId)
            ) {
              //eslint-disable-line
              message += "'s";
            }
            relatedEntity.type = 'device';
            relatedEntity.id = req.body.deviceId;
            email.path = '/devices';
          } else if (req.body.alarmRuleId === 9) {
            message = siteName.name;
            relatedEntity.type = 'site';
            relatedEntity.id = req.body.siteId;
            email.path = '/sites';
          }
          switch (req.body.alarmRuleId) {
            case 1: {
              if (level === 'WARN') {
                message += ' is offline at';
              } else if (level === 'INFO') {
                message += ' is back online at';
              }
              break;
            }
            case 2: {
              if (level === 'WARN') {
                message += ' has low printer paper at';
              } else if (level === 'INFO') {
                message += ' printer paper was refilled at';
              }
              break;
            }
            case 3: {
              if (level === 'WARN') {
                message += ' printer is out of paper at';
              } else if (level === 'INFO') {
                message += ' printer paper was refilled at';
              }
              break;
            }
            case 4: {
              if (level === 'WARN') {
                message += ' printer is jammed at';
              } else if (level === 'INFO') {
                message += ' prinetr jam was resolved at';
              }
              break;
            }
            case 5: {
              if (level === 'WARN') {
                message += ' has been tampered at';
              } else if (level === 'INFO') {
                message += ' tamper was cleared at';
              }
              break;
            }
            case 6: {
              if (level === 'WARN') {
                message += ' has low memory at';
              } else if (level === 'INFO') {
                message += ' memory was restored at';
              }
              break;
            }
            case 7: {
              if (level === 'WARN') {
                message += ' experienced excessive bad card reads at';
              } else if (level === 'INFO') {
                message +=
                  ' is no longer experiencing excessive bad card reads at';
              }
              break;
            }
            case 8: {
              if (level === 'WARN') {
                message += ' experienced excessive reboots at';
              } else if (level === 'INFO') {
                message += ' is no longer experiencing excessive reboots at';
              }
              break;
            }
            case 9: {
              if (level === 'WARN') {
                message += ' has a majority of devices offline.';
              } else if (level === 'INFO') {
                message += ' has a majority of devices now online.';
              }
              break;
            }
            case 10: {
              if (level === 'WARN') {
                message += ' software was downgraded at';
              } else if (level === 'INFO') {
                message += ' software was restored at';
              }
              break;
            }
            // Adding a default case for eslint
            default:
              break;
          }
          if (req.body.alarmRuleId !== 9) {
            message = `${message} ${siteName.name}`;
          }
          break;
        }
        case 'file-upload': {
          const fileUploadRequest = yield getFileUploadRequestRow(
            req.body.fileUploadRequestId
          );

          // SUCCESS level is for notifications types which have completed processing successfully
          if (level === 'SUCCESS') {
            message = `${fileUploadRequest.name} is ready to download`;
          } else if (level === 'INFO') {
            message = `${fileUploadRequest.name} is now processing. We will send you a notification when it's ready to download.`;
          } else {
            message = `${fileUploadRequest.name} does not contain any files`;
          }

          relatedEntity.type = 'fileUploadRequest';
          relatedEntity.id = req.body.fileUploadRequestId;
          break;
        }
        case 'rki-device-success': {
          const device = yield getDeviceRow(req.body.deviceId);
          const rkiRequest = yield getRkiRequestRow(req.body.rkiRequestId);
          message = `RKI request ${rkiRequest.name} has completed for ${device.serialNumber}`;
          break;
        }
        case 'rki-device-failed': {
          const device = yield getDeviceRow(req.body.deviceId);
          const rkiRequest = yield getRkiRequestRow(req.body.rkiRequestId);
          message = `RKI request ${rkiRequest.name} has failed for ${device.serialNumber}`;
          break;
        }
        case 'rki-request-approved': {
          const rkiRequest = yield getRkiRequestRow(req.body.rkiRequestId);
          message = `RKI request ${rkiRequest.name} was approved`;
          break;
        }
        case 'rki-request-rejected': {
          const rkiRequest = yield getRkiRequestRow(req.body.rkiRequestId);
          message = `RKI request ${rkiRequest.name} was rejected`;
          break;
        }
        case 'rki-request-failed': {
          const rkiRequest = yield getRkiRequestRow(req.body.rkiRequestId);
          message = `RKI request ${rkiRequest.name} failed`;
          break;
        }
        case 'rki-request-created-creator': {
          const rkiRequest = yield getRkiRequestRow(req.body.rkiRequestId);
          message = `RKI request ${rkiRequest.name} was created`;
          break;
        }
        case 'rki-request-updated': {
          const rkiRequest = yield getRkiRequestRow(req.body.rkiRequestId);
          message = `RKI request ${rkiRequest.name} was updated`;
          break;
        }
        case 'rki-request-created-approver': {
          const rkiRequest = yield getRkiRequestRow(req.body.rkiRequestId);
          message = `${rkiRequest.creator} requested you to review RKI request ${rkiRequest.name}`;
          break;
        }
        default:
          break;
      }

      yield server.db.write.execute(
        `
              INSERT INTO notification
                ( id, user_id, created, read, message, related_entity, type, level )
              VALUES
                ( $1, $2, $3, $4, $5, $6, $7, $8 )
            `,
        [
          id,
          recipient,
          timestamp,
          false,
          message,
          relatedEntity,
          notificationType,
          level,
        ]
      );

      const toUser = yield getRecipientById(recipient);
      email.message = message;
      const { senderEmail } = yield mailerHelper.getSenderEmailsByCompanyId(
        toUser.companyId
      );
      yield mailer.sendNotification(
        { to: toUser.email, from: senderEmail },
        email,
        message
      ); // send email notification

      res.send(message);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
