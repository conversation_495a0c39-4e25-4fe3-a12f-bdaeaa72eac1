const restify = require('restify');

const errorHandler = require('../../lib/errorhandler');
const { server } = require('../../app');
const paginationHelper = require('../../helpers/pagination-helper');

function prepareWhere(user, start, end) {
  const params = [];

  let i = 1;
  let where = `WHERE user_id = $${i}`;
  params.push(user);

  if (start) {
    i += 1;
    where += ` AND created > $${i}`;
    params.push(start);
  }

  if (end) {
    i += 1;
    where += ` AND created <= $${i}`;
    params.push(end);
  }

  return { where, params };
}

module.exports = {
  getNotifications: async (req, res, next) => {
    try {
      const pagingParams = paginationHelper.parsePaginationParams(req);

      const WHERE = prepareWhere(req.user.sub, req.query.start, req.query.end);

      const results = await server.db.read.rows(
        `
                SELECT id, type, read, level, related_entity, created, message
                FROM notification
                ${WHERE.where}
                ORDER BY created DESC
                LIMIT ${pagingParams.pageSize}
                OFFSET ${pagingParams.offset};
            `,
        WHERE.params
      );

      const resultCount = await server.db.read.row(
        `
                SELECT COUNT(id)
                FROM notification
                ${WHERE.where};
            `,
        WHERE.params
      );

      res.send(200, {
        results,
        resultsMetadata: {
          totalResults: resultCount.count,
          pageIndex: pagingParams.pageIndex,
          pageSize: pagingParams.pageSize,
        },
      });
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getUnReadCount: async (req, res, next) => {
    try {
      const WHERE = prepareWhere(req.user.sub, req.query.start, req.query.end);

      res.send(
        200,
        (
          await server.db.read.row(
            `
                SELECT COUNT( id )
                FROM notification
                ${WHERE.where} 
                AND NOT read
            `,
            WHERE.params
          )
        ).count
      );

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  updateNotification: async (req, res, next) => {
    try {
      const notification = await server.db.write.row(
        `
                UPDATE notification SET 
                  read = $1
                WHERE id = $2
                  AND user_id = $3
                  AND read != $1
                RETURNING id
            `,
        [req.body.read, req.params.id, req.user.sub]
      );

      if (!notification) {
        return next(new restify.NotFoundError('Notification not found'));
      }

      res.send(204);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  markAllRead: async (req, res, next) => {
    try {
      await server.db.write.execute(
        `
                UPDATE notification SET 
                  read = TRUE
                WHERE user_id = $1 
                  AND NOT read;
            `,
        [req.user.sub]
      );

      res.send(204);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  deleteNotifications: async (req, res, next) => {
    try {
      await server.db.write.execute(
        `
                DELETE FROM notification
                WHERE created <= $1
            `,
        [req.body.end]
      );

      res.send(204);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
