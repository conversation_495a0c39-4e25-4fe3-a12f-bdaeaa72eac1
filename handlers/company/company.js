const fs = require('fs').promises;
const path = require('path');
const _ = require('lodash');
const co = require('co');
const uuid = require('uuid/v4');
const restify = require('restify');

const { server } = require('../../app');
const companyHelper = require('../../helpers/company-helper');
const errorHandler = require('../../lib/errorhandler');

module.exports = {
  createCompany: async (req, res, next) => {
    const { body } = req;

    if (body.featureFlags && body.featureFlags.length) {
      const featureFlags = (
        await server.db.read.rows(
          ` 
                    SELECT code FROM feature_flag
                    WHERE code = ANY($1);
                `,
          [body.featureFlags]
        )
      ).map(featureFlag => featureFlag.code);

      if (featureFlags.length !== body.featureFlags.length) {
        return next(
          new restify.NotFoundError(
            `Feature flag(s) ${_.difference(
              body.featureFlags,
              featureFlags
            ).join(', ')} not found`
          )
        );
      }
    }

    if (req.body.deviceKeys && req.body.deviceKeys.length) {
      const companyDeviceTypes = req.body.deviceKeys.map(
        deviceKey => deviceKey.deviceType
      );

      const deviceTypes = (
        await server.db.read.rows(
          ` 
                    SELECT device_type FROM product_type
                    WHERE device_type = ANY($1);
                `,
          [companyDeviceTypes]
        )
      ).map(deviceKey => deviceKey.deviceType);

      if (deviceTypes.length !== companyDeviceTypes.length) {
        return next(
          new restify.NotFoundError(
            `Device type(s) ${_.difference(
              companyDeviceTypes,
              deviceTypes
            ).join(', ')} not found`
          )
        );
      }
    }

    if (req.body.deviceTypes && req.body.deviceTypes.length) {
      const companyDeviceTypes = req.body.deviceTypes.map(
        deviceTypes => deviceTypes.deviceType
      );

      const deviceTypes = (
        await server.db.read.rows(
          ` 
                    SELECT device_type FROM product_type
                    WHERE device_type = ANY($1);
                `,
          [companyDeviceTypes]
        )
      ).map(deviceKey => deviceKey.deviceType);

      if (deviceTypes.length !== companyDeviceTypes.length) {
        return next(
          new restify.NotFoundError(
            `Device type(s) ${_.difference(
              companyDeviceTypes,
              deviceTypes
            ).join(', ')} not found`
          )
        );
      }
    }

    if (req.body.keyGroups && req.body.keyGroups.length) {
      const certificateIssuerIds = [
        ...new Set(
          req.body.keyGroups.map(keyGroup => keyGroup.certificateIssuerId)
        ),
      ];
      const certificateIssuers = (
        await server.db.read.rows(
          ` 
                    SELECT certificate_issuer_id FROM certificate_issuer
                    WHERE certificate_issuer_id = ANY($1);
                `,
          [certificateIssuerIds]
        )
      ).map(certificateIssuer => certificateIssuer.id);
      if (certificateIssuers.length !== certificateIssuerIds.length) {
        return next(
          new restify.NotFoundError(
            `Certificate issuer(s) ${_.difference(
              certificateIssuerIds,
              certificateIssuers
            ).join(', ')} not found`
          )
        );
      }
    }

    if (req.body.promptTemplates && req.body.promptTemplates.length) {
      const promptTemplates = (
        await server.db.read.rows(
          ` 
                    SELECT id FROM prompt_template
                    WHERE id = ANY($1);
                `,
          [req.body.promptTemplates]
        )
      ).map(promptTemplate => promptTemplate.id);

      if (promptTemplates.length !== req.body.promptTemplates.length) {
        return next(
          new restify.NotFoundError(
            `Prompt template(s) ${_.difference(
              req.body.promptTemplates,
              promptTemplates
            ).join(', ')} not found`
          )
        );
      }
    }

    if (req.body.companyRelationships && req.body.companyRelationships.length) {
      const companies = (
        await server.db.read.rows(
          ` 
                    SELECT id FROM company
                    WHERE id = ANY($1);
                `,
          [req.body.companyRelationships]
        )
      ).map(c => c.id);

      if (companies.length !== req.body.companyRelationships.length) {
        return next(
          new restify.NotFoundError(
            `Company(s) ${_.difference(
              req.body.companyRelationships,
              companies
            ).join(', ')} not found`
          )
        );
      }
    }

    if (body.reference) {
      const companyCount = await server.db.read.row(
        `
                SELECT COUNT(1) FROM company
                WHERE reference = $1;
            `,
        [body.reference]
      );

      if (companyCount && companyCount.count) {
        return next(
          new restify.ConflictError(
            `Company with reference of ${body.reference} exists`
          )
        );
      }
    }

    const conn = await server.db.write.getConnection();
    try {
      await conn.execute('BEGIN');

      const properties = [
        'name',
        'reference',
        'supportEmail',
        'senderEmail',
        'sessionExpiryDeviceMins',
        'sessionExpiryUserMins',
        'promptsetPackageVersion',
        'deletable',
      ];

      const params = properties
        .filter(property =>
          Object.prototype.hasOwnProperty.call(body, property)
        )
        .map(property => body[property]);

      const company = (
        await conn.execute(
          `
                INSERT INTO company ( id,
                    ${properties
                      .filter(property =>
                        Object.prototype.hasOwnProperty.call(body, property)
                      )
                      .map(property => `${_.snakeCase(property)}`)
                      .join(', ')}
                ) VALUES ( uuid_generate_v1mc(),
                    ${params.map((param, index) => `$${index + 1}`).join(', ')}
                ) RETURNING id, name, default_site, reference, support_email, sender_email,
                    session_expiry_device_mins, session_expiry_user_mins,
                    promptset_package_version;
            `,
          params
        )
      ).rows[0];

      // Feature Flags
      company.featureFlags = [];
      if (body.featureFlags && body.featureFlags.length) {
        company.featureFlags = (
          await conn.execute(
            `
                    INSERT INTO company_feature_flag ( company, feature_flag ) 
                    SELECT * FROM UNNEST ( $1::uuid[], $2::text[] )
                    RETURNING feature_flag;
                `,
            [body.featureFlags.map(() => company.id), body.featureFlags]
          )
        ).rows.map(featureFlag => featureFlag.featureFlag);
      }

      // Device Keys
      company.deviceKeys = [];
      if (req.body.deviceKeys && req.body.deviceKeys.length) {
        company.deviceKeys = (
          await conn.execute(
            `
                    INSERT INTO company_device_keys ( company, device_type, key, key_type )
                    SELECT * FROM UNNEST ( $1::uuid[], $2::text[], $3::text[], $4::text[] )
                    RETURNING device_type, key, key_type;
                `,
            [
              req.body.deviceKeys.map(() => company.id),
              req.body.deviceKeys.map(deviceKey => deviceKey.deviceType),
              req.body.deviceKeys.map(deviceKey => deviceKey.key),
              req.body.deviceKeys.map(deviceKey => deviceKey.keyType),
            ]
          )
        ).rows;
      }

      // Device Types
      company.deviceTypes = [];
      if (req.body.deviceTypes && req.body.deviceTypes.length) {
        company.deviceTypes = (
          await conn.execute(
            `
                    INSERT INTO company_product_type ( company, display_name, device_type )
                    SELECT * FROM UNNEST ( $1::uuid[], $2::text[], $3::text[] )
                    RETURNING display_name, device_type;
                `,
            [
              req.body.deviceTypes.map(() => company.id),
              req.body.deviceTypes.map(deviceType => deviceType.displayName),
              req.body.deviceTypes.map(deviceType => deviceType.deviceType),
            ]
          )
        ).rows;
      }

      // Prompt Templates
      company.promptTemplates = [];
      if (req.body.promptTemplates && req.body.promptTemplates.length) {
        company.promptTemplates = (
          await conn.execute(
            `
                    INSERT INTO company_prompt_template ( company, prompt_template )
                    SELECT * FROM UNNEST ( $1::uuid[], $2::uuid[] )
                    RETURNING prompt_template;
                `,
            [
              req.body.promptTemplates.map(() => company.id),
              req.body.promptTemplates,
            ]
          )
        ).rows.map(promptTemplate => promptTemplate.promptTemplate);
      }

      // Company Relationships
      company.companyRelationships = [];
      if (
        req.body.companyRelationships &&
        req.body.companyRelationships.length
      ) {
        company.companyRelationships = (
          await conn.execute(
            `
                    INSERT INTO company_relationship ( company_id, allowed_company_id )
                    SELECT * FROM UNNEST ( $1::uuid[], $2::uuid[] )
                    RETURNING company_id, allowed_company_id;
                `,
            [
              req.body.companyRelationships.map(() => company.id),
              req.body.companyRelationships,
            ]
          )
        ).rows.map(relationship => relationship.allowedCompanyId);
      }

      // Key Groups
      company.keyGroups = [];
      if (req.body.keyGroups && req.body.keyGroups.length) {
        company.keyGroups = (
          await conn.execute(
            `
                    INSERT INTO key_group ( key_group_id, company_id, key_group_name, key_group_ref, certificate_issuer_id )
                    SELECT * FROM UNNEST ( $1::uuid[], $2::uuid[], $3::text[], $4::text[], $5::int[] )
                    RETURNING key_group_id AS id, key_group_name AS name, key_group_ref AS ref;
                `,
            [
              req.body.keyGroups.map(() => uuid()),
              req.body.keyGroups.map(() => company.id),
              req.body.keyGroups.map(keyGroup => keyGroup.name),
              req.body.keyGroups.map(keyGroup => keyGroup.ref),
              req.body.keyGroups.map(keyGroup => keyGroup.certificateIssuerId),
            ]
          )
        ).rows;
      }

      await conn.execute('COMMIT');
      res.send(company);

      return next();
    } catch (err) {
      await conn.execute('ROLLBACK');
      return next(new restify.errors.InternalServerError(err));
    } finally {
      conn.done();
    }
  },

  updateCompany: async (req, res, next) => {
    const { body } = req;
    const companyId = req.params.id;

    if (!Object.keys(body).length) {
      return next(new restify.BadRequestError('No payload available'));
    }

    let company = await server.db.read.row(
      `
            SELECT
                id, name, default_site, reference, support_email, sender_email,
                session_expiry_device_mins, session_expiry_user_mins, promptset_package_version, deletable
            FROM company
            WHERE
                id = $1;
        `,
      [companyId]
    );

    if (!company) {
      return next(new restify.NotFoundError('Company not found'));
    }

    if (
      typeof req.body.deletable === 'boolean' &&
      req.body.deletable !== company.deletable
    ) {
      const isSuperAdmin = req.user.roles.includes('SUPER_ADMIN');
      if (!isSuperAdmin) {
        return next(
          new restify.NotAuthorizedError(
            'Contact Super Admin to change company deletable value'
          )
        );
      }
    }

    if (body.defaultSite) {
      const defaultSite = await server.db.read.row(
        `
                    SELECT * FROM site
                    WHERE
                        active AND
                        site_id = $1 AND
                        company_id = $2;
                `,
        [body.defaultSite, companyId]
      );

      if (!defaultSite) {
        return next(new restify.NotFoundError('Default site not found'));
      }
    }

    if (req.body.featureFlags && req.body.featureFlags.length) {
      const featureFlags = (
        await server.db.read.rows(
          `
                    SELECT code FROM feature_flag
                    WHERE code = ANY($1);
                `,
          [req.body.featureFlags]
        )
      ).map(featureFlag => featureFlag.code);

      if (featureFlags.length !== req.body.featureFlags.length) {
        return next(
          new restify.NotFoundError(
            `Feature flag(s) ${_.difference(
              req.body.featureFlags,
              featureFlags
            ).join(', ')} not found`
          )
        );
      }
    }

    if (req.body.deviceKeys && req.body.deviceKeys.length) {
      const companyDeviceTypes = req.body.deviceKeys.map(
        deviceKey => deviceKey.deviceType
      );

      const deviceTypes = (
        await server.db.read.rows(
          `
                    SELECT device_type FROM product_type
                    WHERE device_type = ANY($1);
                `,
          [companyDeviceTypes]
        )
      ).map(deviceKey => deviceKey.deviceType);

      if (deviceTypes.length !== companyDeviceTypes.length) {
        return next(
          new restify.NotFoundError(
            `Device type(s) ${_.difference(
              companyDeviceTypes,
              deviceTypes
            ).join(', ')} not found`
          )
        );
      }
    }

    if (req.body.deviceTypes && req.body.deviceTypes.length) {
      const companyDeviceTypes = req.body.deviceTypes.map(
        deviceTypes => deviceTypes.deviceType
      );

      const deviceTypes = (
        await server.db.read.rows(
          `
                    SELECT device_type FROM product_type
                    WHERE device_type = ANY($1);
                `,
          [companyDeviceTypes]
        )
      ).map(deviceKey => deviceKey.deviceType);

      if (deviceTypes.length !== companyDeviceTypes.length) {
        return next(
          new restify.NotFoundError(
            `Device type(s) ${_.difference(
              companyDeviceTypes,
              deviceTypes
            ).join(', ')} not found`
          )
        );
      }
    }

    if (req.body.promptTemplates && req.body.promptTemplates.length) {
      const promptTemplates = (
        await server.db.read.rows(
          `
                    SELECT id FROM prompt_template
                    WHERE id = ANY($1);
                `,
          [req.body.promptTemplates]
        )
      ).map(promptTemplate => promptTemplate.id);

      if (promptTemplates.length !== req.body.promptTemplates.length) {
        return next(
          new restify.NotFoundError(
            `Prompt template(s) ${_.difference(
              req.body.promptTemplates,
              promptTemplates
            ).join(', ')} not found`
          )
        );
      }
    }

    if (req.body.companyRelationships && req.body.companyRelationships.length) {
      const companies = (
        await server.db.read.rows(
          `
                    SELECT id FROM company
                    WHERE id = ANY($1);
                `,
          [req.body.companyRelationships]
        )
      ).map(c => c.id);

      if (companies.length !== req.body.companyRelationships.length) {
        return next(
          new restify.NotFoundError(
            `Company(s) ${_.difference(
              req.body.companyRelationships,
              companies
            ).join(', ')} not found`
          )
        );
      }
    }

    const conn = await server.db.write.getConnection();
    try {
      await conn.execute('BEGIN');

      body.lastEditedDate = 'now()';
      const properties = [
        'name',
        'reference',
        'defaultSite',
        'supportEmail',
        'senderEmail',
        'sessionExpiryDeviceMins',
        'sessionExpiryUserMins',
        'promptsetPackageVersion',
        'deletable',
        'lastEditedDate',
      ];

      const params = [
        companyId,
        ...properties
          .filter(property =>
            Object.prototype.hasOwnProperty.call(body, property)
          )
          .map(property => body[property]),
      ];

      if (params.length > 1) {
        const { 0: firstRow } = (
          await conn.execute(
            `
                    UPDATE company SET
                        ${properties
                          .filter(property =>
                            Object.prototype.hasOwnProperty.call(body, property)
                          )
                          .map(
                            (property, index) =>
                              `${_.snakeCase(property)} = $${index + 2}`
                          )
                          .join(', ')}
                    WHERE 
                        id = $1
                    RETURNING id, name, default_site, reference, support_email, sender_email,
                        session_expiry_device_mins, session_expiry_user_mins,
                        promptset_package_version, deletable, last_edited_date;
                `,
            params
          )
        ).rows;
        company = firstRow;
      }

      const companyDependency = await server.db.read.row(
        `
                SELECT
                    ARRAY(SELECT feature_flag FROM company_feature_flag f WHERE f.company = id) AS FeatureFlags,
                    ARRAY(SELECT json_build_object('key', key, 'deviceType', device_type) FROM company_device_keys k WHERE k.company = id) AS DeviceKeys,
                    ARRAY(SELECT json_build_object('displayName', display_name, 'deviceType', device_type) FROM company_product_type p WHERE p.company = id) AS DeviceTypes,
                    ARRAY(SELECT prompt_template FROM company_prompt_template t WHERE t.company = id) AS PromptTemplates,
                    ARRAY(SELECT allowed_company_id FROM company_relationship r WHERE r.company_id = id) AS CompanyRelationships
                FROM company
                WHERE
                    id = $1;
            `,
        [companyId]
      );

      company.featureFlags = companyDependency.featureflags;
      company.deviceKeys = companyDependency.devicekeys;
      company.deviceTypes = companyDependency.devicetypes;
      company.promptTemplates = companyDependency.prompttemplates;
      company.companyRelationships = companyDependency.companyrelationships;

      // Feature Flags
      if (req.body.featureFlags && req.body.featureFlags.length) {
        await conn.execute(
          'DELETE FROM company_feature_flag WHERE company = $1;',
          [companyId]
        );

        company.featureFlags = (
          await conn.execute(
            `
                    INSERT INTO company_feature_flag ( company, feature_flag )
                    SELECT * FROM UNNEST ( $1::uuid[], $2::text[] )
                    RETURNING feature_flag;
                `,
            [req.body.featureFlags.map(() => company.id), req.body.featureFlags]
          )
        ).rows.map(featureFlag => featureFlag.featureFlag);
      }

      // Device Keys
      if (req.body.deviceKeys && req.body.deviceKeys.length) {
        await conn.execute(
          'DELETE FROM company_device_keys WHERE company = $1;',
          [companyId]
        );

        company.deviceKeys = (
          await conn.execute(
            `
                    INSERT INTO company_device_keys ( company, device_type, key, key_type )
                    SELECT * FROM UNNEST ( $1::uuid[], $2::text[], $3::text[], $4::text[] )
                    RETURNING device_type, key, key_type;
                `,
            [
              req.body.deviceKeys.map(() => company.id),
              req.body.deviceKeys.map(deviceKey => deviceKey.deviceType),
              req.body.deviceKeys.map(deviceKey => deviceKey.key),
              req.body.deviceKeys.map(deviceKey => deviceKey.keyType),
            ]
          )
        ).rows;
      }

      // Device Types
      if (req.body.deviceTypes && req.body.deviceTypes.length) {
        await conn.execute(
          'DELETE FROM company_product_type WHERE company = $1;',
          [companyId]
        );

        company.deviceTypes = (
          await conn.execute(
            `
                    INSERT INTO company_product_type ( company, display_name, device_type )
                    SELECT * FROM UNNEST ( $1::uuid[], $2::text[], $3::text[] )
                    RETURNING display_name, device_type;
                `,
            [
              req.body.deviceTypes.map(() => company.id),
              req.body.deviceTypes.map(deviceType => deviceType.displayName),
              req.body.deviceTypes.map(deviceType => deviceType.deviceType),
            ]
          )
        ).rows;
      }

      // Prompt Templates
      if (req.body.promptTemplates && req.body.promptTemplates.length) {
        await conn.execute(
          'DELETE FROM company_prompt_template WHERE company = $1;',
          [companyId]
        );

        company.promptTemplates = (
          await conn.execute(
            `
                    INSERT INTO company_prompt_template ( company, prompt_template )
                    SELECT * FROM UNNEST ( $1::uuid[], $2::uuid[] )
                    RETURNING prompt_template;
                `,
            [
              req.body.promptTemplates.map(() => company.id),
              req.body.promptTemplates,
            ]
          )
        ).rows.map(promptTemplate => promptTemplate.promptTemplate);
      }

      // Company Relationships
      if (
        req.body.companyRelationships &&
        req.body.companyRelationships.length
      ) {
        await conn.execute(
          'DELETE FROM company_relationship WHERE company_id = $1;',
          [companyId]
        );

        company.companyRelationships = (
          await conn.execute(
            `
                    INSERT INTO company_relationship ( company_id, allowed_company_id )
                    SELECT * FROM UNNEST ( $1::uuid[], $2::uuid[] )
                    RETURNING company_id, allowed_company_id;
                `,
            [
              req.body.companyRelationships.map(() => company.id),
              req.body.companyRelationships,
            ]
          )
        ).rows.map(relationship => relationship.allowedCompanyId);
      }

      await conn.execute('COMMIT');

      res.send(company);
      return next();
    } catch (err) {
      await conn.execute('ROLLBACK');
      return next(new restify.errors.InternalServerError(err));
    } finally {
      conn.done();
    }
  },

  deleteCompany: async (req, res, next) => {
    const companyId = req.params.id;

    const company = await server.db.read.row(
      `
            SELECT deletable FROM company
            WHERE id = $1;
        `,
      [companyId]
    );

    if (!company) {
      return next(new restify.errors.NotFoundError('Company not found'));
    }

    // Protection against mistakenly deleting live prod companies
    if (!company.deletable) {
      return next(
        new restify.errors.BadRequestError('Company can not be deleted')
      );
    }

    const teardownPath = path.join(__dirname, '/teardown/');

    const commands = (
      await fs
        .readdir(teardownPath)
        .then(files =>
          Promise.all(
            files.map(file =>
              fs
                .readFile(path.resolve(teardownPath, file))
                .then(buffer => buffer.toString())
            )
          )
        )
    )
      .join('\n')
      .split(';');

    const conn = await server.db.write.getConnection();
    try {
      await conn.execute('BEGIN');

      await conn.execute('SET CONSTRAINTS ALL DEFERRED;');

      for (let i = 0; i < commands.length; i++) {
        if (commands[i]) {
          const before = new Date();
          // eslint-disable-next-line no-await-in-loop
          await conn.execute(commands[i], [companyId]);
          const after = new Date();
          const diff = ((after.getTime() - before.getTime()) / 1000).toFixed(3);
          req.log.info(
            `Delete company ${companyId} - ${commands[i]} cost: ${diff} s`
          );
        }
      }

      await conn.execute('COMMIT');

      res.send(200);
      return next();
    } catch (err) {
      await conn.execute('ROLLBACK');
      return next(new restify.errors.InternalServerError(err));
    } finally {
      conn.done();
    }
  },

  /**
   * Get all companies which are allowed to view my company (suppliers)
   */
  getSuppliers: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;

      // Get all companies which are allowed to view my company (suppliers)
      const suppliers = yield server.db.read.rows(
        `
                SELECT c.id,
                       c.name
                  FROM company c
                  JOIN company_relationship cr ON cr.allowed_company_id = c.id
                    AND cr.company_id = $1;
            `,
        [companyId]
      );
      res.send(suppliers);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Get all companies which my company can see (consumers)
   */
  getConsumers: (req, res, next) =>
    co(function* execute() {
      const companyId = req.user.company.id;

      // Get all companies which my company can see (consumers)
      const consumers = yield server.db.read.rows(
        `
                SELECT c.id,
                       c.name
                  FROM company c
                  JOIN company_relationship cr ON cr.company_id = c.id
                    AND cr.allowed_company_id = $1;
                `,
        [companyId]
      );
      res.send(consumers);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Get the current user's company
   */
  getSelf: (req, res, next) =>
    co(function* execute() {
      req.log.info(`userId: ${req.user.sub}`);
      req.log.info(`companyId: ${req.user.company.id}`);

      const company = yield companyHelper.getCompanyByID(req.user.company.id);
      res.send(company);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Get a specific company by ID
   * This function checks to ensure the caller is allowed to see the specified company
   *
   * Also used by TGW API server for verifying company id parameters
   */
  getCompany: (req, res, next) => {
    const companyId = req.params.id;
    const userCompanyId = req.user.company.id;

    return co(function* execute() {
      // Join ensures we only return companies the user should be able to see
      // Ie. this is my own company, or my company is listed as 'allowed'
      const result = yield server.db.read.row(
        `
                SELECT c.id, c.name
                FROM company c
                LEFT OUTER JOIN company_relationship cr ON cr.company_id = c.id
                WHERE c.id = $1 AND ( c.id = $2 OR cr.allowed_company_id = $2) LIMIT 1;
            `,
        [companyId, userCompanyId]
      );

      if (!result) {
        return next(new restify.NotFoundError('Company not found'));
      }
      res.send(result);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  /**
   * Get all companies, filtered by name
   */
  getAll: async (req, res, next) => {
    try {
      const companies = await companyHelper.getCompanies(req.query.name);
      res.send(companies);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getLanguages: async (req, res, next) => {
    const userCompanyId = req.user.company.id;
    const companyId = req.params.id;

    if (userCompanyId !== companyId) {
      return next(
        new restify.errors.ForbiddenError(
          'CompanyID parameter does not match with user company.'
        )
      );
    }

    try {
      // TODO: Refactor and move below SQL to language service.
      const languages = await server.db.read.rows(
        `
                SELECT language_support_id, language, iso_code, "default" 
                    FROM language_support 
                WHERE company_id = $1 AND deleted = false;
            `,
        [companyId]
      );

      res.send(languages);

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
