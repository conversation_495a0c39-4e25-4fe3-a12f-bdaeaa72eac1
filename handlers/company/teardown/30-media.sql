DELETE
FROM prompt_state_override pso
WHERE pso.prompt_id IN (SELECT id FROM prompt WHERE company = $1);

DELETE
FROM prompt_state_override pso
USING ics_user u
WHERE pso.created_by = u.id
AND u.company_id = $1;

DELETE FROM prompt_exception e
USING prompt p
WHERE e.prompt = p.id AND
  p.company = $1;

DELETE FROM prompt_set_approvers a
USING prompt_set p
WHERE a.prompt_set_id = p.id AND
  p.company = $1;

DELETE FROM prompt_assignment a
USING prompt p
WHERE a.default_prompt = p.id AND
  p.company = $1;

DELETE FROM softkey_assignments sa
USING prompt p
WHERE sa.prompt = p.id AND
  p.company = $1;

DELETE FROM prompt_state_override as pso
USING prompt p
WHERE p.id = pso.prompt_id and p.company = $1;

DELETE FROM prompt p
WHERE p.company = $1;

DELETE FROM prompt_set_language_support as psls
WHERE psls.prompt_set_id IN (SELECT id FROM prompt_set WHERE company = $1);

DELETE FROM prompt_set s
WHERE s.company = $1;

DELETE FROM day_part
WHERE company = $1;

DELETE FROM asset
WHERE company = $1;

DELETE FROM asset_package
WHERE company = $1;

DELETE FROM touchmap t
USING ics_user u
WHERE t.created_by = u.id AND
  u.company_id = $1;