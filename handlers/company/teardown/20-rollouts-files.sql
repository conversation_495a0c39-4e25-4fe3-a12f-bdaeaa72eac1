DELETE FROM public.target_release_dependency trd
USING target_release tr, site s, target t
WHERE trd.target_release_id = tr.target_release_id AND 
tr.target_id = t.target_id AND 
s.site_id = t.site_id AND 
s.company_id = $1;

DELETE FROM public.target_release_dependency trd
USING target_release tr, site s, target t
WHERE trd.dependant_target_release_id = tr.target_release_id AND 
tr.target_id = t.target_id AND 
s.site_id = t.site_id AND 
s.company_id = $1;

DELETE FROM public.target_release tr
WHERE release_id IN (
    SELECT r.release_id
    FROM release r LEFT JOIN software s
    ON r.software_id = s.software_id
    WHERE s.company_id=$1
);


DELETE FROM public.target_release tr
WHERE tr.target_id IN (
	SELECT t.target_id FROM TARGET t
	INNER JOIN SITE s ON s.site_id = t.site_id
	WHERE s.company_id = $1
);


DELETE FROM offline_package_software ops
USING offline_package op, ics_user u
WHERE op.id = ops.offline_package_id AND
  op.created_by = u.id AND
  u.company_id = $1;

DELETE FROM offline_package_rki opr
USING offline_package op
WHERE op.id=opr.offline_package_id AND
op.company_id = $1;

DELETE FROM offline_package op
USING ics_user u
WHERE op.created_by = u.id AND
  u.company_id = $1;

DELETE FROM release r
USING software s 
WHERE s.company_id = $1 
AND s.software_id = r.software_id;

DELETE FROM software s
WHERE s.company_id = $1;

DELETE FROM device_rki_key_bundle drkb
USING target t, site s
WHERE t.target_id = drkb.device_id AND
  t.site_id = s.site_id AND
  s.company_id = $1;

DELETE FROM device_files f
USING target t, site s
WHERE
  f.device_id = t.target_id AND
  t.site_id = s.site_id AND
  s.company_id = $1;

DELETE FROM file f
USING target t, site s
WHERE
  f.device_id = t.target_id AND
  t.site_id = s.site_id AND
  s.company_id = $1;

DELETE FROM file_upload_job j
USING file_upload_request r, ics_user u
WHERE
  j.request_id::UUID = r.id::UUID AND
  r.created_by::UUID = u.id AND
  u.company_id = $1;

DELETE FROM file_upload_job j
USING job, ics_user u
WHERE
  j.job_id::UUID = job.id::UUID AND
  job.created_by::UUID = u.id AND
  u.company_id = $1;

DELETE FROM file_upload_job j
USING job, target t, site s, company c
WHERE
  j.job_id::UUID = job.id::UUID AND
  t.target_id = job.device_id AND
  t.site_id = s.site_id AND
  s.company_id = $1;

DELETE FROM file_upload_notification n
USING file_upload_request r, ics_user u
WHERE
  n.request_id::UUID = r.id::UUID AND
  r.created_by::UUID = u.id AND
  u.company_id = $1;

DELETE FROM file_upload_request r
USING ics_user u
WHERE
  r.created_by::UUID = u.id AND
  u.company_id = $1;

DELETE FROM bulk_operation_item boi
USING target t, site s
WHERE
  boi.device_id = t.target_id AND
  t.site_id = s.site_id AND
  s.company_id = $1;


DELETE FROM custom_attribute.custom_attribute_entity_values v
USING custom_attribute.attribute_definition a 
WHERE a.company_id = $1 
AND  v.attribute_definition_id = a.attribute_definition_id;

DELETE FROM custom_attribute.attribute_definition a
WHERE a.company_id = $1;

DELETE FROM custom_attribute.custom_attribute_group g
WHERE g.company_id = $1;