DELETE FROM key_request_device krd 
where krd.key_request_session_id in (select krs.key_request_session_id from key_request_session krs where krs.company_id = $1);

DELETE FROM key_request_session_authorizer
USING key_request_session
WHERE key_request_session_authorizer.key_request_session_id = key_request_session.key_request_session_id AND
  key_request_session.company_id = $1;

DELETE FROM key_request_session
WHERE key_request_session.company_id = $1;

-- Sites get deleted later
UPDATE site SET
  key_group_id = NULL
WHERE company_id = $1;

DELETE FROM key_group
WHERE key_group.company_id = $1;

DELETE FROM tamper_challenge_response t
USING ics_user u
WHERE t.created_by = u.id AND
  u.company_id = $1;