DELETE FROM web_activity wa
USING ics_user u
WHERE wa.user_id::UUID = u.id AND
  u.company_id = $1;

DELETE FROM notification n
USING ics_user u
WHERE n.user_id::UUID = u.id AND
  u.company_id = $1;

DELETE FROM user_group_authorization_site_group
USING user_group
WHERE user_group_id = user_group.id AND
  user_group.company_id = $1;

DELETE FROM user_group_authorization_site_group ugasg
USING authorization_site_group asg
WHERE ugasg.authorization_site_group_id = asg.id AND
  asg.company_id = $1;
 

DELETE FROM user_group_user
USING user_group
WHERE user_group_id = user_group.id AND
  user_group.company_id = $1;

DELETE FROM user_group
WHERE user_group.company_id = $1;

DELETE FROM authorization_site_group_site
USING site
WHERE authorization_site_group_site.site_id = site.site_id AND
  site.company_id = $1;

DELETE FROM ics_state.device_data dd
WHERE dd.device_id IN (
	SELECT t.target_id FROM TARGET t
	INNER JOIN SITE s ON s.site_id = t.site_id
	WHERE s.company_id = $1
);

DELETE FROM public.payment_dashboard pd
WHERE pd.device_id IN (
	SELECT t.target_id FROM TARGET t
	INNER JOIN SITE s ON s.site_id = t.site_id
	WHERE s.company_id = $1
);

DELETE FROM public.device_profile dp
WHERE dp.target_id IN (
	SELECT t.target_id FROM TARGET t
	INNER JOIN SITE s ON s.site_id = t.site_id
	WHERE s.company_id = $1
);

DELETE FROM target
USING site
WHERE site.site_id = target.site_id AND
  site.company_id = $1;

UPDATE company
SET default_site = null
WHERE company.id = $1;

-- site tag
DELETE FROM site_tag
USING site
WHERE site_tag.site_id = site.site_id AND
  site.company_id = $1;

DELETE FROM site
WHERE company_id = $1;

DELETE FROM tag
WHERE company_id = $1;

DELETE FROM authorization_site_group_company
WHERE company_id = $1;

DELETE FROM authorization_site_group_company asgc
USING authorization_site_group asg
WHERE asgc.authorization_site_group_id = asg.id AND
asg.company_id = $1;

DELETE FROM authorization_site_group
WHERE company_id = $1;

DELETE FROM device_sync_config
WHERE company_id = $1;

DELETE FROM user_change_field f
USING user_change c, ics_user u
WHERE f.change_id = c.id AND
  (c.action_user_id = u.id OR c.target_user_id = u.id) AND
  u.company_id = $1;

DELETE FROM user_change c
USING ics_user u
WHERE (c.action_user_id = u.id OR c.target_user_id = u.id) AND
  u.company_id = $1;

DELETE FROM email_token t
USING ics_user u
WHERE t.user_id = u.id AND
  u.company_id = $1;

DELETE FROM auth_history
USING ics_user
WHERE user_id = ics_user.id AND
  ics_user.company_id = $1;

DELETE FROM user_role
USING ics_user
WHERE user_id = ics_user.id AND
  ics_user.company_id = $1;

DELETE FROM ics_user
WHERE ics_user.company_id = $1;


DELETE FROM company_relationship
WHERE company_id = $1 OR
  allowed_company_id = $1;

DELETE FROM company_feature_flag
WHERE company = $1;

DELETE FROM company_prompt_template
WHERE company = $1;

DELETE FROM company_product_type
WHERE company = $1;

DELETE FROM company_device_keys
WHERE company = $1;

DELETE FROM language_support
WHERE company_id = $1;

DELETE FROM company
WHERE id = $1;