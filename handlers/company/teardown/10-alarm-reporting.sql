DELETE FROM ics_alarm.alarm_notification n
USING ics_alarm.alarm a
WHERE n.alarm_id = a.id AND
  a.company_id = $1;

DELETE FROM ics_alarm.alarm_subject s
USING ics_alarm.alarm a
WHERE s.alarm_id = a.id AND
  a.company_id = $1;

DELETE FROM ics_alarm.alarm_rule r
USING ics_alarm.alarm a
WHERE r.alarm_id = a.id AND
  a.company_id = $1;

DELETE FROM ics_alarm.alarm a
WHERE a.company_id = $1;

DELETE FROM ics_alarm.alarm_rules_settings ars
USING site s
WHERE ars.site_id = s.site_id AND
  s.company_id = $1;

DELETE FROM site_alarms sa
USING site s
WHERE sa.site_id = s.site_id AND
  s.company_id = $1;

DELETE FROM device_states ds
USING target t, site s
WHERE ds.device_id = t.target_id AND
  t.site_id = s.site_id AND
  s.company_id = $1;

DELETE FROM device_versions dv
USING target t, site s
WHERE dv.device_id = t.target_id AND
t.site_id = s.site_id AND
s.company_id = $1;

DELETE FROM device_alarms da
USING target t, site s
WHERE da.device_id = t.target_id AND
t.site_id = s.site_id AND
s.company_id = $1;

DELETE FROM report_alarm_history rah
USING target t, site s
WHERE
  ( ( rah.device_id = t.target_id AND t.site_id = s.site_id ) OR (rah.site_id = s.site_id ) )
  AND s.company_id = $1;

DELETE FROM report_api_audit raa
WHERE raa.company_id = $1;

DELETE FROM report_audit_alarm_history raah
WHERE raah.company_id = $1;