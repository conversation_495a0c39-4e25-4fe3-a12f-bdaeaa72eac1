const url = require('url');
const co = require('co');
const jwt = require('jsonwebtoken');
const restify = require('restify');

const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');

// TODO: Marcelo - What does web activity do ? (prombt builder for analitycs purposes) (We need to test this somehow)
module.exports = {
  createWebActivity: (req, res, next) =>
    co(function* execute() {
      const token = jwt.decode(
        req.headers.authorization.replace('Bearer ', '')
      );
      if (!token) {
        return next(
          new restify.errors.BadRequestError('Unable to decode user token')
        );
      }

      if (!req.headers.referer) {
        return next(
          new restify.errors.BadRequestError('Missing referer header')
        );
      }
      const referer = url.parse(req.headers.referer);

      const activityType = yield server.db.read.row(
        `
                SELECT * FROM web_activity_activity_types
                WHERE activity_type = $1;
            `,
        [req.body.activityType]
      );

      if (!activityType) {
        return next(new restify.NotFoundError('Activity type not found'));
      }

      yield server.db.write.execute(
        `
                INSERT INTO web_activity
                    ( user_id, user_roles, date_created, source_domain, source_path,
                      activity_type, object_id, command, result, feature_flags,
                      token_created, token_expiry )
                VALUES
                    ( $1, $2, NOW(), $3, $4, $5, $6, $7, $8, $9, $10, $11);
            `,
        [
          req.user.sub,
          req.user.roles,
          referer.hostname,
          referer.pathname,
          req.body.activityType,
          req.body.objectId,
          req.body.command,
          req.body.result,
          req.user.company.featureFlags,
          new Date(token.iat * 1000), // Issued At
          new Date(token.exp * 1000), // Expiration Time
        ]
      );

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
