const moment = require('moment');
const restify = require('restify');
const stringify = require('csv-stringify');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const { config } = require('../../env');
const AWS = require('../../lib/aws');
const paginationHelper = require('../../helpers/pagination-helper');
const { msToTime } = require('../../lib/utils');

const csvOptions = {
  delimiter: ',',
  endLine: '\n',
  columns: [
    'siteId',
    'siteName',
    'referenceId',
    'name',
    'runtime',
    'count',
    'date',
  ],
  escapeChar: '"',
  enclosedChar: '"',
  header: true,
};

async function processSpecifiedSitesCSV(
  req,
  res,
  next,
  pagination,
  start,
  end,
  authorizedSites,
  mediaNames
) {
  const stringifier = stringify(csvOptions);
  stringifier.pipe(res);

  const siteNameLookup = {};
  const unprocessedSites = req.body.siteIds;

  const filteredSites = authorizedSites.filter(authorizedSite =>
    unprocessedSites.includes(authorizedSite.siteId)
  );
  if (filteredSites.length < unprocessedSites.length) {
    return next(
      new restify.NotFoundError(
        'One or more specified sites has not been found.'
      )
    );
  }
  filteredSites.forEach(site => {
    siteNameLookup[site.siteId] = {
      siteName: site.siteName,
      referenceId: site.referenceId,
    };
  });

  const params = {
    ExpressionAttributeValues: {
      ':date_name_SK_start': start,
      ':date_name_SK_end': end,
    },
    KeyConditionExpression: `
                siteId = :siteId AND 
                date_name_SK BETWEEN :date_name_SK_start AND :date_name_SK_end
            `,
    ProjectionExpression: 'siteId, #n, runtime, #c, date_name_SK',
    ExpressionAttributeNames: { '#c': 'count', '#n': 'name' },
    TableName: config.AWS.DDB.sitePOPTable,
    ScanIndexForward: false,
  };

  let siteToProcess;
  do {
    siteToProcess = unprocessedSites.shift();

    params.ExpressionAttributeValues[':siteId'] = siteToProcess;
    if (mediaNames && mediaNames.length > 0) {
      params.FilterExpression = `#n IN (${mediaNames
        .map((name, index) => `:v${index}`)
        .join(',')})`;
      for (let i = 0; i < mediaNames.length; i++) {
        params.ExpressionAttributeValues[`:v${i}`] = mediaNames[i];
      }
    }

    let data = {};
    do {
      // eslint-disable-next-line no-await-in-loop
      data = await AWS.DDBDC.query(params);
      if (data.LastEvaluatedKey) {
        params.ExclusiveStartKey = data.LastEvaluatedKey;
      }
      /* eslint-disable no-param-reassign */
      data.Items.forEach(item => {
        item.siteName = siteNameLookup[item.siteId].siteName;
        item.referenceId = siteNameLookup[item.siteId].referenceId;
        const { 0: date } = item.date_name_SK.split('#');
        item.date = date;
        item.runtime = msToTime(item.runtime);
        delete item.date_name_SK;
        stringifier.write(item);
      });
      /* eslint-enable no-param-reassign */
    } while (data.LastEvaluatedKey);
  } while (unprocessedSites.length > 0);
  return stringifier.end();
}

async function processAllSitesCSV(
  req,
  res,
  next,
  pagination,
  start,
  end,
  authorizedSites,
  mediaNames,
  companyId
) {
  const stringifier = stringify(csvOptions);
  stringifier.pipe(res);

  const authorizedSitesIds = [];
  const siteNameLookup = {};
  authorizedSites.forEach(site => {
    authorizedSitesIds.push(site.siteId);
    siteNameLookup[site.siteId] = {
      siteName: site.siteName,
      referenceId: site.referenceId,
    };
  });

  let data = {};

  const params = {
    ExpressionAttributeValues: {
      ':GSI_PK': companyId,
      ':date_name_SK_start': start,
      ':date_name_SK_end': end,
    },
    KeyConditionExpression: `
                GSI_PK = :GSI_PK AND
                date_name_SK BETWEEN :date_name_SK_start AND :date_name_SK_end
            `,
    ProjectionExpression: 'siteId, #n, runtime, #c, date_name_SK',
    ExpressionAttributeNames: { '#c': 'count', '#n': 'name' },
    TableName: config.AWS.DDB.sitePOPTable,
    IndexName: 'ALL_SITES-date_GSI_SK-index',
    ScanIndexForward: false,
  };

  if (mediaNames && mediaNames.length > 0) {
    params.FilterExpression = `#n IN (${mediaNames
      .map((name, index) => `:v${index}`)
      .join(',')})`;
    for (let i = 0; i < mediaNames.length; i++) {
      params.ExpressionAttributeValues[`:v${i}`] = mediaNames[i];
    }
  }

  do {
    // eslint-disable-next-line no-await-in-loop
    data = await AWS.DDBDC.query(params);
    if (data.LastEvaluatedKey) {
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    }

    /* eslint-disable no-param-reassign */
    data.Items.forEach(item => {
      if (authorizedSitesIds.includes(item.siteId)) {
        item.siteName = siteNameLookup[item.siteId].siteName;
        item.referenceId = siteNameLookup[item.siteId].referenceId;
        const { 0: date } = item.date_name_SK.split('#');
        item.date = date;
        item.runtime = msToTime(item.runtime);
        delete item.date_name_SK;
        stringifier.write(item);
      }
    });
    /* eslint-enable no-param-reassign */
  } while (data.LastEvaluatedKey);
  return stringifier.end();
}

async function processSpecifiedSites(
  req,
  res,
  next,
  pagination,
  start,
  end,
  authorizedSites,
  mediaNames
) {
  let unprocessedSites;
  let filteredSites;

  if (req.body.siteIds) {
    unprocessedSites = req.body.siteIds;
    filteredSites = authorizedSites.filter(authorizedSite =>
      unprocessedSites.includes(authorizedSite.siteId)
    );
    if (filteredSites.length < unprocessedSites.length) {
      return next(
        new restify.NotFoundError(
          'One or more specified sites has not been found.'
        )
      );
    }
  } else {
    filteredSites = authorizedSites;
  }

  unprocessedSites = [];
  const siteNameLookup = {};
  filteredSites.forEach(site => {
    unprocessedSites.push(site.siteId);
    siteNameLookup[site.siteId] = {
      siteName: site.siteName,
      referenceId: site.referenceId,
      companyName: site.owner.name,
      siteAddress: site.formattedAddress,
      sitePhone: site.contactPhone,
    };
  });

  const queryResult = [];
  let data;

  do {
    const siteToProcess = unprocessedSites.shift();
    const params = {
      ExpressionAttributeValues: {
        ':siteId': siteToProcess,
        ':date_name_SK_start': start,
        ':date_name_SK_end': end,
      },
      KeyConditionExpression: `
                siteId = :siteId AND 
                date_name_SK BETWEEN :date_name_SK_start AND :date_name_SK_end
            `,
      ProjectionExpression: 'siteId, #n, runtime, #c, date_name_SK',
      ExpressionAttributeNames: { '#c': 'count', '#n': 'name' },
      TableName: config.AWS.DDB.sitePOPTable,
      ScanIndexForward: false,
    };

    if (pagination.pageKey) {
      params.ExclusiveStartKey = pagination.pageKey;
    }

    if (mediaNames && mediaNames.length > 0) {
      params.FilterExpression = `#n IN (${mediaNames
        .map((name, index) => `:v${index}`)
        .join(',')})`;
      for (let i = 0; i < mediaNames.length; i++) {
        params.ExpressionAttributeValues[`:v${i}`] = mediaNames[i];
      }
    }

    do {
      // eslint-disable-next-line no-await-in-loop
      data = await AWS.DDBDC.query(params);
      if (data.LastEvaluatedKey) {
        params.ExclusiveStartKey = data.LastEvaluatedKey;
      }
      // add site names to records
      /* eslint-disable no-param-reassign */
      data.Items = data.Items.map(item => {
        item.siteName = siteNameLookup[item.siteId].siteName;
        item.referenceId = siteNameLookup[item.siteId].referenceId;
        const { 0: date } = item.date_name_SK.split('#');
        item.date = date;
        item.companyName = siteNameLookup[item.siteId].companyName;
        item.siteAddress = siteNameLookup[item.siteId].siteAddress;
        item.sitePhone = siteNameLookup[item.siteId].sitePhone;
        delete item.date_name_SK;
        return item;
      });
      /* eslint-enable no-param-reassign */

      queryResult.push(...data.Items);
    } while (data.LastEvaluatedKey);
  } while (
    queryResult.length < pagination.pageMinSize &&
    unprocessedSites.length > 0
  );

  return {
    results: queryResult,
    resultsMetadata: {
      unprocessedSiteIds: unprocessedSites,
      pageKey: data.LastEvaluatedKey,
      pageMinSize: pagination.pageMinSize,
    },
  };
}

async function getParams(req) {
  const pagination = paginationHelper.parsePaginationBodyDDB(req);
  const userId = req.user.sub;
  const companyId = req.user.company.id;
  const start = req.body.start ? req.body.start : '1970-01-01';
  const { mediaNames } = req.body;
  // "end" needs to be shifted 1 day forward for BETWEEN comparison on composite key date_name_SK ( date#name )
  // e.g. to include key value  '2015-03-24#somefilename.webm' in results of a query
  // "req.body.start = '2015-03-23', req.body.start = '2015-03-24'" a DDB query need to use
  // date_name_SK BETWEEN '2015-03-23' AND '2015-03-25'
  const end = req.body.end
    ? moment(req.body.end).add(1, 'days').format('YYYY-MM-DD')
    : '2999-12-31';

  const authorizedSites = await server.db.replica.rows(
    `
        SELECT
            s.site_id,
            s.name as site_name,
            s.reference_id,
            s.formatted_address,
            s.phone_number AS "contactPhone",
                json_build_object(
                'id', c.id,
                'name', c.name
            )::jsonb AS "owner"
        FROM site s
        JOIN company c ON s.company_id=c.id
        JOIN user_site_authorization usa ON usa.site_id = s.site_id
        WHERE s.active
        AND usa.user_id = $1
        ORDER BY s.name
    `,
    [userId]
  );

  return {
    pagination,
    userId,
    companyId,
    start,
    mediaNames,
    end,
    authorizedSites,
  };
}

module.exports = {
  async getReport(req, res, next) {
    try {
      const p = await getParams(req);
      const response = await processSpecifiedSites(
        req,
        res,
        next,
        p.pagination,
        p.start,
        p.end,
        p.authorizedSites,
        p.mediaNames
      );
      res.send(response);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  async getReportCSV(req, res, next) {
    const p = await getParams(req);
    try {
      if (req.body.siteIds && req.body.siteIds.length > 0) {
        await processSpecifiedSitesCSV(
          req,
          res,
          next,
          p.pagination,
          p.start,
          p.end,
          p.authorizedSites,
          p.mediaNames
        );
      } else {
        await processAllSitesCSV(
          req,
          res,
          next,
          p.pagination,
          p.start,
          p.end,
          p.authorizedSites,
          p.mediaNames,
          p.companyId
        );
      }
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
