const { Readable } = require('stream');
const restify = require('restify');
const errorHandler = require('../../lib/errorhandler');
const {
  getSiteBySiteIdAndUserId,
  getAssetReportsBySiteId,
  generateFileName,
  generateZipFile,
} = require('./assets-report-helper');

const assetsReportHandler = {
  async getSiteAssetsReport(req, res, next) {
    const { siteId } = req.params;
    const userId = req.user.sub;
    const companyId = req.user.company.id;

    try {
      // Checking if site exists and retrieving site name for the dotted string
      const site = await getSiteBySiteIdAndUserId(siteId, userId, companyId);
      if (!site) {
        return next(new restify.NotFoundError('Site not found'));
      }
      const assets = await getAssetReportsBySiteId(siteId);
      if (!assets.length) {
        return next(new restify.NotFoundError('No assets found for the site'));
      }

      const truncatedSiteName = site.name.slice(0, 100);
      if (assets.length === 1) {
        // there is only one asset in the site
        const xmlFileName = generateFileName(
          truncatedSiteName,
          assets[0].deviceId
        );
        res.writeHead(200, {
          'Content-Type': 'text/xml',
          'Access-Control-Expose-Headers': 'Content-Disposition',
          'Content-Disposition': `attachment; ${xmlFileName}.xml`,
        });
        const stream = new Readable();
        stream.push(assets[0].value); // the string you want
        stream.push(null); // indicates end-of-file basically - the end of the stream
        stream.pipe(res);
      } else {
        // there are multiple assets for the site
        const zipFileName = generateFileName(truncatedSiteName);
        res.writeHead(200, {
          'Content-Type': 'application/zip',
          'Access-Control-Expose-Headers': 'Content-Disposition',
          'Content-Disposition': `attachment; ${zipFileName}.zip`,
        });
        const zipFileStream = await generateZipFile(
          zipFileName,
          truncatedSiteName,
          assets
        );
        zipFileStream.pipe(res);
        await zipFileStream.finalize();
      }
      return next();
    } catch (error) {
      return errorHandler.onError(req, res, next)(error);
    }
  },
};

module.exports = assetsReportHandler;
