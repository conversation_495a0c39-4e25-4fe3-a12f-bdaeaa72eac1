const co = require('co');
const QueryStream = require('pg-query-stream');
const stringify = require('csv-stringify');
const transform = require('stream-transform');
const moment = require('moment');

const alarmsHelper = require('../../helpers/alarms-helper');
const paginationHelper = require('../../helpers/pagination-helper');
const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const { enumToScreenSize } = require('../../helpers/screensize-enum.json');
const { mainLogger } = require('../../lib/logger');

const logger = mainLogger();

function prepareWhere(req) {
  let idx = 1;
  let where = '';
  const params = [];
  const { alarms, start, end, sites } = req.body;
  const { company, sub } = req.user;

  // First param also to be used inside JOIN statement
  where += `WHERE c.id = $${idx} `;
  params.push(company.id);
  idx += 1;

  const alarmParams = alarmsHelper.updateAlarmParams(alarms);
  where += `AND r.alarm_type LIKE ANY($${idx}) `;
  params.push(alarmParams);
  idx += 1;

  where += `AND r.triggered_timestamp BETWEEN $${idx} `;
  params.push(start);
  idx += 1;

  where += `AND $${idx} `;
  params.push(end);
  idx += 1;

  where += `AND usa.user_id = $${idx} `;
  params.push(sub);
  idx += 1;

  if (alarms.includes('site_critical')) {
    where += `AND (t.active = true OR s.active = true) `;
  } else {
    where += `AND t.active = true `;
  }

  if (sites) {
    where += `AND r.site_id = ANY($${idx})`;
    params.push(sites);
    idx += 1;
  }

  return { value: where, params };
}

const SELECT = `
    SELECT
        c.id                                                           AS company_id,
        c.name                                                         AS company_name,
        s.site_id                                                      AS site_id,
        s.name                                                         AS site_name,
        s.formatted_address                                            AS site_address,
        s.phone_number                                                 AS site_phone,
        s.reference_id                                                 AS site_reference_id,
        r.device_id                                                    AS device_id,
        t.name                                                         AS device_name,
        json_build_object('id', p.device_type, 'name', p.display_name , 'screenSize', t.screen_size) AS device_Type,
        t.serial_number                                                AS device_serial_number,
        r.alarm_type                                                   AS alarm_type,
        r.triggered_timestamp                                          AS alarm_triggered,
        r.remedy_timestamp                                             AS alarm_remedied,
        r.reason                                                       AS oos_category_condition 
`;

const FROM = `
    FROM report_alarm_history r
        JOIN site s ON s.site_id = r.site_id
        JOIN company c ON s.company_id = c.id
        LEFT JOIN target t ON t.target_id = r.device_id
        LEFT JOIN company_product_type p ON p.device_type = t.device_type AND p.company = $1
        JOIN user_site_authorization usa ON usa.site_id = s.site_id
`;

function processOOSStatus(data) {
  const item = { ...data };
  const type = item.alarmType || item.alarm_type;
  if (type === 'device_out_of_service' || type === 'device_unreachable') {
    item.oosItems = [];
    if (item.oosCategoryCondition) {
      try {
        const reasons = item.oosCategoryCondition.split(';');
        for (let i = 0; i < reasons.length; i++) {
          const [category, condition] = reasons[i].split('/');
          item.oosItems.push([category, condition]);
        }
      } catch (err) {
        server.log.warn(
          { err },
          'Failed to parse OOS info for the category message'
        );
      }
    }
  }
  delete item.oosCategoryCondition;
  delete item.totalCount;
  return item;
}

function prepareWhereForAlarmReportWithAccess(req) {
  let idx = 1;
  let where = '';
  const params = [];
  const { sites } = req.body;
  const { company, sub } = req.user;

  where += `where usa.user_id = $${idx} `;
  params.push(sub);
  idx += 1;

  if (sites) {
    where += `and s.site_id = ANY($${idx}) `;
    params.push(sites);
    idx += 1;
  }

  where += `and s.company_id = $${idx} `;
  params.push(company.id);
  idx += 1;

  where += `and s.active = true`;

  return { value: where, params };
}

const prepareWhereForReport = req => {
  let idx = 4;
  let where = '';
  const params = [];
  const { alarms, start, end } = req.body;

  if (alarms) {
    const alarmParams = alarmsHelper.updateAlarmParams(alarms);
    where += `WHERE r.alarm_type LIKE ANY($${idx}) `;
    params.push(alarmParams);
    idx += 1;
  }

  where += `AND r.triggered_timestamp BETWEEN $${idx} `;
  params.push(start);
  idx += 1;

  where += `AND $${idx} `;
  params.push(end);
  idx += 1;

  if (alarms.includes('site_critical')) {
    where += `AND (t.active = true OR sta.site_active = true) `;
  } else {
    where += `AND t.active = true `;
  }

  return { value: where, params };
};

function generateAccessQuery(whereValue) {
  return `
  with SiteToAccess as (
    select
	    c.id as company_id,
	    c.name as company_name,
	    s.site_id as site_id,
	    s.name as site_name,
	    s.formatted_address as site_address,
	    s.phone_number as site_phone,
	    s.reference_id as site_reference_id,
	    s.active as site_active
    from company c
    inner join site s on s.company_id = c.id
    inner join user_site_authorization usa on s.site_id = usa.site_id 
    ${whereValue}
  ),`;
}

const generateReportQuery = whereValue =>
  `
  ReportData as (
    select
      sta.company_id,
	    sta.company_name,
	    sta.site_id,
	    sta.site_name,
	    sta.site_address,
	    sta.site_phone,
	    sta.site_reference_id,
	    r.device_id as device_id,
	    t.name as device_name,
	    json_build_object('id', p.device_type,
	      'name', p.display_name,
        'screenSize', t.screen_size) as device_Type,
	    t.serial_number as device_serial_number,
	    r.alarm_type as alarm_type,
	    r.triggered_timestamp as alarm_triggered,
	    r.remedy_timestamp as alarm_remedied,
	    r.reason as oos_category_condition,
      COUNT(*) OVER() AS total_count
    from report_alarm_history r
    inner join SiteToAccess sta on r.site_id = sta.site_id
    left join target t on t.target_id = r.device_id
    left join company_product_type p on p.device_type = t.device_type and p.company = sta.company_id 
    ${whereValue}
  )`;

const SELECT_WITH_COUNT_QUERY = `
  select
    company_id,
    company_name,
    site_id,
    site_name,
    site_address,
    site_phone,
    site_reference_id,
    device_id,
    device_name,
    device_Type,
    device_serial_number,
    alarm_type,
    alarm_triggered,
    alarm_remedied,
    oos_category_condition,
    total_count
  from
  	ReportData
`;

function generateQueryAlarmReport(req, pageParams) {
  const WHERE_ACCESS = prepareWhereForAlarmReportWithAccess(req);
  const WHERE_REPORT = prepareWhereForReport(req);
  const ACCESS_QUERY = generateAccessQuery(WHERE_ACCESS.value);
  const REPORT_QUERY = generateReportQuery(WHERE_REPORT.value);
  const query = `
      ${ACCESS_QUERY}
      ${REPORT_QUERY}
      ${SELECT_WITH_COUNT_QUERY}
      order by company_name, site_name, device_serial_number, alarm_type, alarm_triggered
      LIMIT ${pageParams.pageSize}
      OFFSET ${pageParams.offset};
  `;
  return { query, params: [...WHERE_ACCESS.params, ...WHERE_REPORT.params] };
}

module.exports = {
  getReport: async (req, res, next) => {
    try {
      let totalCount = 0;
      const pageParams = paginationHelper.parsePaginationBody(req);
      const reportQuery = generateQueryAlarmReport(req, pageParams);
      const getReportData = await server.db.replica.trows(
        reportQuery.query,
        reportQuery.params
      );
      const results = await getReportData.map(result => {
        if (totalCount <= 0) {
          totalCount = result.totalCount;
        }
        const processedResult = processOOSStatus(result);
        processedResult.deviceType.screenSize =
          enumToScreenSize[processedResult.deviceType.screenSize] || null;
        return processedResult;
      });

      res.send(200, {
        resultsMetadata: {
          totalResults: totalCount,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results,
      });
      return next();
    } catch (error) {
      logger.error(
        { error },
        '[alarm-history-report][getReport] Error in generating report!'
      );
      return errorHandler.onError(req, res, next)(error);
    }
  },

  getReportCSV: (req, res, next) => {
    const options = {
      delimiter: ',',
      endLine: '\n',
      columns: [
        'company_id',
        'company_name',
        'site_id',
        'site_name',
        'site_address',
        'site_phone',
        'site_reference_id',
        'device_id',
        'device_name',
        'device_type',
        'device_type_name',
        'device_serial_number',
        'alarm_type',
        'alarm_triggered',
        'alarm_remedied',
      ],
      escapeChar: '"',
      enclosedChar: '"',
      header: true,
    };

    if (req.body.type === 'device-health-status-history-report') {
      options.columns.push('oos_category_condition');
    }

    const WHERE = prepareWhere(req);

    return co(function* execute() {
      const connection = yield server.db.replica.getConnection();
      const query = new QueryStream(
        `
                ${SELECT}
                ${FROM}
                ${WHERE.value}
                ORDER BY c.name, s.name, t.serial_number, r.alarm_type, alarm_triggered;
            `,
        WHERE.params
      );

      res.writeHead(200, {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename=report.csv',
      });

      const stream = connection.client.query(query);
      stream.on('end', connection.done);
      const stringifier = stringify(options);
      stream
        .pipe(
          transform(data => {
            const row = data;
            if (row.alarm_triggered instanceof Date) {
              row.alarm_triggered = moment(row.alarm_triggered).utc().format();
            }
            if (row.alarm_remedied instanceof Date) {
              row.alarm_remedied = moment
                .utc(row.alarm_remedied)
                .utc()
                .format();
            }
            if (row.device_type) {
              const screenSize =
                enumToScreenSize[row.device_type.screenSize] || null;
              row.device_type_name = row.device_type.name;
              row.device_type =
                screenSize !== null
                  ? `${row.device_type.id} (${screenSize})`
                  : row.device_type.id;
            }

            return row;
          })
        )
        .pipe(stringifier)
        .pipe(res);

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
};
