const _ = require('lodash');
const moment = require('moment');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');
const { toStream } = require('../../lib/to-stream');
const { enumToScreenSize } = require('../../helpers/screensize-enum.json');

const DEPLOY_POLICY = {
  0: 'Sequential per site',
  1: 'All at once',
  2: 'Consecutive',
};

const JOB_STATUS = {
  0: 'New',
  1: 'Accepted',
  2: 'In progress',
  3: 'Completed',
  4: 'Failed',
  5: 'Cancelled',
};

function prepareWhere(req) {
  const params = [];
  let idx = 1;
  let where = `WHERE usa.user_id = $${idx}`;
  params.push(req.user.sub);

  idx += 1;

  if (req.body.softwarePackages && req.body.softwarePackages.length > 0) {
    const placeholders = req.body.softwarePackages
      .map((sp, i) => `$${idx + i}`)
      .join(', ');
    where += ` AND s.software_file IN (${placeholders})`;

    params.push(...req.body.softwarePackages);

    idx += req.body.softwarePackages.length;
  }

  where = `${where} AND t.active = true`;

  return { value: where, params };
}

function timeToEndTime(endTime) {
  return moment(endTime).diff(new Date(), 'seconds');
}

function updateStatus(update) {
  if (update.cancelled) {
    return 'Canceled';
  }
  if (update.totalDeploymentsCount === update.deploymentsCompleteCount) {
    // all deployments completed
    return 'Completed';
  }
  if (update.deploymentsFailCount) {
    // at least 1 deployment failed
    return 'Failed';
  }
  if (update.deploymentsInProgressCount) {
    // at least 1 deployment in progress
    return 'In Progress';
  }
  if (update.deploymentsCompleteCount) {
    // gets here only when deploymentsCompleteCount < totalDeploymentsCount (some have completed, some haven't)
    return 'In Progress';
  }
  if (timeToEndTime(update.installWindowEnd || update.install_window_end) > 0) {
    return 'Scheduled';
  }
  if (
    timeToEndTime(update.installWindowEnd || update.install_window_end) <= 0
  ) {
    return 'Expired';
  }
  return '';
}

// Download_failed,_error_code:_67590;_JobId:_2,_pkg:_16f975ae-b14d-4b06-abe2-3c6cf7029ca8-6e30814c-11.99.9999-S.pkg
function parseFailureMessage(message) {
  const regJob = /(_JobId:[^,]+)/gi;
  const regPackage = /(_pkg:[^,]+)/gi;

  const jobInfo = regJob.exec(message);
  const packageInfo = regPackage.exec(message);

  let res = null;

  if (jobInfo) {
    try {
      res = {};
      res.job = jobInfo[0].split(':')[1].slice(1);
    } catch (err) {
      res = null;
      server.log.warn(
        { err },
        'Failed to parse job info for the failure message'
      );
    }
  }
  if (packageInfo) {
    try {
      res = res || {};
      res.pkg = packageInfo[0].split(':')[1].slice(1);
    } catch (err) {
      res = null;
      server.log.warn(
        { err },
        'Failed to parse pkg info for the failure message'
      );
    }
  }
  return res;
}

/* eslint-disable no-param-reassign */
function calculateReleaseStatus(targetData, fullData) {
  try {
    let releaseData = null;
    const releases = {};
    if (targetData.length !== fullData.length) {
      releaseData = [...new Set(targetData.map(t => t.releaseId))];
    }
    for (let i = 0; i < fullData.length; i++) {
      const item = fullData[i];
      if (
        (releaseData && releaseData.includes(item.releaseId)) ||
        releaseData === null
      ) {
        if (item.releaseId in releases) {
          releases[item.requestId][item.installStatus] += 1;
        } else {
          releases[item.requestId] = {};
          releases[item.requestId][item.installStatus] = 1;
        }
      }
    }
    for (let i = 0; i < targetData.length; i++) {
      targetData[i].summary = releases[targetData[i].requestId];
    }
    return targetData;
  } catch (e) {
    server.log.debug(
      {
        targetData,
        fullData,
      },
      `calculateReleaseStatus error`
    );
    throw e;
  }
}

function processItem(result, isCSV = false) {
  try {
    result.deploymentsCancelCount =
      result.summary[server.constants.job.STATUS.CANCELLED] || 0;
    result.deploymentsCompleteCount =
      result.summary[server.constants.job.STATUS.COMPLETED] || 0;
    result.deploymentsFailCount =
      result.summary[server.constants.job.STATUS.FAILED] || 0;
    result.deploymentsInProgressCount =
      result.summary[server.constants.job.STATUS.IN_PROGRESS] || 0;

    result.totalDeploymentsCount =
      (result.summary[server.constants.job.STATUS.NEW] || 0) +
      (result.summary[server.constants.job.STATUS.ACCEPTED] || 0) +
      (result.summary[server.constants.job.STATUS.IN_PROGRESS] || 0) +
      (result.summary[server.constants.job.STATUS.COMPLETED] || 0) +
      (result.summary[server.constants.job.STATUS.CANCELLED] || 0) +
      (result.summary[server.constants.job.STATUS.FAILED] || 0);

    result.screenSize = enumToScreenSize[result.screenSize] || null;
    result.requestStatus = updateStatus(result);
    if (isCSV) {
      result.installWindowStart = moment(result.installWindowStart)
        .utc()
        .format();
      result.installWindowEnd = moment(result.installWindowEnd).utc().format();
      result.downloadStart = result.downloadStart
        ? moment(result.downloadStart).utc().format()
        : '';
      result.downloadComplete = result.downloadComplete
        ? moment(result.downloadComplete).utc().format()
        : '';
      result.installStart = result.installStart
        ? moment(result.installStart).utc().format()
        : '';
      result.installComplete = result.installComplete
        ? moment(result.installComplete).utc().format()
        : '';

      result.deviceType = result.screenSize
        ? `${result.deviceType} (${result.screenSize})`
        : result.deviceType;
    }
    const hour = moment.duration(result.installWindow).asHours();
    result.installWindow = `${hour} ${hour > 1 ? 'hours' : 'hour'}`;

    const failureInfo = parseFailureMessage(result.failureMessage);
    if (failureInfo) {
      result.failureJob = failureInfo.job;
      result.softwarePackage = failureInfo.pkg;
    }
    result.installType = DEPLOY_POLICY[result.installType];
    result.downloadStatus = JOB_STATUS[result.downloadStatus];
    result.installStatus = JOB_STATUS[result.installStatus];

    delete result.summary;
    delete result.deploymentsCancelCount;
    delete result.deploymentsCompleteCount;
    delete result.deploymentsFailCount;
    delete result.deploymentsInProgressCount;
    delete result.totalDeploymentsCount;

    return result;
  } catch (e) {
    server.log.debug(
      {
        result,
        isCSV,
      },
      `processItem error`
    );
    throw e;
  }
}
/* eslint-enable */

const SELECT = `
SELECT tr.target_id                         as device_id,
       t.name                               as device_name,
       t.serial_number                      as SN,
       t.device_type,
       t.screen_size,
       site.name                            as site_name,
       site.formatted_address               as address,
       site.reference_id                    as site_id,
       r.release_id                         as request_id,
       r.name                               as request_name,
       r.schedule_start_time                as install_window_start,
       r.schedule_end_time                  as install_window_end,
       r.install_window,
       r.cancelled,
       iu.full_name                         as created_by,
       r.deploy_policy_id                   as install_type,
       s.name                               as software_name,
       s.software_file                      as file_name,
       (CASE
           WHEN (dj.status = 5 AND ij.status != ALL ('{3,4}')) OR ij.status = ANY ('{5}') THEN 'Cancelled'
           WHEN (dj.status = 4 AND ij.status != 5) OR ij.status = 4 THEN 'Failed'
           WHEN ((dj.status = 0 OR dj.status = 2) AND ij.status = 0) THEN 'PendingDownload'
           WHEN (dj.status = 3 ) AND (ij.status = ANY ('{0,2}')) THEN 'PendingInstall'
           WHEN ij.status = 3 THEN 'Completed'
       END)                                 as e2e_status,
       dj.status                            as download_status,
       dj.download_start,
       dj.download_complete,
       ij.status                            as install_status,
       ij.install_start,
       ij.install_complete,
       coalesce(dj.download_job_failed_message, ij.install_job_failed_message) as failure_message,
       coalesce(
            (SELECT value FROM device_states ds WHERE device_id = t.target_id and state  = 'invenco.contentplayer.media-player.terminal-id' limit 1),
            (SELECT value FROM device_states ds WHERE device_id = t.target_id and state  = 'invenco.system.cfg.net-terminal-id' limit 1)
        ) as terminal_id
`;

const FROM_FULL = `
    FROM target_release tr
             JOIN target_release_status trs ON tr.current_status_id = trs.target_release_status_id
             JOIN target t ON tr.target_id = t.target_id
             JOIN site ON t.site_id = site.site_id
             JOIN user_site_authorization usa ON usa.site_id = site.site_id
             JOIN release r ON r.release_id = tr.release_id
             JOIN ics_user iu ON r.created_by = iu.id            
             JOIN software s ON s.software_id = tr.software_id
             INNER JOIN LATERAL (
                SELECT
                    dj.status,
                    (   SELECT jh.created_at
                        FROM job_status_history jh
                        WHERE dj.id = jh.job_id AND jh.status = 2
                        ORDER BY jh.created_at DESC
                        limit 1
                    ) download_start,
                    (   SELECT jh.created_at
                        FROM job_status_history jh
                        WHERE dj.id = jh.job_id AND jh.status = 3 AND dj.status = 3
                        ORDER BY jh.created_at DESC
                        limit 1
                    ) download_complete,
                    (   SELECT jh.message
                        FROM job_status_history jh
                        WHERE dj.id = jh.job_id AND jh.status = 4 AND dj.status = 4
                        ORDER BY jh.created_at DESC
                        limit 1
                    ) download_job_failed_message
                FROM job dj
                where dj.id = tr.download_job
            ) dj on true
            INNER JOIN LATERAL (
                SELECT
                    ij.status,
                    (   SELECT jh.created_at
                        FROM job_status_history jh
                        WHERE ij.id = jh.job_id AND jh.status = 2
                        ORDER BY jh.created_at DESC
                        limit 1
                    ) install_start,
                    (   SELECT jh.created_at
                        FROM job_status_history jh
                        WHERE ij.id = jh.job_id AND jh.status = 3 AND ij.status = 3
                        ORDER BY jh.created_at DESC
                        limit 1
                    ) install_complete,
                    (   SELECT jh.message
                        FROM job_status_history jh
                        WHERE ij.id = jh.job_id AND jh.status = 4 AND ij.status = 4
                        ORDER BY jh.created_at DESC
                        limit 1
                    ) install_job_failed_message
                FROM job ij               
                where ij.id = tr.install_job
            ) ij on true
    `;

module.exports = {
  getReport: async (req, res, next) => {
    const pagingParams = paginationHelper.parsePaginationBody(req);
    const WHERE = prepareWhere(req);

    try {
      const fullData = await server.db.replica.rows(
        `
                    ${SELECT}
                    ${FROM_FULL}
                    ${WHERE.value}
                    ORDER BY r.release_id, r.date_time_created DESC, site.name
                `,
        WHERE.params
      );
      let resultData = fullData.slice(
        pagingParams.offset,
        pagingParams.offset + pagingParams.pageSize
      );
      resultData = calculateReleaseStatus(resultData, fullData);
      const results = _.map(resultData, result => processItem(result));

      res.send(200, {
        resultsMetadata: {
          totalResults: fullData.length,
          pageIndex: pagingParams.pageIndex,
          pageSize: pagingParams.pageSize,
        },
        results,
      });

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  getReportCSV: async (req, res, next) => {
    const WHERE = prepareWhere(req);

    const fields = [
      {
        label: 'Request Name',
        value: 'requestName',
      },
      {
        label: 'Request Status',
        value: 'requestStatus',
      },
      {
        label: 'Site Name',
        value: 'siteName',
      },
      {
        label: 'Site Location',
        value: 'address',
      },
      {
        label: 'Site Reference ID',
        value: 'siteId',
      },
      {
        label: 'Device Name',
        value: 'deviceName',
      },
      {
        label: 'Device Serial',
        value: 'sn',
      },
      {
        label: 'Device Type',
        value: 'deviceType',
      },
      {
        label: 'Terminal ID',
        value: 'terminalId',
      },
      {
        label: 'Device ID',
        value: 'deviceId',
      },
      {
        label: 'Created By',
        value: 'createdBy',
      },
      {
        label: 'Install Type',
        value: 'installType',
      },
      {
        label: 'Asset Name',
        value: 'softwareName',
      },
      {
        label: 'File Name',
        value: 'fileName',
      },
      {
        label: 'Install Window Start (UTC)',
        value: 'installWindowStart',
      },
      {
        label: 'Install Window Duration',
        value: 'installWindow',
      },
      {
        label: 'Download Status',
        value: 'downloadStatus',
      },
      {
        label: 'Download Start (UTC)',
        value: 'downloadStart',
      },
      {
        label: 'Download Complete (UTC)',
        value: 'downloadComplete',
      },
      {
        label: 'Install Status',
        value: 'installStatus',
      },
      {
        label: 'Install Start (UTC)',
        value: 'installStart',
      },
      {
        label: 'Install Complete (UTC)',
        value: 'installComplete',
      },
      {
        label: 'E2E Status',
        value: 'e2eStatus',
      },
      {
        label: 'Failure Reason',
        value: 'failureMessage',
      },
      {
        label: 'Failed Job ID',
        value: 'failureJob',
      },
      {
        label: 'Failed Package Name',
        value: 'softwarePackage',
      },
    ];

    try {
      const fullData = await server.db.replica.rows(
        `
                    ${SELECT}
                    ${FROM_FULL}
                    ${WHERE.value}
                    ORDER BY r.release_id, r.date_time_created DESC, site.name
                `,
        WHERE.params
      );
      const resultData = calculateReleaseStatus(fullData, fullData);
      const results = _.map(resultData, result => processItem(result, true));
      res.writeHead(200, {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename=report.csv',
      });
      let header = '';
      for (let i = 0; i < fields.length; i++) {
        header += `${fields[i].label}${i === fields.length - 1 ? '' : ','}`;
      }
      toStream(`${header} \n`).pipe(res);

      for (let i = 0; i < results.length; i++) {
        let str = '';
        const item = results[i];
        for (let j = 0; j < fields.length; j++) {
          let field = item[fields[j].value]
            ? item[fields[j].value].toString()
            : '';
          if (field.indexOf(',') > -1) {
            field = `"${field}"`;
          }
          str = `${str + field}${j === fields.length - 1 ? '' : ','}`;
        }
        toStream(`${str} \n`).pipe(res);
      }

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
