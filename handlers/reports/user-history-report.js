const _ = require('lodash');
const moment = require('moment');
const stringify = require('csv-stringify');
const transform = require('stream-transform');
const QueryStream = require('pg-query-stream');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');

function prepareWhere(req) {
  const params = [];
  let idx = 1;
  let where = 'WHERE 1=1';

  params.push(req.user.company.id);
  idx += 1;

  if (req.body.user) {
    where = `${where} AND (
            LOWER(u.full_name) LIKE $${idx} OR
            LOWER(u.email) LIKE $${idx}
        )`;

    const user = req.body.user || '';
    params.push(`%${user.toLowerCase()}%`);
    idx += 1;
  }

  if (req.body.statuses) {
    where = `${where} AND status = ANY($${idx})`;
    params.push(req.body.statuses);
    idx += 1;
  }

  if (req.body.roles) {
    where = `${where} AND u.roles && $${idx}`;
    params.push(req.body.roles);
    idx += 1;
  }

  if (req.body.permissions) {
    where = `${where} AND u.roles && $${idx}`;
    params.push(req.body.permissions);
    idx += 1;
  }

  return { value: where, params };
}

const WITH = `
    --- Get user's with roles
    WITH user_with_roles AS (
        SELECT u.id, u.full_name, u.email, u.created, u.company_id, u.status, u.type,
            array (
                SELECT r.role_name as name
                FROM role as r
                JOIN user_role ur on ur.user_id = u.id AND ur.role_id = r.role_id
            ) as roles
        FROM ics_user u
        WHERE u.company_id = $1
            AND u.type = 'USER'
    ),
    
    --- Get user's last successful authentication
    user_last_logged_in AS (
        SELECT
            DISTINCT ON (ah.user_id) user_id,
            ah.timestamp
        FROM user_with_roles ur
            INNER JOIN auth_history ah ON ah.user_id = ur.id
        WHERE ah.type = 'email-password-mfa' AND ah.success = true
        ORDER BY ah.user_id, ah.timestamp DESC
    )

    
`;

const SELECT = `
    SELECT
        u.id,
        u.email,
        u.full_name,
        u.status,
        ul.timestamp as last_logged_in,
        u.roles
`;

const FROM = `
    FROM user_with_roles u
    LEFT JOIN user_last_logged_in ul ON ul.user_id = u.id
`;

function prepareCSVResults(data) {
  const roleName = {
    SUPER_ADMIN: 'Super Admin',
    COMPANY_ADMIN: 'Company Admin',
    POWER_USER: 'Power User',
    ANALYST: 'Analyst',
    SPECIALIST: 'Specialist',
    USER: 'User',
    RKI: 'RKI',
    TAMPER_CLEAR: 'Tamper Clear',
    MEDIA_DESIGNER: 'Design Media',
    MEDIA_APPROVER: 'Approve Media',
    MEDIA_DEPLOYER: 'Deploy Media',
    FACTORY_RESET: 'Factory Reset',
  };

  const roles = [
    'COMPANY_ADMIN',
    'POWER_USER',
    'USER',
    'SPECIALIST',
    'ANALYST',
  ];
  const statusName = {
    0: 'Pending',
    1: 'Active',
    2: 'Inactive',
  };
  const result = { ...data };

  result.status = statusName[data.status];

  if (result.last_logged_in instanceof Date) {
    result.last_logged_in = moment(data.last_logged_in).utc().format();
  }

  if (_.isArray(data.roles)) {
    result.role = data.roles
      .filter(role => roles.indexOf(role) >= 0)
      .map(role => roleName[role])
      .filter(Boolean)
      .join(', ');
    result.permissions = data.roles
      .filter(role => roles.indexOf(role) < 0)
      .map(role => roleName[role])
      .filter(Boolean)
      .join(', ');
  }

  delete result.id;
  return result;
}

module.exports = {
  async getReports(req, res, next) {
    const pagingParams = paginationHelper.parsePaginationBody(req);
    const WHERE = prepareWhere(req);

    try {
      const totalObj = await server.db.replica.trow(
        `
                ${WITH}
                SELECT COUNT(1)
                ${FROM}
                ${WHERE.value};
            `,
        WHERE.params
      );

      const results = await server.db.replica.trows(
        `
                ${WITH}
                ${SELECT}
                ${FROM}
                ${WHERE.value}
                ORDER BY u.full_name
                LIMIT ${pagingParams.pageSize}
                OFFSET ${pagingParams.offset};
            `,
        WHERE.params
      );

      res.send(200, {
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pagingParams.pageIndex,
          pageSize: pagingParams.pageSize,
        },
        results,
      });

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  async getReportCSV(req, res, next) {
    const options = {
      delimiter: ',',
      endLine: '\n',
      columns: [
        'full_name',
        'email',
        'status',
        'last_logged_in',
        'role',
        'permissions',
      ],
      escapeChar: '"',
      enclosedChar: '"',
      header: true,
    };

    try {
      const WHERE = prepareWhere(req);
      const connection = await server.db.replica.getConnection();
      const query = new QueryStream(
        `
                ${WITH}
                ${SELECT}
                ${FROM}
                ${WHERE.value}
                ORDER BY u.full_name;
            `,
        WHERE.params
      );

      res.writeHead(200, {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename=report.csv',
      });

      const stream = connection.client.query(query);
      stream.on('end', connection.done);

      const stringifier = stringify(options);
      stream
        .pipe(transform(data => prepareCSVResults(data)))
        .pipe(stringifier)
        .pipe(res);

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
