const _ = require('lodash');
const co = require('co');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');

function getReportLookup(req, res, next, type) {
  return co(function* execute() {
    const data = yield server.db.replica.rows(
      `SELECT ( CASE WHEN ds is not null THEN (ds || '|' || dn ) ELSE dn END ) as id,
                                                         name, description, tag FROM report_data_lookup where type = $1`,
      [type]
    );
    res.send(200, data);
    return next();
  }).catch(errorHandler.onError(req, res, next));
}

module.exports = {
  /**
   * List of available reports
   */
  getReports: (req, res, next) =>
    co(function* execute() {
      const userRoles = req.user.getRoles();
      const userCompanyFeatureFlags = _.get(
        req.user,
        'company.featureFlags',
        []
      );

      let reports = yield server.db.replica.rows(`
                SELECT id, module, name, base_url, active, description, require_roles, require_feature_flags
                FROM report;
            `);

      reports = reports.filter(report => {
        const hasAllowedRole =
          _.intersection(report.requireRoles, userRoles).length > 0;

        const hasAllowedFeatureFlags =
          report.requireFeatureFlags.length === 1
            ? true
            : _.intersection(
                _.filter(report.requireFeatureFlags, f => f !== 'REPORTING'),
                _.filter(userCompanyFeatureFlags, f => f !== 'REPORTING')
              ).length > 0;

        return hasAllowedRole && hasAllowedFeatureFlags;
      });

      res.send(200, reports);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getReportAlarms: (req, res, next) => getReportLookup(req, res, next, 'ALARM'),

  getReportStatuses: (req, res, next) =>
    getReportLookup(req, res, next, 'STATUS'),

  getReportMetrics: (req, res, next) =>
    getReportLookup(req, res, next, 'METRIC'),

  getReportVersions: (req, res, next) =>
    getReportLookup(req, res, next, 'VERSION'),

  getReportEvents: (req, res, next) => getReportLookup(req, res, next, 'EVENT'),

  getSoftware: async (req, res, next) => {
    try {
      const companyId = req.user.company.id;
      const query = req.query.q || '';
      const q = `%${query}%`.toLowerCase();
      const limit = req.query.limit || 100;

      const software = await server.db.replica.rows(
        `SELECT package
      FROM (
          SELECT DISTINCT s.software_file AS package
          FROM software AS s
          WHERE s.uploaded_by IS NOT NULL
          AND LOWER(software_file) LIKE $1
          AND s.type = 'software'
          AND s.active = true
          AND s.company_id = $2
      ) AS distinct_packages
      ORDER BY
        CASE
          WHEN LEFT(package, 1) ~ '^[^a-zA-Z0-9]' THEN 1  
          WHEN LEFT(package, 1) ~ '^[0-9]' THEN 2          
          WHEN LEFT(package, 1) ~ '^[A-Z]' THEN 3          
          WHEN LEFT(package, 1) ~ '^[a-z]' THEN 4          
        END,
        package  
      LIMIT ${limit};`,
        [q, companyId]
      );

      res.send(200, software);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
