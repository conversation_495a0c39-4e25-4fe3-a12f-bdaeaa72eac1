const _ = require('lodash');
const co = require('co');
const moment = require('moment');
const stringify = require('csv-stringify');
const transform = require('stream-transform');
const QueryStream = require('pg-query-stream');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');
const { enumToScreenSize } = require('../../helpers/screensize-enum.json');

function getSpecificWhereClause(tags) {
  const sequioaCase =
    "t.release_version >= 'R2.5' or t.release_version like '[R3%]'";
  let specificWhere = '';
  for (let i = 0; i < tags.length; i++) {
    const tag = tags[i];
    if (tag === 'system') {
      if (specificWhere.length) {
        specificWhere += ' OR ';
      }
      specificWhere += `(rdl.tag = 'system' AND CASE
                    WHEN (${sequioaCase}) THEN dv.state LIKE '%.opt-system-ver'
                    ELSE dv.state LIKE '%.opt-platform-ver'
                END)
            `;
    } else if (tag === 'platform') {
      if (specificWhere.length) {
        specificWhere += ' OR ';
      }
      specificWhere += `(rdl.tag = 'platform' AND CASE
                    WHEN (${sequioaCase}) THEN dv.state LIKE '%.opt-platform-ver'
                    ELSE dv.state LIKE '%.opt-platformbuild-ver'
                END)
            `;
    } else if (tag === 'pci') {
      if (specificWhere.length) {
        specificWhere += ' OR ';
      }
      specificWhere += `(rdl.tag = 'pci' AND CASE
                    WHEN (${sequioaCase}) THEN dv.state LIKE '%.opt-firmware-ver'
                    ELSE dv.state LIKE '%.opt-pci-ver'
                END)
            `;
    }
  }
  return specificWhere.length
    ? ` AND ( ${specificWhere} OR ( rdl.tag IS NULL OR rdl.tag NOT IN ('system', 'platform', 'pci') ) )`
    : '';
}

const prepareWhere = req => {
  const params = [];
  let idx = 1;

  // First param is used inside JOIN statement
  params.push(req.user.company.id);
  idx += 1;

  let whereClause = ` WHERE dv.state = ANY ($${idx})`;

  params.push(req.body.statuses.map(status => status.replace('|', '.')));
  idx += 1;

  whereClause += ` AND usa.user_id=$${idx}`;
  params.push(req.user.sub);
  idx += 1;

  if (req.body.sites && req.body.sites.length) {
    whereClause += ` AND s.site_id = ANY($${idx})`;
    params.push(req.body.sites);
  }

  const tags = req.body.tags || [];
  return {
    value: whereClause,
    params,
    special: getSpecificWhereClause(tags || []),
  };
};

const SELECT = `
    SELECT
        c.id                                                               as company_id,
        c.name                                                             as company_name,
        s.site_id                                                          as site_id,
        s.name                                                             as site_name,
        s.formatted_address                                                as site_address,
        s.phone_number                                                     as site_phone,
        s.reference_id                                                     as site_reference_id,
        dv.device_id                                                       as device_id,
        t.name                                                             as device_name,
        json_build_object( 'id', cp.device_type, 'name', cp.display_name, 'screenSize', t.screen_size) as device_type,
        t.serial_number                                                    as device_serial_number,
        rdl.name                                                           as status_type,
        dv.timestamp                                                       as status_timestamp,
        dv.state                                                           as status_name,
        dv.value                                                           as status_value
    `;

const FROM = `
    FROM device_versions dv 
        JOIN user_site_authorization usa on usa.site_id = dv.site_id
        JOIN site s on s.site_id = dv.site_id
        JOIN company c on s.company_id = c.id
        JOIN target t on t.target_id = dv.device_id
        LEFT JOIN company_product_type cp on cp.device_type = t.device_type and cp.company = $1
        LEFT JOIN report_data_lookup rdl on concat(rdl.ds, '.', rdl.dn) = dv.state
    `;

const ORDER =
  'ORDER BY c.name, s.name, t.serial_number, status_type, dv.timestamp DESC';

/**
 * construct statusType if name from report_data_lookup is null
 */
function transformCSVResult(result) {
  const data = result;

  if (data.device_type) {
    const screenSize = enumToScreenSize[data.device_type.screenSize] || null;
    data.device_type_name = data.device_type.name;
    data.device_type =
      screenSize !== null
        ? `${data.device_type.id} (${screenSize})`
        : data.device_type.id;
  }

  if (data.status_timestamp instanceof Date) {
    data.status_timestamp = moment(data.status_timestamp).utc().format();
  }

  delete data.status_namespace;
  delete data.status_name;

  return data;
}

function transformJSONResult(result) {
  const data = result;
  if (!data.statusType) {
    if (!data.statusNamespace) {
      data.statusType = data.statusName;
    } else if (data.statusNamespace === 'invenco.system.g6opt') {
      data.statusType = `G6OPT ${data.statusName}`;
    } else if (data.statusNamespace === 'invenco.system.g7opt') {
      data.statusType = `G7OPT ${data.statusName}`;
    } else {
      data.statusType = `${data.statusNamespace}|${data.statusName}`;
    }
  }

  if (data.statusTimestamp instanceof Date) {
    data.statusTimestamp = moment(data.statusTimestamp).utc().format();
  }

  if (data.deviceType) {
    data.deviceType.screenSize =
      enumToScreenSize[data.deviceType.screenSize] || null;
  }

  delete data.statusNamespace;
  delete data.statusName;

  return data;
}

module.exports = {
  /**
   * Get Report Data for Status History
   */
  getVersionReport: (req, res, next) => {
    const pageParams = paginationHelper.parsePaginationBody(req);

    return co(function* execute() {
      const WHERE = yield prepareWhere(req);
      const totalObj = yield server.db.replica.trow(
        `
                SELECT count(1)
                ${FROM}
                ${WHERE.value}
                ${WHERE.special}
            `,
        WHERE.params
      );

      const qResults = yield server.db.replica.trows(
        `
                ${SELECT}
                ${FROM}
                ${WHERE.value}
                ${WHERE.special}
                ${ORDER}
                LIMIT ${pageParams.pageSize}
                OFFSET ${pageParams.offset};
            `,
        WHERE.params
      );

      const results = _.map(qResults, data => transformJSONResult(data));

      res.send(200, {
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results,
      });
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  /**
   * Get Report Data for Status History CSV
   */
  getVersionReportCsv: (req, res, next) => {
    const options = {
      delimiter: ',',
      endLine: '\n',
      columns: [
        'company_id',
        'company_name',
        'site_id',
        'site_name',
        'site_address',
        'site_phone',
        'site_reference_id',
        'device_id',
        'device_name',
        'device_type',
        'device_type_name',
        'device_serial_number',
        'status_type',
        'status_timestamp',
        'status_value',
      ],
      escapeChar: '"',
      enclosedChar: '"',
      header: true,
    };

    return co(function* execute() {
      const WHERE = yield prepareWhere(req);
      const connection = yield server.db.replica.getConnection();
      const query = new QueryStream(
        `
                ${SELECT}
                ${FROM}
                ${WHERE.value}
                ${WHERE.special}
                ${ORDER};
            `,
        WHERE.params
      );

      res.writeHead(200, {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename=status-report.csv',
      });

      const stream = connection.client.query(query);
      stream.on('end', connection.done);
      const stringifier = stringify(options);
      stream
        .pipe(transform(data => transformCSVResult(data)))
        .pipe(stringifier)
        .pipe(res);

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
};
