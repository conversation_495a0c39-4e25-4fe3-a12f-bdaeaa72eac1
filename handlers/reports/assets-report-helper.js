const fs = require('fs-extra');
const archiver = require('archiver');
const moment = require('moment');
const { server } = require('../../app');
const { siteAssetsDottedStrings } = require('../../lib/app-constants');

const assetsReportHelpers = {
  async getSiteBySiteIdAndUserId(siteId, userId, companyId) {
    return server.db.replica.row(
      `
            SELECT
                s.site_id,
                s.name
            FROM site s
                JOIN user_site_authorization usa ON s.site_id = usa.site_id
            WHERE 
                s.site_id = $1 AND 
                s.company_id = $3 AND
                s.active = TRUE AND
                usa.user_id = $2;
            `,
      [siteId, userId, companyId]
    );
  },
  async getAssetReportsBySiteId(siteId) {
    // eslint-disable-next-line no-return-await
    return await server.db.replica.rows(
      `
            SELECT
                *
            FROM
                device_states
            WHERE
                site_id = $1 AND
                state = ANY ( $2 );
            `,
      [siteId, siteAssetsDottedStrings]
    );
  },
  generateFileName(siteName, deviceId = undefined) {
    const spacesRegex = / +/g;
    const noSpacesSiteName = siteName.replace(spacesRegex, '_');
    const fileNameDate = moment().format('YYMMDD');
    const newSiteName = deviceId
      ? `${noSpacesSiteName}-${deviceId.toString().replace(spacesRegex, '_')}`
      : noSpacesSiteName;
    return deviceId
      ? `Site-Assets-${newSiteName}-${fileNameDate}`
      : `Site-Assets-${newSiteName}-${fileNameDate}`;
  },
  async generateZipFile(zipFileName, siteName, assets) {
    const tmpPkgDirPrefix = '/tmp/assetsReport-';
    const tmpDirRoot = await fs.mkdtemp(tmpPkgDirPrefix);
    // Generate a zip file from that folder and return it
    const zipFileRoute = `${tmpDirRoot}/${zipFileName}.zip`;
    const archive = archiver('zip', {
      store: true, // Sets the compression method to STORE
      zlib: {
        level: 9, // Sets the compression level.
      },
    });
    // eslint-disable-next-line no-restricted-syntax
    for (const asset of assets) {
      const xmlFileName = assetsReportHelpers.generateFileName(
        siteName,
        asset.deviceId
      );
      archive.append(asset.value, { name: `${xmlFileName}.xml` });
    }
    archive.on('warning', error => {
      if (error.code === 'ENOENT') {
        server.log.warn(error);
      } else {
        server.log.error(
          { path: zipFileRoute, error },
          `Error creating zip file: ${zipFileRoute}.`
        );
        throw error;
      }
    });
    archive.on('error', err => {
      throw err;
    });
    archive.on('close', async () => {
      await fs.remove(tmpDirRoot);
      return Promise.resolve();
    });
    server.log.info(
      { path: zipFileRoute },
      `Zip file created: ${zipFileRoute}.`
    );
    return archive;
  },
};

module.exports = assetsReportHelpers;
