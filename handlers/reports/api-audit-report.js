const co = require('co');
const QueryStream = require('pg-query-stream');
const stringify = require('csv-stringify');
const errors = require('restify-errors');
const transform = require('stream-transform');
const moment = require('moment');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');

function prepareWhere(req) {
  let idx = 0;
  let where;
  const params = [];

  // only include results from caller's company
  where = `WHERE ( r.company_id = $${(idx += 1)} OR r.owner = $${idx} ) `;
  params.push(req.user.company.id);

  // date from/to filter
  where = `
        ${where}
        AND r.timestamp >= $${(idx += 1)}
        AND r.timestamp <= $${(idx += 1)}
    `;
  params.push(req.body.start, req.body.end);

  // user filter
  if (req.body.users) {
    where = `
            ${where} AND (
            r.user_id = ANY ($${(idx += 1)})
            ${req.body.users.includes(null) ? 'OR r.user_id IS NULL)' : ')'}
        `;
    params.push(req.body.users);
  }

  // route filter
  if (req.body.base_routes) {
    const baseRouteWildcards = req.body.base_routes.map(
      baseRoute => `${baseRoute.toLowerCase()}%`
    );
    where = `${where} AND LOWER(r.route) LIKE ANY ($${(idx += 1)})`;
    params.push(baseRouteWildcards);
  }

  // Status filter
  // Enum: 1XX, 2XX, 3XX, 4XX or 5XX (/[1-5]XX/g).
  // Query string: &statuses[]=2xx&statuses[]=3xx
  // SQL composition:
  // AND (
  //     (status_code >= 200 AND status_code <= 299) OR
  //     (status_code >= 300 AND status_code <= 399) OR
  //     (status_code >= 400 AND status_code <= 499)
  // )
  if (req.body.statuses) {
    req.body.statuses.forEach((status, index, array) => {
      where = `
                ${where}
                ${index === 0 ? 'AND (' : 'OR'}
                ( r.status_code >= $${(idx += 1)} AND
                  r.status_code <= $${(idx += 1)} )
                ${index === array.length - 1 ? ')' : ''}
            `;
      params.push(`${status[0]}00`, `${status[0]}99`);
    });
  }

  return { value: where, params };
}

const select = `
    WITH
        ics_user("id", "fullName", "email") AS (
            SELECT u.id, u.full_name, u.email
            FROM ics_user u
        ),
        company AS (
            SELECT c.id, c.name
            FROM company c
        ),
        owner AS (
            SELECT o.id, o.name
            FROM company o
        )
    SELECT
        r.request_id, r.route,
        row_to_json(u.*) AS user,
        row_to_json(c.*) AS company,
        row_to_json(o.*) AS owner,
        r.status_code, r.request, r.response, r.timestamp
`;

const from = `
    FROM report_api_audit r
        LEFT JOIN ics_user u ON u.id = r.user_id
        JOIN company c ON c.id = r.company_id
        LEFT JOIN company o ON o.id = r.owner
`;

const transformResult = row => {
  const data = row;
  data.timestamp = moment(row.timestamp).utc().format();
  return data;
};

module.exports = {
  getReport: (req, res, next) => {
    const pagingParams = paginationHelper.parsePaginationBody(req);
    const where = prepareWhere(req);

    return co(function* execute() {
      const totalObj = yield server.db.replica.trow(
        `SELECT COUNT(1) ${from} ${where.value}`,
        where.params
      );
      const results = yield server.db.replica.trows(
        `
                ${select}
                ${from}
                ${where.value}
                ORDER BY r.timestamp DESC, r.status_code, r.route
                LIMIT ${pagingParams.pageSize}
                OFFSET ${pagingParams.offset};
            `,
        where.params
      );

      res.send(200, {
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pagingParams.pageIndex,
          pageSize: pagingParams.pageSize,
        },
        results,
      });

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  getReportCSV: (req, res, next) => {
    const options = {
      delimiter: ',',
      endLine: '\n',
      columns: [
        'timestamp',
        'request_id',
        'status_code',
        'route',
        'user.id',
        'user.fullName',
        'user.email',
        'company.id',
        'company.name',
        'owner.id',
        'owner.name',
        'request',
        'response',
      ],
      escapeChar: '"',
      enclosedChar: '"',
      header: true,
    };

    const where = prepareWhere(req);

    return co(function* execute() {
      const connection = yield server.db.replica.getConnection();

      const query = new QueryStream(
        `
                ${select}
                ${from}
                ${where.value}
                ORDER BY
                    r.timestamp DESC, r.status_code, r.route;
            `,
        where.params
      );

      res.writeHead(200, {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename=report.csv',
      });

      const stream = connection.client.query(query);
      stream.on('end', connection.done);
      stream
        .pipe(transform(transformResult))
        .pipe(stringify(options))
        .pipe(res);

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  validateDateRange: (req, res, next) => {
    const MS_PER_DAY = 1000 * 60 * 60 * 24;
    const DAYS_MAX_RANGE = 100;

    const start = new Date(req.body.start);
    const end = new Date(req.body.end);

    const timeDiff = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.round(timeDiff / MS_PER_DAY);

    if (diffDays > DAYS_MAX_RANGE) {
      return next(
        new errors.BadRequestError(
          `Field 'end' must be less than ${DAYS_MAX_RANGE} days after 'start'`
        )
      );
    }

    return next();
  },
};
