const _ = require('lodash');
const co = require('co');
const moment = require('moment');
const stringify = require('csv-stringify');
const transform = require('stream-transform');
const QueryStream = require('pg-query-stream');
const { NA, NOT_APPLICABLE } = require('../../lib/app-constants').assetReport;
const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');
const deviceHelper = require('../../helpers/device-helper');
const { enumToScreenSize } = require('../../helpers/screensize-enum.json');

function prepareWhere(req) {
  let idx = 1;
  let where;
  const params = [];

  // First param is used inside JOIN statement
  params.push(req.user.company.id);
  idx += 1;

  if (req.body.sites && req.body.sites.length) {
    where = `WHERE t.active AND s.site_id = ANY($${idx})`;
    params.push(req.body.sites);
    idx += 1;
  } else where = `WHERE t.active`;

  where = `${where} AND exists (
        select 1
        from user_site_authorization usa
        where usa.site_id = s.site_id
          and usa.user_id = $${idx}
      )`;
  params.push(req.user.sub);

  return { value: where, params };
}

function formatColumnName(s) {
  return s.replace(/([A-Z])/g, a => `_${a.toLowerCase()}`);
}

function getDeviceState(deviceStates, state) {
  return (
    state.states
      .map(s => ({ state: s, value: deviceStates ? deviceStates[s] : null }))
      .find(s => s != null && s.value != null) || {
      state: state.name,
      value: null,
    }
  );
}

function calculateTimeLapse(timestamp) {
  let delta = Math.abs(new Date() - new Date(timestamp)) / 1000;

  const days = Math.floor(delta / 86400);
  delta -= days * 86400;

  const hours = Math.floor(delta / 3600) % 24;
  delta -= hours * 3600;

  const minutes = Math.floor(delta / 60) % 60;
  delta -= minutes * 60;

  const seconds = Math.floor(delta % 60);
  const totalTime = `${days} Days ${hours} Hours ${minutes} Minute(s) ${seconds} Second(s)`;
  if (days > 0) {
    return [`${days} Days`, totalTime];
  }
  if (hours > 0) {
    return [`${hours} Hours`, totalTime];
  }
  if (minutes > 0) {
    return [`${minutes} Minute(s)`, totalTime];
  }
  return [`${seconds} Second(s)`, totalTime];
}

const stateTransformations = {
  isMaster: (deviceState, device) =>
    (
      (device.deviceSerialNumber || device.device_serial_number) ===
      deviceState.value
    ).toString(),
  isUnicast: deviceState => deviceState.value && deviceState.value.substring(4),
  pkiExpiry: (deviceState, device) => {
    if (
      (device.deviceType ? device.deviceType.id : device.device_type) ===
      'G6-200'
    ) {
      return deviceHelper.getG6PkiExpiry(deviceState.value);
    }
    return deviceState.value;
  },
  terminalType: (deviceState, device) => {
    const deviceType = device.deviceType
      ? device.deviceType.id
      : device.device_type;
    return (
      deviceHelper.getDeviceModel(
        device.deviceSerialNumber || device.device_serial_number,
        deviceType
      ) || deviceState.value
    );
  },
  masterVolume: deviceState =>
    deviceState.value ? `${deviceState.value}%` : null,
  dateTime: deviceState => {
    const dt = deviceState.value;
    return dt
      ? `${dt.substring(0, 4)}-${dt.substring(4, 6)}-${dt.substring(6, 8)}
            T${dt.substring(8, 10)}:${dt.substring(10, 12)}:${dt.substring(
              12,
              14
            )}`
      : null;
  },
  dns: deviceState => {
    const dt = deviceState.value;
    return dt ? dt.replace(';', ',') : null;
  },
  auxiliary: deviceState => {
    const dt = deviceState.value;
    return dt != null && dt.toUpperCase() === 'AUX' ? 'true' : null;
  },
  default: deviceState => deviceState.value,
};

const defaultDeviceConfig = [
  {
    name: 'apcSerialNumber',
    states: ['invenco.system.g7opt.apc-hw-serialnumber'],
  },
  {
    name: 'sdcSerialNumber',
    states: ['invenco.system.g7opt.sdc-hw-serialnumber'],
  },
  {
    name: 'terminalType',
    states: ['invenco.system.g6opt.env-kernel-type'],
  },
  {
    name: 'auxiliary',
    states: ['invenco.system.cfg.net-terminal-rank'],
  },
  {
    name: 'sccSerialNumber',
    states: ['invenco.system.g7opt.scc-hw-serialnumber'],
  },
  {
    name: 'sysContactlessModule',
    states: ['invenco.system.g7opt.sys-contactless-module'],
  },
  {
    name: 'sccChannelStatus',
    states: ['invenco.system.g7opt.scc-channel-status'],
  },
  {
    name: 'sysSecurelinkUPCSCC',
    states: ['invenco.system.g7opt.sys-securelink-upc-scc'],
  },
];

const SELECT_SQL = `
    SELECT
        s.company_id                                                   AS company_id,
        ( select c.name from company c where c.id = s.company_id )     AS company_name,
        s.site_id                                                      AS site_id,
        s.name                                                         AS site_name,
        s.formatted_address                                            AS site_address,
        s.phone_number                                                 AS site_phone,
        s.reference_id                                                 AS site_reference_id,
        t.target_id                                                    AS device_id,
        get_device_health(t.target_id)                                 AS device_status,
        t.name                                                         AS device_name,
        t.mac_address                                                  AS mac_address,
        t.subnet_mask                                                  AS subnet_mask,
        t.gateway_address                                              AS gateway_address,
        t.key_group_ref                                                AS key_group_ref,
        t.serial_number                                                AS device_serial_number,
        t.last_registered                                              AS device_commissioned_date,
        t.release_version                                              AS device_release_version,
    `;

const COUNT_SQL = `
    count(*) OVER()                                                AS full_count,
`;

const generateSubselectSql = stateIdx => `
        (
            select kg.key_group_name
            from key_group kg
            where kg.key_group_id = s.key_group_id
              and kg.company_id = s.company_id
        )  AS site_key_group_name,
        (
            SELECT json_build_object(
                'id', p.device_type,
                'name', p.display_name,
                'screenSize', t.screen_size
            )
            FROM company_product_type p
            WHERE p.device_type = t.device_type
              AND p.company = $1
        )                                                              AS device_type,
        (
            SELECT json_object_agg(
                state,
                value
            )
            FROM device_states ds
            WHERE t.target_id = ds.device_id
              AND ds.state = ANY($${stateIdx})
        )                                                              AS device_states,
        COALESCE(t.real_ip_address, t.ip_address)                      AS device_ip_address
`;

const FROM_SQL = `
    FROM target t
    JOIN site s ON t.site_id = s.site_id
`;

const generateDeviceOosSql = siteParams => {
  // Conditionally create the WHERE condition for sites
  const sitesWhereConditionFilter =
    siteParams.length > 0 ? `s.site_id = ANY($1) AND` : '';

  const DEVICE_OOS_SQL_QRY = `SELECT
                            t.target_id as device_id,
                            device_status,
                            (
                            SELECT json_object( array_agg(dh.name), array_agg(json_build_object('value', dh.value, 'timestamp', dh.timestamp )::text) )
                            FROM ics_state._device_health dh
                            WHERE dh.device_id = t.target_id AND dh.is_critical
                            ) as oos
                        FROM target t
                        JOIN LATERAL get_device_health(t.target_id) as device_status ON true
                        JOIN site s ON s.site_id = t.site_id
                        JOIN user_site_authorization a ON a.site_id = s.site_id
                        WHERE
                            ${sitesWhereConditionFilter}
                            t.active AND
                            device_status = 'OUT_OF_SERVICE'
                        GROUP BY t.target_id, device_status`;

  return {
    deviceOosSql: DEVICE_OOS_SQL_QRY,
    queryParams: siteParams.length > 0 ? [siteParams] : undefined,
  };
};

const flatConfigs = configs => {
  const stateTypeArray = [];
  for (let i = 0; i < configs.length; i++) {
    const { states } = configs[i];
    if (states && states.length) {
      states.forEach(state => {
        stateTypeArray.push(state);
      });
    }
  }
  return stateTypeArray.join(',');
};

module.exports = {
  getAssetReport: async (req, res, next) => {
    const pagingParams = paginationHelper.parsePaginationBody(req);
    try {
      const WHERE = prepareWhere(req);

      const stateType = flatConfigs(defaultDeviceConfig);
      let hasAuxiliary = false;

      WHERE.params.push(`{${stateType}}`);

      const SUBSELECT_SQL = generateSubselectSql(WHERE.params.length);

      const qResults = await server.db.read.rows(
        `
                ${SELECT_SQL}
                ${COUNT_SQL}
                ${SUBSELECT_SQL}
                ${FROM_SQL}
                ${WHERE.value}
                ORDER BY company_name, s.name, t.serial_number
                LIMIT ${pagingParams.pageSize}
                OFFSET ${pagingParams.offset};
            `,
        WHERE.params
      );

      const count = qResults.length ? qResults[0].fullCount : 0;

      const siteParams = req.body?.sites ?? [];
      const { deviceOosSql, queryParams } = generateDeviceOosSql(siteParams);

      const oosResults = await server.db.read.rows(deviceOosSql, queryParams);

      const results = _.map(qResults, data => {
        const result = data;

        if (result.deviceType) {
          result.deviceType.screenSize =
            enumToScreenSize[result.deviceType.screenSize] || null;
          delete result.deviceType.company;
        }
        // ICS-8904 : Show SCC serial number if only connected
        if (result.deviceStates) {
          if (
            result.deviceStates[
              'invenco.system.g7opt.sys-contactless-module'
            ] &&
            ['scc-default', 'scc-config'].indexOf(
              result.deviceStates['invenco.system.g7opt.sys-contactless-module']
            ) > -1
          ) {
            if (
              result.deviceStates['invenco.system.g7opt.scc-channel-status'] &&
              result.deviceStates['invenco.system.g7opt.scc-channel-status'] !==
                'up'
            ) {
              result.deviceStates['invenco.system.g7opt.scc-hw-serialnumber'] =
                '';
            }

            if (
              result.deviceStates[
                'invenco.system.g7opt.sys-securelink-upc-scc'
              ] &&
              result.deviceStates[
                'invenco.system.g7opt.sys-securelink-upc-scc'
              ] !== 'up'
            ) {
              result.deviceStates['invenco.system.g7opt.scc-hw-serialnumber'] =
                '';
            }
          } else {
            result.deviceStates['invenco.system.g7opt.scc-hw-serialnumber'] =
              '';
          }
        }

        defaultDeviceConfig.forEach(state => {
          const st = getDeviceState(result.deviceStates, state);
          result[state.name] =
            state.name in stateTransformations
              ? stateTransformations[state.name](st, data)
              : stateTransformations.default(st);
        });

        const deviceOOSResult = oosResults.filter(
          d => d.deviceId === result.deviceId
        );

        if (result.deviceStatus === 'OUT_OF_SERVICE') {
          const oosDetailsArray = [];
          let highestTimestamp;
          if (deviceOOSResult.length !== 0) {
            const deviceOOSDetails =
              deviceOOSResult[0] && deviceOOSResult[0].oos;
            if (deviceOOSDetails) {
              // eslint-disable-next-line no-restricted-syntax
              for (const [key, value] of Object.entries(deviceOOSDetails)) {
                const currentValueObj = value && JSON.parse(value);
                const currentTimestamp =
                  currentValueObj && currentValueObj.timestamp;
                const oosCategoryAndCondition =
                  deviceHelper.getOOSCategoryAndCondition({
                    name: key,
                    value: currentValueObj.value,
                  });
                if (oosCategoryAndCondition) {
                  const {
                    category = NOT_APPLICABLE,
                    condition = NOT_APPLICABLE,
                  } = oosCategoryAndCondition;
                  if (
                    (currentTimestamp && !highestTimestamp) ||
                    new Date(highestTimestamp) > new Date(currentTimestamp)
                  ) {
                    highestTimestamp = currentTimestamp;
                    oosDetailsArray.unshift([category, condition]);
                  } else {
                    oosDetailsArray.push([category, condition]);
                  }
                } else {
                  oosDetailsArray.push([
                    NOT_APPLICABLE,
                    NOT_APPLICABLE,
                    NOT_APPLICABLE,
                    NOT_APPLICABLE,
                  ]);
                }
              }
            }
          }

          if (oosDetailsArray.length > 0) {
            oosDetailsArray[0] = [
              ...oosDetailsArray[0],
              ...calculateTimeLapse(highestTimestamp),
            ];
          } else {
            oosDetailsArray.push([
              NOT_APPLICABLE,
              NOT_APPLICABLE,
              NOT_APPLICABLE,
              NOT_APPLICABLE,
            ]);
          }
          result.oosDetails = oosDetailsArray;
        } else {
          result.oosDetails = [null, null, null];
        }

        delete result.deviceStates;
        if (result.auxiliary) {
          hasAuxiliary = true;
        }
        return result;
      });

      res.send(200, {
        resultsMetadata: {
          totalResults: count,
          pageIndex: pagingParams.pageIndex,
          pageSize: pagingParams.pageSize,
          hasAuxiliary,
        },
        results,
      });
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getAssetReportCSV: (req, res, next) => {
    const WHERE = prepareWhere(req);

    const { allConfig } = req.body;
    const deviceConfig = allConfig
      ? deviceHelper.getDeviceStateConfig()
      : defaultDeviceConfig;

    const stateType = flatConfigs(deviceConfig);

    WHERE.params.push(`{${stateType}}`);

    const options = {
      delimiter: ',',
      endLine: '\n',
      escapeChar: '"',
      enclosedChar: '"',
      header: true,
    };

    if (
      req.body.isAppFFEnabled_ADA === true &&
      req.body.hasAuxiliary === true
    ) {
      options.columns = [
        'company_name',
        'site_name',
        'site_reference_id',
        'device_name',
        'device_status',
        'oos_timestamp',
        'oos_category_condition',
        'device_type',
        'device_type_name',
        'terminal_type',
        'device_serial_number',
        'auxiliary',
        'apc_serial_number',
        'scc_serial_number',
        'sdc_serial_number',
        'device_commissioned_date',
        'device_release_version',
        'mac_address',
        'site_key_group_name',
        'key_group_ref',
      ];
    } else {
      options.columns = [
        'company_name',
        'site_name',
        'site_reference_id',
        'device_name',
        'device_status',
        'oos_timestamp',
        'oos_category_condition',
        'device_type',
        'device_type_name',
        'terminal_type',
        'device_serial_number',
        'scc_serial_number',
        'apc_serial_number',
        'sdc_serial_number',
        'device_commissioned_date',
        'device_release_version',
        'mac_address',
        'site_key_group_name',
        'key_group_ref',
      ];
    }

    if (allConfig) {
      options.columns.splice(12, 0, 'apc_mode');
      options.columns = options.columns.concat([
        'controller_ip',
        'controller_port',
        'dns',
        'syslog',
        'ntp_server_ip_address',
        'ntp_pool',
        'configuration_service_ip_address',
        'configuration_service_port',
        'printer_model',
        'printer_margin_x',
        'printer_margin_y',
        'printer_cut_offset',
        'network_mode',
        'network_mask',
        'network_gateway',
        'memory',
        'is_unicast',
        'terminal_id',
        'terminal_ip_address',
        'date_time',
        'time_zone',
        'pci_reboot_time',
        'master_volume',
        'audio_output_channel',
        'is_master',
        'temperature',
      ]);
    }

    const SUBSELECT_SQL = generateSubselectSql(WHERE.params.length);

    return co(function* execute() {
      const connection = yield server.db.read.getConnection();
      const query = new QueryStream(
        `
                ${SELECT_SQL}
                ${SUBSELECT_SQL}
                ${FROM_SQL}
                ${WHERE.value} 
                ORDER BY company_name, s.site_id, t.serial_number
            `,
        WHERE.params
      );

      res.writeHead(200, {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename=report.csv',
      });

      const stream = connection.client.query(query);
      stream.on('end', connection.done);
      stream.on('error', error => {
        req.log.error(`Asset report CSV error in database query: ${error}`);
      });

      const stringifier = stringify(options);

      const siteParams = req.body?.sites ?? [];
      const { deviceOosSql, queryParams } = generateDeviceOosSql(siteParams);

      // Execute the query with the determined parameter
      const oosResults = yield server.db.read.rows(deviceOosSql, queryParams);

      const transoformStream = transform(data => {
        const row = data;

        if (row.device_type) {
          const screenSize =
            enumToScreenSize[row.device_type.screenSize] || null;
          row.device_type_name = row.device_type.name;
          row.device_type =
            screenSize !== null
              ? `${row.device_type.id} (${screenSize})`
              : row.device_type.id;
        }

        if (row.device_commissioned_date instanceof Date) {
          row.device_commissioned_date = moment(
            row.device_commissioned_date
          ).format();
        }
        if (row.device_states) {
          if (
            row.device_states['invenco.system.g7opt.sys-contactless-module'] &&
            ['scc-default', 'scc-config'].indexOf(
              row.device_states['invenco.system.g7opt.sys-contactless-module']
            ) > -1
          ) {
            if (
              row.device_states['invenco.system.g7opt.scc-channel-status'] &&
              row.device_states['invenco.system.g7opt.scc-channel-status'] !==
                'up'
            ) {
              row.device_states['invenco.system.g7opt.scc-hw-serialnumber'] =
                '';
            }

            if (
              row.device_states[
                'invenco.system.g7opt.sys-securelink-upc-scc'
              ] &&
              row.device_states[
                'invenco.system.g7opt.sys-securelink-upc-scc'
              ] !== 'up'
            ) {
              row.device_states['invenco.system.g7opt.scc-hw-serialnumber'] =
                '';
            }
          } else {
            row.device_states['invenco.system.g7opt.scc-hw-serialnumber'] = '';
          }
        }

        deviceConfig.forEach(state => {
          const st = getDeviceState(data.device_states, state);
          row[formatColumnName(state.name)] =
            state.name in stateTransformations
              ? stateTransformations[state.name](st, data)
              : stateTransformations.default(st);
        });

        const deviceOOSResult = oosResults.filter(
          d => d.deviceId === row.device_id
        );
        if (row.device_status === 'OUT_OF_SERVICE') {
          const oosArray = [];
          let highestTimestamp;
          if (deviceOOSResult.length !== 0) {
            const deviceOOSDetails =
              deviceOOSResult[0] && deviceOOSResult[0].oos;
            if (deviceOOSDetails) {
              // eslint-disable-next-line no-restricted-syntax
              for (const [key, value] of Object.entries(deviceOOSDetails)) {
                const currentValueObj = value && JSON.parse(value);
                const currentTimestamp =
                  currentValueObj && currentValueObj.timestamp;
                if (currentTimestamp)
                  highestTimestamp =
                    !highestTimestamp ||
                    new Date(highestTimestamp) > new Date(currentTimestamp)
                      ? currentTimestamp
                      : highestTimestamp;
                const oosCategoryAndCondition =
                  deviceHelper.getOOSCategoryAndCondition({
                    name: key,
                    value: currentValueObj.value,
                  });
                if (oosCategoryAndCondition) {
                  const { category = NA, condition = NA } =
                    oosCategoryAndCondition;
                  const oosStr = `${category} / ${condition}`;
                  oosArray.push(oosStr);
                }
              }
            }
          }
          row.oos_category_condition =
            oosArray.length > 0 ? oosArray.join() : NA;
          row.oos_timestamp = highestTimestamp || NA;
        }

        if (
          row.device_states &&
          !row.printer_model &&
          typeof row.device_states['invenco.system.g7opt.apc-printer-model'] ===
            'string'
        ) {
          row.printer_model =
            row.device_states['invenco.system.g7opt.apc-printer-model'];
        }
        delete row.device_states;

        if (
          (row.printer_model || '').toUpperCase() === 'NIPPON' &&
          !!row.printer_paper_width &&
          row.printer_paper_width !== 'none' &&
          Number(row.printer_paper_width) > 2.2
        ) {
          row.printer_model = `${row.printer_model.toUpperCase()} 3"`;
          delete row.printer_paper_width;
        } else if (
          (row.printer_model || '').toUpperCase() === 'NIPPON' &&
          !!row.printer_paper_width &&
          row.printer_paper_width !== 'none' &&
          Number(row.printer_paper_width) <= 2.2
        ) {
          row.printer_model = `${row.printer_model.toUpperCase()} 2"`;
          delete row.printer_paper_width;
        }

        return row;
      });

      stream
        .pipe(transoformStream)
        .on('error', error => {
          req.log.error(
            `Asset report CSV error in tranform pipeline: ${error} ${error.stack}`
          );
          stream.pipe(stringifier);
        })
        .pipe(stringifier)
        .on('error', error => {
          req.log.error(
            `Asset report CSV error in stringifier pipeline: ${error} ${error.stack}`
          );
        })
        .pipe(res);

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
};
