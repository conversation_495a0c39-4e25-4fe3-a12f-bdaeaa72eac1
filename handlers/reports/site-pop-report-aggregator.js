const timezone = require('moment-timezone');
const newrelic = require('newrelic');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const { config } = require('../../env');
const AWS = require('../../lib/aws');
const utils = require('../../lib/utils');
const env = require('../../env');

const maxTimeout = parseInt(config.AWS.DDB.ddbMaxTimeoutInSeconds, 10) * 1000;

const ttlDelay = config.AWS.DDB.sitePOPTableTTLinDays * 86400;

async function sendToSQS(items, req) {
  let index = 0;
  while (index < items.length) {
    let entries = items.slice(index, index + 10);

    entries = entries.map(item => {
      const sequenceId =
        item.PutRequest.Item.siteId + item.PutRequest.Item.date_name_SK;
      return { MessageBody: JSON.stringify(item), Id: sequenceId };
    });

    // eslint-disable-next-line no-await-in-loop
    await AWS.sendSqsMessageBatch({
      Entries: entries,
      QueueUrl: config.AWS.DDB.sitePOP_DLQ,
    })
      .then(sqsResponse => {
        req.log.error(
          `Successfully sent ${sqsResponse.Successful.length} records to SitePOP Dead Letter Queue`
        );
        if (sqsResponse.Failed.length > 0) {
          req.log.error(
            'Failed to send records to Site POP Dead Letter Queue: ',
            JSON.stringify(entries)
          );
        }
      })
      .catch(err => {
        req.log.error(
          'Error occurred when sending messages to Site POP Dead Letter Queue: ',
          JSON.stringify(err)
        );
      });
    index += 10;
  }
}

module.exports = {
  async aggregateSiteDailyData(req, res, next) {
    let timeout = parseInt(config.AWS.DDB.ddbInitTimeoutInSeconds, 10) * 500;
    const excludeTenants =
      config.filterPopSiteAggregator?.excludeCompaniesList &&
      config.filterPopSiteAggregator.excludeCompaniesList.length > 0
        ? config.filterPopSiteAggregator.excludeCompaniesList
        : [];
    try {
      const date = new Date();
      const sitesToProcess = await server.db.replica.rows(
        `
          SELECT s.site_id, s.company_id, tz.name as tz
          FROM pg_timezone_names() tz
          JOIN site s ON tz.name = s.timezone_id
          WHERE ($1::uuid[] IS NULL OR COALESCE(array_length($1::uuid[], 1), 0) = 0 OR NOT (s.company_id = ANY($1::uuid[])))
          AND timezone_id IS NOT NULL
          AND extract(hour from timezone(tz.name,$2))=0
        `,
        [excludeTenants, date]
      );

      const aggregationDate = new Date(date.getTime() - 86400000);
      const timeInUTC = timezone.tz(aggregationDate, 'UTC');
      let items = [];
      for (let i = 0; i < sitesToProcess.length; i++) {
        const aggregationDateString = timeInUTC
          .clone()
          .tz(sitesToProcess[i].tz)
          .format()
          .slice(0, 10);
        const queryResult = [];
        const params = {
          ExpressionAttributeValues: {
            ':siteId': sitesToProcess[i].siteId,
            ':event_date_GSI_SK': `gstv-pop#${aggregationDateString}`,
          },
          KeyConditionExpression:
            'siteId = :siteId AND event_date_GSI_SK = :event_date_GSI_SK',
          ProjectionExpression: 'dv',
          TableName: config.AWS.DDB.deviceDataTable,
          IndexName: 'siteId-event_date_GSI_SK-index',
          ScanIndexForward: false,
        };
        let data;
        do {
          // eslint-disable-next-line no-await-in-loop
          data = await AWS.DDBDC.query(params);
          if (typeof data.LastEvaluatedKey !== 'undefined') {
            params.ExclusiveStartKey = data.LastEvaluatedKey;
          }
          queryResult.push(...data.Items);
        } while (typeof data.LastEvaluatedKey !== 'undefined');

        /* eslint-disable no-param-reassign, no-unsafe-finally */
        const reducedResult = queryResult.reduce((acc, queryResultItem) => {
          try {
            queryResultItem.dv = JSON.parse(queryResultItem.dv);
            if (
              queryResultItem.dv.filename &&
              queryResultItem.dv.endTimestamp &&
              queryResultItem.dv.startTimestamp
            ) {
              const endTime = Date.parse(queryResultItem.dv.endTimestamp);
              const startTime = Date.parse(queryResultItem.dv.startTimestamp);
              const runtime = endTime - startTime;

              if (
                Object.prototype.hasOwnProperty.call(
                  acc,
                  queryResultItem.dv.filename
                )
              ) {
                acc[queryResultItem.dv.filename].count += 1;
                acc[queryResultItem.dv.filename].runtime += runtime;
              } else {
                acc[queryResultItem.dv.filename] = { count: 1, runtime };
              }
            }
          } catch (err) {
            req.log.error(
              `Error '${err}' occurred in pop-site-aggregator: queryResultItem = ${JSON.stringify(
                queryResultItem
              )}`
            );
          } finally {
            return acc;
          }
        }, {});
        /* eslint-enable no-param-reassign, no-unsafe-finally */

        const keys = Object.keys(reducedResult);
        for (let k = 0; k < keys.length; k++) {
          items.push({
            PutRequest: {
              Item: {
                GSI_PK: sitesToProcess[i].companyId,
                siteId: sitesToProcess[i].siteId,
                date_name_SK: `${aggregationDateString}#${keys[k]}`,
                name: keys[k],
                count: reducedResult[keys[k]].count,
                runtime: reducedResult[keys[k]].runtime,
                ttl: parseInt(Date.now() / 1000, 10) + ttlDelay,
              },
            },
          });
        }
      }

      // Report to NewRelic for site-pop monitoring.
      if (env.config.enableNewRelic) {
        const itemsGroupBySites = items.reduce((rv, x) => {
          // eslint-disable-next-line no-param-reassign
          (rv[x.PutRequest.Item.siteId] =
            rv[x.PutRequest.Item.siteId] || []).push(x.PutRequest.Item);
          return rv;
        }, {});

        // eslint-disable-next-line no-restricted-syntax
        for (const itemSiteId of Object.keys(itemsGroupBySites)) {
          // Report all sites unless sitePopSites object defined. (Empty sites array will not report any).
          if (
            !env.config.sitePopSites ||
            (Array.isArray(env.config.sitePopSites) &&
              env.config.sitePopSites.includes(itemSiteId))
          ) {
            newrelic.recordCustomEvent('SitePopMonitor', {
              siteId: itemSiteId,
              totalRuntime: itemsGroupBySites[itemSiteId].reduce(
                (a, b) => a + b.runtime,
                0
              ),
            });
          }
        }
      }

      // save all records to Dynamo sitePopTable
      let remainingItems = items.splice(25);
      while (items.length > 0) {
        const params = { RequestItems: {} };
        params.RequestItems[config.AWS.DDB.sitePOPTable] = items;
        try {
          // eslint-disable-next-line no-await-in-loop
          const data = await AWS.DDBDC.batchWrite(params);
          if (data.UnprocessedItems[config.AWS.DDB.sitePOPTable]) {
            items = data.UnprocessedItems[config.AWS.DDB.sitePOPTable];
            timeout *= 2;
            if (timeout > maxTimeout) {
              req.log.error(
                'Site POP aggregation reached maximum timeout = ',
                maxTimeout
              );
              // eslint-disable-next-line no-await-in-loop
              await sendToSQS(items, req);
              items = [];
            }
            // eslint-disable-next-line no-await-in-loop
            await utils.sleep(timeout);
          } else {
            items = [];
          }
        } catch (err) {
          req.log.error(
            'Error occurred while saving aggregated site POP records in DynamoDB: ',
            JSON.stringify(err)
          );
          req.log.error(
            'Following site POP records have not been saved in DynamoDB: ',
            JSON.stringify(items)
          );
          // eslint-disable-next-line no-await-in-loop
          await sendToSQS(items, req);
          items = [];
        }
        items.push(...remainingItems);
        remainingItems = items.splice(25);
      }
      res.send(200);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  /* ONLY MEANT TO BE USED DURING ROLLBACK FROM 3.0 to 2.X */
  /* IT WILL AUTOMATICALLY RE-AGGREGATE ALL DATA FOR ALL SITES FOR LAST FULL DAY (FROM MIDNIGHT TO MIDNIGHT IN SITES OWN TIMEZONE */
  /* TTL ON device-data TABLE MUST BE TO AT LEAST 48 HOURS RATHER THAN NORMAL 24 HOURS !!! */
  /* TTL NEEDS TO BE CHANGED AT LEAST 48 HOURS BEFORE INVOCATION OF THIS FUNCTION TO ALLOW NECESSARY DATA TO BE COLLECTED */
  async reaggregateSiteDailyData(req, res, next) {
    const currentDate = new Date();
    try {
      let timeout = parseInt(config.AWS.DDB.ddbInitTimeoutInSeconds, 10) * 500;
      for (let h = 0; h < 24; h++) {
        const date = new Date(currentDate.getTime() - h * 3600000);
        // eslint-disable-next-line no-await-in-loop
        const sitesToProcess = await server.db.replica.rows(
          `
            SELECT s.site_id, s.company_id, tz.name as tz FROM pg_timezone_names() tz JOIN site s ON tz.name = s.timezone_id
            WHERE timezone_id IS NOT NULL AND extract( hour from timezone( tz.name, $1 ) ) = 0
            `,
          [date]
        );

        const aggregationDate = new Date(date.getTime() - 86400000);
        const timeInUTC = timezone.tz(aggregationDate, 'UTC');
        let items = [];
        for (let i = 0; i < sitesToProcess.length; i++) {
          const aggregationDateString = timeInUTC
            .clone()
            .tz(sitesToProcess[i].tz)
            .format()
            .slice(0, 10);
          const queryResult = [];
          const params = {
            ExpressionAttributeValues: {
              ':siteId': sitesToProcess[i].siteId,
              ':event_date_GSI_SK': `gstv-pop#${aggregationDateString}`,
            },
            KeyConditionExpression:
              'siteId = :siteId AND event_date_GSI_SK = :event_date_GSI_SK',
            ProjectionExpression: 'dv',
            TableName: config.AWS.DDB.deviceDataTable,
            IndexName: 'siteId-event_date_GSI_SK-index',
            ScanIndexForward: false,
          };
          let data;
          do {
            // eslint-disable-next-line no-await-in-loop
            data = await AWS.DDBDC.query(params);
            if (typeof data.LastEvaluatedKey !== 'undefined') {
              params.ExclusiveStartKey = data.LastEvaluatedKey;
            }
            queryResult.push(...data.Items);
          } while (typeof data.LastEvaluatedKey !== 'undefined');

          /* eslint-disable no-param-reassign, no-unsafe-finally */
          const reducedResult = queryResult.reduce((acc, queryResultItem) => {
            try {
              queryResultItem.dv = JSON.parse(queryResultItem.dv);
              if (
                queryResultItem.dv.filename &&
                queryResultItem.dv.endTimestamp &&
                queryResultItem.dv.startTimestamp
              ) {
                const endTime = Date.parse(queryResultItem.dv.endTimestamp);
                const startTime = Date.parse(queryResultItem.dv.startTimestamp);
                const runtime = endTime - startTime;

                if (
                  Object.prototype.hasOwnProperty.call(
                    acc,
                    queryResultItem.dv.filename
                  )
                ) {
                  acc[queryResultItem.dv.filename].count += 1;
                  acc[queryResultItem.dv.filename].runtime += runtime;
                } else {
                  acc[queryResultItem.dv.filename] = { count: 1, runtime };
                }
              }
            } catch (err) {
              req.log.error(
                `Error '${err}' occurred in pop-site-aggregator: queryResultItem = ${JSON.stringify(
                  queryResultItem
                )}`
              );
            } finally {
              return acc;
            }
          }, {});
          /* eslint-enable no-param-reassign, no-unsafe-finally */

          const keys = Object.keys(reducedResult);
          for (let k = 0; k < keys.length; k++) {
            items.push({
              PutRequest: {
                Item: {
                  GSI_PK: sitesToProcess[i].companyId,
                  siteId: sitesToProcess[i].siteId,
                  date_name_SK: `${aggregationDateString}#${keys[k]}`,
                  name: keys[k],
                  count: reducedResult[keys[k]].count,
                  runtime: reducedResult[keys[k]].runtime,
                  ttl: parseInt(Date.now() / 1000, 10) + ttlDelay,
                },
              },
            });
          }
        }

        // Report to NewRelic for site-pop monitoring.
        if (env.config.enableNewRelic) {
          const itemsGroupBySites = items.reduce((rv, x) => {
            // eslint-disable-next-line no-param-reassign
            (rv[x.PutRequest.Item.siteId] =
              rv[x.PutRequest.Item.siteId] || []).push(x.PutRequest.Item);
            return rv;
          }, {});

          // eslint-disable-next-line no-restricted-syntax
          for (const itemSiteId of Object.keys(itemsGroupBySites)) {
            // Report all sites unless sitePopSites object defined. (Empty sites array will not report any).
            if (
              !env.config.sitePopSites ||
              (Array.isArray(env.config.sitePopSites) &&
                env.config.sitePopSites.includes(itemSiteId))
            ) {
              newrelic.recordCustomEvent('SitePopMonitor', {
                siteId: itemSiteId,
                totalRuntime: itemsGroupBySites[itemSiteId].reduce(
                  (a, b) => a + b.runtime,
                  0
                ),
              });
            }
          }
        }

        // save all records to Dynamo sitePopTable
        let remainingItems = items.splice(25);
        while (items.length > 0) {
          const params = { RequestItems: {} };
          params.RequestItems[config.AWS.DDB.sitePOPTable] = items;
          try {
            // eslint-disable-next-line no-await-in-loop
            const data = await AWS.DDBDC.batchWrite(params);
            if (data.UnprocessedItems[config.AWS.DDB.sitePOPTable]) {
              items = data.UnprocessedItems[config.AWS.DDB.sitePOPTable];
              timeout *= 2;
              if (timeout > maxTimeout) {
                req.log.error(
                  'Site POP aggregation reached maximum timeout = ',
                  maxTimeout
                );
                // eslint-disable-next-line no-await-in-loop
                await sendToSQS(items, req);
                items = [];
              }
              // eslint-disable-next-line no-await-in-loop
              await utils.sleep(timeout);
            } else {
              items = [];
            }
          } catch (err) {
            req.log.error(
              'Error occurred while saving aggregated site POP records in DynamoDB: ',
              JSON.stringify(err)
            );
            req.log.error(
              'Following site POP records have not been saved in DynamoDB: ',
              JSON.stringify(items)
            );
            // eslint-disable-next-line no-await-in-loop
            await sendToSQS(items, req);
            items = [];
          }
          items.push(...remainingItems);
          remainingItems = items.splice(25);
        }
      }
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
    res.send(200);
    return next();
  },
};
