const restify = require('restify');
const stringify = require('csv-stringify');
const moment = require('moment');

const AWS = require('../../lib/aws');
const errorHandler = require('../../lib/errorhandler');
const { config } = require('../../env');
const { server } = require('../../app');

async function getParams(req) {
  const devices = req.body.deviceIds;
  const sites = req.body.siteIds;
  const userId = req.user.sub;
  const companyId = req.user.company.id;
  const start = req.body.start
    ? parseInt(req.body.start, 10)
    : Date.now() - 86400000;
  const end = req.body.end ? parseInt(req.body.end, 10) : Date.now();

  const authorizedSites = await server.db.replica.rows(
    `
        SELECT
            s.site_id,
            s.name as site_name,
            s.reference_id,
            s.formatted_address,
           (SELECT json_agg(target_id) from target where site_id = s.site_id) AS devices
        FROM site s
        JOIN user_site_authorization usa ON usa.site_id = s.site_id
        WHERE s.active
        AND usa.user_id = $1
        ORDER BY s.name
    `,
    [userId]
  );

  return {
    userId,
    companyId,
    start,
    end,
    devices,
    sites,
    authorizedSites,
  };
}

async function getDevicesRecords(devices, start, end) {
  const results = [];
  const individualResults = [];
  const resultPromises = [];
  const paramsArray = [];
  const subqueryComplete = [];

  for (let i = 0; i < devices.length; i++) {
    individualResults[i] = [];
    paramsArray.push({
      ExpressionAttributeValues: {
        ':deviceId': `${devices[i]}`,
        ':eventStart': 'gstv-',
        ':excluded': 'gstv-pop',
        ':timeStart': start,
        ':timeEnd': end,
      },
      KeyConditionExpression:
        'deviceId = :deviceId AND begins_with( event_ts_GSI_SK, :eventStart )',
      FilterExpression:
        'dn <> :excluded AND ts BETWEEN :timeStart AND :timeEnd',
      ProjectionExpression: 'deviceId, siteId, dn, dv, ts',
      TableName: config.AWS.DDB.deviceDataTable,
      IndexName: 'deviceId-event_ts_GSI_SK-index',
      ScanIndexForward: false,
    });

    resultPromises.push(AWS.DDBDC.query(paramsArray[i]));
  }

  let queryNotComplete = false;
  do {
    queryNotComplete = false;
    // eslint-disable-next-line no-await-in-loop
    const partialResult = await Promise.all(resultPromises);
    for (let j = 0; j < partialResult.length; j++) {
      if (subqueryComplete[j]) {
        // do nothing
      } else {
        const data = partialResult[j];
        if (typeof data.LastEvaluatedKey !== 'undefined') {
          paramsArray[j].ExclusiveStartKey = data.LastEvaluatedKey;
          queryNotComplete = true;
          resultPromises[j] = AWS.DDBDC.query(paramsArray[j]);
        } else {
          subqueryComplete[j] = true;
        }
        individualResults[j].push(...data.Items);
      }
    }
  } while (queryNotComplete);

  // combine individual results into combined result
  for (let x = 0; x < individualResults.length; x++) {
    results.push(...individualResults[x]);
  }
  return results;
}

async function getSiteRecords(sites, start, end) {
  const results = [];
  const individualResults = [];
  const resultPromises = [];
  const paramsArray = [];
  const subqueryComplete = [];

  for (let i = 0; i < sites.length; i++) {
    individualResults[i] = [];
    paramsArray.push({
      ExpressionAttributeValues: {
        ':siteId': sites[i],
        ':eventStart': 'gstv-',
        ':excluded': 'gstv-pop',
        ':timeStart': start,
        ':timeEnd': end,
      },
      KeyConditionExpression:
        'siteId = :siteId AND begins_with( event_date_GSI_SK, :eventStart )',
      FilterExpression:
        'dn <> :excluded AND ts BETWEEN :timeStart AND :timeEnd',
      ProjectionExpression: 'deviceId, siteId, dn, dv, ts',
      TableName: config.AWS.DDB.deviceDataTable,
      IndexName: 'siteId-event_date_GSI_SK-index',
      ScanIndexForward: false,
    });

    resultPromises.push(AWS.DDBDC.query(paramsArray[i]));
  }

  let queryNotComplete = false;
  do {
    queryNotComplete = false;
    // eslint-disable-next-line no-await-in-loop
    const partialResult = await Promise.all(resultPromises);
    for (let j = 0; j < partialResult.length; j++) {
      if (subqueryComplete[j]) {
        // do nothing
      } else {
        const data = partialResult[j];
        if (typeof data.LastEvaluatedKey !== 'undefined') {
          paramsArray[j].ExclusiveStartKey = data.LastEvaluatedKey;
          queryNotComplete = true;
          resultPromises[j] = AWS.DDBDC.query(paramsArray[j]);
        } else {
          subqueryComplete[j] = true;
        }
        individualResults[j].push(...data.Items);
      }
    }
  } while (queryNotComplete);

  // combine individual results into combined result
  for (let x = 0; x < individualResults.length; x++) {
    results.push(...individualResults[x]);
  }
  return results;
}

async function getCompanyRecords(companyId, start, end) {
  const results = [];
  const params = {
    ExpressionAttributeValues: {
      ':companyId': companyId,
      ':timeStart': start,
      ':timeEnd': end,
    },
    KeyConditionExpression: 'companyId = :companyId',
    FilterExpression: 'ts BETWEEN :timeStart AND :timeEnd',
    ProjectionExpression: 'deviceId, siteId, dn, dv, ts',
    TableName: config.AWS.DDB.deviceDataTable,
    IndexName: 'companyId-ts_GSI_SK-index',
    ScanIndexForward: false,
  };
  let data;
  do {
    // eslint-disable-next-line no-await-in-loop
    data = await AWS.DDBDC.query(params);
    if (typeof data.LastEvaluatedKey !== 'undefined') {
      params.ExclusiveStartKey = data.LastEvaluatedKey;
    }
    results.push(...data.Items);
  } while (typeof data.LastEvaluatedKey !== 'undefined');
  return results;
}

function anyUnauthorizedDevices(authorizedSites, devices) {
  let authorizedDevices = [];
  if (authorizedSites && authorizedSites.length > 0) {
    authorizedSites.forEach(site => {
      if (site.devices && site.devices.length > 0) {
        authorizedDevices = authorizedDevices.concat(site.devices);
      }
    });
    const unAuthorizedDevices = devices.filter(
      d => !authorizedDevices.includes(d)
    );
    return unAuthorizedDevices && unAuthorizedDevices.length > 0;
  }
  return true;
}

function anyUnauthorizedSites(authorizedSites, sites) {
  if (authorizedSites && authorizedSites.length > 0) {
    const filteredSites = authorizedSites.filter(authorizedSite =>
      sites.includes(authorizedSite.siteId)
    );
    return filteredSites.length < sites.length;
  }
  return true;
}

function transformData(records, companyId, authorizedSites, stringifier) {
  let results = [];
  if (records && records.length > 0) {
    results = records.map(item => {
      const result = {
        deviceId:
          item.deviceId && !item.deviceId.startsWith('COMPANYID')
            ? item.deviceId
            : null,
        siteId: item.siteId,
        companyId,
        eventName: (item.dn && item.dn.substring(5)) || null,
      };

      if (item.dv) {
        let notification = null;
        try {
          notification = JSON.parse(item.dv);
        } catch (err) {
          notification = item.dv;
        }
        if (notification) {
          if (result.eventName === 'playlist') {
            result.playlistId = notification.guid || null;
          } else if (result.eventName === 'media') {
            result.assetId = notification.guid || null;
          }
          result.assetName = notification.filename || null;
          result.notificationType = notification.notificationType || null;
          result.status = notification.status || null;
          result.notificationTimestamp =
            notification.notificationTimestamp ||
            (item.ts && moment(item.ts).format('YYYY-MM-DD HH:mm:ss.SSS')) ||
            null;
          if (notification.additionalInformation) {
            result.additionalInformation = notification.additionalInformation;
          }
        }
      }
      const site = authorizedSites.find(s => s.siteId === item.siteId);
      if (site) {
        result.siteName = site.siteName;
      }
      if (stringifier) {
        stringifier.write(result);
      }
      return result;
    });
  }

  return results;
}

module.exports = {
  async getReport(req, res, next) {
    try {
      const p = await getParams(req);
      let results = [];
      if (p.devices && p.devices.length > 0) {
        if (anyUnauthorizedDevices(p.authorizedSites, p.devices)) {
          return next(
            new restify.NotFoundError(
              'One or more specified devices has not been found.'
            )
          );
        }
        results = await getDevicesRecords(p.devices, p.start, p.end);
      } else if (p.sites && p.sites.length > 0) {
        if (anyUnauthorizedSites(p.authorizedSites, p.sites)) {
          return next(
            new restify.NotFoundError(
              'One or more specified sites has not been found.'
            )
          );
        }
        results = await getSiteRecords(p.sites, p.start, p.end);
      } else {
        results = await getCompanyRecords(p.companyId, p.start, p.end);
      }
      results = transformData(results, p.companyId, p.authorizedSites);

      res.send({ results });
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
  async getReportCSV(req, res, next) {
    try {
      const p = await getParams(req);
      const stringifier = stringify({
        delimiter: ',',
        endLine: '\n',
        columns: [
          'companyId',
          'siteId',
          'siteName',
          'deviceId',
          'eventName',
          'assetId',
          'assetName',
          'playlistId',
          'notificationType',
          'status',
          'notificationTimestamp',
          'additionalInformation',
        ],
        escapeChar: '"',
        enclosedChar: '"',
        header: true,
      });
      stringifier.pipe(res);
      let results = [];
      if (p.devices && p.devices.length > 0) {
        if (anyUnauthorizedDevices(p.authorizedSites, p.devices)) {
          return next(
            new restify.NotFoundError(
              'One or more specified devices has not been found.'
            )
          );
        }
        results = await getDevicesRecords(p.devices, p.start, p.end);
      } else if (p.sites && p.sites.length > 0) {
        if (anyUnauthorizedSites(p.authorizedSites, p.sites)) {
          return next(
            new restify.NotFoundError(
              'One or more specified sites has not been found.'
            )
          );
        }
        results = await getSiteRecords(p.sites, p.start, p.end);
      } else {
        results = await getCompanyRecords(p.companyId, p.start, p.end);
      }
      transformData(results, p.companyId, p.authorizedSites, stringifier);
      stringifier.end();
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },
};
