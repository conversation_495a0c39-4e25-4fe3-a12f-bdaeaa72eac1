const uuid = require('uuid/v4');

/**
 * Prompt Set Service
 * @param {*} promptsetRepository
 * @returns
 */
function promptsetService(promptsetRepository) {
  /**
   * Get List of Prompts
   * @param {UUID} promptsetId
   * @returns {Array<Prompt>}
   */
  const getAllPromptsByPromptSetId = promptsetId =>
    promptsetRepository.getPrompts(promptsetId);

  /**
   * Get Language Enabled for a company
   * TODO: Move this service under company directory.
   * @param {UUID} companyId
   * @returns
   */
  const getCompanyLanguagesById = companyId =>
    promptsetRepository.getCompanyLanguages(companyId);

  /**
   * Create Language Support entry
   * @param {Object} connection
   * @param {Array<Language>} languages
   * @param {UUID} userId
   * @param {UUID} promptSetId
   * @returns
   */
  const createPromptSetLanguageSupportIds = (
    connection,
    languages,
    userId,
    promptSetId
  ) => {
    const prepareJson = languages.map(lang => ({
      prompt_set_language_support_id:
        lang.prompt_set_language_support_id || uuid(),
      created_by: userId,
      default: lang.default,
      deleted: false,
      type: lang.type,
      size: lang.size,
      language_support_id: lang.languageSupportId,
      prompt_set_id: promptSetId,
    }));

    return promptsetRepository
      .createLanguageSupportIds(connection, prepareJson)
      .then(result => result.rows);
  };

  const updatePromptSetLanguageSupportIds = async (
    connection,
    languages,
    userId,
    promptSetId
  ) => {
    const prepareJson = languages.map(lang => ({
      prompt_set_language_support_id: lang.prompt_set_language_support_id,
      created_by: userId,
      default: lang.default,
      deleted: lang.deleted,
      type: lang.type,
      size: lang.size,
      language_support_id: lang.languageSupportId,
      prompt_set_id: promptSetId,
    }));

    return Promise.all(
      prepareJson.map(lang =>
        promptsetRepository.updateLanguageSupportId(connection, lang)
      )
    );
  };

  /**
   * Get Prompt-Set Languages
   * @param {UUID} promptSetId
   * @param {Object} connection optional
   * @returns
   */
  const getPromptSetLanguageById = async (
    promptSetId,
    returnInternalId = false,
    connection = undefined
  ) => {
    // TODO: Refactor repository and service to optimize the SQL query efficiency.
    const res = await promptsetRepository.getPromptSetLanguages(
      promptSetId,
      connection
    );
    return res.map(i => {
      if (!returnInternalId) {
        // eslint-disable-next-line no-param-reassign
        delete i.promptSetLanguageSupportId;
      }
      return i;
    });
  };

  return {
    getAllPromptsByPromptSetId,
    getCompanyLanguagesById,
    getPromptSetLanguageById,
    createPromptSetLanguageSupportIds,
    updatePromptSetLanguageSupportIds,
  };
}

module.exports = promptsetService;
