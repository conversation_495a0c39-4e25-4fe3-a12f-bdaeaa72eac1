const _ = require('lodash');
const co = require('co');
const restify = require('restify');
const request = require('request');
const uuid = require('uuid/v4');
const { get } = require('lodash');

const { server } = require('../../app');
const helper = require('../../helpers/promptset-helper');
const errorHandler = require('../../lib/errorhandler');
const {
  bumpMinorVersion,
  validate,
  stripProperties,
} = require('../../helpers/promptset-helper');
const deviceHelper = require('../../helpers/device-helper');
const {
  promptService,
  promptstateService,
  promptsetService,
} = require('../../src/media-mgmt/imports.service');
const errors = require('../../src/media-mgmt/errors');

function* getPromptSetByPromptId(promptId) {
  return yield server.db.read.row(
    `
        SELECT 
            pset.id, 
            pset.version, 
            pstate.numeric_input, 
            pstate.dynamic_text,
            pstate.dynamic_text, 
            pstate.allow_video, 
            pstate.soft_keys, 
            pstate.secure,
            dt.screen_width,
            dt.screen_height
        FROM
            prompt_set pset
        JOIN prompt p ON p.prompt_set = pset.id
        JOIN prompt_state pstate ON p.prompt_state = pstate.id
        INNER JOIN product_type dt ON pset.device_type = dt.device_type
        WHERE
            p.id = $1
    `,
    [promptId]
  );
}

module.exports = {
  updatePrompt: (req, res, next) =>
    co(function* execute() {
      const { user } = req;
      const promptId = req.params.id;
      const companyId = req.user.company.id;
      const { softkeys } = req.body;
      const elements = stripProperties(req.body.elements);
      const touchmapRequest = req.body.touchmap;
      const { transactionState } = req.body;

      const prompt = yield server.db.read.row(
        `
                SELECT p.device_type, COUNT(s.id) as "softkeyAssignments"
                FROM prompt p
                    LEFT JOIN softkey_assignments s ON s.prompt = p.id
                WHERE
                    p.id = $1 AND
                    p.company = $2
                GROUP BY p.device_type;
            `,
        [promptId, companyId]
      );

      if (!prompt) {
        return next(new restify.NotFoundError('Prompt not found'));
      }

      const promptSet = yield getPromptSetByPromptId(promptId);
      if (!promptSet) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      if (!promptSet.softKeys) {
        if (softkeys && softkeys.length > 0) {
          return next(
            new restify.errors.BadRequestError(
              'Prompt type does not allow softkeys but assignment was found'
            )
          );
        }
      }

      const isG6100orG6200 = deviceHelper.isG6100orG6200(prompt.deviceType);
      const isG7100orG6300 = deviceHelper.isG7100orG6300(prompt.deviceType);

      if (!isG6100orG6200 && !isG7100orG6300) {
        return next(
          new restify.errors.ConflictError(
            `Device type ${prompt.deviceType} is not supported`
          )
        );
      }

      if (isG6100orG6200 && touchmapRequest) {
        return next(
          new restify.errors.BadRequestError(
            'Device does not support touchmaps'
          )
        );
      }

      let touchmap = null;
      if (touchmapRequest && touchmapRequest.id) {
        touchmap = yield server.db.read.row(
          `
                    SELECT id FROM touchmap WHERE id = $1 AND company_id = $2;
                `,
          [touchmapRequest.id, req.user.company.id]
        );

        if (!touchmap) {
          return next(new restify.errors.NotFoundError('Touchmap not found'));
        }
      }

      try {
        yield validate(elements, prompt, promptSet, {
          width: promptSet.screenWidth,
          height: promptSet.screenHeight,
        });
      } catch (err) {
        req.log.error({ err });
        if (err instanceof restify.errors.BadRequestError) {
          return next(err);
        }
        return next(
          new restify.errors.InternalServerError(
            'Unable to validate prompt elements'
          )
        );
      }

      let bumpedUpPromptSetVersion = null;
      let updatedPromptSet = null;

      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');

        yield connection.execute(
          `
                    UPDATE prompt
                    SET elements = $1, touchmap_id = $2, transaction_state = $3
                    WHERE id = $4;
                `,
          [
            JSON.stringify(elements),
            touchmap ? touchmap.id : null,
            transactionState || null,
            promptId,
          ]
        );

        // clear all softkeys first
        yield connection.execute(
          `
                    DELETE FROM softkey_assignments
                    WHERE prompt = $1;
                `,
          [promptId]
        );

        // add new softkeys
        yield Promise.all(
          softkeys.map(softkeyAssignment => {
            const softkeyAssignmentId = uuid();
            return connection.execute(
              `
                        INSERT INTO softkey_assignments (id, prompt, softkey, label, font_color, font_size, keycode)
                        VALUES ( $1, $2, $3, $4, $5, $6, $7 );`,
              [
                softkeyAssignmentId,
                promptId,
                softkeyAssignment.softkey,
                softkeyAssignment.label,
                softkeyAssignment.fontColor,
                softkeyAssignment.fontSize,
                softkeyAssignment.keycode,
              ]
            );
          })
        );

        bumpedUpPromptSetVersion = bumpMinorVersion(promptSet.version);
        const { 0: firstRow } = (yield connection.execute(
          `
                      UPDATE prompt_set SET
                          version = $1,
                          modified = NOW(),
                          modified_by = $2
                      WHERE id = $3
                      RETURNING modified;
                  `,
          [bumpedUpPromptSetVersion, user.sub, promptSet.id]
        )).rows;
        updatedPromptSet = firstRow;

        yield connection.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        return next(
          new restify.errors.InternalServerError(
            'Failed trying to update prompt elements'
          )
        );
      } finally {
        connection.done();
      }

      res.send(200, {
        version: bumpedUpPromptSetVersion,
        modified: updatedPromptSet.modified,
        modifiedBy: {
          id: user.sub,
          name: user.fullName,
          email: user.email,
        },
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),
  deletePrompt: (req, res, next) =>
    co(function* execute() {
      const promptId = req.params.id;
      const { user } = req;

      const userId = get(user, 'sub', null);
      if (!userId) {
        return next(new restify.NotFoundError(errors.user.notFound));
      }

      const prompt = yield promptService.getPromptById(promptId);
      if (!prompt) {
        return next(new restify.NotFoundError(errors.prompt.notFound));
      }

      if (prompt.deleted)
        return next(new restify.BadRequestError(errors.prompt.alreadyDeleted));

      const promptCompany = get(prompt, 'company', null);
      if (promptCompany !== user.company.id) {
        return next(new restify.UnauthorizedError(errors.user.accessDenied));
      }

      const promptSet = yield promptsetService.getPromptSetById(
        prompt.promptSet
      );
      if (!promptSet) {
        return next(new restify.NotFoundError(errors.promptSet.notFound));
      }

      if (!_.isEmpty(prompt.promptState)) {
        return next(
          new restify.BadRequestError(errors.prompt.deleteDefaultPromptErr)
        );
      }

      let bumpedUpPromptSetVersion = null;
      let updatedPromptSet = null;

      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');

        yield connection.execute(
          `
                    UPDATE prompt SET
                        deleted = true
                        , deleted_date = now()
                        , deleted_by = $1
                    WHERE id = $2
                `,
          [userId, promptId]
        );

        // Unlinking prompts
        yield connection.execute(
          `
                    UPDATE prompt
                    SET aux_prompt = $1
                    WHERE aux_prompt = $2
                `,
          [null, promptId]
        );

        bumpedUpPromptSetVersion = bumpMinorVersion(promptSet.version);
        const { 0: firstRow } = (yield connection.execute(
          `
                    UPDATE prompt_set SET
                        version = $1,
                        modified = NOW(),
                        modified_by = $2
                    WHERE id = $3
                    RETURNING modified;
                `,
          [bumpedUpPromptSetVersion, req.user.sub, prompt.promptSet]
        )).rows;
        updatedPromptSet = firstRow;

        yield connection.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        return next(
          new restify.errors.InternalServerError('Failed to delete prompt')
        );
      } finally {
        connection.done();
      }

      res.send(200, {
        version: bumpedUpPromptSetVersion,
        modified: updatedPromptSet.modified,
        modifiedBy: {
          id: req.user.sub,
          name: req.user.fullName,
          email: req.user.email,
        },
      });
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  toODML: (req, res, next) =>
    co(function* execute() {
      const assets = req.body;

      // make sure beautify is a boolean
      let beautified = req.query.beautify || false;
      if (typeof beautified === 'string') {
        beautified = beautified === 'true';
      }

      let companyClause;
      let queryParams;

      // Allow System Users to get assets regardless of company
      if (req.user.roles.includes('ICS_SYSTEM')) {
        companyClause = '';
        queryParams = [req.params.id];
      } else {
        companyClause = 'AND p.company = $2';
        queryParams = [req.params.id, req.user.company.id];
      }

      const prompt = yield server.db.read.row(
        `
                SELECT p.id, p.prompt_state, p.elements, p.device_type
                FROM prompt p
                WHERE
                    p.id = $1 ${companyClause};
            `,
        queryParams
      );

      if (!prompt) {
        return next(new restify.NotFoundError(errors.prompt.unknownNotFound));
      }

      const state = yield promptstateService.getPromptStateByPrompt(prompt);

      prompt.secure = state.secure;

      let odml = null;
      try {
        if (
          deviceHelper.isG6100orG6200(prompt.deviceType) ||
          deviceHelper.isG7100orG6300(prompt.deviceType)
        ) {
          odml = helper.parseElementsToODML(
            prompt.deviceType,
            prompt.elements,
            assets,
            beautified,
            { secure: prompt.secure }
          );
        } else {
          return next(
            new restify.errors.ConflictError(errors.deviceType.notSupported)
          );
        }
      } catch (err) {
        req.log.error({ err });
        if (err instanceof restify.errors.BadRequestError) {
          return next(err);
        }
        return next(
          new restify.errors.InternalServerError(
            'Failed to parse prompt elements'
          )
        );
      }

      res.send(200, odml);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  validate: (req, res, next) =>
    co(function* execute() {
      const promptId = req.params.id;

      const queryParams = [];
      queryParams.push(promptId);

      const isSystemUser =
        req.user && req.user.roles && req.user.roles.includes('ICS_SYSTEM');
      if (!isSystemUser) {
        queryParams.push(req.user.company.id);
      }

      const prompt = yield server.db.read.row(
        `
                SELECT p.device_type, p.elements, COALESCE(t.areas, '[]') as areas, COUNT(s.id) as softkey_assignments
                FROM prompt p
                    LEFT JOIN touchmap t ON t.id = p.touchmap_id
                    LEFT JOIN softkey_assignments s ON s.prompt = p.id
                WHERE
                    p.id = $1 
                    ${isSystemUser ? '' : 'AND p.company = $2'}
                GROUP BY p.device_type, p.elements, t.areas;
            `,
        queryParams
      );

      if (!prompt) {
        return next(new restify.NotFoundError('Prompt not found'));
      }

      const promptSet = yield getPromptSetByPromptId(promptId);
      if (!promptSet) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      try {
        let elements = null;

        if (deviceHelper.isG6100orG6200(prompt.deviceType)) {
          elements = prompt.elements;
        } else if (deviceHelper.isG7100orG6300(prompt.deviceType)) {
          elements = [...prompt.elements, ...prompt.areas];
        } else {
          return next(
            new restify.errors.ConflictError(
              `Device type ${prompt.deviceType} is not supported`
            )
          );
        }

        yield validate(elements, prompt, promptSet, {
          width: promptSet.screenWidth,
          height: promptSet.screenHeight,
        });
      } catch (err) {
        req.log.error({ err });
        if (err instanceof restify.errors.BadRequestError) {
          return next(err);
        }
        return next(
          new restify.errors.InternalServerError(
            'Unable to validate prompt elements'
          )
        );
      }

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  // !!!
  // THIS SERVICE IS DEPRECATED IN FAVOUR OF `updatePrompts`
  // PLEASE SEE `PUT /promptsets/:id/prompts`
  // !!!
  updateElements: (req, res, next) =>
    co(function* execute() {
      const promptId = req.params.id;
      const companyId = req.user.company.id;
      const elements = stripProperties(req.body);

      const prompt = yield server.db.read.row(
        `
                SELECT p.device_type
                FROM prompt p
                WHERE
                    p.id = $1 AND
                    p.company = $2;
            `,
        [promptId, companyId]
      );

      if (!prompt) {
        return next(new restify.NotFoundError('Prompt not found'));
      }

      const promptSet = yield getPromptSetByPromptId(promptId);
      if (!promptSet) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      try {
        if (
          deviceHelper.isG6100orG6200(prompt.deviceType) ||
          deviceHelper.isG7100orG6300(prompt.deviceType)
        ) {
          yield validate(elements, prompt, promptSet, {
            width: promptSet.screenWidth,
            height: promptSet.screenHeight,
          });
        } else {
          return next(
            new restify.errors.ConflictError(
              `Device type ${prompt.deviceType} is not supported`
            )
          );
        }
      } catch (err) {
        req.log.error({ err });
        if (err instanceof restify.errors.BadRequestError) {
          return next(err);
        }
        return next(
          new restify.errors.InternalServerError(
            'Unable to validate prompt elements'
          )
        );
      }

      let bumpedUpPromptSetVersion = null;
      let updatedPromptSet = null;

      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');

        yield connection.execute(
          `
                    UPDATE prompt
                    SET elements = $1
                    WHERE id = $2
                `,
          [JSON.stringify(elements), promptId]
        );

        bumpedUpPromptSetVersion = bumpMinorVersion(promptSet.version);
        const { 0: firstRow } = (yield connection.execute(
          `
                      UPDATE prompt_set SET
                          version = $1,
                          modified = NOW(),
                          modified_by = $2
                      WHERE id = $3
                      RETURNING modified;
                  `,
          [bumpedUpPromptSetVersion, req.user.sub, promptSet.id]
        )).rows;
        updatedPromptSet = firstRow;

        yield connection.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        return next(
          new restify.errors.InternalServerError(
            'Failed trying to update prompt elements'
          )
        );
      } finally {
        connection.done();
      }

      res.send(200, {
        version: bumpedUpPromptSetVersion,
        modified: updatedPromptSet.modified,
        modifiedBy: {
          id: req.user.sub,
          name: req.user.fullName,
          email: req.user.email,
        },
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  removeTouchmap: (req, res, next) =>
    co(function* execute() {
      const promptId = req.params.id;
      const companyId = req.user.company.id;

      const prompt = yield server.db.read.row(
        `
                SELECT p.id FROM prompt p
                WHERE
                    p.id = $1 AND
                    p.company = $2;
            `,
        [promptId, companyId]
      );

      if (!prompt) {
        return next(new restify.NotFoundError('Prompt not found'));
      }

      const promptSet = yield getPromptSetByPromptId(promptId);
      if (!promptSet) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      let bumpedUpPromptSetVersion = null;
      let updatedPromptSet = null;

      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');

        yield connection.execute(
          `
                    UPDATE prompt
                    SET touchmap_id = NULL
                    WHERE id = $1;
                `,
          [promptId]
        );

        bumpedUpPromptSetVersion = bumpMinorVersion(promptSet.version);
        const { 0: firstRow } = (yield connection.execute(
          `
                    UPDATE prompt_set SET
                        version = $1,
                        modified = NOW(),
                        modified_by = $2
                    WHERE id = $3
                    RETURNING modified;
                `,
          [bumpedUpPromptSetVersion, req.user.sub, promptSet.id]
        )).rows;
        updatedPromptSet = firstRow;

        yield connection.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        return next(
          new restify.errors.InternalServerError(
            'Failed to remove touchmap from prompt'
          )
        );
      } finally {
        connection.done();
      }

      res.send(200, {
        version: bumpedUpPromptSetVersion,
        modified: updatedPromptSet.modified,
        modifiedBy: {
          id: req.user.sub,
          name: req.user.fullName,
          email: req.user.email,
        },
      });
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getThumbnail: (req, res, next) =>
    co(function* execute() {
      const prompt = yield server.db.read.row(
        `SELECT 
                    p.thumbnail_url
                FROM prompt p
                WHERE p.id = $1
                `,
        [req.params.id]
      );

      if (!prompt) {
        return next(
          new restify.NotFoundError(`Prompt ${req.params.id} not found`)
        );
      }

      if (!prompt.thumbnailUrl) {
        return next(new restify.NotFoundError('Promptset has no thumbnail'));
      }

      try {
        request.get(prompt.thumbnailUrl).pipe(res);
      } catch (err) {
        return next(new restify.NotFoundError('Invalid thumbnail URL'));
      }

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  setThumbnailUrl: (req, res, next) =>
    co(function* execute() {
      const promptId = req.params.id;
      const { thumbnailUrl } = req.body;

      // NOTE: This API doesn't check company relationships because it's system user only
      const prompt = yield server.db.read.row(
        'SELECT id FROM prompt WHERE id = $1',
        [promptId]
      );
      if (!prompt) {
        return next(new restify.NotFoundError('Prompt not found'));
      }

      yield server.db.write.execute(
        'UPDATE prompt SET thumbnail_url = $1 WHERE id = $2',
        [thumbnailUrl, promptId]
      );

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
