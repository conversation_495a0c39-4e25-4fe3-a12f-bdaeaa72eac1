const co = require('co');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');

module.exports = {
  getKeycodes: (req, res, next) =>
    co(function* execute() {
      const results = yield server.db.read.rows(
        'SELECT k.code, k.name FROM keycodes as k ORDER BY k.code'
      );
      res.send(results);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
