const co = require('co');
const uuid = require('uuid/v4');
const restify = require('restify');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');

module.exports = {
  // -----------
  // GET All States
  // -----------
  getAllStates: (req, res, next) =>
    co(function* execute() {
      const pageParams = paginationHelper.parsePaginationParams(req);

      const { q } = req.params;

      let where = 'WHERE active = true';
      let params = [];

      if (q) {
        where += ` AND (LOWER( ps.name ) LIKE LOWER('%'||$1||'%')
                            OR LOWER( ps.description ) LIKE LOWER('%'||$1||'%')
                            OR LOWER( ps.code ) LIKE LOWER('%'||$1||'%'))`;
        params = [q];
      }

      const countQuery = `SELECT COUNT(1) from prompt_state ps ${where}`;
      const totalObj = yield server.db.read.row(countQuery, params);

      const query = `
                SELECT
                    ps.id,
                    ps.code,
                    ps.description,
                    ps.secure,
                    ps.numeric_input,
                    ps.dynamic_text,
                    ps.soft_keys,
                    ps.sequence,
                    ps.attribs,
                    ps.name,
                    ps.example_text
                    FROM prompt_state ps
                    ${where}
                    LIMIT ${pageParams.pageSize}
                    OFFSET ${pageParams.offset};
            `;
      const result = yield server.db.read.rows(query, params);

      const output = {
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results: result,
      };

      res.send(output);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  // -----------
  // CREATE State
  // -----------
  createState: (req, res, next) =>
    co(function* execute() {
      const promptState = req.body;
      const id = uuid();

      const promptTemplate = yield server.db.read.row(
        `SELECT * FROM prompt_template
                WHERE id = $1
                `,
        [promptState.promptTemplateId]
      );

      if (!promptTemplate) {
        return next(new restify.NotFoundError('PromptTemplate not found'));
      }

      const defaults = {
        exampleText: null,
        name: null,
        description: '',
        secure: false,
        numericInput: false,
        dynamicText: false,
        softKeys: false,
        sequence: 0,
        attribs: [],
      };

      const filledPromptState = Object.assign(defaults, promptState);

      const createdPromptState = yield server.db.write.row(
        `
                INSERT INTO prompt_state( id, prompt_template, code, name, description, secure, numeric_input, dynamic_text, soft_keys,
                    sequence, attribs, example_text )
                VALUES
                    ( $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12 )
                RETURNING *
            `,
        [
          id,
          filledPromptState.promptTemplateId,
          filledPromptState.code,
          filledPromptState.name,
          filledPromptState.description,
          filledPromptState.secure,
          filledPromptState.numericInput,
          filledPromptState.dynamicText,
          filledPromptState.softKeys,
          filledPromptState.sequence,
          JSON.stringify(filledPromptState.attribs),
          filledPromptState.exampleText,
        ]
      );

      res.send(createdPromptState);

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  // -----------
  // EDIT States
  // -----------
  editState: (req, res, next) =>
    co(function* execute() {
      // get the old state
      const oldState = yield server.db.read.row(
        `
                SELECT id, name, code, description, secure, numeric_input,
                    dynamic_text, soft_keys, sequence, attribs, example_text
                FROM prompt_state WHERE id = $1 AND active = true
            `,
        [req.params.id]
      );

      if (!oldState) {
        return next(new restify.NotFoundError('State not found'));
      }

      // overwrite to old state
      const newState = Object.assign(oldState, req.body);

      yield server.db.write.row(
        `
                UPDATE prompt_state SET
                    name = $1,
                    code = $2,
                    description = $3,
                    secure = $4,
                    numeric_input = $5,
                    dynamic_text = $6,
                    soft_keys = $7,
                    sequence = $8,
                    attribs = $9,
                    example_text = $10
                WHERE id = $11
                RETURNING *
            `,
        [
          newState.name,
          newState.code,
          newState.description,
          newState.secure,
          newState.numericInput,
          newState.dynamicText,
          newState.softKeys,
          newState.sequence,
          JSON.stringify(newState.attribs),
          newState.exampleText,
          newState.id,
        ]
      );

      res.send(newState);

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  // -----------
  // DELETE State
  // -----------
  deleteState: (req, res, next) =>
    co(function* execute() {
      // check if state exists and active
      const promptState = yield server.db.read.row(
        `
                SELECT * FROM prompt_state WHERE
                    id = $1 AND active = true;
            `,
        [req.params.id]
      );

      if (!promptState) {
        return next(new restify.NotFoundError('State not found'));
      }

      yield server.db.write.execute(
        `
                UPDATE prompt_state SET active = false
                WHERE id = $1
            `,
        [req.params.id]
      );

      res.send(204);

      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
