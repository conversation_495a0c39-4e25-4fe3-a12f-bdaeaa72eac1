const co = require('co');
const uuid = require('uuid/v4');
const _ = require('lodash');
const restify = require('restify');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const prompttemplateHelper = require('../../helpers/prompttemplate-helper');

module.exports = {
  /**
   * Get Prompt State by Prompt Template ID
   */
  getPromptTemplateStateByID: (req, res, next) => {
    const promptTemplateId = req.params.id;
    const companyId = req.user.company.id;
    return co(function* execute() {
      const promptStates = yield prompttemplateHelper.getPromptTemplateStates(
        promptTemplateId,
        companyId
      );
      res.send(promptStates);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  /**
   * Get All Prompt Templates
   */
  getAllPromptTemplates: (req, res, next) => {
    const companyId = req.user.company.id;
    return co(function* execute() {
      const query = `
                SELECT
                    pt.id ,
                    pt.name,
                    pt.description
                FROM
                    prompt_template pt
                JOIN
                    company_prompt_template cpt
                ON
                    pt.id = cpt.prompt_template
                WHERE
                    cpt.company = $1;
            `;
      const result = yield server.db.read.rows(query, [companyId]);
      res.send(result);
      return next();
    }).catch(err => {
      server.log.error(err);
      throw err;
    });
  },

  /**
   * Create a Prompt Template
   */
  createPromptTemplate: (req, res, next) =>
    co(function* execute() {
      const promptTemplate = req.body;
      const id = uuid();

      // validate states
      if (promptTemplate.states && promptTemplate.states.length > 0) {
        const whereClause = _.map(
          promptTemplate.states,
          (state, index) => `$${index + 1}`
        ).join(',');
        const whereParams = _.map(promptTemplate.states, 'id');
        const states = yield server.db.read.rows(
          `SELECT id, prompt_template
                    FROM prompt_state ps
                    WHERE id in ( ${whereClause} )`,
          whereParams
        );

        if (states.length !== promptTemplate.states.length) {
          return next(new restify.NotFoundError('Prompt State not found'));
        }
      }

      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');

        yield connection.execute(
          `INSERT INTO prompt_template( id, name, description, default_bg_color )
                    VALUES ( $1, $2, $3, $4 )`,
          [
            id,
            promptTemplate.name,
            promptTemplate.description,
            promptTemplate.defaultBg,
          ]
        );

        // Create state - prompttemplate relationship
        // eslint-disable-next-line no-restricted-syntax
        for (const state of promptTemplate.states) {
          yield connection.execute(
            `UPDATE prompt_state
                        SET prompt_template = $1
                        WHERE id = $2;`,
            [id, state.id]
          );
        }

        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error(err);

        yield connection.execute('ROLLBACK');
        connection.done();
        return next(new restify.InternalServerError(err));
      }

      // requery prompttemplates object
      const createdPromptTemplate = yield server.db.read.row(
        `SELECT
                id,
                name,
                description,
                default_bg_color as default_bg
                FROM prompt_template pt
                WHERE id=$1`,
        [id]
      );

      const promptStates = yield server.db.read.rows(
        `SELECT
                id,
                code,
                description,
                secure,
                numeric_input,
                dynamic_text,
                soft_keys,
                sequence,
                attribs
                FROM prompt_state as ps
                WHERE ps.prompt_template=$1 ORDER BY ps.sequence ASC`,
        [id]
      );
      createdPromptTemplate.states = promptStates;

      res.send(createdPromptTemplate);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Edit the name, description and defaultBg of a prompttemplate
   */
  editPromptTemplate: (req, res, next) =>
    co(function* execute() {
      const promptTemplateId = req.params.id;
      const promptTemplateParams = req.body;

      const oldPromptTemplate = yield server.db.read.row(
        `
                SELECT * FROM prompt_template WHERE id = $1
            `,
        [promptTemplateId]
      );

      if (!oldPromptTemplate) {
        return next(new restify.NotFoundError('PromptTemplate not found'));
      }

      const newPromptTemplate = Object.assign(
        oldPromptTemplate,
        promptTemplateParams
      );

      yield server.db.write.execute(
        `
                UPDATE prompt_template SET
                    name = $1,
                    description = $2,
                    default_bg_color = $3
                WHERE id = $4
            `,
        [
          newPromptTemplate.name,
          newPromptTemplate.description,
          newPromptTemplate.defaultBg,
          promptTemplateId,
        ]
      );

      res.send(newPromptTemplate);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Assign a prompttemplate to a company
   */
  assignToCompany: (req, res, next) =>
    co(function* execute() {
      const promptTemplateId = req.params.id;
      const { companyId } = req.params;

      const promptTemplate = yield server.db.read.row(
        `
                SELECT * FROM prompt_template WHERE id = $1
            `,
        [promptTemplateId]
      );

      if (!promptTemplate) {
        return next(new restify.NotFoundError('Prompt Template not found'));
      }

      const company = yield server.db.read.row(
        `
                SELECT * FROM company where id = $1
            `,
        [companyId]
      );

      if (!company) {
        return next(new restify.NotFoundError('Company not found'));
      }

      const promptTemplateCompanyAssignment = yield server.db.read.row(
        `
                SELECT * FROM company_prompt_template WHERE company = $1 AND prompt_template = $2
            `,
        [companyId, promptTemplateId]
      );

      if (!promptTemplateCompanyAssignment) {
        yield server.db.write.execute(
          `
                    INSERT INTO company_prompt_template( company, prompt_template )
                        VALUES( $1, $2 )
                `,
          [companyId, promptTemplateId]
        );
      }

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Unassign a prompttemplate to a company
   */
  unassignFromCompany: (req, res, next) =>
    co(function* execute() {
      const promptTemplateId = req.params.id;
      const { companyId } = req.params;

      // TODO: there is code duplication with assignFromCompany

      const promptTemplate = yield server.db.read.row(
        `
                SELECT * FROM prompt_template WHERE id = $1
            `,
        [promptTemplateId]
      );

      if (!promptTemplate) {
        return next(new restify.NotFoundError('Prompt Template not found'));
      }

      const company = yield server.db.read.row(
        `
                SELECT * FROM company where id = $1
            `,
        [companyId]
      );

      if (!company) {
        return next(new restify.NotFoundError('Company not found'));
      }

      const promptTemplateCompanyAssignment = yield server.db.read.row(
        `
                SELECT * FROM company_prompt_template WHERE company = $1 AND prompt_template = $2
            `,
        [companyId, promptTemplateId]
      );

      if (!promptTemplateCompanyAssignment) {
        return next(
          new restify.NotFoundError(
            'PromptTemplate not assigned with this company'
          )
        );
      }

      yield server.db.write.execute(
        `
                DELETE FROM company_prompt_template
                    WHERE company = $1 AND prompt_template = $2
            `,
        [companyId, promptTemplateId]
      );

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
