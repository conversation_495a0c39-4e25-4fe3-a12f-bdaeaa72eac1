const { server } = require('../../app');

/**
 * Return all prompts in a promptset, order by prompt state code
 * @param {UUID} promptsetId
 * @returns {Array<Prompt>}
 */
async function getPrompts(promptsetId) {
  const query = `
  SELECT p.id as id, coalesce(pso.code, pss.code) as code, p.elements as elements
  FROM prompt p
  LEFT JOIN prompt_state pss
      on p.prompt_state = pss.id
  LEFT JOIN prompt_state_override pso
      on p.id = pso.prompt_id
  WHERE p.prompt_set = ($1)::uuid
  AND p.deleted = false
  ORDER BY code;
`;
  try {
    return await server.db.read.rows(query, [promptsetId]);
  } catch (err) {
    server.log.debug(
      {
        query,
        params: { promptsetId },
      },
      `getPrompts error`
    );
    throw err;
  }
}

/**
 * Get List of Company Languages
 * @param {UUID} companyId
 * @returns {Array<Language>}
 */
async function getCompanyLanguages(companyId) {
  // eslint-disable-next-line no-return-await
  return await server.db.read.rows(
    `
        SELECT language_support_id, company_id, language, iso_code, "default", deleted 
        FROM language_support 
        WHERE deleted = false AND company_id = ($1)::uuid;
    `,
    [companyId]
  );
}

/**
 *
 * @param {Object} connection
 * @param {Array<Language>} langs
 * @returns {Array<PromptSetLanguage>}
 */
async function createLanguageSupportIds(connection, langs) {
  // eslint-disable-next-line no-return-await
  return await connection.execute(
    `
        INSERT INTO prompt_set_language_support
            SELECT prompt_set_language_support_id,language_support_id,prompt_set_id,type,size,deleted,"default",created_by
            FROM jsonb_populate_recordset(NULL::prompt_set_language_support, $1::jsonb)
        RETURNING *;
    `,
    [JSON.stringify(langs)]
  );
}

/**
 *
 * @param {Object} connection
 * @param {Language} lang
 */
async function updateLanguageSupportId(connection, lang) {
  // eslint-disable-next-line no-return-await
  return await connection.execute(
    `
        UPDATE prompt_set_language_support
            SET type = $2, size = $3, deleted = $4, "default" = $5
            WHERE prompt_set_language_support_id = $1;
    `,
    [
      lang.prompt_set_language_support_id,
      lang.type,
      lang.size,
      lang.deleted,
      lang.default,
    ]
  );
}

/**
 * Get Prompt Set Language Support entries.
 * @param {UUID} promptSetId
 * @param {Object} connection optional
 * @returns {Array<PromptSetLanguage>}
 */
async function getPromptSetLanguages(promptSetId, connection) {
  const prepareQuery = `
        SELECT psls.prompt_set_language_support_id, ls.language_support_id, ls.language, ls.iso_code,
            (select row_to_json(_) from (select psls.type,psls.size, psls."default",psls.deleted) as _) as prompt_set_language_support
        FROM prompt_set_language_support psls
            INNER JOIN language_support ls on psls.language_support_id = ls.language_support_id
        WHERE psls.prompt_set_id = ($1)::uuid
        AND psls.deleted = false
        GROUP BY ls.language_support_id, psls.prompt_set_language_support_id;
    `;

  if (connection) {
    return (await connection.execute(prepareQuery, [promptSetId])).rows;
  }

  // eslint-disable-next-line no-return-await
  return await server.db.read.rows(prepareQuery, [promptSetId]);
}

async function getPromptSetProfiles(pageSize, pageIndex, companyId) {
  return server.db.read.rows(
    `
      SELECT 
      p.name AS id,
      p.ux_label AS label,
      p.main_resolutions,
      p.aux_resolutions,
      p.sec_model
      FROM company_product_type as c
      JOIN prompt_set_profile as p
      ON c.device_type = p.legacy_device_type
      WHERE c.company = $3
      ORDER BY p.ux_label
      LIMIT $1 OFFSET $2;
    `,
    [pageSize, pageIndex, companyId]
  );
}

module.exports = {
  getPrompts,
  getCompanyLanguages,
  getPromptSetLanguages,
  createLanguageSupportIds,
  updateLanguageSupportId,
  getPromptSetProfiles,
};
