const co = require('co');
const restify = require('restify');
const Joi = require('joi');
const _ = require('lodash');
const uuid = require('uuid/v4');

const paginationHelper = require('../../helpers/pagination-helper');
const errorHandler = require('../../lib/errorhandler');
const { server } = require('../../app');

// Validate that toucharea shapes have valid coordinates
function isValidCoords(areas) {
  return _.every(areas, area => {
    if (area.shape === 'circle') {
      return area.coords.split(',').length === 3;
    }
    if (area.shape === 'rect') {
      return area.coords.split(',').length === 4;
    }
    return false;
  });
}

function stripProperties(areas) {
  return areas.map(
    area =>
      Joi.validate(
        area,
        Joi.object().keys({
          id: Joi.string(),
          type: Joi.string().valid('area'),
          shape: Joi.string().valid('rect', 'circle'),
          coords: Joi.string(),
          keyCode: Joi.string().optional(),
          softkeyId: Joi.string().optional(),
        }),
        { presence: 'required', stripUnknown: true }
      ).value
  );
}

const minifyODML = odml =>
  odml
    .split('\n')
    .map(line => {
      let trimmed = line.trim();
      if (trimmed.length && trimmed[trimmed.length - 1] !== '>') {
        trimmed = `${trimmed} `;
      }
      return trimmed;
    })
    .join('')
    .replace(/ \/?>/g, '>');

function parseToODML(touchmap, softkeys, keycodes) {
  const elementMap = {
    area: area => {
      if (!area.keyCode && !area.softkeyId) {
        return null;
      }

      let altValue;
      if (area.softkeyId) {
        const softkey = _.find(softkeys, { id: parseInt(area.softkeyId, 10) });
        if (softkey) {
          altValue = `${softkey.internalName}_AREA`;
        }
      } else if (area.keyCode) {
        const keyCode = _.find(keycodes, { code: area.keyCode });
        if (keyCode) {
          altValue = `LC_${keyCode.code}`;
        }
      }

      if (!altValue) {
        return null;
      }

      let { coords } = area;
      if (area.shape === 'rect') {
        // rects are x1,y1,x2,y2 but we're storing it as x1,y1,w,h
        const coordValues = area.coords
          .split(',')
          .map(item => parseInt(item, 10)); // eslint-disable-line arrow-body-style
        coords = `${coordValues[0]},${coordValues[1]},${
          coordValues[0] + coordValues[2]
        },${coordValues[1] + coordValues[3]}`;
      }

      return `
                <area
                    shape='${area.shape}'
                    coords='${coords}'
                    alt='${altValue}'
                />
            `;
    },
  };

  const parsed = touchmap.areas
    .map(element => {
      const map = elementMap[element.type];
      if (!map) {
        return null;
      }
      return map(element);
    })
    .join('');

  return minifyODML(`
        <map name='${touchmap.id}'>
            ${parsed}
        </map>
    `);
}

module.exports = {
  getAllTouchmaps: (req, res, next) =>
    co(function* execute() {
      const pageParams = paginationHelper.parsePaginationParams(req);
      const { q } = req.params;
      const companyId = req.user.company.id;

      const from =
        ' FROM touchmap as tm LEFT JOIN ics_user u on u.id = tm.created_by';
      const params = [companyId];
      let where = ' WHERE tm.company_id = $1 AND active = true';

      if (q) {
        params.push(q);
        where += ` AND ( LOWER( tm.name ) LIKE LOWER('%'||$2||'%')
                            OR LOWER(tm.description) LIKE LOWER('%'||$2||'%') )
                        `;
      }

      const countQuery = `SELECT COUNT(1) ${from} ${where}`;
      const totalObj = yield server.db.read.row(countQuery, params);

      const selectQuery = `SELECT
                tm.id,
                tm.name,
                tm.areas,
                tm.description,
                json_build_object(
                    'id', u.id,
                    'fullName', u.full_name
                )
                ${from}
                ${where}
                ORDER BY tm.name ASC
                LIMIT ${pageParams.pageSize}
                OFFSET ${pageParams.offset}
            `;
      const result = yield server.db.read.rows(selectQuery, params);

      const output = {
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results: result,
      };

      res.send(output);

      return next();
    }).catch(errorHandler.onError(req, res, next)),
  getTouchmap: (req, res, next) =>
    co(function* execute() {
      const touchmapId = req.params.id;
      const companyId = req.user.company.id;

      const touchmap = yield server.db.read.row(
        `
                SELECT
                    tm.id,
                    tm.name,
                    tm.areas,
                    tm.description,
                    json_build_object(
                        'id', u.id,
                        'fullName', u.full_name
                    ) as creator
                    FROM touchmap as tm
                        LEFT JOIN ics_user u ON u.id = tm.created_by
                    WHERE tm.id = $1 AND tm.company_id = $2
            `,
        [touchmapId, companyId]
      );

      if (!touchmap) {
        return next(new restify.NotFoundError('Touchmap not found'));
      }

      // Only care about softkeys and keycodes if this touchmap has softkeys or keycodes
      if (_.some(touchmap.areas, area => area.keyCode || area.softkeyId)) {
        const keycodes = yield server.db.read.rows(
          'SELECT k.code, k.name FROM keycodes as k ORDER BY k.code'
        );
        const softkeys = yield server.db.read.rows(`SELECT
                            s.id,
                            s.name,
                            s.device_type,
                            s.side,
                            s.offset,
                            s.physical_code
                        FROM softkeys AS s`);

        touchmap.areas = touchmap.areas.map(area => {
          if (area.keyCode) {
            // eslint-disable-next-line no-param-reassign
            area.keyCodeName = _.result(
              _.find(keycodes, { code: area.keyCode }),
              'name'
            );
          }

          if (area.softkeyId) {
            // eslint-disable-next-line no-param-reassign
            area.softkeyName = _.result(
              _.find(softkeys, { id: parseInt(area.softkeyId, 10) }),
              'name'
            );
          }

          return area;
        });
      }

      res.send(touchmap);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
  createTouchmap: (req, res, next) =>
    co(function* execute() {
      const touchmapId = uuid();
      const touchmapParams = req.body;
      const areas = stripProperties(req.body.areas || []);

      if (!isValidCoords(areas)) {
        return next(
          new restify.BadRequestError('Invalid coordinates in toucharea')
        );
      }

      yield server.db.write.execute(
        `
                INSERT INTO touchmap( id, name, description, areas, created_by, company_id )
                VALUES( $1, $2, $3, $4, $5, $6 );
            `,
        [
          touchmapId,
          touchmapParams.name,
          touchmapParams.description,
          JSON.stringify(areas),
          req.user.sub,
          req.user.company.id,
        ]
      );

      const touchmap = yield server.db.read.row(
        `
                SELECT
                    tm.id,
                    tm.name,
                    tm.description,
                    tm.areas,
                    json_build_object(
                        'id', u.id,
                        'fullName', u.full_name
                    ) as creator
                    FROM touchmap as tm
                        LEFT JOIN ics_user u ON u.id = tm.created_by
                    WHERE tm.id = $1 AND tm.company_id = $2 AND active = true
            `,
        [touchmapId, req.user.company.id]
      );

      res.send(touchmap);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
  editTouchmapAreas: (req, res, next) =>
    co(function* execute() {
      const touchmapId = req.params.id;
      const companyId = req.user.company.id;
      const areas = stripProperties(req.body); // make sure we are only saving schema values

      // custom validation (shape must match with coordinates)
      if (areas.length > 0) {
        if (!isValidCoords(areas)) {
          // TODO: write proper validation context object to indicate the exact element that was invalid
          return next(new restify.BadRequestError('Invalid coordinates found'));
        }
      }

      const oldTouchmap = yield server.db.read.row(
        `
                SELECT
                    tm.id,
                    tm.areas
                    FROM touchmap as tm
                    WHERE tm.id = $1 AND tm.company_id = $2 AND active = true
            `,
        [touchmapId, companyId]
      );

      if (!oldTouchmap) {
        return next(new restify.NotFoundError('Touchmap not found'));
      }

      yield server.db.write.execute(
        `
                UPDATE touchmap
                SET areas = $1
                WHERE id = $2 AND company_id = $3
            `,
        [JSON.stringify(areas), touchmapId, companyId]
      );

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
  editTouchmap: (req, res, next) =>
    co(function* execute() {
      const touchmapId = req.params.id;
      const companyId = req.user.company.id;
      const touchmapParam = req.body;

      const oldTouchmap = yield server.db.read.row(
        `
                SELECT
                    tm.id,
                    tm.name,
                    tm.description,
                    tm.areas,
                    json_build_object(
                        'id', u.id,
                        'fullName', u.full_name
                    ) as creator
                    FROM touchmap as tm
                        LEFT JOIN ics_user u ON u.id = tm.created_by
                    WHERE tm.id = $1 AND tm.company_id = $2 AND active = true
            `,
        [touchmapId, companyId]
      );

      if (!oldTouchmap) {
        return next(new restify.NotFoundError('Touchmap not found'));
      }

      const newTouchmap = { ...oldTouchmap, ...touchmapParam };
      newTouchmap.areas = oldTouchmap.areas; // retain areas value (you can't edit it through this endpoint)

      yield server.db.write.execute(
        `
                UPDATE touchmap
                SET name = $1,
                    description = $2
                WHERE id = $3 AND company_id = $4
            `,
        [newTouchmap.name, newTouchmap.description, newTouchmap.id, companyId]
      );

      res.send(newTouchmap);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
  deleteTouchmap: (req, res, next) =>
    co(function* execute() {
      const touchmapId = req.params.id;
      const companyId = req.user.company.id;

      const touchmap = yield server.db.read.row(
        `
                SELECT * FROM touchmap WHERE id = $1 AND company_id = $2 AND active = true
            `,
        [touchmapId, companyId]
      );

      if (!touchmap) {
        return next(new restify.NotFoundError('Touchmap not found'));
      }

      const countObj = yield server.db.read.row(
        `
                SELECT COUNT(1) FROM prompt WHERE touchmap_id = $1
            `,
        [touchmapId]
      );

      if (countObj.count > 0) {
        return next(
          new restify.ConflictError(
            'Cannot delete a touchmap that is being used by a prompt'
          )
        );
      }

      yield server.db.write.execute(
        `
                UPDATE touchmap
                SET active = false
                WHERE id = $1 AND company_id = $2
            `,
        [touchmapId, companyId]
      );

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
  toODML: (req, res, next) =>
    co(function* execute() {
      const touchmapId = req.params.id;

      const isSystemUser = req.user.roles.includes('ICS_SYSTEM');
      const params = [touchmapId];

      if (!isSystemUser) {
        params.push(req.user.company.id);
      }

      const touchmap = yield server.db.read.row(
        `
                SELECT tm.id, tm.name, tm.areas
                FROM touchmap tm
                WHERE
                    tm.id = $1
                    ${isSystemUser ? '' : 'AND tm.company_id = $2'};
            `,
        params
      );

      if (!touchmap) {
        return next(new restify.NotFoundError('Touchmap not found'));
      }

      const softkeys = yield server.db.read.rows(`
                SELECT id, internal_name
                FROM softkeys
            `);

      const keycodes = yield server.db.read.rows(`
                SELECT code
                FROM keycodes
            `);

      const parsed = parseToODML(touchmap, softkeys, keycodes);

      res.send(parsed);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
  getPromptSets: (req, res, next) =>
    co(function* execute() {
      const touchmapId = req.params.id;
      const companyId = req.user.company.id;

      const promptsets = yield server.db.read.rows(
        `
                SELECT ps.id, ps.name, ps.status, ps.version, UPPER(s.code) as code
                FROM prompt p
                JOIN prompt_set ps ON p.prompt_set = ps.id
                JOIN prompt_state s on p.prompt_state = s.id
                WHERE
                    p.touchmap_id = $1 AND
                    ps.active IS TRUE AND
                    ps.company = $2
            `,
        [touchmapId, companyId]
      );

      res.send(promptsets);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
