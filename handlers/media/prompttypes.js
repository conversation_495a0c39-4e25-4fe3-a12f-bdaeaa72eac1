/* eslint-disable no-multi-spaces */

const co = require('co');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');

module.exports = {
  /**
   * Get Prompt Types
   */
  getPromptTypes: (req, res, next) =>
    co(function* execute() {
      const { state } = req.params;
      let stateFilter = 'WHERE pt.active = true ';
      let filterParams = [];
      if (state) {
        stateFilter = `
                    JOIN prompt_state st USING (numeric_input, dynamic_text, secure)
                    WHERE st.id = $1 AND pt.active = true
                `;
        filterParams = [state];
      }

      const types = yield server.db.read.rows(
        `
                SELECT
                  pt.id,
                  pt.name,
                  pt.secure,
                  pt.numeric_input,
                  pt.dynamic_text,
                  pt.static_text,
                  pt.background_asset,
                  pt.background_color,
                  pt.font_color,
                  pt.font_size,
                  pt.video_asset,
                  pt.html_asset,
                  pt.g6_template,
                  pt.g7_template,
                  pt.internal_name
                FROM prompt_type pt
                ${stateFilter}
                GROUP BY pt.id;
            `,
        filterParams
      );

      res.send(types);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
