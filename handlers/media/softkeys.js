const co = require('co');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const deviceTypeMap = require('../../helpers/device-types.json');

module.exports = {
  getSoftkeysForDeviceType: (req, res, next) => {
    const softkeysResolveTargets = ['G6-300', 'G6-400', 'G6-500'];
    const resolvedDevice = softkeysResolveTargets.includes(
      deviceTypeMap[req.params.deviceType]
    )
      ? 'G7-100'
      : deviceTypeMap[req.params.deviceType];
    const mappedDeviceType = resolvedDevice || req.params.deviceType;

    return co(function* execute() {
      const query = `
                SELECT
                  s.id,
                  s.name,
                  s.device_type,
                  s.side,
                  s.offset,
                  s.physical_code
                FROM softkeys AS s
                WHERE device_type = $1
                ORDER BY side, "offset";`;

      const results = yield server.db.read.rows(query, [mappedDeviceType]);
      res.send(results);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
};
