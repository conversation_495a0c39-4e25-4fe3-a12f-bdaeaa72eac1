/* eslint-disable no-param-reassign */
const fs = require('fs').promises;
const childProcess = require('child_process');
const _ = require('lodash');
const co = require('co');
const formidable = require('formidable');
const shortid = require('shortid');
const restify = require('restify');
const uuid = require('uuid/v4');
const jwt = require('jsonwebtoken');
const env = require('../../env');
const AWS = require('../../lib/aws');
const totp = require('../../lib/totp');
const { server } = require('../../app');
const { config } = require('../../env');
const mailer = require('../../lib/mailer');
const helper = require('../../helpers/promptset-helper');
const constants = require('../../lib/app-constants');
const usersHelper = require('../../helpers/users-helper');
const mailerHelper = require('../../helpers/mailer-helper');
const errorHandler = require('../../lib/errorhandler');
const prompttemplateHelper = require('../../helpers/prompttemplate-helper');
const { mainLogger } = require('../../lib/logger');
const {
  bumpPatchVersion,
  bumpMinorVersion,
  validate,
  stripProperties,
} = require('../../helpers/promptset-helper');
const deviceHelper = require('../../helpers/device-helper');
const deviceTypeMap = require('../../helpers/device-types.json');
const {
  promptService,
  promptstateService,
} = require('../../src/media-mgmt/imports.service');
const errors = require('../../src/media-mgmt/errors');
const { signingKeyTypes } = require('../../lib/app-constants');
const promptsetRepository = require('./promptsets.repository');
const promptsetService = require('./promptsets.service')(promptsetRepository);

const IS_HEX_COLOR_REGEX = /^[0-9A-F]{6}$/i;

const logger = mainLogger();
const {
  devicesThatNeedSignatureFingerprints,
  noFingerprintErrorMessage,
  devicesThatNeedOverridePackageVersionToZero,
  zeroPrompsetPackageVersion,
} = constants;

function generatePackageSignatureHash(key, user, slot, deviceType, filePath) {
  return new Promise((resolve, reject) =>
    // eslint-disable-next-line no-promise-executor-return
    childProcess.exec(
      `${env.config.signPackage.scriptPath} ${key} ${user} ${slot} ${deviceType} ${filePath}`,
      (error, stdout, stderr) => {
        if (stdout) {
          stdout = stdout.trim();
        }

        if (error) {
          error.stdout = stdout;
          error.stderr = stderr;
          return reject(error);
        }

        const ERROR_SIGNATURE = 'Err:';
        if (stdout.indexOf(ERROR_SIGNATURE) === 0) {
          return reject(stdout);
        }

        return resolve(stdout);
      }
    )
  );
}

function sanitiseHSMError(err, key, user) {
  const sanitised = '<sanitised>';

  const keyRegex = new RegExp(key, 'g');
  const userPasswordRegex = new RegExp(user, 'g');

  const deepSanitize = object => {
    Object.getOwnPropertyNames(object).forEach(property => {
      if (object[property] instanceof Object) {
        deepSanitize(object[property]);
      } else if (typeof object[property] === 'string') {
        object[property] = object[property]
          .replace(keyRegex, sanitised)
          .replace(userPasswordRegex, sanitised);
      }
    });
  };

  return deepSanitize(err);
}

function* copyPromptset(conn, { clonerId, rootId, sourceId, destId, status }) {
  // source is also the root unless specified
  rootId = rootId || sourceId; // eslint-disable-line
  return yield conn.execute(
    `
        INSERT INTO prompt_set
          ( id, prompt_template, bg, device_type, company, name,
            created, created_by, modified, modified_by, version,
            published, published_by, status, cloned_from, root_id,prompt_set_profile_name)
          (
            SELECT
              $1 AS new_id, prompt_template, bg, device_type, company, name,
              created, created_by, NOW(), $2, version,
              NOW(), $2, $3, id, $4, prompt_set_profile_name
            FROM prompt_set
            WHERE id = $5
          );
    `,
    [destId, clonerId, status, rootId, sourceId]
  );
}

async function getAssetById(assetId, companyId) {
  // eslint-disable-next-line no-return-await
  return await server.db.read.row(
    'SELECT * FROM asset WHERE id = $1 AND company = $2;',
    [assetId, companyId]
  );
}

function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1000));
  return `${(bytes / 1000 ** i).toFixed(decimals)} ${sizes[i]}`;
}

async function toUIPromptSet(promptSet) {
  let softkeys;
  let keycodes;

  if (promptSet.promptSetProfileName.startsWith('G7')) {
    keycodes = await server.db.read.rows(
      'SELECT k.code, k.name FROM keycodes as k ORDER BY k.code'
    );
    softkeys = await server.db.read.rows(`SELECT
                    s.id,
                    s.name,
                    s.device_type,
                    s.side,
                    s.offset,
                    s.physical_code
                FROM softkeys AS s`);
  }

  // NINJA-50: get asset metadata so we can inject the elements with it
  const assets = await server.db.read.rows(
    `SELECT
                    a.id,
                    a.name,
                    a.filename
                FROM asset AS a WHERE a.id = ANY($1)`,
    [helper.getUniquePromptAssets(promptSet)]
  );
  return helper.toUIPromptSet(promptSet, assets, softkeys, keycodes);
}

/**
 * @param {Array<string>} filterClause
 * @param {Array<string>} sortClause
 * @param {Array<any>} params - $1 must be user_id, $2 must be company_id
 *
 * @return {Promise<Array<Object>>}
 */
function queryPromptSetWith(filterClause, sortClause, params) {
  // Ensure we generate valid sql for any number of filter and sort columns...
  filterClause.unshift('');
  sortClause.push('ps.id');

  return server.db.read.rows(
    `
        WITH related_users AS NOT MATERIALIZED (
            SELECT
                u.id,
                u.full_name AS name,
                u.email
            FROM ics_user u
        )
        SELECT
            ps.id,
            ps.name,
            ps.version,
            ps.secure_fingerprint,
            ps.non_secure_fingerprint,
            ps.created,
            ps.modified,
            ps.status,
            ps.device_type,
            ps.prompt_set_profile_name,
            (
                SELECT software_id
                FROM software
                WHERE related_entity = ps.id::text
                LIMIT 1
            ) as software_id,
            (
                SELECT count(cps.id)
                FROM prompt_set cps
                WHERE cps.root_id = ps.id
                  AND cps.status != 'DRAFT'
                  and cps.active
            ) as children_count,
            (
                SELECT to_jsonb(u.*)
                FROM related_users u
                WHERE u.id = ps.created_by
            ) AS "createdBy",
            (
                SELECT to_jsonb(u.*)
                FROM related_users u
                WHERE u.id = ps.modified_by
            ) AS "modifiedBy",
            (SELECT to_jsonb(t) FROM (
                SELECT
                    pt.id,
                    pt.description AS name
                FROM prompt_template pt
                WHERE pt.id = ps.prompt_template
            ) t) AS template,
            (
                SELECT p.thumbnail_url
                FROM prompt p
                LEFT JOIN prompt_set_language_support psls
                    ON p.id = psls.prompt_set_id
                    AND psls.Default = true
                WHERE p.prompt_set = ps.Id
                AND P.company = ps.company
                AND p.thumbnail_url IS NOT NULL
                and p.deleted = false           
                LIMIT 1
            ) AS prompt_thumbnail,
            (
                SELECT to_jsonb(u.*)
                FROM related_users u
                WHERE u.id = ps.first_approver
            ) AS first_approver,
            ps.first_approved_time,
            (
                SELECT to_jsonb(u.*)
                FROM related_users u
                WHERE u.id = ps.second_approver
            ) AS second_approver,
            ps.second_approved_time,
            exists( select 1 from prompt_set_approvers psa where prompt_set_id = ps.id and psa.approver_id = ($1)::uuid ) AS is_approver
        FROM prompt_set ps
        WHERE ps.active
          AND ps.company = ($2)::uuid
          ${filterClause.join(' AND ')}
        ORDER BY
            ${sortClause.join(', ')}
    `,
    params
  );
}

module.exports = {
  // ------------------------------------------------------------------------
  // Get Prompt Set by ID
  // ------------------------------------------------------------------------
  getPromptSetByID: (req, res, next) => {
    const { user } = req;

    return co(function* execute() {
      const promptSet = yield helper.getPromptSetByID(req.params.id, user.sub);
      if (!promptSet) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      if (
        !user.roles.includes('ICS_SYSTEM') &&
        promptSet.company !== user.company.id
      ) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      res.send(yield toUIPromptSet(promptSet));
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  getPromptSetPackage: (req, res, next) => {
    co(function* execute() {
      const { user } = req;
      const softwareId = parseInt(req.params.id, 10);
      const companyId = user.company.id;

      const software = yield server.db.read.row(
        `
                SELECT software_id, software_file FROM software
                WHERE
                    software_id = $1 AND
                    company_id = $2 AND
                    type = 'media' AND
                    active = TRUE;
            `,
        [softwareId, companyId]
      );
      if (!software) {
        return next(new restify.NotFoundError('Package not found'));
      }
      res.send(200, software);
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  getAllPromptSets: async (req, res, next) => {
    /* eslint-disable indent */
    try {
      const pageIndex = parseInt(`${req.params.pageIndex || 0}`, 10);
      const pageSize = parseInt(`${req.params.pageSize || 100}`, 10);
      const order = req.params.order || 'modified';

      const { q } = req.params;
      const { state } = req.params;
      const { promptSetProfileName } = req.params;

      const queryParams = [req.user.sub, req.user.company.id];

      const filterClause = [];
      if (q) {
        queryParams.push(`%${q}%`);
        filterClause.push(`exists (
                    SELECT 1 
                    FROM ( VALUES (ps.name), (ps.secure_fingerprint), (ps.non_secure_fingerprint) ) s (t)
                    WHERE s.t ILIKE $${queryParams.length} )
                `);
      }
      if (state) {
        queryParams.push(state);
        filterClause.push(`$${queryParams.length} = ps.status`);
      }
      if (promptSetProfileName) {
        queryParams.push(promptSetProfileName);
        filterClause.push(
          `$${queryParams.length} = ps.prompt_set_profile_name`
        );
      }

      const sortClause = [];

      switch (order) {
        case 'creator':
          // eslint-disable-next-line quotes
          sortClause.push(
            `( SELECT r.name FROM related_users r WHERE r.id = ps.created_by )`
          );
          sortClause.push('ps.name');
          break;
        case 'status':
          sortClause.push('ps.status');
          sortClause.push('ps.name');
          break;
        case 'name':
          sortClause.push('ps.name');
          break;
        case 'modified':
        default:
          sortClause.push('ps.modified DESC');
      }

      const promptSets = await queryPromptSetWith(
        filterClause,
        sortClause,
        queryParams
      );

      res.send(200, {
        resultsMetadata: {
          totalResults: promptSets.length,
          pageIndex,
          pageSize,
        },

        results: promptSets.slice(
          pageIndex * pageSize,
          pageIndex * pageSize + pageSize // eslint-disable-line no-mixed-operators
        ),
      });
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getAllPromptSetProfiles: async (req, res, next) => {
    const { user } = req;
    try {
      const pageIndex = parseInt(`${req.params.pageIndex || 0}`, 10);
      const pageSize = parseInt(`${req.params.pageSize || 100}`, 10);
      const companyId = user.company.id;

      const promptSetProfiles = await promptsetRepository.getPromptSetProfiles(
        pageSize,
        pageIndex * pageSize,
        companyId
      );
      res.send(200, {
        resultsMetadata: {
          totalResults: promptSetProfiles.length,
          pageIndex,
          pageSize,
        },
        results: promptSetProfiles,
      });
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  // ------------------------------------------------------------------------
  // Create Prompt Set
  // ------------------------------------------------------------------------
  createPromptSet: (req, res, next) =>
    co(function* execute() {
      const { user } = req;
      const companyId = user.company.id;
      const templateId = req.body.template;
      const { promptSetProfileName } = req.body;
      const { name } = req.body;

      // Check if valid template
      const template = yield helper.getPromptSetTemplateById(
        user.company.id,
        templateId
      );
      if (!template) {
        return next(new restify.NotFoundError('Prompt template not found'));
      }

      // Get Company Languages
      const companyLanguages = yield promptsetService.getCompanyLanguagesById(
        user.company.id
      );

      // TODO: Override selectedLanguages variable to accept user input (e.g.) default of the prompt-set default language.
      const defaultLanguage = companyLanguages.find(item => !!item.default);
      const selectedLanguages = [defaultLanguage || null];

      // Check if valid promptSetProfile
      const promptSetProfile =
        yield helper.getPromptSetProfileByName(promptSetProfileName);
      if (!promptSetProfile) {
        return next(new restify.NotFoundError('promptSetProfile not found'));
      }

      const { legacyDeviceType } = promptSetProfile;

      // Check if valid device type
      const productType = yield helper.getProductTypeByName(legacyDeviceType);
      if (!productType) {
        return next(new restify.NotFoundError('Device type not found'));
      }

      // get main resolution screenwidth and screenheight
      const mainResolution = promptSetProfile.mainResolutions.split('x');
      promptSetProfile.screenWidth = parseInt(mainResolution[0], 10);
      promptSetProfile.screenHeight = parseInt(mainResolution[1], 10);

      // Load all states
      const states = yield prompttemplateHelper.getPromptTemplateStates(
        templateId,
        companyId
      );

      // Create prompts from list of states
      const promptAssignments = _.sortBy(
        _.map(states, state => {
          const elements = helper.createDefaultElements(
            state,
            template,
            promptSetProfile
          );

          let transactionState = null;
          if (state.attribs && state.attribs['txn-state']) {
            transactionState = state.attribs['txn-state'];
          }

          return {
            id: uuid(),
            state,
            prompts: selectedLanguages.map(language => ({
              id: uuid(),
              deviceType: legacyDeviceType,
              companyId,
              elements,
              transactionState,
              promptSetLanguageId: language ? language.languageSupportId : null,
            })),
            exceptions: [],
          };
        }),
        'state.code'
      ); // sort the prmpt state by code;

      const promptSetId = uuid();
      const conn = yield server.db.write.getConnection();
      try {
        yield conn.execute('BEGIN');

        // create  prompt set record
        const newPromptSet = (yield conn.execute(
          `
                    INSERT INTO prompt_set
                    (id, prompt_template, device_type, company, name,
                        created, created_by, modified, modified_by, active, bg, version, font_color, prompt_set_profile_name)
                    VALUES
                      ($1, $2, $3, $4, $5, NOW(), $6, NOW(), $6, $7, $8, '0.1.0', $9, $10 )
                    RETURNING id;
                `,
          [
            promptSetId,
            templateId,
            legacyDeviceType,
            companyId,
            name,
            user.sub,
            true,
            template.defaultBg,
            template.defaultFontColor,
            promptSetProfileName,
          ]
        )).rows[0];

        // Creating Prompt-Set Language Support Entries
        const promptSetLanguageSupports =
          selectedLanguages.filter(Boolean).length > 0
            ? yield promptsetService.createPromptSetLanguageSupportIds(
                conn,
                selectedLanguages,
                user.sub,
                promptSetId
              )
            : [];

        const preparePromptParams = promptAssignments.flatMap(pa =>
          pa.prompts.map(prompt => {
            const psls = promptSetLanguageSupports.find(
              i => i.languageSupportId === prompt.promptSetLanguageId
            );

            return {
              id: prompt.id,
              prompt_set: newPromptSet.id,
              prompt_state: pa.state.id,
              prompt_type: 1,
              device_type: prompt.deviceType,
              company: prompt.companyId,
              elements: prompt.elements,
              transaction_state: prompt.transactionState,
              screen_option: constants.PROMPT_CONSTANTS.SCREEN_OPTION.MAIN,
              contactless: false,
              deleted: false,
              ...(psls && {
                prompt_set_language_support_id: psls.promptSetLanguageSupportId,
              }),
            };
          })
        );

        // create prompt and assign it as default prompt to an assignment
        yield conn.execute(
          `
                    INSERT INTO prompt
                    SELECT * FROM jsonb_populate_recordset(NULL::prompt, $1::jsonb);
                `,
          [JSON.stringify(preparePromptParams)]
        );

        yield conn.execute('COMMIT');
        conn.done();
      } catch (err) {
        req.log.error({ err });
        yield conn.execute('ROLLBACK');
        conn.done();
        throw err;
      }

      // Get Prompt Set Language Support with Language Support IDs
      const promptSetLanguages = defaultLanguage
        ? yield promptsetService.getPromptSetLanguageById(promptSetId)
        : [];

      // Prepare final response (new prompt set object)
      const promptSet = {
        id: promptSetId,
        name,
        deviceType: legacyDeviceType,
        promptSetProfileName,
        template,
        created: new Date(),
        createdBy: {
          id: user.sub,
          name: user.fullName,
          email: user.email,
        },
        modified: new Date(),
        modifiedBy: {
          id: user.sub,
          name: user.fullName,
          email: user.email,
        },
        assignments: promptAssignments,
        status: 'DRAFT',
        lang: helper.groupLanguageObject(promptSetLanguages, 'isoCode'),
      };

      const promptSetForUI = yield toUIPromptSet(promptSet);

      const defaultPrompt = promptSetForUI.states
        .find(Boolean)
        .assignments.find(Boolean).id;
      yield helper.createPromptSetThumbnail(promptSet, [defaultPrompt], true);

      res.send(promptSetForUI);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  // ------------------------------------------------------------------------
  // Update Prompt Set (Internal)
  // ------------------------------------------------------------------------
  updatePromptSetInternal: (req, res, next) =>
    co(function* execute() {
      const promptSetId = req.params.id;
      const { status } = req.body;

      const oldPromptSet = yield helper.getPromptSetByID(
        promptSetId,
        req.user.sub
      );
      if (!oldPromptSet) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      yield server.db.write.execute(
        'UPDATE prompt_set SET status = $1 WHERE id = $2',
        [status, promptSetId]
      );

      res.send(204, 'Promptset Updated');
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  // ------------------------------------------------------------------------
  // Update Prompt Set
  // ------------------------------------------------------------------------
  updatePromptSet: (req, res, next) =>
    co(function* execute() {
      const { user } = req;
      const promptSetId = req.params.id;
      const promptSet = req.body;

      const oldPromptSet = yield helper.getPromptSetByID(promptSetId, user.sub);
      if (!oldPromptSet || oldPromptSet.company !== user.company.id) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      if (oldPromptSet.status !== 'DRAFT') {
        return next(new restify.ConflictError('Prompt set is not editable'));
      }

      let bumpedUpPromptSetVersion = null;
      let finalPromptSet = null;

      // Perform the Update
      const conn = yield server.db.write.getConnection();
      try {
        yield conn.execute('BEGIN');

        bumpedUpPromptSetVersion = bumpMinorVersion(oldPromptSet.version);

        yield conn.execute(
          `
                    UPDATE prompt_set SET
                      name = $1,
                      font_color = $2,
                      modified = NOW(),
                      modified_by = $3,
                      version = $4
                    WHERE
                      id = $5;
                `,
          [
            promptSet.name,
            promptSet.fontColor,
            user.sub,
            bumpedUpPromptSetVersion,
            promptSetId,
          ]
        );

        // Read full prompt set back from the DB to return to caller
        finalPromptSet = yield helper.getPromptSetByID(
          promptSetId,
          user.sub,
          conn
        ); // read with 'write' connection to avoid replication delay errors

        yield conn.execute('COMMIT');
        conn.done();
      } catch (err) {
        req.log.error({ err });
        yield conn.execute('ROLLBACK');
        conn.done();
        throw err;
      }

      res.send(yield toUIPromptSet(finalPromptSet));
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  // ------------------------------------------------------------------------
  //  Update promptset background
  // ------------------------------------------------------------------------
  updatePromptSetBg: async (req, res, next) => {
    try {
      const { user } = req;
      const promptsetId = req.params.id;
      const { bg } = req.params;

      // make sure force is a boolean
      let force = req.params.force || false;
      if (typeof force === 'string') {
        force = force === 'true';
      }

      const oldPromptSet = await helper.getPromptSetByID(promptsetId, user.sub);
      if (!oldPromptSet || oldPromptSet.company !== user.company.id) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      // if bg is not a color, then it must be an asset
      if (!IS_HEX_COLOR_REGEX.test(bg)) {
        // try querying if this is an asset we can access
        const bgAsset = await getAssetById(bg, req.user.company.id);
        if (!bgAsset) {
          return next(new restify.NotFoundError('Background Asset not found'));
        }
      }

      const allPrompts =
        await promptsetService.getAllPromptsByPromptSetId(promptsetId);

      const conn = await server.db.write.getConnection();
      let finalPromptSet = null;
      try {
        await conn.execute('BEGIN');
        const modifiedPrompts = allPrompts.map(prompt => {
          const bgElement = prompt.elements[0];

          // NOTE: this should be an impossible situation ( all prompts have bg elements by default )
          if (!bgElement) {
            prompt.elements = [
              {
                id: shortid.generate(),
                type: 'bg',
                value: bg,
                lock: false,
              },
            ];
            return prompt;
          }

          if (force || !bgElement.lock) {
            bgElement.value = bg;
          }

          return prompt;
        });

        // update all the prompts
        await Promise.all(
          modifiedPrompts.map(prompt =>
            conn.execute(
              `
                        UPDATE prompt SET
                            elements = $1
                            WHERE id = $2
                    `,
              [JSON.stringify(prompt.elements), prompt.id]
            )
          )
        );

        // Update the promptset bg itself
        await conn.execute(
          `
                    UPDATE prompt_set SET
                        bg = $1,
                        modified_by = $2,
                        modified = NOW(),
                        version = $3
                        WHERE id = $4
                `,
          [
            bg,
            req.user.sub,
            helper.bumpMinorVersion(oldPromptSet.version),
            promptsetId,
          ]
        );

        // Read full prompt set back from the DB to return to caller
        finalPromptSet = await helper.getPromptSetByID(
          promptsetId,
          user.sub,
          conn
        ); // read with 'write' connection to avoid replication delay errors

        await conn.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        await conn.execute('ROLLBACK');
        throw err;
      } finally {
        conn.done();
      }

      await helper.createPromptSetThumbnail(finalPromptSet);

      // get the promptset back
      res.send(await toUIPromptSet(finalPromptSet));
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  updatePromptSetFont: async (req, res, next) => {
    try {
      const { user } = req;
      const promptsetId = req.params.id;
      const { fontColor, fontFace, fontSize } = req.params;

      // make sure all is a boolean
      let all = req.params.all || false;
      if (typeof all === 'string') {
        all = all === 'true';
      }

      const oldPromptSet = await helper.getPromptSetByID(promptsetId, user.sub);
      if (!oldPromptSet || oldPromptSet.company !== user.company.id) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      let allPrompts = [];

      // Update all prompt font color
      if (all) {
        allPrompts =
          await promptsetService.getAllPromptsByPromptSetId(promptsetId);
      }

      const conn = await server.db.write.getConnection();
      let finalPromptSet = null;
      try {
        await conn.execute('BEGIN');
        const modifiedPrompts = allPrompts.map(prompt => {
          prompt.elements.forEach(element => {
            if (element.type === 'text' || element.type === 'input') {
              if (fontColor) {
                element.color = fontColor;
              }
              if (fontFace) {
                element.face = fontFace;
              }
              if (fontSize) {
                element.size = parseInt(fontSize, 10);
              }
            }
          });
          return prompt;
        });

        // Update all the prompts
        await Promise.all(
          modifiedPrompts.map(prompt =>
            conn.execute(
              `
                        UPDATE prompt SET
                            elements = $1
                            WHERE id = $2
                    `,
              [JSON.stringify(prompt.elements), prompt.id]
            )
          )
        );

        if (fontColor) {
          // Update the promptset font color
          await conn.execute(
            `
                    UPDATE prompt_set SET
                        font_color = $1,
                        modified_by = $2,
                        modified = NOW(),
                        version = $3
                    WHERE id = $4
                `,
            [
              fontColor,
              req.user.sub,
              helper.bumpMinorVersion(oldPromptSet.version),
              promptsetId,
            ]
          );
        } else {
          await conn.execute(
            `
                        UPDATE prompt_set SET
                            modified_by = $1,
                            modified = NOW(),
                            version = $2
                        WHERE id = $3
                    `,
            [
              req.user.sub,
              helper.bumpMinorVersion(oldPromptSet.version),
              promptsetId,
            ]
          );
        }

        // Read full prompt set back from the DB to return to caller
        finalPromptSet = await helper.getPromptSetByID(
          promptsetId,
          user.sub,
          conn
        ); // read with 'write' connection to avoid replication delay errors

        await conn.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        await conn.execute('ROLLBACK');
        throw err;
      } finally {
        conn.done();
      }

      if (all) {
        await helper.createPromptSetThumbnail(finalPromptSet);
      }
      // get the promptset back
      res.send(await toUIPromptSet(finalPromptSet));
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next);
    }
  },

  // ------------------------------------------------------------------------
  // Clone Prompt Set
  // ------------------------------------------------------------------------
  clonePromptSet: async (req, res, next) => {
    let finalPromptSet = null;

    try {
      const { user } = req;
      const userId = user.sub;
      const sourceId = req.params.id;
      const newPromptProps = req.body;

      const oldPromptSet = await helper.getPromptSetByID(sourceId, userId);
      if (!oldPromptSet || oldPromptSet.company !== user.company.id) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }
      const newPromptName = newPromptProps.name || oldPromptSet.name;

      const promptSetProfile = await helper.getPromptSetProfileByName(
        oldPromptSet.promptSetProfileName
      );
      if (!promptSetProfile) {
        return next(new restify.NotFoundError('promptSetProfile not found'));
      }

      const oldPromptSetLanguageSupports =
        await promptsetService.getPromptSetLanguageById(sourceId);

      // Clone prompt set
      const conn = await server.db.write.getConnection();
      try {
        await conn.execute('BEGIN');

        const promptSetId = uuid();
        await conn.execute(
          `
                    INSERT INTO prompt_set
                    (id, prompt_template, device_type, bg, company, name, created, created_by, modified, modified_by, status, cloned_from, prompt_set_profile_name)
                      (
                        SELECT
                          $1 AS new_id,
                          prompt_template,
                          device_type,
                          bg,
                          company,
                          $3,
                          NOW(),
                          $2,
                          NOW(),
                          $2,
                          'DRAFT',
                          id,
                          prompt_set_profile_name
                        FROM prompt_set
                        WHERE id = $4
                      );
                `,
          [promptSetId, userId, newPromptName, sourceId]
        );

        let assignments;
        assignments = oldPromptSet.assignments;
        if (oldPromptSet.assignments) {
          assignments = oldPromptSet.assignments.filter(
            assignment => assignment.prompts
          );
        }

        const newLanguages = oldPromptSetLanguageSupports.map(l => ({
          languageSupportId: l.languageSupportId,
          default: l.promptSetLanguageSupport.default,
          type: l.promptSetLanguageSupport.type,
          size: l.promptSetLanguageSupport.size,
        }));

        // Insert new Prompt-Set Language Supports
        const promptSetLanguageSupports =
          await promptsetService.createPromptSetLanguageSupportIds(
            conn,
            newLanguages,
            userId,
            promptSetId
          );

        // Insert new prompts, exceptions, assignments, etc
        await helper.insertPromptSetContent(
          promptSetId,
          user,
          assignments,
          conn,
          oldPromptSet.deviceType,
          promptSetProfile.auxResolutions,
          promptSetLanguageSupports
        );

        // Read full prompt set back from the DB to return to caller
        finalPromptSet = await helper.getPromptSetByID(
          promptSetId,
          userId,
          conn
        ); // read with 'write' connection to avoid replication delay errors

        const lang = await promptsetService.getPromptSetLanguageById(
          promptSetId,
          false,
          conn
        );
        finalPromptSet.lang = helper.groupLanguageObject(lang, 'isoCode');
        await conn.execute('COMMIT');
        conn.done();
      } catch (err) {
        req.log.error({ err });
        await conn.execute('ROLLBACK');
        conn.done();
        throw err;
      }

      const promptSetForUI = await toUIPromptSet(finalPromptSet);
      await helper.createPromptSetThumbnail(finalPromptSet);

      res.send(promptSetForUI);
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  // ------------------------------------------------------------------------
  // Get a list of approver users for this promptset
  // ------------------------------------------------------------------------
  getApprovers: (req, res, next) =>
    co(function* execute() {
      const { user } = req;
      const promptSetId = req.params.id;

      const oldPromptSet = yield server.db.read.row(
        `
                SELECT id
                    FROM prompt_set
                    WHERE id = $1 AND company = $2
            `,
        [promptSetId, user.company.id]
      );
      if (!oldPromptSet) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      const approvers = yield server.db.read.rows(
        `
                SELECT
                    u.id,
                    u.full_name as name,
                    u.email,
                    ap.action,
                    ap.modified
                    FROM ics_user as u
                    JOIN prompt_set_approvers ap ON ap.approver_id = u.id
                WHERE
                    ap.prompt_set_id = $1
            `,
        [promptSetId]
      );

      res.send(approvers);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  // ------------------------------------------------------------------------
  // Set prompt set to FOR_APPROVAL
  // ------------------------------------------------------------------------
  forApprovalPromptSet: (req, res, next) =>
    co(function* execute() {
      const { user } = req;
      const userId = user.sub;
      const promptSetId = req.params.id;
      const approverIds = req.body.approvers;

      const oldPromptSet = yield helper.getPromptSetByID(promptSetId, user.sub);
      if (!oldPromptSet || oldPromptSet.company !== user.company.id) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      const promptSetProfile = yield helper.getPromptSetProfileByName(
        oldPromptSet.promptSetProfileName
      );
      if (!promptSetProfile) {
        return next(new restify.NotFoundError('promptSetProfile not found'));
      }

      // Promptset must be in the PREVIEW state
      if (oldPromptSet.status !== 'PREVIEW') {
        return next(new restify.ConflictError('PromptSet must be in PREVIEW'));
      }

      const approvers = yield server.db.read.rows(
        `
                SELECT u.id, u.full_name, u.email, array_agg( r.role_name ) as roles
                    FROM ics_user AS u
                    JOIN user_role ur on ur.user_id = u.id
                    JOIN role r on r.role_id = ur.role_id
                    WHERE id = ANY( $1 ) AND company_id = $2
                    GROUP BY u.id
            `,
        [approverIds.map(approver => approver.id), user.company.id]
      );

      if (approvers.length !== approverIds.length) {
        return next(new restify.NotFoundError('Approver not found'));
      }

      // Make sure approvers have the correct roles:
      // (could have been filtered in the select query but we want a specific error code)
      const nonApprover = approvers.filter(
        approver => !approver.roles.includes('MEDIA_APPROVER')
      );
      if (nonApprover && nonApprover.length > 0) {
        return next(
          new restify.ConflictError('User does not have required role')
        );
      }

      const oldPromptSetLanguageSupports =
        yield promptsetService.getPromptSetLanguageById(promptSetId);

      let finalPromptSet;
      const conn = yield server.db.write.getConnection();
      try {
        // TOOO: FIX BELOW
        yield conn.execute('BEGIN');

        // Clone promptset
        const newPromptSetId = uuid();
        yield copyPromptset(conn, {
          clonerId: user.sub,
          rootId: oldPromptSet.clonedFrom.id,
          sourceId: oldPromptSet.id,
          destId: newPromptSetId,
          status: 'FOR_APPROVAL',
        });

        const newLanguages = oldPromptSetLanguageSupports.map(l => ({
          languageSupportId: l.languageSupportId,
          default: l.promptSetLanguageSupport.default,
          type: l.promptSetLanguageSupport.type,
          size: l.promptSetLanguageSupport.size,
        }));

        // Insert new Prompt-Set Language Supports
        const promptSetLanguageSupports =
          newLanguages.length > 0
            ? yield promptsetService.createPromptSetLanguageSupportIds(
                conn,
                newLanguages,
                userId,
                newPromptSetId
              )
            : [];

        // Insert new prompts, exceptions, assignments, etc
        yield helper.insertPromptSetContent(
          newPromptSetId,
          user,
          oldPromptSet.assignments,
          conn,
          oldPromptSet.deviceType,
          promptSetProfile.auxResolutions,
          promptSetLanguageSupports
        );

        finalPromptSet = yield helper.getPromptSetByID(
          newPromptSetId,
          user.sub,
          conn
        );

        const lang =
          newLanguages.length > 0
            ? yield promptsetService.getPromptSetLanguageById(
                newPromptSetId,
                false,
                conn
              )
            : [];
        finalPromptSet.lang = helper.groupLanguageObject(lang, 'isoCode');

        // Insert into list of approvers for this promptset
        yield Promise.all(
          approvers.map(approver =>
            conn.execute(
              `INSERT INTO prompt_set_approvers( prompt_set_id, approver_id, created )
                        VALUES ( $1, $2, now() )`,
              [newPromptSetId, approver.id]
            )
          )
        );

        yield Promise.all(
          approvers.map(approver =>
            conn.execute(
              `INSERT INTO notification( id, user_id, created, read, message, related_entity, type, level )
                        VALUES ( $1, $2, now(), false, $3, $4, 'promptset_approval', 'INFO' )`,
              [
                uuid(),
                approver.id,
                `PromptSet ${finalPromptSet.name} awaits your approval`,
                JSON.stringify({ id: newPromptSetId, type: 'promptset' }),
              ]
            )
          )
        );

        const { senderEmail } = yield mailerHelper.getSenderEmailsByCompanyId(
          user.company.id
        );
        // Send email notification
        yield Promise.all(
          approvers.map(approver =>
            mailer.sendPromptSetApprovalRequest(
              { to: approver.email, from: senderEmail },
              finalPromptSet,
              `PromptSet ${finalPromptSet.name} Awaits Your Approval`
            )
          )
        );

        yield conn.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield conn.execute('ROLLBACK');
        throw err;
      } finally {
        conn.done();
      }

      res.send(yield toUIPromptSet(finalPromptSet));
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  // ------------------------------------------------------------------------
  // Preview Prompt Set
  // ------------------------------------------------------------------------
  previewPromptSet: (req, res, next) =>
    co(function* execute() {
      const promptSetId = req.params.id;
      const { user } = req;
      const userId = user.sub;

      const oldPromptSet = yield helper.getPromptSetByID(promptSetId, user.sub);
      if (!oldPromptSet || oldPromptSet.company !== user.company.id) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }
      const promptSetProfile = yield helper.getPromptSetProfileByName(
        oldPromptSet.promptSetProfileName
      );
      if (!promptSetProfile) {
        return next(new restify.NotFoundError('promptSetProfile not found'));
      }

      if (oldPromptSet.status !== 'DRAFT') {
        req.log.info(
          `Rejecting attempt to preview ${oldPromptSet.id}. Invalid status: ${oldPromptSet.status}`
        );
        return next(new restify.ConflictError('Invalid PromptSet Status'));
      }

      const oldPromptSetLanguageSupports =
        yield promptsetService.getPromptSetLanguageById(promptSetId);

      let finalPromptSet;
      const conn = yield server.db.write.getConnection();
      try {
        yield conn.execute('BEGIN');

        const bumpedUpPromptSetVersion = bumpPatchVersion(oldPromptSet.version);

        yield conn.execute(
          `
                    UPDATE prompt_set
                    SET version = $1
                    WHERE id = $2;
                `,
          [bumpedUpPromptSetVersion, oldPromptSet.id]
        );

        const newPromptSetId = uuid();

        yield copyPromptset(conn, {
          clonerId: user.sub,
          sourceId: oldPromptSet.id,
          destId: newPromptSetId,
          status: 'GENERATING_PREVIEW',
        });

        const newLanguages = oldPromptSetLanguageSupports.map(l => ({
          languageSupportId: l.languageSupportId,
          default: l.promptSetLanguageSupport.default,
          type: l.promptSetLanguageSupport.type,
          size: l.promptSetLanguageSupport.size,
        }));

        // Insert new Prompt-Set Language Supports
        const promptSetLanguageSupports =
          newLanguages.length > 0
            ? yield promptsetService.createPromptSetLanguageSupportIds(
                conn,
                newLanguages,
                userId,
                newPromptSetId
              )
            : [];

        // Insert new prompts, exceptions, assignments, etc
        const { deviceType } = oldPromptSet;
        yield helper.insertPromptSetContent(
          newPromptSetId,
          user,
          oldPromptSet.assignments,
          conn,
          deviceType,
          promptSetProfile.auxResolutions,
          promptSetLanguageSupports
        );

        finalPromptSet = yield helper.getPromptSetByID(
          newPromptSetId,
          user.sub,
          conn
        );

        const lang =
          newLanguages.length > 0
            ? yield promptsetService.getPromptSetLanguageById(
                newPromptSetId,
                false,
                conn
              )
            : [];
        finalPromptSet.lang = helper.groupLanguageObject(lang, 'isoCode');

        // Invoke lambda packaging
        const company = yield server.db.read.row(
          `
                    SELECT name, promptset_package_version as "packageVersion" FROM company WHERE id = $1
                `,
          [user.company.id]
        );

        // Get fingerprint to sign G6-400 device
        const companyDeviceKey = yield server.db.read.row(
          `
        SELECT 
          key
        FROM 
          company_device_keys cdk 
        WHERE
          company = $1
          AND device_type = $2
          AND key_type = 'vendor'`,
          [user.company.id, deviceType]
        );
        const { key: fingerprint } = companyDeviceKey || {};

        if (
          !fingerprint &&
          devicesThatNeedSignatureFingerprints.includes(deviceType)
        ) {
          const message = `[promptsets].[previewPromptSet] ${noFingerprintErrorMessage}`;
          logger.error({ company: user.company.id, promptSetId }, message);
          return next(new restify.NotFoundError(message));
        }

        const lambdaParamters = {
          id: finalPromptSet.id,
          name: finalPromptSet.name,
          token: req.headers.authorization,
          packageVersion: devicesThatNeedOverridePackageVersionToZero.includes(
            deviceType
          )
            ? zeroPrompsetPackageVersion
            : company.packageVersion,
          fingerprint,
        };

        yield helper.invokeLambda(config.media.packageLambda, lambdaParamters);

        yield conn.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield conn.execute('ROLLBACK');
        throw err;
      } finally {
        conn.done();
      }

      res.send(yield toUIPromptSet(finalPromptSet));
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  exportPromptSet: async (req, res, next) => {
    const promptSetId = req.params.id;
    const { user } = req;

    try {
      const promptSet = await helper.getPromptSetByID(promptSetId, user.sub);
      if (!promptSet || promptSet.company !== user.company.id) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      if (promptSet.status !== 'DRAFT') {
        req.log.info(
          `Rejecting attempt to preview ${promptSet.id}. Invalid status: ${promptSet.status}`
        );
        return next(new restify.ConflictError('Invalid PromptSet Status'));
      }

      const promptInfo = {
        promptSetId: promptSet.id,
        userId: user.sub,
        companyId: user.company.id,
      };

      req.log.info(
        `Starting export prompt set (${JSON.stringify(promptInfo)})`
      );
      await helper.invokeLambda(
        `ics-lambda-promptset-export-${env.environment}`,
        promptInfo
      );
      req.log.info(`Finish export prompt set (${promptSet.id})`);
    } catch (err) {
      req.log.error('Catch error of exporting prompt set');
      req.log.error({ err });
      errorHandler.onError(req, res, next);
    }

    res.send(204);
    return next();
  },

  approvePromptSet: (req, res, next) =>
    co(function* execute() {
      const promptSetId = req.params.id;
      const { user } = req;
      const { mfaCode } = req.body;

      const mfaSecret = yield usersHelper.getUserMfaSecretById(user.sub);
      const isMfaCodeValid = yield totp.validateTotp(req, mfaSecret, mfaCode);
      if (!isMfaCodeValid) {
        return next(
          new restify.errors.BadRequestError(
            `MFA code ${mfaCode} validation failed`
          )
        );
      }

      const oldPromptSet = yield helper.getPromptSetByID(promptSetId, user.sub);
      if (!oldPromptSet || oldPromptSet.company !== user.company.id) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      if (oldPromptSet.status !== 'FOR_APPROVAL') {
        req.log.info(
          `Rejecting attempt to approve ${oldPromptSet.id}. Invalid status: ${oldPromptSet.status}`
        );
        return next(new restify.ConflictError('Invalid PromptSet Status'));
      }

      if (
        oldPromptSet.firstApprover &&
        oldPromptSet.firstApprover.id === user.sub
      ) {
        req.log.info(
          'User has already approved this request. Ignoring Request.'
        );
        res.send(oldPromptSet);
        return next();
      }

      // Get package version, in case we want to invoke lambda
      const company = yield server.db.read.row(
        `
                SELECT name, promptset_package_version as "packageVersion" FROM company WHERE id = $1
                `,
        [user.company.id]
      );

      // check if user is in the list of approvers
      const approver = yield server.db.read.row(
        `
                SELECT approver_id as id
                    FROM prompt_set_approvers
                    WHERE prompt_set_id = $1 AND approver_id = $2
            `,
        [promptSetId, user.sub]
      );

      if (!approver) {
        return next(new restify.ForbiddenError('User is not an approver'));
      }

      let finalPromptSet;
      const conn = yield server.db.write.getConnection();
      try {
        yield conn.execute('BEGIN');

        // check if first or second approver
        if (!oldPromptSet.firstApprover) {
          // update first approver
          yield conn.execute(
            `
                        UPDATE prompt_set
                        SET
                            first_approver = $1,
                            first_approved_time = now()
                        WHERE
                            id = $2
                    `,
            [user.sub, promptSetId]
          );

          // update approvers table
          yield conn.execute(
            `
                        UPDATE prompt_set_approvers
                        SET
                            action = 'APPROVE',
                            modified = now()
                        WHERE
                            prompt_set_id = $1 AND
                            approver_id = $2
                    `,
            [promptSetId, user.sub]
          );
        } else {
          const { deviceType } = oldPromptSet;

          // Get fingerprint to sign G6-400 device
          const companyDeviceKey = yield server.db.read.row(
            `
          SELECT 
            key
          FROM 
            company_device_keys cdk 
          WHERE
            company = $1
            AND device_type = $2
            AND key_type = 'vendor'`,
            [user.company.id, deviceType]
          );
          const { key: fingerprint } = companyDeviceKey || {};

          if (
            !fingerprint &&
            devicesThatNeedSignatureFingerprints.includes(deviceType)
          ) {
            const message = `[promptsets].[approvePromptSet] ${noFingerprintErrorMessage}`;
            logger.error({ company: user.company.id, promptSetId }, message);
            return next(new restify.NotFoundError(message));
          }

          // update 2nd approver and update status
          yield conn.execute(
            `
                        UPDATE prompt_set
                        SET
                            second_approver = $1,
                            second_approved_time = now(),
                            status = 'PUBLISHING'
                        WHERE
                            id = $2
                    `,
            [user.sub, promptSetId]
          );

          // update approvers table
          yield conn.execute(
            `
                        UPDATE prompt_set_approvers
                        SET
                            action = 'APPROVE',
                            modified = now()
                        WHERE
                            prompt_set_id = $1 AND
                            approver_id = $2
                    `,
            [promptSetId, user.sub]
          );

          const lambdaParamters = {
            id: oldPromptSet.id,
            name: oldPromptSet.name,
            token: req.headers.authorization,
            packageVersion:
              devicesThatNeedOverridePackageVersionToZero.includes(deviceType)
                ? zeroPrompsetPackageVersion
                : company.packageVersion,
            fingerprint,
          };

          // invoke lambda
          yield helper.invokeLambda(
            config.media.packageLambda,
            lambdaParamters
          );
        }

        finalPromptSet = yield helper.getPromptSetByID(
          promptSetId,
          user.sub,
          conn
        );

        yield conn.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield conn.execute('ROLLBACK');
        throw err;
      } finally {
        conn.done();
      }

      res.send(yield toUIPromptSet(finalPromptSet));
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  rejectPromptSet: (req, res, next) =>
    co(function* execute() {
      const { user } = req;
      const promptSetId = req.params.id;
      const { mfaCode } = req.body;

      const mfaSecret = yield usersHelper.getUserMfaSecretById(user.sub);
      const isMfaCodeValid = yield totp.validateTotp(req, mfaSecret, mfaCode);
      if (!isMfaCodeValid) {
        return next(
          new restify.errors.BadRequestError(
            `MFA code ${mfaCode} validation failed`
          )
        );
      }

      const oldPromptSet = yield helper.getPromptSetByID(promptSetId, user.sub);
      if (!oldPromptSet || oldPromptSet.company !== user.company.id) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      if (oldPromptSet.status !== 'FOR_APPROVAL') {
        req.log.info(
          `Rejecting attempt to approve ${oldPromptSet.id}. Invalid status: ${oldPromptSet.status}`
        );
        return next(new restify.ConflictError('Invalid PromptSet Status'));
      }

      // check if user is in the list of approvers
      const approver = yield server.db.read.row(
        `
                SELECT approver_id as id
                    FROM prompt_set_approvers
                    WHERE prompt_set_id = $1 AND approver_id = $2
            `,
        [promptSetId, user.sub]
      );

      if (!approver) {
        return next(new restify.ForbiddenError('User is not an approver'));
      }

      let finalPromptSet;
      const conn = yield server.db.write.getConnection();
      try {
        yield conn.execute('BEGIN');

        // update approvers table
        yield conn.execute(
          `
                    UPDATE prompt_set_approvers
                    SET
                        action = 'REJECT',
                        modified = now()
                    WHERE
                        prompt_set_id = $1 AND
                        approver_id = $2
                `,
          [promptSetId, user.sub]
        );

        // set promptset to rejected
        yield conn.execute(
          `
                    UPDATE prompt_set
                    SET
                        status = 'REJECTED',
                        modified = now()
                    WHERE
                        id = $1
                `,
          [promptSetId]
        );

        finalPromptSet = yield helper.getPromptSetByID(
          promptSetId,
          user.sub,
          conn
        );

        yield conn.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield conn.execute('ROLLBACK');
        throw err;
      } finally {
        conn.done();
      }

      res.send(yield toUIPromptSet(finalPromptSet));
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  // ------------------------------------------------------------------------
  // Delete (Archive) Prompt Set
  // ------------------------------------------------------------------------
  deletePromptSet: (req, res, next) =>
    co(function* execute() {
      const { user } = req;
      const promptSetId = req.params.id;

      const promptSet = yield helper.getPromptSetByID(promptSetId, user.sub);
      if (!promptSet || promptSet.company !== user.company.id) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      const conn = yield server.db.write.getConnection();
      try {
        yield conn.execute('BEGIN');

        yield conn.execute(
          `
                    UPDATE prompt_set
                    SET active = FALSE
                    WHERE id = $1;
                `,
          [promptSetId]
        );

        yield conn.execute(
          `
                    UPDATE software
                    SET active = FALSE
                    WHERE related_entity = $1;
                `,
          [promptSetId]
        );

        // Delete Prompt-Set Language Support Records
        yield conn.execute(
          `
                    UPDATE prompt_set_language_support 
                    SET deleted = true 
                    WHERE prompt_set_id = $1;
                `,
          [promptSetId]
        );

        yield conn.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield conn.execute('ROLLBACK');
        throw err;
      } finally {
        conn.done();
      }

      res.send(204, 'Prompt set deleted');
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  createPromptException: (req, res, next) =>
    co(function* execute() {
      const { user } = req;
      const { promptSetId } = req.params;
      const { promptId } = req.params;
      const dayPartId = req.body.dayPart.id;

      if (!promptId) {
        return next(new restify.NotFoundError(errors.prompt.notFound));
      }

      const promptSet = yield helper.getPromptSetByID(promptSetId, user.sub);
      if (!promptSet || promptSet.company !== user.company.id) {
        return next(new restify.NotFoundError(errors.promptSet.notFound));
      }

      const promptSetProfile = yield helper.getPromptSetProfileByName(
        promptSet.promptSetProfileName
      );
      if (!promptSetProfile) {
        return next(new restify.NotFoundError('promptSetProfile not found'));
      }
      // get main resolution screenwidth and screenheight
      const mainResolution = promptSetProfile.mainResolutions.split('x');
      promptSetProfile.screenWidth = parseInt(mainResolution[0], 10);
      promptSetProfile.screenHeight = parseInt(mainResolution[1], 10);

      const userId = _.get(user, 'sub', null);
      if (!userId) {
        return next(new restify.NotFoundError(errors.user.notFound));
      }

      const defaultPrompt = yield promptService.getPromptById(promptId);
      if (!defaultPrompt) {
        return next(new restify.NotFoundError(errors.prompt.notFound));
      }

      const state =
        yield promptstateService.getPromptStateByPrompt(defaultPrompt);
      if (!state) {
        return next(new restify.NotFoundError(errors.promptState.notFound));
      }

      const stateId = state.id || state.promptStateOvrId;

      const { promptSetLanguageSupportId } = defaultPrompt;

      const dayPart = yield server.db.read.row(
        'SELECT * FROM day_part WHERE id = $1;',
        [dayPartId]
      );
      if (!dayPart) {
        return next(new restify.NotFoundError(errors.daypart.notFound));
      }

      const productType = yield helper.getProductTypeByName(
        promptSet.deviceType
      );
      if (!productType) {
        return next(new restify.NotFoundError(errors.deviceType.notFound));
      }

      const daypartAlreadyAssigned = yield server.db.read.row(
        `
                    SELECT id FROM prompt
                    WHERE
                        prompt_set = $1 AND
                        prompt_state = $2 AND
                        day_part = $3 AND
                        deleted = false AND
                        (prompt_set_language_support_id is NULL or prompt_set_language_support_id = $4);
                `,
        [promptSetId, stateId, dayPartId, promptSetLanguageSupportId]
      );
      if (daypartAlreadyAssigned) {
        return next(new restify.ConflictError(errors.daypart.daypartConflict));
      }

      let touchmap = null;
      if (defaultPrompt) {
        touchmap = yield server.db.read.row(
          `
                    SELECT t.id, t.name, t.areas
                    FROM prompt p
                        JOIN touchmap t ON t.id = p.touchmap_id
                    WHERE
                        p.id = $1;
                `,
          [defaultPrompt.id]
        );
      }

      let updatedPromptSet = null;
      let bumpedUpPromptSetVersion = null;

      const elements = helper.createDefaultElements(
        state,
        {
          defaultBg: promptSet.bg,
          defaultFontColor: promptSet.fontColor,
        },
        promptSetProfile
      );

      const touchmapId = _.get(touchmap, 'id', null);
      const promptType = state.promptType || 'standard';
      const transactionState = _.get(defaultPrompt, 'transactionState', 'idle');

      const dayPartToAdd = {
        deviceType: promptSet.deviceType,
        promptType,
        company: user.company.id,
        elements,
        promptSet: defaultPrompt.promptSet,
        promptState: defaultPrompt.promptState || null,
        transactionState, // default idle state for new prompts
        touchmapId,
        dayPart: dayPartId,
        code: state.code,
        promptSetLanguageSupportId,
      };

      const [daypartCreated] = yield promptService.createPrompts(
        promptSetId,
        [dayPartToAdd],
        user
      );

      const isNewPrompt = !defaultPrompt.promptState;

      if (isNewPrompt) {
        const promptPso =
          yield promptstateService.getPromptStateByPrompt(defaultPrompt);

        const pso = {
          promptId: daypartCreated.id,
          code: promptPso.code,
          description: promptPso.description,
          secure: promptPso.secure,
          numericInput: promptPso.numericInput,
          dynamicText: promptPso.dynamicText,
          softKeys: promptPso.softKeys,
          active: true,
          promptType: promptPso.promptType,
          allowVideo: promptPso.allowVideo,
        };

        yield promptstateService.createPromptStatesOverride([pso], user);
      }

      const conn = yield server.db.write.getConnection();

      try {
        yield conn.execute('BEGIN');

        bumpedUpPromptSetVersion = bumpMinorVersion(promptSet.version);
        const { 0: firstRow } = (yield conn.execute(
          `
                    UPDATE prompt_set SET
                        version = $1,
                        modified = NOW(),
                        modified_by = $2
                    WHERE id = $3
                    RETURNING modified;
                `,
          [bumpedUpPromptSetVersion, req.user.sub, promptSet.id]
        )).rows;
        updatedPromptSet = firstRow;

        yield conn.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield conn.execute('ROLLBACK');
        throw err;
      } finally {
        conn.done();
      }

      const promptSetLanguages =
        yield promptsetService.getPromptSetLanguageById(promptSetId, true);
      const promptSetLanguage = promptSetLanguages.find(
        lang => lang.promptSetLanguageSupportId === promptSetLanguageSupportId
      );

      // create prompt thumbnail
      yield helper.createPromptSetThumbnail(promptSet);

      res.send({
        id: daypartCreated.id,
        parentId: stateId,
        dayPart,
        elements: daypartCreated.elements,
        promptSetLanguageId: promptSetLanguage
          ? promptSetLanguage.languageSupportId
          : null,
        touchmap,
        promptSet: {
          version: bumpedUpPromptSetVersion,
          modified: updatedPromptSet.modified,
          modifiedBy: {
            id: req.user.sub,
            name: req.user.fullName,
            email: req.user.email,
          },
        },
      });
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  signPackage: (req, res, next) => {
    const signPackageConfig = env.config.signPackage;

    const form = new formidable.IncomingForm();
    form.parse(req, (err, fields, file) => {
      if (err) return next(new restify.errors.InternalServerError(err));

      return co(function* execute() {
        try {
          const promptSetId = req.params.id;
          req.log.info(
            `Succesfully uploaded file ${file.fileContent.path} (${formatBytes(
              file.fileContent.size,
              2
            )}) for promptset ${promptSetId}`
          );

          const { userToken } = fields;
          if (!userToken) {
            return next(
              new restify.errors.BadRequestError('Field userToken is required')
            );
          }

          // TODO: Marcelo - We need to check if this still works
          const user = jwt.decode(userToken.replace('Bearer ', ''));
          if (!user) {
            return next(
              new restify.errors.BadRequestError('Unable to decode user token')
            );
          }

          const companyId = user.company.id;
          const promptSet = yield server.db.read.row(
            `
                        SELECT ps.device_type, ps.status FROM prompt_set ps
                        WHERE
                            ps.id = $1 AND
                            ps.company = $2;
                    `,
            [promptSetId, companyId]
          );

          if (!promptSet) {
            return next(new restify.NotFoundError('Prompt set not found'));
          }

          const isG6100orG6200 = deviceHelper.isG6100orG6200(
            promptSet.deviceType
          );
          const isG7100orG6300 = deviceHelper.isG7100orG6300(
            promptSet.deviceType
          );

          if (!isG6100orG6200 && !isG7100orG6300) {
            return next(
              new restify.errors.BadRequestError(
                `Device type ${promptSet.deviceType} not supported`
              )
            );
          }

          let key = null;
          if (promptSet.status === 'GENERATING_PREVIEW') {
            req.log.info(
              `Generating preview for ${promptSetId} with device type of ${promptSet.deviceType} and profile name ${promptSet.promptSetProfileName}`
            );

            key =
              isG6100orG6200 && !isG7100orG6300
                ? constants.packageSigning.G6_PREVIEW_KEY
                : constants.packageSigning.G7_PREVIEW_KEY;

            if (!key) {
              return next(
                new restify.errors.NotFoundError(
                  `Preview key for device type ${promptSet.deviceType} not found`
                )
              );
            }
          } else if (promptSet.status === 'PUBLISHING') {
            req.log.info(
              `Publishing for ${promptSetId} with device type of ${promptSet.deviceType} and profile name ${promptSet.promptSetProfileName}`
            );

            const resolveG6300 =
              deviceTypeMap[promptSet.deviceType] === 'G6-300'
                ? 'G7-100'
                : deviceTypeMap[promptSet.deviceType];
            const mappedDeviceType = resolveG6300 || promptSet.deviceType;
            const result = yield server.db.read.row(
              `
                            SELECT key FROM company_device_keys
                            WHERE
                                company = $1 AND
                                device_type = $2 AND
                                key_type = $3;
                        `,
              [companyId, mappedDeviceType, signingKeyTypes.SPONSOR]
            );

            if (result) {
              key = result.key;
            } else {
              return next(
                new restify.errors.NotFoundError(
                  `Publish key for device type ${promptSet.deviceType} not found`
                )
              );
            }
          } else {
            return next(
              new restify.errors.BadRequestError(
                `Prompt set status ${promptSet.status} not supported`
              )
            );
          }

          if (key.length !== 6) {
            return next(
              new restify.errors.ConflictError(
                'Invalid key size. Key must be 6 characters long'
              )
            );
          }

          let signature = null;
          try {
            req.log.info(`Generating signature for prompt set ${promptSetId}`);
            signature = yield generatePackageSignatureHash(
              key,
              signPackageConfig.user,
              signPackageConfig.slot,
              isG6100orG6200 && !isG7100orG6300 ? 'G6' : 'G7',
              file.fileContent.path
            );
            req.log.info(
              `Successfully generated signature for prompt set ${promptSetId}`
            );
          } catch (err2) {
            sanitiseHSMError(err2, key, signPackageConfig.user);
            req.log.error(err2);
            return next(
              new restify.errors.InternalServerError(
                'An error occurred while generating the signature hash'
              )
            );
          }

          res.send(200, signature);
          return next();
        } finally {
          yield fs.unlink(file.fileContent.path);
        }
      }).catch(errorHandler.onError(req, res, next));
    });

    return null;
  },

  saveFingerprint: (req, res, next) =>
    co(function* execute() {
      const promptSetId = req.params.id;

      // check if promptset id exists
      const promptSet = yield helper.getPromptSetByID(promptSetId);
      if (!promptSet) {
        return next(
          new restify.errors.NotFoundError(`PromptSet ${promptSetId} not found`)
        );
      }

      // save fingerprint
      yield server.db.write.row(
        `
                UPDATE prompt_set 
                    SET secure_fingerprint = $1, non_secure_fingerprint = $2
                WHERE id = $3;
            `,
        [req.body.secureFingerprint, req.body.nonSecureFingerprint, promptSetId]
      );

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getPromptSetChildren: async (req, res, next) => {
    try {
      res.send(
        200,
        await queryPromptSetWith(
          ['($3)::uuid = ps.root_id', `'DRAFT' != ps.status`], // eslint-disable-line quotes
          ['ps.modified DESC'],
          [req.user.sub, req.user.company.id, req.params.id]
        )
      );
      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  updatePrompts: (req, res, next) =>
    co(function* execute() {
      const { user } = req;
      const companyId = req.user.company.id;
      const promptSetId = req.params.id;

      const prompts = req.body;
      const errorMessages = [];

      const promptSet = yield helper.getPromptSetByID(promptSetId);
      if (!promptSet || promptSet.company !== companyId) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      const promptSetProfile = yield helper.getPromptSetProfileByName(
        promptSet.promptSetProfileName
      );
      if (!promptSetProfile) {
        return next(new restify.NotFoundError('promptSetProfile not found'));
      }
      // get main resolution screenwidth and screenheight
      const mainResolution = promptSetProfile.mainResolutions.split('x');

      const isG6100orG6200 = deviceHelper.isG6100orG6200(promptSet.deviceType);
      const isG7100orG6300 = deviceHelper.isG7100orG6300(promptSet.deviceType);

      if (!isG6100orG6200 && !isG7100orG6300) {
        return next(
          new restify.errors.ConflictError(
            `Device type ${promptSet.deviceType} is not supported`
          )
        );
      }

      // eslint-disable-next-line no-restricted-syntax
      for (const reqPrompt of prompts) {
        const { promptId, auxPrompt } = reqPrompt;
        const { softkeys } = reqPrompt;

        // Set default textAlign if it is missing for sequoia devices
        if (deviceHelper.isG7100orG6300(promptSet.deviceType)) {
          reqPrompt.elements.forEach(element => {
            if (
              (element.type === 'text' || element.type === 'input') &&
              !element.textAlign
            ) {
              element.textAlign = 'center';
            }
          });
        }

        const elements = stripProperties(reqPrompt.elements);
        const touchmapRequest = reqPrompt.touchmap;
        const { contactless } = reqPrompt;

        const prompt = yield promptService.getPromptById(promptId);
        if (!prompt) {
          return next(new restify.NotFoundError(errors.prompt.notFound));
        }

        // Linking Validation
        if (auxPrompt) {
          const validation = yield helper.validatePromptLinking(
            auxPrompt,
            prompt,
            reqPrompt.code
          );
          if (validation) return next(validation);
        }

        const promptState =
          yield promptstateService.getPromptStateByPrompt(prompt);

        if (!promptState) {
          return next(new restify.NotFoundError(errors.promptState.notFound));
        }

        if (!promptState.softKeys) {
          if (softkeys && softkeys.length > 0) {
            return next(
              new restify.errors.BadRequestError(
                `Prompt ${promptId} does not allow softkeys`
              )
            );
          }
        }

        if (
          (prompt.deviceType === 'G6-100' || prompt.deviceType === 'G6-200') &&
          touchmapRequest
        ) {
          return next(
            new restify.errors.BadRequestError(
              'Device does not support touchmaps'
            )
          );
        }

        let touchmap = null;
        if (touchmapRequest && touchmapRequest.id) {
          touchmap = yield server.db.read.row(
            `
                        SELECT id FROM touchmap
                        WHERE
                            id = $1 AND
                            company_id = $2;
                    `,
            [touchmapRequest.id, req.user.company.id]
          );

          if (!touchmap) {
            return next(
              new restify.errors.NotFoundError(
                `Touchmap ${touchmapRequest.id} not found`
              )
            );
          }
        }

        let setWidth = mainResolution[0];
        let setHeight = mainResolution[1];
        if (prompt.screenOption === 'aux') {
          const auxResolution = promptSetProfile.auxResolutions.split('x');
          setWidth = parseInt(auxResolution[0], 10);
          setHeight = parseInt(auxResolution[1], 10);
        }

        try {
          yield validate(elements, prompt, promptState, {
            width: setWidth,
            height: setHeight,
          });

          if (isG6100orG6200) {
            const assets = helper.getG6MockAssets(elements, promptState.secure);
            const odml = helper.parseElementsToODML(
              prompt.deviceType,
              elements,
              assets
            );

            if (odml.length > 2000) {
              errorMessages.push({
                promptId,
                err: 'ODML exceeds 2000 characters',
              });
            }
          }

          // contactless flag is for G6 only
          if (contactless && !isG6100orG6200) {
            errorMessages.push({
              promptId,
              err: 'Device does not support contactless flag',
            });
          }
        } catch (err) {
          req.log.error({ err });
          if (err instanceof restify.errors.BadRequestError) {
            errorMessages.push({
              promptId,
              elementId:
                err.body && err.body.elementId ? err.body.elementId : undefined,
              err: err.body
                ? err.body.context || err.body.message
                : err.message,
            });
          } else {
            return next(
              new restify.errors.InternalServerError(
                `Unable to validate prompt ${promptId}`
              )
            );
          }
        }
      }

      if (errorMessages.length) {
        res.send(400, errorMessages);
        return next();
      }

      // Check if editing these prompts results to the promptset not having an idle prompt,
      const allPrompts = promptSet.assignments
        .flatMap(assignment => assignment.prompts)
        .map(p => {
          const editedPrompt = prompts.find(
            reqPrompt => reqPrompt.promptId === p.id
          );

          // Ensure language ID retained and not allowing user to update.
          if (editedPrompt) {
            editedPrompt.promptSetLanguageId = p.promptSetLanguageId;
          }

          return editedPrompt || p;
        });

      helper.checkIdlePrompts(allPrompts);

      let bumpedUpPromptSetVersion = null;
      let updatedPromptSet = null;

      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');

        // eslint-disable-next-line no-restricted-syntax
        for (const reqPrompt of prompts) {
          const { promptId } = reqPrompt;
          const { softkeys } = reqPrompt;
          const elements = stripProperties(reqPrompt.elements);
          const touchmapRequest = reqPrompt.touchmap;
          const { transactionState } = reqPrompt;
          const { contactless } = reqPrompt;
          const { auxPrompt, code, screenOption } = reqPrompt;
          const promptState =
            yield promptstateService.getPromptStateByPrompt(reqPrompt);

          //  Renaming prompts
          // eslint-disable-next-line no-prototype-builtins
          if (reqPrompt.hasOwnProperty('description')) {
            const promptCodeExists =
              yield promptstateService.checkPromptStateCodeExists(
                promptSetId,
                code
              );

            const renamePromptRes = yield promptService.renamePrompt(
              connection,
              promptState,
              promptSetId,
              promptId,
              screenOption,
              code,
              reqPrompt.description,
              promptCodeExists
            );
            if (renamePromptRes.errorCode) {
              res.send(renamePromptRes.errorCode, renamePromptRes.errorMsg);
              return next();
            }
          }

          yield connection.execute(
            `
                        UPDATE prompt
                        SET elements = $1, touchmap_id = $2, transaction_state = $3, contactless = $4,
                        aux_prompt=$5 
                        WHERE id = $6;
                    `,
            [
              JSON.stringify(elements),
              touchmapRequest ? touchmapRequest.id : null,
              transactionState || null,
              contactless || false,
              auxPrompt || null,
              promptId,
            ]
          );

          // eslint-disable-next-line no-prototype-builtins
          if (promptState && promptState.attribs.hasOwnProperty('txn-state')) {
            const updatedAttribs = JSON.stringify({
              ...promptState.attribs,
              'txn-state': transactionState,
            });

            yield connection.execute(
              `
              UPDATE prompt_state
              SET attribs = $1
              WHERE id = $2;
              `,
              [updatedAttribs, promptState.id]
            );
          }

          // clear all softkeys first
          yield connection.execute(
            `
                        DELETE FROM softkey_assignments
                        WHERE prompt = $1;
                    `,
            [promptId]
          );

          // add new softkeys
          yield Promise.all(
            softkeys.map(softkeyAssignment => {
              const softkeyAssignmentId = uuid();
              return connection.execute(
                `
                            INSERT INTO softkey_assignments (id, prompt, softkey, label, font_color, font_size, keycode)
                            VALUES ( $1, $2, $3, $4, $5, $6, $7 );
                        `,
                [
                  softkeyAssignmentId,
                  promptId,
                  softkeyAssignment.softkey,
                  softkeyAssignment.label,
                  softkeyAssignment.fontColor,
                  softkeyAssignment.fontSize,
                  softkeyAssignment.keycode,
                ]
              );
            })
          );
        }

        bumpedUpPromptSetVersion = bumpMinorVersion(promptSet.version);
        const { 0: firstRow } = (yield connection.execute(
          `
                    UPDATE prompt_set SET
                        version = $1,
                        modified = NOW(),
                        modified_by = $2
                    WHERE id = $3
                    RETURNING modified;
                `,
          [bumpedUpPromptSetVersion, user.sub, promptSet.id]
        )).rows;
        updatedPromptSet = firstRow;

        yield connection.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        return next(
          new restify.errors.InternalServerError(
            'Failed trying to update prompt elements'
          )
        );
      } finally {
        connection.done();
      }

      yield helper.createPromptSetThumbnail(
        promptSet,
        prompts.map(prompt => prompt.promptId)
      );

      res.send(200, {
        version: bumpedUpPromptSetVersion,
        modified: updatedPromptSet.modified,
        modifiedBy: {
          id: user.sub,
          name: user.fullName,
          email: user.email,
        },
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  importPromptSet: async (req, res, next) => {
    try {
      const { id } = req.body;

      req.log.info('Start to request s3 pre-signed url');
      const item = `imported/${new Date().getTime()}/${id}.db`;
      const url = await AWS.generateSignedURL(
        {
          Bucket: env.config.AWS.S3.bucket,
          Key: item,
          Metadata: {
            user: req.user.sub,
            company: req.user.company.id,
          },
        },
        60
      );

      req.log.info(
        `Successfully request s3 pre-signed url for prompt set(${id}): ${url}`
      );
      res.send(200, { url });
      return next();
    } catch (err) {
      req.log.error({ err });
      return errorHandler.onError(req, res, next)(err);
    }
  },

  getPromptSetLanguages: async (req, res, next) => {
    const { id } = req.params;
    const { user } = req;
    const userId = user.sub;

    const promptSet = await helper.getPromptSetByID(req.params.id, userId);
    if (
      !promptSet &&
      !user.roles.includes('ICS_SYSTEM') &&
      promptSet.company !== user.company.id
    ) {
      return next(new restify.NotFoundError('Prompt set not found'));
    }

    try {
      // Get Prompt Set Language Support
      const promptSetLanguages =
        await promptsetService.getPromptSetLanguageById(id, false);
      if (promptSetLanguages.length <= 0) {
        res.send([]);
        return next();
      }

      res.send(promptSetLanguages);

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  setPromptSetLanguages: async (req, res, next) => {
    const promptSetId = req.params.id;
    const companyId = req.user.company.id;
    const userId = req.user.sub;
    const languagesToUpdate = req.body;

    const connection = await server.db.write.getConnection();

    try {
      // Check Prompt-Set Exist
      const promptSet = await helper.getPromptSetByID(promptSetId, userId);
      if (!promptSet || promptSet.company !== companyId) {
        return next(new restify.NotFoundError('Prompt set not found'));
      }

      const promptSetProfile = await helper.getPromptSetProfileByName(
        promptSet.promptSetProfileName
      );
      if (!promptSetProfile) {
        return next(new restify.NotFoundError('promptSetProfile not found'));
      }
      // get main resolution screenwidth and screenheight
      const mainResolution = promptSetProfile.mainResolutions.split('x');
      promptSetProfile.screenWidth = parseInt(mainResolution[0], 10);
      promptSetProfile.screenHeight = parseInt(mainResolution[1], 10);

      // Check Prompt-Set Status
      if (promptSet.status !== constants.promptSetStatus.DRAFT) {
        return next(new restify.ConflictError('Prompt set is not editable'));
      }

      const templateId = promptSet.promptTemplateId;
      const deviceTypeName = promptSet.deviceType;

      // Check if valid template
      const template = await helper.getPromptSetTemplateById(
        companyId,
        templateId
      );
      if (!template) {
        return next(new restify.NotFoundError('Prompt template not found'));
      }

      // Check if valid device type
      const productType = await helper.getProductTypeByName(deviceTypeName);
      if (!productType) {
        return next(new restify.NotFoundError('Device type not found'));
      }

      // Get All Company Langs
      const companyLanguages =
        await promptsetService.getCompanyLanguagesById(companyId);
      if (companyLanguages.length <= 0) {
        return next(
          new restify.errors.BadRequestError(
            'Company has no language support enabled.'
          )
        );
      }

      // Get Languages enabled for a prompt-set
      const promptSetLanguages =
        await promptsetService.getPromptSetLanguageById(promptSetId, true);
      const promptSetDefaultLang = promptSetLanguages.find(
        psl => psl.promptSetLanguageSupport.default === true
      );

      // Validate Payloads
      const updatedPromptSetLanguages = [];
      // eslint-disable-next-line no-restricted-syntax
      for (const updatedLang of languagesToUpdate) {
        // Existing Prompt-Set Language
        const matchExisting = promptSetLanguages.find(
          i => i.languageSupportId === updatedLang.languageSupportId
        );

        // Check duplicated entry in the payload
        const duplicate = updatedPromptSetLanguages.find(
          i => i.languageSupportId === updatedLang.languageSupportId
        );
        if (duplicate) {
          return next(
            new restify.errors.BadRequestError(
              `Language support ID '${updatedLang.languageSupportId}' is duplicated.`
            )
          );
        }

        if (!matchExisting) {
          // Skip If has no existing but marked as deleted
          if (updatedLang.promptSetLanguageSupport.deleted !== true) {
            // Check if Company has Language
            const companyLangExist = companyLanguages.find(
              i => i.languageSupportId === updatedLang.languageSupportId
            );
            if (!companyLangExist) {
              return next(
                new restify.errors.BadRequestError(
                  `Language support ID '${updatedLang.languageSupportId}' does not exist.`
                )
              );
            }

            updatedPromptSetLanguages.push(updatedLang);
          }
        } else {
          const merged = { ...matchExisting, ...updatedLang };
          updatedPromptSetLanguages.push(merged);
        }
      }

      // Merge existing langs and updated langs
      const allPromptSetLangs = [
        ...updatedPromptSetLanguages,
        ...promptSetLanguages.filter(
          j =>
            !updatedPromptSetLanguages.find(
              i => i.languageSupportId === j.languageSupportId
            )
        ),
      ];
      const isLegacyPromptSet = promptSetLanguages.length <= 0;

      // Validate default language count
      const newPromptSetDefaultLangs = allPromptSetLangs.filter(
        i =>
          i.promptSetLanguageSupport.default === true &&
          i.promptSetLanguageSupport.deleted === false
      );
      if (
        newPromptSetDefaultLangs.length > 1 ||
        newPromptSetDefaultLangs.length <= 0
      ) {
        return next(
          new restify.errors.BadRequestError(
            "Prompt-Set can have one 'default' language."
          )
        );
      }
      const newPromptSetDefaultLang = newPromptSetDefaultLangs[0];

      const pslsToUpdate = updatedPromptSetLanguages.filter(
        p => p.promptSetLanguageSupportId
      );
      const pslsToCreate = updatedPromptSetLanguages.filter(
        p => !p.promptSetLanguageSupportId
      );

      await connection.execute('BEGIN');

      // Create PSLS and Prompts
      if (pslsToCreate.length > 0) {
        // Create new prompt-set language
        const newPromptSetLanguageSupports =
          await promptsetService.createPromptSetLanguageSupportIds(
            connection,
            pslsToCreate.map(lang => ({
              default: lang.promptSetLanguageSupport.default,
              type: lang.promptSetLanguageSupport.type,
              size: lang.promptSetLanguageSupport.size,
              languageSupportId: lang.languageSupportId,
            })),
            userId,
            promptSetId
          );

        // Update Prompts for the default language if legacy prompt-set
        if (isLegacyPromptSet) {
          const defaultPSLS = newPromptSetLanguageSupports.find(
            psls =>
              psls.languageSupportId ===
              newPromptSetDefaultLang.languageSupportId
          );
          await promptService.setPromptSetLanguageSupportIdByPromptSetId(
            promptSetId,
            defaultPSLS.promptSetLanguageSupportId,
            connection
          );
        }

        // Create Prompt State Override and Prompts Entries.
        const promptLanguageId = promptSetDefaultLang
          ? promptSetDefaultLang.languageSupportId
          : newPromptSetDefaultLang.languageSupportId;
        const newAssignments = promptSet.assignments.map(assignment => {
          const defaultPrompt =
            promptSetLanguages.length > 0
              ? assignment.prompts.find(
                  p => p.promptSetLanguageId === promptLanguageId
                )
              : assignment.prompts[0];
          return {
            state: assignment.state,
            prompts: newPromptSetLanguageSupports
              .filter(i => i.languageSupportId !== promptLanguageId)
              .map(psls => ({
                ...defaultPrompt,
                ...{
                  elements: helper.createDefaultElements(
                    assignment.state,
                    template,
                    promptSetProfile
                  ),
                  promptSetLanguageId: psls.languageSupportId,
                  touchmap: null,
                  softkeys: [],
                  type: { id: 1 },
                },
              })),
            exceptions: [],
          };
        });
        await helper.insertPromptSetContent(
          promptSetId,
          req.user,
          newAssignments,
          connection,
          promptSet.deviceType,
          promptSetProfile.auxResolutions,
          newPromptSetLanguageSupports
        );
      }

      // Update PSLS
      if (pslsToUpdate.length > 0) {
        // eslint-disable-next-line no-restricted-syntax
        for (const lang of pslsToUpdate) {
          // Update existing prompt-set language support
          // eslint-disable-next-line no-await-in-loop
          await promptsetService.updatePromptSetLanguageSupportIds(
            connection,
            [
              {
                prompt_set_language_support_id: lang.promptSetLanguageSupportId,
                default: lang.promptSetLanguageSupport.default,
                deleted: lang.promptSetLanguageSupport.deleted,
                type: lang.promptSetLanguageSupport.type,
                size: lang.promptSetLanguageSupport.size,
              },
            ],
            userId,
            promptSetId
          );

          if (lang.promptSetLanguageSupport.deleted) {
            // Delete Prompt State Override
            // eslint-disable-next-line no-await-in-loop
            await promptstateService.deletePromptStateOverrideByPSLSId(
              promptSetId,
              lang.promptSetLanguageSupportId,
              connection
            );

            // Delete Prompts
            // eslint-disable-next-line no-await-in-loop
            await promptService.deletePromptByPromptSetLanguageSupportIds(
              [lang.promptSetLanguageSupportId],
              connection
            );
          }
        }
      }

      const bumpedUpPromptSetVersion = bumpMinorVersion(promptSet.version);
      helper.updatePromptSetVersion(
        promptSetId,
        userId,
        bumpedUpPromptSetVersion,
        connection
      );
      await connection.execute('COMMIT');

      res.send(
        await promptsetService.getPromptSetLanguageById(promptSetId, false)
      );
      return next();
    } catch (err) {
      await connection.execute('ROLLBACK');
      return errorHandler.onError(req, res, next)(err);
    } finally {
      connection.done();
    }
  },
};
