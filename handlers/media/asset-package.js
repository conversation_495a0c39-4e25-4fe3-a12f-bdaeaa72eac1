const stream = require('stream');
const co = require('co');
const restify = require('restify');
const formidable = require('formidable');
const uuid = require('uuid/v4');

const env = require('../../env');
const AWS = require('../../lib/aws');
const constants = require('../../lib/app-constants');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');
const { server } = require('../../app');

const selectClause = `
    SELECT
        ap.id,
        pt.device_type,
        ap.name,
        ap.signed,
        ap.uploaded,
        (
            SELECT json_build_object(
                'id', u.id,
                'fullName', u.full_name,
                'company', json_build_object(
                    'id', c.id,
                    'name', c.name
                )
            )
        ) as uploader,
        ap.status
    FROM asset_package as ap
        JOIN ics_user u ON u.id = ap.uploaded_by
        LEFT JOIN company c ON c.id = u.company_id
        LEFT JOIN product_type pt ON pt.device_type = ap.device_type
`;

module.exports = {
  uploadAssetPackage: (req, res, next) => {
    const form = new formidable.IncomingForm();

    form.on('field', (name, value) => {
      // prevent assetNames shorter than 3 characters
      if (name === 'assetPackageName' && value && value.length < 3) {
        return next(new restify.BadRequestError('assetPackageName too short'));
      }
      return (req.params[name] = value); // eslint-disable-line
    });
    // eslint-disable-next-line consistent-return
    form.onPart = part => {
      if (!part.filename) {
        // let formidable handle all non-file parts
        form.handlePart(part);
      } else {
        const s3stream = new stream.PassThrough();
        part.pipe(s3stream);
        s3stream.on('error', errorHandler.onError(req, res, next));
        return co(function* execute() {
          req.log.info('Try to upload to S3');
          const id = uuid();
          const { filename } = part;

          if (
            !filename.endsWith('tar.gz.bin') &&
            !filename.endsWith('tgz.bin') &&
            !filename.endsWith('pkg') &&
            !filename.endsWith('zip')
          ) {
            return next(new restify.BadRequestError('Invalid file type'));
          }

          const { bucket } = env.config.AWS.S3;
          const key = `uploads/${id}/${part.filename}`;
          const body = s3stream;
          const acl = 'public-read';

          const uploadResults = yield AWS.uploadToS3(bucket, key, body, acl);
          const created = new Date().toISOString();

          // by this time, req.params is supposed to be populated because S3 has finished uploading
          if (!req.params.assetPackageName) {
            return next(
              new restify.BadRequestError('assetPackageName required')
            );
          }

          yield server.db.write.execute(
            'INSERT INTO asset_package (id, company, name, source_file_url, uploaded, status, uploaded_by )' +
              ' VALUES ($1, $2, $3, $4, $5, $6, $7)',
            [
              id,
              req.user.company.id,
              req.params.assetPackageName,
              uploadResults.Location,
              created,
              constants.assetStatus.NEW,
              req.user.sub,
            ]
          );
          const company = yield server.db.read.row(
            'select id, name from company where id = $1',
            [req.user.company.id]
          );
          const uploader = yield server.db.read.row(
            'select id, full_name from ics_user where id = $1',
            [req.user.sub]
          );
          const response = {
            id,
            name: req.params.assetPackageName,
            company,
            sourceUrl: uploadResults.Location,
            package: null,
            signed: null, // when does the backend know that a package is signed?
            deviceType: null, // device type is null at first
            status: constants.assetStatus.NEW,
            uploaded: created,
            uploader,
          };
          req.log.info('Upload to S3 success');
          res.send(response, 200);
          return next();
        }).catch(errorHandler.onError(req, res, next));
      }
    };

    form.parse(req);
  },
  getAssetPackages: (req, res, next) =>
    co(function* execute() {
      const pageParams = paginationHelper.parsePaginationParams(req);

      let whereClause = 'WHERE ap.company = $1 AND ap.active = TRUE';
      const queryParams = [req.user.company.id];

      if (req.query.name) {
        whereClause = `${whereClause} AND LOWER(ap.name) LIKE LOWER('%'||$2||'%') `;
        queryParams.push(req.query.name);
      }

      let sortClause = '';
      const order = req.query.order || 'uploaded-desc';
      const sortParam = order.split('-');
      sortClause = `ORDER BY ap.${sortParam[0]} ${sortParam[1]}`;

      const totalObj = yield server.db.read.row(
        `SELECT count(1) FROM asset_package as ap ${whereClause}`,
        queryParams
      );
      const results = yield server.db.read.rows(
        `${selectClause} ${whereClause}
                    ${sortClause}
                LIMIT ${pageParams.pageSize} OFFSET ${pageParams.offset}`,
        queryParams
      );

      res.send({
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results,
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getAssetPackage: (req, res, next) =>
    co(function* execute() {
      let companyClause = '';
      let queryParams = [];

      // Allow System Users to get assets regardless of company
      if (req.user.roles.includes('ICS_SYSTEM')) {
        companyClause = '';
        queryParams = [req.params.id];
      } else {
        companyClause = 'AND ap.company = $2';
        queryParams = [req.params.id, req.user.company.id];
      }

      const query = `${selectClause}
                WHERE ap.active = TRUE and ap.id = $1 ${companyClause}`;

      const assetPackage = yield server.db.read.row(query, queryParams);
      if (!assetPackage) {
        return next(new restify.NotFoundError('Cannot find asset package'));
      }

      res.send(assetPackage);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getAssetsInPackage: (req, res, next) => {
    const pageParams = paginationHelper.parsePaginationParams(req);

    return co(function* execute() {
      const query =
        'SELECT id FROM asset_package WHERE id = $1 AND active = TRUE AND company = $2';

      const assetPackage = yield server.db.read.row(query, [
        req.params.id,
        req.user.company.id,
      ]);
      if (!assetPackage) {
        return next(new restify.NotFoundError('Cannot find asset package'));
      }

      // TODO: refactor this because assets.js use the same exact query
      const selectAssets = `SELECT  a.id,
                    a.name,
                    a.width,
                    a.height,
                    a.status,
                    a.type,
                    a.uploaded,
                    a.thumbnail_file_url as thumbnail_url,
                    a.source_file_url as source_url,
                    (
                        SELECT row_to_json(_) FROM (select u.id, u.full_name as "fullName") as _
                    ) as uploader,
                    (
                        SELECT row_to_json(_) FROM (select p.id, p.name, p.signed ) as _
                    ) as package
                    FROM asset as a
                        JOIN ics_user u on u.id = a.uploaded_by
                        LEFT JOIN asset_package p on p.id = a.asset_package`;
      const whereClause = 'WHERE a.asset_package = $1';
      const totalObj = yield server.db.read.row(
        `SELECT count(1) FROM asset as a LEFT JOIN asset_package p on p.id = a.asset_package ${whereClause}`,
        [req.params.id]
      );
      const results = yield server.db.read.rows(
        `${selectAssets} ${whereClause}
                ORDER BY a.name
                LIMIT ${pageParams.pageSize} OFFSET ${pageParams.offset}`,
        [req.params.id]
      );

      res.send({
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results,
      });
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  editAssetPackage: (req, res, next) =>
    co(function* execute() {
      const assetPackageParams = req.body;
      const query = `${selectClause}
                WHERE ap.company = $1 AND ap.active = TRUE and ap.id = $2`;

      const assetPackage = yield server.db.read.row(query, [
        req.user.company.id,
        req.params.id,
      ]);
      if (!assetPackage) {
        return next(new restify.NotFoundError('Cannot find asset package'));
      }

      const productTypeName =
        assetPackageParams.deviceType || assetPackage.deviceType;

      const productType = yield server.db.read.row(
        `
                SELECT device_type
                FROM product_type
                WHERE device_type = $1;
            `,
        [productTypeName]
      );
      if (!productType) {
        return next(new restify.NotFoundError('Device type not found'));
      }

      assetPackage.name = assetPackageParams.name;
      assetPackage.deviceType = productTypeName;

      const updateQuery = `
                UPDATE asset_package
                SET name = $1,
                    device_type = $2
                WHERE id = $3
            `;

      yield server.db.write.execute(updateQuery, [
        assetPackage.name,
        productType.deviceType,
        assetPackage.id,
      ]);

      res.send(assetPackage);

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  editAssetPackageInternal: (req, res, next) =>
    co(function* execute() {
      const assetPackageParams = req.body;

      const query = `${selectClause}
                WHERE ap.active = TRUE and ap.id = $1`;

      const assetPackage = yield server.db.read.row(query, [req.params.id]);
      if (!assetPackage) {
        return next(new restify.NotFoundError('Asset Package not found'));
      }

      const productTypeName =
        assetPackageParams.deviceType || assetPackage.deviceType;

      const productType = yield server.db.read.row(
        `
                SELECT device_type
                FROM product_type
                WHERE device_type = $1;
            `,
        [productTypeName]
      );
      if (!productType) {
        return next(new restify.NotFoundError('Device type not found'));
      }

      assetPackage.name = assetPackageParams.name || assetPackage.name;
      assetPackage.deviceType = productTypeName;
      assetPackage.status = assetPackageParams.status || assetPackage.status;
      assetPackage.signed =
        assetPackageParams.signed == null
          ? assetPackage.signed
          : assetPackageParams.signed;

      const updateQuery = `
                UPDATE asset_package
                SET name = $1,
                    device_type = $2,
                    signed = $3,
                    status = $4
                WHERE id = $5
            `;

      yield server.db.write.execute(updateQuery, [
        assetPackage.name,
        productType.deviceType,
        assetPackage.signed,
        assetPackage.status,
        assetPackage.id,
      ]);

      res.send(assetPackage);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  deleteAssetPackage: (req, res, next) =>
    co(function* execute() {
      const query = `SELECT id FROM asset_package ap
                WHERE id = $1 AND active = TRUE AND company = $2`;

      const assetPackage = yield server.db.read.row(query, [
        req.params.id,
        req.user.company.id,
      ]);
      if (!assetPackage) {
        return next(new restify.NotFoundError('Asset Package not found'));
      }

      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');

        // mark package as deleted
        const updateAssetPackageQuery = `
                    UPDATE asset_package
                    SET active = false
                    WHERE id = $1
                `;
        yield connection.execute(updateAssetPackageQuery, [req.params.id]);
        req.log.info(`Deleted asset_package ${req.params.id}`);

        // mark associated assets as deleted
        const updateAssetsQuery = `
                    UPDATE asset
                    SET active = false
                    WHERE asset_package = $1
                `;
        yield connection.execute(updateAssetsQuery, [req.params.id]);
        req.log.info(`Deleted assets from package ${req.params.id}`);

        yield connection.execute('COMMIT');
        connection.done();
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        throw err;
      }

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
