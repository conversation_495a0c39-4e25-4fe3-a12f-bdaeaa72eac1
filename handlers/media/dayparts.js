const uuid = require('uuid/v4');
const co = require('co');
const restify = require('restify');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');

function getDayPartHelper(id, company) {
  return co(function* execute() {
    const result = yield server.db.read.row(
      `
          SELECT
              id,
              name,
              start,
              "end"
          FROM day_part
          WHERE id = $1
          AND company = $2
        ;`,
      [id, company]
    );
    return result;
  }).catch(err => {
    throw err;
  });
}

function validateMinuteValue(time, timeStr) {
  const hrs = time.getUTCHours();
  const mins = time.getMinutes();
  const isMinuteValueValid = /^(0|30)$/g.test(mins);
  const isElevenFiftyNinePM = hrs === 23 && mins === 59;
  // if daypart END time, valid values are:
  // 11:59PM, HH:00, HH:30
  if (
    !isMinuteValueValid &&
    timeStr.toUpperCase() === 'END' &&
    !isElevenFiftyNinePM
  ) {
    return `${timeStr} time should be: HH:00, HH:30 or 11:59PM`;
  }
  // daypart START time
  if (!isMinuteValueValid && timeStr.toUpperCase() !== 'END') {
    return `${timeStr} time's minute value should be either 00 or 30`;
  }
  return null;
}

module.exports = {
  /**
   * Get Daypart by ID
   */
  getDayPartByID: (req, res, next) =>
    co(function* execute() {
      const daypartId = req.params.id;
      const { user } = req;

      // Get dayparts for a specific company
      const daypart = yield getDayPartHelper(daypartId, user.company.id);
      if (!daypart) {
        return next(new restify.NotFoundError('Day part not found'));
      }
      res.send(daypart);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Get ALL Dayparts
   */
  getAllDayParts: (req, res, next) =>
    co(function* execute() {
      const company = req.user.company.id;
      const params = [company];

      let where = '';
      let index = 1;

      const { active } = req.query;
      if (active !== undefined) {
        where = `${where} AND active = $${(index += 1)}`;
        params.push(active);
      }

      // Get daypart belonging to the user's company
      const dayparts = yield server.db.read.rows(
        `
              SELECT id, name, start, "end", active
              FROM day_part
              WHERE
                company = $1
                ${where}
              ORDER BY start ASC;
            `,
        params
      );

      res.send(dayparts);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Create Daypart
   */
  createDayPart: (req, res, next) => {
    const id = uuid();
    const daypart = req.body;

    if (daypart.start >= daypart.end) {
      return next(
        new restify.BadRequestError(
          'Start time cannot be greater than end time'
        )
      );
    }
    // accept only start/end time with minute value equal to 00 or 30
    const invalidSTime = validateMinuteValue(new Date(daypart.start), 'Start');
    const invalidETime = validateMinuteValue(new Date(daypart.end), 'End');
    if (invalidSTime) {
      return next(new restify.BadRequestError(invalidSTime));
    }
    if (invalidETime) {
      return next(new restify.BadRequestError(invalidETime));
    }

    return co(function* execute() {
      req.log.info('Creating day parts');

      // Get company id of the user
      const company = req.user.company.id;

      // Using .rows instead of .row so it won't throw `More than one row found`.
      // It's possible that multiple dayparts with the same name were created before this change
      const sameNamesExist = yield server.db.read.rows(
        `
                SELECT * FROM day_part
                WHERE company = $1 AND name = $2 AND active = true
            `,
        [company, daypart.name]
      );

      if (sameNamesExist.length > 0) {
        return next(
          new restify.BadRequestError(
            `Daypart name "${req.body.name}" already in use`
          )
        );
      }

      // Inserting day parts

      yield server.db.write.execute(
        `
              INSERT INTO day_part (id, company, name, start, "end")
                  VALUES ($1, $2, $3, $4, $5)
            ;`,
        [id, company, daypart.name, daypart.start, daypart.end]
      );

      daypart.id = id;

      res.send(daypart);

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  /**
   * Update Daypart
   */
  updateDayPart: (req, res, next) => {
    const dayPartId = req.params.id;
    const company = req.user.company.id;
    const daypart = req.body;

    return co(function* execute() {
      req.log.info('Updating day part');

      // Check if daypart exists
      const exists = yield getDayPartHelper(dayPartId, company);

      if (!exists) {
        return next(new restify.NotFoundError('Daypart not found'));
      }
      // accept only start/end time with minute value equal to 00 or 30
      const invalidSTime = validateMinuteValue(
        new Date(daypart.start),
        'Start'
      );
      const invalidETime = validateMinuteValue(new Date(daypart.end), 'End');
      if (invalidSTime) {
        return next(new restify.BadRequestError(invalidSTime));
      }
      if (invalidETime) {
        return next(new restify.BadRequestError(invalidETime));
      }

      // Perform the Update
      yield server.db.write.execute(
        `
              UPDATE day_part
              SET name = $1,
                  start = $2,
                  "end" = $3
              WHERE id = $4
              AND company = $5
            ;`,
        [daypart.name, daypart.start, daypart.end, dayPartId, company]
      );

      daypart.id = dayPartId;

      res.send(daypart);

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },

  /**
   * Delete Daypart
   */
  deleteDayPart: (req, res, next) => {
    const dayPartId = req.params.id;
    const company = req.user.company.id;

    return co(function* execute() {
      req.log.info('Deleting day part');

      // Check if daypart exists
      const dayPart = yield getDayPartHelper(dayPartId, company);

      if (!dayPart) {
        return next(new restify.NotFoundError('Daypart not found'));
      }

      const countObj = yield server.db.read.row(
        `
                SELECT COUNT(1) FROM prompt p
                    JOIN prompt_set ps on p.prompt_set = ps.id
                    WHERE 
                        p.day_part = $1 AND
                        ps.active;
            `,
        [dayPartId]
      );

      if (countObj.count > 0) {
        return next(
          new restify.ConflictError(
            'Cannot delete a daypart that is associated to a prompt'
          )
        );
      }

      // Perform delete
      yield server.db.write.execute(
        `
                UPDATE day_part
                SET active = false
                WHERE
                    id = $1 AND
                    company = $2;
            `,
        [dayPartId, company]
      );

      res.send(204, 'Deleted');

      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
};
