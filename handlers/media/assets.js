const stream = require('stream');
const formidable = require('formidable');
const co = require('co');
const restify = require('restify');
const request = require('request');
const uuid = require('uuid/v4');
const _ = require('lodash');
const fontkit = require('fontkit');

const env = require('../../env');
const { server } = require('../../app');
const AWS = require('../../lib/aws');
const constants = require('../../lib/app-constants');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');

function getType(filename, mimetype) {
  function endsWith(string, fileExtensions) {
    return _.some(fileExtensions, fileExtension =>
      string.toLowerCase().endsWith(fileExtension)
    );
  }

  if (
    constants.assetMimeTypes.IMAGE.includes(mimetype) &&
    endsWith(filename, constants.assetFileExtensions.IMAGE)
  ) {
    return constants.assetType.IMAGE;
  }
  if (
    constants.assetMimeTypes.VIDEO.includes(mimetype) &&
    endsWith(filename, constants.assetFileExtensions.VIDEO)
  ) {
    return constants.assetType.VIDEO;
  }
  if (
    constants.assetMimeTypes.FONT.includes(mimetype) &&
    endsWith(filename, constants.assetFileExtensions.FONT)
  ) {
    return constants.assetType.FONT;
  }

  return false;
}

function cleanupAsset(asset) {
  if (asset.package.id === null) {
    delete asset.package; // eslint-disable-line no-param-reassign
  }

  // mask the s3 urls
  if (asset.thumbnailUrl) {
    asset.thumbnailUrl = `${env.config.url.api}/media/assets/${asset.id}/thumbnail`; // eslint-disable-line no-param-reassign
  }

  if (asset.sourceUrl) {
    asset.sourceUrl = `${env.config.url.api}/media/assets/${asset.id}/source`; // eslint-disable-line no-param-reassign
  }

  return asset;
}

const selectAssets = `
        SELECT
          a.id,
          a.name,
          a.width,
          a.height,
          a.status,
          a.size,
          a.type,
          a.uploaded,
          a.company,
          a.thumbnail_file_url as thumbnail_url,
          a.source_file_url as source_url,
          (
              SELECT json_build_object(
                  'id', u.id,
                  'fullName', u.full_name,
                  'company', json_build_object(
                      'id', c.id,
                      'name', c.name
                  )
              )
          ) as uploader,
          (
              SELECT row_to_json(_) FROM (select p.id, p.name, p.signed ) as _
          ) as package,
          a.properties,
          a.active
        FROM asset as a
            JOIN ics_user u on u.id = a.uploaded_by
            LEFT JOIN company c ON c.id = u.company_id
            LEFT JOIN asset_package p on p.id = a.asset_package`;

module.exports = {
  uploadAsset: (req, res, next) => {
    const form = new formidable.IncomingForm();

    form.on('field', (name, value) => {
      // prevent assetNames shorter than 3 characters
      if (name === 'assetName' && value && value.length < 3) {
        return next(new restify.BadRequestError('assetName too short'));
      }
      return (req.params[name] = value); // eslint-disable-line
    });
    // eslint-disable-next-line consistent-return
    form.onPart = async part => {
      let size = 0;

      part.on('data', buffer => {
        size += buffer.length;
      });

      if (!part.filename) {
        // let formidable handle all non-file parts
        form.handlePart(part);
      } else {
        const s3stream = new stream.PassThrough();
        part.pipe(s3stream);
        s3stream.on('error', errorHandler.onError(req, res, next));
        try {
          const id = uuid();
          const fileName = part.filename.replaceAll(' ', '');
          const mimeType = part.mime;
          const type = getType(fileName, mimeType);
          req.log.info(
            { assetName: fileName, assetId: id },
            `[Media].[UploadAsset] Try to upload to S3: ${fileName}.`
          );

          if (!type) {
            return next(new restify.BadRequestError('Unknown file type'));
          }

          const { bucket } = env.config.AWS.S3;
          const key = `uploads/${id}/${fileName}`;
          const body = s3stream;
          const acl = 'public-read';

          // Generate properties based on assetType
          const properties = await (() =>
            // eslint-disable-next-line consistent-return
            new Promise(resolve => {
              if (type === constants.assetType.FONT) {
                const fontDataBufferArr = [];
                const fontStream = new stream.PassThrough();
                fontStream.on('error', errorHandler.onError(req, res, next));
                part.pipe(fontStream);
                fontStream.on('data', bufferPart => {
                  fontDataBufferArr.push(bufferPart);
                });

                fontStream.on('end', () => {
                  try {
                    const fontBuffer = Buffer.concat(fontDataBufferArr);
                    const font = fontkit.create(fontBuffer);

                    // It is recommended to use preferredFamily when it is available for the font
                    // E.g Saria SemiBold will have preferredFamily of Saria and preferredSubfamily of SemiBold
                    // Its family is Saira SemiBold, subfamilyis Regular
                    const fontFamilyName = font.familyName;
                    const fontSubfamilyName = font.subfamilyName;

                    const fontfaceName =
                      font.fullName ||
                      (font.familyName && font.subfamilyName
                        ? `${font.familyName} ${font.subfamilyName}`
                        : font.postscriptName ||
                          fileName.split('.').slice(0, -1).join('.'));

                    return resolve({
                      mimeType,
                      ...{
                        postscriptName: font.postscriptName,
                        fullName: font.fullName,
                        familyName: fontFamilyName,
                        subfamilyName: fontSubfamilyName,
                        copyright: font.copyright,
                        version: font.version,
                        face: fontfaceName,
                      },
                    });
                  } catch (e) {
                    return next(
                      new restify.BadRequestError('Unknown font format')
                    );
                  }
                });
              } else {
                resolve({ mimeType });
              }
            }))();

          const uploadResults = await AWS.uploadToS3(bucket, key, body, acl);
          const created = new Date().toISOString();

          // by this time, req.params.assetName is supposed to be populated because S3 has finished uploading
          if (!req.params.assetName) {
            return next(new restify.BadRequestError('assetName required'));
          }

          await server.db.write.execute(
            'INSERT INTO asset (id, company, name, filename, source_file_url, uploaded, status, type, uploaded_by, properties, size )' +
              ' VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)',
            [
              id,
              req.user.company.id,
              req.params.assetName,
              fileName,
              uploadResults.Location,
              created,
              constants.assetStatus.NEW,
              type,
              req.user.sub,
              properties,
              size,
            ]
          );
          const company = await server.db.read.row(
            'select id, name from company where id = $1',
            [req.user.company.id]
          );
          const uploader = await server.db.read.row(
            'select id, full_name from ics_user where id = $1',
            [req.user.sub]
          );
          const response = {
            id,
            name: req.params.assetName,
            company,
            type,
            sourceUrl: uploadResults.Location,
            package: null,
            status: constants.assetStatus.NEW,
            uploaded: created,
            uploader,
            active: true,
            properties,
          };
          req.log.info(
            { assetName: fileName, assetId: id },
            `[Media].[UploadAsset] Upload to S3 success: ${fileName}.`
          );
          res.send(response, 200);
          return next();
        } catch (err) {
          return errorHandler.onError(req, res, next)(err);
        }
      }
    };

    form.parse(req);
  },
  createAssetInternal: (req, res, next) =>
    co(function* execute() {
      const id = uuid();
      const asset = req.body;
      const created = new Date().toISOString();

      const filename = asset.sourceUrl.split('/').pop();

      // check that a package exists
      let assetPackage = null;
      if (asset.package) {
        assetPackage = yield server.db.read.row(
          'SELECT * FROM asset_package WHERE id = $1',
          [asset.package]
        );
        if (!assetPackage) {
          return next(new restify.NotFoundError('Package not found'));
        }
      }

      // check that the company exists
      const company = yield server.db.read.row(
        'SELECT * FROM company WHERE id = $1',
        [asset.company]
      );
      if (!company) {
        return next(new restify.NotFoundError('Company not found'));
      }

      const createAssetQuery = `
                INSERT INTO asset (
                    id,
                    company,
                    name,
                    filename,
                    source_file_url,
                    thumbnail_file_url,
                    asset_package,
                    uploaded,
                    status,
                    width,
                    height,
                    size,
                    type,
                    uploaded_by
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            `;

      yield server.db.write.execute(createAssetQuery, [
        id,
        asset.company,
        asset.name,
        filename,
        asset.sourceUrl,
        asset.thumbnailUrl,
        asset.package,
        created,
        asset.status,
        asset.width,
        asset.height,
        asset.size,
        asset.type,
        assetPackage.uploadedBy,
      ]);

      const createdAsset = yield server.db.read.row(
        `${selectAssets} WHERE a.id = $1`,
        [id]
      );

      res.send(createdAsset);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
  getAssets: (req, res, next) => {
    const pageParams = paginationHelper.parsePaginationParams(req);

    function prepareWhere(r) {
      let idx = 1;
      let where = '';
      const params = [];

      function addWhereClause(whereNew, param) {
        where = `${where} ${whereNew}`;
        idx += 1;
        params.push(param);
      }

      addWhereClause(`WHERE a.company = $${idx}`, r.user.company.id);

      let activeValue = 'TRUE';
      if (r.query.active !== undefined) {
        activeValue = r.query.active;
      }
      if (r.query.active !== 'BOTH') {
        addWhereClause(`AND a.active = $${idx}`, activeValue);
      }

      if (r.query.name) {
        addWhereClause(
          `AND LOWER(a.name) LIKE LOWER('%'||$${idx}||'%')`,
          r.query.name
        );
      }

      if (r.query.type) {
        switch (r.query.type) {
          case 'IMAGE_STRICT':
            addWhereClause(
              `AND a.type = $${idx} AND a.filename NOT LIKE '%.gif'`,
              'IMAGE'
            );
            break;
          case 'IMAGE_GIF':
            addWhereClause(
              `AND a.type = $${idx} AND a.filename LIKE '%.gif'`,
              'IMAGE'
            );
            break;
          default:
            addWhereClause(`AND a.type = $${idx}`, r.query.type);
        }
      }

      if (r.query.signed !== undefined) {
        const signed = JSON.parse(r.query.signed); // Value is passed as 'true' or 'false'
        if (signed) {
          where = `${where} AND p.signed = TRUE`;
        } else {
          where = `${where} AND (a.asset_package IS NULL OR p.signed = FALSE)`;
        }
      }

      if (r.query.status) {
        addWhereClause(`AND a.status = $${idx}`, r.query.status);
      }

      if (r.query.width) {
        addWhereClause(`AND a.width = $${idx}`, r.query.width);
      }

      if (r.query.height) {
        addWhereClause(`AND a.height = $${idx}`, r.query.height);
      }

      if (r.query.uploader) {
        addWhereClause(`AND a.uploaded_by = $${idx}`, r.query.uploader);
      }

      if (r.query.minWidth) {
        addWhereClause(`AND a.width >= $${idx}`, r.query.minWidth);
      }
      if (r.query.minHeight) {
        addWhereClause(`AND a.height >= $${idx}`, r.query.minHeight);
      }
      if (r.query.maxWidth) {
        addWhereClause(`AND a.width <= $${idx}`, r.query.maxWidth);
      }
      if (r.query.maxHeight) {
        addWhereClause(`AND a.height <= $${idx}`, r.query.maxHeight);
      }
      if (r.query.videoExtension && r.query.type === 'VIDEO') {
        addWhereClause(
          `AND LOWER(a.filename) LIKE $${idx}`,
          `%${r.query.videoExtension.toLowerCase()}`
        );
      }
      req.log.info(
        { getAssetsParams: params, companyId: r.user.company.id },
        '[Media][GetAssets] get assets.'
      );

      return {
        value: where,
        params,
      };
    }

    const where = prepareWhere(req);

    return co(function* execute() {
      const queryOrder = req.query.order || 'uploaded'; // this assumes that `order` has been escaped by JOI in the endpoint
      const order =
        queryOrder === 'uploader' ? 'u.full_name' : `a.${queryOrder}`; // use ics_user table's full_name when ordering via creator
      const direction = queryOrder === 'uploaded' ? 'DESC' : 'ASC'; // sort by latest uploaded by default

      const query = `
                ${selectAssets}
                ${where.value}
                ORDER BY ${order} ${direction}, a.id ASC
                LIMIT ${pageParams.pageSize} OFFSET ${pageParams.offset} `;
      const totalObj = yield server.db.read.row(
        `SELECT count(1) FROM asset as a LEFT JOIN asset_package p on p.id = a.asset_package ${where.value}`,
        where.params
      );
      const results = yield server.db.read.rows(query, where.params);

      // data cleanup
      results.forEach(item => {
        cleanupAsset(item);
      });

      res.send({
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results,
      });
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
  getAsset: (req, res, next) =>
    co(function* execute() {
      let companyClause = '';
      let queryParams = [];

      // Allow System Users to get assets regardless of company
      if (req.user.roles.includes('ICS_SYSTEM')) {
        companyClause = '';
        queryParams = [req.params.id];
      } else {
        companyClause = 'AND a.company = $2';
        queryParams = [req.params.id, req.user.company.id];
      }

      const query = `
                ${selectAssets}
                    WHERE a.id = $1 ${companyClause};
            `;

      const result = yield server.db.read.row(query, queryParams);

      if (!result) {
        return next(
          new restify.NotFoundError(`Asset ${req.params.id} not found`)
        );
      }

      // System Users get the raw record from db, no url masking for images
      if (req.user.roles.includes('ICS_SYSTEM')) {
        res.send(result);
      } else {
        res.send(cleanupAsset(result));
      }

      return next();
    }).catch(errorHandler.onError(req, res, next)),
  updateAsset: (req, res, next) =>
    co(function* execute() {
      const asset = req.body;

      const query = `
                ${selectAssets}
                    WHERE a.id = $1 AND a.company = $2
            `;

      const assetToUpdate = yield server.db.read.row(query, [
        req.params.id,
        req.user.company.id,
      ]);

      if (!assetToUpdate) {
        return next(
          new restify.NotFoundError(`Asset ${req.params.id} not found`)
        );
      }

      const updateQuery = `
                UPDATE asset SET
                name = $1
                WHERE id = $2;
            `;

      yield server.db.write.execute(updateQuery, [asset.name, req.params.id]);

      const updatedAsset = yield server.db.read.row(query, [
        req.params.id,
        req.user.company.id,
      ]);

      res.send(cleanupAsset(updatedAsset));

      return next();
    }).catch(errorHandler.onError(req, res, next)),
  deleteAsset: (req, res, next) =>
    co(function* execute() {
      const query = `
                ${selectAssets}
                    WHERE a.id = $1 AND a.company = $2
            `;

      const assetToDelete = yield server.db.read.row(query, [
        req.params.id,
        req.user.company.id,
      ]);

      if (!assetToDelete) {
        return next(
          new restify.NotFoundError(`Asset ${req.params.id} not found`)
        );
      }

      const updateQuery = `
                UPDATE asset SET
                active = false
                WHERE id = $1;
            `;
      yield server.db.write.execute(updateQuery, [req.params.id]);
      res.send(204, 'Asset deleted');
      return next();
    }).catch(errorHandler.onError(req, res, next)),
  updateAssetInternal: (req, res, next) =>
    co(function* execute() {
      const asset = req.body;

      const query = `
                ${selectAssets}
                    WHERE a.id = $1
            `;

      const assetToPatch = yield server.db.read.row(query, [req.params.id]);

      if (!assetToPatch) {
        return next(new restify.NotFoundError('Asset not found'));
      }

      if (asset.package) {
        const assetPackage = yield server.db.read.row(
          `SELECT * FROM asset_package
                    WHERE id = $1 AND company = $2`,
          [asset.package, assetToPatch.company]
        );
        if (!assetPackage) {
          return next(new restify.NotFoundError('Package not found'));
        }
      }

      // try to update asset
      const updateQuery = `
                UPDATE asset
                SET name = $1,
                    thumbnail_file_url = $2,
                    source_file_url = $3,
                    asset_package = $4,
                    width = $5,
                    height = $6,
                    status = $7,
                    size = $8
                WHERE
                    id = $9
            `;
      yield server.db.write.execute(updateQuery, [
        asset.name,
        asset.thumbnailUrl,
        asset.sourceUrl,
        asset.package,
        asset.width,
        asset.height,
        asset.status,
        asset.size,
        req.params.id,
      ]);

      const updatedAsset = yield server.db.read.row(query, [req.params.id]);
      res.send(updatedAsset);

      return next();
    }).catch(errorHandler.onError(req, res, next)),
  getAssetThumbnail: (req, res, next) =>
    co(function* execute() {
      const asset = yield server.db.read.row(
        `
                SELECT
                  a.thumbnail_file_url
                FROM asset a
                WHERE a.id = $1
                `,
        [req.params.id]
      );

      if (!asset) {
        return next(
          new restify.NotFoundError(`Asset ${req.params.id} not found`)
        );
      }

      if (!asset.thumbnailFileUrl) {
        return next(new restify.NotFoundError('Asset has no thumbnail'));
      }

      // Pipe image to res
      try {
        request.get(asset.thumbnailFileUrl).pipe(res);
      } catch (err) {
        return next(new restify.NotFoundError('Invalid thumbnail URI'));
      }

      return next();
    }).catch(errorHandler.onError(req, res, next)),
  getAssetSource: (req, res, next) =>
    co(function* execute() {
      const asset = yield server.db.read.row(
        `
                SELECT
                  a.source_file_url
                FROM asset a
                WHERE a.id = $1
              `,
        [req.params.id]
      );

      if (!asset) {
        return next(
          new restify.NotFoundError(`Asset ${req.params.id} not found`)
        );
      }

      // this is very unlikely
      if (!asset.sourceFileUrl) {
        return next(new restify.NotFoundError('Asset has no image source'));
      }

      // Pipe image to res
      try {
        request.get(asset.sourceFileUrl).pipe(res);
      } catch (err) {
        return next(new restify.NotFoundError('Invalid source URI'));
      }

      return next();
    }).catch(errorHandler.onError(req, res, next)),
  getAssetsPublishStatus: async (req, res, next) => {
    try {
      const publishStatusAsset = await server.db.read.row(
        `
            SELECT
            (SELECT a.name FROM asset a WHERE a.id::text = $1),
            COUNT(CASE WHEN ps.status = 'PUBLISHED' THEN ps.id END ) as PUBLISHED,
            COUNT(CASE WHEN ps.status != 'PUBLISHED' THEN ps.id END ) as UNPUBLISHED
          FROM prompt_set ps
          JOIN prompt p ON ps.id = p.prompt_set
          WHERE jsonb_typeof(elements) = 'array' AND EXISTS(
            SELECT *
            FROM jsonb_array_elements(elements) as item
            WHERE ( item ->> 'value' = $1 or item ->> 'face' = $1 )
          )`,
        [req.params.id]
      );

      if (!publishStatusAsset.name) {
        return next(
          new restify.NotFoundError(`Asset ${req.params.id} not found`)
        );
      }
      res.send(200, publishStatusAsset);
      return next();
    } catch (e) {
      return next(new restify.InternalServerError(e)); // 500
    }
  },
};
