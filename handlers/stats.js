const co = require('co');

const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');

module.exports = {
  /**
   * Get Statistics listing site-count and device-count for all companies
   */
  getStatistics: (req, res, next) =>
    co(function* execute() {
      const counts = yield server.db.read.rows(`
                SELECT c.name as company_name,
                   (
                     SELECT COUNT(*)
                      FROM site s
                      WHERE s.company_id=c.id AND s.active=true
                  ) AS site_count,
                  (
                     SELECT COUNT(t.target_id)
                      FROM target t, site s
                      WHERE t.site_id=s.site_id AND c.id=s.company_id AND t.active=true
                  ) AS device_count
                FROM company c
                GROUP BY c.id
                ORDER BY c.name ASC
          `);
      res.send(counts);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
