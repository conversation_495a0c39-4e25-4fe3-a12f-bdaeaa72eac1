const co = require('co');
const restify = require('restify');

const { server } = require('../../app');
const errorHandler = require('../../lib/errorhandler');
const paginationHelper = require('../../helpers/pagination-helper');

module.exports = {
  getReleases: (req, res, next) =>
    co(function* execute() {
      const pageParams = paginationHelper.parsePaginationParams(req);

      const results = yield server.db.read.rows(
        `
                SELECT
                    release,
                    release_date,
                    title,
                    description,
                    updated_by,
                    visible,
                    date_created,
                    date_updated
                FROM ics_releases
                ORDER BY release DESC
                LIMIT $1
                OFFSET $2;
            `,
        [pageParams.pageSize, pageParams.offset]
      );

      const totalObj = yield server.db.read.row(
        'SELECT COUNT(1) FROM ics_releases',
        []
      );

      res.send({
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results,
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  getReleaseById: (req, res, next) =>
    co(function* execute() {
      const { release } = req.params;

      const result = yield server.db.read.row(
        `
                SELECT release,
                    release_date,
                    title,
                    description,
                    updated_by,
                    visible,
                    date_created,
                    date_updated
                FROM ics_releases
                WHERE
                    release = $1;
            `,
        [release]
      );

      if (!result) {
        return next(new restify.errors.NotFoundError('Release not found'));
      }

      res.send(result);

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  searchReleases: (req, res, next) =>
    co(function* execute() {
      const pageParams = paginationHelper.parsePaginationParams(req);

      const { release } = req.query;
      const { title } = req.query;
      const { description } = req.query;
      const { updatedBy } = req.query;

      let where = '';
      let index = 0;
      const params = [];

      if (release) {
        where = `WHERE release LIKE $${(index += 1)}`;
        params.push(`%${release}%`);
      }

      if (title) {
        where += ` ${
          where ? 'OR' : 'WHERE'
        } LOWER(title) LIKE $${(index += 1)}`;
        params.push(`%${title.toLowerCase()}%`);
      }

      if (description) {
        where += ` ${
          where ? 'OR' : 'WHERE'
        } LOWER(description) LIKE $${(index += 1)}`;
        params.push(`%${description.toLowerCase()}%`);
      }

      if (updatedBy) {
        where += ` ${
          where ? 'OR' : 'WHERE'
        } LOWER(updated_by) LIKE $${(index += 1)}`;
        params.push(`%${updatedBy.toLowerCase()}%`);
      }

      const results = yield server.db.read.rows(
        `
                SELECT
                    release,
                    release_date,
                    title,
                    description,
                    updated_by,
                    date_created,
                    date_updated,
                    visible
                FROM ics_releases
                ${where}
                ORDER BY release DESC
                LIMIT $${(index += 1)}
                OFFSET $${(index += 1)};
            `,
        [...params, pageParams.pageSize, pageParams.offset]
      );

      const totalObj = yield server.db.read.row(
        `
                SELECT COUNT(1)
                FROM ics_releases
                ${where};
            `,
        params
      );

      res.send({
        resultsMetadata: {
          totalResults: totalObj.count,
          pageIndex: pageParams.pageIndex,
          pageSize: pageParams.pageSize,
        },
        results,
      });

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  createRelease: (req, res, next) =>
    co(function* execute() {
      const release = yield server.db.read.row(
        `
                SELECT release
                FROM ics_releases
                WHERE
                    release = $1;
            `,
        [req.body.release]
      );

      if (release) {
        return next(
          new restify.errors.BadRequestError(
            `Release ${release.release} already exists`
          )
        );
      }

      const result = yield server.db.write.row(
        `
                INSERT INTO ics_releases 
                    ( release, release_date, title, description, visible, updated_by, date_created, date_updated, user_guide )
                VALUES
                    ( $1, $2, $3, $4, $5, $6, NOW(), NOW(), $7 )
                RETURNING
                    release, release_date, title, description, visible, updated_by, date_created, date_updated, user_guide;
            `,
        [
          req.body.release,
          req.body.releaseDate,
          req.body.title,
          req.body.description,
          req.body.visible,
          req.body.updatedBy,
          req.body.userGuide,
        ]
      );

      res.send(result);

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  updateRelease: (req, res, next) =>
    co(function* execute() {
      const release = yield server.db.read.row(
        `
                SELECT release, visible
                FROM ics_releases
                WHERE
                    release = $1;
            `,
        [req.params.release]
      );

      if (!release) {
        return next(new restify.errors.NotFoundError('Release not found'));
      }

      if (!release.visible) {
        return next(new restify.errors.BadRequestError('Release not visible'));
      }

      const result = yield server.db.write.row(
        `
                UPDATE ics_releases SET 
                    release_date = $2, 
                    title = $3, 
                    description = $4, 
                    visible = $5, 
                    updated_by = $6, 
                    date_updated = NOW(),
                    user_guide = $7
                WHERE
                    release = $1
                RETURNING
                    release, release_date, title, description, visible, updated_by, date_created, date_updated, user_guide;
            `,
        [
          req.params.release,
          req.body.releaseDate,
          req.body.title,
          req.body.description,
          req.body.visible,
          req.body.updatedBy,
          req.body.userGuide,
        ]
      );

      res.send(result);

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  deleteRelease: (req, res, next) =>
    co(function* execute() {
      const release = yield server.db.read.row(
        `
                SELECT release, visible
                FROM ics_releases
                WHERE
                    release = $1;
            `,
        [req.params.release]
      );

      if (!release) {
        return next(new restify.errors.NotFoundError('Release not found'));
      }

      if (!release.visible) {
        return next(new restify.errors.BadRequestError('Release not visible'));
      }

      yield server.db.write.row(
        `
                UPDATE ics_releases SET
                    visible = FALSE,
                    date_updated = NOW()
                WHERE 
                    release = $1;
            `,
        [req.params.release]
      );

      res.send(204);

      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
