const restify = require('restify');
const co = require('co');

const moment = require('moment');
const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');
const mailer = require('../lib/mailer');
const mailerHelper = require('../helpers/mailer-helper');
const hasher = require('../lib/hash-password');
const constants = require('../lib/app-constants');
const usersHelper = require('../helpers/users-helper');
const { config } = require('../env');

const insertEmailToken = `INSERT INTO email_token(user_id, type, value, expires)
    VALUES ($1, $2, $3, (NOW() + interval '6 hours'));`;

module.exports = {
  /**
   * Function to reset a password. It takes an email in the request body and checks if it exists and is active
   * If it is it will send the user an email with a token in a url to reset the password
   */
  forgotPassword: (req, res, next) =>
    co(function* execute() {
      const { email } = req.body;
      // Check if the user exists
      const user = yield usersHelper.findUserByEmail(email);
      // If the user does not exist then stop, user shouldn't know that they don't exist though.
      // So return 204 still
      if (!user || !user.id) {
        req.log.info(
          `Email ${req.body.email} does not exits. Cannot reset password`
        );
        res.send(204);
        return next();
      }
      if (user.lastLocked) {
        const lastLocked = moment.utc(user.lastLocked);
        const currentTime = moment.utc();
        const differenceInMinutes = currentTime.diff(lastLocked, 'minutes');
        const data = {
          failedStatus: true,
          message:
            'Your account has been locked due to multiple failed login attempts. Please try again in 30 minutes or contact your company admin',
        };
        if (differenceInMinutes <= (config.auth.lockedUserTimeFrame || 30)) {
          res.send(403, data);
          return next();
        }
      }

      // Generate a random sting for the token
      const token = hasher.generatePlainPassword(64);

      // Save the password reset into the database

      // Get a connection for transaction control
      const connection = yield server.db.write.getConnection();
      try {
        // Start transaction
        yield connection.execute('BEGIN');

        // Insert the email token into the database
        yield connection.execute(insertEmailToken, [
          user.id,
          constants.emailTokenType.FORGOT_PASSWORD,
          token,
        ]);

        // Commit transaction
        yield connection.execute('COMMIT');
        connection.done();

        req.log.info(
          `Password reset token saved to database for user ${user.id}`
        );
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        connection.done();
        return next(new restify.InternalServerError(err));
      }

      // Send email with the random string as the url
      const { senderEmail } = yield mailerHelper.getSenderEmailsByCompanyId(
        user.companyId
      );
      yield mailer.sendPasswordReset(
        { to: email, from: senderEmail },
        token,
        user.companyId
      );
      req.log.info(
        `Email notification for password reset token sent to user ${user.id}`
      );

      res.send(204);

      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
