const restify = require('restify');
const { isEmpty } = require('lodash');
const { server } = require('../app');
const { translateDeviceHealthToNumber } = require('../helpers/device-helper');
const constants = require('../lib/app-constants');
const errorHandler = require('../lib/errorhandler');

const {
  deviceOnboardingAdapter,
} = require('../helpers/deviceOnboarding-helper');
const { BridgeHelper } = require('../helpers/bridge-helper');

const bridgeHelper = new BridgeHelper(server.db, server.log);

const validateProductTypeIdAndSiteId = async ({
  productTypeId,
  siteId,
  logger,
}) => {
  if (!productTypeId || !siteId) {
    logger.error(
      { productTypeId, siteId },
      'productTypeId and siteId are required'
    );
    return {
      error: new restify.BadRequestError(
        'productTypeId and siteId are required'
      ),
    };
  }
  const bridgeSpecificTypeData =
    await deviceOnboardingAdapter.getBridgeSpecificTypeData(productTypeId);
  const bridgeDeviceType = bridgeSpecificTypeData?.product_type || null;
  if (isEmpty(bridgeDeviceType)) {
    logger.error(
      { bridgeSpecificTypeData, bridgeDeviceType, siteId },
      `ProductType with id (${productTypeId}) not found`
    );
    return {
      error: new restify.NotFoundError(
        `ProductType with id (${productTypeId}) not found`
      ),
    };
  }
  const site = await bridgeHelper.validateSiteExistence(siteId);
  if (!site) {
    logger.error(
      { bridgeSpecificTypeData, site, productTypeId },
      `Site with id (${siteId}) not found`
    );
    return {
      error: new restify.NotFoundError(`Site with id (${siteId}) not found`),
    };
  }
  return { bridgeDeviceType, bridgeSpecificTypeData, site };
};

const createDevice = async (req, res, next) => {
  const { body } = req;
  const {
    certificate,
    isJsonCertificates,
    data,
    name,
    productTypeId,
    serialNumber,
    siteId,
    description,
    macAddress,
  } = body;
  const userId = req.user.sub;
  try {
    req.log.info(`bridge is starting to create device of ${serialNumber}`);
    const windowEnd = new Date();
    windowEnd.setFullYear(windowEnd.getFullYear() + 20);
    const { bridgeDeviceType, bridgeSpecificTypeData, site, error } =
      await validateProductTypeIdAndSiteId({
        productTypeId,
        siteId,
        logger: req.log,
      });
    if (error) {
      return next(error);
    }
    const { companyId } = site;

    const isDeviceExist = await bridgeHelper.validateDeviceExistence(
      serialNumber,
      bridgeDeviceType
    );
    if (isDeviceExist) {
      req.log.error(
        { body, bridgeDeviceType, siteId },
        `Another target found with serial number (${serialNumber})`
      );
      return next(
        new restify.ConflictError(
          `Another target found with serial number (${serialNumber})`
        )
      );
    }

    const deviceType = await bridgeHelper.getCompanyProductType(
      bridgeDeviceType,
      companyId
    );

    if (!deviceType) {
      req.log.error(
        { body, bridgeDeviceType, companyId },
        `Target company does not support the device type (${bridgeDeviceType})`
      );
      return next(
        new restify.NotFoundError(
          `Target company does not support the device type (${bridgeDeviceType})`
        )
      );
    }

    const deviceParams = [
      siteId,
      null,
      '777',
      null,
      serialNumber.toUpperCase(),
      name,
      description,
      serialNumber.toUpperCase(),
      true,
      windowEnd,
      certificate,
      isJsonCertificates,
      macAddress,
      data,
      constants.devices.STATUS.INACTIVE,
      constants.devices.PRESENCE.PRESENT,
      null,
      deviceType,
      null,
      userId,
    ];

    const INSERT_DEVICE = `
              INSERT INTO target (site_id, password, version, last_registered, registration_key,
                  name, description, serial_number, active, registration_window_end,
                  certificate, is_json_certificates, mac_address, data, status, presence, key_group_ref, device_type, last_rki, updated_by)
              VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
              RETURNING target_id, serial_number, last_contact, last_registered, name, description, site_id,
                  get_device_health( target_id ) as status;`;

    const insertResult = await server.db.write.execute(
      INSERT_DEVICE,
      deviceParams
    );

    if (insertResult && insertResult.length && insertResult[0].targetId) {
      req.log.info(
        `Target id [${insertResult[0].targetId}] with serial number ${insertResult[0].serialNumber} is created by ${req.user.email}`
      );
      insertResult[0].status = translateDeviceHealthToNumber(
        insertResult[0].status
      );
      const { targetId: id } = insertResult[0];
      const device = {
        id,
        siteName: site.name,
        productType: bridgeSpecificTypeData.legacy || null,
        keyGroupRef: null,
        ...insertResult[0],
      };
      if (isJsonCertificates) {
        await bridgeHelper.processIssuerCode(certificate);
      }
      res.send(bridgeHelper.pruneResponseData(device));
      return next();
    }
    return next(new restify.InternalServerError('Failed to bridge device'));
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

const putDevice = async (req, res, next) => {
  const { body } = req;
  const { serialNumber } = req.params;
  const {
    certificate,
    isJsonCertificates,
    data,
    name,
    description,
    macAddress,
    productTypeId,
    siteId,
  } = body;
  const userId = req.user.sub;
  let foundDevice;
  let siteName;
  try {
    req.log.info(`bridge is starting looking up device of ${serialNumber}`);
    foundDevice = await bridgeHelper.getDeviceBySN(serialNumber);
    if (!foundDevice) {
      req.body.serialNumber = serialNumber;
      return createDevice(req, res, next);
    }

    req.log.info(`bridge is starting to update device of ${serialNumber}`);

    const isProductTypeIdOnly = productTypeId && !siteId;

    if (!isProductTypeIdOnly) {
      const { bridgeDeviceType, site, error } =
        await validateProductTypeIdAndSiteId({
          productTypeId,
          siteId,
          logger: req.log,
        });
      if (error) {
        return next(error);
      }
      const { companyId } = site;
      const companyDeviceType = await bridgeHelper.getCompanyProductType(
        bridgeDeviceType,
        companyId
      );
      if (companyDeviceType !== foundDevice.deviceType) {
        return next(
          new restify.ConflictError(
            `Device Type ${foundDevice.deviceType} with SN (${serialNumber}) does not match with provided productTypeId ${productTypeId} - ${companyDeviceType}`
          )
        );
      }
    }
    siteName = foundDevice.siteName;

    let deviceParams = [];
    if (foundDevice.presence === 'OUT_OF_INSTANCE') {
      req.log.info(`Update device (${serialNumber}) with Presence UPDATE`);
      deviceParams.push('UPDATE');
    } else {
      deviceParams.push('PRESENT');
    }

    const windowEnd = new Date();
    windowEnd.setFullYear(windowEnd.getFullYear() + 20);

    deviceParams = [
      ...deviceParams,
      ...[
        certificate,
        isJsonCertificates,
        name,
        description,
        data,
        macAddress,
        windowEnd,
        userId,
        serialNumber,
      ],
    ];

    const UPDATE_DEVICE = `
              UPDATE target
              SET presence = $1,
                  key_group_ref = NULL,
                  last_rki = NULL,
                  certificate = $2,
                  is_json_certificates = $3,
                  name = $4,
                  description = $5,
                  data = $6,
                  mac_address = $7,
                  registration_window_end = $8,
                  last_edited_date = NOW(),
                  updated_by = $9
              WHERE serial_number = $10 AND active = TRUE                
              RETURNING 
                  target_id,
                  serial_number,
                  device_type,
                  last_contact,
                  last_registered,
                  name,
                  description,
                  site_id,
                  get_device_health( target_id ) as status;
          `;

    const updateResult = await server.db.write.execute(
      UPDATE_DEVICE,
      deviceParams
    );

    if (updateResult && updateResult.length && updateResult[0].targetId) {
      req.log.info(
        deviceParams,
        `Target id [${updateResult[0].targetId}] with serial number ${updateResult[0].serialNumber} is updated by ${req.user.email}`
      );
      updateResult[0].status = translateDeviceHealthToNumber(
        updateResult[0].status
      );
      const { targetId: id, deviceType } = updateResult[0];

      const deviceTypeLegacyData =
        await deviceOnboardingAdapter.convertLegacyProductType(deviceType);

      const productType = deviceTypeLegacyData || null;

      const device = {
        id,
        siteName,
        productType,
        keyGroupRef: null,
        ...updateResult[0],
      };
      if (isJsonCertificates) {
        await bridgeHelper.processIssuerCode(certificate);
      }
      res.send(bridgeHelper.pruneResponseData(device));
      return next();
    }

    return next(new restify.InternalServerError('Failed to bridge device'));
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

const getDevice = async (req, res, next) => {
  const { serialNumber } = req.params;

  try {
    req.log.info(`bridge is starting to get device of ${serialNumber}`);
    const target = await bridgeHelper.getDeviceBySN(serialNumber);
    if (target) {
      req.log.info(
        `Target id [${target.targetId}] with serial number ${target.serialNumber} is retrieved by ${req.user.email}`
      );
      const { targetId: id, deviceType } = target;
      const deviceTypeLegacyData =
        await deviceOnboardingAdapter.convertLegacyProductType(deviceType);
      const productType = deviceTypeLegacyData || null;
      target.status = translateDeviceHealthToNumber(target.status);
      const device = { ...target, id, productType, keyGroupRef: null };
      res.send(bridgeHelper.pruneResponseData(device));
      return next();
    }
    return next(
      new restify.NotFoundError(`Device with SN (${serialNumber}) not found`)
    );
  } catch (err) {
    return errorHandler.onError(req, res, next)(err);
  }
};

module.exports = { createDevice, putDevice, getDevice };
