const co = require('co');

const { server } = require('../app');
const errorHandler = require('../lib/errorhandler');

module.exports = {
  getRoles: (req, res, next) =>
    co(function* execute() {
      const query = `
                SELECT
                    role_id AS id,
                    role_name AS name,
                    description
                FROM
                    role
                WHERE
                    internal = false;
            `;
      const result = yield server.db.read.rows(query);

      res.send(result);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
