const co = require('co');

const { server } = require('../app');
const siteHelper = require('../helpers/sites-helper');
const errorHandler = require('../lib/errorhandler');

function prepareWhere({ user, company, status, query, tags, siteGroups }) {
  const params = [];
  params.push(user);
  let where = `WHERE u.user_id = $${params.length} AND s.active = true `;

  if (company) {
    // Filter sites by query param `company`
    params.push(company);
    where = `${where} AND s.company_id = $${params.length} `;
  }

  if (status) {
    // Filter sites by query param `status`
    params.push(siteHelper.translateSiteHealthToString(status));
    where = `${where} AND get_site_health( s.site_id ) = $${params.length} `;
  }

  // The search will either contain a query or tags or site groups

  if (query && query.length > 0) {
    const q = `%${query.replace(/([%_])/g, '\\$1').toLowerCase()}%`;
    params.push(q);
    where = `
            ${where} AND (LOWER(s.name) LIKE $${params.length}
            OR LOWER(s.formatted_address) LIKE $${params.length}) `;
  }

  if (tags && tags.length > 0) {
    tags = tags.split(','); // eslint-disable-line
    params.push(tags);
    where = `${where} AND t.name = ANY( $${params.length} ) `;
  }

  if (siteGroups && siteGroups.length > 0) {
    siteGroups = siteGroups.split(','); // eslint-disable-line
    params.push(siteGroups);
    where = `${where} AND asg.name = ANY( $${params.length} ) `;
  }

  return { params, where };
}

module.exports = {
  getSitesSummary: (req, res, next) =>
    co(function* execute() {
      const userId = req.user.sub;

      const whereClause = prepareWhere({
        user: userId,
        company: req.query.company,
        status: req.query.status,
        query: req.query.q,
        tags: req.query.tags,
        siteGroups: req.query.siteGroups,
      });

      const sql = `
                WITH tag AS (
                    SELECT
                        id,
                        name,
                        company_id AS "companyId"
                    FROM tag
                ),
                auth_site_group AS (
                    SELECT
                        id,
                        name,
                        company_id AS "companyId"
                    FROM authorization_site_group
                )
                SELECT
                    s.site_id as id,
                    s.name,
                    s.formatted_address,
                    get_site_health( s.site_id ) as status,
                    COALESCE(json_agg(t.*) FILTER (WHERE t.id IS NOT NULL), '[]') AS tags,
                    COALESCE(json_agg(asg.*) FILTER (WHERE asg.id IS NOT NULL), '[]') AS "siteGroups",
                    (SELECT COUNT(*) FROM target d WHERE d.site_id = s.site_id AND d.active) AS devices_counter
                FROM
                    site s
                    JOIN user_site_authorization u ON u.site_id = s.site_id
                    LEFT JOIN site_tag st ON st.site_id = s.site_id AND st.deleted = false
                    LEFT JOIN tag t ON t.id = st.tag_id
                    LEFT JOIN authorization_site_group_site asgs ON asgs.site_id = s.site_id
                    LEFT JOIN auth_site_group asg ON asg.id = asgs.authorization_site_group_id
                ${whereClause.where}
                GROUP BY
                    s.site_id
                ORDER BY
                  s.name, s.site_id
            `;

      let sitesSummary = yield server.db.read.rows(sql, whereClause.params);
      if (sitesSummary && sitesSummary.length) {
        sitesSummary.forEach(s => {
          // eslint-disable-next-line no-param-reassign
          s.status = siteHelper.translateSiteHealthToNumber(s.status);
        });
      }

      // ICS-2195 Users can see their company tag only
      sitesSummary = siteHelper.filterSiteTagByCompany(
        req.user.company.id,
        sitesSummary
      );
      sitesSummary = siteHelper.filterSiteGroupByCompany(
        req.user.company.id,
        sitesSummary
      );
      res.send(sitesSummary);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
