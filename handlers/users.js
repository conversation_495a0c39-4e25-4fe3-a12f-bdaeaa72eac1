const util = require('util');
const restify = require('restify');
const co = require('co');
const _ = require('lodash');
const uuid = require('uuid/v4');
const validateUUID = require('uuid-validate');
const hasher = require('../lib/hash-password');
const errorHandler = require('../lib/errorhandler');
const { server } = require('../app');
const constants = require('../lib/app-constants');
const mailer = require('../lib/mailer');
const mailerHelper = require('../helpers/mailer-helper');
const env = require('../env');
const aws = require('../lib/aws');

const usersHelper = require('../helpers/users-helper');
const userChangeHelper = require('../helpers/user-change-helper');
const paginationHelper = require('../helpers/pagination-helper');
const { refreshMVUserSiteAuthorization } = require('../helpers/mv-helper');
const personaHelper = require('../helpers/persona-helper');

/**
 * Checks whether the specified user roles exist in the system
 */
function* checkRolesExist(conn, roles) {
  for (let i = 0; i < roles.length; i++) {
    const result = yield conn.execute(
      'SELECT role_id FROM role WHERE role_name = $1',
      [roles[i]]
    );
    if (!result || !result.rows || !result.rows.length) {
      return false;
    }
  }
  return true;
}

/**
 * Update the roles for a user
 * roles should be a list of role names
 */
function* updateUserRoles(conn, userId, roles) {
  // Ensure all roles are valid
  const exist = yield checkRolesExist(conn, roles);
  if (!exist) {
    throw new Error('Tried to update user roles with roles that do not exist');
  }

  // Remove any old Roles
  yield conn.execute('DELETE FROM user_role WHERE user_id = $1;', [userId]);

  // Insert new Roles
  const query = `
        INSERT INTO user_role
            ( user_id, role_id )
        VALUES
            ( $1, (SELECT role_id FROM role WHERE role_name = $2) );
    `;
  for (let i = 0; i < roles.length; i++) {
    yield conn.execute(query, [userId, roles[i]]);
  }

  return true;
}

/**
 * Checks whether the specified user groups exist within the specified company
 */
function* checkGroupsExist(conn, companyId, groups) {
  if (groups.length) {
    for (let i = 0; i < groups.length; i++) {
      const result = yield conn.execute(
        'SELECT id FROM user_group WHERE id = $1 AND company_id = $2;',
        [groups[i], companyId]
      );
      if (!result || !result.rows || !result.rows.length) {
        return false;
      }
    }
  }
  return true;
}

/**
 * Update the groups for a user
 * groups should be a list of User Group IDs
 *
 * Checks to ensure that the user and the groups
 * are from the same company
 */
function* updateUserGroups(conn, userId, companyId, groups, roles) {
  // Ensure caller can access all specified groups
  const exist = yield checkGroupsExist(conn, companyId, groups);
  if (!exist) {
    throw new Error("Tried to update user's groups with invalid groups");
  }

  // Remove any old Groups
  yield conn.execute('DELETE FROM user_group_user WHERE user_id = $1;', [
    userId,
  ]);

  // Company admins shouldn't be part of any usergroup
  if (groups.length && !roles.includes('COMPANY_ADMIN')) {
    // Insert new Groups
    const query = `
            INSERT INTO user_group_user
                ( user_id, user_group_id )
            VALUES
                ( $1, $2 );
        `;
    for (let i = 0; i < groups.length; i++) {
      yield conn.execute(query, [userId, groups[i]]);
    }
  }

  // Update the authorization view because groups have changed
  refreshMVUserSiteAuthorization('updateUserGroups when invite/update user');
}

module.exports = {
  /**
   * Get the user associated with the specified user id
   * User must be active, and calling user must be from the
   * same company as the target user
   */
  getUserById: (req, res, next) =>
    co(function* execute() {
      if (!validateUUID(req.params.id)) {
        req.log.info(`Invalid user id: ${req.params.id}`);
        return next(new restify.NotFoundError('User not found'));
      }

      const userId = req.params.id;
      const result = yield usersHelper.getFullUser([userId]);
      if (!result || !result.length) {
        return next(new restify.NotFoundError('User not found'));
      }

      const user = result[0];
      if (user.status === 2 || user.company.id !== req.user.company.id) {
        // Cannot get archived users, or users from another company
        return next(new restify.NotFoundError('User not found'));
      }

      // get the usergroups
      const userGroups = yield server.db.read.rows(
        `SELECT ug.id, ug.name FROM user_group as ug
                JOIN user_group_user ugu ON ugu.user_group_id = ug.id
                    AND ugu.user_id = $1;`,
        [userId]
      );
      user.userGroups = userGroups;

      res.send(user);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Update the user with the specified user id
   */
  updateUserById: (req, res, next) =>
    co(function* execute() {
      if (!validateUUID(req.params.id)) {
        req.log.info(`Invalid user id: ${req.params.id}`);
        return next(new restify.NotFoundError('User not found'));
      }
      const userId = req.params.id;

      const oldValues = yield usersHelper.getFullUser([userId]);
      if (!oldValues || !oldValues.length) {
        return next(new restify.NotFoundError('User not found'));
      }
      const oldUser = oldValues[0];

      if (oldUser.status === 2 || oldUser.company.id !== req.user.company.id) {
        // Cannot update archived users, or users from another company
        return next(new restify.NotFoundError('User not found'));
      }
      // New Values
      const { email } = req.body;
      const { fullName } = req.body;
      const { roles } = req.body;
      const { groups } = req.body;
      const { persona } = req.body;
      const isUserUnlocked = req.body.userUnlocked;

      const userCompany = req.user.company.id;

      if (email !== oldUser.email && oldUser.status === 0) {
        // Cannot update the email of a pending user
        return next(
          new restify.ForbiddenError('Cannot edit email of a pending user')
        );
      }

      const conn = yield server.db.write.getConnection();

      let personaId;

      try {
        // Ensure all roles are valid
        let exist = yield checkRolesExist(conn, roles);
        if (!exist) {
          return next(new restify.NotFoundError('User Role(s) not found'));
        }
        // Ensure caller can access all specified groups
        exist = yield checkGroupsExist(conn, req.user.company.id, groups);
        if (!exist) {
          return next(new restify.NotFoundError('User Group(s) not found'));
        }

        if (persona && persona !== 'Custom') {
          const personaExists =
            yield personaHelper.checkPersonaRolesExistByCompanyId(
              userCompany,
              persona,
              roles
            );
          if (!personaExists)
            return next(
              new restify.NotFoundError('Roles do not match the persona')
            );
        }

        yield conn.execute('BEGIN');
        const updateLastLock = isUserUnlocked ? ', last_locked = NULL' : '';
        const updatePersona = persona
          ? `, persona_id = get_persona_id($4)`
          : '';

        // Update User
        const userQuery = `
            UPDATE ics_user SET
              email = LOWER($1),
              full_name = $2
              ${updateLastLock}
              ${updatePersona}
            WHERE
              id = $3
            RETURNING company_id, persona_id;
          `;

        const updateQueryParams = [email, fullName, userId];

        if (persona) {
          updateQueryParams.push(persona);
        }

        const updateResult = yield conn.execute(userQuery, updateQueryParams);
        let companyId;
        if (updateResult && updateResult.rows) {
          companyId = updateResult.rows[0].companyId;
          personaId = updateResult.rows[0].personaId;
        }

        // Update Roles
        yield updateUserRoles(conn, userId, roles);

        // Update User Groups
        yield updateUserGroups(
          conn,
          userId,
          req.user.company.id,
          groups,
          roles
        );

        // Send verification email if email has changed
        if (email !== oldUser.email) {
          yield conn.execute(
            `UPDATE ics_user SET email_verified = NULL
                        WHERE id = $1;`,
            [userId]
          );

          // Create email token
          const token = hasher.generatePlainPassword(64);
          yield conn.execute(
            `
                        INSERT INTO email_token ( user_id, type, value, expires )
                        VALUES( $1, $2, $3, (CURRENT_TIMESTAMP + INTERVAL '2 days') );
                    `,
            [userId, constants.emailTokenType.VERIFY_EMAIL, token]
          );

          // Send email
          const { senderEmail } = yield mailerHelper.getSenderEmailsByCompanyId(
            req.user.company.id
          );
          yield mailer.sendVerifyEmail(
            { to: email, from: senderEmail },
            token,
            companyId
          );
        }

        yield conn.execute('COMMIT');

        // update user in configfilestatus.user_group_user
        aws.sendEventsToEventBridge({
          data: {
            userId,
            userGroupIds: groups,
            type: constants.ACTIONS.UPDATE,
          },
          detailType: env.config.AWS.eventBus.renditions.rules.updateUser,
          source: 'handlers/users/fn_updateUserById',
        });
      } catch (err) {
        req.log.error({ err });
        yield conn.execute('ROLLBACK');
        throw err;
      } finally {
        conn.done();
      }

      // Update User Change Log
      const changes = [];
      if (oldUser.email !== email) {
        changes.push({
          fieldName: 'email',
          fromValue: oldUser.email,
          toValue: email.toLowerCase(),
        });
      }
      if (oldUser.fullName !== fullName) {
        changes.push({
          fieldName: 'full_name',
          fromValue: oldUser.fullName,
          toValue: fullName,
        });
      }
      if (_.difference(oldUser.roles, req.body.roles).length > 0) {
        changes.push({
          fieldName: 'roles',
          fromValue: JSON.stringify(oldUser.roles),
          toValue: JSON.stringify(req.body.roles),
        });
      }
      if (persona && oldUser.personaId !== personaId) {
        changes.push({
          fieldName: 'persona_id',
          fromValue: oldUser.personaId,
          toValue: personaId,
        });
      }

      try {
        if (changes.length > 0) {
          yield userChangeHelper.recordUserChange(
            userId,
            'Update User',
            req.user.sub,
            changes
          );
        }
      } catch (err) {
        // Swallow errors when recording user changes
        req.log.warn(err);
      }

      // TODO race condition due to switching connection?
      const user = yield usersHelper.getFullUser([userId]);
      res.send(user[0]);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Archive the user associated with the specified user id.
   * This is a soft delete, but there is no service to reverse this.
   *
   * User must be from the same company as the caller, and not
   * be already archived.
   */
  archiveUserById: (req, res, next) =>
    co(function* execute() {
      if (!validateUUID(req.params.id)) {
        req.log.info(`Invalid user id: ${req.params.id}`);
        return next(new restify.NotFoundError('User not found'));
      }

      if (req.params.id === req.user.sub) {
        return next(new restify.ForbiddenError('Cannot delete yourself'));
      }

      const userId = req.params.id;
      const companyId = req.user.company.id;

      // Check caller is allowed to archive the user
      const checkQuery = `
                SELECT id, status
                FROM ics_user
                WHERE id = $1 AND company_id = $2 AND status != 2;
            `;
      const result = yield server.db.read.row(checkQuery, [userId, companyId]);

      if (!result || !result.id) {
        // User not available to be archived
        return next(new restify.NotFoundError('User not found'));
      }

      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');

        const archiveQuery = `
                    UPDATE ics_user
                    SET status = 2
                    WHERE id = $1;
                `;
        // Archive the user
        yield connection.execute(archiveQuery, [userId]);

        const removeFromUserGroupQuery = `
                    DELETE FROM user_group_user
                    WHERE user_id = $1
                `;
        yield connection.execute(removeFromUserGroupQuery, [userId]);

        const removeFromAlarmNotificationQuery = `
                    DELETE FROM ics_alarm.alarm_notification
                    WHERE user_id = $1
                `;
        const deleteNotificationResult = yield connection.execute(
          removeFromAlarmNotificationQuery,
          [userId]
        );

        if (deleteNotificationResult && deleteNotificationResult.rowCount) {
          // disable all alarms that don't have subscribers after site is deleted
          yield connection.execute(`
                      UPDATE ics_alarm.alarm a SET active = false
                      WHERE ( SELECT COUNT(1) FROM ics_alarm.alarm_notification an WHERE an.alarm_id = a.id ) = 0
                    ;`);
        }

        yield connection.execute('COMMIT');

        // delete user in configfilestatus.user_group_user
        aws.sendEventsToEventBridge({
          data: {
            userId,
            userGroupIds: [],
            type: constants.ACTIONS.DELETE,
          },
          detailType: env.config.AWS.eventBus.renditions.rules.updateUser,
          source: 'handlers/users/fn_archiveUserById',
        });
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        throw err;
      } finally {
        connection.done();
      }

      try {
        userChangeHelper.recordUserChange(
          userId,
          'Archive User',
          req.user.sub,
          [{ fieldName: 'status', fromValue: result.status, toValue: 2 }]
        );
      } catch (err) {
        // Swallow errors when recording user changes
        req.log.warn(err);
      }
      res.send(204);

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Remove the configured MFA secret from a specific user account.
   * They will be prompted to reconfigure MFA on next login.
   *
   * User must be active and from the same company as the caller.
   */
  clearUserMFA: (req, res, next) =>
    co(function* execute() {
      if (!validateUUID(req.params.id)) {
        req.log.error(`Invalid user id: ${req.params.id}`);
        throw new restify.NotFoundError('User not found');
      }

      const userId = req.params.id;
      const companyId = req.user.company.id;

      // Check caller is allowed to clear MFA for this user
      const checkQuery = `
                SELECT id, mfa_secret
                FROM ics_user
                WHERE
                    id = $1 AND
                    company_id = $2 AND
                    status = 1 AND
                    mfa_secret IS NOT NULL;
            `;
      const result = yield server.db.read.row(checkQuery, [userId, companyId]);

      if (!result || !result.id) {
        // User not available to be archived
        return next(new restify.NotFoundError('User not found'));
      }

      // Archive the user
      const clearMFAQuery = `
                UPDATE ics_user
                SET mfa_secret = NULL
                WHERE id = $1;
            `;
      yield server.db.write.execute(clearMFAQuery, [userId]);

      try {
        userChangeHelper.recordUserChange(
          userId,
          'Reset User MFA',
          req.user.sub,
          [
            {
              fieldName: 'mfa_secret',
              fromValue: result.mfaSecret,
              toValue: null,
            },
          ]
        );
      } catch (err) {
        // Swallow errors when recording user changes
        req.log.warn(err);
      }

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
     * Get a list of all users. This list can be filtered to only users with a specific role, and is always filtered to return users only from a specific company.
     * If a company filter is specified which is not the company of the current user, then the user's company must have a relationship with the specified company.

     * If roles are specified then these
     * will be validated that the caller has all roles they are filtering on, or the caller is a COMPANY_ADMIN.
     * Roles are only included in the response if the if the caller is a COMPANY_ADMIN.
     * Requires Role: COMPANY_ADMIN
     */
  getUsers: (req, res, next) =>
    co(function* execute() {
      const userCompany = req.user.company.id;

      const { roles } = req.params;
      const { companyId } = req.params;
      const { pending } = req.params;
      const query = req.params.q;
      const { allRoles } = req.params;

      let companyToSearch = userCompany;

      const pagingParams = paginationHelper.parsePaginationParams(req);

      // can query company
      if (companyId) {
        // make sure I can see this company
        if (companyId !== userCompany) {
          const result = yield server.db.read.row(
            `
                        SELECT c.id
                          FROM company c
                          JOIN company_relationship cr ON cr.company_id = c.id
                            AND cr.allowed_company_id = $2
                            AND c.id = $1;
                    `,
            [companyId, userCompany]
          );
          req.log.debug({ result }, 'Found company:');

          if (!result) {
            req.log.info(`Not allowed to view users from Company ${companyId}`);
            return next(new restify.NotFoundError('Cannot find company'));
          }
          companyToSearch = companyId;
        }
      }

      let queryStr = '';
      const statuses = [1];

      if (pending) {
        statuses.push(0);
      }

      const paramsArr = [companyToSearch, statuses];

      if (roles) {
        paramsArr.push(roles.split(','));
        queryStr = `${queryStr}
                    AND r.role_name = ANY($${paramsArr.length})`;
      }

      if (allRoles && allRoles.toLowerCase() === 'false') {
        queryStr = `${queryStr}
                    AND r.internal = false`;
      }

      const joinStr = queryStr
        ? `
                JOIN user_role ur ON ur.user_id = u.id
                JOIN role r ON r.role_id = ur.role_id
                `
        : '';

      if (query) {
        paramsArr.push(`%${query.toLowerCase()}%`);
        queryStr = `
                    AND
                    (
                        LOWER(u.full_name) LIKE $${paramsArr.length} OR
                        LOWER(u.email) LIKE $${paramsArr.length}
                    )
                `;
      }

      const fromAndWhereStr = `
                FROM ics_user u
                ${joinStr}
                WHERE
                u.company_id = $1 AND
                u.status = ANY($2) AND
                u.type = 'USER'
                ${queryStr}
            `;

      const users = yield server.db.read.rows(
        `
                SELECT DISTINCT(u.id), u.full_name
                    ${fromAndWhereStr}
                ORDER BY u.full_name ASC, u.id ASC
                LIMIT ${pagingParams.pageSize}
                OFFSET ${pagingParams.offset};
            `,
        paramsArr
      );

      const { count } = yield server.db.read.row(
        `
                SELECT COUNT(DISTINCT u.id) 
                    ${fromAndWhereStr}
            `,
        paramsArr
      );

      const fullUsers = yield usersHelper.getFullUser(
        users.map(user => user.id)
      );

      const conditionalRoles = ['TAMPER_CLEAR', 'FACTORY_RESET'];

      _.forEach(fullUsers, user => {
        _.forEach(conditionalRoles, cr => {
          if (
            !_.get(user, 'company.featureFlags', []).includes(cr) &&
            _.get(user, 'roles', []).includes(cr)
          ) {
            _.set(
              user,
              'roles',
              user.roles.filter(r => r !== cr)
            );
          }
        });
      });

      const data = {
        resultsMetadata: {
          totalResults: count,
          pageIndex: pagingParams.pageIndex,
          pageSize: pagingParams.pageSize,
        },
        results: fullUsers,
      };

      res.send(data);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Creates a new pending user account and then sends an email to the user inviting them to complete their registration (by choosing a password and configuring MFA).
   * Email address should be validated as unique before the invite email is sent.
   *
   * Requires Role: COMPANY_ADMIN
   */
  invite: (req, res, next) =>
    co(function* execute() {
      let { company } = req.user;
      const isSuperAdmin = req.user.roles.includes('SUPER_ADMIN');

      if (isSuperAdmin && req.body.companyId) {
        company = yield server.db.read.row(
          `
                    SELECT id, name FROM company
                    WHERE id = $1;
                `,
          [req.body.companyId]
        );

        if (!company) {
          return next(new restify.errors.BadRequestError('Company not found'));
        }
      }

      const reqObj = req.body;

      // Check if roles do not include COMPANY_ADMIN or Bank_User and groups are empty
      if (
        !reqObj.roles.includes('COMPANY_ADMIN') &&
        !reqObj.roles.includes('BANK_USER') &&
        (!reqObj.groups || reqObj.groups.length === 0)
      ) {
        return next(
          new restify.errors.BadRequestError(
            'Teams cannot be empty if not a COMPANY_ADMIN or Bank User'
          )
        );
      }

      // verify that email address is unique
      const exist = yield server.db.read.row(
        `SELECT count(email) FROM ics_user
                                                      WHERE email=LOWER($1) AND status != 2`,
        [reqObj.email]
      );
      if (exist.count) {
        req.log.info(`Email ${reqObj.email} already exists`);
        return next(new restify.errors.ConflictError('Email already exists'));
      }

      let newUser = null;
      let token = null;
      // Insert the record
      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');

        // create user
        // eslint-disable-next-line prefer-destructuring
        newUser = (yield connection.execute(
          `
                    INSERT INTO ics_user (
                        id, email, full_name, status, created, company_id, persona_id
                    ) VALUES (
                        $1, LOWER($2), $3, $4, now(), $5, get_persona_id($6)
                    )
                    RETURNING id, email, full_name, status, created, company_id, persona_id;
                `,
          [
            uuid(),
            reqObj.email,
            reqObj.fullName,
            server.constants.userStatus.PENDING,
            company.id,
            reqObj.persona,
          ]
        )).rows[0];

        req.log.info({ newUser }, 'Created new user');

        // Ensure all roles are valid for selected persona
        if (reqObj.persona && reqObj.persona !== 'Custom') {
          const personaExists =
            yield personaHelper.checkPersonaRolesExistByCompanyId(
              company.id,
              reqObj.persona,
              reqObj.roles
            );

          if (!personaExists)
            throw new restify.NotFoundError('Roles do not match the persona');
        }

        // Update Roles
        yield updateUserRoles(connection, newUser.id, reqObj.roles);

        // Update User Groups
        yield updateUserGroups(
          connection,
          newUser.id,
          company.id,
          reqObj.groups,
          reqObj.roles
        );

        // create email token
        token = hasher.generatePlainPassword(64);
        yield connection.execute(
          `INSERT INTO email_token ( user_id, type, value, expires )
                        VALUES( $1, $2, $3, (CURRENT_TIMESTAMP + INTERVAL '7 days') );`,
          [newUser.id, 'registration-invite', token]
        );

        // Send email
        req.log.info({ token }, 'Created email token: ');
        const { senderEmail } = yield mailerHelper.getSenderEmailsByCompanyId(
          company.id
        );
        yield mailer.sendInvite(
          { to: newUser.email, from: senderEmail },
          token,
          req.user.fullName,
          company,
          'Registration'
        );

        yield connection.execute('COMMIT');

        // create user in configfilestatus.user_group_user
        aws.sendEventsToEventBridge({
          data: {
            userId: newUser.id,
            userGroupIds: reqObj.groups,
            type: constants.ACTIONS.CREATE,
          },
          detailType: env.config.AWS.eventBus.renditions.rules.updateUser,
          source: 'handlers/users/fn_invite',
        });
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        throw err;
      } finally {
        connection.done();
      }

      // Update User Change Log
      try {
        userChangeHelper.recordUserChange(
          newUser.id,
          'Invite User',
          req.user.sub,
          [
            {
              fieldName: 'email',
              fromValue: null,
              toValue: newUser.email.toLowerCase(),
            },
            {
              fieldName: 'full_name',
              fromValue: null,
              toValue: newUser.fullName,
            },
            { fieldName: 'status', fromValue: null, toValue: newUser.status },
            { fieldName: 'created', fromValue: null, toValue: newUser.created },
            {
              fieldName: 'company_id',
              fromValue: null,
              toValue: newUser.companyId,
            },
            {
              fieldName: 'persona_id',
              fromValue: null,
              toValue: newUser.personaId,
            },
          ]
        );
      } catch (err) {
        // Swallow errors when recording user changes
        req.log.warn(err);
      }

      const response = isSuperAdmin ? { emailToken: token } : 204;

      res.send(response);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Allows searching for a user using a case insensitive, partial match. Ignore inputs which are null or undefined.
   * At least one search criteria (name or email) must be specified. If both are specified then use an AND join.
   * Add pagination as per existing services
   *
   * Requires Role: COMPANY_ADMIN
   */
  search: (req, res, next) =>
    co(function* execute() {
      // Get the query params and pagination
      const name = req.body.name != null ? req.body.name : null;
      const email = req.body.email != null ? req.body.email : null;
      const pageSize =
        req.params.pageSize != null && req.params.pageSize > 100
          ? req.params.pageSize
          : 100;
      const pageIndex =
        req.params.pageIndex != null && req.params.pageIndex < 0
          ? req.params.pageIndex
          : 0;

      // Construct the query values
      let queryString = '';
      const params = [req.user.company.id];

      if (name && email) {
        queryString = `(lower(full_name) LIKE LOWER('%'||$${
          params.length + 1
        }||'%') OR email LIKE LOWER('%'||$${params.length + 2}||'%'))`;
        params.push(name, email);
      } else if (name) {
        queryString = `lower(full_name) LIKE LOWER('%'||$${
          params.length + 1
        }||'%')`;
        params.push(name);
      } else if (email) {
        queryString = `email LIKE LOWER('%'||$${params.length + 1}||'%')`;
        params.push(email);
      } else {
        return next(
          new restify.errors.BadRequestError('No search criteria defined')
        );
      }

      // Perform the count query first
      const countQuery = `SELECT COUNT(u.id) FROM ics_user u
                        WHERE %s
                        AND company_id = $1`;

      const totalResults = (yield server.db.read.row(
        util.format(countQuery, queryString),
        params
      )).count;

      let results = [];

      // If there are results then get them
      if (totalResults > 0) {
        const selectQuery = `
                    ${usersHelper.SQL_SELECTS.USER_DTO_SELECT}
                    WHERE %s
                    AND company_id = $1
                    ORDER BY full_name ASC
                    LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;

        params.push(pageSize, pageIndex);

        results = yield server.db.read.rows(
          util.format(selectQuery, queryString),
          params
        );
      }

      // Construct the result object
      const data = {
        resultsMetadata: {
          totalResults,
          pageIndex,
          pageSize,
        },
        results,
      };

      res.send(data);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  /**
   * Functions to resend an email invite to a currently pending and invited user
   *
   * Requires Role: COMPANY_ADMIN
   */
  resendPendingRegistration: (req, res, next) =>
    co(function* execute() {
      const userId = req.params.id;

      const user = yield server.db.read.row(
        `
                SELECT status, email, company_id
                FROM ics_user
                WHERE id = $1;`,
        [userId]
      );

      // Check if user is found and has a pending status still
      if (!user || user.status !== constants.userStatus.PENDING) {
        req.log.info(
          `User id: ${req.params.id}, is not found or no longer pending`
        );
        return next(
          new restify.NotFoundError('User is not found or no longer pending')
        );
      }

      // Get most recent pending registration for user. There should only be one, but use most recent if more
      const token = yield server.db.read.row(
        `
                SELECT
                  id,
                  value
                FROM email_token
                WHERE user_id = $1 AND type = $2
                ORDER BY expires DESC
                LIMIT 1;`,
        [userId, constants.emailTokenType.REGISTRATION_INVITE]
      );

      if (!token || !token.id) {
        req.log.info(
          `User id: ${userId}, does not have a pending registration token to resend`
        );
        return next(
          new restify.NotFoundError(
            'User does not have a pending registration token to resend'
          )
        );
      }

      // Extend token life time
      yield server.db.write.execute(
        `
                UPDATE email_token SET
                    expires = ( NOW() + INTERVAL '7 days' )
                WHERE id = $1;`,
        [token.id]
      );

      // Resend invite email
      const { senderEmail } = yield mailerHelper.getSenderEmailsByCompanyId(
        user.companyId
      );
      yield mailer.sendInvite(
        { to: user.email, from: senderEmail },
        token.value,
        req.user.fullName,
        req.user.company,
        'Registration'
      );
      req.log.info({ token }, 'Resend email token');

      res.send(204);
      return next();
    }).catch(errorHandler.onError(req, res, next)),

  lockInactiveUsers: (req, res, next) =>
    co(function* execute() {
      req.log.info('Preparing to lock inactive users');

      let lockedUsers = [];

      let { remoteAddress } = req.connection;
      const xForwardedFor = req.headers['x-forwarded-for'];
      if (xForwardedFor) {
        // eslint-disable-next-line prefer-destructuring
        remoteAddress = xForwardedFor.split(', ')[0];
      }

      let { url } = req;
      if (req.url && env.config.base) {
        url = req.url.replace(`/${env.config.base}/`, '/rest/v1/');
      }

      const route = `${req.method} ${url}`;

      const connection = yield server.db.write.getConnection();
      try {
        yield connection.execute('BEGIN');

        lockedUsers = (yield connection.execute(`
                    UPDATE ics_user SET status = 2
                    WHERE
                        status != 2 AND
                        type = 'USER' AND
                        created < ( NOW() - INTERVAL '90 days' ) AND
                        id NOT IN (
                            SELECT DISTINCT h.user_id FROM auth_history h
                            WHERE
                                h.timestamp > ( NOW() - INTERVAL '90 days' )
                        )
                    RETURNING id, email, full_name, created, company_id;
                `)).rows;

        if (lockedUsers.length) {
          // Group by company and store companyId indexes
          const companies = [];
          const lockedUsersByCompany = {};
          const companyEmails = {};
          for (let i = 0; i < lockedUsers.length; i++) {
            const user = lockedUsers[i];
            const { companyId } = user;

            if (!companies.includes(companyId)) {
              companies.push(companyId);
              lockedUsersByCompany[companyId] = [user];
              companyEmails[companyId] =
                (yield mailerHelper.getSenderEmailsByCompanyId(
                  companyId
                )).senderEmail;
            } else {
              lockedUsersByCompany[companyId].push(user);
            }

            yield mailer.sendInactiveUserNotificationEmail({
              to: user.email,
              from: companyEmails[companyId],
            });
          }

          let idx = 0;
          let values = null;
          const params = [];

          companies.forEach(companyId => {
            const users = lockedUsersByCompany[companyId];
            const timestamp = new Date().toISOString();
            values = `${values ? `${values},` : ''}
                            ( $${(idx += 1)}, NULL, $${(idx += 1)}, $${(idx += 1)}, NULL, $${(idx += 1)}, 200, '${timestamp}' , $${(idx += 1)} )
                        `;
            params.push(
              uuid(),
              companyId,
              route,
              JSON.stringify(users),
              remoteAddress
            );
          });
          values = `${values};`;

          yield connection.execute(
            `
                        INSERT INTO report_api_audit ( request_id, user_id, company_id, route, request, response, status_code, timestamp, remote_address )
                        VALUES ${values}
                    `,
            params
          );
        }

        yield connection.execute('COMMIT');
      } catch (err) {
        req.log.error({ err });
        yield connection.execute('ROLLBACK');
        return next(new restify.errors.InternalServerError(err));
      } finally {
        connection.done();
      }

      req.log.warn(`${lockedUsers.length} users were locked`, lockedUsers);

      if (lockedUsers.length) {
        res.send(200, lockedUsers);
      } else {
        res.send(204);
      }

      return next();
    }).catch(errorHandler.onError(req, res, next)),

  warnInactiveUsers: async (req, res, next) => {
    req.log.info('Preparing to notify inactive users');

    try {
      const users = await server.db.replica.rows(
        `
                SELECT i.email, c.sender_email, ah.timestamp from ics_user i
                JOIN company c ON i.company_id = c.id
                LEFT JOIN (SELECT h.user_id, max(h.timestamp) as timestamp FROM auth_history h GROUP BY h.user_id) ah ON ah.user_id = i.id
                WHERE status = 1
                AND i.type = 'USER'
                AND i.created < ( NOW() - INTERVAL '83 days' )
                AND (
                      (ah.timestamp is not null AND ah.timestamp::date = ( NOW() - INTERVAL '83 days' )::date)
                      OR
                      (ah.timestamp is null AND i.created::date = ( NOW() - INTERVAL '83 days' )::date)
                    )
                ORDER BY i.email
                ;`,
        []
      );

      const message = `${users.length} users were notified to be inactivated`;
      if (users.length) {
        const emailSent = users.map(user =>
          mailer.sendInactiveUserWarmingEmail({
            to: user.email,
            from: user.senderEmail,
          })
        );
        await Promise.all(emailSent);
        req.log.warn(message);
        res.send(
          200,
          users.map(user => ({ email: user.email }))
        );
      } else {
        req.log.info(message);
        res.send(204);
      }

      return next();
    } catch (err) {
      return errorHandler.onError(req, res, next)(err);
    }
  },

  /**
   * Function to get both active and inactive
   */
  getAllUsers: (req, res, next) =>
    co(function* execute() {
      const userCompany = req.user.company.id;

      const { roles } = req.params;
      const { companyId } = req.params;
      const { pending } = req.params;
      const query = req.params.q;
      const { allRoles } = req.params;

      let companyToSearch = userCompany;

      const pagingParams = paginationHelper.parsePaginationParams(req);

      // can query company
      if (companyId) {
        // make sure I can see this company
        if (companyId !== userCompany) {
          const result = yield server.db.read.row(
            `
                        SELECT c.id
                          FROM company c
                          JOIN company_relationship cr ON cr.company_id = c.id
                            AND cr.allowed_company_id = $2
                            AND c.id = $1;
                    `,
            [companyId, userCompany]
          );
          req.log.debug({ result }, 'Found company:');

          if (!result) {
            req.log.info(`Not allowed to view users from Company ${companyId}`);
            return next(new restify.NotFoundError('Cannot find company'));
          }
          companyToSearch = companyId;
        }
      }

      let queryStr = '';
      const statuses = [1, 2];

      if (pending) {
        statuses.push(0);
      }

      const paramsArr = [companyToSearch, statuses];

      if (roles) {
        paramsArr.push(roles.split(','));
        queryStr = `${queryStr}
                    AND r.role_name = ANY($${paramsArr.length})`;
      }

      if (allRoles && allRoles.toLowerCase() === 'false') {
        queryStr = `${queryStr}
                    AND r.internal = false`;
      }

      const joinStr = queryStr
        ? `
                JOIN user_role ur ON ur.user_id = u.id
                JOIN role r ON r.role_id = ur.role_id
                `
        : '';

      if (query) {
        paramsArr.push(`%${query.toLowerCase()}%`);
        queryStr = `
                    AND
                    (
                        LOWER(u.full_name) LIKE $${paramsArr.length}
                    )
                `;
      }

      const fromAndWhereStr = `
                FROM ics_user u
                ${joinStr}
                WHERE
                u.company_id = $1 AND
                u.status = ANY($2) AND
                u.type = 'USER'
                ${queryStr}
            `;

      const users = yield server.db.read.rows(
        `
                SELECT DISTINCT(u.id), u.full_name
                    ${fromAndWhereStr}
                ORDER BY u.full_name ASC, u.id ASC
                LIMIT ${pagingParams.pageSize}
                OFFSET ${pagingParams.offset};
            `,
        paramsArr
      );

      const { count } = yield server.db.read.row(
        `
                SELECT COUNT(DISTINCT u.id) 
                    ${fromAndWhereStr}
            `,
        paramsArr
      );

      const fullUsers = yield usersHelper.getFullUser(
        users.map(user => user.id)
      );

      const conditionalRoles = ['TAMPER_CLEAR', 'FACTORY_RESET'];

      _.forEach(fullUsers, user => {
        _.forEach(conditionalRoles, cr => {
          if (
            !_.get(user, 'company.featureFlags', []).includes(cr) &&
            _.get(user, 'roles', []).includes(cr)
          ) {
            _.set(
              user,
              'roles',
              user.roles.filter(r => r !== cr)
            );
          }
        });
      });

      const data = {
        resultsMetadata: {
          totalResults: count,
          pageIndex: pagingParams.offset,
          pageSize: pagingParams.pageSize,
        },
        results: fullUsers,
      };

      res.send(data);
      return next();
    }).catch(errorHandler.onError(req, res, next)),
};
