const axios = require('axios');
const NodeCache = require('node-cache');
const restify = require('restify');
const jwt = require('restify-jwt');
const corsMiddleware = require('restify-cors-middleware');
const auditLogger = require('./lib/audit');
const env = require('./env');
const pre = require('./lib/pre');
const additionalHeaders = require('./lib/additional-headers');
const logger = require('./lib/logger');
const AppFeatureFlag = require('./src/feature-flag');
const { isKeycloakTheIDP } = require('./lib/utils');
const KafkaStreamHelper = require('./helpers/kafkaStreamHelper');
const dbOps = require('./lib/db-ops');
const { verifyCsrfToken } = require('./src/csrf/csrf.handler');

const { config } = env;
const { environment } = env;

const log = logger.createMainLogger(env);
const server = restify.createServer({
  name: `Node Server ${environment}`,
  log,
  contentType: [
    'application/json',
    'text/plain',
    'application/x-www-form-urlencoded',
    'multipart/form-data',
  ],
});

server.db = dbOps;

server.constants = require('./lib/app-constants');

server.appFeatureFlag = new AppFeatureFlag(server.db);

// Throttle all incoming requests
// TODO: need to implement some sort of unless() exception
if (config.throttle) {
  server.use(restify.throttle(config.throttle));
}

// Set default content-type to application/json
server.pre((req, res, next) => {
  if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
    const { headers } = req;
    headers['content-type'] = headers['content-type'] || 'application/json';
  }
  return next();
});

server.on('UnsupportedMediaType', (req, res, error, next) => {
  req.log.warn(
    `UnsupportedMediaTypeError - the Content-Type header specified (${req.headers['content-type']}) is not supported`
  );
  res.send(
    new restify.errors.UnsupportedMediaTypeError(
      `The Content-Type header specified (${req.headers['content-type']}) is not supported`
    )
  );
  return next();
});

// Parse request body and puts properties under req.params (and req.body)
server.use(restify.jsonBodyParser({ mapParams: false }));
server.use((req, res, next) => {
  if (
    req.route.path.includes('/jobs/config-management') ||
    req.route.path.includes('/custom-attributes/import')
  ) {
    return restify.multipartBodyParser({
      mapParams: true,
      mapFiles: true,
      hash: 'sha256',
    })(req, res, next);
  }
  return next();
});

// Parse query params
server.use(restify.queryParser({ allowDots: false }));

// Setup CORS
const CORS = corsMiddleware(config.CORS);
server.pre(CORS.preflight);
server.use(CORS.actual);

server.use(additionalHeaders()); // Adds additional useful headers without messing up CORS

let context = '';
if (config.base) {
  context = `/${config.base}`;
}

// Cache for keycloak public key
const publicKeyCacheTimeoutInSeconds = parseInt(
  (config.keycloakAuth && config.keycloakAuth.publicKeyCacheTimeoutInSeconds) ||
    '900',
  10
);
const keycloakCache = new NodeCache({
  stdTTL: publicKeyCacheTimeoutInSeconds,
});

// Use keycloak vs old auth to validate tokens
const secretCallback = (req, payload, done) => {
  if (
    req.headers.authorization &&
    req.headers.authorization.split(' ')[0] === 'Bearer'
  ) {
    if (isKeycloakTheIDP(payload)) {
      // validate the token using keycloak
      // This next call is a workaround to check if the token has been revoked from KC
      // Once we migrate completely to keycloak we should use a KC library to check the token and
      // get rid of jwt to check tokens
      // axios // TODO: Marcelo - This call checks for blacklisted tokens, but activating it creates a timeout in the FE, we need to figure out how to solve this
      //   .get(config.keycloakAuth.userInfoEndpoint, {
      //     headers: { Authorization: req.headers.authorization },
      //   })
      //   .then(() => {
      // We try to retrieve the keycloak public key from the cache
      // If it has expired we ask for it again
      let cachedPublicKey = keycloakCache.get('publicKey');
      if (cachedPublicKey) {
        done(null, cachedPublicKey);
      }
      axios
        .get(config.keycloakAuth.realmEndpoint)
        .then(({ data: { public_key: publicKey } }) => {
          // call keycloak and check the token
          // We authenticate using keycloak public Key
          cachedPublicKey = `-----BEGIN PUBLIC KEY-----\n${publicKey}\n-----END PUBLIC KEY-----`;
          keycloakCache.set('publicKey', cachedPublicKey);
          done(null, cachedPublicKey);
        });
      // })
      // .catch(error => {
      //   const {
      //     response: { status },
      //   } = error;
      //   if (status === 401) {
      //     // Token has been invalidated by kc
      //     // we return the default dummy key so user can't log in
      //     done(null, 'notValidToken');
      //   } else {
      //     // console.log('KEYCLOAK USERINFO ERROR', error);
      //   }
      // });
    } else {
      // We authenticate using the old key
      done(null, config.auth.secret);
    }
  } else {
    // We authenticate using the old key
    done(null, config.auth.secret);
  }
};

const excludedPaths = [
  '/health',
  `${context}/config/public`,
  `${context}/stats/time`,
  `${context}/authenticate`,
  `${context}/authenticateuser`,
  `${context}/authenticateuser/hub`,
  `${context}/authenticate/mfa`,
  `${context}/emailtoken/exists`,
  `${context}/emailtoken/register`,
  `${context}/emailtoken/forgotpassword`,
  `${context}/emailtoken/verifyemail`,
  `${context}/emailtoken/passwordValidity`,
  `${context}/forgotpassword`,
  `${context}/acceptable/password`,
  `${context}/devices/register`,
  `${context}/devices/authenticate`,
  new RegExp(`${context}/offlinepackage/.+/content`),
  new RegExp(`${context}/media/assets/.+/(source|thumbnail)`),
];

// Requires JWT auth for calls except for the following
server.use(
  jwt({
    secret: secretCallback,
    // Overwrite default token processing using getToken function to allow usage of x-auth-token
    getToken: req => {
      if (
        req.headers.authorization &&
        req.headers.authorization.split(' ')[0] === 'Bearer'
      ) {
        return req.headers.authorization.split(' ')[1];
      }
      if (req.headers['x-auth-token']) {
        return req.headers['x-auth-token'];
      }
      return null;
    },
  }).unless({
    path: excludedPaths,
  })
);

server.use((req, res, next) => {
  if (!config.csrf?.enable || excludedPaths.some(path => req.url.match(path))) {
    return next();
  }
  return verifyCsrfToken(req, res, next);
});

if (
  env.config.AWS.kafka &&
  env.config.AWS.kafka.enable &&
  env.config.AWS.kafka.enable_consumer
) {
  KafkaStreamHelper.consumeDataFromKafka(server);
}

// Attach a helper function for getting roles
server.use(pre.attachRole());
server.use(pre.attachHeaders());

// Global handler to renew the user's JWT token on each API call (so it doesn't expire)
server.use(pre.refreshToken());

server.pre((req, res, next) => {
  res.setHeader('Cache-Control', 'no-cache');
  next();
});

// Global default error handler
server.on('error', (req, res, err, cb) => {
  if (err && !config.stackTraceResponse) {
    // Prevent original error message from being sent back to the caller
    err.body = 'Internal Server Error'; // eslint-disable-line no-param-reassign
  } else {
    return server.log.error(req); // if err is not populated, this must be a nodejs error
  }
  return cb();
});

/* eslint-enable */
server.on(
  'after',
  auditLogger({
    log: logger.createLogger(
      logger.loggerOpts(env.config, {
        name: 'audit',
        level: config.logLevel,
      })
    ),
    body: true,
  })
);

module.exports = {
  server,
};
