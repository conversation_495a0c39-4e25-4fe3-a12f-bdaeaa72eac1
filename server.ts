/* eslint-disable no-console, global-require */
const env = require('./env');

if (env.config.enableNewRelic) {
  require('newrelic');
}

// @ts-ignore
const { server } = require('./app');

try {
  require('./endpoints/devices');
  require('./endpoints/company');
  require('./endpoints/user');
  require('./endpoints/sites');
  require('./endpoints/notifications');
  require('./endpoints/sitegroup');
  require('./endpoints/usergroup');
  require('./endpoints/authentication');
  require('./endpoints/forgotpassword');
  require('./endpoints/emailtoken');
  require('./endpoints/acceptable');
  require('./endpoints/stats');
  require('./endpoints/self');
  require('./endpoints/roles');
  require('./endpoints/persona');
  require('./endpoints/docs');
  require('./endpoints/reports');
  require('./endpoints/offlinepackage');
  require('./endpoints/media/prompttemplates');
  require('./endpoints/media/promptstates');
  require('./endpoints/media/prompttypes');
  require('./endpoints/media/assets');
  require('./endpoints/media/promptsets');
  require('./endpoints/media/prompt');
  require('./endpoints/media/dayparts');
  require('./endpoints/media/asset-package');
  require('./endpoints/media/keycodes');
  require('./endpoints/media/softkeys');
  require('./endpoints/media/touchmaps');
  require('./endpoints/devicetypes');
  require('./endpoints/devices-sync-enhanced');
  require('./endpoints/software');
  require('./endpoints/jobs');
  require('./endpoints/tags');
  require('./endpoints/alarms');
  require('./endpoints/releases');
  require('./endpoints/admin/releases');
  require('./endpoints/s3logger');
  require('./endpoints/web-activity');
  require('./endpoints/states/device-state');
  // eslint-disable-next-line
  env.config.rolloutMigration && env.config.rolloutMigration.enabled
    ? require('./src/rollout/api/rollout.endpoint')
    : require('./endpoints/rollout');
  require('./endpoints/bridge');
  require('./src/notifications/notifications.endpoint');
  require('./src/dashboard/dashboard.endpoint');
  require('./src/health-status/health-status.endpoint');
  require('./src/rki/api/approval/approval.endpoint');
  require('./src/rki/api/docs/docs.endpoint');
  require('./src/rki/api/krs/krs.endpoint');
  require('./src/files/api/files.endpoint');
  require('./src/file-upload/api/fileupload-request/fileupload-request.endpoint');
  require('./src/file-upload/api/devices-file/devices-file.endpoint');
  require('./src/automation/prompt-template/prompt-template.endpoint');
  require('./src/automation/prompts/prompts.endpoint');
  require('./src/automation/emails/emails.endpoint');
  require('./src/automation/devices/devices.endpoint');
  require('./src/authentication/authenticate-user.endpoint');
  require('./src/media-mgmt/prompts/prompts.endpoint');
  require('./src/language/language.endpoint');
  require('./src/automation/database/query/automation.query.endpoint');
  require('./src/automation/database/command/automation.command.endpoint');
  require('./src/hierarchy/hierarchy.endpoint');
  require('./src/payments/dashboard/dashboard.endpoint');
  require('./src/payments/actions/actions.endpoint');
  require('./src/payments/schemas/schemas.endpoint');
  require('./src/custom-attributes/custom-attributes.endpoint');
  require('./src/entities/api/site-tag/site-tag.endpoint');
  require('./src/entities/api/site/site.endpoint');
  require('./src/entities/api/company/company.endpoint');
  require('./src/entities/api/device/device.endpoint');
  require('./src/entities/api/device/mfa/jobs/mfa-jobs.endpoint');
  require('./src/entities/api/entity/definitions/setting-definition.endpoint');
  require('./src/entities/api/entity/settings/setting.endpoint');
  require('./src/entities/api/application/dashboard/application-dashboard.endpoint');
  require('./src/monitoring/monitoring.endpoint');
  require('./endpoints/schedule/schedule-task');
  require('./endpoints/config');
  require('./endpoints/health'); // Should be last
} catch (e: any) {
  console.error(e, e.stack, 'Error Happened:');
}

const port = process.env.PORT || 7080;
const isLocalEnv = process.env.ENVIRONMENT === 'local';

try {
  server.listen(port, () => {
    console.info(`${server.name} listening on ${port}`);
  });
} catch (err) {
  console.error('Server crash error:', err);
}

process.on('uncaughtException', (err: any) => {
  if (err.code === 'EPIPE') {
    console.error('EPIPE error:', err.stack);
  } else {
    console.error('Uncaught Exception:', err.stack);
    process.exit(1);
  }
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Initialize the SQS consumer
if (env.config.sync && env.config.sync.sourceQueueUrl && !isLocalEnv) {
  require('./workers/devices-sync').init();
}

// Initialize the Job worker
if (!isLocalEnv) {
  require('./workers/job-worker').init();
}

// Initialize the SSH tunnel IF dev and IF configured
if (isLocalEnv && env.config.dbSshTunnel && env.config.dbSshTunnel.enabled) {
  require('./dev/ssh-tunnel/ssh-server-helper').init(server);
}
