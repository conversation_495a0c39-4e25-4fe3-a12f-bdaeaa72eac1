const os = require('os');
const cluster = require('cluster');

if (
  cluster.isMaster &&
  !/test|local/i.test(`${process.env.ENVIRONMENT || 'local'}`)
) {
  const cpus = os.cpus().length - 1;

  console.log(`Forking for ${cpus} CPUs`); // eslint-disable-line no-console
  for (let i = 0; i < cpus; i++) {
    cluster.fork();
  }

  cluster.on('online', (worker: { id: any; process: { pid: any } }) => {
    console.log(`Worker ${worker.id} (pid: ${worker.process.pid}) is online.`); // eslint-disable-line no-console
  });

  cluster.on(
    'exit',
    (
      worker: { exitedAfterDisconnect: any; id: any; process: { pid: any } },
      code: number
    ) => {
      if (code !== 0 && !worker.exitedAfterDisconnect) {
        // eslint-disable-next-line no-console
        console.log(
          `Worker ${worker.id} (pid: ${worker.process.pid}) crashed. Starting a new worker...`
        ); // eslint-disable-line no-console
        cluster.fork();
      }
    }
  );
} else {
  require('./server'); // eslint-disable-line global-require
}
