import {
  afterEach,
  beforeEach,
  beforeAll,
  describe,
  it,
  expect,
  vi,
} from 'vitest';
import moment from 'moment';
import * as rolloutHelper from '../../../src/rollout/api/rollout-helper';
import SettingRepository from '../../../src/entities/api/entity/settings/setting.repository';
import { rolloutDtoFromReq } from '../../../src/rollout/api/rollout-helper';
import * as deploymentModel from '../../../src/rollout/models/deployment.model';

const { getJobSize } = require('../../../src/rollout/lib/deploymentHelper');
const Rollout = require('../../../src/rollout/models/rollout.model');

describe('createRollout', () => {
  const nextMock = vi.fn();
  const res = { send: vi.fn() };
  const addApiAuditLogMock = vi.fn().mockResolvedValue([]);

  beforeAll(() => {});

  beforeEach(() => {});

  afterEach(() => {
    vi.restoreAllMocks();
    res.send.mockClear();
    nextMock.mockClear();
    addApiAuditLogMock.mockClear();
  });

  const req = {
    user: {
      sub: '4d7301f8-c076-11ee-903b-5f85ff1e01e4',
      email: '<EMAIL>',
      company: { id: '4d7301f8-c076-11ee-903b-5f85ff1e01e4' },
      getRoles: () => [
        'COMPANY_ADMIN',
        'RKI',
        'TAMPER_CLEAR',
        'MEDIA_DESIGNER',
        'MEDIA_APPROVER',
        'MEDIA_DEPLOYER',
        'FACTORY_RESET',
        'CONFIG_MGMT_PUBLISH',
        'CONFIG_MGMT_ASSIGN',
        'CONFIG_MGMT_DEPLOY',
        'FUEL_PRICE_MGMT_VIEW',
        'FUEL_PRICE_MGMT_EDIT',
        'FUEL_PRICE_MGMT_CANCEL',
        'FUEL_PRICE_MGMT_THRESHOLD',
        'SITE_SETTINGS_VIEW',
        'SITE_SETTINGS_EDIT',
      ],
    },
    body: {
      name: 'CoscoDownload',
      description: '',
      softwareId: 1478,
      startTime: 1738331100000,
      installWindow: 10800000,
      installFrequency: 900000,
      targets: [
        {
          id: 981,
        },
      ],
      sites: [],
      deploymentPolicy: 1,
      type: 'software',
    },
    getId: () => 'requestId',
  };

  const jobType = 'sys.download';

  const rolloutData = {
    '38254': {
      downloadJobId: '3280f349-2e5c-4c7f-82bd-b856b3b970f7',
      targetReleaseId: 2197,
      status: 3,
    },
  };

  const sortedByTargetNameMap = {
    'e669e038-b1bd-4a59-8ff8-228a32afde88': [
      {
        active: true,
        site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
        presence: 'PRESENT',
        target_id: 38253,
        device_type: 'G7-100',
        is_edge_box: false,
        last_contact: null,
        delete_timestamp: null,
      },
      {
        active: true,
        site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
        presence: 'PRESENT',
        target_id: 38254,
        device_type: 'G7-100',
        is_edge_box: false,
        last_contact: null,
        delete_timestamp: null,
      },
      {
        active: true,
        site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
        presence: 'PRESENT',
        target_id: 38255,
        device_type: 'G7-100',
        is_edge_box: false,
        last_contact: null,
        delete_timestamp: null,
      },
    ],
  };

  it('should get site target limit successfully', async () => {
    const siteTargetLimit = {
      siteLimit: 100,
      targetLimit: 500,
    };

    const settingRepositoryInstance = new SettingRepository();

    vi.spyOn(settingRepositoryInstance.dataContext, 'row').mockImplementation(
      () =>
        Promise.resolve({
          totalCount: 0,
          results: null,
        })
    );

    const rolloutHelperMock = vi
      .spyOn(rolloutHelper, 'getRolloutSiteTargetLimit')
      .mockImplementation(() => Promise.resolve(siteTargetLimit));

    const result = await rolloutHelper.getRolloutSiteTargetLimit(req);

    expect(rolloutHelperMock).toHaveBeenCalledWith(req);
    expect(result).toEqual(siteTargetLimit);
  });

  it('should correctly transform the request body into rolloutDto', () => {
    const rolloutDto = rolloutDtoFromReq(req, req.user.sub, req.user.email);
    expect(rolloutDto.scheduleEndTime?.toISOString()).toEqual(
      '2025-01-31T16:45:00.000Z'
    );
  });

  it('should calculate download start and end time correctly when only downloadStartTime and downloadWindow are provided', () => {
    const downloadStartTime = 1739514600000;
    const downloadWindow = 86400000;

    const result = rolloutHelper.calculateDownloadStartAndEndTime(
      downloadStartTime,
      downloadWindow,
      undefined,
      undefined,
      undefined
    );

    expect(result.scheduleDownloadStartTime.format()).toBe(
      moment(downloadStartTime).format()
    );
    expect(result.scheduleDownloadEndTime.format()).toBe(
      moment(downloadStartTime).add(downloadWindow, 'ms').format()
    );
  });

  it('should return the correct job size for download action', () => {
    const targets = [
      {
        active: true,
        site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
        presence: 'PRESENT',
        target_id: 38253,
        device_type: 'G7-100',
        is_edge_box: false,
        last_contact: null,
        delete_timestamp: null,
      },
      {
        active: true,
        site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
        presence: 'PRESENT',
        target_id: 38254,
        device_type: 'G7-100',
        is_edge_box: false,
        last_contact: null,
        delete_timestamp: null,
      },
      {
        active: true,
        site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
        presence: 'PRESENT',
        target_id: 38255,
        device_type: 'G7-100',
        is_edge_box: false,
        last_contact: null,
        delete_timestamp: null,
      },
    ];

    const action = 'download';

    const result = getJobSize(targets, action);
    expect(result).toBe(5);
  });

  it('should return the correct job size for install action', () => {
    const targets = [
      {
        active: true,
        site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
        presence: 'PRESENT',
        target_id: 38253,
        device_type: 'G7-100',
        is_edge_box: false,
        last_contact: null,
        delete_timestamp: null,
      },
      {
        active: true,
        site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
        presence: 'PRESENT',
        target_id: 38254,
        device_type: 'G7-100',
        is_edge_box: false,
        last_contact: null,
        delete_timestamp: null,
      },
      {
        active: true,
        site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
        presence: 'PRESENT',
        target_id: 38255,
        device_type: 'G7-100',
        is_edge_box: false,
        last_contact: null,
        delete_timestamp: null,
      },
    ];

    const action = 'install';

    const result = getJobSize(targets, action);
    expect(result).toBe(3);
  });

  it('should generate two download job IDs for leader with no edge box', () => {
    const targetReleaseParams = {
      downloadJobs: [],
    };
    const jobDependencyParams = {
      job_id: [],
      dependens_on: [],
      continue_on_fail: [],
    };
    const jobCreationParams = {
      jobId: [],
      deviceId: [],
      destination: ['invenco.system'],
      type: [],
      jobData: [],
      status: [],
      jobEmbargoTime: [],
      expiry: [],
      createdBy: ['e718fd1f-d9b3-4f70-b421-05ee80edd681'],
      createdOn: [new Date()],
      message: ['new job'],
    };
    const jobEmbargoTime = {
      downloadJobEmbargoDate: new Date('2025-02-14T12:00:00+05:30'),
      installJobEmbargoDate: null,
      downloadJobExpiryDate: new Date('2025-02-14T14:00:00+05:30'),
      jobExpiryDate: new Date('2025-02-15T06:30:00.000Z'),
    };
    const siteLeaderDownloadJobs: string[] = [];
    const isLeader = true;
    const isEdgeBox = false;
    const targetId = 38253;

    deploymentModel.createDownloadJob({
      targetReleaseParams,
      jobDependencyParams,
      jobCreationParams,
      targetId,
      jobEmbargoTime,
      siteLeaderDownloadJobs,
      isLeader,
      isEdgeBox,
    });

    expect(jobCreationParams.jobId.length).toBe(2);
    expect(jobCreationParams.jobId).toHaveLength(2);
    jobCreationParams.jobId.forEach(id => {
      expect(id).not.toBeUndefined();
      expect(id).not.toBeNull();
    });
    expect(jobCreationParams.type).toContain(jobType);
  });

  it('should only generate one download job ID for leader with edge box', () => {
    const targetReleaseParams = {
      downloadJobs: [],
    };
    const jobDependencyParams = {
      job_id: [],
      dependens_on: [],
      continue_on_fail: [],
    };
    const jobCreationParams = {
      jobId: [],
      deviceId: [],
      destination: ['invenco.system'],
      type: [],
      jobData: [],
      status: [],
      jobEmbargoTime: [],
      expiry: [],
      createdBy: ['e718fd1f-d9b3-4f70-b421-05ee80edd681'],
      createdOn: [new Date()],
      message: ['new job'],
    };
    const jobEmbargoTime = {
      downloadJobEmbargoDate: new Date('2025-02-14T12:00:00+05:30'),
      installJobEmbargoDate: null,
      downloadJobExpiryDate: new Date('2025-02-14T14:00:00+05:30'),
      jobExpiryDate: new Date('2025-02-15T06:30:00.000Z'),
    };
    const siteLeaderDownloadJobs: string[] = [
      'e669e038-b1bd-4a59-8ff8-228a32afde88',
      'e669e038-b1bd-4a59-8ff8-228a32afde88',
    ];
    const isLeader = true;
    const isEdgeBox = true;
    const targetId = 38253;

    deploymentModel.createDownloadJob({
      targetReleaseParams,
      jobDependencyParams,
      jobCreationParams,
      targetId,
      jobEmbargoTime,
      siteLeaderDownloadJobs,
      isLeader,
      isEdgeBox,
    });

    expect(jobCreationParams.jobId.length).toBe(1);
    expect(jobCreationParams.jobId).toHaveLength(1);

    jobCreationParams.jobId.forEach(id => {
      expect(id).not.toBeUndefined();
      expect(id).not.toBeNull();
    });
    expect(jobCreationParams.type).toContain(jobType);
  });

  it('should add dependencies for non-leader with two site leader download jobs', () => {
    const targetReleaseParams = {
      downloadJobs: [],
    };
    const jobDependencyParams = {
      job_id: [],
      dependens_on: [],
      continue_on_fail: [],
    };
    const jobCreationParams = {
      jobId: [],
      deviceId: [],
      destination: ['invenco.system'],
      type: [],
      jobData: [],
      status: [],
      jobEmbargoTime: [],
      expiry: [],
      createdBy: ['e718fd1f-d9b3-4f70-b421-05ee80edd681'],
      createdOn: [new Date()],
      message: ['new job'],
    };
    const jobEmbargoTime = {
      downloadJobEmbargoDate: new Date('2025-02-14T12:00:00+05:30'),
      installJobEmbargoDate: null,
      downloadJobExpiryDate: new Date('2025-02-14T14:00:00+05:30'),
      jobExpiryDate: new Date('2025-02-15T06:30:00.000Z'),
    };
    const siteLeaderDownloadJobs = [
      '2310a0a8-a1d5-4bca-8ff0-7039f2382e0f',
      '0a49e449-1396-40d1-86cf-9e6f377287f9',
    ];
    const isLeader = false;
    const isEdgeBox = false;
    const targetId = 38255;

    deploymentModel.createDownloadJob({
      targetReleaseParams,
      jobDependencyParams,
      jobCreationParams,
      targetId,
      jobEmbargoTime,
      siteLeaderDownloadJobs,
      isLeader,
      isEdgeBox,
    });
    expect(jobCreationParams.jobId.length).toBe(1);
    expect(jobCreationParams.jobId).toHaveLength(1);

    jobCreationParams.jobId.forEach(id => {
      expect(id).not.toBeUndefined();
      expect(id).not.toBeNull();
    });
    expect(jobCreationParams.type).toContain(jobType);
  });

  it('should not add dependencies for non-leader with less than two site leader download jobs', () => {
    const targetReleaseParams = {
      downloadJobs: [],
    };
    const jobDependencyParams = {
      job_id: [],
      dependens_on: [],
      continue_on_fail: [],
    };
    const jobCreationParams = {
      jobId: [],
      deviceId: [],
      destination: ['invenco.system'],
      type: [],
      jobData: [],
      status: [],
      jobEmbargoTime: [],
      expiry: [],
      createdBy: ['e718fd1f-d9b3-4f70-b421-05ee80edd681'],
      createdOn: [new Date()],
      message: ['new job'],
    };
    const jobEmbargoTime = {
      downloadJobEmbargoDate: new Date('2025-02-14T12:00:00+05:30'),
      installJobEmbargoDate: null,
      downloadJobExpiryDate: new Date('2025-02-14T14:00:00+05:30'),
      jobExpiryDate: new Date('2025-02-15T06:30:00.000Z'),
    };
    const siteLeaderDownloadJobs = ['2310a0a8-a1d5-4bca-8ff0-7039f2382e0f'];
    const isLeader = false;
    const isEdgeBox = false;
    const targetId = 38253;

    deploymentModel.createDownloadJob({
      targetReleaseParams,
      jobDependencyParams,
      jobCreationParams,
      targetId,
      jobEmbargoTime,
      siteLeaderDownloadJobs,
      isLeader,
      isEdgeBox,
    });
    expect(jobCreationParams.jobId.length).toBe(1);
    expect(jobCreationParams.jobId).toHaveLength(1);
  });

  it('should filter targets that have a corresponding entry in rolloutData', () => {
    const { filteredMap, filterDevicesCount } = Rollout.filterTargetMap(
      sortedByTargetNameMap,
      rolloutData
    );

    expect(filteredMap).toEqual({
      'e669e038-b1bd-4a59-8ff8-228a32afde88': [
        {
          active: true,
          site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
          presence: 'PRESENT',
          target_id: 38254,
          device_type: 'G7-100',
          is_edge_box: false,
          last_contact: null,
          delete_timestamp: null,
        },
      ],
    });

    expect(filterDevicesCount).toBe(1);
  });

  it('should return an empty map if no targets match rolloutData', () => {
    const { filteredMap, filterDevicesCount } = Rollout.filterTargetMap(
      sortedByTargetNameMap,
      {}
    );

    expect(filteredMap).toEqual({
      'e669e038-b1bd-4a59-8ff8-228a32afde88': [],
    });

    expect(filterDevicesCount).toBe(0);
  });

  it('should filter only active targets', () => {
    const sortedByTargetNameMapWithInactive = {
      'e669e038-b1bd-4a59-8ff8-228a32afde88': [
        {
          active: false, // Inactive target
          site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
          presence: 'PRESENT',
          target_id: 38254,
          device_type: 'G7-100',
          is_edge_box: false,
          last_contact: null,
          delete_timestamp: null,
        },
      ],
    };

    const { filteredMap, filterDevicesCount } = Rollout.filterTargetMap(
      sortedByTargetNameMapWithInactive,
      rolloutData
    );

    expect(filteredMap).toEqual({
      'e669e038-b1bd-4a59-8ff8-228a32afde88': [],
    });

    expect(filterDevicesCount).toBe(0);
  });

  it('should handle multiple matching targets correctly', () => {
    const rolloutTestData = {
      '38253': {
        downloadJobId: '1111f349-2e5c-4c7f-82bd-b856b3b970f7',
        targetReleaseId: 2198,
        status: 3,
      },
      '38254': {
        downloadJobId: '2222f349-2e5c-4c7f-82bd-b856b3b970f7',
        targetReleaseId: 2197,
        status: 3,
      },
    };

    const { filteredMap, filterDevicesCount } = Rollout.filterTargetMap(
      sortedByTargetNameMap,
      rolloutTestData
    );

    expect(filteredMap).toEqual({
      'e669e038-b1bd-4a59-8ff8-228a32afde88': [
        {
          active: true,
          site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
          presence: 'PRESENT',
          target_id: 38253,
          device_type: 'G7-100',
          is_edge_box: false,
          last_contact: null,
          delete_timestamp: null,
        },
        {
          active: true,
          site_id: 'e669e038-b1bd-4a59-8ff8-228a32afde88',
          presence: 'PRESENT',
          target_id: 38254,
          device_type: 'G7-100',
          is_edge_box: false,
          last_contact: null,
          delete_timestamp: null,
        },
      ],
    });

    expect(filterDevicesCount).toBe(2);
  });

  it('should return true when scheduleTime is exactly equal to downloadTime', () => {
    const scheduleTime = '2025-02-14T06:30:00.000Z';
    const downloadTime = '2025-02-14T06:30:00.000Z';

    expect(Rollout.isValidScheduleTime(scheduleTime, downloadTime)).toBe(true);
  });

  it('should return true when scheduleTime is within one week after downloadTime', () => {
    const scheduleTime = '2025-02-17T12:21:40.000Z';
    const downloadTime = '2025-02-14T06:30:00.000Z';

    expect(Rollout.isValidScheduleTime(scheduleTime, downloadTime)).toBe(true);
  });

  it('should return false when scheduleTime is before downloadTime', () => {
    const scheduleTime = '2025-02-13T06:30:00.000Z';
    const downloadTime = '2025-02-14T06:30:00.000Z';

    expect(Rollout.isValidScheduleTime(scheduleTime, downloadTime)).toBe(false);
  });

  it('should return false when scheduleTime is more than one week after downloadTime', () => {
    const scheduleTime = '2025-02-22T06:30:01.000Z';
    const downloadTime = '2025-02-14T06:30:00.000Z';

    expect(Rollout.isValidScheduleTime(scheduleTime, downloadTime)).toBe(false);
  });

  it('should return true when scheduleTime is exactly one week after downloadTime', () => {
    const scheduleTime = '2025-02-21T06:30:00.000Z';
    const downloadTime = '2025-02-14T06:30:00.000Z';

    expect(Rollout.isValidScheduleTime(scheduleTime, downloadTime)).toBe(true);
  });

  it('should return false when scheduleTime is one second past the valid window', () => {
    const scheduleTime = '2025-02-21T06:30:01.000Z';
    const downloadTime = '2025-02-14T06:30:00.000Z';

    expect(Rollout.isValidScheduleTime(scheduleTime, downloadTime)).toBe(false);
  });
});
