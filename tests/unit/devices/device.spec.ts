import {
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
  vi,
  beforeAll,
} from 'vitest';
import Joi from 'joi';

import { getDeviceById } from '../../../handlers/device/device';
import { server } from '../../../app';
import * as deviceData from './device_mock_data.json';
import * as userData from './user_data.json';

let req: any;
let res: any;
let next: any;

function createSpy() {
  const calls: any[] = [];
  const spy = (...args: any[]) => {
    calls.push(args);
  };
  // Add properties to the spy function for inspection
  spy.called = () => calls.length > 0;
  spy.callCount = () => calls.length;
  spy.calls = () => calls;
  return spy;
}

describe('Test device data', () => {
  const userDataSchema = Joi.object({
    fullName: Joi.string().required(),
    email: Joi.string().email().required(),
    created: Joi.date().iso().required(),
    userId: Joi.string().guid().required(),
    roles: Joi.array().items(Joi.string()).unique().min(1).required(),
    company: Joi.object({
      id: Joi.string().guid().required(),
      name: Joi.string().required(),
      featureFlags: Joi.array().items(Joi.string()).unique().min(1).required(),
      sessionExpiryUserMins: Joi.number().integer().required(),
    }).required(),
    token: Joi.string().required(),
    flags: Joi.object({
      ADA: Joi.boolean().required(),
      RENDITIONS: Joi.boolean().required(),
    }).required(),
    default: Joi.any().allow(null),
  });

  const deviceDataschema = Joi.object({
    id: Joi.number().integer().required(),
    siteId: Joi.string().guid().required(),
    siteName: Joi.string().required(),
    lastRegistered: Joi.date().iso().required(),
    lastContact: Joi.date().iso().required(),
    name: Joi.string().required(),
    description: Joi.string().allow(null).required(),
    serialNumber: Joi.string().required(),
    keyGroupRef: Joi.any().allow(null),
    keyGroupId: Joi.any().allow(null),
    presence: Joi.string().valid('PRESENT').required(),
    status: Joi.number().integer().required(),
    gatewayAddress: Joi.any().allow(null),
    macAddress: Joi.any().allow(null),
    subnetMask: Joi.any().allow(null),
    releaseVersion: Joi.any().allow(null),
    alarmRulesSettings: Joi.object({
      suspendedByDeviceUntil: Joi.date().iso().allow(null),
      suspendedFrom: Joi.date().iso().allow(null),
      suspendedUntil: Joi.date().iso().allow(null),
      suspended: Joi.boolean().required(),
    }).required(),
    ipAddress: Joi.string().ip().required(),
    deviceType: Joi.object({
      id: Joi.string().required(),
      name: Joi.string().required(),
      screenSize: Joi.any().allow(null),
    }).required(),
    promptSet: Joi.object({
      id: Joi.any().allow(null),
      name: Joi.any().allow(null),
      version: Joi.any().allow(null),
    }).required(),
    ksn: Joi.any().allow(null),
    playlistId: Joi.any().allow(null),
    lastSuccessfulRki: Joi.any().allow(null),
    siteKeygroupId: Joi.any().allow(null),
    statusAlarmTs: Joi.any().allow(null),
    oosConditions: Joi.any().allow(null),
    statusStr: Joi.string().valid('UNKNOWN').required(),
    config: Joi.object().required(),
    inFlight: Joi.boolean().required(),
    configSchema: Joi.object().required(),
    configForm: Joi.object().required(),
    configData: Joi.object().required(),
    default: Joi.any().allow(null),
  });

  beforeAll(() => {
    server.db.read = { rows: vi.fn() };
    server.db.replica = { rows: vi.fn() };
    server.db.write = {
      getConnection: () => ({
        execute: vi.fn(),
        done: vi.fn(),
      }),
    };
  });

  beforeEach(() => {
    res = { send: createSpy() };
    next = createSpy();
    req = {
      params: { entity: 'devices', id: '67' },
      user: userData,
      log: { error: createSpy() },
    };
  });
  afterEach(() => {
    vi.restoreAllMocks();
    vi.clearAllMocks();
  });
  it.concurrent('Joi validation for userdata', () => {
    const { error } = userDataSchema.validate(userData);
    if (error) {
      expect(error).not.toBeNull();
    }
    expect(error).toBeNull();
  });

  // failing the request call for get device details - undefined as no data exists
  it('Get device by ID failure on mock call expected undefined', async () => {
    req.body = userData;
    // Assertions
    await getDeviceById(req, res, next);
    expect(res.send.called()).toBeFalsy();
    // eslint-disable-next-line vitest/valid-expect
    expect(res.send.calls()[0]).to.deep.equal(undefined);
  });

  it('device data schema validation', () => {
    const { error } = deviceDataschema.validate(deviceData);
    if (error) {
      expect(error).not.toBeNull();
    }
    expect(error).toBeNull();
  });
  // parameters check values need to be added for G6-500

  it('configSchema object validations', () => {
    const device = JSON.parse(JSON.stringify(deviceData));
    const keys = Object.keys(device?.configSchema);
    expect(keys).not.toBeNull();
  });

  it('configForm object validations', () => {
    const device = JSON.parse(JSON.stringify(deviceData));
    const keys = Object.keys(device?.configForm);
    expect(keys).not.toBeNull();
  });

  it('configData object validations', () => {
    const device = JSON.parse(JSON.stringify(deviceData));
    const keys = Object.keys(device?.configData);
    expect(keys).not.toBeNull();
  });

  it('config object validations', () => {
    const device = JSON.parse(JSON.stringify(deviceData));
    const keys = Object.keys(device?.config);
    expect(keys).not.toBeNull();
  });
});
