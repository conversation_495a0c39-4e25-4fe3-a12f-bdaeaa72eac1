{"id": 67, "siteId": "112d02c5-8399-42de-8c9b-6ac8927be22b", "siteName": "TestSite2", "lastRegistered": "2024-04-30T10:44:50.607Z", "lastContact": "2024-05-24T04:09:21.400Z", "name": "ADA-Test", "description": null, "serialNumber": "68714135", "keyGroupRef": null, "keyGroupId": null, "presence": "PRESENT", "status": 3, "gatewayAddress": null, "macAddress": null, "subnetMask": null, "releaseVersion": null, "alarmRulesSettings": {"suspendedByDeviceUntil": null, "suspendedFrom": null, "suspendedUntil": null, "suspended": false}, "ipAddress": "**************", "deviceType": {"id": "G6-500", "name": "G6-500", "screenSize": null}, "promptSet": {"id": null, "name": null, "version": null}, "ksn": null, "playlistId": null, "lastSuccessfulRki": null, "siteKeygroupId": null, "statusAlarmTs": null, "oosConditions": null, "statusStr": "UNKNOWN", "config": {"apcSerialNumber": null, "sdcSerialNumber": "DJS334T", "printerModel": null, "printerPaperWidth": null, "printerMarginX": null, "printerMarginY": null, "printerCutOffset": null, "terminalId": null, "terminalIpAddress": null, "terminalType": "PROD", "dateTime": null, "temperature": null, "pciRebootTime": null, "controllerIp": null, "controllerPort": null, "networkMode": null, "networkMask": null, "networkGateway": null, "dns": null, "syslog": null, "ntpServerIpAddress": null, "ntpPool": null, "configurationServiceIpAddress": null, "configurationServicePort": null, "timeZone": null, "masterVolume": "4", "audioOutputChannel": "external", "auxiliary": null, "apcMode": null}, "configSchema": {"client.invenco-icp-peer-cast-mode": {"label": "Media Sync Mode", "readonly": true, "target": "remote", "type": "string", "format": "string", "options": {"enum": ["Unicast", "Multicast"]}}, "client.invenco-icp-peer-master": {"label": "Is Master", "type": "boolean", "format": "string", "readonly": true}, "client.invenco-emulation-aux-status": {"label": "Aux Status", "type": "string", "format": "string", "readonly": true, "options": {"enum": ["none", "disconnected", "connected", "bad-version"]}}, "db.target.mac_address": {"label": "MAC Address", "readonly": true, "type": "string"}, "db.target.name": {"label": "Name", "readonly": false, "target": "local", "type": "string", "format": "string"}, "db.target.description": {"label": "Notes", "readonly": false, "target": "local", "type": "string", "format": "string", "options": {"multiline": true}}, "db.target.serial_number": {"label": "UPC Serial", "readonly": true, "type": "string"}, "db.target.device_type": {"label": "Type", "readonly": true, "type": "string"}, "db.target.key_group_ref": {"label": "RKI Key Group", "readonly": true, "type": "string"}, "db.target.last_registered": {"label": "Registration Date", "readonly": true, "type": "datetime", "format": "string"}, "cfg.net-terminal-id": {"label": "Terminal ID", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-controller-ip": {"label": "Controller IP Address", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-controller-port": {"label": "Controller Port", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-configurationservice-ip": {"label": "Configuration Service IP Address", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-configurationservice-port": {"label": "Configuration Service Port", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-dhcp-enabled": {"label": "DHCP Enabled", "readonly": false, "target": "remote", "type": "boolean", "format": "string"}, "cfg.net-ip-addr": {"label": "Terminal IP Address", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-netmask": {"label": "Network Mask", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-gateway": {"label": "Network Gateway", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-dns": {"label": "DNS", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-ntp-pool": {"label": "NTP Pool", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-ntp-server": {"label": "NTP Server", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-log-server": {"label": "Syslog Server", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-eth-speed": {"label": "Ethernet Speed", "readonly": false, "target": "remote", "type": "string", "format": "string", "options": {"enum": ["auto", "10base-t"]}}, "cfg.mgt-pci-reboot-time": {"label": "PCI Reboot Time", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.rtc-time-zone": {"label": "Timezone", "readonly": false, "target": "remote", "type": "string", "format": "string", "options": {"enum": []}}, "cfg.rtc-date-time-local": {"label": "Date/Time", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.lcd-brightness": {"label": "LCD Brightness", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "g7opt.upc-tamper-temperature.gauge.last": {"label": "Temperature", "readonly": true, "type": "string"}, "g7opt.apc-system-freeram.gauge.last": {"label": "Memory", "readonly": true, "type": "string"}, "g6opt.ca-dyn-freespace-ram.gauge.mean": {"label": "Memory", "readonly": true, "type": "string"}, "g6opt.env-kernel-type": {"label": "Model", "readonly": true, "type": "string"}, "emvapp.pinkey.ksn": {"label": "RKI KSN", "readonly": true, "type": "string"}, "g7opt.apc-hw-serialnumber": {"label": "APC Serial", "readonly": true, "type": "string"}, "g7opt.sdc-hw-serialnumber": {"label": "SDC Serial", "readonly": true, "type": "string"}, "cfg.aud-volume-master": {"label": "Master Volume", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.aud-outchannel": {"label": "Audio Output Channel", "readonly": false, "target": "remote", "type": "string", "format": "string", "options": {"multi": true, "enum": ["internal", "external", "both"]}}, "g7opt.apc-printer-model": {"label": "APC Printer Model", "readonly": true, "type": "string"}, "cfg.prt-model": {"label": "Printer Model", "readonly": false, "target": "startup", "type": "string", "format": "string", "options": {"enum": ["Auto-detect", "Zebra", "Citizen/Axiom", "Nippon 3”", "Nippon 2”", "Internal", "<PERSON><PERSON><PERSON><PERSON>", "Custom printer", "None"]}}, "cfg.prt-port-type": {"label": "Printer Serial Port", "readonly": false, "target": "startup", "type": "string", "format": "string", "options": {"enum": ["User controlled", "Internal printer"]}}, "cfg.prt-paper-width": {"label": "Printer Paper Width", "readonly": true, "type": "string", "format": "string"}, "cfg.prt-margin-x": {"label": "Printer <PERSON><PERSON>", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.prt-margin-y": {"label": "Printer <PERSON><PERSON>", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.prt-cut-offset": {"label": "Printer Cut Offset", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.prt-font-size": {"label": "Font Size", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.prt-auto-cut": {"label": "Auto Printer Paper Cut", "readonly": false, "target": "remote", "type": "boolean", "format": "string"}, "cfg.prt-paper-full-length": {"label": "Paper Roll Length", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.prt-paper-full-diameter": {"label": "Paper Roll Diameter", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.prt-paper-core-diameter": {"label": "Paper Roll Core Diameter", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.prt-paper-low-threshold": {"label": "Paper Low Threshold", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-terminal-rank": {"label": "Terminal Rank", "readonly": false, "target": "remote", "type": "string", "format": "string", "options": {"enum": ["main", "aux"]}}, "cfg.net-icsagent-bind-ip": {"label": "ICS Agent Bind IP", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-aux-ip": {"label": "Aux IP", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-controller-backup": {"label": "Backup Fuel Controller", "readonly": false, "target": "remote", "type": "boolean", "format": "string"}, "cfg.net-controller-ip2": {"label": "Secondary Fuel Controller IP", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-controller-port2": {"label": "Secondary Fuel Controller Port", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-controller-con-retry-attempts": {"label": "Fuel Controller Connection Retry Attempts", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.net-controller-con-retry-interval": {"label": "Fuel Controller Connection Retry Interval", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.tamper-analog": {"label": "Tamper Analog", "readonly": false, "target": "remote", "type": "boolean", "format": "string"}, "g7opt.scc-hw-serialnumber": {"label": "SCC Serial", "readonly": true, "type": "string"}, "g7opt.sys-contactless-module": {"label": "System Contactless Module", "readonly": true, "type": "string"}, "g7opt.scc-channel-status": {"label": "SCC Channel Status", "readonly": true, "type": "string"}, "g7opt.sys-securelink-upc-scc": {"label": "System Secure Link", "readonly": true, "type": "string"}, "cfg.mgt-device-type": {"label": "APC Mode", "readonly": true, "target": "remote", "type": "string", "format": "string", "options": {"enum": ["EDGE", "OPT"]}}, "g7opt.sys.aux-keypad": {"label": "ADA CONNECTION STATUS", "readonly": true, "target": "remote", "type": "string", "format": "string", "options": {"enum": ["BASIC", "ADVANCED"]}}, "cfg.mgt-dual-display-enabled": {"label": "Dual Display Enabled", "readonly": false, "target": "remote", "type": "boolean", "format": "string"}, "cfg.lcd-brightness-2": {"label": "Secondary Display Brightness", "readonly": false, "target": "remote", "type": "string", "format": "string"}, "cfg.lcd-gstv-screen": {"label": "GSTV Screen", "readonly": false, "target": "remote", "type": "string", "format": "string", "options": {"enum": ["primary", "secondary"]}}, "g7opt.display-mode-sdc": {"label": "Secondary Display Status", "readonly": true, "target": "remote", "type": "string", "format": "string", "options": {"enum": ["unavailable", "available", "trusted", "untrusted"]}}, "g7opt.display-screens": {"label": "Supported Screens Specifications", "readonly": true, "target": "remote", "type": "string", "format": "string"}, "g7opt.display-resolution-2": {"label": "Secondary Display Resolution", "readonly": true, "target": "remote", "type": "string", "format": "string"}, "g7opt.sys-serialnumber-udc": {"label": "UDC Serial", "readonly": true, "type": "string"}, "g7opt.opt-fw-udc-ver": {"label": "UDC Version", "readonly": true, "target": "remote", "type": "string", "format": "string"}}, "configForm": {"type": "VerticalLayout", "elements": [{"order": 0, "type": "Group", "label": "Details", "elements": [{"order": 0, "type": "Control", "scope": "#/properties/db.target.name"}, {"order": 1, "type": "Control", "scope": "#/properties/cfg.net-terminal-id"}, {"order": 2, "type": "Control", "scope": "#/properties/db.target.device_type"}, {"order": 3, "type": "Control", "scope": "#/properties/cfg.mgt-device-type"}, {"order": 4, "type": "Control", "scope": "#/properties/g6opt.env-kernel-type"}, {"order": 5, "type": "Control", "scope": "#/properties/db.target.last_registered"}, {"order": 6, "type": "Control", "scope": "#/properties/db.target.serial_number"}, {"order": 7, "type": "Control", "scope": "#/properties/g7opt.scc-hw-serialnumber"}, {"order": 8, "type": "Control", "scope": "#/properties/g7opt.apc-hw-serialnumber"}, {"order": 9, "type": "Control", "scope": "#/properties/g7opt.sdc-hw-serialnumber"}, {"order": 10, "type": "Control", "scope": "#/properties/g7opt.sys-serialnumber-udc"}, {"order": 11, "type": "Control", "scope": "#/properties/g7opt.opt-fw-udc-ver"}, {"order": 12, "type": "Control", "scope": "#/properties/db.target.mac_address"}, {"order": 13, "type": "Control", "scope": "#/properties/db.target.description"}, {"order": 14, "type": "Control", "scope": "#/properties/g7opt.sys.aux-keypad"}, {"order": 15, "type": "Control", "scope": "#/properties/cfg.lcd.accessibility-mode"}, {"order": 16, "type": "Control", "scope": "#/properties/g7opt.display-mode-sdc"}, {"order": 17, "type": "Control", "scope": "#/properties/g7opt.display-screens"}, {"order": 18, "type": "Control", "scope": "#/properties/g7opt.display-resolution-2"}]}, {"order": 1, "type": "Group", "label": "Network", "elements": [{"order": 0, "type": "Control", "scope": "#/properties/cfg.net-ip-addr"}, {"order": 1, "type": "Control", "scope": "#/properties/cfg.net-dhcp-enabled"}, {"order": 2, "type": "Control", "scope": "#/properties/cfg.net-dns"}, {"order": 3, "type": "Control", "scope": "#/properties/cfg.net-gateway"}, {"order": 4, "type": "Control", "scope": "#/properties/cfg.net-netmask"}, {"order": 5, "type": "Control", "scope": "#/properties/cfg.net-ntp-server"}, {"order": 6, "type": "Control", "scope": "#/properties/cfg.net-ntp-pool"}, {"order": 7, "type": "Control", "scope": "#/properties/cfg.net-log-server"}, {"order": 8, "type": "Control", "scope": "#/properties/cfg.net-configurationservice-ip"}, {"order": 9, "type": "Control", "scope": "#/properties/cfg.net-configurationservice-port"}, {"order": 10, "type": "Control", "scope": "#/properties/cfg.net-controller-ip"}, {"order": 11, "type": "Control", "scope": "#/properties/cfg.net-controller-port"}, {"order": 12, "type": "Control", "scope": "#/properties/cfg.net-eth-speed"}]}, {"order": 2, "type": "Group", "label": "Alternative Controller Setup", "elements": [{"order": 0, "type": "Control", "scope": "#/properties/cfg.net-controller-backup"}, {"order": 1, "type": "Control", "scope": "#/properties/cfg.net-controller-ip2"}, {"order": 2, "type": "Control", "scope": "#/properties/cfg.net-controller-port2"}, {"order": 3, "type": "Control", "scope": "#/properties/cfg.net-controller-con-retry-attempts"}, {"order": 4, "type": "Control", "scope": "#/properties/cfg.net-controller-con-retry-interval"}]}, {"order": 3, "type": "Group", "label": "RKI", "elements": [{"order": 0, "type": "Control", "scope": "#/properties/db.target.key_group_ref"}]}, {"order": 4, "type": "Group", "label": "Media", "elements": [{"order": 0, "type": "Control", "scope": "#/properties/client.invenco-icp-peer-cast-mode"}, {"order": 1, "type": "Control", "scope": "#/properties/cfg.aud-volume-master"}, {"order": 2, "type": "Control", "scope": "#/properties/cfg.aud-outchannel"}, {"order": 3, "type": "Control", "scope": "#/properties/cfg.lcd-gstv-screen"}, {"order": 4, "type": "Control", "scope": "#/properties/cfg.lcd-brightness-2"}, {"order": 5, "type": "Control", "scope": "#/properties/cfg.mgt-dual-display-enabled"}]}, {"order": 5, "type": "Group", "label": "Printer", "elements": [{"order": 0, "type": "Control", "scope": "#/properties/cfg.prt-model"}, {"order": 1, "type": "Control", "scope": "#/properties/cfg.prt-port-type"}, {"order": 2, "type": "Control", "scope": "#/properties/cfg.prt-margin-x"}, {"order": 3, "type": "Control", "scope": "#/properties/cfg.prt-margin-y"}, {"order": 4, "type": "Control", "scope": "#/properties/cfg.prt-cut-offset"}, {"order": 5, "type": "Control", "scope": "#/properties/g7opt.apc-printer-model"}, {"order": 6, "type": "Control", "scope": "#/properties/cfg.prt-font-size"}, {"order": 7, "type": "Control", "scope": "#/properties/cfg.prt-auto-cut"}, {"order": 8, "type": "Control", "scope": "#/properties/cfg.prt-paper-full-length"}, {"order": 9, "type": "Control", "scope": "#/properties/cfg.prt-paper-full-diameter"}, {"order": 10, "type": "Control", "scope": "#/properties/cfg.prt-paper-core-diameter"}, {"order": 11, "type": "Control", "scope": "#/properties/cfg.prt-paper-low-threshold"}]}, {"order": 6, "type": "Group", "label": "Others", "elements": [{"order": 0, "type": "Control", "scope": "#/properties/cfg.rtc-date-time-local"}, {"order": 1, "type": "Control", "scope": "#/properties/cfg.rtc-time-zone"}, {"order": 2, "type": "Control", "scope": "#/properties/cfg.mgt-pci-reboot-time"}, {"order": 3, "type": "Control", "scope": "#/properties/g7opt.apc-system-freeram.gauge.last"}, {"order": 3, "type": "Control", "scope": "#/properties/g6opt.ca-dyn-freespace-ram.gauge.mean"}, {"order": 5, "type": "Control", "scope": "#/properties/cfg.lcd-brightness"}, {"order": 6, "type": "Control", "scope": "#/properties/cfg.tamper-analog"}]}, {"order": 7, "type": "Group", "label": "Auxiliary Setup", "elements": [{"order": 0, "type": "Control", "scope": "#/properties/cfg.net-terminal-rank"}, {"order": 1, "type": "Control", "scope": "#/properties/cfg.net-icsagent-bind-ip"}, {"order": 2, "type": "Control", "scope": "#/properties/cfg.net-aux-ip"}]}]}, "configData": {"client.invenco-icp-peer-cast-mode": {"value": "unicast", "timestamp": "2024-05-14T11:26:48.661+00:00"}, "g7opt.sdc-hw-serialnumber": {"value": "DJS334T", "timestamp": "2024-05-21T10:51:41.687+00:00"}, "cfg.aud-volume-master": {"value": "4", "timestamp": "2024-05-17T12:53:05.5+00:00"}, "cfg.aud-outchannel": {"value": "external", "timestamp": "2024-05-17T12:53:55.798+00:00"}, "cfg.mgt-dual-display-enabled": {"value": "true", "timestamp": "2024-05-24T03:58:44.058+00:00", "pending": "false"}, "cfg.lcd-brightness-2": {"value": "12", "timestamp": "2024-05-21T08:07:09.183+00:00"}, "cfg.lcd-gstv-screen": {"value": "secondary", "timestamp": "2024-05-17T12:34:54.242+00:00"}, "g7opt.display-mode-sdc": {"value": "available", "timestamp": "2024-05-21T09:15:26.002+00:00"}, "g7opt.display-resolution-2": {"value": "1x2", "timestamp": "2024-05-17T12:49:12.48+00:00"}, "g7opt.sys-serialnumber-udc": {"value": "UKLK2D", "timestamp": "2024-05-21T07:24:32.44+00:00"}, "g7opt.opt-fw-udc-ver": {"value": "2.3", "timestamp": "2024-05-17T13:57:17.072+00:00"}, "cfg.net-dhcp-enabled": {"value": "", "pending": "true", "timestamp": "2024-05-23T10:44:25.228Z"}, "db.target.mac_address": {"value": null, "timestamp": "2024-05-25T09:35:02.726117+00:00"}, "db.target.name": {"value": "ADA-Test", "timestamp": "2024-05-25T09:35:02.726117+00:00"}, "db.target.description": {"value": null, "timestamp": "2024-05-25T09:35:02.726117+00:00"}, "db.target.serial_number": {"value": "68714135", "timestamp": "2024-05-25T09:35:02.726117+00:00"}, "db.target.device_type": {"value": "G6-500", "timestamp": "2024-05-25T09:35:02.726117+00:00"}, "db.target.key_group_ref": {"value": null, "timestamp": "2024-05-25T09:35:02.726117+00:00"}, "db.target.last_registered": {"value": "2024-04-30T10:44:50.607006+00:00", "timestamp": "2024-05-25T09:35:02.726117+00:00"}}, "inFlight": false}