import { describe, it, vi, expect, beforeEach, afterEach } from 'vitest';
import restifyErrors from 'restify-errors';
import { server } from '../../../app';
import deviceHelper from '../../../helpers/device-helper';

describe('validateDevices', () => {
  it('should return device summary for valid profile and devices', async () => {
    const deviceSummary = [
      { id: 1, devices: [{ id: 100 }, { id: 200 }] },
      { id: 2, devices: [{ id: 300 }] },
    ];
    const promptSetProfile = 'valid-profile';

    const deviceSummaryResult = [
      { id: 1, devices: [{ id: 100 }, { id: 200 }] },
    ];

    server.db.read = {
      rows: vi
        .fn()
        .mockResolvedValueOnce([
          {
            main_resolutions: '1080x720',
            aux_resolutions: '720x1280',
            sec_model: 'seq5',
          },
        ])
        .mockResolvedValueOnce([{ targetId: 100 }, { targetId: 200 }]),
    };

    const result = await deviceHelper.validateDevices(
      deviceSummary,
      promptSetProfile
    );
    expect(result).toEqual({
      error: false,
      deviceSummary: deviceSummaryResult,
    });
  });

  it('should throw NotFoundError if no profile found', async () => {
    const promptSetProfile = 'invalid-profile';

    server.db.read = {
      rows: vi.fn(),
    };

    await expect(async () => {
      await deviceHelper.validateDevices([], promptSetProfile);
    }).rejects.toThrowError(restifyErrors.NotFoundError);
  });

  it('should throw NotFoundError if no compatible devices found', async () => {
    const deviceSummary = [{ id: 1, devices: [{ id: 100 }, { id: 200 }] }];

    const promptSetProfile = 'valid-profile';

    server.db.read = {
      rows: vi
        .fn()
        .mockResolvedValueOnce([
          {
            main_resolutions: '1080x720',
            aux_resolutions: null,
            sec_model: 'seq5',
          },
        ])
        .mockResolvedValueOnce([]),
    };

    await expect(async () => {
      await deviceHelper.validateDevices(deviceSummary, promptSetProfile);
    }).rejects.toThrowError(restifyErrors.NotFoundError);
  });

  it('should filter out devices that are not compatible', async () => {
    const deviceSummary = [
      { id: 1, devices: [{ id: 100 }, { id: 200 }] },
      { id: 2, devices: [{ id: 300 }] },
    ];

    const promptSetProfile = 'valid-profile';

    server.db.read = {
      rows: vi
        .fn()
        .mockResolvedValueOnce([
          {
            main_resolutions: '1080x720',
            aux_resolutions: '720x1280',
            sec_model: 'seq5',
          },
        ])
        .mockResolvedValueOnce([{ targetId: 100 }]),
    };

    const result = await deviceHelper.validateDevices(
      deviceSummary,
      promptSetProfile
    );

    expect(result).toEqual({
      error: false,
      deviceSummary: [{ id: 1, devices: [{ id: 100 }] }],
    });
  });
});

describe('deviceHelper.updateDeviceTerminalLocation', () => {
  let mockConnection: any;

  beforeEach(() => {
    mockConnection = {
      execute: vi.fn(),
      done: vi.fn(),
    };
    server.db.write.getConnection = vi.fn().mockResolvedValue(mockConnection);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should successfully update existing terminal location', async () => {
    const config = { terminalLocation: 'Backcourt' };
    const deviceId = 123;
    const siteId = '456';
    const state = 'invenco.system.cfg.net-terminal-location';
    const value = 'Forecourt';
    const timestamp = new Date().toISOString();

    mockConnection.execute
      .mockResolvedValueOnce() // BEGIN
      .mockResolvedValueOnce() // UPDATE
      .mockResolvedValueOnce(); // COMMIT

    const result = await deviceHelper.updateDeviceTerminalLocation(
      config,
      deviceId,
      siteId,
      state,
      value,
      timestamp
    );

    expect(server.db.write.getConnection).toHaveBeenCalled();
    expect(mockConnection.execute).toHaveBeenCalledWith('BEGIN');
    expect(mockConnection.execute).toHaveBeenCalledWith(
      'UPDATE device_states SET value = $4, timestamp = $5 WHERE device_id = $1 AND site_id = $2 AND state = $3',
      [deviceId, siteId, state, value, timestamp]
    );
    expect(mockConnection.execute).toHaveBeenCalledWith('COMMIT');
    expect(mockConnection.done).toHaveBeenCalled();
    expect(result).toEqual({ succeeded: [deviceId], failed: [], ignored: [] });
  });

  it('should handle database errors and add deviceId to failed list', async () => {
    const config = { terminalLocation: 'some invalid value' };
    const deviceId = 123;
    const siteId = '456';
    const state = 'invenco.system.cfg.net-terminal-location';
    const value = 'Forecourt';
    const timestamp = new Date().toISOString();
    const dbError = new Error('Database connection error');

    mockConnection.execute
      .mockResolvedValueOnce() // BEGIN
      .mockRejectedValueOnce(dbError); // UPDATE fails

    const result = await deviceHelper.updateDeviceTerminalLocation(
      config,
      deviceId,
      siteId,
      state,
      value,
      timestamp
    );

    expect(server.db.write.getConnection).toHaveBeenCalled();
    expect(mockConnection.execute).toHaveBeenCalledWith('BEGIN');
    expect(mockConnection.execute).toHaveBeenCalledWith(
      'UPDATE device_states SET value = $4, timestamp = $5 WHERE device_id = $1 AND site_id = $2 AND state = $3',
      [deviceId, siteId, state, value, timestamp]
    );

    expect(result).toEqual({
      succeeded: [],
      failed: [deviceId],
      ignored: [],
    });
  });

  it('should insert new terminal location when config.terminalLocation is undefined', async () => {
    const config = {};
    const deviceId = 123;
    const siteId = '456';
    const state = 'invenco.system.cfg.net-terminal-location';
    const value = 'Forecourt';
    const timestamp = new Date().toISOString();

    mockConnection.execute
      .mockResolvedValueOnce() // BEGIN
      .mockResolvedValueOnce() // INSERT
      .mockResolvedValueOnce(); // COMMIT

    const result = await deviceHelper.updateDeviceTerminalLocation(
      config,
      deviceId,
      siteId,
      state,
      value,
      timestamp
    );

    expect(server.db.write.getConnection).toHaveBeenCalled();
    expect(mockConnection.execute).toHaveBeenCalledWith('BEGIN');
    expect(mockConnection.execute).toHaveBeenCalledWith(
      'INSERT INTO device_states (device_id, site_id, state, value, timestamp) VALUES ($1, $2, $3, $4, $5)',
      [deviceId, siteId, state, value, timestamp]
    );
    expect(mockConnection.execute).toHaveBeenCalledWith('COMMIT');
    expect(result).toEqual({ succeeded: [deviceId], failed: [], ignored: [] });
  });
});
