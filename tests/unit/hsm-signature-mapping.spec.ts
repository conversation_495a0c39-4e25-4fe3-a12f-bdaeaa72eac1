import { describe, it, expect } from 'vitest';
import {
  generateConfigHashSignatureKeyMappings,
  generateRedisKey,
} from '../../handlers/jobs.service';

const args = {
  keys: ['abc', 'def', 'ghi'],
  companyId: 'unit-test',
  slot: '1',
  deviceType: 'pss',
  fileHash: 'a6fab14876ae66833aa96f13a4b818f4008bf88f5910687c7981447cb8412268',
};

describe('Test generateConfigHashSignatureKeyMappings', () => {
  it('- key mappings should come from redis', async () => {
    const response = await generateConfigHashSignatureKeyMappings({
      ...args,
      keys: args.keys.slice(0, 2),
    });
    expect(response).toStrictEqual({
      abc: '06bec86f94ed09e3ad2908bad63b915091dd75de9df72aaad3f82a11',
      def: '06bec86f94ed09e3ad2908bad63b915091dd75de9df72aaad3f82a11',
    });
  });

  it('-Two should come from redis and one should be fetched from hsm as not in redis', async () => {
    const response = await generateConfigHashSignatureKeyMappings(args);
    expect(response).toStrictEqual({
      abc: '06bec86f94ed09e3ad2908bad63b915091dd75de9df72aaad3f82a11',
      def: '06bec86f94ed09e3ad2908bad63b915091dd75de9df72aaad3f82a11',
      ghi: 'mockedSignature',
    });
  });
});

describe('Test generateRedisKey', () => {
  it('- generated redis key must be equal', async () => {
    const redisKey = generateRedisKey({ key: args.keys[0], ...args });
    expect(redisKey).toBe('sgn:{unit-test}:0679b0a312f55ed0d1a2754d818c5129');
  });
  it('- generated redis keys with two args must be different', async () => {
    const redisKey1 = generateRedisKey({ key: args.keys[0], ...args });
    const redisKey2 = generateRedisKey({ key: args.keys[2], ...args });
    expect(redisKey1).not.toBe(redisKey2);
  });
});
