/* eslint-disable import/named */
import { describe, expect, it } from 'vitest';
import { createPrompt } from '../../../src/media-mgmt/prompts/prompts.handler';

describe('Test createPrompt', () => {
  it('- when screenOption is empty', async () => {
    const req = {
      body: {
        screenOption: '',
        code: 'testPrompt1',
        description: 'testPrompt1 description',
        promptType: 'pin',
        promptSetId: '1',
      },
    };

    const res = {
      prompts: [
        {
          id: '8c90d471-258a-43aa-b305-80ae72bf84e9',
          deviceType: 'G6-300',
          promptType: 1,
          backgroundColor: null,
          fontColor: null,
          fontSize: null,
          primaryAsset: null,
          staticText: null,
          company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
          textTop: null,
          elements: [
            {
              id: 'j6xSNeSC-',
              lock: false,
              type: 'bg',
              value: '0000ff',
            },
            {
              id: 'TiH64opjam',
              top: 20,
              face: 'Liberation Sans',
              left: 195,
              size: 48,
              type: 'input',
              color: 'ffffff',
              value: '????',
              width: 250,
              textAlign: 'center',
            },
          ],
          touchmapId: null,
          promptState: null,
          promptSet: '1',
          dayPart: null,
          transactionState: 'idle',
          contactless: false,
          thumbnailUrl: null,
          deleted: false,
          deletedBy: null,
          deletedDate: null,
          screenOption: 'main',
          auxPrompt: null,
          promptSetLanguageId: null,
        },
      ],
      states: [
        {
          promptStateOvrId: '1b48e84e-cf80-4423-aa09-32e735d567d8',
          promptId: '8c90d471-258a-43aa-b305-80ae72bf84e9',
          promptStateName: '',
          code: 'testPrompt1',
          description: 'testPrompt1 description',
          secure: false,
          numericInput: true,
          dynamicText: false,
          softKeys: false,
          active: true,
          promptType: 'pin',
          allowVideo: true,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          width: 250,
          fontSize: 24,
          exampleText: null,
          created: '2024-03-27T15:41:00.660Z',
          createdBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
          modified: '2024-03-27T15:41:00.660Z',
          modifiedBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        },
      ],
      exceptions: [],
    };

    let called = false;
    const next = () => {
      called = true;
    };
    await createPrompt(req, res, next);
    expect(called).toBeTruthy();
  });

  it('- when screenOption field is not provided', async () => {
    const req = {
      body: {
        code: 'testPrompt1',
        description: 'testPrompt1 description',
        promptType: 'pin',
        promptSetId: '1',
      },
    };

    const res = {
      prompts: [
        {
          id: '8c90d471-258a-43aa-b305-80ae72bf84e9',
          deviceType: 'G6-300',
          promptType: 1,
          backgroundColor: null,
          fontColor: null,
          fontSize: null,
          primaryAsset: null,
          staticText: null,
          company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
          textTop: null,
          elements: [
            {
              id: 'j6xSNeSC-',
              lock: false,
              type: 'bg',
              value: '0000ff',
            },
            {
              id: 'TiH64opjam',
              top: 20,
              face: 'Liberation Sans',
              left: 195,
              size: 48,
              type: 'input',
              color: 'ffffff',
              value: '????',
              width: 250,
              textAlign: 'center',
            },
          ],
          touchmapId: null,
          promptState: null,
          promptSet: '1',
          dayPart: null,
          transactionState: 'idle',
          contactless: false,
          thumbnailUrl: null,
          deleted: false,
          deletedBy: null,
          deletedDate: null,
          screenOption: 'main',
          auxPrompt: null,
          promptSetLanguageId: null,
        },
      ],
      states: [
        {
          promptStateOvrId: '1b48e84e-cf80-4423-aa09-32e735d567d8',
          promptId: '8c90d471-258a-43aa-b305-80ae72bf84e9',
          promptStateName: '',
          code: 'testPrompt1',
          description: 'testPrompt1 description',
          secure: false,
          numericInput: true,
          dynamicText: false,
          softKeys: false,
          active: true,
          promptType: 'pin',
          allowVideo: true,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          width: 250,
          fontSize: 24,
          exampleText: null,
          created: '2024-03-27T15:41:00.660Z',
          createdBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
          modified: '2024-03-27T15:41:00.660Z',
          modifiedBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        },
      ],
      exceptions: [],
    };

    let called = false;
    const next = () => {
      called = true;
    };
    await createPrompt(req, res, next);
    expect(called).toBeTruthy();
  });

  it('- when screenOption is main', async () => {
    const req = {
      body: {
        screenOption: 'main',
        code: 'testPrompt1',
        description: 'testPrompt1 description',
        promptType: 'pin',
        promptSetId: '1',
      },
    };

    const res = {
      prompts: [
        {
          id: '8c90d471-258a-43aa-b305-80ae72bf84e9',
          deviceType: 'G6-300',
          promptType: 1,
          backgroundColor: null,
          fontColor: null,
          fontSize: null,
          primaryAsset: null,
          staticText: null,
          company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
          textTop: null,
          elements: [
            {
              id: 'j6xSNeSC-',
              lock: false,
              type: 'bg',
              value: '0000ff',
            },
            {
              id: 'TiH64opjam',
              top: 20,
              face: 'Liberation Sans',
              left: 195,
              size: 48,
              type: 'input',
              color: 'ffffff',
              value: '????',
              width: 250,
              textAlign: 'center',
            },
          ],
          touchmapId: null,
          promptState: null,
          promptSet: '1',
          dayPart: null,
          transactionState: 'idle',
          contactless: false,
          thumbnailUrl: null,
          deleted: false,
          deletedBy: null,
          deletedDate: null,
          screenOption: 'main',
          auxPrompt: null,
          promptSetLanguageId: null,
        },
      ],
      states: [
        {
          promptStateOvrId: '1b48e84e-cf80-4423-aa09-32e735d567d8',
          promptId: '8c90d471-258a-43aa-b305-80ae72bf84e9',
          promptStateName: '',
          code: 'testPrompt1',
          description: 'testPrompt1 description',
          secure: false,
          numericInput: true,
          dynamicText: false,
          softKeys: false,
          active: true,
          promptType: 'pin',
          allowVideo: true,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          width: 250,
          fontSize: 24,
          exampleText: null,
          created: '2024-03-27T15:41:00.660Z',
          createdBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
          modified: '2024-03-27T15:41:00.660Z',
          modifiedBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        },
      ],
      exceptions: [],
    };

    let called = false;
    const next = () => {
      called = true;
    };
    await createPrompt(req, res, next);
    expect(called).toBeTruthy();
  });

  it('- when screenOption is aux but code is invalid', async () => {
    const req = {
      body: {
        screenOption: 'aux',
        code: 'testPrompt1',
        description: 'testPrompt1 description',
        promptType: 'pin',
        promptSetId: '1',
      },
    };

    const res = {
      prompts: [
        {
          id: '8c90d471-258a-43aa-b305-80ae72bf84e9',
          deviceType: 'G6-300',
          promptType: 1,
          backgroundColor: null,
          fontColor: null,
          fontSize: null,
          primaryAsset: null,
          staticText: null,
          company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
          textTop: null,
          elements: [
            {
              id: 'j6xSNeSC-',
              lock: false,
              type: 'bg',
              value: '0000ff',
            },
            {
              id: 'TiH64opjam',
              top: 20,
              face: 'Liberation Sans',
              left: 195,
              size: 48,
              type: 'input',
              color: 'ffffff',
              value: '????',
              width: 250,
              textAlign: 'center',
            },
          ],
          touchmapId: null,
          promptState: null,
          promptSet: '1',
          dayPart: null,
          transactionState: 'idle',
          contactless: false,
          thumbnailUrl: null,
          deleted: false,
          deletedBy: null,
          deletedDate: null,
          screenOption: 'main',
          auxPrompt: null,
          promptSetLanguageId: null,
        },
      ],
      states: [
        {
          promptStateOvrId: '1b48e84e-cf80-4423-aa09-32e735d567d8',
          promptId: '8c90d471-258a-43aa-b305-80ae72bf84e9',
          promptStateName: '',
          code: 'testPrompt1',
          description: 'testPrompt1 description',
          secure: false,
          numericInput: true,
          dynamicText: false,
          softKeys: false,
          active: true,
          promptType: 'pin',
          allowVideo: true,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          width: 250,
          fontSize: 24,
          exampleText: null,
          created: '2024-03-27T15:41:00.660Z',
          createdBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
          modified: '2024-03-27T15:41:00.660Z',
          modifiedBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        },
      ],
      exceptions: [],
    };

    let called = false;
    const next = () => {
      called = true;
    };
    await createPrompt(req, res, next);
    expect(called).toBeTruthy();
  });

  it('- when screenOption is aux and code is valid', async () => {
    const req = {
      body: {
        screenOption: 'aux',
        code: 'aux-testPrompt1',
        description: 'aux-testPrompt1 description',
        promptType: 'pin',
        promptSetId: '1',
      },
    };

    const res = {
      prompts: [
        {
          id: '8c90d471-258a-43aa-b305-80ae72bf84e9',
          deviceType: 'G6-300',
          promptType: 1,
          backgroundColor: null,
          fontColor: null,
          fontSize: null,
          primaryAsset: null,
          staticText: null,
          company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
          textTop: null,
          elements: [
            {
              id: 'j6xSNeSC-',
              lock: false,
              type: 'bg',
              value: '0000ff',
            },
            {
              id: 'TiH64opjam',
              top: 20,
              face: 'Liberation Sans',
              left: 195,
              size: 48,
              type: 'input',
              color: 'ffffff',
              value: '????',
              width: 250,
              textAlign: 'center',
            },
          ],
          touchmapId: null,
          promptState: null,
          promptSet: '1',
          dayPart: null,
          transactionState: 'idle',
          contactless: false,
          thumbnailUrl: null,
          deleted: false,
          deletedBy: null,
          deletedDate: null,
          screenOption: 'main',
          auxPrompt: null,
          promptSetLanguageId: null,
        },
      ],
      states: [
        {
          promptStateOvrId: '1b48e84e-cf80-4423-aa09-32e735d567d8',
          promptId: '8c90d471-258a-43aa-b305-80ae72bf84e9',
          promptStateName: '',
          code: 'testPrompt1',
          description: 'testPrompt1 description',
          secure: false,
          numericInput: true,
          dynamicText: false,
          softKeys: false,
          active: true,
          promptType: 'pin',
          allowVideo: true,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          width: 250,
          fontSize: 24,
          exampleText: null,
          created: '2024-03-27T15:41:00.660Z',
          createdBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
          modified: '2024-03-27T15:41:00.660Z',
          modifiedBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        },
      ],
      exceptions: [],
    };

    let called = false;
    const next = () => {
      called = true;
    };
    await createPrompt(req, res, next);
    expect(called).toBeTruthy();
  });
});
