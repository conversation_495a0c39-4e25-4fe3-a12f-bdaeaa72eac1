import { describe, expect, it } from 'vitest';

const {
  promptService,
  promptstateService,
} = require('../../../src/media-mgmt/imports.service');
const { promptsetRepo } = require('../../../src/media-mgmt/imports.repo');
const helper = require('../../../helpers/promptset-helper');

describe('validatePromptLinking', () => {
  it('should return BadRequestError if selected auxPrompt does not exist', async () => {
    // Mock data
    const auxPrompt = 'auxPrompt';
    const prompt = { auxPrompt: 'auxPrompt1' };
    const reqPromptCode = 'reqPromptCode';

    // Mock getPromptById function from promptService
    promptService.getPromptById = () => Promise.resolve(null);

    const result = await helper.validatePromptLinking(
      auxPrompt,
      prompt,
      reqPromptCode
    );
    expect(result.body.message).toEqual(
      'Provided auxiliary prompt does not exist'
    );
  });

  it('should return BadRequestError if selected auxPrompt id has a screentype of main', async () => {
    // Mock data
    const auxPrompt = 'auxPrompt';
    const prompt = { auxPrompt: 'auxPrompt1' };
    const reqPromptCode = 'reqPromptCode';

    // Mock getPromptById function from promptService
    promptService.getPromptById = () =>
      Promise.resolve({
        screenOption: 'main',
        promptType: 1,
      });

    const result = await helper.validatePromptLinking(
      auxPrompt,
      prompt,
      reqPromptCode
    );
    expect(result.body.message).toEqual('Selected prompt is not auxiliary');
  });

  it('should return BadRequestError when types mismatch', async () => {
    // Mock data
    const auxPrompt = 'auxPrompt';
    const prompt = { auxPrompt: 'auxPrompt1', promptType: 1 };
    const reqPromptCode = 'reqPromptCode';

    // Mock getPromptTypeById function from promptsetRepo for main prompt type
    promptService.getPromptById = () =>
      Promise.resolve({
        screenOption: 'aux',
        promptType: 1,
      });

    let counter = 0;
    promptsetRepo.getPromptTypeById = () => {
      const obj = {};
      if (counter === 0) {
        counter += 1;
        Object.assign(obj, { name: 'Data Entry' });
      } else {
        Object.assign(obj, { name: 'PIN Entry' });
      }
      return Promise.resolve(obj);
    };

    const result = await helper.validatePromptLinking(
      auxPrompt,
      prompt,
      reqPromptCode
    );
    expect(result.body.message).toEqual(
      'Cannot link aux prompt of type "PIN Entry" to a main prompt of type "Data Entry" for <b>reqPromptCode</b>'
    );
  });

  it('should pass the validation without any error', async () => {
    // Mock data
    const auxPrompt = 'auxPrompt';
    const prompt = { auxPrompt: 'auxPrompt1', promptType: 1 };
    const reqPromptCode = 'reqPromptCode';

    // Mock getPromptTypeById function from promptsetRepo for main prompt type
    promptService.getPromptById = () =>
      Promise.resolve({
        screenOption: 'aux',
        promptType: 1,
      });

    promptsetRepo.getPromptTypeById = () =>
      Promise.resolve({ name: 'Standard' });

    const result = await helper.validatePromptLinking(
      auxPrompt,
      prompt,
      reqPromptCode
    );
    expect(result).toBeUndefined();
  });
});

describe('insertPromptSetContent', () => {
  it('Should include aux_prompt and screen_option keys in the prompt object for G6-500 device', async () => {
    const promptSetId = 'eed3c2b1-0cb1-455d-8f15-3e3ec840d2b7';
    const user = {
      fullName: 'Default User',
      email: '<EMAIL>',
      type: 'Bearer',
      roles: [
        'COMPANY_ADMIN',
        'RKI',
        'MEDIA_DESIGNER',
        'MEDIA_APPROVER',
        'MEDIA_DEPLOYER',
        'FACTORY_RESET',
        'FUEL_PRICE_MGMT_VIEW',
        'FUEL_PRICE_MGMT_EDIT',
        'FUEL_PRICE_MGMT_CANCEL',
        'FUEL_PRICE_MGMT_THRESHOLD',
        'SITE_SETTINGS_VIEW',
        'SITE_SETTINGS_EDIT',
      ],
      created: 1708497676,
      company: {
        id: '369fc862-d084-11ee-93f2-f3cab3411d42',
        name: 'Invenco',
        featureFlags: [
          'REMOTE_MANAGEMENT',
          'OFFLINE_JOBS',
          'OFFLINE_RKI',
          'PROVISION_DEVICES',
          'DEVICES_SWAP_OUT',
          'MEDIA',
          'GSTV',
          'PLAYLIST',
          'PLAYLIST_MANAGEMENT',
          'FACTORY_RESET',
          'FUEL_PRICE_MGMT',
        ],
      },
      iat: 1716447195,
      nbf: 1716447195,
      exp: 61716447135,
      aud: 'invenco.cloud',
      iss: 'LOCAL - Invenco Cloud',
      sub: '36a8cea8-d084-11ee-93f2-2f7dd8d60940',
      jti: '7aa3127b-e959-4554-99b6-11a640d96dbf',
      getRoles: () => {},
      hasRole: () => {},
    };
    const assignments = [
      {
        state: {
          id: '79270e6d-8fb7-4512-89d2-65e0991b7c1d',
          name: null,
          code: 'ENTER-ODOMETER',
          description: 'Record vehicle odometer reading',
          secure: true,
          numericInput: true,
          dynamicText: false,
          softKeys: true,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          promptType: null,
          width: 250,
          fontSize: 24,
          screenOption: 'main',
          auxPrompt: null,
          sequence: '',
        },
        prompts: [
          {
            id: '14c8a56f-dc4f-41ed-a25a-1e96cf472da6',
            code: 'ENTER-ODOMETER',
            elements: [
              {
                id: '7EWaUjmkcm',
                lock: false,
                type: 'bg',
                value: '0000ff',
              },
              {
                id: 'l3-PM1iRro',
                top: 20,
                face: 'Liberation Sans',
                left: 235,
                size: 48,
                type: 'input',
                color: 'ffffff',
                value: '????',
                width: 250,
                textAlign: 'center',
              },
            ],
            promptState: '79270e6d-8fb7-4512-89d2-65e0991b7c1d',
            transactionState: null,
            contactless: false,
            thumbnailUrl: null,
            screenOption: 'main',
            auxPrompt: null,
            promptSetLanguageId: null,
            touchmap: null,
            softkeys: [],
            type: {
              id: 1,
            },
          },
        ],
        exceptions: [],
      },
      {
        state: {
          id: 'a2dda594-6074-4db8-bc47-7e907ca1fc69',
          name: null,
          code: 'ENTER-PIN',
          description: 'PIN entry screen',
          secure: false,
          numericInput: true,
          dynamicText: false,
          softKeys: true,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          promptType: null,
          width: 250,
          fontSize: 24,
          screenOption: 'main',
          auxPrompt: null,
          sequence: '',
        },
        prompts: [
          {
            id: '5b621df4-8185-45a8-8649-b705f93342a8',
            code: 'ENTER-PIN',
            elements: [
              {
                id: 'P4aO8_GIm_',
                lock: false,
                type: 'bg',
                value: '0000ff',
              },
              {
                id: 'zdxpP8proP',
                top: 20,
                face: 'Liberation Sans',
                left: 235,
                size: 48,
                type: 'input',
                color: 'ffffff',
                value: '????',
                width: 250,
                textAlign: 'center',
              },
            ],
            promptState: 'a2dda594-6074-4db8-bc47-7e907ca1fc69',
            transactionState: null,
            contactless: false,
            thumbnailUrl: null,
            screenOption: 'main',
            auxPrompt: null,
            promptSetLanguageId: null,
            touchmap: null,
            softkeys: [],
            type: {
              id: 1,
            },
          },
        ],
        exceptions: [],
      },
      {
        state: {
          id: '09914305-b636-41c3-9e57-bada8765d9d3',
          name: null,
          code: 'ERROR',
          description: 'Shown when an error occurs',
          secure: false,
          numericInput: false,
          dynamicText: true,
          softKeys: true,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          promptType: null,
          width: 250,
          fontSize: 24,
          screenOption: 'main',
          auxPrompt: null,
          sequence: '',
        },
        prompts: [
          {
            id: '9ff172b6-a12c-4017-823a-fe73905dd8ae',
            code: 'ERROR',
            elements: [
              {
                id: '1rTz4AtbRv',
                lock: false,
                type: 'bg',
                value: '0000ff',
              },
              {
                id: 'd8yoX8E3-a',
                top: 20,
                face: 'Liberation Sans',
                left: 235,
                size: 48,
                type: 'text',
                color: 'ffffff',
                value: '{{Message}}',
                width: 250,
                textAlign: 'center',
              },
              {
                id: 'qodGM-GpQB',
                top: 660,
                face: 'Liberation Sans',
                left: 235,
                size: 48,
                type: 'text',
                color: 'ffffff',
                value: '{{Message}}',
                width: 250,
                textAlign: 'center',
              },
            ],
            promptState: '09914305-b636-41c3-9e57-bada8765d9d3',
            transactionState: null,
            contactless: false,
            thumbnailUrl: null,
            screenOption: 'main',
            auxPrompt: null,
            promptSetLanguageId: null,
            touchmap: null,
            softkeys: [],
            type: {
              id: 1,
            },
          },
        ],
        exceptions: [],
      },
      {
        state: {
          id: '4c6b0502-28b3-4b03-b0d1-20e29c9d43be',
          name: null,
          code: 'FUELING',
          description: 'Shown while fuel is being delivered',
          secure: false,
          numericInput: false,
          dynamicText: false,
          softKeys: true,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          promptType: null,
          width: 250,
          fontSize: 24,
          screenOption: 'main',
          auxPrompt: null,
          sequence: '',
        },
        prompts: [
          {
            id: '73be6161-dc77-47ee-9623-2806ff8435fa',
            code: 'FUELING',
            elements: [
              {
                id: 'lBcpLq0ShL',
                lock: false,
                type: 'bg',
                value: '0000ff',
              },
            ],
            promptState: '4c6b0502-28b3-4b03-b0d1-20e29c9d43be',
            transactionState: null,
            contactless: false,
            thumbnailUrl: null,
            screenOption: 'main',
            auxPrompt: null,
            promptSetLanguageId: null,
            touchmap: null,
            softkeys: [],
            type: {
              id: 1,
            },
          },
        ],
        exceptions: [],
      },
      {
        state: {
          id: 'cacb0412-50f6-417d-ba93-5ce7cef5cf25',
          name: null,
          code: 'IDLE',
          description: 'Shown when terminal is idle',
          secure: false,
          numericInput: false,
          dynamicText: false,
          softKeys: true,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          promptType: null,
          width: 250,
          fontSize: 24,
          screenOption: 'main',
          auxPrompt: '9197b6ae-c4a4-4e3a-8466-5fe418553dc0',
          sequence: '',
        },
        prompts: [
          {
            id: '5591017e-9a94-4042-a09a-01074b85fab5',
            code: 'IDLE',
            elements: [
              {
                id: 'GNkZSm6P6',
                lock: false,
                type: 'bg',
                value: '0000ff',
              },
            ],
            promptState: 'cacb0412-50f6-417d-ba93-5ce7cef5cf25',
            transactionState: null,
            contactless: false,
            thumbnailUrl: null,
            screenOption: 'main',
            auxPrompt: '9197b6ae-c4a4-4e3a-8466-5fe418553dc0',
            promptSetLanguageId: null,
            touchmap: null,
            softkeys: [],
            type: {
              id: 1,
            },
          },
        ],
        exceptions: [],
      },
      {
        state: {
          id: '7ef537c4-8fc1-45b3-ac8f-cccd5be0e158',
          name: '',
          code: 'MAIN-DATA1',
          description: 'demo',
          secure: true,
          numericInput: true,
          dynamicText: false,
          softKeys: false,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          promptType: 'data',
          width: 250,
          fontSize: 24,
          screenOption: 'main',
          auxPrompt: 'c41c9032-5ff1-4a30-a0af-86490992f5e8',
          sequence: '',
        },
        prompts: [
          {
            id: '6ec99493-b8d8-4bd4-9720-3fafc00daa53',
            code: 'MAIN-DATA1',
            elements: [
              {
                id: 'n9BfxHFJ_',
                lock: false,
                type: 'bg',
                value: '0000ff',
              },
              {
                id: '-tBBVLbsKh',
                top: 20,
                face: 'Liberation Sans',
                left: 235,
                size: 48,
                type: 'input',
                color: 'ffffff',
                value: '????',
                width: 250,
                textAlign: 'center',
              },
            ],
            promptState: null,
            transactionState: 'idle',
            contactless: false,
            thumbnailUrl: null,
            screenOption: 'main',
            auxPrompt: 'c41c9032-5ff1-4a30-a0af-86490992f5e8',
            promptSetLanguageId: null,
            touchmap: null,
            softkeys: [],
            type: {
              id: 1,
            },
          },
        ],
        exceptions: [],
      },
      {
        state: {
          id: '267c88a4-9458-4486-8bf4-a4ad3d939fe8',
          name: '',
          code: 'MAIN-STD1',
          description: 'demo',
          secure: false,
          numericInput: false,
          dynamicText: false,
          softKeys: false,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          promptType: 'standard',
          width: 250,
          fontSize: 24,
          screenOption: 'main',
          auxPrompt: 'bebbec80-4ee6-4914-a3f5-99e4cc7ac7d9',
          sequence: '',
        },
        prompts: [
          {
            id: 'abc4441e-4e8b-4164-a6ae-624529bd8ff9',
            code: 'MAIN-STD1',
            elements: [
              {
                id: 'Xc2U41Nrs',
                lock: false,
                type: 'bg',
                value: '0000ff',
              },
              {
                id: 'vIcrpWgyYn',
                top: 20,
                face: 'Liberation Sans',
                left: 235,
                size: 48,
                type: 'text',
                color: 'ffffff',
                value: 'Enter Message',
                width: 250,
                textAlign: 'center',
                userclass: '',
              },
            ],
            promptState: null,
            transactionState: 'idle',
            contactless: false,
            thumbnailUrl: null,
            screenOption: 'main',
            auxPrompt: 'bebbec80-4ee6-4914-a3f5-99e4cc7ac7d9',
            promptSetLanguageId: null,
            touchmap: null,
            softkeys: [],
            type: {
              id: 1,
            },
          },
        ],
        exceptions: [],
      },
      {
        state: {
          id: '9c8b8bea-9030-45c6-bb6d-79ae6ec6dc56',
          name: '',
          code: 'aux-DATA',
          description: 'demo',
          secure: true,
          numericInput: true,
          dynamicText: false,
          softKeys: false,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          promptType: 'data',
          width: 250,
          fontSize: 24,
          screenOption: 'aux',
          auxPrompt: null,
          sequence: '',
        },
        prompts: [
          {
            id: 'bebbec80-4ee6-4914-a3f5-99e4cc7ac7d9',
            code: 'aux-DATA',
            elements: [
              {
                id: 'YZLtUiY5T',
                lock: false,
                type: 'bg',
                value: '0000ff',
              },
              {
                id: '016SokudOe',
                top: 20,
                face: 'Liberation Sans',
                left: 235,
                size: 48,
                type: 'input',
                color: 'ffffff',
                value: '????',
                width: 250,
                textAlign: 'center',
              },
            ],
            promptState: null,
            transactionState: 'idle',
            contactless: false,
            thumbnailUrl: null,
            screenOption: 'aux',
            auxPrompt: null,
            promptSetLanguageId: null,
            touchmap: null,
            softkeys: [],
            type: {
              id: 1,
            },
          },
        ],
        exceptions: [],
      },
      {
        state: {
          id: '30f23411-8739-4079-a949-d896a21a175a',
          name: '',
          code: 'aux-PIN',
          description: 'demo',
          secure: false,
          numericInput: true,
          dynamicText: false,
          softKeys: false,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          promptType: 'pin',
          width: 250,
          fontSize: 24,
          screenOption: 'aux',
          auxPrompt: null,
          sequence: '',
        },
        prompts: [
          {
            id: '9197b6ae-c4a4-4e3a-8466-5fe418553dc0',
            code: 'aux-PIN',
            elements: [
              {
                id: '4tjZXBmVH',
                lock: false,
                type: 'bg',
                value: '0000ff',
              },
              {
                id: 'QO_ajTbHTr',
                top: 20,
                face: 'Liberation Sans',
                left: 235,
                size: 48,
                type: 'input',
                color: 'ffffff',
                value: '????',
                width: 250,
                textAlign: 'center',
              },
            ],
            promptState: null,
            transactionState: 'idle',
            contactless: false,
            thumbnailUrl: null,
            screenOption: 'aux',
            auxPrompt: null,
            promptSetLanguageId: null,
            touchmap: null,
            softkeys: [],
            type: {
              id: 1,
            },
          },
        ],
        exceptions: [],
      },
      {
        state: {
          id: '514ee7ae-f9c2-4260-b8cf-e026bdd6ef59',
          name: '',
          code: 'aux-STANDARD',
          description: 'demo',
          secure: false,
          numericInput: false,
          dynamicText: false,
          softKeys: false,
          attribs: {
            'cless-msg-draw': false,
            'cless-event-msg': true,
          },
          promptType: 'standard',
          width: 250,
          fontSize: 24,
          screenOption: 'aux',
          auxPrompt: null,
          sequence: '',
        },
        prompts: [
          {
            id: 'c41c9032-5ff1-4a30-a0af-86490992f5e8',
            code: 'aux-STANDARD',
            elements: [
              {
                id: 'XoiBat6e7',
                lock: false,
                type: 'bg',
                value: '0000ff',
              },
              {
                id: 'zpz4MkoMH2',
                top: 20,
                face: 'Liberation Sans',
                left: 235,
                size: 48,
                type: 'text',
                color: 'ffffff',
                value: 'Enter Message',
                width: 250,
                textAlign: 'center',
              },
            ],
            promptState: null,
            transactionState: 'idle',
            contactless: false,
            thumbnailUrl: null,
            screenOption: 'aux',
            auxPrompt: null,
            promptSetLanguageId: null,
            touchmap: null,
            softkeys: [],
            type: {
              id: 1,
            },
          },
        ],
        exceptions: [],
      },
    ];
    const deviceType = 'G6-500';
    promptstateService.getPromptStateByPrompt = () =>
      Promise.resolve({
        code: '123',
        description: 'test',
        secure: 'test',
        attribs: 'test',
        numericInput: 1,
        dynamicText: 'test',
        softKeys: 'test',
        promptType: 'test',
        allowVideo: 'test',
        width: 'test',
        fontSize: 'test',
        exampleText: 'test',
      });
    const conn = {
      execute: () => {},
    };
    conn.execute = () => Promise.resolve(null);
    const result = await helper.insertPromptSetContent(
      promptSetId,
      user,
      assignments,
      conn,
      deviceType
    );
    expect(result).toBeUndefined();
  });
});
