import { describe, it, expect, vi } from 'vitest';
import restify from 'restify-errors';
import { getPromptSetPackage } from '../../../handlers/media/promptsets';
import { server } from '../../../app';

describe('getPromptSetPackage', () => {
  it('should return software package if found', async () => {
    const req = {
      user: {
        company: {
          id: 1,
        },
      },
      params: {
        id: '123',
      },
    };
    const res = {
      send: vi.fn(),
    };
    const next = vi.fn();
    const mockSoftware = {
      software_id: 123,
      software_file: 'file_content',
    };
    server.db.read = {
      row: () => Promise.resolve(mockSoftware),
    };

    await getPromptSetPackage(req, res, next);

    expect(res.send).toHaveBeenCalledWith(200, mockSoftware);
  });

  it('should return 404 if package not found', async () => {
    const req = {
      user: {
        company: {
          id: 1,
        },
      },
      params: {
        id: '123',
      },
    };
    const res = {
      send: vi.fn(),
    };
    const next = vi.fn();
    server.db.read = {
      row: () => Promise.resolve(null),
    };

    await getPromptSetPackage(req, res, next);

    expect(next).toHaveBeenCalledOnce();
    expect(next).toHaveBeenCalledWith(
      new restify.NotFoundError('Package not found')
    );
  });
});
