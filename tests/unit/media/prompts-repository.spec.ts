/* eslint-disable import/named */
import { describe, it, expect } from 'vitest';
import { createPrompts } from '../../../src/media-mgmt/prompts/prompts.repository';

describe('Test createPrompts repository', () => {
  it('- when screenOption is main', async () => {
    const mockResp = {
      rows: [
        {
          id: 'b4f3ee2c-4fce-4105-99a7-e2779c2cb3ec',
          deviceType: 'G6-300',
          promptType: 1,
          backgroundColor: null,
          fontColor: null,
          fontSize: null,
          primaryAsset: null,
          staticText: null,
          company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
          textTop: null,
          elements: [[Object], [Object]],
          touchmapId: null,
          promptState: null,
          promptSet: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
          dayPart: null,
          transactionState: 'idle',
          contactless: false,
          thumbnailUrl: null,
          deleted: false,
          deletedBy: null,
          deletedDate: null,
          promptSetLanguageSupportId: null,
          screenOption: 'main',
          auxPrompt: null,
        },
      ],
    };
    const conectionObj = {
      execute: function propFn() {
        return Promise.resolve(mockResp);
      },
    };

    const prompts = [
      {
        deviceType: 'G6-300',
        promptType: 1,
        company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
        elements:
          '[{"id":"JQUsktVjL","type":"bg","value":"0000ff","lock":false},{"id":"z06pqKDT8h","type":"input","value":"????","color":"ffffff","top":20,"left":195,"width":250,"textAlign":"center","face":"Liberation Sans","size":48}]',
        promptSet: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
        promptState: null,
        transactionState: 'idle',
        touchmapId: undefined,
        dayPart: undefined,
        promptSetLanguageSupportId: undefined,
        screenOption: 'main',
      },
    ];

    const res = await createPrompts(prompts, conectionObj);
    expect(res[0].screenOption).toBe(mockResp.rows[0].screenOption);
  });

  it('- when screenOption is aux', async () => {
    const mockResp = {
      rows: [
        {
          id: 'b4f3ee2c-4fce-4105-99a7-e2779c2cb3ec',
          deviceType: 'G6-300',
          promptType: 1,
          backgroundColor: null,
          fontColor: null,
          fontSize: null,
          primaryAsset: null,
          staticText: null,
          company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
          textTop: null,
          elements: [[Object], [Object]],
          touchmapId: null,
          promptState: null,
          promptSet: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
          dayPart: null,
          transactionState: 'idle',
          contactless: false,
          thumbnailUrl: null,
          deleted: false,
          deletedBy: null,
          deletedDate: null,
          promptSetLanguageSupportId: null,
          screenOption: 'aux',
          auxPrompt: null,
        },
      ],
    };
    const conectionObj = {
      execute: function propFn() {
        return Promise.resolve(mockResp);
      },
    };

    const prompts = [
      {
        deviceType: 'G6-300',
        promptType: 1,
        company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
        elements:
          '[{"id":"JQUsktVjL","type":"bg","value":"0000ff","lock":false},{"id":"z06pqKDT8h","type":"input","value":"????","color":"ffffff","top":20,"left":195,"width":250,"textAlign":"center","face":"Liberation Sans","size":48}]',
        promptSet: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
        promptState: null,
        transactionState: 'idle',
        touchmapId: undefined,
        dayPart: undefined,
        promptSetLanguageSupportId: undefined,
        screenOption: 'aux',
      },
    ];

    const res = await createPrompts(prompts, conectionObj);
    expect(res[0].screenOption).toBe(mockResp.rows[0].screenOption);
  });
});
