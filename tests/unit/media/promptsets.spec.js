import { describe, expect, it, vi } from 'vitest';

const repository = require('../../../handlers/media/promptsets.repository');

const {
  getAllPromptSetProfiles,
} = require('../../../handlers/media/promptsets');

describe('getPromptSetprofiles', () => {
  it('should return data with pagination', async () => {
    const mockResp = {
      resultsMetadata: {
        totalResults: 3,
        pageIndex: 0,
        pageSize: 3,
      },
      results: [
        {
          id: 'G6-200',
          label: 'G6-200',
          mainResolutions: '640x480',
          auxResolutions: null,
          secModel: 'g6200',
        },
      ],
    };

    const getPromptSetProfilesMock = vi
      .spyOn(repository, 'getPromptSetProfiles')
      .mockResolvedValue([
        {
          name: 'G6-500',
        },
      ]);

    const createMockResponse = () => {
      const res = {};
      res.status = vi.fn().mockReturnValue(200);
      res.send = vi.fn().mockReturnValue(mockResp);
      return res;
    };
    const req = {
      params: {
        pageIndex: 1,
        pageSize: 2,
      },
      user: {
        company: {
          id: '4d7301f8-c076-11ee-903b-5f85ff1e01e4',
        },
      },
      log() {},
    };
    const res = createMockResponse();
    const next = vi.fn();
    await getAllPromptSetProfiles(req, res, next);
    getPromptSetProfilesMock.mockRestore();
    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
    expect(res.send).toHaveBeenCalled();
  });
});

describe('getPromptSetprofiles - Negative Test Case', () => {
  it('should handle error from repository', async () => {
    const getPromptSetProfilesMock = vi
      .spyOn(repository, 'getPromptSetProfiles')
      .mockRejectedValue(new Error('Repository Error'));

    const createMockResponse = () => {
      const res = {};
      res.status = vi.fn().mockReturnThis();
      res.send = vi.fn();
      return res;
    };

    const req = {
      params: {
        pageIndex: 1,
        pageSize: 2,
      },
      user: {
        company: {
          id: '4d7301f8-c076-11ee-903b-5f85ff1e01e4',
        },
      },
      log: {
        error: vi.fn(),
      },
    };

    const res = createMockResponse();
    const next = vi.fn();

    await getAllPromptSetProfiles(req, res, next);

    expect(getPromptSetProfilesMock).toHaveBeenCalled();
    expect(next).toHaveBeenCalledWith(expect.any(Error));
    expect(res.status).not.toHaveBeenCalled();
    expect(res.send).not.toHaveBeenCalled();

    getPromptSetProfilesMock.mockRestore();
  });
});
