/* eslint-disable import/named */
import { describe, it, expect, vi } from 'vitest';
import {
  createPrompts,
  renamePrompt,
} from '../../../src/media-mgmt/prompts/prompts.service';
import { server } from '../../../app';

const {
  promptRepo,
  promptsetRepo,
} = require('../../../src/media-mgmt/imports.repo');
const validator = require('../../../src/media-mgmt/prompts/prompts.validator');

describe('Test Prompts service', () => {
  it('- when screenOption is main', async () => {
    // Mocking the validateCreatePrompt function
    validator.validateCreatePrompt = () => true;

    // Mocking the createPrompts function
    promptRepo.createPrompts = () =>
      Promise.resolve([
        {
          id: 'f3248768-06a4-4462-aa11-8003e6db8932',
          deviceType: 'G6-300',
          promptType: 1,
          backgroundColor: null,
          fontColor: null,
          fontSize: null,
          primaryAsset: null,
          staticText: null,
          company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
          textTop: null,
          elements: [[Object], [Object]],
          touchmapId: null,
          promptState: null,
          promptSet: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
          dayPart: null,
          transactionState: 'idle',
          contactless: false,
          thumbnailUrl: null,
          deleted: false,
          deletedBy: null,
          deletedDate: null,
          promptSetLanguageSupportId: null,
          screenOption: 'main',
          auxPrompt: null,
        },
      ]);

    // Mocking the getPromptSetById function
    promptsetRepo.getPromptSetById = () =>
      Promise.resolve({
        id: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
        promptTemplate: '21b365d0-0e3c-42a9-aeae-258b766414d9',
        deviceType: 'G6-300',
        company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
        name: 't1w',
        createdBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        modifiedBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        active: true,
        secureAssetPackage: null,
        status: 'DRAFT',
        published: null,
        publishedBy: null,
        publishedPackageUrl: null,
        clonedFrom: null,
        bg: '0000ff',
        version: '0.3.0',
        firstApprover: null,
        firstApprovedTime: null,
        secondApprover: null,
        secondApprovedTime: null,
        rootId: null,
        fontColor: 'ffffff',
        secureFingerprint: null,
        nonSecureFingerprint: null,
      });

    // Mocking the createPrompts function
    promptsetRepo.getPromptSetProductType = () =>
      Promise.resolve({
        id: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
        promptTemplate: '21b365d0-0e3c-42a9-aeae-258b766414d9',
        deviceType: 'G6-300',
        company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
        name: 't1w',
        createdBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        modifiedBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        active: true,
        secureAssetPackage: null,
        status: 'DRAFT',
        published: null,
        publishedBy: null,
        publishedPackageUrl: null,
        clonedFrom: null,
        bg: '0000ff',
        version: '0.3.0',
        firstApprover: null,
        firstApprovedTime: null,
        secondApprover: null,
        secondApprovedTime: null,
        rootId: null,
        fontColor: 'ffffff',
        secureFingerprint: null,
        nonSecureFingerprint: null,
      });

    // Mocking the updatePromptSetVersion function
    promptsetRepo.updatePromptSetVersion = () =>
      Promise.resolve({
        id: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
        promptTemplate: '21b365d0-0e3c-42a9-aeae-258b766414d9',
        deviceType: 'G6-300',
        company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
        name: 't1w',
        createdBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        modifiedBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        active: true,
        secureAssetPackage: null,
        status: 'DRAFT',
        published: null,
        publishedBy: null,
        publishedPackageUrl: null,
        clonedFrom: null,
        bg: '0000ff',
        version: '0.3.0',
        firstApprover: null,
        firstApprovedTime: null,
        secondApprover: null,
        secondApprovedTime: null,
        rootId: null,
        fontColor: 'ffffff',
        secureFingerprint: null,
        nonSecureFingerprint: null,
      });

    const promptSetId = '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6';
    const prompts = [
      {
        promptSet: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
        promptType: 'pin',
        elements: [[Object], [Object]],
        code: 'test1',
        promptSetLanguageSupportId: undefined,
        screenOption: 'main',
      },
    ];
    const user = {
      fullName: 'Default User',
      email: '<EMAIL>',
      type: 'Bearer',
      roles: [
        'COMPANY_ADMIN',
        'RKI',
        'MEDIA_DESIGNER',
        'MEDIA_APPROVER',
        'MEDIA_DEPLOYER',
        'FACTORY_RESET',
        'FUEL_PRICE_MGMT_VIEW',
        'FUEL_PRICE_MGMT_EDIT',
        'FUEL_PRICE_MGMT_CANCEL',
        'FUEL_PRICE_MGMT_THRESHOLD',
        'SITE_SETTINGS_VIEW',
        'SITE_SETTINGS_EDIT',
      ],
      created: 1711538172,
      company: {
        id: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
        name: 'Invenco',
        featureFlags: [
          'REMOTE_MANAGEMENT',
          'OFFLINE_JOBS',
          'OFFLINE_RKI',
          'PROVISION_DEVICES',
          'DEVICES_SWAP_OUT',
          'MEDIA',
          'GSTV',
          'PLAYLIST',
          'PLAYLIST_MANAGEMENT',
          'FACTORY_RESET',
          'FUEL_PRICE_MGMT',
        ],
      },
      iat: 1711553979,
      nbf: 1711553979,
      exp: 2071553979,
      aud: 'invenco.cloud',
      iss: 'LOCAL - Invenco Cloud',
      sub: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
      jti: '846feba2-20ee-42f3-9a17-0c984ab44635',
    };
    const aws = undefined;
    const connectStub = { execute: () => {} };

    const expectedRes = [
      {
        id: 'f3248768-06a4-4462-aa11-8003e6db8932',
        deviceType: 'G6-300',
        promptType: 1,
        backgroundColor: null,
        fontColor: null,
        fontSize: null,
        primaryAsset: null,
        staticText: null,
        company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
        textTop: null,
        elements: [[Object], [Object]],
        touchmapId: null,
        promptState: null,
        promptSet: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
        dayPart: null,
        transactionState: 'idle',
        contactless: false,
        thumbnailUrl: null,
        deleted: false,
        deletedBy: null,
        deletedDate: null,
        promptSetLanguageSupportId: null,
        screenOption: 'main',
        auxPrompt: null,
      },
    ];
    const res = await createPrompts(
      promptSetId,
      prompts,
      user,
      aws,
      connectStub
    );
    expect(res).toEqual(expectedRes);
  });

  it('- when screenOption is aux', async () => {
    validator.validateCreatePrompt = () => true;
    promptRepo.createPrompts = () =>
      Promise.resolve([
        {
          id: 'f3248768-06a4-4462-aa11-8003e6db8932',
          deviceType: 'G6-300',
          promptType: 1,
          backgroundColor: null,
          fontColor: null,
          fontSize: null,
          primaryAsset: null,
          staticText: null,
          company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
          textTop: null,
          elements: [[Object], [Object]],
          touchmapId: null,
          promptState: null,
          promptSet: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
          dayPart: null,
          transactionState: 'idle',
          contactless: false,
          thumbnailUrl: null,
          deleted: false,
          deletedBy: null,
          deletedDate: null,
          promptSetLanguageSupportId: null,
          screenOption: 'aux',
          auxPrompt: null,
        },
      ]);
    promptsetRepo.getPromptSetById = () =>
      Promise.resolve({
        id: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
        promptTemplate: '21b365d0-0e3c-42a9-aeae-258b766414d9',
        deviceType: 'G6-300',
        company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
        name: 't1w',
        createdBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        modifiedBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        active: true,
        secureAssetPackage: null,
        status: 'DRAFT',
        published: null,
        publishedBy: null,
        publishedPackageUrl: null,
        clonedFrom: null,
        bg: '0000ff',
        version: '0.3.0',
        firstApprover: null,
        firstApprovedTime: null,
        secondApprover: null,
        secondApprovedTime: null,
        rootId: null,
        fontColor: 'ffffff',
        secureFingerprint: null,
        nonSecureFingerprint: null,
      });

    promptsetRepo.getPromptSetProductType = () =>
      Promise.resolve({
        id: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
        promptTemplate: '21b365d0-0e3c-42a9-aeae-258b766414d9',
        deviceType: 'G6-300',
        company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
        name: 't1w',
        createdBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        modifiedBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        active: true,
        secureAssetPackage: null,
        status: 'DRAFT',
        published: null,
        publishedBy: null,
        publishedPackageUrl: null,
        clonedFrom: null,
        bg: '0000ff',
        version: '0.3.0',
        firstApprover: null,
        firstApprovedTime: null,
        secondApprover: null,
        secondApprovedTime: null,
        rootId: null,
        fontColor: 'ffffff',
        secureFingerprint: null,
        nonSecureFingerprint: null,
      });

    promptsetRepo.updatePromptSetVersion = () =>
      Promise.resolve({
        id: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
        promptTemplate: '21b365d0-0e3c-42a9-aeae-258b766414d9',
        deviceType: 'G6-300',
        company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
        name: 't1w',
        createdBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        modifiedBy: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
        active: true,
        secureAssetPackage: null,
        status: 'DRAFT',
        published: null,
        publishedBy: null,
        publishedPackageUrl: null,
        clonedFrom: null,
        bg: '0000ff',
        version: '0.3.0',
        firstApprover: null,
        firstApprovedTime: null,
        secondApprover: null,
        secondApprovedTime: null,
        rootId: null,
        fontColor: 'ffffff',
        secureFingerprint: null,
        nonSecureFingerprint: null,
      });

    const promptSetId = '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6';
    const prompts = [
      {
        promptSet: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
        promptType: 'pin',
        elements: [[Object], [Object]],
        code: 'aux-test1',
        promptSetLanguageSupportId: undefined,
        screenOption: 'aux',
      },
    ];
    const user = {
      fullName: 'Default User',
      email: '<EMAIL>',
      type: 'Bearer',
      roles: [
        'COMPANY_ADMIN',
        'RKI',
        'MEDIA_DESIGNER',
        'MEDIA_APPROVER',
        'MEDIA_DEPLOYER',
        'FACTORY_RESET',
        'FUEL_PRICE_MGMT_VIEW',
        'FUEL_PRICE_MGMT_EDIT',
        'FUEL_PRICE_MGMT_CANCEL',
        'FUEL_PRICE_MGMT_THRESHOLD',
        'SITE_SETTINGS_VIEW',
        'SITE_SETTINGS_EDIT',
      ],
      created: 1711538172,
      company: {
        id: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
        name: 'Invenco',
        featureFlags: [
          'REMOTE_MANAGEMENT',
          'OFFLINE_JOBS',
          'OFFLINE_RKI',
          'PROVISION_DEVICES',
          'DEVICES_SWAP_OUT',
          'MEDIA',
          'GSTV',
          'PLAYLIST',
          'PLAYLIST_MANAGEMENT',
          'FACTORY_RESET',
          'FUEL_PRICE_MGMT',
        ],
      },
      iat: 1711553979,
      nbf: 1711553979,
      exp: 2071553979,
      aud: 'invenco.cloud',
      iss: 'LOCAL - Invenco Cloud',
      sub: '6b35488c-ec2b-11ee-b27c-fbe65df90d24',
      jti: '846feba2-20ee-42f3-9a17-0c984ab44635',
    };
    const aws = undefined;
    const connectStub = { execute: () => {} };

    const expectedRes = [
      {
        id: 'f3248768-06a4-4462-aa11-8003e6db8932',
        deviceType: 'G6-300',
        promptType: 1,
        backgroundColor: null,
        fontColor: null,
        fontSize: null,
        primaryAsset: null,
        staticText: null,
        company: '6b3487e4-ec2b-11ee-b27c-6794c743566b',
        textTop: null,
        elements: [[Object], [Object]],
        touchmapId: null,
        promptState: null,
        promptSet: '2b47738e-2d4a-43f3-bd13-4952b6f3d2d6',
        dayPart: null,
        transactionState: 'idle',
        contactless: false,
        thumbnailUrl: null,
        deleted: false,
        deletedBy: null,
        deletedDate: null,
        promptSetLanguageSupportId: null,
        screenOption: 'aux',
        auxPrompt: null,
      },
    ];
    const res = await createPrompts(
      promptSetId,
      prompts,
      user,
      aws,
      connectStub
    );
    expect(expectedRes).toEqual(res);
  });
  it('- When prompt name or description is changed', async () => {
    const connectStub = { execute: () => {} };
    const promptState = {};
    const promptSetId = 'test-123';
    const promptId = 'test-123';
    const screenOption = 'main';
    const code = 'renamed-prompt-name';
    const description = 'renamed-prompt-description';
    const promptCodeExists = 0;

    let counter = 0;
    const existingPromptStateDetails = {};
    const existingPromptStateOvrDetails = {};
    server.db.read.row = () => {
      if (counter === 0) {
        counter += 1;
        Object.assign(existingPromptStateDetails, {
          code: 'name',
          description: 'description',
        });
      } else
        Object.assign(existingPromptStateOvrDetails, {
          code: 'name',
          description: 'description',
        });
    };

    const expectedRes = await renamePrompt(
      connectStub,
      promptState,
      promptSetId,
      promptId,
      screenOption,
      code,
      description,
      promptCodeExists
    );
    expect(expectedRes).toEqual({ errorCode: null });
  });
  it('- When prompt name is empty', async () => {
    const connectStub = { execute: () => {} };
    const promptState = {};
    const promptSetId = 'test-123';
    const promptId = 'test-123';
    const screenOption = 'main';
    const code = '';
    const description = 'renamed-prompt-description';
    const promptCodeExists = 0;
    server.db.read = { row: vi.fn() };
    const expectedRes = await renamePrompt(
      connectStub,
      promptState,
      promptSetId,
      promptId,
      screenOption,
      code,
      description,
      promptCodeExists
    );
    expect(expectedRes).toEqual({
      errorCode: 400,
      errorMsg: 'Prompt name cannot be empty',
    });
  });
  it('- When prompt description is empty', async () => {
    const connectStub = { execute: () => {} };
    const promptState = {};
    const promptSetId = 'test-123';
    const promptId = 'test-123';
    const screenOption = 'main';
    const code = 'renamed';
    const description = '';
    const promptCodeExists = 0;

    server.db.read = { row: vi.fn() };

    const expectedRes = await renamePrompt(
      connectStub,
      promptState,
      promptSetId,
      promptId,
      screenOption,
      code,
      description,
      promptCodeExists
    );
    expect(expectedRes).toEqual({
      errorCode: 400,
      errorMsg: 'Prompt description cannot be empty',
    });
  });
  it('- When it is a default Prompt', async () => {
    const connectStub = { execute: () => {} };
    const promptState = { id: 'test' };
    const promptSetId = 'test-123';
    const promptId = 'test-123';
    const screenOption = 'main';
    const code = 'rename';
    const description = 'changed';
    const promptCodeExists = 0;

    const existingPromptStateDetails = {};
    server.db.read = {
      row: () =>
        Promise.resolve(
          Object.assign(existingPromptStateDetails, {
            code: 'name',
            description: 'description',
          })
        ),
    };
    const expectedRes = await renamePrompt(
      connectStub,
      promptState,
      promptSetId,
      promptId,
      screenOption,
      code,
      description,
      promptCodeExists
    );
    expect(expectedRes).toEqual({
      errorCode: 400,
      errorMsg: 'Cannot change the name or description of a default prompt',
    });
  });
  it('- When unique prompt name given', async () => {
    const connectStub = { execute: () => {} };
    const promptState = {};
    const promptSetId = 'test-123';
    const promptId = 'test-123';
    const screenOption = 'main';
    const code = 'rename';
    const description = 'changed';
    const promptCodeExists = 1;

    const existingPromptStateOvrDetails = {};
    server.db.read = {
      row: () =>
        Promise.resolve(
          Object.assign(existingPromptStateOvrDetails, {
            code: 'name',
            description: 'description',
          })
        ),
    };

    const expectedRes = await renamePrompt(
      connectStub,
      promptState,
      promptSetId,
      promptId,
      screenOption,
      code,
      description,
      promptCodeExists
    );
    expect(expectedRes).toEqual({
      errorCode: 400,
      errorMsg: 'Unique name required',
    });
  });
  it('- When prompt name has special Characters', async () => {
    const connectStub = { execute: () => {} };
    const promptState = {};
    const promptSetId = 'test-123';
    const promptId = 'test-123';
    const screenOption = 'main';
    const code = 'rename!';
    const description = 'changed';
    const promptCodeExists = 0;

    const existingPromptStateOvrDetails = {};
    server.db.read = {
      row: () =>
        Promise.resolve(
          Object.assign(existingPromptStateOvrDetails, {
            code: 'name',
            description: 'description',
          })
        ),
    };

    const expectedRes = await renamePrompt(
      connectStub,
      promptState,
      promptSetId,
      promptId,
      screenOption,
      code,
      description,
      promptCodeExists
    );
    expect(expectedRes).toEqual({
      errorCode: 400,
      errorMsg:
        'Prompt name must not include any special characters except hyphens (-) and underscores (_)',
    });
  });
  it('- When it is an aux prompt', async () => {
    const connectStub = { execute: () => {} };
    const promptState = {};
    const promptSetId = 'test-123';
    const promptId = 'test-123';
    const screenOption = 'aux';
    const code = 'rename';
    const description = 'changed';
    const promptCodeExists = 0;

    const existingPromptStateOvrDetails = {};
    server.db.read = {
      row: () =>
        Promise.resolve(
          Object.assign(existingPromptStateOvrDetails, {
            code: 'name',
            description: 'description',
          })
        ),
    };

    const expectedRes = await renamePrompt(
      connectStub,
      promptState,
      promptSetId,
      promptId,
      screenOption,
      code,
      description,
      promptCodeExists
    );
    expect(expectedRes).toEqual({
      errorCode: 400,
      errorMsg:
        "The name of an auxiliary prompt must start with the prefix 'aux-'.",
    });
  });
});
