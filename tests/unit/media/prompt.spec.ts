import { describe, it, expect, vi } from 'vitest';
import restify from 'restify-errors';
import { server } from '../../../app';
import errors from '../../../src/media-mgmt/errors';

const { deletePrompt } = require('../../../handlers/media/prompt');
const { promptService } = require('../../../src/media-mgmt/imports.service');
const { promptsetRepo } = require('../../../src/media-mgmt/imports.repo');

describe('deletePrompt', () => {
  it('should return 404 if user is not found', async () => {
    const req = { params: { id: 'promptId' }, user: {} };
    const res = { send: vi.fn() };
    const next = vi.fn();

    await deletePrompt(req, res, next);

    expect(next).toHaveBeenCalledWith(
      new restify.NotFoundError(errors.user.notFound)
    );
  });

  it('should return 404 if prompt is not found', async () => {
    const req = {
      params: { id: 'promptId' },
      user: { sub: 'userId', company: { id: 'companyId' } },
    };
    const res = { send: vi.fn() };
    const next = vi.fn();

    promptService.getPromptById = () => Promise.resolve(null);

    await deletePrompt(req, res, next);

    expect(next).toHaveBeenCalledWith(
      new restify.NotFoundError(errors.prompt.notFound)
    );
  });

  it('should return 400 if prompt is already deleted', async () => {
    const req = {
      params: { id: 'promptId' },
      user: { sub: 'userId', company: { id: 'companyId' } },
    };
    const res = { send: vi.fn() };
    const next = vi.fn();

    promptService.getPromptById = () => Promise.resolve({ deleted: true });

    await deletePrompt(req, res, next);

    expect(next).toHaveBeenCalledWith(
      new restify.NotFoundError(errors.prompt.alreadyDeleted)
    );
  });

  it('should return 401 if user is not authorized', async () => {
    const req = {
      params: { id: 'promptId' },
      user: { sub: 'userId', company: { id: 'otherCompanyId' } },
    };
    const res = { send: vi.fn() };
    const next = vi.fn();

    promptService.getPromptById = () =>
      Promise.resolve({ company: 'companyId' });

    await deletePrompt(req, res, next);

    expect(next).toHaveBeenCalledWith(
      new restify.UnauthorizedError(errors.user.accessDenied)
    );
  });

  it('should return 404 if prompt set is not found', async () => {
    const req = {
      params: { id: 'promptId' },
      user: { sub: 'userId', company: { id: 'companyId' } },
    };
    const res = { send: vi.fn() };
    const next = vi.fn();

    promptService.getPromptById = () =>
      Promise.resolve({ company: 'companyId', promptSet: 'promptSetId' });
    promptsetRepo.getPromptSetById = () => Promise.resolve(null);

    await deletePrompt(req, res, next);

    expect(next).toHaveBeenCalledWith(
      new restify.NotFoundError(errors.promptSet.notFound)
    );
  });

  it('should return 400 if prompt has state', async () => {
    const req = {
      params: { id: 'promptId' },
      user: { sub: 'userId', company: { id: 'companyId' } },
    };
    const res = { send: vi.fn() };
    const next = vi.fn();

    promptService.getPromptById = () =>
      Promise.resolve({
        promptState: { someState: true },
        company: 'companyId',
        promptSet: 'promptSetId',
      });
    promptsetRepo.getPromptSetById = () =>
      Promise.resolve({ version: '1.0.0' });

    await deletePrompt(req, res, next);

    expect(next).toHaveBeenCalledWith(
      new restify.BadRequestError(errors.prompt.deleteDefaultPromptErr)
    );
  });

  it('should successfully delete a prompt and update prompt set version', async () => {
    const req = {
      params: { id: 'promptId' },
      user: {
        sub: 'userId',
        fullName: 'User Name',
        email: '<EMAIL>',
        company: { id: 'companyId' },
      },
    };
    const res = { send: vi.fn() };
    const next = vi.fn();

    server.db.write = {
      getConnection: vi.fn().mockResolvedValue({
        execute: vi
          .fn()
          .mockResolvedValueOnce({}) // For prompt update
          .mockResolvedValueOnce({}) // For unlink prompts
          .mockResolvedValue({ rows: [{ modified: new Date() }] }), // For prompt_set update
        done: vi.fn(),
      }),
    };
    promptService.getPromptById = () =>
      Promise.resolve({
        promptSet: 'promptSetId',
        company: 'companyId',
        deleted: false,
      });
    promptsetRepo.getPromptSetById = () =>
      Promise.resolve({ version: '1.0.0' });

    await deletePrompt(req, res, next);

    expect(res.send).toHaveBeenCalledWith(200, {
      version: '1.1.0',
      modified: expect.any(Date),
      modifiedBy: {
        id: 'userId',
        name: 'User Name',
        email: '<EMAIL>',
      },
    });
    expect(next).toHaveBeenCalled();
  });
});
