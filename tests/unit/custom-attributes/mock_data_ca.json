{"body": {"attributes": [{"attributeDefinitionId": 41, "value": "10"}, {"attributeDefinitionId": 33, "value": "Welcome to Shel"}, {"attributeDefinitionId": 32, "value": 123}], "entityIds": ["438e9c00-3c38-4e02-bf0e-c0bcdd4efd6d", "bef2b35d-835b-4a35-9fe2-b822ef4d5315", "afba8f43-96c7-4ff6-b7ab-5a9dd899b010", "03fb26c3-8a1b-49d6-8b73-d6b58f68c45e", "89a3d54b-8f05-42f8-9bd5-669dc092b773", "06d8f076-1077-4e90-94d7-76558ee85bd4", "7fa816d4-73b9-4c0e-bbfd-4512bd19412c", "2aeebe55-a775-403e-9f83-271ed380fd41", "ecffdd21-658c-4390-b5d0-243be4d2cfa3", "69979fea-78a6-4e40-94d7-b9fb14570e82", "cd60f6ba-c3d0-472c-a32c-f6eadea8b6a9", "6348af6c-f211-4005-a608-295120309c02", "983ca0d6-3a45-4646-a3bc-30c733540342", "e9fca51d-d8e5-49e5-9a81-9fe333aa4b27"], "deploymentType": "immediate", "meta": "undefined"}, "user": {"fullName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "Bearer", "roles": ["COMPANY_ADMIN", "RKI", "TAMPER_CLEAR", "MEDIA_DESIGNER", "MEDIA_APPROVER", "MEDIA_DEPLOYER", "FACTORY_RESET", "CONFIG_MGMT_PUBLISH", "CONFIG_MGMT_ASSIGN", "CONFIG_MGMT_DEPLOY", "SITE_SETTINGS_VIEW", "SITE_SETTINGS_EDIT"], "created": 1702610270, "company": {"id": "971d8f40-9933-11ee-9a50-d7898b6aadcd", "name": "Invenco", "featureFlags": ["MEDIA", "OFFLINE_JOBS", "PROVISION_DEVICES", "TAMPER_CLEAR", "OFFLINE_RKI", "DEVICES_SWAP_OUT", "GSTV", "PLAYLIST_MANAGEMENT", "REMOTE_MANAGEMENT", "PLAYLIST", "FACTORY_RESET", "ADA_AUXILIARY_PLAYLISTS_ENABLED", "SITE_DETAIL_V2", "SITE_ATTRIBUTES", "FUEL_PRICE_MGMT", "SCHEDULING_UST", "FORECOURT_GATEWAY", "SCHEDULING", "PREFER_HTTPS", "ENHANCED_POP_CONFIG", "SCHEDULING_PCI", "SCHEDULING_UA", "SCHEDULING_AD", "SHELL_SUPPORT", "SCHEDULING_UCA", "REPORTING", "CONFIG_MGMT", "PAYMENT_MGMT"]}, "aud": "invenco.cloud", "iss": "unit-test", "sub": "0d577950-5ebd-4153-8bc4-88be169b1f7a", "jti": "2f23c0fd-57b9-4376-9bfa-5ef67fe974fa"}, "onFirstCall": [{"entityId": "d60b5656-b359-43d4-8ec5-1871ebe1cf33"}, {"entityId": "68eb96d5-36c4-4a37-876d-33ffef111148"}, {"entityId": "a5e432d4-b47d-443f-b1b6-058cd95c5818"}, {"entityId": "d8b99abd-667d-4e81-b263-0c3de8799273"}, {"entityId": "0518e404-f33f-4f87-86f6-fbd97478e553"}, {"entityId": "e27ceff8-8158-4b9b-a5ff-43fb6dca5bf4"}, {"entityId": "13af449e-fb87-46ca-a891-84023ca8e2e0"}], "onSecondCall": [{"attributeDefinitionId": 32, "attributeName": "Validate SIP Access Code", "schema": {"type": "number"}}, {"attributeDefinitionId": 33, "attributeName": "Enterprise Header Message Narrow", "schema": {"type": "string", "maxLength": 1000}}, {"attributeDefinitionId": 41, "attributeName": "CTLS Provisional Amount", "schema": {"type": "string"}}]}