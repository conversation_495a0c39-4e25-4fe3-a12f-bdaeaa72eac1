import {
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
  vi,
  beforeAll,
} from 'vitest';
import Joi from 'joi';
import restifyErrors from 'restify-errors';
import { putBulkCustomAttributes } from '../../../src/custom-attributes/custom-attributes.handler';
import { checkMaintenanceFrameAndRoles } from '../../../helpers/scheduled-deployment-helper';
import { server } from '../../../app';
import * as reqData from './mock_data_ca.json';

let req: any;
let res: any;
let next: any;

function createSpy() {
  const calls: any[] = [];
  const spy = (...args: any[]) => {
    calls.push(args);
  };
  // Add properties to the spy function for inspection
  spy.called = () => calls.length > 0;
  spy.callCount = () => calls.length;
  spy.calls = () => calls;
  return spy;
}

describe('Test bulk-custom-attribute', () => {
  const Reqschema = Joi.object({
    entityIds: Joi.array().items(Joi.string()).unique().min(1).required(),
    attributes: Joi.array()
      .items(
        Joi.object().keys({
          attributeDefinitionId: Joi.number().integer().required(),
          value: Joi.alternatives()
            .try(Joi.any().empty(null).allow(null, '').default(null))
            .required(),
        })
      )
      .min(1)
      .required(),
    deploymentType: Joi.string()
      .valid('maintenance-window', 'immediate', 'schedule')
      .optional(),
    scheduledDateTime: Joi.date().iso().optional(),
    meta: Joi.string().default(undefined).optional(),
  });

  beforeAll(() => {
    server.db.read = { rows: vi.fn() };
    server.db.replica = { rows: vi.fn() };
    server.db.write = {
      getConnection: () => ({
        execute: vi.fn(),
        done: vi.fn(),
      }),
    };
  });

  beforeEach(() => {
    res = { send: createSpy() };
    next = createSpy();
    req = {
      params: { entity: 'sites' },
      user: reqData.user,
      log: { error: createSpy() },
    };
  });
  afterEach(() => {
    vi.restoreAllMocks();
    vi.clearAllMocks();
  });
  it.concurrent(
    'Joi validation with deployement type immediate/maintenance-window/schedule',
    () => {
      const { error } = Reqschema.validate(reqData.body);
      if (error) {
        expect(error).not.toBeNull();
      }
      expect(error).toBeNull();
    }
  );

  it('Joi validation with invalid deployement type', () => {
    const body = JSON.parse(JSON.stringify(reqData.body));
    body.deploymentType = 'invalid';
    const { error } = Reqschema.validate(body);
    if (error) {
      expect(error).not.toBeNull();
      expect(error.details[0].message).toBe(
        '"deploymentType" must be one of [maintenance-window, immediate, schedule]'
      );
    }
  });

  it('bulk CA with immediate deployment - should return equal to 200', async () => {
    req.body = reqData.body;
    // Assertions
    await putBulkCustomAttributes(req, res, next);
    expect(res.send.called()).toBeTruthy();
    // eslint-disable-next-line vitest/valid-expect
    expect(res.send.calls()[0]).to.deep.equal([200]);
  });

  it('bulk CA with immediate deployment - error validateBulkAttributeUpdateLimit', async () => {
    const body = JSON.parse(JSON.stringify(reqData.body));
    req.body = body;
    req.body.entityIds = Array.from(Array(50000).keys());
    // Assertions
    await putBulkCustomAttributes(req, res, next);
    expect(next.calls()[0]).toEqual([
      new restifyErrors.BadRequestError(
        'Total number of updates should not exceed 50000'
      ),
    ]);
  });

  it('bulk CA with maintenance-window deployment - should return equal to 200', async () => {
    reqData.body.deploymentType = 'maintenance-window';
    req.body = reqData.body;
    server.db.read.rows.mockResolvedValue();
    server.db.read.rows.mockResolvedValue();
    server.db.write.getConnection().execute.mockResolvedValue();
    // Assertions
    await putBulkCustomAttributes(req, res, next);
    expect(res.send.called()).toBeTruthy();
    // eslint-disable-next-line vitest/valid-expect
    expect(res.send.calls()[0]).to.deep.equal([200]);
  });

  it('bulk CA with schedule deployment - should return equal to 200', async () => {
    const body = JSON.parse(JSON.stringify(reqData.body));
    body.deploymentType = 'schedule';
    const currentDate = new Date();
    const newDate = new Date(currentDate.getTime() + 5 * 60000);
    body.scheduledDateTime = newDate.toISOString();
    req.body = body;
    // Assertions
    await checkMaintenanceFrameAndRoles('UpdateSiteCustomAttribute')(
      req,
      res,
      next
    );
    expect(res.send.called()).toBeFalsy();
  });

  it('bulk CA with invalid scheduledDateTime of schedule deployment - should return error message', async () => {
    const body = JSON.parse(JSON.stringify(reqData.body));
    body.deploymentType = 'schedule';
    const currentDate = new Date();
    const newDate = new Date(currentDate.getTime() - 60 * 24 * 60000);
    body.scheduledDateTime = newDate.toISOString();
    req.body = body;
    // Assertions
    await checkMaintenanceFrameAndRoles('UpdateSiteCustomAttribute')(
      req,
      res,
      next
    );
    expect(next.calls()[0]).toEqual([
      new restifyErrors.BadRequestError(
        'scheduledDateTime must lie within 5 days'
      ),
    ]);
  });
});
