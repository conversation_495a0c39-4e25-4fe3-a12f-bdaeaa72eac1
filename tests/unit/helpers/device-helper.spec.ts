// getCertificateIssuerCodes.test.js
import { describe, it, expect, vi, afterEach } from 'vitest';
import { getCertificateIssuerCodes } from '../../../helpers/device-helper';
import { server } from '../../../app';
import CONSTANTS from '../../../src/rki/lib/constants';

const mockDbReadRow = vi.fn();
server.db.read.row = mockDbReadRow; // Access the mocked function

describe('getCertificateIssuerCodes', () => {
  const deviceId = 'test-device-id';

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should return issuer codes from JSON certificates', async () => {
    const mockDevice = {
      certificate: JSON.stringify([
        { issuer_code: CONSTANTS.ValidCertificateIssuerCode.LEGACY },
        { issuer_code: CONSTANTS.ValidCertificateIssuerCode.TAVE },
      ]),
      isJsonCertificates: true,
    };
    server.db.read.row.mockResolvedValue(mockDevice);

    const result = await getCertificateIssuerCodes(deviceId);
    expect(result).toEqual([
      CONSTANTS.ValidCertificateIssuerCode.LEGACY,
      CONSTANTS.ValidCertificateIssuerCode.TAVE,
    ]);
  });

  it('should return legacy issuer code if certificates are not JSON', async () => {
    const mockDevice = {
      certificate: 'some-legacy-cert',
      isJsonCertificates: false,
    };
    server.db.read.row.mockResolvedValue(mockDevice);

    const result = await getCertificateIssuerCodes(deviceId);
    expect(result).toEqual([CONSTANTS.ValidCertificateIssuerCode.LEGACY]);
  });

  it('should return empty array if device is not found', async () => {
    server.db.read.row.mockResolvedValue(null);

    const result = await getCertificateIssuerCodes(deviceId);
    expect(result).toEqual([]);
  });

  it('should return empty array if certificate is missing', async () => {
    const mockDevice = {
      certificate: null,
      isJsonCertificates: true,
    };
    server.db.read.row.mockResolvedValue(mockDevice);

    const result = await getCertificateIssuerCodes(deviceId);
    expect(result).toEqual([]);
  });

  it('should return empty array if JSON parsing fails', async () => {
    const mockDevice = {
      certificate: null,
      isJsonCertificates: true,
    };
    server.db.read.row.mockResolvedValue(mockDevice);

    const result = await getCertificateIssuerCodes(deviceId);
    expect(result).toEqual([]);
  });
});
