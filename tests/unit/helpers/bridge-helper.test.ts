import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { BridgeHelper } from '../../../helpers/bridge-helper';

describe('BridgeHelper', () => {
  let dbMock: { read: any; write: any };
  let bridge: BridgeHelper;

  beforeEach(() => {
    dbMock = {
      read: { row: vi.fn(), rows: vi.fn() },
      write: { execute: vi.fn() },
    };

    // eslint-disable-next-line new-cap
    bridge = new BridgeHelper(dbMock);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('getICSBridgeDeviceType', () => {
    it('should return the correct ICS type for a valid productTypeId', () => {
      expect(bridge.getICSBridgeDeviceType(6)).toBe('G6-200');
      expect(bridge.getICSBridgeDeviceType(12)).toBe('C1-100');
      expect(bridge.getICSBridgeDeviceType(50)).toBe('WIN-SERVER');
    });

    it('should return null for an invalid productTypeId', () => {
      expect(bridge.getICSBridgeDeviceType(99)).toBeNull();
    });
  });

  describe('convertLegacyProductType', () => {
    it('should return the correct legacy type for a valid ICS type', () => {
      expect(bridge.convertLegacyProductType('G6-200')).toBe('G6OPT');
      expect(bridge.convertLegacyProductType('FUELPOS')).toBe('FUELPOS');
    });

    it('should return null for an invalid ICS type', () => {
      expect(bridge.convertLegacyProductType('INVALID_TYPE')).toBeNull();
    });
  });

  describe('validateDeviceExistence', () => {
    it('should call db.read.row with correct parameters', async () => {
      const serialNumber = '12345';
      const bridgeDeviceType = 'G6-200';

      await bridge.validateDeviceExistence(serialNumber, bridgeDeviceType);

      expect(dbMock.read.row).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM target'),
        [serialNumber, bridgeDeviceType]
      );
    });
  });

  describe('validateSiteExistence', () => {
    it('should call db.read.row with correct parameters', async () => {
      const siteId = 'site-id';

      await bridge.validateSiteExistence(siteId);

      expect(dbMock.read.row).toHaveBeenCalledWith(
        expect.stringContaining('SELECT site_id, name, company_id'),
        [siteId]
      );
    });
  });

  describe('saveNewIssuerCodes', () => {
    it('should call db.write.execute with the correct query and new issuer codes', async () => {
      const newIssuerCodes = ['NEW_CODE_1', 'NEW_CODE_2'];

      await bridge.saveNewIssuerCodes(newIssuerCodes);

      expect(dbMock.write.execute).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO certificate_issuer'),
        [newIssuerCodes]
      );
    });
  });

  describe('processIssuerCode', () => {
    it('should save new issuer codes if there are any', async () => {
      const certificateString = JSON.stringify([
        { issuer_code: 'NEW_CODE_1' },
        { issuer_code: 'NEW_CODE_2' },
      ]);
      dbMock.read.rows.mockResolvedValue([{ issuerCode: 'EXISTING_CODE' }]); // Mock existing codes

      await bridge.processIssuerCode(certificateString);

      expect(dbMock.write.execute).toHaveBeenCalled(); // Ensure saveNewIssuerCodes is called
    });

    it('should not save new issuer codes if all are existing', async () => {
      const certificateString = JSON.stringify([
        { issuer_code: 'EXISTING_CODE' },
      ]);
      dbMock.read.rows.mockResolvedValue([{ issuerCode: 'EXISTING_CODE' }]); // Mock existing codes

      await bridge.processIssuerCode(certificateString);

      expect(dbMock.write.execute).not.toHaveBeenCalled(); // Ensure saveNewIssuerCodes is not called
    });
  });

  describe('getCompanyProductType', () => {
    it('should return the device type for a valid companyId and deviceType', async () => {
      const companyId = 'company-id';
      const deviceType = 'G6-200';
      dbMock.read.row.mockResolvedValueOnce({ deviceType: 'G6-200' });

      const result = await bridge.getCompanyProductType(deviceType, companyId);

      expect(result).toBe('G6-200');
    });

    it('should return null for an invalid companyId or deviceType', async () => {
      const companyId = 'company-id-99';
      const deviceType = 'INVALID_TYPE';
      dbMock.read.row.mockResolvedValueOnce(null);

      const result = await bridge.getCompanyProductType(deviceType, companyId);

      expect(result).toBeNull();
    });
  });

  describe('getDeviceBySN', () => {
    it('should call db.read.row with the correct serial number', async () => {
      const sn = '12345';

      await bridge.getDeviceBySN(sn);

      expect(dbMock.read.row).toHaveBeenCalledWith(
        expect.stringContaining('SELECT t.target_id, t.serial_number'),
        [sn]
      );
    });
  });

  describe('pruneResponseData', () => {
    it('should return an object with only the specified keys', () => {
      const device = {
        id: 1,
        serialNumber: '12345',
        lastContact: '2021-01-01',
        lastRegistered: '2021-01-01',
        name: 'Device 1',
        description: 'A test device',
        siteId: 1,
        status: 'active',
        siteName: 'Test Site',
        keyGroupRef: 'key-group',
        productType: 'G6-200',
        extraField: 'should not be included',
      };

      const prunedData = bridge.pruneResponseData(device);

      expect(prunedData).toEqual({
        id: 1,
        serialNumber: '12345',
        lastContact: '2021-01-01',
        lastRegistered: '2021-01-01',
        name: 'Device 1',
        description: 'A test device',
        siteId: 1,
        status: 'active',
        siteName: 'Test Site',
        keyGroupRef: 'key-group',
        productType: 'G6-200',
      });
      // @ts-ignore
      expect(prunedData.extraField).toBeUndefined();
    });
  });
});
