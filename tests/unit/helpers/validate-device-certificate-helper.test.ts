// vitest.test.js
import { describe, it, expect, vi, afterEach } from 'vitest';
import restifyErrors from 'restify-errors';
import { validateDeviceJsonCertificate } from '../../../helpers/validate-device-certificate-helper';
import CONSTANTS from '../../../src/rki/lib/constants';

describe('validateDeviceJsonCertificate', () => {
  const next = vi.fn();
  const log = { error: vi.fn() };
  const res = {
    send: vi.fn(),
  };

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should call next() when JSON certificate is valid', async () => {
    const req = {
      body: {
        certificate: JSON.stringify([
          {
            issuer_code: CONSTANTS.ValidCertificateIssuerCode.LEGACY,
            certificate: 'cert1',
          },
        ]),
        isJsonCertificates: true,
      },
      log,
    };

    await validateDeviceJsonCertificate('AddDevice')(req, res, next);
    expect(next).toHaveBeenCalled();
    expect(log.error).not.toHaveBeenCalled();
  });

  it('should throw an error when JSON certificate has duplicated issuer code certificates', async () => {
    const req = {
      body: {
        certificate: JSON.stringify([
          {
            issuer_code: CONSTANTS.ValidCertificateIssuerCode.LEGACY,
            certificate: 'cert1',
          },
          {
            issuer_code: CONSTANTS.ValidCertificateIssuerCode.LEGACY,
            certificate: 'cert1',
          },
        ]),
        isJsonCertificates: true,
      },
      log,
    };
    await validateDeviceJsonCertificate('AddDevice')(req, res, next);
    expect(next).toHaveBeenCalledWith(
      expect.any(restifyErrors.BadRequestError)
    );
    expect(log.error).toHaveBeenCalledWith(
      expect.any(Object),
      '[validateDeviceJsonCertificate]'
    );
  });

  it('should throw an error when body.certificate is missing', async () => {
    const req = {
      body: {
        certificate: null,
        isJsonCertificates: true,
      },
      log,
    };

    await validateDeviceJsonCertificate('AddDevice')(req, res, next);
    expect(next).toHaveBeenCalledWith(
      expect.any(restifyErrors.BadRequestError)
    );
    expect(log.error).toHaveBeenCalledWith(
      expect.any(Object),
      '[validateDeviceJsonCertificate]'
    );
  });

  it('should throw an error when certificate is invalid JSON string', async () => {
    const req = {
      body: {
        certificate: 'invalid_json',
        isJsonCertificates: true,
      },
      log,
    };

    await validateDeviceJsonCertificate('AddDevice')(req, res, next);
    expect(next).toHaveBeenCalledWith(
      expect.any(restifyErrors.BadRequestError)
    );
  });

  it('should throw an error when parsed result is not an array', async () => {
    const req = {
      body: {
        certificate: JSON.stringify(res),
        isJsonCertificates: true,
      },
      log,
    };

    await validateDeviceJsonCertificate('AddDevice')(req, res, next);
    expect(next).toHaveBeenCalledWith(
      expect.any(restifyErrors.BadRequestError)
    );
  });

  it('should throw an error when issuer_code is missing', async () => {
    const req = {
      body: {
        certificate: JSON.stringify([{ certificate: 'cert1' }]),
        isJsonCertificates: true,
      },
      log,
    };

    await validateDeviceJsonCertificate('AddDevice')(req, res, next);
    expect(next).toHaveBeenCalledWith(
      expect.any(restifyErrors.BadRequestError)
    );
  });

  it('should throw an error when certificate is missing', async () => {
    const req = {
      body: {
        certificate: JSON.stringify([{ issuer_code: 'code1' }]),
        isJsonCertificates: true,
      },
      log,
    };

    await validateDeviceJsonCertificate('AddDevice')(req, res, next);
    expect(next).toHaveBeenCalledWith(
      expect.any(restifyErrors.BadRequestError)
    );
  });

  it('should throw an error for unhandled issuer_code', async () => {
    const req = {
      body: {
        certificate: JSON.stringify([
          { issuer_code: 'invalid_code', certificate: 'cert1' },
        ]),
        isJsonCertificates: true,
      },
      log,
    };

    await validateDeviceJsonCertificate('AddDevice')(req, res, next);
    expect(next).toHaveBeenCalledWith(
      expect.any(restifyErrors.BadRequestError)
    );
  });

  it('should call next() when new issuer code is allowed for bridgeAddDevice', async () => {
    const req = {
      body: {
        certificate: JSON.stringify([
          { issuer_code: 'new-code', certificate: 'cert1' },
        ]),
        isJsonCertificates: true,
      },
      log,
    };

    await validateDeviceJsonCertificate('bridgeAddDevice')(req, res, next);
    expect(next).toHaveBeenCalled();
    expect(log.error).not.toHaveBeenCalled();
  });

  it('should call next() when new issuer code is allowed for bridgeUpdateDevice', async () => {
    const req = {
      body: {
        certificate: JSON.stringify([
          { issuer_code: 'new-code', certificate: 'cert1' },
        ]),
        isJsonCertificates: true,
      },
      log,
    };

    await validateDeviceJsonCertificate('bridgeUpdateDevice')(req, res, next);
    expect(next).toHaveBeenCalled();
    expect(log.error).not.toHaveBeenCalled();
  });

  it('should throw an error when issuer_code length exceeds 25 characters', async () => {
    const req = {
      body: {
        certificate: JSON.stringify([
          { issuer_code: 'a'.repeat(26), certificate: 'cert1' },
        ]),
        isJsonCertificates: true,
      },
      log,
    };

    await validateDeviceJsonCertificate('AddDevice')(req, res, next);
    expect(next).toHaveBeenCalledWith(
      expect.any(restifyErrors.BadRequestError)
    );
  });

  it('should throw an error when certificate is null', async () => {
    const req = {
      body: {
        certificate: JSON.stringify([
          { issuer_code: 'valid_code_1', certificate: null },
        ]),
        isJsonCertificates: true,
      },
      log,
    };

    await validateDeviceJsonCertificate('AddDevice')(req, res, next);
    expect(next).toHaveBeenCalledWith(
      expect.any(restifyErrors.BadRequestError)
    );
  });
});
