import { describe, it, expect } from 'vitest';
import { mapToEventData } from '../../handlers/sites/sites-replication-events';

describe('Test mapEventData', () => {
  it('- should return equal', async () => {
    const site1 = {
      tenantId: 'companyId',
      siteId: 'siteId',
      tenantName: 'tenantName',
      siteName: 'siteName',
      deleteTimestamp: 431243,
      isActive: true,
      createdDate: 'createdDate',
      updatedDate: 'updatedDate',
    };
    const site2 = {
      tenantId: 'companyId',
      siteId: 'siteId',
      tenantName: 'tenantName',
      siteName: 'siteName',
      deleteTimestamp: null,
      isActive: true,
      createdDate: 'createdDate',
      updatedDate: 'updatedDate',
    };
    const expectedResult1 = {
      tenantId: 'companyId',
      siteId: 'siteId',
      tenantName: 'tenantName',
      siteName: 'siteName',
      isDeleted: true,
      isActive: true,
      createdDate: 'createdDate',
      updatedDate: 'updatedDate',
    };
    const expectedResult2 = {
      tenantId: 'companyId',
      siteId: 'siteId',
      tenantName: 'tenantName',
      siteName: 'siteName',
      isDeleted: false,
      isActive: true,
      createdDate: 'createdDate',
      updatedDate: 'updatedDate',
    };

    const result1 = mapToEventData(site1);
    expect(JSON.stringify(result1)).toBe(JSON.stringify(expectedResult1));
    const result2 = mapToEventData(site2);
    expect(JSON.stringify(result2)).toBe(JSON.stringify(expectedResult2));
  });
});
