import { afterEach, describe, it, expect, vi } from 'vitest';

import { validateCaptcha } from '../../../src/authentication/validators';
import { httpCode } from '../../../lib/app-constants';
import * as recaptcha from '../../../lib/recaptcha';

describe('Test Authentication Validators', () => {
  const sendFunctionMock = vi.fn();
  const res = { send: sendFunctionMock };
  const nextMock = vi.fn();

  describe('Test ValidateCaptcha', () => {
    afterEach(() => {
      vi.restoreAllMocks();
    });

    it('- should fail if there is no captcha response', async () => {
      const req = { body: {} };

      await validateCaptcha(true)(req, res, nextMock);

      expect(sendFunctionMock).toHaveBeenCalledOnce();
      expect(sendFunctionMock).toHaveBeenLastCalledWith(
        httpCode.UNAUTHORIZED.STATUS_CODE,
        {
          message: 'Authentication failed',
          isCaptchaEnabled: true,
        }
      );
      expect(nextMock).toHaveBeenCalledTimes(0);
    });

    it('- should pass when captcha response is valid', async () => {
      const req = { body: { captchaResponse: 'captcha123' } };

      const recaptchaValidateSpy = vi
        .spyOn(recaptcha, 'validateRecaptcha')
        .mockResolvedValue({
          success: true,
          message: 'response valid',
        });

      await validateCaptcha(true)(req, res, nextMock);

      expect(sendFunctionMock).toHaveBeenCalledTimes(0);
      expect(recaptchaValidateSpy).toHaveBeenCalledOnce();
      expect(nextMock).toHaveBeenCalledOnce();
    });
  });
});
