import { describe, expect, it } from 'vitest';
import { listToMatrix } from '../../../../src/notifications/utils/listToMatrix';

describe('notification - listToMatrix Test', () => {
  it('- should create a matrix with 2 length', () => {
    const arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    const arrMatrix = listToMatrix(arr, 5);
    expect(arrMatrix).toHaveLength(2);
    expect(arrMatrix[0]).toHaveLength(5);
    expect(arrMatrix[1]).toHaveLength(5);
  });
  it('- should create a matrix with 10 length', () => {
    const arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    const arrMatrix = listToMatrix(arr, 1);
    expect(arrMatrix).toHaveLength(10);
    expect(arrMatrix[0]).toHaveLength(1);
    expect(arrMatrix[1]).toHaveLength(1);
    expect(arrMatrix[8]).toHaveLength(1);
    expect(arrMatrix[9]).toHaveLength(1);
  });
  it('- should create a matrix with 0 length', () => {
    const arr: any[] = [];
    const arrMatrix = listToMatrix(arr, 5);
    expect(arrMatrix).toHaveLength(0);
  });
});
