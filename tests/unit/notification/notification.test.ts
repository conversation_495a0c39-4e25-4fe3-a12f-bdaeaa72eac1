import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import restify from 'restify-errors';
import { cloneDeep } from 'lodash';
import <PERSON><PERSON> from 'joi';
import { mockServerDb } from '../mock/mockServerDb';
import { notificationBodyJoiSchema } from '../../../src/notifications/notificationBodyJoiSchema';
import { createEmailUINotification } from '../../../src/notifications/notifications.handler';
import { creatAPIResReqNextSpy } from '../mock/createAPIResReqNextSpy';
import { mockUserData } from '../mock/mockUserData';
import * as GetRecipientsById from '../../../src/notifications/utils/getRecipientById';
import * as GetRecipientByRoles from '../../../src/notifications/utils/getRecipientByRoles';
import * as GetRoles from '../../../src/notifications/utils/getRoles';
import * as ValidateRecipients from '../../../src/notifications/utils/validateRecipients';
import * as GetTenantById from '../../../src/notifications/utils/getTenantById';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
let server: any;

const { mockDoEmailNotification } = vi.hoisted(() => ({
  mockDoEmailNotification: vi.fn().mockImplementation(() => Promise.resolve()),
}));
vi.mock('../../../src/notifications/utils/doEmailNotification', () => ({
  doEmailNotification: mockDoEmailNotification,
}));
const { mockInsertNotification } = vi.hoisted(() => ({
  mockInsertNotification: vi.fn().mockImplementation(() => Promise.resolve()),
}));
vi.mock('../../../src/notifications/utils/insertNotification', () => ({
  insertNotification: mockInsertNotification,
}));

const mockTenantData = {
  id: '4d7301f8-c076-11ee-903b-5f85ff1e01e4',
  senderEmail: '<EMAIL>',
};
const mockValidatedRecipientForRole = [
  {
    id: '21f977fa-6746-11eb-b759-7b0986cc0b4f',
    companyId: '4d7301f8-c076-11ee-903b-5f85ff1e01e4',
    email: '<EMAIL>',
  },
  {
    id: '231c02ce-6746-11eb-b75c-431b7943f247',
    companyId: '4d7301f8-c076-11ee-903b-5f85ff1e01e4',
    email: '<EMAIL>',
  },
];

describe('createEmailUINotification - Email/UI notification Test', () => {
  const mockValidNotificationBody = {
    type: 'test type',
    tenantId: '4d7301f8-c076-11ee-903b-5f85ff1e01e4',
    recipientRoles: ['FUEL_PRICE_MGMT_THRESHOLD'],
    recipientIds: ['4c0a04aa-3c5d-4f32-b0a8-2aeaaf5dad41'],
    timestamp: new Date().toISOString(),
    level: 'test level',
    relatedEntity: {
      type: 'fps',
      path: '/fuel-price-service/approval',
    },
    message: 'test message',
  };
  const mockValidEmailNotificationBody = {
    type: 'test type',
    tenantId: '4d7301f8-c076-11ee-903b-5f85ff1e01e4',
    recipientRoles: ['FUEL_PRICE_MGMT_THRESHOLD'],
    recipientIds: ['4c0a04aa-3c5d-4f32-b0a8-2aeaaf5dad41'],
    timestamp: new Date().toISOString(),
    level: 'test level',
    relatedEntity: {
      type: 'fps',
      path: '/fuel-price-service/approval',
    },
    message: 'test message',
    isSendEmailNotification: true,
    emailNotification: {
      subject: 'TEST Email Notification',
      message: 'TEST Email Subject',
      htmlStyle: '',
    },
  };

  beforeEach(() => {
    vi.spyOn(GetTenantById, 'getTenantById').mockResolvedValue([
      mockTenantData,
    ]);
  });
  afterEach(() => {
    vi.restoreAllMocks();
    vi.clearAllMocks();
  });
  it('- should validate body successfully', () => {
    const joiSchema = Joi.object(notificationBodyJoiSchema);
    const { error } = joiSchema.validate(mockValidNotificationBody);
    expect(error).toBeNull();
  });
  it('- should validate body Fail when missing tenant id', () => {
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.tenantId = '';

    const joiSchema = Joi.object(notificationBodyJoiSchema);
    const { error } = joiSchema.validate(mockBody);
    expect(error).not.toBeNull();
  });
  it('- should validate body Fail when missing type', () => {
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.type = '';

    const joiSchema = Joi.object(notificationBodyJoiSchema);
    const { error } = joiSchema.validate(mockBody);
    expect(error).not.toBeNull();
  });
  it('- should validate body Fail when missing timestamp', () => {
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.timestamp = '';

    const joiSchema = Joi.object(notificationBodyJoiSchema);
    const { error } = joiSchema.validate(mockBody);
    expect(error).not.toBeNull();
  });
  it('- should validate body Fail when missing level', () => {
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.level = '';

    const joiSchema = Joi.object(notificationBodyJoiSchema);
    const { error } = joiSchema.validate(mockBody);
    expect(error).not.toBeNull();
  });
  it('- should validate body Fail when missing message', () => {
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.message = '';

    const joiSchema = Joi.object(notificationBodyJoiSchema);
    const { error } = joiSchema.validate(mockBody);
    expect(error).not.toBeNull();
  });
  it('- should validate body Fail when tenant id NOT uuid', () => {
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.tenantId = 'WRONG';

    const joiSchema = Joi.object(notificationBodyJoiSchema);
    const { error } = joiSchema.validate(mockBody);
    expect(error).not.toBeNull();
  });
  it('- should validate body Pass when relatedEntity/recipientRoles/recipientIds is NOT present', () => {
    const mockBody = cloneDeep(mockValidNotificationBody);
    // @ts-ignore
    mockBody.relatedEntity = {};
    // @ts-ignore
    mockBody.recipientIds = undefined;
    // @ts-ignore
    mockBody.recipientRoles = undefined;

    const joiSchema = Joi.object(notificationBodyJoiSchema);
    const { error } = joiSchema.validate(mockBody);
    expect(error).toBeNull();
  });
  it('- should validate body Fail when recipient id is NOT uuid', () => {
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.recipientIds = ['WRONG'];

    const joiSchema = Joi.object(notificationBodyJoiSchema);
    const { error } = joiSchema.validate(mockBody);
    expect(error).not.toBeNull();
  });

  it('- should Fail when recipientIds and recipientRoles NOT defined', async () => {
    server = mockServerDb({});
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.recipientIds = [];
    mockBody.recipientRoles = [];
    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockBody,
      params: {},
      userData: mockUserData,
    });
    await expect(createEmailUINotification(req, res, next)).resolves.toEqual(
      new restify.BadRequestError(
        'Both RecipientIds and RecipientRoles Not Defined'
      )
    );
  });

  it('- should Fail when recipientIds duplicated', async () => {
    server = mockServerDb({});
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.recipientIds = [
      '0d577950-5ebd-4153-8bc4-88be169b1f7a',
      '0d577950-5ebd-4153-8bc4-88be169b1f7a',
    ];
    mockBody.recipientRoles = [];
    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockBody,
      params: {},
      userData: mockUserData,
    });
    await expect(createEmailUINotification(req, res, next)).resolves.toEqual(
      new restify.BadRequestError('Found duplicates in recipients list')
    );
  });
  it('- should Fail when tenant NOT found for tenantId', async () => {
    vi.spyOn(GetTenantById, 'getTenantById').mockResolvedValueOnce(undefined);

    server = mockServerDb({});
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.recipientIds = ['0d577950-5ebd-4153-8bc4-88be169b1f7a'];
    mockBody.recipientRoles = [];
    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockBody,
      params: {},
      userData: mockUserData,
    });
    await expect(createEmailUINotification(req, res, next)).resolves.toEqual(
      new restify.NotFoundError('Tenant Not Found By tenantId')
    );
  });

  it('- should Fail when user not found for recipientId', async () => {
    vi.spyOn(GetRecipientsById, 'getRecipientsById').mockResolvedValueOnce([]);

    server = mockServerDb({});
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.recipientIds = ['0d577950-5ebd-4153-8bc4-88be169b1f7a'];
    mockBody.recipientRoles = [];
    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockBody,
      params: {},
      userData: mockUserData,
    });
    await expect(createEmailUINotification(req, res, next)).resolves.toEqual(
      new restify.NotFoundError('Recipient not found')
    );
  });
  it('- should Fail when recipientRoles duplicated', async () => {
    server = mockServerDb({});
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.recipientIds = [];
    mockBody.recipientRoles = ['COMPANY_ADMIN', 'COMPANY_ADMIN'];
    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockBody,
      params: {},
      userData: mockUserData,
    });
    await expect(createEmailUINotification(req, res, next)).resolves.toEqual(
      new restify.NotFoundError('Found duplicates in recipient roles list')
    );
  });
  it('- should Fail when recipientRoles NOT found', async () => {
    vi.spyOn(GetTenantById, 'getTenantById').mockResolvedValueOnce(
      mockTenantData
    );
    vi.spyOn(GetRoles, 'getRoles').mockResolvedValueOnce([]);

    server = mockServerDb({});
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.recipientIds = [];
    mockBody.recipientRoles = ['COMPANY_ADMIN'];
    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockBody,
      params: {},
      userData: mockUserData,
    });
    await expect(createEmailUINotification(req, res, next)).resolves.toEqual(
      new restify.NotFoundError('Recipient Role not found')
    );
  });
  it('- should Fail when user NOT found recipientRoles', async () => {
    vi.spyOn(GetRoles, 'getRoles').mockResolvedValueOnce(['COMPANY_ADMIN']);
    vi.spyOn(GetRecipientByRoles, 'getRecipientByRoles').mockResolvedValueOnce(
      []
    );

    server = mockServerDb({});
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.recipientIds = [];
    mockBody.recipientRoles = ['COMPANY_ADMIN'];
    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockBody,
      params: {},
      userData: mockUserData,
    });
    await expect(createEmailUINotification(req, res, next)).resolves.toEqual(
      new restify.NotFoundError('User not found with role(s)')
    );
  });

  it('- should Fail when user NOT from same company', async () => {
    const invalidMockValidatedRecipientForRole = cloneDeep(
      mockValidatedRecipientForRole
    );
    invalidMockValidatedRecipientForRole[0].companyId =
      'a834eb58-4095-11ee-95ad-330c87d938d5';
    vi.spyOn(ValidateRecipients, 'validateRecipients').mockResolvedValueOnce(
      invalidMockValidatedRecipientForRole
    );

    server = mockServerDb({});
    const mockBody = cloneDeep(mockValidNotificationBody);
    mockBody.recipientIds = [];
    mockBody.recipientRoles = ['COMPANY_ADMIN'];
    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockBody,
      params: {},
      userData: mockUserData,
    });
    await expect(createEmailUINotification(req, res, next)).resolves.toEqual(
      new restify.ConflictError('Recipient must all be of the same company')
    );
  });
  it('- should Fail when email notification and tenant.senderEmail is empty', async () => {
    const invalidMockTenantData = cloneDeep(mockTenantData);
    invalidMockTenantData.senderEmail = '';
    vi.spyOn(GetTenantById, 'getTenantById').mockResolvedValueOnce(
      invalidMockTenantData
    );
    server = mockServerDb({});
    const mockBody = cloneDeep(mockValidEmailNotificationBody);

    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockBody,
      params: {},
      userData: mockUserData,
    });
    await expect(createEmailUINotification(req, res, next)).resolves.toEqual(
      new restify.ConflictError(
        `Sender Email Not Defined for tenant ${mockBody.tenantId}`
      )
    );
  });
  it('- should Fail when email notification and emailNotification is undefined', async () => {
    server = mockServerDb({});
    const mockBody = cloneDeep(mockValidEmailNotificationBody);

    // @ts-ignore
    delete mockBody.emailNotification;
    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockBody,
      params: {},
      userData: mockUserData,
    });
    await expect(createEmailUINotification(req, res, next)).resolves.toEqual(
      new restify.BadRequestError(
        'Email Notification Requires emailNotification object'
      )
    );
  });
  it('- should Fail when email notification and subject is empty', async () => {
    server = mockServerDb({});
    const mockBody = cloneDeep(mockValidEmailNotificationBody);

    mockBody.emailNotification.subject = '';
    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockBody,
      params: {},
      userData: mockUserData,
    });
    await expect(createEmailUINotification(req, res, next)).resolves.toEqual(
      new restify.BadRequestError(
        'Email Notification Requires email notification subject'
      )
    );
  });
  it('- should Pass for ui notification only', async () => {
    server = mockServerDb({});
    const mockBody = cloneDeep(mockValidNotificationBody);
    vi.spyOn(ValidateRecipients, 'validateRecipients').mockResolvedValueOnce(
      mockValidatedRecipientForRole
    );

    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockBody,
      params: {},
      userData: mockUserData,
    });

    await createEmailUINotification(req, res, next);
    expect(mockInsertNotification).toBeCalledTimes(1);
    expect(mockDoEmailNotification).toBeCalledTimes(0);
  });
  it('- should Pass for both email and ui notification', async () => {
    server = mockServerDb({});
    vi.spyOn(ValidateRecipients, 'validateRecipients').mockResolvedValueOnce(
      mockValidatedRecipientForRole
    );
    vi.spyOn(GetTenantById, 'getTenantById').mockResolvedValueOnce(
      mockTenantData
    );
    const { res, req, next } = creatAPIResReqNextSpy({
      body: mockValidEmailNotificationBody,
      params: {},
      userData: mockUserData,
    });
    await createEmailUINotification(req, res, next);
    expect(mockInsertNotification).toBeCalledTimes(1);
    expect(mockDoEmailNotification).toBeCalledTimes(1);
  });
});
