import { describe, it, expect, vi, afterEach } from 'vitest';
import { server } from '../../../../../app';
import logger from '../../../../../lib/logger';
import {
  deleteRequestDeviceEntry,
  getAllAllowedKeyGroupByCompany,
  getAllKeyGroupByCompany,
  getAllKeyRequestSessionSummaries,
  getAllKeyRequestSessionSummariesCount,
  getAllowedCompanyId,
  getApproverKRSSession,
  getKeyRequestSessionDetails,
  isAllowedKRS,
  updateKeyRequestSession,
} from '../../../../../src/rki/api/krs/krs-helper';

const mockWriteExecute = vi.fn().mockResolvedValue([]);
server.db.write.execute = mockWriteExecute;
const mockDbReadRow = vi.fn();
server.db.read.row = mockDbReadRow;
const mockDbReadRows = vi.fn();
server.db.read.rows = mockDbReadRows;
const mockLogError = vi.fn();
server.log.error = mockLogError;

logger.mainLogger = vi.fn().mockImplementation(() => ({
  error: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
}));

describe('isAllowedKRS', () => {
  const mockUserId = 'user-id';
  const mockCompanyId = 'company-id';
  const mockDeviceIds = [1, 2, 3];
  const mockDevices = [{ target_id: 1 }, { target_id: 2 }];

  afterEach(() => {
    vi.resetAllMocks(); // Clear mocks after each test
  });
  it('should return devices if query is successful', async () => {
    mockDbReadRows.mockResolvedValue(mockDevices);

    const result = await isAllowedKRS(mockUserId, mockCompanyId, mockDeviceIds);

    expect(result).toEqual(mockDevices);
    expect(mockDbReadRows).toHaveBeenCalledWith(
      `
        SELECT target_id FROM target t 
        JOIN site s on t.site_id =s.site_id 
        JOIN user_site_authorization u on s.site_id = u.site_id
        JOIN company c on s.company_id =c.id 
        WHERE t.active = true AND u.user_id = $1
        AND c.id = $2
        AND t.target_id = ANY($3::int[])
        `,
      [mockUserId, mockCompanyId, mockDeviceIds]
    );
  });

  it('should log error and throw if query fails', async () => {
    const mockError = new Error('Query failed');
    mockDbReadRows.mockRejectedValue(mockError);

    await expect(
      isAllowedKRS(mockUserId, mockCompanyId, mockDeviceIds)
    ).rejects.toThrow(mockError);
    expect(mockLogError).toHaveBeenCalledWith(
      {
        error: mockError,
        userId: mockUserId,
        companyId: mockCompanyId,
        deviceIds: mockDeviceIds,
      },
      'keyRequestHelper.isAllowedKRS error'
    );
  });
});

describe('getAllowedCompanyId', () => {
  const mockServiceRecipientId = 'test-company-id';
  const mockServiceCompanyId = 'allowed-id';

  afterEach(() => {
    vi.resetAllMocks(); // Clear mocks after each test
  });

  it('should return allowed company IDs when the database call is successful', async () => {
    const mockResponse = { allowed_company_id: mockServiceCompanyId };
    server.db.read.row.mockResolvedValue(mockResponse); // Mock the database response

    const result = await getAllowedCompanyId({
      serviceCompanyId: mockServiceCompanyId,
      serviceRecipientId: mockServiceRecipientId,
    });

    expect(server.db.read.row).toHaveBeenCalledWith(
      expect.stringContaining(
        'SELECT cr.allowed_company_id FROM company_relationship cr WHERE cr.allowed_company_id =$1 AND cr.company_id =$2'
      ),
      [mockServiceCompanyId, mockServiceRecipientId]
    );
    expect(result).toEqual(mockResponse);
  });

  it('should log an error and throw when the database call fails', async () => {
    const mockError = new Error('Database error');
    server.db.read.row.mockRejectedValue(mockError); // Mock the database error

    await expect(
      getAllowedCompanyId({
        serviceCompanyId: mockServiceCompanyId,
        serviceRecipientId: mockServiceRecipientId,
      })
    ).rejects.toThrow(mockError);
    expect(mockLogError).toHaveBeenCalledWith(
      mockError,
      expect.stringContaining(
        `keyRequestHelper.getAllowedCompanyId(serviceCompanyId: ${mockServiceCompanyId}, servoceRecipientId: ${mockServiceRecipientId}, error`
      )
    );
  });
});

describe('getAllKeyGroupByCompany', () => {
  const mockCompanyId = 'test-company-id';

  afterEach(() => {
    vi.resetAllMocks(); // Clear mocks after each test
  });

  it('should return key groups when the database call is successful', async () => {
    const mockResponse = [
      {
        key_group_id: 'kg1',
        key_group_name: 'Key Group 1',
        key_group_ref: 'KG1',
        company_id: mockCompanyId,
        certificate_issuer_id: 'issuer1',
        issuer_code: 'ISSUER_CODE_1',
        name: 'Company Name 1',
      },
      {
        key_group_id: 'kg2',
        key_group_name: 'Key Group 2',
        key_group_ref: 'KG2',
        company_id: mockCompanyId,
        certificate_issuer_id: 'issuer2',
        issuer_code: 'ISSUER_CODE_2',
        name: 'Company Name 2',
      },
    ];
    server.db.read.rows.mockResolvedValue(mockResponse); // Mock the database response

    const result = await getAllKeyGroupByCompany(mockCompanyId);

    expect(server.db.read.rows).toHaveBeenCalledWith(
      expect.stringContaining(
        'JOIN certificate_issuer ci on kg.certificate_issuer_id = ci.certificate_issuer_id'
      ),
      [mockCompanyId]
    );
    expect(result).toEqual(mockResponse);
  });

  it('should return an empty array when no key groups are found', async () => {
    server.db.read.rows.mockResolvedValue([]); // Mock an empty response

    const result = await getAllKeyGroupByCompany(mockCompanyId);

    expect(result).toEqual([]); // Expect an empty array
  });

  it('should log an error and throw when the database call fails', async () => {
    const mockError = new Error('Database error');
    server.db.read.rows.mockRejectedValue(mockError); // Mock the database error

    await expect(getAllKeyGroupByCompany(mockCompanyId)).rejects.toThrow(
      mockError
    );
    expect(mockLogError).toHaveBeenCalledWith(
      mockError,
      expect.stringContaining(
        `keyRequestHelper.getAllKeyGroupByCompany(companyId: ${mockCompanyId}, error`
      )
    );
  });
});

describe('getAllAllowedKeyGroupByCompany', () => {
  const mockCompanyId = 'test-company-id';

  afterEach(() => {
    vi.resetAllMocks(); // Clear mocks after each test
  });

  it('should return allowed key groups when the database call is successful', async () => {
    const mockResponse = [
      {
        key_group_id: 'kg1',
        key_group_name: 'Key Group 1',
        key_group_ref: 'KG1',
        company_id: mockCompanyId,
        name: 'Company Name 1',
        certificate_issuer_id: 'issuer1',
        issuer_code: 'ISSUER_CODE_1',
      },
      {
        key_group_id: 'kg2',
        key_group_name: 'Key Group 2',
        key_group_ref: 'KG2',
        company_id: mockCompanyId,
        name: 'Company Name 2',
        certificate_issuer_id: 'issuer2',
        issuer_code: 'ISSUER_CODE_2',
      },
    ];
    server.db.read.rows.mockResolvedValue(mockResponse); // Mock the database response

    const result = await getAllAllowedKeyGroupByCompany(mockCompanyId);

    expect(server.db.read.rows).toHaveBeenCalledWith(
      expect.stringContaining('SELECT distinct'),
      [mockCompanyId]
    );
    expect(result).toEqual(mockResponse);
  });

  it('should return an empty array when no allowed key groups are found', async () => {
    server.db.read.rows.mockResolvedValue([]); // Mock an empty response

    const result = await getAllAllowedKeyGroupByCompany(mockCompanyId);

    expect(result).toEqual([]); // Expect an empty array
  });

  it('should log an error and throw when the database call fails', async () => {
    const mockError = new Error('Database error');
    server.db.read.rows.mockRejectedValue(mockError); // Mock the database error

    await expect(getAllAllowedKeyGroupByCompany(mockCompanyId)).rejects.toThrow(
      mockError
    );
    expect(mockLogError).toHaveBeenCalledWith(
      mockError,
      expect.stringContaining(
        `keyRequestHelper.getAllAllowedKeyGroupByCompany(companyId: ${mockCompanyId}, error`
      )
    );
  });
});

describe('getApproverKRSSession', () => {
  const mockKeyRequestSessionId = 'test-session-id';

  afterEach(() => {
    vi.resetAllMocks(); // Clear mocks after each test
  });

  it('should return approvers when the database call is successful', async () => {
    const mockResponse = [
      { authorizer_user_id: 'user1' },
      { authorizer_user_id: 'user2' },
    ];
    server.db.read.rows.mockResolvedValue(mockResponse); // Mock the database response

    const result = await getApproverKRSSession(mockKeyRequestSessionId);

    expect(server.db.read.rows).toHaveBeenCalledWith(
      'select authorizer_user_id from key_request_session_authorizer where key_request_session_id=$1',
      [mockKeyRequestSessionId]
    );
    expect(result).toEqual(mockResponse);
  });

  it('should return an empty array when no approvers are found', async () => {
    server.db.read.rows.mockResolvedValue([]); // Mock an empty response

    const result = await getApproverKRSSession(mockKeyRequestSessionId);

    expect(result).toEqual([]); // Expect an empty array
  });

  it('should log an error and throw when the database call fails', async () => {
    const mockError = new Error('Database error');
    server.db.read.rows.mockRejectedValue(mockError); // Mock the database error

    await expect(
      getApproverKRSSession(mockKeyRequestSessionId)
    ).rejects.toThrow(mockError);
    expect(mockLogError).toHaveBeenCalledWith(
      mockError,
      expect.stringContaining(
        `keyRequestHelper.getApproverKRSSession(keyRequestSessionId: ${mockKeyRequestSessionId}, error`
      )
    );
  });
});

describe('deleteRequestDeviceEntry', () => {
  const mockKeyRequestSessionId = 'test-session-id';

  afterEach(() => {
    vi.resetAllMocks(); // Clear mocks after each test
  });

  it('should successfully delete device entries when the database call is successful', async () => {
    mockWriteExecute.mockResolvedValue({}); // Mock a successful database execution

    await deleteRequestDeviceEntry(mockKeyRequestSessionId);

    expect(mockWriteExecute).toHaveBeenCalledWith(
      'DELETE FROM key_request_device WHERE key_request_session_id = $1',
      [mockKeyRequestSessionId]
    );
  });

  it('should log an error and throw when the database call fails', async () => {
    const mockError = new Error('Database error');
    mockWriteExecute.mockRejectedValue(mockError); // Mock the database error

    await expect(
      deleteRequestDeviceEntry(mockKeyRequestSessionId)
    ).rejects.toThrow(mockError);
    expect(mockLogError).toHaveBeenCalledWith(
      mockError,
      expect.stringContaining(
        `keyRequestHelper.deleteRequestDeviceEntry(keyRequestSessionId: ${mockKeyRequestSessionId}, error`
      )
    );
  });
});

describe('updateKeyRequestSession', () => {
  afterEach(() => {
    vi.resetAllMocks(); // Clear mocks after each test
  });

  it('should successfully update the key request session when the database call is successful', async () => {
    const mockSessionData = {
      krsId: 'test-session-id',
      krscompanyId: 'test-company-id',
      keyGroupRef: 'test-key-group-ref',
      krsname: 'Test Session Name',
    };

    mockWriteExecute.mockResolvedValue({}); // Mock a successful database execution

    await updateKeyRequestSession(mockSessionData);

    expect(mockWriteExecute).toHaveBeenCalledWith(
      'UPDATE key_request_session set company_id = $2, name = $3, key_group_ref=$4 where key_request_session_id = $1',
      [
        mockSessionData.krsId,
        mockSessionData.krscompanyId,
        mockSessionData.krsname,
        mockSessionData.keyGroupRef,
      ]
    );
  });

  it('should log an error and throw when the database call fails', async () => {
    const mockSessionData = {
      krsId: 'test-session-id',
      krscompanyId: 'test-company-id',
      keyGroupRef: 'test-key-group-ref',
      krsname: 'Test Session Name',
    };

    const mockError = new Error('Database error');
    mockWriteExecute.mockRejectedValue(mockError); // Mock the database error

    await expect(updateKeyRequestSession(mockSessionData)).rejects.toThrow(
      mockError
    );
  });
});

// Unit tests
describe('getKeyRequestSessionDetails', () => {
  const keyRequestId = 'test-key-request-id';

  it('should return session details when the database call is successful', async () => {
    const mockSessionData = {
      key_request_session_id: keyRequestId,
      status: 'active',
      creator: 'test-user',
    };

    mockDbReadRow.mockResolvedValue(mockSessionData); // Mock successful DB response

    const result = await getKeyRequestSessionDetails(keyRequestId);

    expect(result).toEqual(mockSessionData);
    expect(mockDbReadRow).toHaveBeenCalledWith(
      expect.stringContaining('select distinct on (krs.key_request_session_id'),
      [keyRequestId]
    );
  });

  it('should log an error and throw when the database call fails', async () => {
    const mockError = new Error('Database error');
    mockDbReadRow.mockRejectedValue(mockError); // Mock the database error

    await expect(getKeyRequestSessionDetails(keyRequestId)).rejects.toThrow(
      mockError
    );
  });
});

describe('getAllKeyRequestSessionSummaries', () => {
  const userDetails = {
    sub: 'test-user-id',
    company: {
      id: 'test-company-id',
    },
  };

  const status = '1,2';
  const pageIndex = 0;
  const pageSize = 10;

  it('should return key request session summaries when the database call is successful', async () => {
    const mockSummaryData = [{ id: 'summary1' }, { id: 'summary2' }];
    mockDbReadRows.mockResolvedValue(mockSummaryData); // Mock successful DB response

    const result = await getAllKeyRequestSessionSummaries(
      userDetails,
      [],
      status,
      pageIndex,
      pageSize
    );

    expect(result).toEqual(mockSummaryData);
    expect(mockDbReadRows).toHaveBeenCalledWith(
      expect.stringContaining('SELECT DISTINCT *'),
      expect.arrayContaining([
        userDetails.sub,
        userDetails.company.id,
        ['1', '2'],
        pageIndex * pageSize,
        (pageIndex + 1) * pageSize,
      ])
    );
  });

  it('should log an error and throw when the database call fails', async () => {
    const mockError = new Error('Database error');
    mockDbReadRows.mockRejectedValue(mockError); // Mock the database error

    await expect(
      getAllKeyRequestSessionSummaries(
        userDetails,
        [],
        status,
        pageIndex,
        pageSize
      )
    ).rejects.toThrow(mockError);
  });
});

describe('getAllKeyRequestSessionSummariesCount', () => {
  const userDetails = {
    sub: 'test-user-id',
    company: {
      id: 'test-company-id',
    },
  };

  const status = '1,2';

  it('should return the count of key request session summaries when the database call is successful', async () => {
    const mockCountData = [{ count: 5 }]; // Mocking the count response
    mockDbReadRows.mockResolvedValue(mockCountData); // Mock successful DB response

    const result = await getAllKeyRequestSessionSummariesCount(
      userDetails,
      [],
      status
    );

    expect(result).toEqual(mockCountData);
    expect(mockDbReadRows).toHaveBeenCalledWith(
      expect.stringContaining('SELECT COUNT (DISTINCT krs)'),
      expect.arrayContaining([
        userDetails.sub,
        userDetails.company.id,
        ['1', '2'],
      ])
    );
  });

  it('should log an error and throw when the database call fails', async () => {
    const mockError = new Error('Database error');
    mockDbReadRows.mockRejectedValue(mockError); // Mock the database error

    await expect(
      getAllKeyRequestSessionSummariesCount(userDetails, [], status)
    ).rejects.toThrow(mockError);
  });
});
