import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import restifyErrors from 'restify-errors';
import _ from 'lodash';
import {
  approveRequest,
  cancelRequest,
  expire,
  processKeyRequestDevice,
} from '../../../src/rki/api/approval/approval.handler';
import {
  KeyRequestDevice,
  KeyRequestSession,
} from '../../../src/rki/api/approval/approval';
import keyRequestHelper, {
  KEY_REQUEST_SESSION_STATUS,
} from '../../../helpers/key-request-helper';
import KRSHelper from '../../../src/rki/api/krs/krs-helper';
import totp from '../../../lib/totp';
import { server } from '../../../app';
import krdStateMachine from '../../../src/rki/lib/key-request-device.statemachine';
import * as invoke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../../src/rki/api/krs/invokeRKILambdaForDevice';
import CONSTANTS from '../../../src/rki/lib/constants';

const mockToptValidateTotp = vi.fn();
totp.validateTotp = mockToptValidateTotp;

// Mock the connection object
const mockConnectionExecute = vi.fn().mockResolvedValue('result'); // Mock implementation for execute
const mockDone = vi.fn(); // Mock implementation for done

const connection = {
  execute: mockConnectionExecute,
  done: mockDone,
};
const mockWriteExecute = vi.fn().mockResolvedValue([]);
server.db.write.execute = mockWriteExecute;
server.db.write.getConnection = vi.fn().mockResolvedValue(connection);

const mockDbReadRows = vi.fn();
const mockDbReadRow = vi.fn();
server.db.read.rows = mockDbReadRows; // Access the mocked function
server.db.read.row = mockDbReadRow; // Access the mocked function

// Mocking the krdStateMachine and AWS.invokeLambda
const mockKrdStateMachineLoad = vi.fn().mockResolvedValueOnce({
  keyRetrieval: vi.fn(),
  keyInstallFailed: vi.fn(),
});
const mockKrdStateMachine = {
  load: mockKrdStateMachineLoad,
  actions: {
    keyRetrieval: { name: 'keyRetrieval' },
    keyInstallFailed: { name: 'keyInstallFailed' },
  },
  states: {
    removed: -1,
  },
};
krdStateMachine.load = mockKrdStateMachine.load;
// @ts-ignore
krdStateMachine.actions = mockKrdStateMachine.actions;
// @ts-ignore
krdStateMachine.states = mockKrdStateMachine.states;

const mockInvokeRIILambda = vi
  .spyOn(invokeRKILambda, 'invokeLambdaForDevice')
  .mockResolvedValue(null);

describe('Key Request Controller', () => {
  let req: any;
  let res: any;
  let next: any;

  beforeEach(() => {
    // @ts-ignore
    server.db.write.getConnection = vi.fn().mockResolvedValue(connection);
    req = {
      body: {},
      params: {},
      user: {
        sub: 'user-id',
        company: { id: 'company-id' },
      },
      log: {
        info: vi.fn(),
        error: vi.fn(),
      },
    };

    res = {
      send: vi.fn(),
    };

    next = vi.fn();

    mockKrdStateMachineLoad.mockResolvedValueOnce({
      keyRetrieval: vi.fn(),
      keyInstallFailed: vi.fn(),
    });
    mockInvokeRIILambda.mockResolvedValueOnce(null);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('approveRequest', () => {
    it('should return 404 if key request session is not found', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      vi.spyOn(keyRequestHelper, 'getKeyRequestSessionById').mockResolvedValue({
        keyRequestSessionId: 'session-id',
        companyId: 'company-id',
        requestUserId: 'user-id',
        status: 'PENDING_AUTHORIZATION',
      });
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      keyRequestHelper.getKeyRequestSessionById = vi
        .fn()
        .mockResolvedValue(null);

      await approveRequest(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.NotFoundError(
          'Key request session with id: session-id not found'
        )
      );
    });

    it('should return 403 if company ID does not match', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      keyRequestHelper.getKeyRequestSessionById = vi
        .fn()
        .mockResolvedValue({ companyId: 'otherCompanyId' });

      await approveRequest(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.ForbiddenError('Permission denied')
      );
    });

    it('should return 400 if the user has already approved the request', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      keyRequestHelper.getKeyRequestSessionById = vi.fn().mockResolvedValue({
        companyId: 'companyId',
        requestUserId: 'userId',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
      });

      await approveRequest(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.ForbiddenError('Permission denied')
      );
    });

    it('should return 409 if the request is not in pending status', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      keyRequestHelper.getKeyRequestSessionById = vi.fn().mockResolvedValue({
        companyId: 'company-id',
        requestUserId: 'otherUser-id',
        status: KEY_REQUEST_SESSION_STATUS.APPROVED,
      });

      await approveRequest(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.ConflictError(
          'The current request can no longer be approved or rejected. Please reload.'
        )
      );
    });

    it('should return 400 if the approval user is same as request user', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      keyRequestHelper.getKeyRequestDevicesBySessionId = vi
        .fn()
        .mockResolvedValue([
          {
            keyRequestDeviceId: 'key-request-device-id',
            deviceId: 1232,
            certificate: 'certificate',
            keyGroupRef: 'key-group-ref',
            issuerCode: 'issuer-code',
            status: KEY_REQUEST_SESSION_STATUS.IN_PROGRESS,
            isJsonCertificates: false,
          },
        ]);
      KRSHelper.getKRSApprover = vi.fn().mockResolvedValue([
        {
          id: 'approver-id',
          fullName: 'full-name',
          email: 'approver-email',
          status: 'Active',
        },
      ]);
      KRSHelper.createRKINotification = vi.fn().mockResolvedValue(null);

      keyRequestHelper.getKeyRequestSessionById = vi.fn().mockResolvedValue({
        name: 'Test Session',
        companyId: 'company-id',
        requestUserId: 'user-id',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
        authorizerUserId: null,
      });

      keyRequestHelper.updateKeyRequestSession = vi
        .fn()
        .mockResolvedValue(true);

      await approveRequest(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.BadRequestError(
          'This RKI has been already approved by this user'
        )
      );
    });

    it('should return 400 if the appprover already approve request', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      keyRequestHelper.getKeyRequestDevicesBySessionId = vi
        .fn()
        .mockResolvedValue([
          {
            keyRequestDeviceId: 'key-request-device-id',
            deviceId: 1232,
            certificate: 'certificate',
            keyGroupRef: 'key-group-ref',
            issuerCode: 'issuer-code',
            status: KEY_REQUEST_SESSION_STATUS.IN_PROGRESS,
            isJsonCertificates: false,
          },
        ]);
      KRSHelper.getKRSApprover = vi.fn().mockResolvedValue([
        {
          id: 'approver-id',
          fullName: 'full-name',
          email: 'approver-email',
          status: 'Active',
        },
      ]);
      KRSHelper.createRKINotification = vi.fn().mockResolvedValue(null);

      keyRequestHelper.getKeyRequestSessionById = vi.fn().mockResolvedValue({
        name: 'Test Session',
        companyId: 'company-id',
        requestUserId: 'request-user-id',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
        authorizerUserId: 'user-id',
      });

      keyRequestHelper.updateKeyRequestSession = vi
        .fn()
        .mockResolvedValue(true);

      await approveRequest(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.BadRequestError(
          'This RKI has been already approved by this user'
        )
      );
    });

    it('should handle the first approval correctly', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      keyRequestHelper.getKeyRequestDevicesBySessionId = vi
        .fn()
        .mockResolvedValue([
          {
            keyRequestDeviceId: 'key-request-device-id',
            deviceId: 1232,
            certificate: 'certificate',
            keyGroupRef: 'key-group-ref',
            issuerCode: 'issuer-code',
            status: KEY_REQUEST_SESSION_STATUS.IN_PROGRESS,
            isJsonCertificates: false,
          },
        ]);
      KRSHelper.getKRSApprover = vi.fn().mockResolvedValue([
        {
          id: 'approver-id',
          fullName: 'full-name',
          email: 'approver-email',
          status: 'Active',
        },
      ]);
      KRSHelper.createRKINotification = vi.fn().mockResolvedValue(null);

      keyRequestHelper.getKeyRequestSessionById = vi.fn().mockResolvedValue({
        companyId: 'company-id',
        requestUserId: 'approver-user-id',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
        authorizerUserId: null,
      });

      keyRequestHelper.updateKeyRequestSession = vi
        .fn()
        .mockResolvedValue(true);

      await approveRequest(req, res, next);

      expect(mockConnectionExecute).toHaveBeenCalledWith('BEGIN');
      expect(keyRequestHelper.updateKeyRequestSession).toHaveBeenCalledWith(
        'session-id',
        expect.any(Object),
        connection
      );
      expect(mockConnectionExecute).toHaveBeenCalledWith('COMMIT');
      expect(res.send).toHaveBeenCalledWith(204);
    });
    it('should handle the first approval correctly - automated RKI', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      keyRequestHelper.getKeyRequestDevicesBySessionId = vi
        .fn()
        .mockResolvedValue([
          {
            keyRequestDeviceId: 'key-request-device-id',
            deviceId: 1232,
            certificate: 'certificate',
            keyGroupRef: 'key-group-ref',
            issuerCode: 'issuer-code',
            status: KEY_REQUEST_SESSION_STATUS.IN_PROGRESS,
            isJsonCertificates: false,
          },
        ]);
      KRSHelper.getKRSApprover = vi.fn().mockResolvedValue([
        {
          id: 'approver-id',
          fullName: 'full-name',
          email: 'approver-email',
          status: 'Active',
        },
      ]);
      KRSHelper.createRKINotification = vi.fn().mockResolvedValue(null);

      keyRequestHelper.getKeyRequestSessionById = vi.fn().mockResolvedValue({
        name: '[Automated] RKI Request',
        companyId: 'company-id',
        requestUserId: 'approver-user-id',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
        authorizerUserId: null,
      });

      keyRequestHelper.updateKeyRequestSession = vi
        .fn()
        .mockResolvedValue(true);

      await approveRequest(req, res, next);

      expect(mockConnectionExecute).toHaveBeenCalledWith('BEGIN');
      expect(keyRequestHelper.updateKeyRequestSession).toHaveBeenCalledWith(
        'session-id',
        expect.any(Object),
        connection
      );
      expect(mockConnectionExecute).toHaveBeenCalledWith('COMMIT');
      expect(res.send).toHaveBeenCalledWith(204);
    });

    it('should handle the second approval correctly', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      keyRequestHelper.getKeyRequestDevicesBySessionId = vi
        .fn()
        .mockResolvedValue([
          {
            keyRequestDeviceId: 'key-request-device-id',
            deviceId: 1232,
            certificate: 'certificate',
            keyGroupRef: 'key-group-ref',
            issuerCode: 'issuer-code',
            status: KEY_REQUEST_SESSION_STATUS.IN_PROGRESS,
            isJsonCertificates: false,
          },
        ]);
      KRSHelper.getKRSApprover = vi.fn().mockResolvedValue([
        {
          id: 'approver-id',
          fullName: 'full-name',
          email: 'approver-email',
          status: 'Active',
        },
      ]);
      KRSHelper.createRKINotification = vi.fn().mockResolvedValue(null);

      keyRequestHelper.getKeyRequestSessionById = vi.fn().mockResolvedValue({
        companyId: 'company-id',
        requestUserId: 'otherUserId',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_SECOND_AUTHORIZATION,
        authorizerUserId: 'anotherUserId',
        secondAuthorizerUserId: null,
      });

      keyRequestHelper.updateKeyRequestSession = vi
        .fn()
        .mockResolvedValue(true);

      await approveRequest(req, res, next);

      expect(mockConnectionExecute).toHaveBeenCalledWith('BEGIN');
      expect(keyRequestHelper.updateKeyRequestSession).toHaveBeenCalledWith(
        'session-id',
        expect.any(Object),
        connection
      );
      expect(mockConnectionExecute).toHaveBeenCalledWith('COMMIT');
      expect(res.send).toHaveBeenCalledWith(204);
    });

    it('should handle should reject RKI request', async () => {
      req.body.approved = false;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      keyRequestHelper.getKeyRequestDevicesBySessionId = vi
        .fn()
        .mockResolvedValue([
          {
            keyRequestDeviceId: 'key-request-device-id',
            deviceId: 1232,
            certificate: 'certificate',
            keyGroupRef: 'key-group-ref',
            issuerCode: 'issuer-code',
            status: KEY_REQUEST_SESSION_STATUS.IN_PROGRESS,
            isJsonCertificates: false,
          },
        ]);
      KRSHelper.getKRSApprover = vi.fn().mockResolvedValue([
        {
          id: 'approver-id',
          fullName: 'full-name',
          email: 'approver-email',
          status: 'Active',
        },
      ]);
      KRSHelper.createRKINotification = vi.fn().mockResolvedValue(null);

      keyRequestHelper.getKeyRequestSessionById = vi.fn().mockResolvedValue({
        companyId: 'company-id',
        requestUserId: 'approver-user-id',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
        authorizerUserId: null,
      });

      keyRequestHelper.updateKeyRequestSession = vi
        .fn()
        .mockResolvedValue(true);

      await approveRequest(req, res, next);

      expect(mockConnectionExecute).toHaveBeenCalledWith('BEGIN');
      expect(keyRequestHelper.updateKeyRequestSession).toHaveBeenCalledWith(
        'session-id',
        expect.any(Object),
        connection
      );
      expect(mockConnectionExecute).toHaveBeenCalledWith('COMMIT');
      expect(res.send).toHaveBeenCalledWith(204);
    });

    it('should handle update failure', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      keyRequestHelper.getKeyRequestDevicesBySessionId = vi
        .fn()
        .mockResolvedValue([
          {
            keyRequestDeviceId: 'key-request-device-id',
            deviceId: 1232,
            certificate: 'certificate',
            keyGroupRef: 'key-group-ref',
            issuerCode: 'issuer-code',
            status: KEY_REQUEST_SESSION_STATUS.IN_PROGRESS,
            isJsonCertificates: false,
          },
        ]);
      KRSHelper.getKRSApprover = vi.fn().mockResolvedValue([
        {
          id: 'approver-id',
          fullName: 'full-name',
          email: 'approver-email',
          status: 'Active',
        },
      ]);
      KRSHelper.createRKINotification = vi.fn().mockResolvedValue(null);

      keyRequestHelper.getKeyRequestSessionById = vi.fn().mockResolvedValue({
        companyId: 'company-id',
        requestUserId: 'otherUserId',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
        authorizerUserId: null,
      });

      keyRequestHelper.updateKeyRequestSession = vi
        .fn()
        .mockResolvedValue(false);

      await approveRequest(req, res, next);

      expect(mockConnectionExecute).toHaveBeenCalledWith('ROLLBACK');
      expect(next).toHaveBeenCalledWith(
        new restifyErrors.InternalServerError(
          'Failed to update key request session'
        )
      );
    });

    it('should invoke RKI service lambda and handle errors', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      keyRequestHelper.getKeyRequestDevicesBySessionId = vi
        .fn()
        .mockResolvedValue([
          {
            keyRequestDeviceId: 'key-request-device-id',
            deviceId: 1232,
            certificate: 'certificate',
            keyGroupRef: 'key-group-ref',
            issuerCode: 'issuer-code',
            status: KEY_REQUEST_SESSION_STATUS.IN_PROGRESS,
            isJsonCertificates: false,
          },
        ]);
      KRSHelper.getKRSApprover = vi.fn().mockResolvedValue([
        {
          id: 'approver-id',
          fullName: 'full-name',
          email: 'approver-email',
          status: 'Active',
        },
      ]);
      KRSHelper.createRKINotification = vi.fn().mockResolvedValue(null);

      keyRequestHelper.getKeyRequestSessionById = vi.fn().mockResolvedValue({
        companyId: 'company-id',
        requestUserId: 'otherUserId',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_SECOND_AUTHORIZATION,
        authorizerUserId: null,
      });
      // Mocking the invocation of RKI service lambda
      keyRequestHelper.getKeyRequestDevicesBySessionId = vi
        .fn()
        .mockResolvedValue([]);
      keyRequestHelper.updateKeyRequestSession = vi
        .fn()
        .mockResolvedValue(true);

      await approveRequest(req, res, next);

      expect(mockConnectionExecute).toHaveBeenCalledWith('COMMIT');
      expect(res.send).toHaveBeenCalledWith(204);
    });

    it('should handle error during RKI service invocation', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(true);
      req.params.keyRequestSessionId = 'session-id';

      keyRequestHelper.getKeyRequestDevicesBySessionId = vi
        .fn()
        .mockResolvedValue([
          {
            keyRequestDeviceId: 'key-request-device-id',
            deviceId: 1232,
            certificate: 'certificate',
            keyGroupRef: 'key-group-ref',
            issuerCode: 'issuer-code',
            status: KEY_REQUEST_SESSION_STATUS.IN_PROGRESS,
            isJsonCertificates: false,
          },
        ]);
      KRSHelper.getKRSApprover = vi.fn().mockResolvedValue([
        {
          id: 'approver-id',
          fullName: 'full-name',
          email: 'approver-email',
          status: 'Active',
        },
      ]);
      KRSHelper.createRKINotification = vi.fn().mockResolvedValue(null);

      keyRequestHelper.getKeyRequestSessionById = vi.fn().mockResolvedValue({
        companyId: 'company-id',
        requestUserId: 'otherUserId',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_SECOND_AUTHORIZATION,
        authorizerUserId: null,
      });
      // Mocking the invocation of RKI service lambda
      keyRequestHelper.getKeyRequestDevicesBySessionId = vi
        .fn()
        .mockRejectedValue(new Error('DB Error'));
      keyRequestHelper.updateKeyRequestSession = vi
        .fn()
        .mockResolvedValue(true);

      await approveRequest(req, res, next);

      expect(mockConnectionExecute).toHaveBeenCalledWith('ROLLBACK');
      expect(next).toHaveBeenCalledWith(
        new restifyErrors.InternalServerError('Failed to approve the session')
      );
    });

    it('should return error for invalid MFA code', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'invalid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '2434354',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(false);

      await approveRequest(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.NotAcceptableError('MFA code invalid')
      );
    });

    // Additional tests for other scenarios...
  });

  describe('cancelRequest', () => {
    it('should return error for invalid MFA code', async () => {
      req.body.approved = true;
      req.body.mfaCode = 'invalid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '2434354',
        stacks: [],
      });
      mockToptValidateTotp.mockResolvedValue(false);

      await cancelRequest(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.NotAcceptableError('MFA code invalid')
      );
    });

    it('should cancel a key request session', async () => {
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });

      mockToptValidateTotp.mockResolvedValue(true);

      vi.spyOn(keyRequestHelper, 'getKeyRequestSessionById').mockResolvedValue({
        keyRequestSessionId: 'session-id',
        requestUserId: 'user-id',
        companyId: 'company-id',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
      });
      keyRequestHelper.updateKeyRequestSession = vi
        .fn()
        .mockResolvedValue(true);

      await cancelRequest(req, res, next);

      expect(keyRequestHelper.updateKeyRequestSession).toHaveBeenCalledWith(
        'session-id',
        {
          status: KEY_REQUEST_SESSION_STATUS.CANCELLED,
          authorizerUserId: 'user-id',
          authorizedTime: expect.any(String),
        }
      );
      expect(res.send).toHaveBeenCalledWith(204);
      expect(next).toHaveBeenCalled();
    });

    it('should return 404 if key request session is not found', async () => {
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });

      mockToptValidateTotp.mockResolvedValue(true);
      vi.spyOn(keyRequestHelper, 'getKeyRequestSessionById').mockResolvedValue(
        null
      );

      await cancelRequest(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.NotFoundError(
          'Key request session with id: session-id not found'
        )
      );
    });

    it('should return 403 if user is not the request user', async () => {
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });

      mockToptValidateTotp.mockResolvedValue(true);

      vi.spyOn(keyRequestHelper, 'getKeyRequestSessionById').mockResolvedValue({
        keyRequestSessionId: 'session-id',
        requestUserId: 'other-user-id',
        companyId: 'company-id',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
      });

      await cancelRequest(req, res, next);
      expect(next).toHaveBeenCalledWith(
        new restifyErrors.ForbiddenError(
          'Permission denied - Authorizer cannot cancel a pending request. Only reject it'
        )
      );
    });

    it('should return 403 if company ID does not match', async () => {
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });

      mockToptValidateTotp.mockResolvedValue(true);

      vi.spyOn(keyRequestHelper, 'getKeyRequestSessionById').mockResolvedValue({
        keyRequestSessionId: 'session-id',
        requestUserId: 'user-id',
        companyId: 'other-company-id',
        status: KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
      });

      await cancelRequest(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.ForbiddenError('Permission denied')
      );
    });

    it('should return 400 if the request is not in pending status', async () => {
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });

      mockToptValidateTotp.mockResolvedValue(true);

      vi.spyOn(keyRequestHelper, 'getKeyRequestSessionById').mockResolvedValue({
        keyRequestSessionId: 'session-id',
        requestUserId: 'user-id',
        companyId: 'company-id',
        status: KEY_REQUEST_SESSION_STATUS.APPROVED,
      });

      await cancelRequest(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.BadRequestError(
          'Request can only be cancelled when pending authorization'
        )
      );
    });

    it('should handle errors during the process', async () => {
      req.body.mfaCode = 'valid-mfa-code';
      req.params.keyRequestSessionId = 'session-id';
      mockDbReadRow.mockResolvedValueOnce({
        mfaSecret: 'ADECFTSF',
        expected: '123456',
        actual: '123456',
        stacks: [],
      });

      mockToptValidateTotp.mockResolvedValue(true);
      keyRequestHelper.getKeyRequestSessionById = vi
        .fn()
        .mockRejectedValue(new Error('DB Error'));

      await cancelRequest(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.InternalServerError(expect.any(Error))
      );
    });
  });

  describe('expire', () => {
    it('should handle the case where there are no expired key request sessions', async () => {
      keyRequestHelper.getExpiredKeyRequestSessions = vi
        .fn()
        .mockResolvedValue([]);

      await expire(req, res, next);

      expect(res.send).toHaveBeenCalledWith(204);
      expect(next).toHaveBeenCalled();
      expect(req.log.info).not.toHaveBeenCalled(); // No log for zero sessions
    });

    it('should handle expired key request sessions correctly', async () => {
      const expiredSessions = [
        { keyRequestSessionId: 'session1' },
        { keyRequestSessionId: 'session2' },
        { keyRequestSessionId: 'session3' },
      ];

      keyRequestHelper.getExpiredKeyRequestSessions = vi
        .fn()
        .mockResolvedValue(expiredSessions);

      mockWriteExecute.mockResolvedValue(true); // Mock successful DB update

      await expire(req, res, next);

      expect(mockWriteExecute).toHaveBeenCalledTimes(1); // Should be called once for the entire chunk
      expect(mockWriteExecute).toHaveBeenCalledWith(
        `
                UPDATE key_request_session SET status = $1
                WHERE key_request_session_id = ANY ($2)`,
        [
          KEY_REQUEST_SESSION_STATUS.EXPIRED,
          ['session1', 'session2', 'session3'],
        ]
      );
      expect(req.log.info).toHaveBeenCalledWith(
        `Expired a total of ${expiredSessions.length} keyRequestSessions as they are passed their expiry date`
      );
      expect(res.send).toHaveBeenCalledWith(204);
      expect(next).toHaveBeenCalled();
    });

    it('should handle database update failure', async () => {
      const expiredSessions = [{ keyRequestSessionId: 'session1' }];

      keyRequestHelper.getExpiredKeyRequestSessions = vi
        .fn()
        .mockResolvedValue(expiredSessions);
      mockWriteExecute.mockRejectedValue(new Error('DB update failed'));

      await expire(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.InternalServerError(new Error('DB update failed'))
      );
    });

    it('should handle error during fetching expired key request sessions', async () => {
      keyRequestHelper.getExpiredKeyRequestSessions = vi
        .fn()
        .mockRejectedValue(new Error('Fetch error'));

      await expire(req, res, next);

      expect(next).toHaveBeenCalledWith(
        new restifyErrors.InternalServerError(new Error('Fetch error'))
      );
    });

    it('should handle errors during expiration', async () => {
      vi.spyOn(
        keyRequestHelper,
        'getExpiredKeyRequestSessions'
      ).mockResolvedValue([]);

      await expire(req, res, next);

      expect(res.send).toHaveBeenCalledWith(204);
      expect(next).toHaveBeenCalled();
    });

    it('should handle multiple chunks of expired key request sessions', async () => {
      const expiredSessions = Array.from({ length: 25 }, (_x, index) => ({
        keyRequestSessionId: `session${index + 1}`,
      }));

      keyRequestHelper.getExpiredKeyRequestSessions = vi
        .fn()
        .mockResolvedValue(expiredSessions);
      mockWriteExecute.mockResolvedValue(true);

      await expire(req, res, next);

      expect(mockWriteExecute).toHaveBeenCalledTimes(3); // Should be called for each chunk
      expect(mockWriteExecute).toHaveBeenCalledWith(expect.any(String), [
        KEY_REQUEST_SESSION_STATUS.EXPIRED,
        [
          'session1',
          'session2',
          'session3',
          'session4',
          'session5',
          'session6',
          'session7',
          'session8',
          'session9',
          'session10',
        ],
      ]);
      expect(mockWriteExecute).toHaveBeenCalledWith(expect.any(String), [
        KEY_REQUEST_SESSION_STATUS.EXPIRED,
        [
          'session11',
          'session12',
          'session13',
          'session14',
          'session15',
          'session16',
          'session17',
          'session18',
          'session19',
          'session20',
        ],
      ]);
      expect(mockWriteExecute).toHaveBeenCalledWith(expect.any(String), [
        KEY_REQUEST_SESSION_STATUS.EXPIRED,
        ['session21', 'session22', 'session23', 'session24', 'session25'],
      ]);
      expect(req.log.info).toHaveBeenCalledWith(
        `Expired a total of ${expiredSessions.length} keyRequestSessions as they are passed their expiry date`
      );
      expect(res.send).toHaveBeenCalledWith(204);
      expect(next).toHaveBeenCalled();
    });
  });

  describe('processKeyRequestDevice', () => {
    const keyRequestSession: KeyRequestSession = {
      keyRequestSessionId: 'session-123',
      companyId: 'company-456',
      name: 'Test Session',
      keyGroupRef: 'group-789',
      issuerCode: CONSTANTS.ValidCertificateIssuerCode.LEGACY,
      requestUserId: 'user-123',
      authorizedTime: '2023-01-01T00:00:00Z',
      status: 1,
      secondAuthorizedTime: '',
    };

    it('should handle removed device', async () => {
      const krd: KeyRequestDevice = {
        keyRequestDeviceId: 'device-1',
        deviceId: 1,
        status: mockKrdStateMachine.states.removed,
        keyGroupRef: '',
        isJsonCertificates: false,
      };

      const result = await processKeyRequestDevice(
        krd,
        {},
        req,
        keyRequestSession
      );
      expect(result).toEqual({ msg: 'Failed', krdId: krd.keyRequestDeviceId });
    });

    it('should handle missing certificate', async () => {
      const krd: KeyRequestDevice = {
        keyRequestDeviceId: 'device-2',
        deviceId: 2,
        certificate: '',
        keyGroupRef: '',
        status: 0,
        isJsonCertificates: false,
      };

      const result = await processKeyRequestDevice(
        krd,
        {},
        req,
        keyRequestSession
      );
      expect(result).toEqual({
        msg: 'Failed - No certificate found for the device',
        krdId: krd.keyRequestDeviceId,
      });
    });
    it('should handle invalid JSON certificate', async () => {
      const krd: KeyRequestDevice = {
        keyRequestDeviceId: 'device-2',
        deviceId: 2,
        certificate: 'invalid-json-certificates',
        keyGroupRef: '',
        status: 0,
        isJsonCertificates: true,
      };

      const result = await processKeyRequestDevice(
        krd,
        {},
        req,
        keyRequestSession
      );
      expect(result).toEqual({
        msg: 'Failed - Invalid JSON certificate',
        krdId: krd.keyRequestDeviceId,
      });
    });

    it('should handle missing JSON certificate for issuer code', async () => {
      const krd: KeyRequestDevice = {
        keyRequestDeviceId: 'device-2',
        deviceId: 2,
        certificate: '[{"certificate":"test-certificate","issuer_code":"04"}]',
        keyGroupRef: '',
        status: 0,
        isJsonCertificates: true,
      };

      const result = await processKeyRequestDevice(
        krd,
        {},
        req,
        keyRequestSession
      );
      expect(result).toEqual({
        msg: 'Failed - No certificate found for the key group issuer code',
        krdId: krd.keyRequestDeviceId,
      });
    });

    it('should handle JSON certificate is NOT an Array', async () => {
      const krd: KeyRequestDevice = {
        keyRequestDeviceId: 'device-2',
        deviceId: 2,
        certificate: '{"certificate":"test-certificate","issuer_code":"04"}',
        keyGroupRef: '',
        status: 0,
        isJsonCertificates: true,
      };

      const result = await processKeyRequestDevice(
        krd,
        {},
        req,
        keyRequestSession
      );
      expect(result).toEqual({
        msg: 'Failed - Invalid JSON certificate',
        krdId: krd.keyRequestDeviceId,
      });
    });

    it('should handle invalid JSON certificate certificate', async () => {
      const krd: KeyRequestDevice = {
        keyRequestDeviceId: 'device-2',
        deviceId: 2,
        certificate: '',
        keyGroupRef: '',
        status: 0,
        isJsonCertificates: true,
      };

      const result = await processKeyRequestDevice(
        krd,
        {},
        req,
        keyRequestSession
      );
      expect(result).toEqual({
        msg: 'Failed - No certificate found for the device',
        krdId: krd.keyRequestDeviceId,
      });
    });

    it('should handle unsupported key group issuer code', async () => {
      const mockKeyRequestSession = _.cloneDeep(keyRequestSession);
      mockKeyRequestSession.issuerCode = 'UNSUPPORTED_CODE';
      const krd: KeyRequestDevice = {
        keyRequestDeviceId: 'device-3',
        deviceId: 3,
        certificate: 'some-certificate',
        keyGroupRef: '',
        status: 0,
        isJsonCertificates: false,
      };

      const result = await processKeyRequestDevice(
        krd,
        {},
        req,
        mockKeyRequestSession
      );
      expect(result).toEqual({
        msg: 'Failed - Unhandled Key Group Certificate Issuer',
        krdId: krd.keyRequestDeviceId,
      });
    });

    it('should handle non-legacy key group issuer code for legacy certificate', async () => {
      const krd: KeyRequestDevice = {
        keyRequestDeviceId: 'device-3',
        deviceId: 3,
        certificate: 'some-certificate',
        keyGroupRef: '',
        status: 0,
        isJsonCertificates: false,
      };
      const mockKeyRequestSession = _.cloneDeep(keyRequestSession);
      mockKeyRequestSession.issuerCode =
        CONSTANTS.ValidCertificateIssuerCode.TAVE;

      const result = await processKeyRequestDevice(
        krd,
        {},
        req,
        mockKeyRequestSession
      );
      expect(result).toEqual({
        msg: 'Unsupported issuer for legacy certificate',
        krdId: krd.keyRequestDeviceId,
      });
    });

    it('should invoke lambda for valid devices with legacy certificate', async () => {
      const krd: KeyRequestDevice = {
        keyRequestDeviceId: 'device-4',
        deviceId: 4,
        certificate: 'some-certificate',
        status: 0,
        isJsonCertificates: false,
        keyGroupRef: '',
      };

      const result = await processKeyRequestDevice(
        krd,
        {},
        req,
        keyRequestSession
      );
      expect(result).toBeNull(); // Assuming invokeLambdaForDevice does not return anything
      expect(mockInvokeRIILambda).toHaveBeenCalledWith(
        krd,
        keyRequestSession,
        expect.anything() // You can further specify what should be passed here based on your implementation
      );
    });

    it('should invoke lambda for valid devices with JSON certificates', async () => {
      const mockKeyRequestSession = _.cloneDeep(keyRequestSession);
      mockKeyRequestSession.issuerCode =
        CONSTANTS.ValidCertificateIssuerCode.TAVE;
      const krd: KeyRequestDevice = {
        keyRequestDeviceId: 'device-4',
        deviceId: 4,
        certificate:
          '[{"certificate":"test-certificate","issuer_code":"02"},{"certificate":"tave-test-certificate","issuer_code":"04"}]',
        status: 0,
        isJsonCertificates: true,
        keyGroupRef: '',
      };

      const result = await processKeyRequestDevice(
        krd,
        {},
        req,
        mockKeyRequestSession
      );
      expect(result).toBeNull(); // Assuming invokeLambdaForDevice does not return anything
      expect(mockInvokeRIILambda).toHaveBeenCalledWith(
        krd,
        mockKeyRequestSession,
        expect.anything() // You can further specify what should be passed here based on your implementation
      );
    });
  });
});
