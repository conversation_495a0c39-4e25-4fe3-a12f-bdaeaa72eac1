import { vi } from 'vitest';

interface CreateAPIResReqNextSpyProps {
  params: any;
  userData: any;
  body: any;
}
export const creatAPIResReqNextSpy = ({
  params,
  userData,
  body,
}: CreateAPIResReqNextSpyProps) => {
  const res = { send: vi.fn() };
  const next = vi.fn().mockImplementation(args => args);
  const req = {
    params,
    body,
    user: userData,
    log: { error: vi.fn(), info: vi.fn(), debug: vi.fn() },
  };
  return { res, next, req };
};
