import { vi } from 'vitest';
import { server } from '../../../app';

interface MockServerDbProps {
  readRowResult?: any;
  readRowsResult?: any[];
  replicaRowsResult?: any[];
  writeExecuteResult?: any[];
}
export const mockServerDb = ({
  readRowResult,
  readRowsResult,
  replicaRowsResult,
  writeExecuteResult,
}: MockServerDbProps) => {
  server.db.read = {
    rows: vi.fn().mockResolvedValue(readRowsResult),
    row: vi.fn().mockResolvedValue(readRowResult),
  };
  server.db.replica = { rows: vi.fn().mockRejectedValue(replicaRowsResult) };
  server.db.write = {
    getConnection: () => ({
      execute: vi.fn().mockResolvedValue(writeExecuteResult),
      done: vi.fn(),
    }),
  };
  return server;
};
