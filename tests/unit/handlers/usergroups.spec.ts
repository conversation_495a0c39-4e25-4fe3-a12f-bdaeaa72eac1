import { describe, it, expect, vi } from 'vitest';
import { getUserGroups } from '../../../handlers/usergroups';

// Define a mock function for the rows method
// const mockRows = async (query: string) => {

//   if (query.includes('COMPANY_ADMIN')) {

//     return [
//       { id: 1, name: 'Group1', userCount: 5 },
//       { id: 2, name: 'Group2', userCount: 10 },
//     ];
//   }

//     return [
//       { id: 1, name: 'Group1', userCount: 3 },
//       { id: 2, name: 'Group2', userCount: 8 },
//     ];

// };

const server = {
  db: {
    read: {
      rows: vi.fn().mockResolvedValue([]),
    },
  },
};

describe('Test getUserGroups', () => {
  it('- should return user groups for company admin', async () => {
    const req = {
      user: {
        company: {
          id: 'companyId',
        },
        roles: ['COMPANY_ADMIN'],
      },
      log: {
        info: () => {},
        debug: () => {},
        error: () => {},
      },
    };
    const res = {
      send: (data: any) => {
        expect(data).toBeDefined();
      },
    };
    const next = () => {};
    getUserGroups(req, res, next);
    expect(server.db.read.rows.call.length).toBe(1);
    expect(req.user.roles).toContain('COMPANY_ADMIN');
  });

  it('- should return user groups for non-company admin user', async () => {
    const req = {
      user: {
        company: {
          id: 'companyId',
        },
        roles: [],
        username: 'testUsername',
      },
      log: {
        info: () => {},
        debug: () => {},
        error: () => {},
      },
    };
    const res = {
      send: (data: any) => {
        expect(data).toBeDefined();
      },
    };
    const next = () => {};
    getUserGroups(req, res, next);
    expect(req.user.roles).not.toContain('COMPANY_ADMIN');
    expect(server.db.read.rows.call.length).toBe(1);
  });
});
