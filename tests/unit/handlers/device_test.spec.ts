import { describe, it, vi, expect, beforeEach } from 'vitest';
import { server } from '../../../app';
import { findDevices } from '../../../handlers/device/device';

interface QueryParams {
  pageIndex: string;
  pageSize: string;
  showOnlyUnrestrictedDevices?: string | boolean;
  [key: string]: string | boolean | undefined;
}

interface TestRequest {
  query: QueryParams;
  user: {
    company: { id: string };
    sub: string;
  };
  log: {
    info: ReturnType<typeof vi.fn>;
    debug: ReturnType<typeof vi.fn>;
    error: ReturnType<typeof vi.fn>;
  };
}

interface TestResponse {
  send: ReturnType<typeof vi.fn>;
  status: ReturnType<typeof vi.fn> & { mockReturnThis: () => TestResponse };
  json: ReturnType<typeof vi.fn>;
}

type NextFunction = () => void;

interface Device {
  id: number;
  deviceType: {
    id: string;
  };
  serialNumber: string;
  presence?: string;
  status?: number;
}

function createReq(queryOverrides: Partial<QueryParams> = {}): TestRequest {
  return {
    query: {
      pageIndex: '0',
      pageSize: '100',
      ...queryOverrides,
    },
    user: {
      company: { id: 'test-company-id' },
      sub: 'test-user-id',
    },
    log: {
      info: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
    },
  };
}

function createRes(): TestResponse {
  return {
    send: vi.fn(),
    status: vi.fn().mockReturnThis() as ReturnType<typeof vi.fn> & {
      mockReturnThis: () => TestResponse;
    },
    json: vi.fn(),
  };
}

function createNext(): NextFunction {
  return vi.fn();
}

function mockDbResponses(devices: Device[]) {
  server.db.read.row.mockResolvedValue({ count: devices.length });
  server.db.read.rows.mockResolvedValue(devices);
  server.db.replica.rows.mockResolvedValue(devices);
}

describe('Device Handler Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    server.db.read = {
      rows: vi.fn(),
      row: vi.fn(),
    };
    server.db.replica = {
      rows: vi.fn(),
    };
    server.db.write = {
      getConnection: () => ({
        execute: vi.fn(),
        done: vi.fn(),
      }),
    };
  });

  describe('showOnlyUnrestrictedDevices filter', () => {
    it('should filter out restricted devices when showOnlyUnrestrictedDevices is true', async () => {
      const mockDevices: Device[] = [
        {
          id: 1,
          deviceType: { id: 'G6-300' },
          serialNumber: 'DEV001',
          presence: 'PRESENT',
          status: 1,
        },
        {
          id: 2,
          deviceType: { id: 'OMNIA' },
          serialNumber: 'DEV002',
          presence: 'PRESENT',
          status: 1,
        },
        {
          id: 3,
          deviceType: { id: 'SmartCrind' },
          serialNumber: 'DEV003',
          presence: 'PRESENT',
          status: 1,
        },
        {
          id: 4,
          deviceType: { id: 'G6-500' },
          serialNumber: 'DEV004',
          presence: 'PRESENT',
          status: 1,
        },
      ];

      mockDbResponses(mockDevices);
      const req = createReq({ showOnlyUnrestrictedDevices: 'true' });
      const res = createRes();
      const next = createNext();

      await findDevices(req, res, next);

      expect(next).toHaveBeenCalled();
      expect(res.send).toHaveBeenCalled();

      const response = res.send.mock.calls[0][0];
      expect(response.results).toHaveLength(2);
      expect(response.results).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 1,
            deviceType: expect.objectContaining({ id: 'G6-300' }),
          }),
          expect.objectContaining({
            id: 4,
            deviceType: expect.objectContaining({ id: 'G6-500' }),
          }),
        ])
      );

      const restrictedDevices = response.results.filter(
        (device: { deviceType: { id: string } }) =>
          ['OMNIA', 'GSOM', 'SmartCrind'].includes(device.deviceType.id)
      );
      expect(restrictedDevices).toHaveLength(0);
    });

    it('should include all devices when showOnlyUnrestrictedDevices is false', async () => {
      const devices: Device[] = [
        {
          id: 1,
          deviceType: { id: 'G6-300' },
          serialNumber: 'DEV001',
        },
        {
          id: 2,
          deviceType: { id: 'OMNIA' },
          serialNumber: 'DEV002',
        },
        {
          id: 3,
          deviceType: { id: 'SmartCrind' },
          serialNumber: 'DEV003',
        },
      ];

      mockDbResponses(devices);
      const req = createReq({ showOnlyUnrestrictedDevices: false });
      const res = createRes();
      const next = createNext();

      await findDevices(req, res, next);

      expect(res.send).toHaveBeenCalledWith(
        expect.objectContaining({
          results: expect.arrayContaining([
            expect.objectContaining({
              id: 1,
              deviceType: expect.objectContaining({ id: 'G6-300' }),
            }),
            expect.objectContaining({
              id: 2,
              deviceType: expect.objectContaining({ id: 'OMNIA' }),
            }),
            expect.objectContaining({
              id: 3,
              deviceType: expect.objectContaining({ id: 'SmartCrind' }),
            }),
          ]),
        })
      );
      const response = res.send.mock.calls[0][0];
      expect(response.results).toHaveLength(3);
      expect(next).toHaveBeenCalled();
    });

    it('should handle undefined showOnlyUnrestrictedDevices parameter', async () => {
      const devices: Device[] = [
        {
          id: 1,
          deviceType: { id: 'G6-300' },
          serialNumber: 'DEV001',
        },
        {
          id: 2,
          deviceType: { id: 'OMNIA' },
          serialNumber: 'DEV002',
        },
      ];

      mockDbResponses(devices);
      const req = createReq();
      const res = createRes();
      const next = createNext();

      await findDevices(req, res, next);

      const response = res.send.mock.calls[0][0];
      expect(response.results).toHaveLength(2);
      const deviceTypeIds = response.results.map(
        (d: { deviceType: { id: string } }) => d.deviceType.id
      );
      expect(deviceTypeIds).toEqual(
        expect.arrayContaining(['G6-300', 'OMNIA'])
      );
      expect(next).toHaveBeenCalled();
    });
  });
});
