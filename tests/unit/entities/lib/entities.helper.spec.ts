import { describe, it, expect } from 'vitest';
import { buildWhereClause } from '../../../../src/entities/lib/entities.helper';

describe('Test buildWhereClause', () => {
  it('- should return equal', async () => {
    const column = 'testColumn';
    const values = { $eq: 'testEqual' };
    const index = 1;

    const whereClause = buildWhereClause(column, values, index);
    const expectedWhereClause = {
      whereClause: ` WHERE ${column} = $${index}`,
      whereValues: [values.$eq],
    };
    expect(JSON.stringify(whereClause)).toBe(
      JSON.stringify(expectedWhereClause)
    );
  });
});
