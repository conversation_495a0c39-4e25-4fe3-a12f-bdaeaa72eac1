import { describe, it, expect, vi, beforeAll } from 'vitest';
import { server } from '../../../app';
import { getDestinationsForJobCreation } from '../../../src/monitoring/monitoring.repository';

vi.mock('../../app');

describe('getDestinationsForJobCreation', () => {
  beforeAll(() => {
    server.db.read = { rows: vi.fn() };
    server.db.replica = { rows: vi.fn() };
    server.db.write = {
      getConnection: () => ({
        execute: vi.fn(),
        done: vi.fn(),
      }),
    };
  });

  it('should return null if devicePaymentDashboardData has fewer elements than requiredStates', async () => {
    server.db.read.rows.mockResolvedValue([]);

    const result = await getDestinationsForJobCreation({
      deviceId: 'device1',
      monitoringModeOn: true,
    });

    expect(result).toBeNull();
  });

  it('should return jobs if devicePaymentDashboardData matches a condition', async () => {
    server.db.read.rows.mockResolvedValue([
      { state: 'invenco.icsagent.metrics.active-config', status: 'Default' },
      { state: 'invenco.system.metrics.active-config', status: 'Default' },
      {
        state: 'invenco.icsagent.metrics.config-file-labels',
        status: ['Default'],
      },
      {
        state: 'invenco.system.metrics.config-file-labels',
        status: ['Default', 'Intensive'],
      },
    ]);

    const result = await getDestinationsForJobCreation({
      deviceId: 'device1',
      monitoringModeOn: true,
    });

    expect(result).toEqual({
      destinations: ['invenco.system'],
      isIntensive: true,
    });
  });

  it('should return null if devicePaymentDashboardData does not match any condition', async () => {
    server.db.read.rows.mockResolvedValue([
      { state: 'invenco.icsagent.metrics.active-config', status: 'Unknown' },
      { state: 'invenco.system.metrics.active-config', status: 'Unknown' },
      {
        state: 'invenco.icsagent.metrics.config-file-labels',
        status: ['Unknown'],
      },
      {
        state: 'invenco.system.metrics.config-file-labels',
        status: ['Unknown'],
      },
    ]);

    const result = await getDestinationsForJobCreation({
      deviceId: 'device1',
      monitoringModeOn: true,
    });

    expect(result).toBeNull();
  });
});
