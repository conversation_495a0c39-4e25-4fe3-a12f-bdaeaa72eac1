const errors = require('restify-errors');

const { config } = require('../env');
const { DbQueryTimeoutError } = require('./errors');
const { httpCode } = require('./app-constants');

module.exports = {
  onError: function onError(req, res, next) {
    return err => {
      req.log.error(
        `onError handler ${JSON.stringify({ err, message: err?.message, errorStack: err.stack }, null, 2)}`
      );

      switch (true) {
        case err instanceof DbQueryTimeoutError:
          return next(
            new errors.GatewayTimeoutError(
              'Request taking too long to complete'
            )
          );
        case err.statusCode !== httpCode.INTERNAL_SERVER_ERROR.STATUS_CODE:
          return next(err);
        default:
          return next(
            new errors.InternalServerError(
              config.stackTraceResponse ? err.stack : ''
            )
          );
      }
    };
  },
};
