const { gunzipSync } = require('zlib');
const { join, extname, basename } = require('path');
const { readdir, stat, readFile, writeFile, unlink } = require('fs').promises;
const extract = require('extract-zip');
const fs = require('fs-extra');
const { fileTypeFromBuffer } = require('fix-esm').require('file-type');
const archiver = require('archiver');

const { server } = require('../app');
const { fileExtensions } = require('./app-constants');

async function createArchive(sourceDir, destinationFile, keepSourceDir) {
  try {
    const archive = archiver('zip', {
      zlib: {
        level: 9, // Sets the compression level.
      },
    });

    const output = fs.createWriteStream(destinationFile);

    // listen for all archive data to be written
    output.on('close', () => Promise.resolve());

    archive.on('warning', err => {
      server.log.warn(
        `[createArchive] : warning: destinationFile : ${destinationFile} error: ${err}`
      );
      if (err.code !== 'ENOENT') {
        throw err;
      }
    });

    archive.on('error', err => {
      server.log.error(
        `[createArchive] : error in creating archieve ${destinationFile} error:${err} `
      );
      throw err;
    });

    archive.pipe(output);
    archive.directory(sourceDir, false);
    const result = await archive.finalize();
    server.log.info(
      `[createArchive] : Zip file created source: ${sourceDir} destination: ${destinationFile}`
    );

    return result;
  } catch (error) {
    server.log.error(
      `[createArchive] : Error creating zip file: ${error} source: ${sourceDir} destination: ${destinationFile}`
    );
    throw error;
  } finally {
    server.log.info(
      `[createArchive] : Remove source directory:${!keepSourceDir}`
    );
    if (!keepSourceDir) {
      server.log.info(
        `[createArchive] : Removing sourceDir:${sourceDir} destination:${destinationFile}`
      );
      await fs.remove(sourceDir);
    }
  }
}

// Recursively unzip files in a directory
async function unzipRecursively(dirPath) {
  try {
    if (!(await stat(dirPath)).isDirectory()) {
      server.log.info(
        `[unzipRecursively] ${dirPath} is not a valid directory. Returning...`
      );
      return;
    }

    const stats = await readdir(dirPath, { withFileTypes: true });
    for (let i = 0; i < stats.length; i++) {
      const file = stats[i];
      let filePath = join(dirPath, file.name);
      if (file.isDirectory()) {
        // eslint-disable-next-line no-await-in-loop
        await unzipRecursively(filePath);
        // eslint-disable-next-line no-continue
        continue;
      }

      let fileExtension = extname(file.name);

      if (!fileExtension) {
        // eslint-disable-next-line no-await-in-loop
        const buffer = await readFile(filePath);
        // eslint-disable-next-line no-await-in-loop
        const fileTypeObj = await fileTypeFromBuffer(buffer);
        if (fileTypeObj && fileTypeObj.ext) {
          fileExtension = `.${fileTypeObj.ext}`;
          const newFilePath = `${filePath}${fileExtension}`;
          // Write new file with extension, and remove old file
          // eslint-disable-next-line no-await-in-loop
          await Promise.all([writeFile(newFilePath, buffer), unlink(filePath)]);
          filePath = newFilePath;
        }
      }

      if (
        fileExtension !== fileExtensions.ZIP &&
        fileExtension !== fileExtensions.GZIP &&
        fileExtension !== fileExtensions.GZ
      ) {
        // eslint-disable-next-line no-continue
        continue;
      }

      const folderName = basename(file.name, fileExtension);

      if (
        fileExtension === fileExtensions.GZIP ||
        fileExtension === fileExtensions.GZ
      ) {
        // If target directory is not present, create it
        // eslint-disable-next-line no-await-in-loop
        await extractGZip(filePath, join(dirPath, folderName));
      } else if (fileExtension === fileExtensions.ZIP) {
        // eslint-disable-next-line no-await-in-loop
        await extractZip(filePath, join(dirPath, folderName));
      }
      // eslint-disable-next-line no-await-in-loop
      await unlink(filePath);
      // eslint-disable-next-line no-await-in-loop
      await unzipRecursively(join(dirPath, folderName));
    }
  } catch (error) {
    server.log.error(
      `[unzipRecursively] Failed to unzip file recursively ${error}`
    );
    throw error;
  }
}

async function extractGZip(source, target) {
  const fileBuffer = await readFile(source);
  const gunzip = gunzipSync(fileBuffer);
  await writeFile(target, gunzip);
}

async function extractZip(source, target) {
  await extract(source, { dir: target });
}

module.exports = {
  createArchive,
  unzipRecursively,
  extractGZip,
  extractZip,
};
