const { Cluster, Redis } = require('ioredis');

const { config } = require('../env');
const logger = require('./logger').mainLogger();

const redisConfig = config.redis || {};
const isRedisEnabled = redisConfig.isEnabled;

const mockedRedisMethods = {
  getValueOfKeyFromRedis: key => {
    if (key === 'sgn:{unit-test}:2ad3300e6e23808665806aa2de8d79b4') return null;
    return key.includes('sgn:{unit-test}')
      ? '06bec86f94ed09e3ad2908bad63b915091dd75de9df72aaad3f82a11'
      : null;
  },
  saveKeyValueWithTTL: () => null,
  removeKey: () => null,
};

function disabledRedisOps() {
  logger.warn('Redis is disabled, performing mock');
}

class RedisClient {
  constructor() {
    if (!RedisClient.instance) {
      this.connection = null;
      this.isConnected = false;
      RedisClient.instance = this;
    }
  }

  static getInstance() {
    if (this.instance) {
      return this.instance;
    }

    this.instance = new this();
    return this.instance;
  }

  async connect() {
    if (this.isConnected) {
      return this.connection;
    }

    if (process.env.ENVIRONMENT === 'local') {
      this.connection = new Redis({
        host: 'localhost',
        port: 6379,
        password: '',
        db: 0,
      });
    } else {
      this.connection = new Cluster(redisConfig.hostInfos, {
        scaleReads: 'slave',
        dnsLookup: (address, callback) => callback(null, address),
        slotsRefreshTimeout: redisConfig.slotsRefreshTimeout || 5000,
        slotsRefreshInterval: redisConfig.slotsRefreshInterval || 60000,
        clusterRetryStrategy: times => {
          if (times > 20) return null; // do not retry infinitely
          const ms = Math.min(100 * times, redisConfig.maxDelayMs || 2000);
          logger.warn(`[Redis] Cluster retry #${times}: Will wait ${ms} ms`);
          return ms;
        },
        // enableOfflineQueue: false, // fail fast if not ready
        redisOptions: {
          tls: {},
          username: redisConfig.username,
          password: redisConfig.password || '',
          connectTimeout: redisConfig.connectTimeout || 10000,
          lazyConnect: true,
        },
      });
    }

    this.connection.on('ready', () => {
      this.isConnected = true;
      logger.info('[Redis] is ready!');
    });

    this.connection.on('error', () => {
      /* Supress ioredis error logging */
    });

    return this.connection;
  }

  async disconnect() {
    this.connection.disconnect();
  }

  async redisInit() {
    if (!this.isConnected) {
      await this.connect();
    }
  }

  async getValue(key) {
    if (!isRedisEnabled) {
      disabledRedisOps();
      return mockedRedisMethods.getValueOfKeyFromRedis(key);
    }
    await this.redisInit();
    return this.connection.get(key);
  }

  async saveKeyValueWithTTL(key, value, EX) {
    if (!isRedisEnabled) {
      disabledRedisOps();
      return mockedRedisMethods.saveKeyValueWithTTL;
    }
    await this.redisInit();
    return this.connection.set(key, value, 'EX', EX);
  }

  async removeKey(key) {
    if (!isRedisEnabled) {
      disabledRedisOps();
      return mockedRedisMethods.removeKey(key);
    }
    await this.redisInit();
    return this.connection.del(key);
  }
}

if (isRedisEnabled) {
  logger.info(`[Redis] hosts, ${JSON.stringify(config.redis.hostInfos)}`);
}

exports.default = RedisClient;
