const errors = require('restify-errors');

const badrequestError = new errors.BadRequestError();
const unauthorizedError = new errors.UnauthorizedError();
const forbiddenError = new errors.ForbiddenError();
const notFoundError = new errors.NotFoundError();
const methodNotAllowedError = new errors.MethodNotAllowedError();
const notAcceptableError = new errors.NotAcceptableError();
const conflictError = new errors.ConflictError();
const internalServerError = new errors.InternalServerError();
const constants = {
  userStatus: {
    PENDING: 0,
    ACTIVE: 1,
    IN_ACTIVE: 2,
  },
  // To group user roles in an endpoint
  baseRoleGroup: {
    ALL: [
      'COMPANY_ADMIN',
      'ANALYST',
      'POWER_USER',
      'SPECIALIST',
      'USER',
      'PLAYLIST_PROVIDER',
    ],
  },
  emailTokenType: {
    REGISTRATION_INVITE: 'registration-invite',
    VERIFY_EMAIL: 'verify-email',
    FORGOT_PASSWORD: 'forgot-password',
  },
  assetType: {
    IMAGE: 'IMAGE',
    VIDEO: 'VIDEO',
    FONT: 'FONT',
  },
  assetFileExtensions: {
    IMAGE: ['.jpg', '.png', '.gif'],
    VIDEO: ['.webm'],
    FONT: ['.otf', '.ttf'],
  },
  assetMimeTypes: {
    IMAGE: ['image/jpeg', 'image/png', 'image/gif'],
    VIDEO: ['video/webm'],
    FONT: ['font/ttf', 'font/otf'],
  },
  assetStatus: {
    NEW: 'NEW',
    PROCESSED: 'PROCESSED',
    ARCHIVED: 'ARCHIVED',
    ERROR: 'ERROR',
  },
  promptSetStatus: {
    PUBLISHING: 'PUBLISHING',
    PUBLISHED: 'PUBLISHED',
    PREVIEW: 'PREVIEW',
    PUBLISHING_FAILED: 'PUBLISHING_FAILED',
    PREVIEW_GENERATION_FAILED: 'PREVIEW_GENERATION_FAILED',
    DRAFT: 'DRAFT',
  },
  transactionState: {
    FUELING: 'fueling',
    IDLE: 'idle',
  },
  assetReport: {
    NOT_APPLICABLE: 'Not applicable',
    NA: 'N/A',
  },
  devices: {
    VERSION: '777',
    STATUS: {
      INACTIVE: 0,
      OPERATIONAL: 1,
      OUT_OF_SERVICE: 2,
      UNKNOWN: 3,
    },
    PRESENCE: {
      PRESENT: 'PRESENT',
      OUT_OF_INSTANCE: 'OUT_OF_INSTANCE',
    },
  },
  sites: {
    STATUS: {
      INACTIVE: 0,
      NORMAL: 1,
      WARNING: 2,
      UNKNOWN: 3,
      CRITICAL: 4,
    },
    ALLOWEDEXTERNALREFERENCETYPES: {
      GVR: 'GVR',
      'MEDIA PROVIDER': 'MEDIA PROVIDER',
      SALESFORCE: 'SALESFORCE',
    },
  },
  offlinePackage: {
    TYPE: {
      SOFTWARE: 'software',
      RKI: 'RKI',
    },
    STATUS: {
      DONE: 'DONE',
      IN_PROGRESS: 'IN_PROGRESS',
    },
    NOTIFICATION_TYPE: 'offlinePackage',
  },
  packageSigning: {
    G6_PREVIEW_KEY: 'E13A84',
    G7_PREVIEW_KEY: '23EE23',
  },
  job: {
    STATUS: {
      NEW: 0,
      ACCEPTED: 1,
      IN_PROGRESS: 2,
      COMPLETED: 3,
      FAILED: 4,
      CANCELLED: 5,
    },
    DONE_STATUS: [3, 4, 5],
    destination: {
      system: 'invenco.system',
    },
  },
  roles: {
    BANK_USER: 'BANK_USER',
    CONFIG_MGMT_PUBLISH: 'CONFIG_MGMT_PUBLISH',
    CONFIG_MGMT_ASSIGN: 'CONFIG_MGMT_ASSIGN',
    CONFIG_MGMT_DEPLOY: 'CONFIG_MGMT_DEPLOY',
    MEDIA_APPROVER: 'MEDIA_APPROVER',
    MEDIA_DEPLOYER: 'MEDIA_DEPLOYER',
    SUPER_ADMIN: 'SUPER_ADMIN',
    BRIDGE_APP: 'BRIDGE_APP',
    DEVICE: 'DEVICE',
    COMPANY_ADMIN: 'COMPANY_ADMIN',
    POWER_USER: 'POWER_USER',
    ICS_SYSTEM: 'ICS_SYSTEM',
    ANALYST: 'ANALYST',
    SPECIALIST: 'SPECIALIST',
    USER: 'USER',
    RKI: 'RKI',
    TAMPER_CLEAR: 'TAMPER_CLEAR',
    MEDIA_DESIGNER: 'MEDIA_DESIGNER',
  },
  httpCode: {
    OK: {
      STATUS_CODE: 200,
    },
    ACCEPTED: {
      STATUS_CODE: 202,
    },
    NO_CONTENT: {
      STATUS_CODE: 204,
    },
    PARTIAL_CONTENT: {
      STATUS_CODE: 206,
    },
    BAD_REQUEST: {
      STATUS_CODE: badrequestError.statusCode,
      CODE: badrequestError.code,
      NAME: badrequestError.name,
    },
    UNAUTHORIZED: {
      STATUS_CODE: unauthorizedError.statusCode,
      CODE: unauthorizedError.code,
      NAME: unauthorizedError.name,
    },
    FORBIDDEN: {
      STATUS_CODE: forbiddenError.statusCode,
      CODE: forbiddenError.code,
      NAME: forbiddenError.name,
    },
    NOT_FOUND: {
      STATUS_CODE: notFoundError.statusCode,
      CODE: notFoundError.code,
      NAME: notFoundError.name,
    },
    METHOD_NOT_ALLOWED: {
      STATUS_CODE: methodNotAllowedError.statusCode,
      CODE: methodNotAllowedError.code,
      NAME: methodNotAllowedError.name,
    },
    NOT_ACCEPTABLE: {
      STATUS_CODE: notAcceptableError.statusCode,
      CODE: notAcceptableError.code,
      NAME: notAcceptableError.name,
    },
    CONFLICT_ERROR: {
      STATUS_CODE: conflictError.statusCode,
      CODE: conflictError.code,
      NAME: conflictError.name,
    },
    INTERNAL_SERVER_ERROR: {
      STATUS_CODE: internalServerError.statusCode,
      CODE: internalServerError.code,
      NAME: internalServerError.name,
    },
  },
  pathSeperator: '/',
  dbTransaction: {
    BEGIN: 'BEGIN',
    COMMIT: 'COMMIT',
    ROLLBACK: 'ROLLBACK',
  },
  crypto: {
    SHA256: 'sha256',
    HEX: 'hex',
  },
  s3Error: {
    NO_SUCH_KEY: {
      CODE: 'NoSuchKey',
      MESSAGE: 'The specified S3 key does not exist',
    },
    NO_SUCH_BUCKET: {
      CODE: 'NoSuchBucket',
      MESSAGE: 'The specified S3 bucket does not exist',
    },
  },
  httpHeaders: {
    CONTENT_DISPOSITION: {
      CODE: 'Content-Disposition',
      KEY: 'content-disposition',
    },
    FILE_NAME: {
      CODE: 'File-Name',
      KEY: 'file-name',
    },
    CONTENT_LENGTH: {
      CODE: 'Content-Length',
      KEY: 'content-length',
    },
    CONTENT_TYPE: {
      CODE: 'Content-Type',
      KEY: 'content-type',
    },
    LAST_MODIFIED: {
      CODE: 'Last-Modified',
      KEY: 'last-modified',
    },
  },
  deviceHealth: {
    INACTIVE: {
      CODE: 0,
      KEY: 'INACTIVE',
    },
    OPERATIONAL: {
      CODE: 1,
      KEY: 'OPERATIONAL',
    },
    OUT_OF_SERVICE: {
      CODE: 2,
      KEY: 'OUT_OF_SERVICE',
    },
    UNKNOWN: {
      CODE: 3,
      KEY: 'UNKNOWN',
    },
  },
  positiveInteger: {
    MIN_VALUE: 1,
    MAX_VALUE: **********,
  },
  loginFailedTypes: {
    NONE: 0,
    WRONG_EMAIL: 1,
    USER_INACTIVE: 2,
    WRONG_PASSWORD: 3,
    WRONG_MFA: 4,
    CAPTCHA_FAILED: 5,
    MFA_NOT_PROVIDED: 6,
    EMAIL_NOT_VERIFIED: 7,
    USER_LOCKED: 8,
    USER_LOCKED_RESET: 9,
    PASSWORD_EXPIRED: 10,
  },
  passwordRegex: '^(?=.*?[a-zA-z])(?=.*?[0-9])(?!.* ).{12,}$',
  signingKeyTypes: {
    VENDOR: 'vendor',
    SPONSOR: 'sponsor',
  },
  epsAppKeyMetricsDefault: [
    {
      appName: 'infx-eps',
      state: 'invenco.system.g7opt.vendor-igl-infx-eps-status',
    },
    {
      appName: 'e1-100-eps',
      state: 'invenco.system.g7opt.vendor-igl-eps-status',
    },
    {
      appName: 'e1-100-eps',
      state: 'invenco.system.g7opt.vendor-igl-e1-100-eps-status',
    },
    {
      appName: 'system',
      state: 'invenco.icsagent.metrics.active-config',
    },
    {
      appName: 'system',
      state: 'invenco.system.metrics.active-config',
    },
    {
      appName: 'infx-bridge-service',
      state: 'invenco.system.g7opt.vendor-igl-fep-bridge-svc-status',
    },
  ],
  deviceApplicationFilter: 'device_application_filter',
  SHOW_APP_VERSIONS: 'SHOW_APP_VERSIONS',
  siteDetails: 'site_details',
  enabledShowAppVersions: 'enabled_show_app_versions',
  DEVICE_APPLCIATION_FILTER_CACHE_KEY: 'deviceApplicationFilterCacheKey',
  COMPANY_ENABLED_APPS_CACHE_KEY: 'companyEnabledAppsCacheKey',
  EXTEND_PAYMENT_DASHBOARD_FEATURE_FLAG: 'EXTEND_PAYMENT_DASHBOARD',
  appKeyMetricsSeparator: '|',
  paymentDashboardFilterSeparator: '|',
  paymentDashboardDefaultPageSize: 5,
  paymentAppConfigKey: 'payment_dashboard_config_schema',
  appKeyMetricsFilter: 'payment_dashboard_application_filter',
  appActionsVisibility: 'payment_dashboard_application_actions_filter',
  siteAssetsDottedStrings: [
    'igl.infx-eps.site-cfg.asset-data',
    'invenco.infx-eps.site-cfg.asset-data',
  ],
  siteEvents: {
    NEW_SITE: 'NEW_SITE',
  },
  ACTIONS: {
    CREATE: 'create',
    UPDATE: 'update',
    DELETE: 'delete',
  },
  eventBusSources: {
    PUT_SITE: 'put-site',
    DEPLOYMENT_REPORT: 'deployment-report',
    FPS_UPDATE_FUEL_PRICES: 'fps-update-fuel-prices',
    SITE_REPLICATION: 'site-replication',
    FPS_UPDATE_JOB_STATUS: 'fps-update-job-status',
    SITE_TAGS_REPLICATION: 'site-tags-replication',
    FPS_UPDATE_DEVICE_SITE: 'fps-process-device-change',
  },
  eventStreamConfig: {
    pubId: 'apiNode',
  },
  devicesThatNeedSignatureFingerprints: ['G6-400'],
  devicesThatNeedOverridePackageVersionToZero: ['G6-400'],
  zeroPrompsetPackageVersion: '00.00.0000',
  noFingerprintErrorMessage:
    'We could not find a fingerprint for the current device, a fingerprint is required for this device type.',
  scheduleDeploymentInterval: 5,

  // Dual display
  AUX_PROMPT_NAME_PREFIX: 'aux-',
  SCREEN_OPTION: {
    MAIN: 'main',
    AUX: 'aux',
  },
  SUPPORTED_SCREEN_OPTIONS: ['main', 'aux'],

  // NOTE: USE THIS CONSTANT KEY ONLY FOR PROMPTS RELATED VALUES
  PROMPT_CONSTANTS: {
    DEVICE_TYPES: ['G6-500'],
    SCREEN_OPTION: {
      MAIN: 'main',
      AUX: 'aux',
    },
    PROMPT_TYPE: {
      STANDARD: 'Standard',
      PIN: 'PIN ENtry',
      DATA: 'Data Entry',
    },
    PROMPT_TYPES_LIST: ['Standard', 'PIN Entry', 'Data Entry'],
  },
  deploymentType: {
    MAINTENANCE_WINDOW: 'maintenance-window',
    IMMEDIATE: 'immediate',
    SCHEDULE: 'schedule',
  },
  DAY_IN_SECONDS: 24 * 60 * 60,
  isUnitTest: process.env.ENVIRONMENT === 'unit-test',
  fileExtensions: {
    ZIP: '.zip',
    GZIP: '.gzip',
    GZ: '.gz',
  },
  fileUploadRequestedFrom: {
    DEVICE_PAGE: 'DEVICE_PAGE',
    PAYMENT_DASHBOARD: 'PAYMENT_DASHBOARD',
  },
  featureFlags: {
    TIMEZONE_AUTOMATION: 'TIMEZONE_AUTOMATION',
    SEQ3_SEQ5_CERT_HANDLER: 'SEQ3_SEQ5_CERT_HANDLER',
  },
  user: {
    TYPE: {
      SYSTEM: 'SYSTEM',
    },
  },
  certificateBundleUpload: {
    TYPE: 'software',
    SOFTWARE_TYPE: {
      PATCH: 1,
    },
    UPLOAD_STATUS: {
      FAILED: 'Failed',
      SUCCESS: 'Success',
    },
  },
  certificateBundleRollout: {
    INSTALL_WINDOWS: 3600000,
    INSTALL_FREQUENCY: 900000,
    DEPLOYMENT_POLICY: 1,
  },
  DEVICE_FILES_JOB_PARAM_SCHEMAS: 'ics-api-nodejs:dfjps',
};

module.exports = constants;
