const pino = require('pino');
const mainEnv = require('../env');

let logger;

const environmentName = process.env.ENVIRONMENT;

const defaultLogProperties = {
  'tags.Env': environmentName,
  'tags.Name': `ics-api-nodejs-${environmentName}`,
  'tags.System': 'ics-api-nodejs',
  'tags.Service': 'Core',
  'tags.Sub-Component': 'ICS API',
};

const loggerOpts = (config, options) => {
  const opts = {};

  if (config.enableNewRelic) {
    // eslint-disable-next-line global-require
    const newrelic = require('newrelic');
    opts.formatters = {
      level(label) {
        return { level: label };
      },
    };
    opts.mixin = () => {
      newrelic.addCustomAttributes(defaultLogProperties);
      const mixin = {};
      return mixin;
    };
  }

  return Object.assign(options, opts);
};

const createMainLogger = env => {
  const opts = {
    name: `Server ${env.environment}`,
    level: env.config.logLevel,
    timestamp: pino.stdTimeFunctions.isoTime,
  };

  if (!logger) {
    logger = pino(loggerOpts(env.config, opts)).child(defaultLogProperties);
  }

  return logger;
};

const mainLogger = () => {
  if (!logger) {
    logger = createMainLogger(mainEnv);
  }
  return logger;
};

const createLogger = (options, dest) =>
  pino(options, dest).child(defaultLogProperties);

module.exports = {
  createMainLogger,
  mainLogger,
  createLogger,
  loggerOpts,
};
