/* eslint-disable no-underscore-dangle */
// eslint-disable-next-line import/no-extraneous-dependencies
const assert = require('assert-plus');

const { safeToString } = require('./utils');

/**
 * Report API audit db logger.
 *
 * @param {Object} opts
 */
function createDBLogger(opts) {
  assert.object(opts.db, 'options.db');

  const { db } = opts;
  const syncInterval = opts.syncInterval || 1000;
  const maxLines = opts.maxLines || 100;
  const { baseUrl } = opts;

  // Cache of entries we are yet to sync
  const unsynced = [];
  async function syncLogsToDB() {
    // Short-circuit if there is nothing to send
    if (unsynced.length === 0) {
      return;
    }

    const logLines = unsynced.splice(0, maxLines);

    let idx = 1;
    const queryParams = [];
    let values = '';
    // eslint-disable-next-line no-restricted-syntax
    for (const logLine of logLines) {
      const time = logLine.time ? new Date(logLine.time) : new Date();

      queryParams.push(
        logLine.res.resourceOwner ? logLine.res.resourceOwner : null
      );
      values = `${values} ( $${idx}`;
      idx += 1;

      queryParams.push(time.toISOString());
      values = `${values}, $${idx}`;
      idx += 1;

      queryParams.push(`${logLine.req.method} ${logLine.req.forwardedUrl}`);
      values = `${values}, $${idx}`;
      idx += 1;

      if (logLine.userId) {
        queryParams.push(logLine.userId);
      } else {
        queryParams.push(null);
      }
      values = `${values}, $${idx}`;
      idx += 1;

      if (logLine.companyId) {
        queryParams.push(logLine.companyId);
      } else {
        queryParams.push(null);
      }
      values = `${values}, $${idx}`;
      idx += 1;

      queryParams.push(safeToString(logLine.req.body));
      values = `${values}, $${idx}`;
      idx += 1;

      queryParams.push(safeToString(logLine.res.body));
      values = `${values}, $${idx}`;
      idx += 1;

      queryParams.push(logLine.res.statusCode);
      values = `${values}, $${idx}`;
      idx += 1;

      queryParams.push(logLine.req_id);
      values = `${values}, $${idx}`;
      idx += 1;

      queryParams.push(logLine.remoteAddress);
      values = `${values}, $${idx}`;
      idx += 1;

      if (logLine.impersonatorId) {
        queryParams.push(logLine.impersonatorId);
      } else {
        queryParams.push(null);
      }
      values = `${values}, $${idx}),`;
      idx += 1;
    }

    values = values.substr(0, values.length - 1);

    await db.execute(
      `
            INSERT INTO report_api_audit 
                (owner, timestamp, route, user_id, company_id, request, response, status_code, request_id, remote_address, impersonator) 
            VALUES ${values}
        `,
      queryParams
    );
  }

  function sync(interval) {
    // Avoid using setInterval as they could overlap with asynchronous processing
    setTimeout(async () => {
      try {
        await syncLogsToDB();
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error(err); // use console as we are in a logger
      }
      sync(interval);
    }, interval);
  }

  // Start sync
  sync(syncInterval);

  return record => {
    if (
      record.name !== 'audit' ||
      record.req.method === 'GET' ||
      record.req.method === 'OPTIONS'
    ) {
      return;
    }

    if (
      record.req.user &&
      record.req.user.roles &&
      record.req.user.roles.includes('ICS_SYSTEM')
    ) {
      return;
    }

    let base = '';
    if (baseUrl) {
      base = `/${baseUrl}`;
    }

    const blacklistedServices = [
      { method: 'POST', url: new RegExp(`^${base}/devices/authenticate$`) },
      { method: 'POST', url: new RegExp(`^${base}/devices/register`) },
      { method: 'POST', url: new RegExp(`^${base}/web-activity`) },
      { method: 'POST', url: new RegExp(`^${base}/acceptable/.+$`) },
      { method: 'PUT', url: new RegExp(`^${base}/devices/([0-9]+)/data$`) },
      { method: 'POST', url: new RegExp(`^${base}/jobs/expire$`) },
      { method: 'POST', url: new RegExp(`^${base}/devices/sync$`) },
      { method: 'POST', url: new RegExp(`^${base}/s3logger/sync$`) },
      { method: 'POST', url: new RegExp(`^${base}/reports/`) },
      { method: 'PATCH', url: new RegExp(`^${base}/jobs/.+$`) },
      { method: 'POST', url: new RegExp(`^${base}/admin/releases$`) },
      { method: 'PUT', url: new RegExp(`^${base}/admin/releases$`) },
      { method: 'DELETE', url: new RegExp(`^${base}/admin/releases$`) },
      { method: 'POST', url: new RegExp(`^${base}/software/get-files$`) },
      { method: 'POST', url: new RegExp(`^${base}/software/delete-files$`) },
      { method: 'POST', url: new RegExp(`^${base}/rollouts`) }, //  Added as part of  prod defect ICS-16550, will blacklist all post API starting with /rollouts and not any nested ones
    ];

    const blacklistedService = blacklistedServices.find(
      service =>
        service.method === record.req.method && service.url.test(record.req.url)
    );

    if (blacklistedService) {
      return;
    }

    if (
      record.req.user &&
      record.req.user.roles &&
      record.req.user.roles.includes('DEVICE')
    ) {
      return;
    }

    unsynced.push(record);
  };
}

module.exports = { createDBLogger };
