/* eslint-disable no-bitwise */

// ---------------------------------------------------------------------
//
// Loosely based on:
//
// QR Code Generator for JavaScript
//
// Copyright (c) 2009 <PERSON><PERSON><PERSON>
//
// URL: http://www.d-project.com/
//
// Licensed under the MIT license:
//  http://www.opensource.org/licenses/mit-license.php
//
// The word 'QR Code' is registered trademark of
// DENSO WAVE INCORPORATED
//  http://www.denso-wave.com/qrcode/faqpatent-e.html
//
// ---------------------------------------------------------------------

module.exports.qrcode = (() => {
  // ---------------------------------------------------------------------
  // QRErrorCorrectLevel
  // ---------------------------------------------------------------------

  const QRErrorCorrectLevel = {
    Low: 1,
    Medium: 0,
    Quality: 3,
    High: 2,
  };

  // ---------------------------------------------------------------------
  // QRMaskPattern
  // ---------------------------------------------------------------------

  const QRMaskPattern = {
    PATTERN000: 0,
    PATTERN001: 1,
    PATTERN010: 2,
    PATTERN011: 3,
    PATTERN100: 4,
    PATTERN101: 5,
    PATTERN110: 6,
    PATTERN111: 7,
  };

  // ---------------------------------------------------------------------
  // QRMode
  // ---------------------------------------------------------------------

  const QRMode = {
    MODE_NUMBER: 1 << 0,
    MODE_ALPHA_NUM: 1 << 1,
    MODE_8BIT_BYTE: 1 << 2,
    MODE_KANJI: 1 << 3,
  };

  // ---------------------------------------------------------------------
  // QRMath
  // ---------------------------------------------------------------------

  const QRMath = (function QRMath() {
    const expTable = new Array(256);
    const logTable = new Array(256);
    for (let index1 = 0; index1 < 8; index1++) {
      expTable[index1] = 1 << index1;
    }
    for (let index2 = 8; index2 < 256; index2++) {
      expTable[index2] =
        expTable[index2 - 4] ^
        expTable[index2 - 5] ^
        expTable[index2 - 6] ^
        expTable[index2 - 8];
    }
    for (let index3 = 0; index3 < 255; index3++) {
      logTable[expTable[index3]] = index3;
    }
    const varThis = {};
    varThis.gLog = value => {
      if (value < 1) {
        throw new Error(`gLog(${value})`);
      }
      return logTable[value];
    };
    varThis.gExp = value => {
      while (value < 0) {
        value += 255; // eslint-disable-line no-param-reassign
      }
      while (value >= 256) {
        value -= 255; // eslint-disable-line no-param-reassign
      }
      return expTable[value];
    };
    return varThis;
  })();

  // ---------------------------------------------------------------------
  // QRUtil
  // ---------------------------------------------------------------------

  const QRUtil = (function QRUtil() {
    const patternPositionTable = [
      [],
      [6, 18],
      [6, 22],
      [6, 26],
      [6, 30],
      [6, 34],
      [6, 22, 38],
      [6, 24, 42],
      [6, 26, 46],
      [6, 28, 50],
      [6, 30, 54],
      [6, 32, 58],
      [6, 34, 62],
      [6, 26, 46, 66],
      [6, 26, 48, 70],
      [6, 26, 50, 74],
      [6, 30, 54, 78],
      [6, 30, 56, 82],
      [6, 30, 58, 86],
      [6, 34, 62, 90],
      [6, 28, 50, 72, 94],
      [6, 26, 50, 74, 98],
      [6, 30, 54, 78, 102],
      [6, 28, 54, 80, 106],
      [6, 32, 58, 84, 110],
      [6, 30, 58, 86, 114],
      [6, 34, 62, 90, 118],
      [6, 26, 50, 74, 98, 122],
      [6, 30, 54, 78, 102, 126],
      [6, 26, 52, 78, 104, 130],
      [6, 30, 56, 82, 108, 134],
      [6, 34, 60, 86, 112, 138],
      [6, 30, 58, 86, 114, 142],
      [6, 34, 62, 90, 118, 146],
      [6, 30, 54, 78, 102, 126, 150],
      [6, 24, 50, 76, 102, 128, 154],
      [6, 28, 54, 80, 106, 132, 158],
      [6, 32, 58, 84, 110, 136, 162],
      [6, 26, 54, 82, 110, 138, 166],
      [6, 30, 58, 86, 114, 142, 170],
    ];
    const G15 =
      (1 << 10) |
      (1 << 8) |
      (1 << 5) |
      (1 << 4) |
      (1 << 2) |
      (1 << 1) |
      (1 << 0);
    const G18 =
      (1 << 12) |
      (1 << 11) |
      (1 << 10) |
      (1 << 9) |
      (1 << 8) |
      (1 << 5) |
      (1 << 2) |
      (1 << 0);
    const G15MASK = (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1);

    const varThis = {};

    function getBCHDigit(data) {
      let digit = 0;
      while (data !== 0) {
        digit += 1;
        data >>>= 1; // eslint-disable-line no-param-reassign
      }
      return digit;
    }

    varThis.getBCHTypeInfo = data => {
      let datum = data << 10;
      while (getBCHDigit(datum) - getBCHDigit(G15) >= 0) {
        datum ^= G15 << (getBCHDigit(datum) - getBCHDigit(G15));
      }
      return ((data << 10) | datum) ^ G15MASK;
    };

    varThis.getBCHTypeNumber = data => {
      let datum = data << 12;
      while (getBCHDigit(datum) - getBCHDigit(G18) >= 0) {
        datum ^= G18 << (getBCHDigit(datum) - getBCHDigit(G18));
      }
      return (data << 12) | datum;
    };

    varThis.getPatternPosition = typeNumber =>
      patternPositionTable[typeNumber - 1];

    varThis.getMaskFunction = maskPattern => {
      switch (maskPattern) {
        case QRMaskPattern.PATTERN000:
          return (first, second) => (first + second) % 2 === 0;
        case QRMaskPattern.PATTERN001:
          return first => first % 2 === 0;
        case QRMaskPattern.PATTERN010:
          return (first, second) => second % 3 === 0;
        case QRMaskPattern.PATTERN011:
          return (first, second) => (first + second) % 3 === 0;
        case QRMaskPattern.PATTERN100:
          return (first, second) =>
            (Math.floor(first / 2) + Math.floor(second / 3)) % 2 === 0;
        case QRMaskPattern.PATTERN101:
          return (first, second) =>
            ((first * second) % 2) + ((first * second) % 3) === 0;
        case QRMaskPattern.PATTERN110:
          return (first, second) =>
            (((first * second) % 2) + ((first * second) % 3)) % 2 === 0;
        case QRMaskPattern.PATTERN111:
          return (first, second) =>
            (((first * second) % 3) + ((first + second) % 2)) % 2 === 0;

        default:
          throw new Error(`bad maskPattern:${maskPattern}`);
      }
    };

    varThis.getErrorCorrectPolynomial = errorCorrectLength => {
      let poly = qrPolynomial([1], 0); // eslint-disable-line no-use-before-define
      for (let index = 0; index < errorCorrectLength; index++) {
        poly = poly.multiply(qrPolynomial([1, QRMath.gExp(index)], 0)); // eslint-disable-line no-use-before-define
      }
      return poly;
    };

    varThis.getLengthInBits = (mode, type) => {
      if (type >= 1 && type < 10) {
        // eslint-disable-line yoda
        switch (mode) {
          case QRMode.MODE_NUMBER:
            return 10;
          case QRMode.MODE_ALPHA_NUM:
            return 9;
          case QRMode.MODE_8BIT_BYTE:
            return 8;
          case QRMode.MODE_KANJI:
            return 8;
          default:
            throw new Error(`mode:${mode}`);
        }
      } else if (type < 27) {
        switch (mode) {
          case QRMode.MODE_NUMBER:
            return 12;
          case QRMode.MODE_ALPHA_NUM:
            return 11;
          case QRMode.MODE_8BIT_BYTE:
            return 16;
          case QRMode.MODE_KANJI:
            return 10;
          default:
            throw new Error(`mode:${mode}`);
        }
      } else if (type < 41) {
        switch (mode) {
          case QRMode.MODE_NUMBER:
            return 14;
          case QRMode.MODE_ALPHA_NUM:
            return 13;
          case QRMode.MODE_8BIT_BYTE:
            return 16;
          case QRMode.MODE_KANJI:
            return 12;
          default:
            throw new Error(`mode:${mode}`);
        }
      } else {
        throw new Error(`type:${type}`);
      }
    };

    varThis.getLostPoint = runQrCode => {
      const moduleCount = runQrCode.getModuleCount();
      let lostPoint = 0;
      // LEVEL1
      for (let row1 = 0; row1 < moduleCount; row1++) {
        for (let col1 = 0; col1 < moduleCount; col1++) {
          let sameCount = 0;
          const dark = runQrCode.isDark(row1, col1);
          for (let row2 = -1; row2 <= 1; row2++) {
            if (!(row1 + row2 < 0 || moduleCount <= row1 + row2)) {
              for (let col2 = -1; col2 <= 1; col2++) {
                if (
                  !(col1 + col2 < 0 || moduleCount <= col1 + col2) && // eslint-disable-line max-depth
                  !(row2 === 0 && col2 === 0) &&
                  dark === runQrCode.isDark(row1 + row2, col1 + col2)
                ) {
                  sameCount += 1;
                }
              }
            }
          }
          if (sameCount > 5) {
            lostPoint += 3 + sameCount - 5;
          }
        }
      }
      // LEVEL2
      for (let row3 = 0; row3 < moduleCount - 1; row3++) {
        for (let col3 = 0; col3 < moduleCount - 1; col3++) {
          let count = 0;
          if (runQrCode.isDark(row3, col3)) {
            count += 1;
          }
          if (runQrCode.isDark(row3 + 1, col3)) {
            count += 1;
          }
          if (runQrCode.isDark(row3, col3 + 1)) {
            count += 1;
          }
          if (runQrCode.isDark(row3 + 1, col3 + 1)) {
            count += 1;
          }
          if (count === 0 || count === 4) {
            lostPoint += 3;
          }
        }
      }
      // LEVEL3
      for (let row4 = 0; row4 < moduleCount; row4++) {
        for (let col4 = 0; col4 < moduleCount - 6; col4++) {
          if (
            runQrCode.isDark(row4, col4) &&
            !runQrCode.isDark(row4, col4 + 1) &&
            runQrCode.isDark(row4, col4 + 2) &&
            runQrCode.isDark(row4, col4 + 3) &&
            runQrCode.isDark(row4, col4 + 4) &&
            !runQrCode.isDark(row4, col4 + 5) &&
            runQrCode.isDark(row4, col4 + 6)
          ) {
            lostPoint += 40;
          }
        }
      }
      for (let col5 = 0; col5 < moduleCount; col5++) {
        for (let row5 = 0; row5 < moduleCount - 6; row5++) {
          if (
            runQrCode.isDark(row5, col5) &&
            !runQrCode.isDark(row5 + 1, col5) &&
            runQrCode.isDark(row5 + 2, col5) &&
            runQrCode.isDark(row5 + 3, col5) &&
            runQrCode.isDark(row5 + 4, col5) &&
            !runQrCode.isDark(row5 + 5, col5) &&
            runQrCode.isDark(row5 + 6, col5)
          ) {
            lostPoint += 40;
          }
        }
      }
      // LEVEL4
      let darkCount = 0;
      for (let col = 0; col < moduleCount; col++) {
        for (let row = 0; row < moduleCount; row++) {
          if (runQrCode.isDark(row, col)) {
            darkCount += 1;
          }
        }
      }
      const ratio =
        Math.abs((100 * darkCount) / moduleCount / moduleCount - 50) / 5;
      lostPoint += ratio * 10;
      return lostPoint;
    };
    return varThis;
  })();

  // ---------------------------------------------------------------------
  // QRRSBlock
  // ---------------------------------------------------------------------

  const QRRSBlock = (function QRRSBlock() {
    const rsBlockTable = [
      // Low
      // Medium
      // Quality
      // High

      // 1
      [1, 26, 19],
      [1, 26, 16],
      [1, 26, 13],
      [1, 26, 9],

      // 2
      [1, 44, 34],
      [1, 44, 28],
      [1, 44, 22],
      [1, 44, 16],

      // 3
      [1, 70, 55],
      [1, 70, 44],
      [2, 35, 17],
      [2, 35, 13],

      // 4
      [1, 100, 80],
      [2, 50, 32],
      [2, 50, 24],
      [4, 25, 9],

      // 5
      [1, 134, 108],
      [2, 67, 43],
      [2, 33, 15, 2, 34, 16],
      [2, 33, 11, 2, 34, 12],

      // 6
      [2, 86, 68],
      [4, 43, 27],
      [4, 43, 19],
      [4, 43, 15],

      // 7
      [2, 98, 78],
      [4, 49, 31],
      [2, 32, 14, 4, 33, 15],
      [4, 39, 13, 1, 40, 14],

      // 8
      [2, 121, 97],
      [2, 60, 38, 2, 61, 39],
      [4, 40, 18, 2, 41, 19],
      [4, 40, 14, 2, 41, 15],

      // 9
      [2, 146, 116],
      [3, 58, 36, 2, 59, 37],
      [4, 36, 16, 4, 37, 17],
      [4, 36, 12, 4, 37, 13],

      // 10
      [2, 86, 68, 2, 87, 69],
      [4, 69, 43, 1, 70, 44],
      [6, 43, 19, 2, 44, 20],
      [6, 43, 15, 2, 44, 16],

      // 11
      [4, 101, 81],
      [1, 80, 50, 4, 81, 51],
      [4, 50, 22, 4, 51, 23],
      [3, 36, 12, 8, 37, 13],

      // 12
      [2, 116, 92, 2, 117, 93],
      [6, 58, 36, 2, 59, 37],
      [4, 46, 20, 6, 47, 21],
      [7, 42, 14, 4, 43, 15],

      // 13
      [4, 133, 107],
      [8, 59, 37, 1, 60, 38],
      [8, 44, 20, 4, 45, 21],
      [12, 33, 11, 4, 34, 12],

      // 14
      [3, 145, 115, 1, 146, 116],
      [4, 64, 40, 5, 65, 41],
      [11, 36, 16, 5, 37, 17],
      [11, 36, 12, 5, 37, 13],

      // 15
      [5, 109, 87, 1, 110, 88],
      [5, 65, 41, 5, 66, 42],
      [5, 54, 24, 7, 55, 25],
      [11, 36, 12, 7, 37, 13],

      // 16
      [5, 122, 98, 1, 123, 99],
      [7, 73, 45, 3, 74, 46],
      [15, 43, 19, 2, 44, 20],
      [3, 45, 15, 13, 46, 16],

      // 17
      [1, 135, 107, 5, 136, 108],
      [10, 74, 46, 1, 75, 47],
      [1, 50, 22, 15, 51, 23],
      [2, 42, 14, 17, 43, 15],

      // 18
      [5, 150, 120, 1, 151, 121],
      [9, 69, 43, 4, 70, 44],
      [17, 50, 22, 1, 51, 23],
      [2, 42, 14, 19, 43, 15],

      // 19
      [3, 141, 113, 4, 142, 114],
      [3, 70, 44, 11, 71, 45],
      [17, 47, 21, 4, 48, 22],
      [9, 39, 13, 16, 40, 14],

      // 20
      [3, 135, 107, 5, 136, 108],
      [3, 67, 41, 13, 68, 42],
      [15, 54, 24, 5, 55, 25],
      [15, 43, 15, 10, 44, 16],

      // 21
      [4, 144, 116, 4, 145, 117],
      [17, 68, 42],
      [17, 50, 22, 6, 51, 23],
      [19, 46, 16, 6, 47, 17],

      // 22
      [2, 139, 111, 7, 140, 112],
      [17, 74, 46],
      [7, 54, 24, 16, 55, 25],
      [34, 37, 13],

      // 23
      [4, 151, 121, 5, 152, 122],
      [4, 75, 47, 14, 76, 48],
      [11, 54, 24, 14, 55, 25],
      [16, 45, 15, 14, 46, 16],

      // 24
      [6, 147, 117, 4, 148, 118],
      [6, 73, 45, 14, 74, 46],
      [11, 54, 24, 16, 55, 25],
      [30, 46, 16, 2, 47, 17],

      // 25
      [8, 132, 106, 4, 133, 107],
      [8, 75, 47, 13, 76, 48],
      [7, 54, 24, 22, 55, 25],
      [22, 45, 15, 13, 46, 16],

      // 26
      [10, 142, 114, 2, 143, 115],
      [19, 74, 46, 4, 75, 47],
      [28, 50, 22, 6, 51, 23],
      [33, 46, 16, 4, 47, 17],

      // 27
      [8, 152, 122, 4, 153, 123],
      [22, 73, 45, 3, 74, 46],
      [8, 53, 23, 26, 54, 24],
      [12, 45, 15, 28, 46, 16],

      // 28
      [3, 147, 117, 10, 148, 118],
      [3, 73, 45, 23, 74, 46],
      [4, 54, 24, 31, 55, 25],
      [11, 45, 15, 31, 46, 16],

      // 29
      [7, 146, 116, 7, 147, 117],
      [21, 73, 45, 7, 74, 46],
      [1, 53, 23, 37, 54, 24],
      [19, 45, 15, 26, 46, 16],

      // 30
      [5, 145, 115, 10, 146, 116],
      [19, 75, 47, 10, 76, 48],
      [15, 54, 24, 25, 55, 25],
      [23, 45, 15, 25, 46, 16],

      // 31
      [13, 145, 115, 3, 146, 116],
      [2, 74, 46, 29, 75, 47],
      [42, 54, 24, 1, 55, 25],
      [23, 45, 15, 28, 46, 16],

      // 32
      [17, 145, 115],
      [10, 74, 46, 23, 75, 47],
      [10, 54, 24, 35, 55, 25],
      [19, 45, 15, 35, 46, 16],

      // 33
      [17, 145, 115, 1, 146, 116],
      [14, 74, 46, 21, 75, 47],
      [29, 54, 24, 19, 55, 25],
      [11, 45, 15, 46, 46, 16],

      // 34
      [13, 145, 115, 6, 146, 116],
      [14, 74, 46, 23, 75, 47],
      [44, 54, 24, 7, 55, 25],
      [59, 46, 16, 1, 47, 17],

      // 35
      [12, 151, 121, 7, 152, 122],
      [12, 75, 47, 26, 76, 48],
      [39, 54, 24, 14, 55, 25],
      [22, 45, 15, 41, 46, 16],

      // 36
      [6, 151, 121, 14, 152, 122],
      [6, 75, 47, 34, 76, 48],
      [46, 54, 24, 10, 55, 25],
      [2, 45, 15, 64, 46, 16],

      // 37
      [17, 152, 122, 4, 153, 123],
      [29, 74, 46, 14, 75, 47],
      [49, 54, 24, 10, 55, 25],
      [24, 45, 15, 46, 46, 16],

      // 38
      [4, 152, 122, 18, 153, 123],
      [13, 74, 46, 32, 75, 47],
      [48, 54, 24, 14, 55, 25],
      [42, 45, 15, 32, 46, 16],

      // 39
      [20, 147, 117, 4, 148, 118],
      [40, 75, 47, 7, 76, 48],
      [43, 54, 24, 22, 55, 25],
      [10, 45, 15, 67, 46, 16],

      // 40
      [19, 148, 118, 6, 149, 119],
      [18, 75, 47, 31, 76, 48],
      [34, 54, 24, 34, 55, 25],
      [20, 45, 15, 61, 46, 16],
    ];

    function qrRSBlock(totalCount, dataCount) {
      const varThis1 = {};
      varThis1.totalCount = totalCount;
      varThis1.dataCount = dataCount;
      return varThis1;
    }

    const varThis = {};

    function getRsBlockTable(typeNumber, errorCorrectLevel) {
      switch (errorCorrectLevel) {
        case QRErrorCorrectLevel.Low:
          return rsBlockTable[(typeNumber - 1) * 4];
        case QRErrorCorrectLevel.Medium:
          return rsBlockTable[(typeNumber - 1) * 4 + 1];
        case QRErrorCorrectLevel.Quality:
          return rsBlockTable[(typeNumber - 1) * 4 + 2];
        case QRErrorCorrectLevel.High:
          return rsBlockTable[(typeNumber - 1) * 4 + 3];
        default:
          throw new Error('invalid errorCorrectLevel');
      }
    }

    varThis.getRSBlocks = (typeNumber, errorCorrectLevel) => {
      const rsBlock = getRsBlockTable(typeNumber, errorCorrectLevel);
      if (typeof rsBlock === 'undefined') {
        // eslint-disable-line eqeqeq
        throw new Error(
          `bad rs block @ typeNumber:${typeNumber}/errorCorrectLevel:${errorCorrectLevel}`
        );
      }
      const length = rsBlock.length / 3;
      const list = [];
      for (let index = 0; index < length; index++) {
        const count = rsBlock[index * 3];
        const totalCount = rsBlock[index * 3 + 1];
        const dataCount = rsBlock[index * 3 + 2];
        for (let index2 = 0; index2 < count; index2++) {
          list.push(qrRSBlock(totalCount, dataCount));
        }
      }
      return list;
    };
    return varThis;
  })();

  // ---------------------------------------------------------------------
  // qrBitBuffer
  // ---------------------------------------------------------------------

  function qrBitBuffer() {
    const varBuffer = [];
    let varLength = 0;
    const varThis = {};
    varThis.getBuffer = () => varBuffer;
    varThis.getAt = index => {
      const bufIndex = Math.floor(index / 8);
      return ((varBuffer[bufIndex] >>> (7 - (index % 8))) & 1) === 1;
    };
    varThis.put = (num, length) => {
      for (let index = 0; index < length; index++) {
        varThis.putBit(((num >>> (length - index - 1)) & 1) === 1);
      }
    };
    varThis.getLengthInBits = () => varLength;
    varThis.putBit = bit => {
      const bufIndex = Math.floor(varLength / 8);
      if (varBuffer.length <= bufIndex) {
        varBuffer.push(0);
      }
      if (bit) {
        // eslint-disable-next-line operator-assignment
        varBuffer[bufIndex] = varBuffer[bufIndex] | (0x80 >>> varLength % 8);
      }
      varLength += 1;
    };
    return varThis;
  }

  // ---------------------------------------------------------------------
  // qr8BitByte
  // ---------------------------------------------------------------------

  function qr8BitByte(data) {
    const varMode = QRMode.MODE_8BIT_BYTE;
    // const varData = data;
    const varBytes = qrcode.stringToBytes(data); // eslint-disable-line no-use-before-define
    const varThis = {};
    varThis.getMode = () => varMode;
    // varThis.getLength = function (buffer) {
    varThis.getLength = () => varBytes.length;
    varThis.write = buffer => {
      for (let index = 0; index < varBytes.length; index++) {
        buffer.put(varBytes[index], 8);
      }
    };
    return varThis;
  }

  // ---------------------------------------------------------------------
  // byteArrayOutputStream
  // ---------------------------------------------------------------------

  function byteArrayOutputStream() {
    const varBytes = [];
    const varThis = {};
    varThis.writeByte = byte => {
      varBytes.push(byte & 0xff);
    };
    varThis.writeShort = index => {
      varThis.writeByte(index);
      varThis.writeByte(index >>> 8);
    };
    varThis.writeBytes = (byte, off, len) => {
      off = off || 0; // eslint-disable-line no-param-reassign
      len = len || byte.length; // eslint-disable-line no-param-reassign
      for (let index = 0; index < len; index++) {
        varThis.writeByte(byte[index + off]);
      }
    };
    varThis.writeString = theString => {
      for (let index = 0; index < theString.length; index++) {
        varThis.writeByte(theString.charCodeAt(index));
      }
    };
    varThis.toByteArray = () => varBytes;
    varThis.toString = () => {
      let theString = '[';
      for (let index = 0; index < varBytes.length; index++) {
        if (index > 0) {
          theString = `${theString},`;
        }
        theString = `${theString}${varBytes[index]}`;
      }
      theString = `${theString}]`;
      return theString;
    };
    return varThis;
  }

  // ---------------------------------------------------------------------
  // gifImage (B/W)
  // ---------------------------------------------------------------------

  function gifImage(width, height) {
    const varWidth = width;
    const varHeight = height;
    const varData = new Array(width * height);

    function lzwTable() {
      const varMap = {};
      let varSize = 0;
      const varThis2 = {};
      varThis2.add = key => {
        if (varThis2.contains(key)) {
          throw new Error(`dup key:${key}`);
        }
        varMap[key] = varSize;
        varSize += 1;
      };
      varThis2.size = () => varSize;
      varThis2.indexOf = key => varMap[key];
      varThis2.contains = key => typeof varMap[key] != 'undefined'; // eslint-disable-line eqeqeq
      return varThis2;
    }

    function bitOutputStream(out) {
      const varOut = out;
      let varBitLength = 0;
      let varBitBuffer = 0;
      const varThis2 = {};
      varThis2.write = (data, length) => {
        if (data >>> length !== 0) {
          throw new Error('length over');
        }
        while (varBitLength + length >= 8) {
          varOut.writeByte(0xff & ((data << varBitLength) | varBitBuffer));
          length -= 8 - varBitLength; // eslint-disable-line no-param-reassign
          data >>>= 8 - varBitLength; // eslint-disable-line no-param-reassign
          varBitBuffer = 0;
          varBitLength = 0;
        }
        // eslint-disable-next-line operator-assignment
        varBitBuffer = (data << varBitLength) | varBitBuffer;
        varBitLength += length;
      };
      varThis2.flush = () => {
        if (varBitLength > 0) {
          varOut.writeByte(varBitBuffer);
        }
      };
      return varThis2;
    }

    function getLZWRaster(lzwMinCodeSize) {
      const clearCode = 1 << lzwMinCodeSize;
      const endCode = (1 << lzwMinCodeSize) + 1;
      let bitLength = lzwMinCodeSize + 1;
      // Setup LZWTable
      const table = lzwTable();
      for (let index = 0; index < clearCode; index++) {
        table.add(String.fromCharCode(index));
      }
      table.add(String.fromCharCode(clearCode));
      table.add(String.fromCharCode(endCode));
      const byteOut = byteArrayOutputStream();
      const bitOut = bitOutputStream(byteOut);
      // clear code
      bitOut.write(clearCode, bitLength);
      let dataIndex = 0;
      let theString1 = String.fromCharCode(varData[dataIndex]);
      dataIndex += 1;
      while (dataIndex < varData.length) {
        const theString2 = String.fromCharCode(varData[dataIndex]);
        dataIndex += 1;
        if (table.contains(theString1 + theString2)) {
          theString1 += theString2;
        } else {
          bitOut.write(table.indexOf(theString1), bitLength);
          if (table.size() < 0xfff) {
            if (table.size() === 1 << bitLength) {
              bitLength += 1;
            }
            table.add(theString1 + theString2);
          }
          theString1 = theString2;
        }
      }
      bitOut.write(table.indexOf(theString1), bitLength);
      // end code
      bitOut.write(endCode, bitLength);
      bitOut.flush();
      return byteOut.toByteArray();
    }

    const varThis = {};
    varThis.setPixel = (xAxis, yAxis, pixel) => {
      varData[yAxis * varWidth + xAxis] = pixel;
    };
    varThis.write = out => {
      // ---------------------------------
      // GIF Signature

      out.writeString('GIF87a');

      // ---------------------------------
      // Screen Descriptor

      out.writeShort(varWidth);
      out.writeShort(varHeight);
      out.writeByte(0x80);
      out.writeByte(0);
      out.writeByte(0);

      // ---------------------------------
      // Global Color Map

      // black
      out.writeByte(0x00);
      out.writeByte(0x00);
      out.writeByte(0x00);

      // white
      out.writeByte(0xff);
      out.writeByte(0xff);
      out.writeByte(0xff);

      // ---------------------------------
      // Image Descriptor

      out.writeString(',');
      out.writeShort(0);
      out.writeShort(0);
      out.writeShort(varWidth);
      out.writeShort(varHeight);
      out.writeByte(0);

      // ---------------------------------
      // Local Color Map

      // ---------------------------------
      // Raster Data

      const lzwMinCodeSize = 2;
      const raster = getLZWRaster(lzwMinCodeSize);

      out.writeByte(lzwMinCodeSize);

      let offset = 0;

      while (raster.length - offset > 255) {
        out.writeByte(255);
        out.writeBytes(raster, offset, 255);
        offset += 255;
      }

      out.writeByte(raster.length - offset);
      out.writeBytes(raster, offset, raster.length - offset);
      out.writeByte(0x00);

      // ---------------------------------
      // GIF Terminator
      out.writeString(';');
    };

    return varThis;
  }

  // ---------------------------------------------------------------------
  // base64EncodeOutputStream
  // ---------------------------------------------------------------------

  function base64EncodeOutputStream() {
    let varBuffer = 0;
    let varBuffLength = 0;
    let varLength = 0;
    let varBase64 = '';

    const varThis = {};

    function encode(value) {
      if (value < 0) {
        // error.
      } else if (value < 26) {
        return 0x41 + value;
      } else if (value < 52) {
        return 0x61 + (value - 26);
      } else if (value < 62) {
        return 0x30 + (value - 52);
      } else if (value === 62) {
        return 0x2b;
      } else if (value === 63) {
        return 0x2f;
      }
      throw new Error(`value:${value}`);
    }

    function writeEncoded(byte) {
      varBase64 += String.fromCharCode(encode(byte & 0x3f));
    }

    varThis.writeByte = value => {
      varBuffer = (varBuffer << 8) | (value & 0xff);
      varBuffLength += 8;
      varLength += 1;
      while (varBuffLength >= 6) {
        writeEncoded(varBuffer >>> (varBuffLength - 6));
        varBuffLength -= 6;
      }
    };

    varThis.flush = () => {
      if (varBuffLength > 0) {
        writeEncoded(varBuffer << (6 - varBuffLength));
        varBuffer = 0;
        varBuffLength = 0;
      }
      if (varLength % 3 !== 0) {
        // padding
        const padLength = 3 - (varLength % 3);
        for (let index = 0; index < padLength; index++) {
          varBase64 = `${varBase64}=`;
        }
      }
    };

    varThis.toString = () => varBase64;
    return varThis;
  }

  // function createImgTag(width, height, getPixel, alt) {
  //     const gif = gifImage(width, height);
  //     for (let yAxis = 0; yAxis < height; yAxis++) {
  //         for (let xAxis = 0; xAxis < width; xAxis++) {
  //             gif.setPixel(xAxis, yAxis, getPixel(xAxis, yAxis));
  //         }
  //     }
  //     const bytesArray = byteArrayOutputStream();
  //     gif.write(bytesArray);
  //     const base64 = base64EncodeOutputStream();
  //     const bytes = bytesArray.toByteArray();
  //     for (let index = 0; index < bytes.length; index++) {
  //         base64.writeByte(bytes[index]);
  //     }
  //     base64.flush();
  //     const img = `<img src="data:image/gif;base64,${base64}" width="${width}" height="${height}"`;
  //     if (alt) {
  //         img = `${img} alt="alt"`;
  //     }
  //     return `${img}/>`;
  // }

  function createRawGif(width, height, getPixel, alt) {
    const gif = gifImage(width, height);
    for (let yAxis = 0; yAxis < height; yAxis++) {
      for (let xAxis = 0; xAxis < width; xAxis++) {
        gif.setPixel(xAxis, yAxis, getPixel(xAxis, yAxis));
      }
    }
    const bytesArray = byteArrayOutputStream();
    gif.write(bytesArray);
    const base64 = base64EncodeOutputStream();
    const bytes = bytesArray.toByteArray();
    for (let index = 0; index < bytes.length; index++) {
      base64.writeByte(bytes[index]);
    }
    base64.flush();
    return {
      data: `data:image/gif;base64,${base64}`,
      width,
      height,
      alt: alt | null,
    };
  }

  // ---------------------------------------------------------------------
  // qrcode
  // ---------------------------------------------------------------------

  function qrcode(typeNumber, errorCorrectLevel) {
    const PAD0 = 0xec;
    const PAD1 = 0x11;
    let varTypeNumber = typeNumber;
    const varErrorCorrectLevel = QRErrorCorrectLevel[errorCorrectLevel];
    let varModules = null;
    let varModuleCount = 0;
    let varDataCache = null;
    const varDataList = [];
    const varThis = {};

    function setupPositionProbePattern(row, col) {
      for (let row1 = -1; row1 <= 7; row1++) {
        if (!(row + row1 <= -1 || varModuleCount <= row + row1)) {
          for (let col1 = -1; col1 <= 7; col1++) {
            // varModules[row + row1][col + col1] = (!(col + col1 <= -1 || varModuleCount <= col + col1)
            // && ((row1 >= 0 && row1 <= 6 && (col1 === 0 || col1 === 6))
            // || (col1 >= 0 && col1 <= 6 && (row1 === 0 || row1 === 6))
            // || (row1 >= 2 && row1 <= 4 && col1 >= 2 && col1 <= 4)));
            if (!(col + col1 <= -1 || varModuleCount <= col + col1)) {
              varModules[row + row1][col + col1] =
                (row1 >= 0 && row1 <= 6 && (col1 === 0 || col1 === 6)) ||
                (col1 >= 0 && col1 <= 6 && (row1 === 0 || row1 === 6)) ||
                (row1 >= 2 && row1 <= 4 && col1 >= 2 && col1 <= 4);
            }
          }
        }
      }
    }

    function setupPositionAdjustPattern() {
      const pos = QRUtil.getPatternPosition(varTypeNumber);
      for (let index1 = 0; index1 < pos.length; index1++) {
        for (let index2 = 0; index2 < pos.length; index2++) {
          const row = pos[index1];
          const col = pos[index2];
          if (varModules[row][col] === null) {
            for (let row1 = -2; row1 <= 2; row1++) {
              for (let col1 = -2; col1 <= 2; col1++) {
                varModules[row + row1][col + col1] =
                  row1 === -2 ||
                  row1 === 2 ||
                  col1 === -2 ||
                  col1 === 2 ||
                  (row1 === 0 && col1 === 0);
              }
            }
          }
        }
      }
    }

    function setupTimingPattern() {
      for (let row = 8; row < varModuleCount - 8; row++) {
        if (varModules[row][6] === null) {
          varModules[row][6] = row % 2 === 0;
        }
      }
      for (let col = 8; col < varModuleCount - 8; col++) {
        if (varModules[6][col] === null) {
          varModules[6][col] = col % 2 === 0;
        }
      }
    }

    function setupTypeInfo(testData, maskPattern) {
      const data = (varErrorCorrectLevel << 3) | maskPattern;
      const bits = QRUtil.getBCHTypeInfo(data);
      // vertical
      for (let vertical = 0; vertical < 15; vertical++) {
        const mod1 = !testData && ((bits >> vertical) & 1) === 1;
        if (vertical < 6) {
          varModules[vertical][8] = mod1;
        } else if (vertical < 8) {
          varModules[vertical + 1][8] = mod1;
        } else {
          varModules[varModuleCount - 15 + vertical][8] = mod1;
        }
      }
      // horizontal
      for (let horizontal = 0; horizontal < 15; horizontal++) {
        const mod2 = !testData && ((bits >> horizontal) & 1) === 1;
        if (horizontal < 8) {
          varModules[8][varModuleCount - horizontal - 1] = mod2;
        } else if (horizontal < 9) {
          varModules[8][15 - horizontal - 1 + 1] = mod2;
        } else {
          varModules[8][15 - horizontal - 1] = mod2;
        }
      }
      // fixed module
      varModules[varModuleCount - 8][8] = !testData;
    }

    function setupTypeNumber(testData) {
      const bits = QRUtil.getBCHTypeNumber(varTypeNumber);
      for (let index1 = 0; index1 < 18; index1++) {
        varModules[Math.floor(index1 / 3)][
          (index1 % 3) + varModuleCount - 8 - 3
        ] = !testData && ((bits >> index1) & 1) === 1;
      }
      for (let index2 = 0; index2 < 18; index2++) {
        varModules[(index2 % 3) + varModuleCount - 8 - 3][
          Math.floor(index2 / 3)
        ] = !testData && ((bits >> index2) & 1) === 1;
      }
    }

    function createBytes(buffer, rsBlocks) {
      let offset = 0;
      let maxDcCount = 0;
      let maxEcCount = 0;
      const dcData = new Array(rsBlocks.length);
      const ecData = new Array(rsBlocks.length);
      for (let row = 0; row < rsBlocks.length; row++) {
        const dcCount = rsBlocks[row].dataCount;
        const ecCount = rsBlocks[row].totalCount - dcCount;
        maxDcCount = Math.max(maxDcCount, dcCount);
        maxEcCount = Math.max(maxEcCount, ecCount);
        dcData[row] = new Array(dcCount);
        for (let index1 = 0; index1 < dcData[row].length; index1++) {
          dcData[row][index1] = 0xff & buffer.getBuffer()[index1 + offset];
        }
        offset += dcCount;
        const rsPoly = QRUtil.getErrorCorrectPolynomial(ecCount);
        const rawPoly = qrPolynomial(dcData[row], rsPoly.getLength() - 1); // eslint-disable-line no-use-before-define
        const modPoly = rawPoly.mod(rsPoly);
        ecData[row] = new Array(rsPoly.getLength() - 1);
        for (let index2 = 0; index2 < ecData[row].length; index2++) {
          const modIndex = index2 + modPoly.getLength() - ecData[row].length;
          ecData[row][index2] = modIndex >= 0 ? modPoly.getAt(modIndex) : 0;
        }
      }
      let totalCodeCount = 0;
      for (let index3 = 0; index3 < rsBlocks.length; index3++) {
        totalCodeCount += rsBlocks[index3].totalCount;
      }
      const data = new Array(totalCodeCount);
      let index = 0;
      for (let index4 = 0; index4 < maxDcCount; index4++) {
        for (let row1 = 0; row1 < rsBlocks.length; row1++) {
          if (index4 < dcData[row1].length) {
            data[index] = dcData[row1][index4];
            index += 1;
          }
        }
      }
      for (let index5 = 0; index5 < maxEcCount; index5++) {
        for (let row2 = 0; row2 < rsBlocks.length; row2++) {
          if (index5 < ecData[row2].length) {
            data[index] = ecData[row2][index5];
            index += 1;
          }
        }
      }
      return data;
    }

    function createData(typeNumberData, errorCorrectLevelData, dataList) {
      const rsBlocks = QRRSBlock.getRSBlocks(
        typeNumberData,
        errorCorrectLevelData
      );
      const buffer = qrBitBuffer();
      for (let index1 = 0; index1 < dataList.length; index1++) {
        const data = dataList[index1];
        buffer.put(data.getMode(), 4);
        buffer.put(
          data.getLength(),
          QRUtil.getLengthInBits(data.getMode(), typeNumberData)
        );
        data.write(buffer);
      }
      // calc num max data.
      let totalDataCount = 0;
      for (let index2 = 0; index2 < rsBlocks.length; index2++) {
        totalDataCount += rsBlocks[index2].dataCount;
      }
      if (buffer.getLengthInBits() > totalDataCount * 8) {
        throw new Error(
          `code length overflow. (${buffer.getLengthInBits()}>${
            totalDataCount * 8
          })`
        );
      }
      // end code
      if (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {
        buffer.put(0, 4);
      }
      // padding
      while (buffer.getLengthInBits() % 8 !== 0) {
        buffer.putBit(false);
      }
      // padding
      // eslint-disable-next-line no-constant-condition
      while (true) {
        if (buffer.getLengthInBits() >= totalDataCount * 8) {
          break;
        }
        buffer.put(PAD0, 8);
        if (buffer.getLengthInBits() >= totalDataCount * 8) {
          break;
        }
        buffer.put(PAD1, 8);
      }
      return createBytes(buffer, rsBlocks);
    }

    function mapData(data, maskPattern) {
      let inc = -1;
      let row = varModuleCount - 1;
      let bitIndex = 7;
      let byteIndex = 0;
      const maskFunc = QRUtil.getMaskFunction(maskPattern);
      for (let col = varModuleCount - 1; col > 0; col -= 2) {
        if (col === 6) {
          col -= 1;
        }
        // eslint-disable-next-line no-constant-condition
        while (true) {
          for (let col1 = 0; col1 < 2; col1++) {
            if (varModules[row][col - col1] === null) {
              let dark = false;
              if (byteIndex < data.length) {
                dark = ((data[byteIndex] >>> bitIndex) & 1) === 1;
              }
              const mask = maskFunc(row, col - col1);
              if (mask) {
                dark = !dark;
              }
              varModules[row][col - col1] = dark;
              bitIndex -= 1;
              if (bitIndex === -1) {
                byteIndex += 1;
                bitIndex = 7;
              }
            }
          }
          row += inc;
          if (row < 0 || varModuleCount <= row) {
            row -= inc;
            inc = -inc;
            break;
          }
        }
      }
    }

    function makeImpl(testData, maskPattern) {
      varModuleCount = varTypeNumber * 4 + 17;
      varModules = (function createModules(moduleCount) {
        const modules = new Array(moduleCount);
        for (let row = 0; row < moduleCount; row++) {
          modules[row] = new Array(moduleCount);
          for (let col = 0; col < moduleCount; col++) {
            modules[row][col] = null;
          }
        }
        return modules;
      })(varModuleCount);
      setupPositionProbePattern(0, 0);
      setupPositionProbePattern(varModuleCount - 7, 0);
      setupPositionProbePattern(0, varModuleCount - 7);
      setupPositionAdjustPattern();
      setupTimingPattern();
      setupTypeInfo(testData, maskPattern);
      if (varTypeNumber >= 7) {
        setupTypeNumber(testData);
      }
      if (varDataCache === null) {
        varDataCache = createData(
          varTypeNumber,
          varErrorCorrectLevel,
          varDataList
        );
      }
      mapData(varDataCache, maskPattern);
    }

    function getBestMaskPattern() {
      let minLostPoint = 0;
      let pattern = 0;
      for (let index = 0; index < 8; index++) {
        makeImpl(true, index);
        const lostPoint = QRUtil.getLostPoint(varThis);
        if (index === 0 || minLostPoint > lostPoint) {
          minLostPoint = lostPoint;
          pattern = index;
        }
      }
      return pattern;
    }

    varThis.addData = data => {
      const newData = qr8BitByte(data);
      varDataList.push(newData);
      varDataCache = null;
    };

    varThis.isDark = (row, col) => {
      if (
        row < 0 ||
        varModuleCount <= row ||
        col < 0 ||
        varModuleCount <= col
      ) {
        throw new Error(`${row},${col}`);
      }
      return varModules[row][col];
    };

    varThis.getModuleCount = () => varModuleCount;

    varThis.make = () => {
      if (varTypeNumber < 1) {
        let typeNumberData = 1;
        for (typeNumberData = 1; typeNumberData < 40; typeNumberData++) {
          const rsBlocks = QRRSBlock.getRSBlocks(
            typeNumberData,
            varErrorCorrectLevel
          );
          // const buffer = new qrBitBuffer();
          const buffer = qrBitBuffer();
          let totalDataCount = 0;
          for (let index1 = 0; index1 < rsBlocks.length; index1++) {
            totalDataCount += rsBlocks[index1].dataCount;
          }
          for (let index2 = 0; index2 < varDataList.length; index2++) {
            const data = varDataList[index2];
            buffer.put(data.getMode(), 4);
            buffer.put(
              data.getLength(),
              QRUtil.getLengthInBits(data.getMode(), typeNumberData)
            );
            data.write(buffer);
          }
          if (buffer.getLengthInBits() <= totalDataCount * 8) {
            break;
          }
        }
        varTypeNumber = typeNumberData;
      }
      makeImpl(false, getBestMaskPattern());
    };

    // varThis.createTableTag = function (cellSize, margin) {
    //     cellSize = cellSize || 2;
    //     margin = (typeof margin == 'undefined') ? cellSize * 4 : margin;
    //     const data = `<table style="border-width: 0px; border-style: none; border-collapse: collapse; padding: 0px; margin: ${margin}px;"><tbody>`;
    //     for (let row = 0; row < varThis.getModuleCount(); row++) {
    //         data = `${data}<tr>`;
    //         for (let col = 0; col < varThis.getModuleCount(); col++) {
    //             data = `${data}<td style="border-width: 0px; border-style: none; border-collapse: collapse; padding: 0px; margin: 0px`;
    //             data = `${data}width: ${cellSize}px; height: ${cellSize}px; background-color: ${varThis.isDark(row, col) ? '#000000' : '#ffffff'};"/>`;
    //         }
    //         data = `${data}</tr>`;
    //     }
    //     return `${data}</tbody></table>`;
    // };

    // varThis.createImage = function (cellSize, margin) {
    //     cellSize = cellSize || 2;
    //     margin = (typeof margin == 'undefined') ? cellSize * 4 : margin;
    //     const size = varThis.getModuleCount() * cellSize + margin * 2;
    //     const min = margin;
    //     const max = size - margin;
    //     const gif = gifImage(size, size);
    //     for (let yAxis = 0; yAxis < size; yAxis++) {
    //         const result = Math.floor((yAxis - min) / cellSize);
    //         for (let xAxis = 0; xAxis < size; xAxis++) {
    //             const pixel = 1;
    //             if (min <= xAxis
    //                 && xAxis < max
    //                 && min <= yAxis
    //                 && yAxis < max
    //                 && varThis.isDark(result, Math.floor((xAxis - min) / cellSize))) {
    //                 pixel = 0;
    //             }
    //             gif.setPixel(xAxis, yAxis, pixel);
    //         }
    //     }
    //     const bytes = byteArrayOutputStream();
    //     gif.write(bytes);
    //     return {
    //         bytes: bytes,
    //         width: size,
    //         height: size
    //     };
    // };

    // varThis.createSvgTag = function (cellSize, margin) {
    //     cellSize = cellSize || 2;
    //     margin = (typeof margin == 'undefined') ? cellSize * 4 : margin;
    //     const size = varThis.getModuleCount() * cellSize + margin * 2;
    //     const col2 = 0;
    //     const row2 = 0;
    //     const rect = `l${cellSize},0 0,${cellSize} -${cellSize},0 0,-${cellSize}z `;
    //     const qrSvg = `<svg width="${size}px" height="${size}px" xmlns="http://www.w3.org/2000/svg"><path d="`;
    //     for (let row1 = 0; row1 < varThis.getModuleCount(); row1++) {
    //         row2 = row1 * cellSize + margin;
    //         for (let col1 = 0; col1 < varThis.getModuleCount(); col1++) {
    //             if (varThis.isDark(row1, col1)) {
    //                 col2 = col1 * cellSize + margin;
    //                 qrSvg = `${qrSvg}Medium${col2},${row2}${rect}`;
    //             }
    //         }
    //     }
    //     return `${qrSvg}" stroke="transparent" fill="black"/></svg>`;
    // };

    // varThis.createImgTag = function (cellSize, margin, alt) {
    //     const data = varThis.createImage(cellSize, margin);
    //     const base64 = base64EncodeOutputStream();
    //     const bytes = data.bytes.toByteArray();
    //     for (let index = 0; index < bytes.length; index++) {
    //         base64.writeByte(bytes[index]);
    //     }
    //     base64.flush();
    //     const img = `<img src="data:image/gif;base64,${base64}" width="${data.width}" height="${data.height}"`;
    //     if (alt) {
    //         img = `${img} alt="${alt}"`;
    //     }
    //     return `${img}/>`;
    // };

    varThis.createRawGif = (cellSize, margin) => {
      cellSize = cellSize || 2; // eslint-disable-line no-param-reassign
      margin = typeof margin == 'undefined' ? cellSize * 4 : margin; // eslint-disable-line no-param-reassign, eqeqeq
      const size = varThis.getModuleCount() * cellSize + margin * 2;
      const min = margin;
      const max = size - margin;
      return createRawGif(size, size, (horizontal, vertical) => {
        if (
          min <= horizontal &&
          horizontal < max &&
          min <= vertical &&
          vertical < max
        ) {
          const col = Math.floor((horizontal - min) / cellSize);
          const row = Math.floor((vertical - min) / cellSize);
          return varThis.isDark(row, col) ? 0 : 1;
        }
        return 1;
      });
    };

    // function createHalfASCII(margin) {
    //     const cellSize = 1;
    //     margin = (typeof margin == 'undefined') ? cellSize * 2 : margin;
    //     const size = varThis.getModuleCount() * cellSize + margin * 2;
    //     const min = margin;
    //     const max = size - margin;
    //     const blocks = {
    //         '██': '█',
    //         '█ ': '▀',
    //         ' █': '▄',
    //         '  ': ' '
    //     };
    //     const ascii = '';
    //     for (let yAxis = 0; yAxis < size; yAxis = yAxis + 2) {
    //         const row1 = Math.floor((yAxis - min) / cellSize);
    //         const row2 = Math.floor((yAxis + 1 - min) / cellSize);
    //         for (let xAxis = 0; xAxis < size; xAxis++) {
    //             const pixel = '█';
    //             if (min <= xAxis && xAxis < max && min <= yAxis && yAxis < max && varThis.isDark(row1, Math.floor((xAxis - min) / cellSize))) {
    //                 pixel = ' ';
    //             }
    //             if (min <= xAxis && xAxis < max && min <= yAxis + 1 && yAxis + 1 < max && varThis.isDark(row2, Math.floor((xAxis - min) / cellSize))) {
    //                 pixel = `${pixel} `;
    //             } else {
    //                 pixel = `${pixel}█`;
    //             }
    //             // Output 2 characters per pixel, to create full square. 1 character per pixels gives only half width of square.
    //             ascii = `${ascii}${blocks[pixel]}`;
    //         }
    //         ascii = `${ascii}\n`;
    //     }
    //     if (size % 2) {
    //         return `${ascii.substring(0, ascii.length - size - 1)}${new Array(size + 1).join('▀')}`;
    //     }
    //     return ascii.substring(0, ascii.length - 1);
    // }

    // varThis.createASCII = function (cellSize, margin) {
    //     cellSize = cellSize || 1;
    //     if (cellSize < 2) {
    //         return createHalfASCII(margin);
    //     }
    //     cellSize = cellSize - 1;
    //     margin = (typeof margin == 'undefined') ? cellSize * 2 : margin;
    //     const size = varThis.getModuleCount() * cellSize + margin * 2;
    //     const min = margin;
    //     const max = size - margin;
    //     const white = new Array(cellSize + 1).join('██');
    //     const black = new Array(cellSize + 1).join('  ');
    //     const ascii = '';
    //     const line = '';
    //     for (let yAxis = 0; yAxis < size; yAxis++) {
    //         const row = Math.floor((yAxis - min) / cellSize);
    //         line = '';
    //         for (let xAxis = 0; xAxis < size; xAxis++) {
    //             const pixel = 1;
    //             if (min <= xAxis && xAxis < max && min <= yAxis && yAxis < max && varThis.isDark(row, Math.floor((xAxis - min) / cellSize))) {
    //                 pixel = 0;
    //             }
    //             // Output 2 characters per pixel, to create full square. 1 character per pixel gives only half width of square.
    //             line = line + pixel ? white : black;
    //         }
    //         for (row = 0; row < cellSize; row++) {
    //             ascii = `${ascii}${line}\n`;
    //         }
    //     }
    //     return ascii.substring(0, ascii.length - 1);
    // };

    return varThis;
  }

  // ---------------------------------------------------------------------
  // qrcode.stringToBytes
  // ---------------------------------------------------------------------

  qrcode.stringToBytes = theString => {
    const bytes = [];
    for (let index = 0; index < theString.length; index++) {
      const code = theString.charCodeAt(index);
      bytes.push(code & 0xff);
    }
    return bytes;
  };

  // ---------------------------------------------------------------------
  // base64DecodeInputStream
  // ---------------------------------------------------------------------

  // function base64DecodeInputStream(str) {
  //     const anotherString = str;
  //     const thePosition = 0;
  //     const varBuffer = 0;
  //     const varBuffLength = 0;
  //
  //     const varThis = {};
  //
  //     function decode(col) {
  //         if (0x41 <= col && col <= 0x5a) {
  //             return col - 0x41;
  //         } else if (0x61 <= col && col <= 0x7a) {
  //             return col - 0x61 + 26;
  //         } else if (0x30 <= col && col <= 0x39) {
  //             return col - 0x30 + 52;
  //         } else if (col === 0x2b) {
  //             return 62;
  //         } else if (col === 0x2f) {
  //             return 63;
  //         }
  //         throw new Error(`col:${col}`);
  //     }
  //
  //     varThis.read = () => {
  //         while (varBuffLength < 8) {
  //             if (thePosition >= anotherString.length) {
  //                 if (varBuffLength === 0) {
  //                     return -1;
  //                 }
  //                 throw new Error(`unexpected end of file./${varBuffLength}`);
  //             }
  //             const col = anotherString.charAt(thePosition);
  //             thePosition = thePosition + 1;
  //             if (col === '=') {
  //                 varBuffLength = 0;
  //                 return -1;
  //             }
  //             varBuffer = (varBuffer << 6) | decode(col.charCodeAt(0));
  //             varBuffLength = varBuffLength + 6;
  //         }
  //         const value = (varBuffer >>> (varBuffLength - 8)) & 0xff;
  //         varBuffLength = varBuffLength - 8;
  //         return value;
  //     };
  //
  //     return varThis;
  // }

  // ---------------------------------------------------------------------
  // qrcode.createStringToBytes
  // ---------------------------------------------------------------------

  // qrcode.createStringToBytes = function (unicodeData, numChars) {
  //     // create conversion map.
  //     const unicodeMap = (function unicodeMap() {
  //         const bin = base64DecodeInputStream(unicodeData);
  //
  //         function read() {
  //             const bit = bin.read();
  //             if (bit === -1) {
  //                 throw new Error();
  //             }
  //             return bit;
  //         }
  //
  //         const count = 0;
  //         const unicodeMapData = {};
  //         while (true) { // eslint-disable-line no-constant-condition
  //             const bit0 = bin.read();
  //             if (bit0 === -1) {
  //                 break;
  //             }
  //             const bit1 = read();
  //             const bit2 = read();
  //             const bit3 = read();
  //             const key = String.fromCharCode((bit0 << 8) | bit1);
  //             unicodeMapData[key] = (bit2 << 8) | bit3;
  //             count = count + 1;
  //         }
  //         if (count !== numChars) {
  //             throw new Error(`${count} != ${numChars}`);
  //         }
  //         return unicodeMapData;
  //     }());
  //     const unknownChar = '?'.charCodeAt(0);
  //     return function (theString) {
  //         const bytes = [];
  //         for (let index = 0; index < theString.length; index++) {
  //             const col = theString.charCodeAt(index);
  //             if (col < 128) {
  //                 bytes.push(col);
  //             } else {
  //                 const bit = unicodeMap[theString.charAt(index)];
  //                 if (typeof bit == 'number') {
  //                     if ((bit & 0xff) === bit) {
  //                         // 1byte
  //                         bytes.push(bit);
  //                     } else {
  //                         // 2bytes
  //                         bytes.push(bit >>> 8);
  //                         bytes.push(bit & 0xff);
  //                     }
  //                 } else {
  //                     bytes.push(unknownChar);
  //                 }
  //             }
  //         }
  //         return bytes;
  //     };
  // };

  // ---------------------------------------------------------------------
  // qrPolynomial
  // ---------------------------------------------------------------------

  function qrPolynomial(num, shift) {
    if (typeof num.length === 'undefined') {
      // eslint-disable-line eqeqeq
      throw new Error(`${num.length}/${shift}`);
    }
    const aNumber = (function aNumber() {
      let offset = 0;
      while (offset < num.length && num[offset] === 0) {
        offset += 1;
      }
      const anotherNumber = new Array(num.length - offset + shift);
      for (let index = 0; index < num.length - offset; index++) {
        anotherNumber[index] = num[index + offset];
      }
      return anotherNumber;
    })();
    const varThis = {};
    varThis.getAt = index => aNumber[index];
    varThis.getLength = () => aNumber.length;
    varThis.multiply = value => {
      const numberValue = new Array(
        varThis.getLength() + value.getLength() - 1
      );
      for (let index1 = 0; index1 < varThis.getLength(); index1++) {
        for (let index2 = 0; index2 < value.getLength(); index2++) {
          numberValue[index1 + index2] =
            numberValue[index1 + index2] ^
            QRMath.gExp(
              QRMath.gLog(varThis.getAt(index1)) +
                QRMath.gLog(value.getAt(index2))
            );
        }
      }
      return qrPolynomial(numberValue, 0);
    };

    varThis.mod = value => {
      if (varThis.getLength() - value.getLength() < 0) {
        return varThis;
      }
      const ratio = QRMath.gLog(varThis.getAt(0)) - QRMath.gLog(value.getAt(0));
      const numberValue = new Array(varThis.getLength());
      for (let index3 = 0; index3 < varThis.getLength(); index3++) {
        numberValue[index3] = varThis.getAt(index3);
      }
      for (let index4 = 0; index4 < value.getLength(); index4++) {
        // eslint-disable-next-line operator-assignment
        numberValue[index4] =
          numberValue[index4] ^
          QRMath.gExp(QRMath.gLog(value.getAt(index4)) + ratio);
      }
      // recursive call
      return qrPolynomial(numberValue, 0).mod(value);
    };

    return varThis;
  }

  // ---------------------------------------------------------------------
  // returns qrcode function.

  return qrcode;
})();
