/* eslint-disable no-promise-executor-return */
const AWS = require('aws-sdk');
const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');
const { SESClient, SendEmailCommand } = require('@aws-sdk/client-ses');
const fs = require('fs-extra');
const streamToPromise = require('stream-to-promise');

const env = require('../env');
const { getTransactionHeaders } = require('./transactionHeaders');

const SQSOldClient = new AWS.SQS({ region: env.config.AWS.sqs.region });
const sqsClient = new SQSClient({ region: env.config.AWS.sqs.region });
const sesClient = new SESClient({ region: env.config.AWS.ses.region });

const DDBconfig = env.config.AWS.DDB.unitTestEndpoint
  ? {
      endpoint: env.config.AWS.DDB.unitTestEndpoint,
      region: env.config.AWS.DDB.region,
    }
  : { region: env.config.AWS.DDB.region };
const configurationSetName =
  env.config.AWS.ses.configurationSetName || 'ics-email-stats';
const DDB = new AWS.DynamoDB(DDBconfig);

const DDBDC = new AWS.DynamoDB.DocumentClient({ service: DDB });
const s3 = new AWS.S3({ region: env.config.AWS.S3.region });
const lambda = new AWS.Lambda({ region: env.config.AWS.Lambda.region });
const s3bucket = env.config.AWS.S3.bucket;
const kinesis = new AWS.Kinesis({ region: env.config.AWS.kinesis.region });
const firehose = new AWS.Firehose({ region: env.config.AWS.kinesis.region });
const noReplyEmail = 'Invenco Cloud Services <<EMAIL>>';
const eventBridge = new AWS.EventBridge({ region: env.config.AWS.sqs.region });
const { eventBusSources } = require('./app-constants');
const logger = require('./logger').mainLogger();

if (env.config.s3Retry) {
  // s3 retry config
  const s3RetryConfig = env.config.s3Retry;
  s3.config.maxRetries = s3RetryConfig.maxRetries || 9;
  s3.config.retryDelayOptions = {
    customBackoff: retryCount => {
      const baseDelayMs = s3RetryConfig.baseDelayMs || 100;
      const maxDelayMs = s3RetryConfig.maxDelayMs || 10000;
      return Math.min(baseDelayMs * 2 ** retryCount, maxDelayMs);
    },
  };
}

const addDistributedHeadersToSqsMessageAttributes = params => {
  const transactionHeaders = getTransactionHeaders();
  if (params.MessageAttributes === undefined) {
    // eslint-disable-next-line no-param-reassign
    params.MessageAttributes = {};
  }

  Object.keys(transactionHeaders).forEach(key => {
    const value = transactionHeaders[key];
    if (value) {
      // eslint-disable-next-line no-param-reassign
      params.MessageAttributes[key] = {
        DataType: 'String',
        StringValue: value,
      };
    }
  });

  return params;
};

const sendSqsMessage = async (params, sqsClientInput = sqsClient) => {
  addDistributedHeadersToSqsMessageAttributes(params);

  const command = new SendMessageCommand(params);
  return sqsClientInput.send(command);
};

const publishSqsMessage = async (message, messageGroupId, type) => {
  const queueUrl = env.config.AWS.sqs.queues[type];
  const params = {
    QueueUrl: queueUrl,
    MessageBody: JSON.stringify({ detail: message }),
    ...(messageGroupId
      ? {
          MessageGroupId: messageGroupId,
        }
      : {}),
  };
  await sqsClient.send(new SendMessageCommand(params));
};

// TODO: new aws-sdk v3 client is not working across different regions in our code. Temporary use the old client from aws-sdk v2
const sendSqsMessageBatch = params =>
  new Promise((resolve, reject) => {
    SQSOldClient.sendMessageBatch(params, err => {
      if (err) {
        return reject(err);
      }
      return resolve();
    });
  });

const createSqsClient = (region, secretAccessKey, accessKeyId) =>
  new SQSClient({
    region,
    credentials: {
      secretAccessKey,
      accessKeyId,
    },
  });

const sendHtmlEmail = async (to, subject, message, sender) => {
  sender = sender || noReplyEmail; // eslint-disable-line no-param-reassign
  const command = new SendEmailCommand({
    Destination: {
      ToAddresses: to,
    },
    Message: {
      Body: {
        Html: {
          Data: message,
          Charset: 'UTF-8',
        },
      },
      Subject: {
        Data: subject,
      },
    },
    Source: sender,
    ConfigurationSetName: configurationSetName,
  });

  return sesClient.send(command);
};

const sendHtmlEmailToBccCc = async (to, bcc, cc, subject, message, sender) => {
  const Source = sender || noReplyEmail;
  const command = new SendEmailCommand({
    Destination: {
      ToAddresses: to,
      BccAddresses: bcc,
      CcAddresses: cc,
    },
    Message: {
      Body: {
        Html: {
          Data: message,
          Charset: 'UTF-8',
        },
      },
      Subject: {
        Data: subject,
      },
    },
    Source,
    ConfigurationSetName: configurationSetName,
  });

  return sesClient.send(command);
};

const uploadToS3 = (bucket, key, body, acl) => {
  const transactionHeaders = getTransactionHeaders();
  const metadata = {};
  Object.keys(transactionHeaders).forEach(i => {
    metadata[i] = transactionHeaders[i];
  });

  const params = {
    Bucket: bucket,
    Key: key,
    Body: body,
    ACL: acl,
    Metadata: metadata,
  };

  return new Promise((resolve, reject) => {
    s3.upload(params, (err, data) => {
      if (err) {
        return reject(err);
      }
      return resolve(data);
    });
  });
};

const s3PutObject = (bucket, key) => {
  const transactionHeaders = getTransactionHeaders();
  const metadata = {};
  Object.keys(transactionHeaders).forEach(i => {
    metadata[i] = transactionHeaders[i];
  });

  const params = {
    Bucket: bucket,
    Key: key,
    Metadata: metadata,
  };

  return new Promise((resolve, reject) => {
    s3.putObject(params, (err, data) => {
      if (err) {
        return reject(err);
      }
      return resolve(data);
    });
  });
};

module.exports = {
  DDBDC: {
    batchWrite: params => DDBDC.batchWrite(params).promise(),
    query: params => DDBDC.query(params).promise(),
    scan: params => DDBDC.scan(params).promise(),
    put: params => DDBDC.put(params).promise(),
  },

  DDB: {
    marshall: (payload, options) =>
      AWS.DynamoDB.Converter.marshall(payload, options),
    batchWriteItem: params => DDB.batchWriteItem(params).promise(),
    query: params => DDB.query(params).promise(),
    createTable: params => DDB.createTable(params).promise(),
    deleteTable: params => DDB.deleteTable(params).promise(),
  },

  Lambda: {
    invoke: params => lambda.invoke(params).promise(),
  },

  getS3URL() {
    return env.config.AWS.S3.baseURL + env.config.AWS.S3.bucket;
  },

  uploadToS3,
  s3PutObject,

  downloadFromS3(params) {
    params.Bucket = params.Bucket || s3bucket; // eslint-disable-line no-param-reassign
    return s3.getObject(params);
  },

  downloadFromS3Stream(fileObj, destination) {
    const file = fs.createWriteStream(destination);
    const s3FileStream = fileObj.createReadStream();
    s3FileStream.pipe(file);
    return streamToPromise(s3FileStream);
  },

  downloadMetadataFromS3(params) {
    params.Bucket = params.Bucket || s3bucket; // eslint-disable-line no-param-reassign
    return s3.headObject(params).promise();
  },

  retrieveS3Files(params) {
    return new Promise((resolve, reject) => {
      s3.listObjects(params, (error, data) => {
        if (error) reject(error);
        resolve(data);
      });
    });
  },

  deleteS3Files(bucket, fileKeys) {
    const params = {
      Bucket: bucket,
      Delete: {
        Objects: fileKeys,
        Quiet: false,
      },
    };
    return new Promise((resolve, reject) => {
      s3.deleteObjects(params, (err, data) => {
        if (err) reject(err);
        resolve(data);
      });
    });
  },

  sendHtmlEmail,
  sendHtmlEmailToBccCc,
  createSqsClient,
  sendSqsMessage,
  sendSqsMessageBatch,
  publishSqsMessage,

  firehosePutRecords(params) {
    return firehose
      .putRecordBatch({
        Records: params.Records,
        DeliveryStreamName: params.DeliveryStreamName,
      })
      .promise();
    // TODO: save failed messages somewhere
  },

  kinesisPutRecords(params) {
    return new Promise((resolve, reject) => {
      kinesis.putRecords(
        {
          Records: params.Records,
          StreamName: params.StreamName,
        },
        (err, data) => {
          if (err) {
            return reject(err);
          }

          const failedRecords = [];
          if (data.FailedRecordCount > 0 && data.Records) {
            // extract failed records and send them to DLQ
            for (let i = 0; i < data.Records.length; i++) {
              if (data.Records[i].ErrorCode) {
                failedRecords.push({
                  Id: i.toString(),
                  MessageBody: JSON.stringify(data.Records[i].Data),
                  MessageAttributes: {
                    ErrorCode: {
                      DataType: 'String',
                      StringValue: data.Records[i].ErrorCode.toString(),
                    },
                    ErrorMessage: {
                      DataType: 'String',
                      StringValue: data.Records[i].ErrorMessage,
                    },
                  },
                });
              }
            }

            let index = 0;
            while (index < failedRecords.length) {
              const items = failedRecords.slice(index, index + 10);
              this.sendSqsMessageBatch({
                Entries: items,
                QueueUrl: env.config.AWS.kinesis.deadLetterQueue,
              });
              index += 10;
            }
          }
          return resolve();
        }
      );
    });
  },

  invokeLambda(params) {
    return new Promise((resolve, reject) => {
      lambda.invoke(params, err => {
        if (err) {
          return reject(err);
        }
        return resolve();
      });
    });
  },

  generateSignedURL(params, expiresIn = 60) {
    return s3.getSignedUrlPromise('putObject', {
      ...params,
      Expires: expiresIn,
    });
  },
  s3GenerateGetObjectSignedURL(params, expiresIn = 60) {
    return s3.getSignedUrlPromise('getObject', {
      ...params,
      Expires: expiresIn,
    });
  },
  async sendEventsToEventBridge(params) {
    if (!params || typeof params !== 'object') {
      throw new TypeError(
        'sendEventsToEventBridge() requires a Entry object or Array<Entry>'
      );
    }

    const entries = [];

    const resolveEventBusName = source => {
      const eventBusNames = {
        [eventBusSources.PUT_SITE]: env.config.AWS.eventBus.proofOfPlay.name,
        [eventBusSources.DEPLOYMENT_REPORT]:
          env.config.AWS.eventBus.deploymentReport.name,
        default: env.config.AWS.eventBus.renditions.name,
      };
      if (env.config.AWS.eventBus && env.config.AWS.eventBus.fuelPriceService) {
        eventBusNames[eventBusSources.FPS_UPDATE_FUEL_PRICES] =
          env.config.AWS.eventBus.fuelPriceService.name;
        eventBusNames[eventBusSources.SITE_REPLICATION] =
          env.config.AWS.eventBus.fuelPriceService.name;
        eventBusNames[eventBusSources.SITE_TAGS_REPLICATION] =
          env.config.AWS.eventBus.fuelPriceService.name;
        eventBusNames[eventBusSources.FPS_UPDATE_JOB_STATUS] =
          env.config.AWS.eventBus.fuelPriceService.name;
      }
      return eventBusNames[source] || eventBusNames.default;
    };

    const entryDefault = source => ({
      EventBusName: resolveEventBusName(source),
      Source: `api-node-${source}`,
    });

    if (Array.isArray(params)) {
      const finalParams = params.map(
        ({ data, detailType, source, ...rest }) => ({
          Detail: JSON.stringify(data),
          DetailType: detailType,
          ...rest,
          ...entryDefault(source),
        })
      );

      entries.push(...finalParams);
    } else {
      const { data, detailType, source, ...rest } = params;
      const entry = {
        Detail: JSON.stringify(data),
        DetailType: detailType,
        ...rest,
        ...entryDefault(source),
      };

      entries.push(entry);
    }

    const eventBusResponse = await eventBridge
      .putEvents({ Entries: entries })
      .promise();

    if (eventBusResponse.Error) {
      logger.error(eventBusResponse.Error.message);
    }

    eventBusResponse.Entries.forEach(eventBusResponseEntry => {
      if (eventBusResponseEntry.ErrorMessage) {
        logger.error(eventBusResponseEntry.ErrorMessage);
      }
    });

    return eventBusResponse;
  },
};
