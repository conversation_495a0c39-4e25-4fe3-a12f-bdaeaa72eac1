/**
 * Service for generating templates
 */
const fs = require('fs');
const _ = require('lodash');
const { url } = require('../env').config;
const AWS = require('./aws');

let sendInviteTemplate = null;
let passwordRestTemplate = null;
let sendVerifyEmail = null;
let promptsetApprovalEmail = null;
let sendNotificationEmail = null;
let sendGenericNotificationEmail = null;
let inactiveUserWarmingEmail = null;
let inactiveUserEmail = null;

const emailTemplate = {
  INVITE: 0,
  PASSWORD_RESET: 1,
  VERIFY_EMAIL: 2,
  PROMPTSET_APPROVAL: 3,
  SEND_NOTIFICATION: 4,
  INACTIVATE_USER_WARMING: 5,
  INACTIVATE_USER: 6,
  SEND_NOTIFICATION_GENERIC: 7,
};

/**
 * Loads the template from a file to a string. Also adding the image url to the template
 * @param location
 * @returns {string}
 */
function loadTemplateToString(location) {
  const template = fs.readFileSync(location, 'utf-8');
  return _.replace(
    template,
    /{{image}}/g,
    `${url.web}/assets/img/email/invenco-cloud-services.jpg`
  );
}

/**
 * Get the correct template. Lazy reads files
 * @param templateName
 * @returns {*}
 */
function getTemplate(templateName) {
  let template = null;
  switch (templateName) {
    case emailTemplate.INVITE:
      if (!sendInviteTemplate)
        sendInviteTemplate = loadTemplateToString(
          './resources/email-templates/signup_email.html'
        );
      template = sendInviteTemplate;
      break;
    case emailTemplate.PASSWORD_RESET:
      if (!passwordRestTemplate)
        passwordRestTemplate = loadTemplateToString(
          './resources/email-templates/password_reset.html'
        );
      template = passwordRestTemplate;
      break;
    case emailTemplate.VERIFY_EMAIL:
      if (!sendVerifyEmail)
        sendVerifyEmail = loadTemplateToString(
          './resources/email-templates/confirm_email.html'
        );
      template = sendVerifyEmail;
      break;
    case emailTemplate.PROMPTSET_APPROVAL:
      if (!promptsetApprovalEmail)
        promptsetApprovalEmail = loadTemplateToString(
          './resources/email-templates/promptset-approval.html'
        );
      template = promptsetApprovalEmail;
      break;
    case emailTemplate.SEND_NOTIFICATION:
      if (!sendNotificationEmail)
        sendNotificationEmail = loadTemplateToString(
          './resources/email-templates/send_notification.html'
        );
      template = sendNotificationEmail;
      break;
    case emailTemplate.SEND_NOTIFICATION_GENERIC:
      if (!sendGenericNotificationEmail)
        sendGenericNotificationEmail = loadTemplateToString(
          './resources/email-templates/send_notification_generic.html'
        );
      template = sendGenericNotificationEmail;
      break;
    case emailTemplate.INACTIVATE_USER_WARMING:
      if (!inactiveUserWarmingEmail)
        inactiveUserWarmingEmail = loadTemplateToString(
          './resources/email-templates/inactive_user_warming.html'
        );
      template = inactiveUserWarmingEmail;
      break;
    case emailTemplate.INACTIVATE_USER:
      if (!inactiveUserEmail)
        inactiveUserEmail = loadTemplateToString(
          './resources/email-templates/inactive_user_notification.html'
        );
      template = inactiveUserEmail;
      break;
    default:
      throw new Error('Template not found');
  }
  return template;
}

module.exports = {
  sendInvite: async (
    { to, from },
    token,
    requester,
    company,
    subject = 'Registration'
  ) => {
    // Construct the correct link
    const link = `${url.web}/signup?token=${token}`;

    // Construct the password reset message here
    let template = getTemplate(emailTemplate.INVITE);
    template = _.replace(template, /{{inviteLink}}/g, link)
      .replace('{{toEmail}}', to)
      .replace('{{requester}}', requester)
      .replace('{{companyName}}', company.name);

    return AWS.sendHtmlEmail([to], subject, template, from);
  },

  sendPasswordReset: async (
    { to, from },
    token,
    companyId,
    subject = 'Password Reset'
  ) => {
    // Construct the correct link
    const link = `${url.web}/resetpassword?token=${token}`;

    // Construct the password reset message here
    let template = getTemplate(emailTemplate.PASSWORD_RESET);
    template = _.replace(template, /{{resetLink}}/g, link).replace(
      '{{toEmail}}',
      to
    );

    return AWS.sendHtmlEmail([to], subject, template, from);
  },

  sendVerifyEmail: async (
    { to, from },
    token,
    companyId,
    subject = 'Verify Email Address'
  ) => {
    // Construct the correct link
    const link = `${url.web}/verifyemail?token=${token}`;

    // Construct the verification message here
    let template = getTemplate(emailTemplate.VERIFY_EMAIL);
    template = _.replace(template, /{{verifyEmailLink}}/g, link).replace(
      '{{toEmail}}',
      to
    );

    return AWS.sendHtmlEmail([to], subject, template, from);
  },

  sendPromptSetApprovalRequest: async (
    { to, from },
    { id, name },
    subject = 'PromptSet Awaiting Approval'
  ) => {
    const link = `${url.web}/media/promptsets/${id}`;

    let template = getTemplate(emailTemplate.PROMPTSET_APPROVAL);
    template = _.replace(template, /{{link}}/g, link)
      .replace('{{name}}', name)
      .replace('{{toEmail}}', to);

    return AWS.sendHtmlEmail([to], subject, template, from);
  },
  sendNotification: async ({ to, from }, { message, path }, subject) => {
    const link = `${url.web}${path}`;
    let template = getTemplate(emailTemplate.SEND_NOTIFICATION);

    template = _.replace(template, /{{link}}/g, link).replace(
      '{{message}}',
      message
    );

    return AWS.sendHtmlEmail([to], subject, template, from);
  },
  sendEmailToManyReceipient: async (
    { to, bcc, cc, from },
    { style, message },
    subject
  ) => {
    let template = getTemplate(emailTemplate.SEND_NOTIFICATION_GENERIC);

    template = _.replace(template, /{{style}}/g, style)
      .replace('{{message}}', message)
      .replace(/{{linkUrlWeb}}/g, url.web);

    return AWS.sendHtmlEmailToBccCc(to, bcc, cc, subject, template, from);
  },
  sendInactiveUserWarmingEmail: async (
    { to, from },
    subject = 'Your account will expire soon'
  ) => {
    const link = `${url.web}`;
    let template = getTemplate(emailTemplate.INACTIVATE_USER_WARMING);
    template = _.replace(template, /{{loginLink}}/g, link).replace(
      '{{toEmail}}',
      to
    );
    return AWS.sendHtmlEmail([to], subject, template, from);
  },
  sendInactiveUserNotificationEmail: async (
    { to, from },
    subject = 'Your account has expired'
  ) => {
    const environmentName = process.env.ENVIRONMENT;
    let link = '';
    if (!environmentName.toLowerCase().includes('prod'))
      link =
        // eslint-disable-next-line no-multi-str
        `<a\
        href="${url.web}"\
        style="\
        background-color: #3849a2;\
        border-radius: 4px;\
        color: #ffffff;\
        display: inline-block;\
        font-family: sans-serif;\
        font-size: 13px;\
        font-weight: bold;\
        line-height: 45px;\
        text-align: center;\
        text-decoration: none;\
        width: 130px;\
        -webkit-text-size-adjust: none;\
        "\
        >Login</a>`;

    let template = getTemplate(emailTemplate.INACTIVATE_USER);
    template = _.replace(template, /{{toEmail}}/g, to).replace(
      '{{loginLink}}',
      link
    );
    return AWS.sendHtmlEmail([to], subject, template, from);
  },
};
