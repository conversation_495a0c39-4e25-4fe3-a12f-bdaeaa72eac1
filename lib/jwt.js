const _ = require('lodash');
const uuid = require('uuid/v4');
const jwt = require('jsonwebtoken');

const env = require('../env');

const JWT_ALG = 'RS256';
const { config } = env;

module.exports = {
  // TODO: Marcelo - We should eliminate this token creation I believe
  createToken: user => {
    const signObj = {
      fullName: user.fullName,
      email: user.email,
      type: 'Bearer',
      roles: user.roles,
    };

    // check if user.created field is presented (refresh token, already populated)
    // otherwise this is a new token creation, convert 'created' into number without miliseconds
    if (_.isNumber(user.created)) {
      signObj.created = user.created;
    } else {
      signObj.created = Math.floor(user.created.getTime() / 1000);
    }

    if (user.company) {
      signObj.company = {
        id: user.company.id,
        name: user.company.name,
        featureFlags: user.company.featureFlags,
      };
    }

    if (user.type === 'SYSTEM') {
      if (signObj.company && signObj.company.featureFlags) {
        delete signObj.company.featureFlags;
      }
    }

    if (user.impersonator) {
      signObj.impersonator = {
        sub: user.impersonator.sub,
        fullName: user.impersonator.fullName,
      };
    }

    if (!config.auth.defaultSessionExpiryUserMins) {
      throw new Error('Missing JWT expiry config property');
    }

    // The creation of device tokens aren't implement in Node yet
    let expiresIn = config.auth.defaultSessionExpiryUserMins;
    if (user.company && user.company.sessionExpiryUserMins) {
      expiresIn = user.company.sessionExpiryUserMins;
    }

    return jwt.sign(signObj, env.privateKey, {
      algorithm: JWT_ALG,
      expiresIn: expiresIn * 60,
      notBefore: 0,
      audience: 'invenco.cloud',
      issuer: config.auth.issuer,
      jwtid: uuid(),
      subject: user.id,
    });
  },
  /**
   * Create long lived ICS_SYSTEM token for invoking external endpoints.
   *
   * @param {Object} user
   * @return {Promise<?string>}
   */
  // TODO: Marcelo - What to do with system users ? can bypass many things including 2FA (Check how to do this)
  createSystemToken: async user =>
    jwt.sign(
      {
        fullName: user.fullName,
        email: user.email,
        type: 'Bearer',
        roles: user.roles,
      },
      env.privateKey,
      {
        algorithm: JWT_ALG,
        audience: 'invenco.cloud',
        expiresIn: 86400,
        issuer: config.auth.issuer,
        jwtid: uuid(),
        subject: user.id,
      }
    ),
  createDeviceToken: (deviceId, companyId) => {
    const signObj = {
      type: 'X-Auth',
      roles: ['DEVICE'],
      companyId,
    };

    if (!config.auth.defaultSessionExpiryDeviceMins) {
      throw new Error('Missing JWT expiry config property');
    }

    const expiresIn = config.auth.defaultSessionExpiryDeviceMins;

    return jwt.sign(signObj, env.privateKey, {
      algorithm: JWT_ALG,
      expiresIn: expiresIn * 60,
      issuer: config.auth.issuer,
      jwtid: uuid(),
      subject: `${deviceId}`,
    });
  },
};
