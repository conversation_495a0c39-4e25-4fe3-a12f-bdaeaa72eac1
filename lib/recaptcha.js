const axios = require('axios');

const env = require('../env');

const { captcha } = env.config;
const logger = require('./logger').mainLogger();

const validateRecaptcha = async captchaResponse => {
  try {
    if (!captchaResponse) {
      return {
        success: false,
        message: 'captcha response not found',
      };
    }
    const result = await axios({
      method: 'post',
      url: captcha.captchaVerifyUrl,
      params: {
        secret: captcha.secretKey,
        response: captchaResponse,
      },
    });
    const data = result.data || {};
    if (data.success) {
      return {
        success: true,
        message: 'response valid',
      };
    }
    return {
      success: false,
      error: 'response not valid',
    };
  } catch (err) {
    logger.error({ err }, '[validateRecaptcha]');
    return {
      success: false,
      error: 'captcha_error',
    };
  }
};

module.exports = {
  validateRecaptcha,
};
