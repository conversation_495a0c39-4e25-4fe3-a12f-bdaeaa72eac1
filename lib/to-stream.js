const { Readable } = require('stream');

class Stream extends Readable {
  constructor(data) {
    super({ objectMode: true });
    if (typeof data === 'object' && !Buffer.isBuffer(data)) {
      if (Array.isArray(data)) {
        this.data = data;
      } else {
        this.data = [];
      }
    } else {
      this.data = data;
    }
  }

  // eslint-disable-next-line no-underscore-dangle
  _read(_size) {
    if (Array.isArray(this.data)) {
      this.push(this.data.splice(0, 1)[0]);
      if (this.data.length === 0) {
        this.push(null);
      }
    } else if (Buffer.isBuffer(this.data)) {
      // eslint-disable-next-line no-buffer-constructor
      this.push(new Buffer(this.data));
      this.push(null);
    } else {
      this.push(this.data);
      this.push(null);
    }
  }
}

const safeWrapper = data => {
  if ((typeof data) in ['boolean', 'number', 'string']) {
    return new Stream(data.valueOf());
  }
  return new Stream(data);
};

module.exports.toStream = safeWrapper;
