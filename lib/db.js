/* eslint-disable no-underscore-dangle */
const _ = require('lodash');
const pg = require('pg');
const pgCamelCase = require('pg-camelcase');
const co = require('co');

const { config } = require('../env');
const mainLogger = require('./logger').mainLogger();
const { DbQueryTimeoutError } = require('./errors');

pgCamelCase.inject(pg);
pg.defaults.parseInt8 = true;

function DBClient(connString, poolSize, logger = mainLogger) {
  const connStringSanitized = connString.replace(/postgresql:\/\/(.*)@/, '');

  pg.defaults.poolSize = poolSize;
  logger.debug(`Creating new DB client: ${connStringSanitized}, ${poolSize}`);

  const pool = new pg.Pool({
    connectionString: connString,
  });

  const _dbConnection = () =>
    pool
      .connect()
      .then(client => ({
        pool,
        client,
        done: () => {
          logger.debug(`Closing DB connection: ${connStringSanitized}`);
          client.release();
        },
        execute(query, args) {
          return _dbQuery(this, query, args); // eslint-disable-line no-use-before-define
        },
      }))
      .catch(err => {
        logger.error('DB connection failed');
        throw new Error(err);
      });

  const _dbQuery = (connection, query, args) =>
    connection.client.query(query, args).catch(err => {
      logger.error({ query, error: err }, '[db].[_dbQuery] Query failed');
      throw new Error(err);
    });

  return {
    row: function row(query, args) {
      return co(function* execute() {
        const connection = yield _dbConnection();
        let result = {};
        try {
          result = yield _dbQuery(connection, query, args);
        } finally {
          connection.done();
        }

        if (result.rows.length > 1) {
          throw new Error('More than one row found');
        } else if (result.rows.length < 1) {
          return null;
        } else {
          return result.rows[0];
        }
      });
    },

    rows: function rows(query, args) {
      return co(function* execute() {
        const connection = yield _dbConnection();
        let result = {};
        try {
          result = yield _dbQuery(connection, query, args);
        } finally {
          connection.done();
        }

        if (result.rows.length < 1) {
          return [];
        }

        return result.rows;
      });
    },

    // timed row
    async trow(query, args) {
      let result = {};
      const connection = await _dbConnection();

      try {
        const info = await connection.client.query('SELECT pg_backend_pid()');
        const pid = _.get(info, 'rows[0].pgBackendPid', null);

        const timer = setTimeout(async () => {
          await connection.pool.query('SELECT pg_cancel_backend($1)', [pid]);
        }, config.dbQueryTimeoutInSeconds * 1000);

        // throws user request cancellation error if pg_cancel_backend() is successful
        result = await connection.client.query(query, args);

        clearTimeout(timer);
      } catch (err) {
        logger.error({ query, error: err }, '[db].[timedRow] Query failed');
        if (
          err.message &&
          /canceling statement due to user request/i.test(err.message)
        ) {
          throw new DbQueryTimeoutError(err.message);
        }
        throw new Error(err);
      } finally {
        connection.done();
      }

      if (result.rows.length > 1) {
        throw new Error('More than one row found');
      } else if (result.rows.length < 1) {
        return null;
      } else {
        return result.rows[0];
      }
    },

    // timed rows
    async trows(query, args) {
      let result = {};
      const connection = await _dbConnection();

      try {
        const info = await connection.client.query('SELECT pg_backend_pid()');
        const pid = _.get(info, 'rows[0].pgBackendPid', null);

        const timer = setTimeout(async () => {
          await connection.pool.query('SELECT pg_cancel_backend($1)', [pid]);
        }, config.dbQueryTimeoutInSeconds * 1000);

        // throws user request cancellation error if pg_cancel_backend() is successful
        result = await connection.client.query(query, args);

        clearTimeout(timer);
      } catch (err) {
        logger.error({ query, error: err }, '[db].[timedRows] Query failed');
        if (
          err.message &&
          /canceling statement due to user request/i.test(err.message)
        ) {
          throw new DbQueryTimeoutError(err.message);
        }
        throw new Error(err);
      } finally {
        connection.done();
      }

      if (result.rows.length < 1) {
        return [];
      }

      return result.rows;
    },

    execute: function execute(query, args) {
      return co(function* run() {
        const connection = yield _dbConnection();
        let result = {};
        try {
          result = yield _dbQuery(connection, query, args);
        } finally {
          connection.done();
        }

        return result.rows;
      });
    },

    getConnection: function getConnection() {
      return _dbConnection();
    },
  };
}

module.exports = DBClient;
