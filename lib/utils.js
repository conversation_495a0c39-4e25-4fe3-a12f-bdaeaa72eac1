// eslint-disable-next-line import/no-unresolved
const { parse: csvParse } = require('csv-parse/sync');

/**
 * @param {object} token
 */
const isKeycloakTheIDP = token => {
  // Added this check for unit-test tokens (unit test token is a string === 'unit-test-token')
  if (typeof token !== 'object') {
    return false;
  }

  return (
    token.idp &&
    token.idp === 'keycloak' && // Is the token a keycloak generated one ?
    token.company &&
    token.company.featureFlags &&
    token.company.featureFlags.includes('USE_KEYCLOAK')
  );
};

/**
 * @param {string} oldValue
 */
const sanitizeString = oldValue => '*'.repeat(oldValue.length);

/**
 * @param {object} object
 * @param {Array.<string>} blacklistedProperties
 * @param {Object.<string, function(object): boolean>} conditionalBlacklistedProperties
 */
const deepSanitize = (
  object,
  blacklistedProperties,
  conditionalBlacklistedProperties = null
) => {
  if (!object) return;
  Object.keys(object).forEach(property => {
    if (blacklistedProperties.includes(property)) {
      object[property] = sanitizeString(object[property]); // eslint-disable-line no-param-reassign
    } else if (
      !!conditionalBlacklistedProperties &&
      Object.keys(conditionalBlacklistedProperties).includes(property) &&
      conditionalBlacklistedProperties[property](object)
    ) {
      object[property] = sanitizeString(object[property]); // eslint-disable-line no-param-reassign
    } else if (object[property] instanceof Array) {
      object[property].forEach(item => {
        deepSanitize(item, blacklistedProperties);
      });
    } else if (object[property] instanceof Object) {
      deepSanitize(object[property], blacklistedProperties);
    }
  });
};

const parseCSV = (csvString, options) => csvParse(csvString, options);
const isObjectType = object =>
  typeof object === 'object' && !Array.isArray(object) && object !== null;
const isEmptyObject = object =>
  !!object && isObjectType(object) && !Object.keys(object).length;

module.exports = {
  // eslint-disable-next-line no-promise-executor-return
  sleep: timeout => new Promise(resolve => setTimeout(resolve, timeout)),
  msToTime: ms => {
    // convert 28hrs of miliseconds to 28:00:00 format
    // Note: this will only handle up to 3 hour digits (e.g. HHH:MM:SS)
    const s = Math.floor(((ms / 1000) % 60).toFixed(2));
    const m = Math.floor(((ms / (1000 * 60)) % 60).toFixed(2));
    let h = Math.floor((ms / (1000 * 60 * 60)).toFixed(3));
    h = h.toString().length < 2 ? `0${h}`.slice(-2) : h;
    return `${h}:${`0${m}`.slice(-2)}:${`0${s}`.slice(-2)}`;
  },
  asyncify: generator => () => {
    // eslint-disable-next-line no-undef
    const gen = generator.apply(this, arguments);

    function iterate(result) {
      if (result.done) return Promise.resolve(result.value);

      return Promise.resolve(result.value).then(
        res => iterate(gen.next(res)),
        err => iterate(gen.throw(err))
      );
    }

    try {
      return iterate(gen.next());
    } catch (ex) {
      return Promise.reject(ex);
    }
  },
  safeToString: obj => {
    try {
      return JSON.stringify(obj);
    } catch (err) {
      try {
        return JSON.stringify(String(obj));
      } catch (err2) {
        return JSON.stringify('error serializing log line');
      }
    }
  },
  deepSanitize,
  parseCSV,
  isKeycloakTheIDP,
  isObjectType,
  isEmptyObject,
};
