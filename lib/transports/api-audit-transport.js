/* eslint-disable no-underscore-dangle */
/* eslint-disable class-methods-use-this */
const { pipeline, Transform } = require('stream');
const split = require('split2');

const environment = process.env.ENVIRONMENT;
// eslint-disable-next-line import/no-dynamic-require
const config = require(`../../build/config/${environment}/config`);

const { createDBLogger } = require('../api-audit-db-logger');
const { createKinesisLogger } = require('../api-audit-kinesis-logger');

/**
 * Out-of-process transport for ICS API audit base off pino-pg.
 */
class AuditTransport extends Transform {
  constructor() {
    super();
    // disabling debug logs
    // eslint-disable-next-line no-console
    console.debug = () => {};
    this.loggers = [];

    if (config.dblogger) {
      const poolSize = 1;
      // eslint-disable-next-line global-require
      const db = require('../db')(config.db.write, poolSize, console);
      this.loggers.push(
        createDBLogger({
          ...config.dblogger,
          baseUrl: config.base,
          db,
        })
      );
    }

    if (config.kinesislogger) {
      // eslint-disable-next-line global-require
      const aws = require('../aws');
      this.loggers.push(
        createKinesisLogger({
          ...config.kinesislogger,
          baseUrl: config.base,
          aws,
        })
      );
    }

    process.on('SIGINT', () => this._shutdown());
    process.on('SIGTERM', () => this._shutdown());
  }

  log(record) {
    // eslint-disable-next-line no-restricted-syntax
    for (const logger of this.loggers) {
      logger(record);
    }
  }

  _shutdown() {
    // using console as we are in a logger
    // eslint-disable-next-line no-console
    console.log('Shutting down ics-logger');
    process.exit(0);
  }

  _transform(chunk, encoding, callback) {
    const content = chunk.toString('utf-8');
    let log;

    try {
      log = JSON.parse(content);
    } catch (e) {
      // skip non-json log lines
      return callback(null, `${chunk}\n`);
    }

    try {
      this.log(log);
    } catch (err) {
      return callback(err, null);
    }

    return callback(null, `${chunk}\n`);
  }
}

function transporter() {
  return new AuditTransport();
}

function main() {
  const splitStream = split();
  const transformStream = transporter();

  process.stdin.on('error', err => {
    console.error('Error in input stream:', err);
  });

  splitStream.on('error', err => {
    console.error('Error in split stream:', err);
  });

  transformStream.on('error', err => {
    console.error('Error in transform stream (AuditTransport):', err);
  });

  process.stdout.on('error', err => {
    console.error('Error in output stream:', err);
  });

  pipeline(process.stdin, splitStream, transformStream, process.stdout, err => {
    if (err !== null) {
      console.error(err);
    }
  });
}

process.on('uncaughtException', err => {
  console.error('Error stack:', err);
});

module.exports = { main, transporter };
