/* eslint-disable no-underscore-dangle */
/**
 * ICS version of Restify Audit Logger module by <PERSON>
 * - Add user and company info
 * - Sanitize sensitive information in request and response
 * - Re-write API URL (Forwarded URL)
 * - Extract remote address
 *
 * Copyright 2012 Mark <PERSON>ge, Inc.  All rights reserved.
 */

const assert = require('assert-plus'); // eslint-disable-line import/no-extraneous-dependencies
const pino = require('pino');
const { HttpError } = require('restify-errors');
const jwt = require('jsonwebtoken');
const _ = require('lodash');

const environment = process.env.ENVIRONMENT;
// eslint-disable-next-line import/no-dynamic-require
const config = require(`../build/config/${environment}/config`);
const ipAddressHelper = require('../helpers/ip-address-helper');

// Delete sensitive fields from request or response
function sanitizeData(data) {
  if (!data) return undefined;
  const copy = _.cloneDeep(data);

  const deepSanitize = (object, blacklistedProperties) => {
    if (!object) return;
    Object.keys(object).forEach(property => {
      if (blacklistedProperties.includes(property)) {
        object[property] = '<sanitized>'; // eslint-disable-line no-param-reassign
      } else if (object[property] instanceof Array) {
        object[property].forEach(item => {
          deepSanitize(item, blacklistedProperties);
        });
      } else if (object[property] instanceof Object) {
        deepSanitize(object[property], blacklistedProperties);
      }
    });
  };

  deepSanitize(copy, [
    'password',
    'token',
    'secret',
    'otpURL',
    'qrCodeData',
    'authorization',
    'mfaSecret',
    'challenge',
    'response',
    'x-auth-token',
    'refresh-token',
    'secretKey',
    'passwordHash',
  ]);

  return copy;
}

/**
 * Returns a PinoJS audit logger suitable to be used in a server.on('after')
 * event.  I.e.:
 *
 * server.on('after', restify.auditLogger({ log: myAuditStream }));
 *
 * This logs at the INFO level.
 *
 * @public
 * @function auditLogger
 * @param   {Object}   options at least a Pino logger (log).
 * optionally .server object to emit log. .logBuffer which is a
 * ringbuffer object to store certain amount of log,
 * printLog flag, default is true,
 * if printlog set to false, user need to pass in server object
 * listen to auditlog event to get log information
 * @returns {Function}         to be used in server.after.
 */
function auditLogger(options) {
  assert.object(options, 'options');
  assert.object(options.log, 'options.log');
  assert.optionalObject(options.server, 'options.server');
  assert.optionalObject(options.logBuffer, 'options.logBuffer');
  assert.optionalBool(options.printLog, 'options.printLog');

  // use server object to emit log data
  const { server } = options;

  // use logMetrics ringbuffer to store certain period of log records
  const logMetrics = options.logBuffer;

  // default always print log
  let { printLog } = options;
  if (typeof printLog === 'undefined') {
    printLog = true;
  }

  let errSerializer = pino.stdSerializers.err;

  if (options.log.serializers && options.log.serializers.err) {
    errSerializer = options.log.serializers.err;
  }

  const customSerializers = {
    err: errSerializer,
    req: req => {
      if (!req) {
        return false;
      }

      const timers = {};
      (req.timers || []).forEach(time => {
        const t = time.time;
        timers[time.name] = Math.floor(1000000 * t[0] + t[1] / 1000);
      });

      const remoteAddress = ipAddressHelper.getIPFromHeader(req);

      let forwardedUrl = req.url;
      if (req.url && config.base) {
        forwardedUrl = req.url.replace(`/${config.base}/`, '/rest/v1/');
      }

      return {
        // account for native and queryParser plugin usage
        user: sanitizeData(req.user),
        query: typeof req.query === 'function' ? req.query() : req.query,
        method: req.method,
        url: req.url,
        forwardedUrl,
        headers: sanitizeData(req.headers),
        httpVersion: req.httpVersion,
        trailers: req.trailers,
        version: req.version(),
        body: options.body === true ? req.body : undefined,
        timers,
        clientClosed: req.clientClosed,
        remoteAddress,
      };
    },
    res: res => {
      if (!res) {
        return false;
      }

      let body;

      if (options.body === true) {
        if (res._body instanceof HttpError) {
          body = res._body.body;
        } else {
          body = res._body;
        }
      }

      return {
        resourceOwner: sanitizeData(res.resourceOwner),
        statusCode: res.statusCode,
        headers: sanitizeData(res._headers),
        trailer: res._trailer || false,
        body: sanitizeData(body),
      };
    },
  };
  const childLog = options.log.child(
    { audit: true },
    { serializers: customSerializers }
  );

  function audit(req, res, route, err) {
    let latency = res.get('Response-Time');
    const timestamp = new Date().getTime();

    if (typeof latency !== 'number') {
      latency = Date.now() - req._time;
    }

    const obj = {
      remoteAddress: req.connection.remoteAddress,
      remotePort: req.connection.remotePort,
      req_id: req.getId(),
      req,
      res,
      err,
      latency,
      secure: req.secure,
      _audit: true,
    };

    // TODO: Marcelo - Check this logic (create a ticket to review this as tech debt)
    if (req.user) {
      obj.userId = req.user.sub || req.user.id;
      if (req.user.company) {
        obj.companyId = req.user.company.id;
      }
      if (req.user.impersonator) {
        obj.impersonatorId = req.user.impersonator.sub;
      }
    } else if (req._userId || req._companyId) {
      obj.userId = req._userId;
      obj.companyId = req._companyId;
    } else if (res._body && res._body.token) {
      const token = jwt.decode(res._body.token);
      obj.userId = token.sub;
      if (token.company) {
        obj.companyId = token.company.id;
      }
    }

    let base = '';
    if (config.base) {
      base = `/${config.base}`;
    }

    const blacklistedServices = [
      { method: 'PUT', url: new RegExp(`^${base}/devices/([0-9]+)/data$`) },
    ];
    const blacklistedService = blacklistedServices.find(
      service =>
        service.method === obj.req.method && service.url.test(obj.req.url)
    );

    // ICS-7817 / DEVOP-5764: Body should be logged only when request fails to complete i.e, Invalid JSON / aws errors / etc.
    if (req.body) {
      if (!blacklistedService) {
        obj.req.body = sanitizeData(obj.req.body);
      } else {
        obj.req.body = res.statusCode >= 300 ? sanitizeData(obj.req.body) : {};
      }
    }
    /* eslint-enable */

    if (printLog) {
      childLog.info(obj, '[Audit] response code handled: %d.', res.statusCode);
    }

    if (logMetrics) {
      obj.timestamp = timestamp;
      logMetrics.write(obj);
    }

    if (server) {
      server.emit('auditlog', obj);
    }

    return true;
  }

  return audit;
}

module.exports = auditLogger;
