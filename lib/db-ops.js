const env = require('../env');

const { config } = env;
// Setup DB connections
if (!config.db.poolSize) {
  throw new Error('Missing db.poolSize in config');
}

const readDB = require('./db')(config.db.read, config.db.poolSize);
const writeDB = require('./db')(config.db.write, config.db.poolSize);
const replicaDB = require('./db')(config.db.replica, config.db.poolSize);

module.exports = {
  read: readDB,
  write: writeDB,
  replica: replicaDB,
};
