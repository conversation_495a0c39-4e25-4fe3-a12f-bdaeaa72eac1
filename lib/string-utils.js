const url = require('url');
const path = require('path');

/**
 * Returns true if both the strings are equal ignoring cases otherwise false
 * @param {String} string1
 * @param {String} string2
 * @returns {Boolean}
 */
function equalsIgnoreCase(string1, string2) {
  return string1 && string2
    ? string1.toLowerCase() === string2.toLowerCase()
    : string1 === string2;
}

/**
 * Returns the path retrieved from the string url parameter
 * @param {String} stringUrl
 * @returns {String}
 */
function getPathName(stringUrl) {
  const pathName = url.parse(stringUrl).pathname.substr(1);
  return pathName;
}

/**
 * Returns the file name retrieved from the string path parameter
 * @param {String} pathName
 * @returns {String}
 */
function getFileName(pathName) {
  const fileName = path.basename(pathName);
  return fileName;
}

module.exports = {
  equalsIgnoreCase,
  getPathName,
  getFileName,
};
