/* eslint-disable no-underscore-dangle */
/**
 * This is a crude duplication of the Restify Plugin rullResponse
 * Except this version doesn't mess with CORS related headers (as these are handled elsewhere)
 *
 * // Copyright 2012 Mark <PERSON>, Inc.  All rights reserved.
 * https://github.com/restify/plugins/blob/master/lib/plugins/fullResponse.js
 */

const crypto = require('crypto');

function setHeaders(req, res) {
  let hash;
  const now = new Date();

  if (!res.getHeader('Connection')) {
    res.setHeader('Connection', req.isKeepAlive() ? 'Keep-Alive' : 'close');
  }

  if (res._data && !res.getHeader('Content-MD5')) {
    hash = crypto.createHash('md5');
    hash.update(res._data);
    res.setHeader('Content-MD5', hash.digest('base64'));
  }

  if (!res.getHeader('Date')) {
    res.setHeader('Date', now.toUTCString());
  }

  if (res.etag && !res.getHeader('Etag')) {
    res.setHeader('Etag', res.etag);
  }

  if (!res.getHeader('Server')) {
    res.setHeader('Server', res.serverName);
  }

  if (res.version && !res.getHeader('Api-Version')) {
    res.setHeader('Api-Version', res.version);
  }

  if (!res.getHeader('Request-Id')) {
    res.setHeader('Request-Id', req.getId());
  }

  if (!res.getHeader('Response-Time')) {
    res.setHeader('Response-Time', now.getTime() - req._time);
  }
}

function additionalHeaders() {
  function restifyResponseHeaders(req, res, next) {
    res.once('header', () => setHeaders(req, res));

    return next();
  }

  return restifyResponseHeaders;
}

// /--- Exports

module.exports = additionalHeaders;
