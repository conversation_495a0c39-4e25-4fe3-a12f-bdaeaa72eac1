// eslint-disable-next-line import/no-extraneous-dependencies
const assert = require('assert-plus');

const { safeToString } = require('./utils');

const formatPayload = item => ({
  time: item.time,
  route: `${item.req.method} ${item.req.forwardedUrl}`,
  userId: item.userId,
  companyId: item.companyId,
  request: item.req.body,
  response: item.res.body,
  statusCode: item.res.statusCode,
  requestId: item.req_id,
  remoteAddress: item.remoteAddress,
});

/**
 * API Audit alarms Kinesis logger
 *
 * @param {Object} opts
 */
function createKinesisLogger(opts) {
  assert.object(opts.aws, 'options.aws');
  assert.string(opts.streamName, 'options.streamName');

  const { aws } = opts;
  const batchSize = opts.batchSize || 500;
  const syncInterval = opts.syncInterval || 1000;
  const kinesisLoggerStream = opts.streamName;
  const { baseUrl } = opts;

  const unsynced = [];
  async function syncLogs() {
    // Short-circuit if there is nothing to send
    if (unsynced.length === 0) {
      return;
    }

    const logLines = unsynced.splice(0, batchSize);
    const records = logLines.map(item => {
      const payload = formatPayload(item);
      return {
        Data: safeToString(payload),
        PartitionKey: item.companyId || 'UNDEFINED',
      };
    });

    await aws.kinesisPutRecords({
      Records: records,
      StreamName: kinesisLoggerStream,
    });
  }

  function sync(interval) {
    // Avoid using setInterval as they could overlap with asynchronous processing
    setTimeout(async () => {
      try {
        await syncLogs();
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error(err); // use console as we are in a logger
      }
      sync(interval);
    }, interval);
  }

  // Start sync
  sync(syncInterval);

  return record => {
    if (
      record.name !== 'audit' ||
      record.req.method === 'GET' ||
      record.req.method === 'OPTIONS'
    ) {
      return;
    }

    let base = '';
    if (baseUrl) {
      base = `/${baseUrl}`;
    }

    const blacklistedServices = [
      { method: 'PUT', url: new RegExp(`^${base}/devices/([0-9]+)/data$`) },
      { method: 'POST', url: new RegExp(`^${base}/jobs/expire$`) },
      { method: 'POST', url: new RegExp(`^${base}/devices/sync$`) },
      { method: 'POST', url: new RegExp(`^${base}/reports/`) },
      { method: 'POST', url: new RegExp(`^${base}/software/get-files$`) },
      { method: 'POST', url: new RegExp(`^${base}/software/delete-files$`) },
    ];

    const blacklistedService = blacklistedServices.find(
      service =>
        service.method === record.req.method && service.url.test(record.req.url)
    );

    if (blacklistedService) {
      return;
    }

    if (
      record.req.user &&
      record.req.user.roles &&
      record.req.user.roles.includes('DEVICE')
    ) {
      return;
    }

    unsynced.push(record);
  };
}

module.exports = { createKinesisLogger, formatPayload };
