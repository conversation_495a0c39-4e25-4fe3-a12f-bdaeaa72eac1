const bcrypt = require('bcryptjs');

module.exports = {
  generatePlainPassword: (length, charSet) => {
    // eslint-disable-next-line no-param-reassign
    charSet =
      charSet ||
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'; // eslint-disable-line no-param-reassign
    let randomString = '';
    for (let i = 0; i < length; i++) {
      const randomPoz = Math.floor(Math.random() * charSet.length);
      randomString += charSet.substring(randomPoz, randomPoz + 1);
    }
    return randomString;
  },
  createHash: plainPassword =>
    new Promise(resolve =>
      // eslint-disable-next-line no-promise-executor-return
      bcrypt.genSalt(10, (err1, salt) => {
        if (err1) throw err1;

        return bcrypt.hash(plainPassword, salt, (err2, hash) => {
          if (err2) throw err2;

          return resolve(hash);
        });
      })
    ),
  verify: (plainPassword, hashedPassword) =>
    new Promise(resolve =>
      // eslint-disable-next-line no-promise-executor-return
      bcrypt.compare(plainPassword, hashedPassword, (err, res) => {
        if (err) throw err;

        return resolve(res);
      })
    ),
};
