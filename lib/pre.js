const _ = require('lodash');
const { JSONPath } = require('jsonpath-plus');
const co = require('co');
const Joi = require('joi');
const restify = require('restify');

const jwt = require('./jwt');
const errorHandler = require('./errorhandler');
const { roles: rolesConstant } = require('./app-constants');
const { isKeycloakTheIDP } = require('./utils');

function validateRequest(
  type,
  rawSchema,
  allowUnknown = true,
  allowOverride = false
) {
  return (req, res, next) => {
    const schema = rawSchema.isJoi ? rawSchema : Joi.object().keys(rawSchema);
    return Joi.validate(
      req[type],
      schema,
      {
        allowUnknown,
        presence: 'required',
      },
      (err, value) => {
        if (err) {
          return next(
            new restify.BadRequestError({
              body: {
                code: 'BadRequestError',
                message: err.message,
                context: err.details,
              },
            })
          );
        }

        if (allowOverride) {
          // eslint-disable-next-line no-param-reassign
          req[type] = value;
        }
        return next();
      }
    );
  };
}

module.exports = {
  attachRole: () => (req, _res, next) => {
    if (req.user) {
      req.user.getRoles = () => req.user.roles;
      req.user.hasRole = roleName =>
        // eslint-disable-line no-param-reassign
        _.find(req.user.roles, role => role === roleName);
    }
    next();
  },
  attachHeaders: () => (req, res, next) => {
    if (
      req.user &&
      !req.user.company &&
      req.user.roles.includes(rolesConstant.ICS_SYSTEM)
    ) {
      req.user.company = { id: req.headers.tenantid, featureFlags: [] }; // eslint-disable-line no-param-reassign
    }
    next();
  },
  validateBody: (schema, allowUnknown = true, allowOverride = false) =>
    validateRequest('body', schema, allowUnknown, allowOverride),
  validateQuery: (schema, allowUnknown = true, allowOverride = false) =>
    validateRequest('query', schema, allowUnknown, allowOverride),
  validateHeaders: (schema, allowUnknown = true, allowOverride = false) =>
    validateRequest('headers', schema, allowUnknown, allowOverride),
  validateParams: (schema, allowUnknown = true, allowOverride = false) =>
    validateRequest('params', schema, allowUnknown, allowOverride),
  validateFormFields: schema => validateRequest('params', schema),
  validateFormFiles: schema => validateRequest('files', schema),
  requiresRole: roles => (req, _res, next) => {
    req.log.info(`[Authz].[RequiresFeatureFlag] User: ${req.user.fullName}.`);

    if (roles.length === 0) {
      return next();
    }

    // check if user is a member of this role
    const userRoles = req.user.getRoles();
    if (userRoles.includes(rolesConstant.ICS_SYSTEM)) {
      return next();
    }
    // eslint-disable-next-line no-restricted-syntax
    for (const role of roles) {
      const userRole = _.find(userRoles, uRole => uRole === role);
      if (userRole) {
        return next();
      }
    }
    return next(new restify.ForbiddenError('Unauthorized User'));
  },
  requiresConfig: (jsonPaths, config) => (req, res, next) => {
    const missingConfig = jsonPaths.filter(path => {
      const value = JSONPath({ path, json: config, wrap: false });
      return value === null || value === undefined;
    });

    if (missingConfig.length > 0) {
      return next(
        new restify.errors.InternalServerError(
          `Missing required config: ${missingConfig.join(', ')}`
        )
      );
    }

    return next();
  },
  requiresFeatureFlag: featureFlags => (req, _res, next) => {
    req.log.info(`[Authz].[RequiresFeatureFlag] User: ${req.user.fullName}.`);

    // Exit if no feature flags are specified
    if (featureFlags.length === 0) {
      return next();
    }

    // Exit if user is ICS_SYSTEM
    const userRoles = req.user.getRoles();
    if (userRoles.includes('ICS_SYSTEM')) {
      return next();
    }

    // Throw if user does not have a company associated
    const { company } = req.user;
    if (!company) {
      return next(new restify.ForbiddenError('Unauthorized User'));
    }

    // Exit if a feature flag is contained in the list of company feature flags
    const companyFeatureFlags = company.featureFlags;
    // eslint-disable-next-line no-restricted-syntax
    for (const featureFlag of featureFlags) {
      const hasFeatureFlag = companyFeatureFlags.includes(featureFlag);
      if (hasFeatureFlag) {
        return next();
      }
    }

    return next(new restify.ForbiddenError('Unauthorized User'));
  },
  refreshToken: () => (req, res, next) => {
    const usersHelper = require('../helpers/users-helper'); // eslint-disable-line global-require

    return co(function* execute() {
      if (!req.user) {
        return next();
      }

      // Attach user id to req as username for use by
      // throttle plugin
      // eslint-disable-next-line no-param-reassign
      req.username = req.user.sub;

      if (req.user.getRoles().length) {
        if (req.params.autoPoll) {
          // Don't refresh the token if URL contains `autoPoll=true` query string
          return next();
        }

        if (isKeycloakTheIDP(req.user)) {
          // TODO: Marcelo - refresh token handled by the FE
          // Remove all of this code once fully migrated to KC
          return next();
        }

        // check if need to refresh the token (token is older than 1 minute)
        if (Date.now() - req.user.iat * 1000 >= 60000) {
          if (req.user.roles.includes('DEVICE')) {
            const { companyId } = req.user;
            const token = jwt.createDeviceToken(req.user.sub, companyId);
            res.setHeader('Refresh-Token', token);
          } else {
            req.log.info(
              `[Authz].[RefreshToken] re-issue token for user: ${req.user.fullName}.`
            );

            const user = yield usersHelper.getJwtTokenUser(req.user.sub, [1]);

            if (!user) {
              return next(new restify.UnauthorizedError('Not authorized'));
            }

            const token = jwt.createToken(user);
            res.setHeader('Refresh-Token', token);
          }
        }
      }
      return next();
    }).catch(errorHandler.onError(req, res, next));
  },
  parseFormFields: formFields => (req, res, next) => {
    Object.keys(req.params)
      .filter(k => formFields.includes(k))
      .forEach(k => {
        // eslint-disable-next-line no-param-reassign
        req.params[k] = JSON.parse(req.params[k] || null);
      });

    return next();
  },
};
