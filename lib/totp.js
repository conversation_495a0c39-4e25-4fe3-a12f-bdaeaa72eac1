const speakeasy = require('speakeasy');
const { auth } = require('../env').config;
const qrCode = require('./qrcode');

const createTotp = async req => {
  try {
    const secret = speakeasy.generateSecret({
      name: req.user.email,
      length: 16,
      symbols: false,
      otpauth_url: true,
    });

    const issuer = encodeURIComponent(auth.issuer);
    secret.otpauth_url = `${secret.otpauth_url}&issuer=${issuer}`;

    const qrCreate = qrCode.qrcode(10, 'Medium');
    qrCreate.addData(secret.otpauth_url);
    qrCreate.make();
    secret.image = qrCreate.createRawGif();

    return secret;
  } catch (err) {
    req.log.error(
      { error: err },
      `[Totp].[CreateTotp] error generating qrCode: ${err.message}.`
    );
    throw err;
  }
};

const validateTotp = async (req, secret, verifyCode) => {
  try {
    if (
      speakeasy.totp.verify({
        algorithm: 'sha1',
        digits: 6,
        encoding: 'base32',
        secret,
        step: 30,
        token: verifyCode,
        window: 2,
      })
    ) {
      return true;
    }
    req.log.info(
      {
        verifyCode,
        userId: req?.user?.sub,
        userEmail: req?.user?.email,
        companyId: req?.user?.company?.id,
      },
      '[Totp].[ValidateTotp] Totp provided verifyCode did not match Totp secret.'
    );
    return false;
  } catch (shouldNeverError) {
    req.log.error(
      {
        error: shouldNeverError,
        verifyCode,
      },
      `[Totp].[ValidateTotp] speakeasy failed badly: ${shouldNeverError.message}.`
    );
    return false;
  }
};

const funcvalidateTotp = async (secret, verifyCode) => {
  try {
    if (
      speakeasy.totp.verify({
        algorithm: 'sha1',
        digits: 6,
        encoding: 'base32',
        secret,
        step: 30,
        token: verifyCode,
        window: 2,
      })
    ) {
      return true;
    }

    return false;
  } catch (e) {
    return false;
  }
};

module.exports = { createTotp, validateTotp, funcvalidateTotp };
