/* eslint-disable no-console, global-require, no-param-reassign */
const { config } = require('../../env');
const sshTunnel = require('./ssh-tunnel');
const { replaceConnectionStringAddressAndPort } = require('./ssh-regex-helper');

/**
 * @param {Promise<void>} promiseToBeAwaited
 * @param {Object.<string, function(...any[]):Promise<any>>} dbFunctionSet
 * @returns {Object.<string, function(...any[]):Promise<any>>}
 */
const wrapDbFunctionSet = (dbFunctionSet, promiseToBeAwaited) => {
  /** @type {Object.<string, function(...any[]):Promise<any>>} */
  const wrappedDbFunctions = {};
  Object.keys(dbFunctionSet).forEach(fnKey => {
    wrappedDbFunctions[fnKey] = async (...args) => {
      await promiseToBeAwaited;
      return dbFunctionSet[fnKey](...args);
    };
  });
  return wrappedDbFunctions;
};

const init = server => {
  const { tunnelAddress, readTunnelPort, writeTunnelPort } = config.dbSshTunnel;

  // Create new connections now pointing to the tunnel entrance
  const readDB = require('../../lib/db')(
    replaceConnectionStringAddressAndPort(
      config.db.read,
      tunnelAddress,
      readTunnelPort
    ),
    config.db.poolSize
  );
  const writeDB = require('../../lib/db')(
    replaceConnectionStringAddressAndPort(
      config.db.write,
      tunnelAddress,
      writeTunnelPort
    ),
    config.db.poolSize
  );

  const { readTunnelPromise, writeTunnelPromise } = sshTunnel.initTunnels();

  // Each respective set of DB operations will only be usable once the respective tunnel is running and ready
  server.db.read = wrapDbFunctionSet(readDB, readTunnelPromise);
  server.db.write = wrapDbFunctionSet(writeDB, writeTunnelPromise);
};

module.exports = {
  init,
};
