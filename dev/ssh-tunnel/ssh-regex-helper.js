const connectionStringPattern = /^(.*@)(.*?)(:?)(\d*)(\/.*)$/;

const beginningPartIndex = 1; // e.g. "postgresql://user:pass@"
const destinationAddressIndex = 2; // e.g. "db-olive.invencocloud.com"
const destinationPortIndex = 4; // e.g. "5432"
const endPartIndex = 5; // e.g. "/tms_server_db"

const postgresDefaultPort = 5432;

/**
 * @param {string} originalConnectionString
 * @param {string} newAddress
 * @param {string} newPort
 * @returns {string}
 */
const replaceConnectionStringAddressAndPort = (
  originalConnectionString,
  newAddress,
  newPort
) => {
  const connectionStringMatches = originalConnectionString.match(
    connectionStringPattern
  );
  if (!connectionStringMatches) {
    throw new Error('No connection string regex matches');
  }

  const { [beginningPartIndex]: beginningPart, [endPartIndex]: endPart } =
    connectionStringMatches;

  return `${beginningPart}${newAddress}:${newPort}${endPart}`;
};

/**
 * @param {string} connectionString
 * @returns {{destinationAddress:string,destinationPort:number}}
 */
const extractConnectionStringAddressAndPort = connectionString => {
  const connectionStringMatches = connectionString.match(
    connectionStringPattern
  );
  if (!connectionStringMatches) {
    throw new Error('No connection string regex matches');
  }

  const {
    [destinationAddressIndex]: destinationAddress,
    [destinationPortIndex]: destinationPortString,
  } = connectionStringMatches;

  const destinationPort = destinationPortString
    ? parseInt(destinationPortString, 10)
    : postgresDefaultPort;

  return { destinationAddress, destinationPort };
};

module.exports = {
  replaceConnectionStringAddressAndPort,
  extractConnectionStringAddressAndPort,
};
