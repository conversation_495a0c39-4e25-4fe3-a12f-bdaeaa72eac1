/* eslint-disable no-console */
const fs = require('fs');
// eslint-disable-next-line import/no-extraneous-dependencies, import/no-unresolved
const { createTunnel } = require('tunnel-ssh');
const { config } = require('../../env');
const { extractConnectionStringAddressAndPort } = require('./ssh-regex-helper');

/**
 * @param {import("tunnel-ssh").TunnelOptions} tunnelOptions
 * @param {import("net").ListenOptions} serverOptions
 * @param {import("ssh2").ConnectConfig} sshOptions
 * @param {import("tunnel-ssh").ForwardOptions} forwardOptions
 * @param {string} type
 */
const createTunnelOfType = async (
  tunnelOptions,
  serverOptions,
  sshOptions,
  forwardOptions,
  type
) => {
  const [tunnelServer, client] = await createTunnel(
    tunnelOptions,
    serverOptions,
    sshOptions,
    forwardOptions
  );

  console.log(`SSH DB ${type} Tunnel: Forwarding to ${forwardOptions.dstAddr}:${forwardOptions.dstPort} is open
        Listening on ${forwardOptions.srcAddr}:${forwardOptions.srcPort}
        Tunelling via ${sshOptions.host}:${sshOptions.port}`);

  tunnelServer.on('connection', connection => {
    console.log(
      `SSH DB ${type} Tunnel: New connection from ${connection.remoteAddress}:${connection.remotePort}`
    );
  });

  tunnelServer.on('error', e => {
    console.error(e);
  });

  client.on('error', e => {
    console.error(e);
  });
};

/**
 * @returns {{readTunnelPromise:Promise.<void>,writeTunnelPromise:Promise.<void>}}
 */
const initTunnels = () => {
  const { dbSshTunnel } = config;

  const tunnelOptions = {
    autoClose: dbSshTunnel.autoClose,
  };

  const readServerOptions = {
    host: dbSshTunnel.tunnelAddress,
    port: dbSshTunnel.readTunnelPort,
  };

  const writeServerOptions = {
    host: dbSshTunnel.tunnelAddress,
    port: dbSshTunnel.writeTunnelPort,
  };

  const sshOptions = {
    host: dbSshTunnel.host,
    port: dbSshTunnel.port,
    username: dbSshTunnel.userName,
    privateKey: fs.readFileSync(dbSshTunnel.privateKeyPath),
    passphrase: dbSshTunnel.privateKeyPassphrase,
  };

  const {
    destinationAddress: readDestinationAddress,
    destinationPort: readDestinationPort,
  } = extractConnectionStringAddressAndPort(config.db.read);
  const readForwardOptions = {
    srcAddr: dbSshTunnel.tunnelAddress,
    srcPort: dbSshTunnel.readTunnelPort,
    dstAddr: readDestinationAddress,
    dstPort: readDestinationPort,
  };

  const {
    destinationAddress: writeDestinationAddress,
    destinationPort: writeDestinationPort,
  } = extractConnectionStringAddressAndPort(config.db.write);
  const writeForwardOptions = {
    srcAddr: dbSshTunnel.tunnelAddress,
    srcPort: dbSshTunnel.writeTunnelPort,
    dstAddr: writeDestinationAddress,
    dstPort: writeDestinationPort,
  };

  // Create tunnels pointing to the original destination
  return {
    readTunnelPromise: createTunnelOfType(
      tunnelOptions,
      readServerOptions,
      sshOptions,
      readForwardOptions,
      'Read'
    ),
    writeTunnelPromise: createTunnelOfType(
      tunnelOptions,
      writeServerOptions,
      sshOptions,
      writeForwardOptions,
      'Write'
    ),
  };
};

module.exports = {
  initTunnels,
};
