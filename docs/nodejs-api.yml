---
swagger: '2.0'
info:
  description: 'This is the REST API documentation for the Invenco Cloud Services application'
  version: 1.0.40
  title: Invenco Cloud Services
  termsOfService: 'http://www.invenco.com/products/terminal-management-system/'
host: api.invencocloud.com
schemes:
  - https
consumes:
  - application/json
produces:
  - application/json
basePath: /rest/v1

# #############################################################################
# PATHS
# #############################################################################
paths:
  /bridge/devices:
    post:
      tags:
        - bridge
      summary: Create a target device
      description:
        Creating a new device in ANY site regardless the access rights. Must
        be system user and BRIDGE_APP role required
      parameters:
        - '$ref': '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: Representation of the target device resource to be created
          required: true
          schema:
            '$ref': '#/definitions/AddDeviceByBridge'
      responses:
        '200':
          description: Device successfully created
          schema:
            '$ref': '#/definitions/TargetResponseDto'
        '400':
          description: Bad request. Either Device type or product type id is mandatory
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          '$ref': '#/responses/authenticationFailed'
        '403':
          description: No permission
          schema:
            $ref: '#/definitions/ErrorMessage'
        '409':
          description: It is generally saying some attribute is conflict with current one (can be SN ...)
          schema:
            $ref: '#/definitions/ErrorMessage'
        '500':
          '$ref': '#/responses/internalServerError'
  /bridge/devices/{serialNumber}:
    put:
      tags:
        - bridge
      summary: Update a device/target
      description:
        Allows updating a new device/target in ANY site regardless the access
        rights. Must be system user and BRIDGE_APP role required
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: serialNumber
          in: path
          description: The serial number of the device to be updated
          required: true
          type: string
        - '$ref': '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: Representation of the target resource to be updated
          required: true
          schema:
            '$ref': '#/definitions/DeviceUpdateDto'
      responses:
        '200':
          description: Device successfully updated
          schema:
            '$ref': '#/definitions/TargetResponseDto'
        '400':
          description: Bad request. Either Device type or product type id is mandatory
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          '$ref': '#/responses/authenticationFailed'
        '403':
          description: No permission
          schema:
            $ref: '#/definitions/ErrorMessage'
        '409':
          description: It is generally saying some attribute is conflict with current one (can be SN ...)
          schema:
            $ref: '#/definitions/ErrorMessage'
        '500':
          '$ref': '#/responses/internalServerError'
    get:
      tags:
        - bridge
      summary: Get a device/target
      description:
        Allows retrieving a device/target in ANY site regardless the access
        rights. Must be system user and BRIDGE_APP role required
      produces:
        - application/json
      parameters:
        - name: serialNumber
          in: path
          description: The serial number of the device to be retrieved
          required: true
          type: string
        - '$ref': '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Device successfully retrieved
          schema:
            '$ref': '#/definitions/TargetBridgeDto'
        '401':
          '$ref': '#/responses/authenticationFailed'
        '403':
          description: No permission
          schema:
            $ref: '#/definitions/ErrorMessage'
        '500':
          '$ref': '#/responses/internalServerError'
  /alarms:
    get:
      tags:
        - alarms
      summary: Get all the alarms
      description: A list of all the active alarms
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: All alarms retrieved successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/AlarmResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
    post:
      tags:
        - alarms
      summary: Create a new Alarm
      description: Create a new alarm in you terminal management system. Admin role required
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: 'Alarm payload object that will be added. Name must be unique. At least one rule, one notification, and one subject in the payload'
          required: true
          schema:
            $ref: '#/definitions/AlarmCreate'
      responses:
        '200':
          description: New rollout successfully created
          schema:
            $ref: '#/definitions/AlarmResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '460':
          description: Duplicated alarm name
        '500':
          $ref: '#/responses/internalServerError'
  '/alarms/{alarmId}':
    delete:
      tags:
        - alarms
      summary: Delete an alarm resource
      description: Delete the alarm by alarm id. Admin role required
      parameters:
        - name: alarmId
          in: path
          description: The id of the alarm
          required: true
          type: integer
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '204':
          description: Alarm that represents by the alarm Id deleted successfully
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Not found. Wrong alarm id
        '500':
          $ref: '#/responses/internalServerError'
    put:
      tags:
        - alarms
      summary: Update an alarm resource
      description: Update the alarm. Admin role required
      parameters:
        - name: alarmId
          in: path
          description: The id of the alarm
          required: true
          type: integer
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: 'Alarm payload object that will be updated. Name must be unique. Active has to be set. At least one rule, one notification, and once subject in the payload'
          required: true
          schema:
            $ref: '#/definitions/AlarmUpdate'
      responses:
        '200':
          description: The targeted alarm resource successfully updated
          schema:
            $ref: '#/definitions/AlarmResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Not found. Wrong alarm id
        '500':
          $ref: '#/responses/internalServerError'
    get:
      tags:
        - alarms
      summary: Get an alarm resource by alarm id
      description: Get the alarm resource represented by the alarm id
      parameters:
        - name: alarmId
          in: path
          description: The id of the alarm
          required: true
          type: integer
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: The targeted alarm resource successfully retrieved
          schema:
            $ref: '#/definitions/AlarmResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Not found. Wrong alarm id
        '500':
          $ref: '#/responses/internalServerError'
  '/alarms/count-per-type':
    get:
      tags:
        - alarms
      summary: Get the count of each alarm type
      description: Get the count of each alarm type per company
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Count of each alarm type
          schema:
            type: array
            items:
              type: object
              properties:
                count:
                  type: integer
                  description: Count of alarm per site
                code:
                  type: string
                  description: Alarm type/code
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  '/alarms/rules':
    get:
      tags:
        - alarms
      summary: Get list of available alarm rules/triggers
      description: Get list of available alarm rules/triggers
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: List of available alarm rules/triggers
          schema:
            type: array
            items:
              type: object
              properties:
                id:
                  type: integer
                  description: Id
                name:
                  type: string
                  description: Name
                type:
                  type: string
                  description: Type
                'description':
                  type: string
                  description: Description
                code:
                  type: string
                  description: Code
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /docs/user-guide:
    get:
      tags:
        - docs
      summary: Returns the user guide documentation of the current release
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        200:
          description: A pdf file of the userguide
        404:
          description: The pdf is missing or inaccessible
        401:
          description: User is not authorized
  /devices/alarmrules:
    put:
      tags:
        - devices
      summary: Modify settings for devices alarm rules
      description: Modify (create if does not exist, update when it does) settings for devices alarm rules
      consumes:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: alarm rules settings payload
          in: body
          required: true
          schema:
            type: array
            items:
              $ref: '#/definitions/AlarmRulesSettings'
      responses:
        '200':
          description: Alarm rules settings modified sucessfully
          schema:
            type: array
            items:
              $ref: '#/definitions/AlarmRulesSettingsItem'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: One or more entity (site or device) specified in payload could not be found
        '500':
          $ref: '#/responses/internalServerError'
  /devices/{id}/alarmrules:
    get:
      tags:
        - devices
      summary: Get alarm rules settings for device
      description: Return alarm rules settings for the given device
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/deviceIdParam'
      responses:
        '200':
          description: Alarm rules settings retrieved sucessfully
          schema:
            $ref: '#/definitions/AlarmRulesSettingsItem'
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: device could not be found
        '500':
          $ref: '#/responses/internalServerError'
  /sites/{id}/devices/alarmrules:
    get:
      tags:
        - devices
      summary: Get alarm rules settings for devices in a site
      description: Return alarm rules settings for the devices in the given site
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/deviceIdParam'
      responses:
        '200':
          description: Alarm rules settings retrieved sucessfully
          schema:
            type: array
            items:
              $ref: '#/definitions/AlarmRulesSettingsItem'
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: device could not be found
        '500':
          $ref: '#/responses/internalServerError'
  /devices/{id}/media/stats:
    get:
      tags:
        - devices
      summary: Get device media statistics for last 24 hours
      description: Return device media statistics for last 24 hours
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/deviceIdParam'
      responses:
        '200':
          description: Device media statistics retrieved sucessfully
          schema:
            $ref: '#/definitions/DeviceMediaStats'
        '400':
          $ref: '#/responses/badPayloadOrParameters'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          $ref: '#/responses/accessDenied'
        '404':
          description: device could not be found
        '500':
          $ref: '#/responses/internalServerError'
  /devices/register:
    post:
      tags:
        - devices
      summary: Register a Device device with TMS
      description:
        Allow a Device to retrieve its secret key based on its type and
        registration key (serial number of mac address). The server will only
        respond if the device is still within its register window.
      parameters:
        - name: body
          in: body
          description: Registration request payload
          required: true
          schema:
            $ref: '#/definitions/Registration'
      produces:
        - application/json
      consumes:
        - application/json
      responses:
        '200':
          description: Registration response payload
          schema:
            $ref: '#/definitions/Authentication'
        '404':
          description:
            Registration was not succesfull (error code and reason are not
            specified on purpose)
        '500':
          $ref: '#/responses/internalServerError'
  /devices/authenticate:
    post:
      tags:
        - devices
      summary: Authenticate a device
      description:
        Authenticate a device and return a JWT token. This API allows a device
        to use the Rest API
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: authentication payload
          in: body
          description: Credentials for the device
          required: true
          schema:
            $ref: '#/definitions/Authentication'
      responses:
        '200':
          description: Device has been successfully authenticated.
          schema:
            $ref: '#/definitions/DeviceAuthenticationResponse'
        '401':
          description: Device unauthorised
        '500':
          $ref: '#/responses/internalServerError'
  '/devices/{id}/alarms':
    get:
      tags:
        - devices
      summary: Get all alarms for a device
      description: Get all alarms for a device
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/deviceIdParam'
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Device alarms retrieved successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/DeviceAlarm'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /devices/file:
    post:
      tags:
        - automation
      summary: Creates a new device file
      description: Creates a new device file
      consumes:
        - application/json
      produces:
        - application/json
      responses:
        '200':
          description: The created device file
          schema:
            $ref: '#/definitions/DeviceFileCreateResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  '/sites/{id}/alarms':
    get:
      tags:
        - sites
      summary: Get all alarms for a site
      description: Get all alarms for a site
      consumes:
        - application/json
      parameters:
        - $ref: '#/parameters/siteIdParam'
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Get site alarms processed successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/SiteAlarm'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Cannot find site alarms
        '500':
          $ref: '#/responses/internalServerError'
  '/sites/{id}/devices/alarms':
    get:
      tags:
        - sites
      summary: Get alarms of all devices at a specific site
      description: Get alarms of all devices at a specific site
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/siteIdParam'
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Site devices alarms retrieved successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/DeviceAlarm'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /releases:
    get:
      tags:
        - release
      summary: List of visible releases
      description: |-
        Returns list of visible releases. Supports pagination.

        Requires Role: COMPANY_ADMIN, ANALYST, POWER_USER, SPECIALIST, USER
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: pageSize
          description: The page size for pagination. Default to 100
          in: query
          required: false
          type: integer
        - name: pageIndex
          description: The page index for pagination. Default to 0
          in: query
          required: false
          type: integer
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              resultsMetadata:
                $ref: '#/definitions/ResultsMetadata'
              results:
                type: array
                items:
                  $ref: '#/definitions/Release'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /releases/{release}:
    get:
      tags:
        - release
      summary: Get release by number
      description: |-
        Get the release associated with the specified release number

        Requires Role: COMPANY_ADMIN, ANALYST, POWER_USER, SPECIALIST, USER
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: release
          description: The number of the release to look up
          in: path
          type: string
          required: true
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/Release'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Release not found or not visible
        '500':
          $ref: '#/responses/internalServerError'
  /releases/search:
    get:
      tags:
        - release
      summary: Get a list of releases
      description: |-
        This list can be optionally filtered by search string. The list can optionally be paginated.

        Requires Role: COMPANY_ADMIN, ANALYST, POWER_USER, SPECIALIST, USER
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: release
          description: Release number (format 0000.000.000)
          in: query
          type: string
          required: false
        - name: title
          description: Release title
          in: query
          type: string
          required: false
        - name: description
          description: Release summary
          in: query
          type: string
          required: false
        - name: updatedBy
          description: Name of the creator/updater
          in: query
          type: string
          required: false
        - name: pageSize
          description: The page size for pagination. Default to 100
          in: query
          required: false
          type: integer
        - name: pageIndex
          description: The page index for pagination. Default to 0
          in: query
          required: false
          type: integer
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              resultsMetadata:
                $ref: '#/definitions/ResultsMetadata'
              results:
                type: array
                items:
                  $ref: '#/definitions/Release'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /admin/releases:
    get:
      tags:
        - release
        - admin
      summary: List of all releases
      description: |-
        Returns list of all releases. Supports pagination.

        Requires Role: ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: pageSize
          description: The page size for pagination. Default to 100
          in: query
          required: false
          type: integer
        - name: pageIndex
          description: The page index for pagination. Default to 0
          in: query
          required: false
          type: integer
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              resultsMetadata:
                $ref: '#/definitions/ResultsMetadata'
              results:
                type: array
                items:
                  $ref: '#/definitions/AdminRelease'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
    post:
      tags:
        - release
        - admin
      summary: Create a new release
      description: |-
        Creates a new API release note.

        Requires Role: ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: details
          in: body
          required: true
          schema:
            type: object
            properties:
              release:
                type: string
                description: Release number (format 0000.000.000)
              releaseDate:
                type: string
                format: date-time
                description: Release date/time
              title:
                type: string
                description: Release title
              description:
                type: string
                description: Release summary
              updatedBy:
                type: string
                description: Name of the creator/updater
              visible:
                type: boolean
                description: Releases with visible set to false won't be accessible by normal users
              userGuide:
                type: string
                description: S3 key of the user guide pdf
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/AdminRelease'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /admin/releases/{release}:
    get:
      tags:
        - release
        - admin
      summary: Get release by number
      description: |-
        Get the release associated with the specified release number

        Requires Role: ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: release
          description: The number of the release to look up
          in: path
          type: string
          required: true
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/AdminRelease'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Release not found
        '500':
          $ref: '#/responses/internalServerError'
    put:
      tags:
        - release
        - admin
      summary: Update a release
      description: |-
        Update the release associated with the specified release number.

        Requires Role: ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: release
          description: The number of the release to update
          in: path
          type: string
          required: true
        - in: body
          name: release
          description: The release values to save
          schema:
            type: object
            properties:
              releaseDate:
                type: string
                description: Array of ODML elements
              title:
                type: string
                description: Release title
              description:
                type: string
                description: Release summary
              visible:
                type: string
                description: Releases with visible set to false won't be accessible by normal users
              updatedBy:
                type: string
                description: Name of the updater
              userGuide:
                type: string
                description: S3 key of the user guide pdf
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/AdminRelease'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Release not found or not visible
        '500':
          $ref: '#/responses/internalServerError'
    delete:
      tags:
        - release
        - admin
      summary: Archive a specific release
      description: |
        Archive the release associated with the specified release id. This is a soft delete.

        Requires Role: ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: release
          description: The release number to delete
          in: path
          type: string
          format: string
          required: true
      responses:
        '204':
          description: Successful response
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Release not found or not visible
        '500':
          $ref: '#/responses/internalServerError'
  /admin/releases/search:
    get:
      tags:
        - release
        - admin
      summary: Get a list of releases
      description: |-
        This list can be optionally filtered by search string. The list can optionally be paginated.

        Requires Role: ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: release
          description: Release number (format 0000.000.000)
          in: query
          type: string
          required: false
        - name: title
          description: Release title
          in: query
          type: string
          required: false
        - name: description
          description: Release summary
          in: query
          type: string
          required: false
        - name: updatedBy
          description: Name of the creator/updater
          in: query
          type: string
          required: false
        - name: pageSize
          description: The page size for pagination. Default to 100
          in: query
          required: false
          type: integer
        - name: pageIndex
          description: The page index for pagination. Default to 0
          in: query
          required: false
          type: integer
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              resultsMetadata:
                $ref: '#/definitions/ResultsMetadata'
              results:
                type: array
                items:
                  $ref: '#/definitions/AdminRelease'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /monitor/site-device-status:
    get:
      tags:
        - internal
      summary: Calculate site and device status
      description: |-
        Calculate site and device status and send it to alarm worker.

        Requires Role: ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '204':
          description: Site successfully retrieved
          schema:
            $ref: '#/definitions/SiteResponseDto'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /authenticateuser:
    post:
      tags:
        - authentication
      summary: Authenticate the user
      description: "1. Validate the Email and Password of the user first *User
        account must be active * If email and or password do not match then
        return an error * If user email is not validated then return an error.
        Note, if the login is rejected due to email being non-verified then
        automatically resend the verification email before returning 2. Then MFA
        validation follows the following rules  * If the user account has a MFA
        secret set and the code field has been populated then the user's MFA
        will be validated.  If the MFA validates successfully then the user is
        logged in and a JWT token will be issued. * If the user account has a
        MFA secret set and the code field has not been populated, then MFA
        validation is skipped and a 202 response is returned. 3. If the user
        account does not have a MFA secret set, and email/password validation
        has passed, then a redirect should be issued to the caller directing
        them to setup MFA for their account.

        If user failed to authenticate once and provided that the captcha config
        is enabled on both frontend and backend, user will be shown captcha."
      produces:
        - application/json
      parameters:
        - name: details
          in: body
          description: The user's authenticate credentials
          required: true
          schema:
            $ref: '#/definitions/UserAuthDetails'
      responses:
        '200':
          description: Successful authentication. User is now logged in
          schema:
            $ref: '#/definitions/AuthResp'
        '202':
          description: 'Email and password validation passed, but MFA is not configured for the account. MFA secret details are included in the response. Note, this MFA secret is NOT saved against the user account at this point.'
          schema:
            $ref: '#/definitions/MFAConfigDetails'
        '204':
          description: 'Email and password validation passed, but MFA validation skipped'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          description: Email/password or MFA validation failed
        '403':
          description: Forbidden, user account expired
        '405':
          description: Wrong method. Must use POST
        '406':
          description: MFA code validation failed
        '500':
          $ref: '#/responses/internalServerError'
  /authenticate:
    post:
      tags:
        - authentication
      summary: Authenticate the user
      description: "1. Validate the Email and Password of the user first *User account must be active * If email and or password do not match then return an error * If user email is not validated then return an error. Note, if the login is rejected due to email being non-verified then automatically resend the verification email before returning 2. Then MFA validation follows the following rules  * If the user account has a MFA secret set and the code field has been populated then the user's MFA will be validated.  If the MFA validates successfully then the user is logged in and a JWT token will be issued. * If the user account has a MFA secret set and the code field has not been populated, then MFA validation is skipped and a 202 response is returned. 3. If the user account does not have a MFA secret set, and email/password validation has passed, then a redirect should be issued to the caller directing them to setup MFA for their account."
      produces:
        - application/json
      parameters:
        - name: details
          in: body
          description: The user's authenticate credentials
          required: true
          schema:
            $ref: '#/definitions/AuthDetails'
      responses:
        '200':
          description: Successful authentication. User is now logged in
          schema:
            $ref: '#/definitions/AuthResp'
        '202':
          description: 'Email and password validation passed, but MFA is not configured for the account. MFA secret details are included in the response. Note, this MFA secret is NOT saved against the user account at this point.'
          schema:
            $ref: '#/definitions/MFAConfigDetails'
        '204':
          description: 'Email and password validation passed, but MFA validation skipped'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          description: Email/password or MFA validation failed
        '404':
          description: Not found. Wrong url. Invalid product/site id
        '405':
          description: Wrong method. Must use POST
        '406':
          description: MFA code validation failed
        '500':
          $ref: '#/responses/internalServerError'
  /authenticate/mfa:
    post:
      tags:
        - authentication
      summary: Set MFA for the Account During Authentication
      description: |-
        This call is used when the user is authenticating, but does not currently have MFA configured against their account.
         Firstly this call will validate the Email and Password of the user which were passed in.
         If these validate successfully then this identifies a specific user record and the lack of an existing MFA secret will be confirmed. If an existing MFA secret is found then a 403 error will be returned. Secondly this call will validate the MFA Secret and Code values which were passed in. If the values match then the user's record in the DB is updated to store the new MFA secret. The user will now be logged in and a valid JWT token will be returned.
      produces:
        - application/json
      parameters:
        - name: details
          in: body
          description: The user's authenticate credentials
          required: true
          schema:
            $ref: '#/definitions/SetupMFADetails'
      responses:
        '200':
          description: Successful authentication. User is now logged in
          schema:
            $ref: '#/definitions/AuthResp'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          description: Email/password or MFA validation failed
        '404':
          description: Not found. Wrong url. Invalid product/site id
        '405':
          description: Wrong method. Must use POST
        '500':
          $ref: '#/responses/internalServerError'
  /stats/assets:
    get:
      tags:
        - stats
      summary: Find the number of live sites and devices
      description: Get the number of active sites and devices for companies. Super Admin role is required.
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Number of live targets successfully retrieved
          schema:
            type: array
            items:
              $ref: '#/definitions/StatsResponseDto'
        '500':
          $ref: '#/responses/internalServerError'
  /stats/time:
    get:
      tags:
        - stats
      summary: Gets the current epoch time
      description: Gets the current epoch time. No authentication required
      produces:
        - application/json
      responses:
        '200':
          description: The time object to be returned
          schema:
            type: object
            properties:
              time:
                type: integer
                description: The epoch time
        '500':
          $ref: '#/responses/internalServerError'
  /users:
    get:
      tags:
        - users
      summary: Get all Users
      description: |
        Get a list of all users.  This list can be filtered to only users with a specific role, and is always filtered to return users only from a specific company.

        If a company filter is specified which is not the company of the current user, then the user's company must have a relationship with the specified company.
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: roles
          description: Only return users with the specified role name(s).  List of comma seperated values
          in: query
          type: string
          format: csv
          required: false
          default: Return users with all roles
        - name: companyId
          description: Only return users from the specified company id
          in: query
          type: string
          format: uuid
          default: The company of the calling user
          required: false
        - name: q
          description: Filter users by full name or email
          in: query
          required: false
          type: string
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/User'
        '404':
          description: Specified role or company not found
  /users/invite:
    post:
      tags:
        - users
      summary: Invite user
      description: |
        Creates a new pending user account and then sends an email to the user inviting them to complete their registration

        (by choosing a password and configuring MFA).

        Email address should be validated as unique before the invite email is sent.

        Requires Role: COMPANY_ADMIN
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: details
          in: body
          schema:
            type: object
            properties:
              email:
                type: string
                description: Email of the user to invite
                minLength: 3
              fullName:
                type: 'string'
                description: Name of the user to invite
                minLength: 3
              groups:
                type: array
                description: Usergroups, user is invited to
                items:
                  type: string
                  format: uuid
              roles:
                type: array
                description: Roles assigned to invited user
                items:
                  type: string
      responses:
        '204':
          description: No content
        '409':
          description: Email already exists
  /users/search:
    post:
      tags:
        - users
      summary: Search for a User
      description: |
        Allows searching for a user using a case insensitive, partial match.  Ignore inputs which are null or undefined.

        At least one search criteria (name or email) must be specified.  If both are specified then use an AND join.

        Requires Role: COMPANY_ADMIN
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: details
          in: body
          schema:
            type: object
            properties:
              name:
                type: string
                description: Name fragment to search for
                minLength: 3
              email:
                type: string
                description: Email fragment to search for
                minLength: 3
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/UserResponseResultObject'
  '/users/{id}':
    get:
      tags:
        - users
      summary: Get a Specific User
      description: |
        Get the user associated with the specified user id

        Requires Role: COMPANY_ADMIN
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: The ID of the user to look up
          in: path
          type: string
          format: uuid
          required: true
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/User'
        '404':
          description: User not found
    put:
      tags:
        - users
      summary: Update user
      description: |
        Update the user with the specified user id

        Requires Role: COMPANY_ADMIN
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: The ID of the user to update
          in: path
          type: string
          format: uuid
          required: true
        - name: body
          in: body
          schema:
            $ref: '#/definitions/UserUpdatePayload'
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/User'
        '403':
          description: Unable to edit a pending user
        '404':
          description: User/user role/user group not found
    delete:
      tags:
        - users
      summary: Archive a Specific User
      description: |
        Archive the user associated with the specified user id.  This is a soft delete, reversing this action requires a request to Inveco support.

        Requires Role: COMPANY_ADMIN
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: The ID of the user to delete
          in: path
          type: string
          format: uuid
          required: true
      responses:
        '204':
          description: Successful response
        '404':
          description: User not found
  '/users/lockinactive':
    post:
      tags:
        - users
      summary: Lock Inactive users
      description: |
        Lock inactive users: Users who haven't authenticated in the last 90 days, after an invite

        Requires Role: ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Locked users list
        '204':
          description: No content
        '500':
          $ref: '#/responses/internalServerError'
  '/users/warninactive':
    post:
      tags:
        - users
      summary: Warn Inactive users
      description: |
        Warn inactive users: Users who haven't authenticated in the last 83 days, after an invite

        Requires Role: ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Warn users list
        '204':
          description: No content
        '500':
          $ref: '#/responses/internalServerError'
  '/users/{id}/pendingregistration/resend':
    post:
      tags:
        - users
      summary: Resend a pending registration email
      description: 'Resend a pending registration email, finds user token by user id and resends it again to their email address'
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: The ID of the user to resend email too
          in: path
          type: string
          format: uuid
          required: true
      responses:
        '204':
          description: Successful response
        '404':
          description: User not found or user does not have a pending email token
        '429':
          description: Rate limit reached
  '/users/{id}/mfa':
    delete:
      tags:
        - users
      summary: Remove configured MFA from a User
      description: |
        Remove the configured MFA secret from a specific user account.  Next time the user attempts to authenticate then the user will recognize that they do not have valid MFA configured and will prompt them to configure new MFA settings (assuming they get they email and password correct).

        Requires Role: COMPANY_ADMIN
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: The ID of the user to remove MFA from
          in: path
          type: string
          format: uuid
          required: true
      responses:
        '204':
          description: Successful response
        '404':
          description: User not found
        '403':
          description: No permission
  '/users/fullname/{id}':
    get:
      tags:
        - users
      summary: Get a full name for a Specific User
      description: |
        Get the user's full name associated with the specified user id
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: The ID of the user to look up
          in: path
          type: string
          format: uuid
          required: true
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/UserFullName'
        '404':
          description: User not found
  /users/all/activeinactive:
    get:
      tags:
        - users
      summary: Get all Active and Inactive Users
      description: |
        Get a list of all users including inactive ones.  This list can be filtered to only users with a specific role, and is always filtered to return users only from a specific company.

        If a company filter is specified which is not the company of the current user, then the user's company must have a relationship with the specified company.
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: roles
          description: Only return users with the specified role name(s).  List of comma seperated values
          in: query
          type: string
          format: csv
          required: false
          default: Return users with all roles
        - name: companyId
          description: Only return users from the specified company id
          in: query
          type: string
          format: uuid
          default: The company of the calling user
          required: false
        - name: q
          description: Filter users by full name
          in: query
          required: false
          type: string
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/User'
        '404':
          description: Specified role or company not found
  /self:
    get:
      tags:
        - users
      summary: Get the Current User
      description: |
        Get the details of the currently logged in user

        Requires an authenticated user
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/User'
  /self/password:
    post:
      tags:
        - users
      summary: Update the Current User's Password
      description: |
        The MFA code will be validated before the password change is accepted.  Password requirements will be validated and the password must not be on the banned passwords list.

        Requires an authenticated user
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: details
          in: body
          required: true
          schema:
            type: object
            properties:
              mfaCode:
                type: string
                description: The user's MFA code
              password:
                type: string
                description: The user's desired new password
      responses:
        '204':
          description: Successful response
        '403':
          description: MFA token failed validation
        '409':
          description: Password requirements not met
  /forgotpassword:
    post:
      tags:
        - users
      summary: Request a Password Reset
      description: |
        Allows the user to request a password reset if their email address is known in the system.  To avoid enumeration of email addresses, this services gives the same response regardless of whether the email is known or not.  Should only consider accounts which are active.

        Client should respond to the user with, "Thanks, if an account with that email exists then you will recieve a password reset email shortly"
      parameters:
        - name: details
          in: body
          required: true
          schema:
            type: object
            properties:
              email:
                type: string
                description: The user's email address
      responses:
        '204':
          description: Successful response regardless of whether the email was found or not
        '429':
          description: Rate limit reached
  /sites/devices/summary:
    get:
      tags:
        - sites
        - devices
      summary: Get all devices grouped by site
      description: |-
        Gets the summary of all Devices for the company the current user belongs to.
        Can optionally be filtered to only Devices of a specific type.
        Requires role: USER or better.
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: type
          description: Filter devices by device type. Can be comma delimited (e.g. 'G6-100,G7-100') to support multiple device types filtering.
          in: query
          required: false
          type: string
        - $ref: '#/parameters/presenceParam'
        - name: tags
          in: query
          description: The tags filter to retrieve sites with the tags associated. the value of tags are separated by comma
          required: false
          type: string
        - name: siteGroups
          in: query
          description: The siteGroups filter to retrieve sites with the associated site-group(s). the value of siteGroups are separated by comma
          required: false
          type: string
        - $ref: '#/parameters/qParam'
      responses:
        '200':
          description: Array of sites and their respective devices
          schema:
            type: array
            items:
              properties:
                id:
                  type: string
                  description: The site's ID
                name:
                  type: string
                  description: The site's name
                tags:
                  type: array
                  items:
                    properties:
                      id:
                        type: number
                        description: The tag's ID
                      name:
                        type: string
                        description: The tag's name
                siteGroups:
                  type: array
                  items:
                    properties:
                      id:
                        type: string
                        description: The site-group's ID
                      name:
                        type: string
                        description: The site-group's name
                devices:
                  type: array
                  items:
                    properties:
                      id:
                        type: number
                        description: The device's ID
                      name:
                        type: string
                        description: The device's name
                      serialNumber:
                        type: string
                        description: The device's serial number
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /sites/summary:
    get:
      tags:
        - sites
      summary: Get the summary of all sites
      description: |-
        Gets the summary of all sites for the company the current user belongs to.
        Requires role: USER or better.
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: status
          in: query
          description: The status code to filter
          required: false
          type: number
        - name: company
          in: query
          description: Filter sites by company
          required: false
          type: string
          format: uuid
        - name: tags
          in: query
          description: The tags filter to retrieve sites with the tags associated. the value of tags are separated by comma
          required: false
          type: string
        - name: siteGroups
          in: query
          description: The siteGroups filter to retrieve sites with the associated site-group(s). the value of siteGroups are separated by comma
          required: false
          type: string
        - $ref: '#/parameters/qParam'
      responses:
        '200':
          description: Array of sites with their respective tags and devices counter
          schema:
            type: array
            items:
              properties:
                id:
                  type: string
                  description: The site's ID
                name:
                  type: string
                  description: The site's name
                formattedAddress:
                  type: string
                  description: The site's address
                status:
                  type: number
                  description: The site's status
                tags:
                  type: array
                  items:
                    properties:
                      id:
                        type: number
                        description: The tag's ID
                      name:
                        type: string
                        description: The tag's name
                siteGroups:
                  type: array
                  items:
                    properties:
                      id:
                        type: number
                        description: The site-group's ID
                      name:
                        type: string
                        description: The site-group's name
                devicesCounter:
                  type: integer
                  description: The number of devices associated with this site
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /sites:
    post:
      tags:
        - sites
      summary: Create a new site resource
      description: Create a new site in you terminal management system. Target devices can be added to the new site. Admin role required
      produces:
        - application/json
      consumes:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: Information about the site being created
          required: true
          schema:
            $ref: '#/definitions/SiteCreate'
      responses:
        '200':
          description: New site successfully created
          schema:
            $ref: '#/definitions/SiteResponseDto'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Wrong URL
        '405':
          description: Must use POST method
        '500':
          $ref: '#/responses/internalServerError'
    get:
      tags:
        - sites
      summary: Get all the available sites
      description: Retrieve all active and accessible sites in the Terminal Management System
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: statuses
          type: array
          description: Filter sites by status
          required: false
          in: query
          items:
            type: string
            enum:
              - NORMAL
              - UNKNOWN
              - INACTIVE
              - WARNING
              - CRITICAL
        - name: deviceStatuses
          type: array
          description: Filter sites by device status
          required: false
          in: query
          items:
            type: string
            enum:
              - OPERATIONAL
              - UNKNOWN
              - INACTIVE
              - OUT_OF_SERVICE
        - name: oosFilter
          type: array
          description: Filter sites by device out-of-service category and condition separated by a pipe "|" (e.g. ?oosFilter[]=category1|condition11&oosFilter[]=category2|condition21 )
          required: false
          in: query
          items:
            type: string
        - name: alarms
          type: array
          description: Filter sites by alarm code
          required: false
          in: query
          items:
            type: string
        - name: company
          in: query
          description: Filter sites by company
          required: false
          type: string
          format: uuid
        - name: tags
          in: query
          description: The tags filter to retrieve sites with the tags associated. the value of tags are separated by comma
          required: false
          type: string
        - name: siteGroups
          in: query
          description: The siteGroups filter to retrieve sites with the associated site-group(s). the value of siteGroups are separated by comma
          required: false
          type: string
        - name: showHiddenSites
          in: query
          description: If showHiddenSites value is true, result/s will include hidden sites
          required: false
          type: boolean
        - name: isCSV
          in: query
          description: If isCSV value is true, result/s will be exported to a CSV file
          required: false
          type: boolean
        - $ref: '#/parameters/qParam'
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: order
          type: string
          description: The field to use when ordering the result list
          required: false
          in: query
          default: name-asc
          enum:
            - name-asc
            - name-desc
            - id-asc
            - id-desc
            - address-asc
            - address-desc
            - status-asc
            - status-desc
      responses:
        '200':
          description: All sites retrieved successfully
          schema:
            $ref: '#/definitions/SitesResult'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /sites/list:
    get:
      tags:
        - sites
      summary: Get list of available sites
      description: Retrieve all active and accessible sites
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: statuses
          type: array
          description: Filter sites by status
          required: false
          in: query
          items:
            type: string
            enum:
              - NORMAL
              - UNKNOWN
              - INACTIVE
              - WARNING
              - CRITICAL
        - name: deviceStatuses
          type: array
          description: Filter sites by device status
          required: false
          in: query
          items:
            type: string
            enum:
              - OPERATIONAL
              - UNKNOWN
              - INACTIVE
              - OUT_OF_SERVICE
        - name: oosFilter
          type: array
          description: Filter sites by device out-of-service category and condition separated by a pipe "|" (e.g. ?oosFilter[]=category1|condition11&oosFilter[]=category2|condition21 )
          required: false
          in: query
          items:
            type: string
        - name: siteEvents
          type: array
          description: Filter sites by site event ['NEW_SITE']
          required: false
          in: query
          items:
            type: string
            enum:
              - NEW_SITE
        - name: isCSV
          in: query
          description: If isCSV value is true, result/s will be exported to a CSV file
          required: false
          type: boolean
        - name: showHiddenSites
          in: query
          description: If showHiddenSites value is true, result/s will include hidden sites
          required: false
          type: boolean
        - $ref: '#/parameters/qParam'
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: tags
          in: query
          description: The tags filter to retrieve sites with the tags associated. the value of tags are separated by comma
          required: false
          type: string
        - name: order
          type: string
          description: The field to use when ordering the result list
          required: false
          in: query
          default: name-asc
          enum:
            - name-asc
            - name-desc
            - id-asc
            - id-desc
            - address-asc
            - address-desc
            - status-asc
            - status-desc
      responses:
        '200':
          description: All sites retrieved successfully
          schema:
            $ref: '#/definitions/SitesResult'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /sites/authorization:
    get:
      tags:
        - sites
      summary: Verify user access authorization to sites
      description: Verify user access authorization to sites
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: sites
          description: List of identifiers for the sites
          type: array
          required: true
          in: query
          items:
            type: string
            format: uuid
      responses:
        '204':
          description: User has authorization to all listed sites
        '404':
          description: Site not found
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /sites/internal:
    get:
      tags:
        - sites
        - internal
      summary: Get all the available sites [INTERNAL]
      description: Retrieve all active sites
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: companyId
          type: string
          description: Filter by Company ID
          required: false
          in: query
      responses:
        '200':
          description: All sites retrieved successfully
          schema:
            $ref: '#/definitions/SitesResult'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  '/sites/external-types':
    get:
      tags:
        - sites
      summary: Gets the allowed external types
      description: Gets the allowed external types
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Returns allowed types
          schema:
            $ref: '#/definitions/AllowedExternalReferenceTypesResult'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
  '/sites/{siteId}':
    get:
      tags:
        - sites
      summary: Get site by site Id
      description: Retrieve a customer site
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: siteId
          in: path
          description: Identifier for the site
          required: true
          type: integer
      responses:
        '200':
          description: Site successfully retrieved
          schema:
            $ref: '#/definitions/SiteResponseDto'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
    put:
      tags:
        - sites
      summary: Update a site resource
      description: Update a new site in you terminal management system. Admin role required
      produces:
        - application/json
      consumes:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: siteId
          in: path
          description: Identifier for the site
          required: true
          type: string
        - name: body
          in: body
          description: Information about the site being created
          required: true
          schema:
            $ref: '#/definitions/SiteUpdate'
      responses:
        '200':
          description: New site successfully created
          schema:
            $ref: '#/definitions/SiteResponseDto'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Wrong URL
        '405':
          description: Must use POST method
        '500':
          $ref: '#/responses/internalServerError'

    patch:
      tags:
        - sites
      summary: Update a site resource
      description: Update a new site in you terminal management system. Admin role required
      produces:
        - application/json
      consumes:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: siteId
          in: path
          description: Identifier for the site
          required: true
          type: string
        - name: body
          in: body
          description: Information about the site being created
          required: true
          schema:
            $ref: '#/definitions/SiteUpdatePatch'
      responses:
        '200':
          description: New site successfully created
          schema:
            $ref: '#/definitions/SiteResponseDto'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Wrong URL
        '405':
          description: Must use POST method
        '500':
          $ref: '#/responses/internalServerError'

    delete:
      tags:
        - sites
      summary: Delete site by site Id
      description: Delete a customer site without any live target devices. Admin role only
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: siteId
          in: path
          description: Identifier for the site
          required: true
          type: string
      responses:
        '204':
          description: Site successfully deleted
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Not found. Wrong site Id
        '460':
          description: Live target device detected in the site. Make sure the target devices either moved or deleted
        '500':
          $ref: '#/responses/internalServerError'
  '/sites/reference/{referenceId}':
    get:
      tags:
        - sites
      summary: Get site by reference ID
      description: Retrieve a customer site
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: referenceId
          in: path
          description: Identifier for the site
          required: true
          type: integer
      responses:
        '200':
          description: Site successfully retrieved
          schema:
            $ref: '#/definitions/SiteResponseDto'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  '/sites/{siteId}/devices':
    get:
      tags:
        - sites
      summary: Get all the devices for a site
      description: Retrieve a list of all the  devices that are associated with the site
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: siteId
          in: path
          description: Identifier for the site
          required: true
          type: integer
      responses:
        '200':
          description: Devices retrieved successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/SiteTargetResponseDto'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /devices:
    post:
      tags:
        - devices
      summary: Create a new device resource (target device)
      description: Creates a new device resource and stores its encryption certificate. The Registraion key (device serial number) must be unique. Admin role required. Registration time window is 365 days
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: Simple target object that will be added
          required: true
          schema:
            $ref: '#/definitions/TargetCreate'
      responses:
        '200':
          description: A new device has been created
          schema:
            $ref: '#/definitions/TargetResponseDto'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Not found. Wrong url. Invalid product/site id
        '405':
          description: Wrong method. Must use POST
        '500':
          $ref: '#/responses/internalServerError'
    get:
      tags:
        - devices
      summary: Get a list of Devices for the current customer.
      description: This list can be optionally filtered by search string. The list can optionally be paginated
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/searchFilterParam'
        - $ref: '#/parameters/companyIdParam'
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: statuses
          type: array
          description: Filter devices by status
          required: false
          in: query
          items:
            type: string
            enum:
              - OPERATIONAL
              - UNKNOWN
              - INACTIVE
              - OUT_OF_SERVICE
        - name: oosFilter
          type: array
          description: Filter devices by device out-of-service category and condition separated by a pipe "|" (e.g. ?oosFilter[]=category1|condition11&oosFilter[]=category2|condition21 )
          required: false
          in: query
          items:
            type: string
        - name: order
          type: string
          description: The field to use when ordering the result list
          required: false
          in: query
          default: status-desc
          enum:
            - serial-asc
            - serial-desc
            - name-asc
            - name-desc
            - status-asc
            - status-desc
        - name: deviceType
          type: string
          description: filter devices by device type
          required: false
          in: query
        - name: configs
          type: array
          description: Append device config info by config name
          required: false
          in: query
          items:
            type: string
        - name: alarms
          type: array
          description: Filter devices by alarm code
          required: false
          in: query
          items:
            type: string
        - name: isCSV
          in: query
          description: If isCSV value is true, result/s will be exported to a CSV file
          required: false
          type: boolean
      responses:
        '200':
          description: All devices retrieved successfully
          schema:
            $ref: '#/definitions/DevicesResult'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /devices/sync:
    post:
      tags:
        - internal
      summary: Export devices to the destination queue
      description: |-
        Allows active devices to be sent as stringified messages to the destination SQS queue

        Required role: ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response. Devices successfully sent to SQS
        '500':
          description: Failed to export devices
  /devices/movetosite:
    post:
      tags:
        - devices
      summary: Move devices to a specified site.
      description: 'Move devices to a specified site. The call either fully succeeds or throws exception if it cannot be completed for any reason. ICS_SYSTEM, COMPANY ADMIN or POWER USER role required'
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: BulkMove request payload. Have to specify either id or serial number for devices to be moved. If serial number is specified id value is ignored.
          required: true
          schema:
            type: object
            required:
              - siteId
              - devices
              - deploymentType
            properties:
              siteId:
                type: string
                description: Site Id
              deploymentType:
                type: string
                enum: [maintenance-window, immediate, schedule]
              scheduledDateTime:
                type: string
                example: 'YYYY-MM-DD HH:mm'
              devices:
                type: array
                items:
                  type: object
                  properties:
                    serialNumber:
                      type: string
                      description: Serial number of device
                    id:
                      type: string
                      description: Device id
      produces:
        - application/json
      consumes:
        - application/json
      responses:
        '200':
          description: Registration response payload
          schema:
            $ref: '#/definitions/DevicesBulkmoveResponse'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '403':
          description: Access denied. User does not have an access to all specified devices or does not have rights to fully execute this request. List of devices that cannot be found will be provided in the error message.
        '404':
          description: One of entities needed to complete this request cannot be found or user has no permission. List of devices that cannot be found will be provided in the error message.
          schema:
            properties:
              msg:
                type: array
                description: List of error messages
                items:
                  type: string
        '422':
          description: Unprocessable Request. Request is semantically correct but destination company does not support device type.
          schema:
            $ref: '#/definitions/ErrorMessage'
        '500':
          $ref: '#/responses/internalServerError'
  /devices/movetocompany:
    post:
      tags:
        - devices
      summary: Move devices to a specified company default site
      description: 'Move devices to a company default site. The call either fully succeeds or throws exception if it cannot be completed for any reason. ICS_SYSTEM, COMPANY ADMIN or POWER USER role required'
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: BulkMove to another company request payload. Have to specify a list of both company reference and serial number for devices to be moved
          required: true
          schema:
            type: array
            items:
              $ref: '#/definitions/CompanyRefSerial'
      produces:
        - application/json
      consumes:
        - application/json
      responses:
        '200':
          description: Registration response payload
          schema:
            $ref: '#/definitions/DevicesBulkmoveResponse'
        '400':
          description: 'Bad request provided in the payload e.g. duplication of serial number, null company reference or serial numbers '
          schema:
            $ref: '#/definitions/ErrorMessage'
        '403':
          description: Access denied. User does not have an access to all specified devices or does not have rights to fully execute this request. List of devices that cannot be found will be provided in the error message.
          schema:
            $ref: '#/definitions/ErrorMessage'
        '404':
          description: 'Entities needed to complete this request cannot be found e.g. Company ref, serial number'
          schema:
            $ref: '#/definitions/ErrorMessage'
        '422':
          description: Unprocessable Request. Request is semantically correct but destination company does not support device type.
          schema:
            $ref: '#/definitions/ErrorMessage'
        '500':
          $ref: '#/responses/internalServerError'
  /devices/sync-keygroup-to-site:
    post:
      tags:
        - devices
      summary: Sync devices' with key group mismatch to its current site.
      description: Sync devices' with key group mismatch to its current site. RKI role required'
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          required: true
          schema:
            type: object
            required:
              - siteId
              - devices
            properties:
              siteId:
                type: string
                format: guid
                description: Site Id
              devices:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                      description: Device id
                    keyGroupId:
                      type: string
                      format: guid
                      description: Current key group id of device
      produces:
        - application/json
      consumes:
        - application/json
      responses:
        '200':
          description: CreateRKIRequestSession response payload
          schema:
            $ref: '#/definitions/CreateAutomatedRKIRequestResponse'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '403':
          description: Access denied. User does not have an access to all specified devices or does not have rights to fully execute this request.
        '404':
          description: One of entities needed to complete this request cannot be found or user has no permission.
        '406':
          description: MFA code validation failed
        '500':
          $ref: '#/responses/internalServerError'
  /emailtoken/forgotpassword:
    post:
      tags:
        - users
      summary: Reset the Password of an Account
      description: |
        The user having their password changed is identified by the token passed in.  The token type must be validated as forgot-password and the token must also not be expired.  The MFA code specified by the user must also be validated before the password change is accepted.  As soon as the password is successfully updated then the token must be deleted so that it cannot be used again.


        Note, successful completion of this step will log the user in.  If the user is authenticated (has a JWT token) when calling this service) then existing authentication should be ignored.
      parameters:
        - name: details
          in: body
          required: true
          schema:
            type: object
            properties:
              token:
                type: string
                description: The secure token emailed to the user
              password:
                type: string
                description: The new password desired by the user
              mfaCode:
                type: string
                description: The user's MFA code
      responses:
        '200':
          description: Successful authentication. User is now logged in
          schema:
            type: object
            properties:
              token:
                type: string
                format: JWT token
                description: The user's JWT token
        '403':
          description: MFA code validation failed
        '404':
          description: Token was not found or expired
        '409':
          description: Password requirements not met
  /emailtoken/exists:
    get:
      tags:
        - users
      summary: Checks whether a users token exists
      description: Checks whether a users token exists for a certain type and is still valid
      parameters:
        - name: token
          in: query
          description: The token value to find
          required: true
          type: string
        - name: type
          in: query
          description: The type of token to find
          required: true
          type: string
      responses:
        '204':
          description: Token was found
        '404':
          description: Token was not found or expired
  /usergroups:
    get:
      tags:
        - usergroups
      summary: Get all user groups
      description: Get all user groups in a list form including the count of users per group
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: The active user groups have been retrieved successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/UserGroupListTypeResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '500':
          $ref: '#/responses/internalServerError'
    post:
      tags:
        - usergroups
      summary: Create a user groups
      description: Creates a new user group
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: userGroupObject
          in: body
          description: The user group object
          required: true
          schema:
            $ref: '#/definitions/UserGroupType'
      responses:
        '200':
          description: The user groups has been created successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/UserGroupType'
        '401':
          $ref: '#/responses/authenticationFailed'
        '500':
          $ref: '#/responses/internalServerError'
  '/usergroups/{id}':
    get:
      tags:
        - usergroups
      summary: Get a specific user group
      description: Get a specific user group
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/userGroupIdParam'
      responses:
        '200':
          description: The user group has been successfully received
          schema:
            type: array
            items:
              $ref: '#/definitions/UserGroupType'
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: User group not found
        '500':
          $ref: '#/responses/internalServerError'
    put:
      tags:
        - usergroups
      summary: Update an existing user group
      description: Update an existing user group
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/userGroupIdParam'
        - name: userGroupObject
          in: body
          description: The user group object
          required: true
          schema:
            $ref: '#/definitions/UserGroupType'
      responses:
        '200':
          description: The user groups has been successfully updated
          schema:
            type: array
            items:
              $ref: '#/definitions/UserGroupType'
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: User group not found
        '500':
          $ref: '#/responses/internalServerError'
    delete:
      tags:
        - usergroups
      summary: Delete an existing user group
      description: Delete an existing user group
      consumes:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/userGroupIdParam'
      responses:
        '204':
          description: The user groups has been successfully deleted
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: User group not found
        '409':
          description: User group contains users. Cannot delete
        '500':
          $ref: '#/responses/internalServerError'
  /companies/suppliers:
    get:
      tags:
        - companies
      summary: Returns companies which supply services to the current company
      description: Companies which have a supplier relationship with the caller's comapny.  Only Company Admins can do this
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Companies which supply services to the current company
          schema:
            type: array
            items:
              $ref: '#/definitions/CompanyType'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /companies/consumers:
    get:
      tags:
        - companies
      summary: Returns companies which consume services to the current company
      description: |
        Companies which consume services from the caller's company.

         Requires role: POWER_USER or better
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Companies which consume services from the current company
          schema:
            type: array
            items:
              $ref: '#/definitions/CompanyType'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /companies/self:
    get:
      tags:
        - companies
      summary: Returns the caller's company
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: The caller's company
          schema:
            $ref: '#/definitions/CompanyType'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  '/companies':
    post:
      tags:
        - companies
      summary: Create new company
      description: |-
        Create new company.

        Requires Role: SUPER_ADMIN
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Company created successfully
        '404':
          description: -|
            Default site not found
            Feature flag(s) not found
            Device type(s) not found
            Prompt template(s) not found
            Company(s) not found (relationships)
        '500':
          $ref: '#/responses/internalServerError'
  '/companies/{id}/languages':
    get:
      tags:
        - companies
      summary: Get company language by id
      description: Get list of languages enabled for the company by ID.
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          in: path
          required: true
          type: string
          format: uuid
      responses:
        '200':
          description: The requested company
          schema:
            $ref: '#/definitions/CompanyLanguages'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: CompanyID parameter does not match with user company.
        '500':
          $ref: '#/responses/internalServerError'
  '/companies/{id}':
    put:
      tags:
        - companies
      summary: Update company by id
      description: |-
        Update company details.

        Requires Role: SUPER_ADMIN
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          in: path
          required: true
          type: string
          format: uuid
      responses:
        '200':
          description: Company updated successfully
        '400':
          description: No payload available
        '404':
          description: -|
            Company not found
            Default site not found
            Feature flag(s) not found
            Device type(s) not found
            Prompt template(s) not found
            Company(s) not found (relationships)
        '500':
          $ref: '#/responses/internalServerError'
    delete:
      tags:
        - companies
      summary: Delete company by id
      description: |-
        Remove all records related to a given company. This is not a soft-delete and can not be undone unless a backup is restored

        Requires Role: SUPER_ADMIN
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          in: path
          required: true
          type: string
          format: uuid
      responses:
        '200':
          description: Company deleted successfully
        '400':
          description: Company can not be deleted
        '404':
          description: Company not found
        '500':
          $ref: '#/responses/internalServerError'
    get:
      tags:
        - companies
      summary: Get company by id
      description: Get a specific company by ID.  This only returns companies which the caller can see Only Company Admins can do this
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          in: path
          required: true
          type: string
          format: uuid
      responses:
        '200':
          description: The requested company
          schema:
            $ref: '#/definitions/CompanyType'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  '/media/prompt/{id}':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - in: path
        name: id
        description: Prompt ID
        type: string
        required: true
    put:
      summary: Save prompt
      tags:
        - prompt
      description: |-
        Save the values of a prompt.

        Requires Role: MEDIA_DESIGNER
      parameters:
        - in: body
          name: prompt
          description: The prompt values to save
          schema:
            type: object
            properties:
              elements:
                type: object
                description: Array of ODML elements
              softkeys:
                type: array
                items:
                  type: object
                  properties:
                    keycode:
                      type: string
                    fontSize:
                      type: string
                    fontColor:
                      type: string
                    label:
                      type: string
                    softkey:
                      type: number
              touchmap:
                type: object
                properties:
                  id:
                    type: string
                    description: touchmap id
              transactionState:
                type: string
                enum:
                  - null
                  - fueling
                  - idle
      responses:
        '200':
          description: Successful save
          schema:
            type: object
            properties:
              version:
                type: string
                description: Bumped up prompt version
              modified:
                type: string
                format: date-time
                description: Modified date
              modifiedBy:
                type: object
                properties:
                  id:
                    type: string
                    description: User UUID
                  name:
                    type: string
                    description: User name
                  email:
                    type: string
                    description: User email address
        '400':
          description: Invalid softkeys/elements
        '404':
          description: Prompt/PromptSet/Touchmap not found
    delete:
      summary: Delete prompt.
      tags:
        - prompt
      description: 'Only exception prompts can be deleted. A default prompt cannot be deleted. Updates the minor version of the parent promptset. Requires Role: MEDIA_DESIGNER'
      responses:
        '200':
          description: Successful delete operation
          schema:
            type: object
            properties:
              version:
                type: string
                description: Bumped up prompted version
              modified:
                type: string
                format: date-time
                description: Modified date
              modifiedBy:
                type: object
                properties:
                  id:
                    type: string
                    description: User UUID
                  name:
                    type: string
                    description: User name
                  email:
                    type: string
                    description: User email address
        '404':
          description: Prompt or Prompt set not found
  '/media/prompt/{id}/validate':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    get:
      summary: Validate prompt elements.
      tags:
        - prompt
      description: 'Validate prompt elements against ODML specification. Requires Role: MEDIA_DESIGNER or SYSTEM_USER'
      parameters:
        - in: path
          name: id
          description: Prompt ID
          type: string
          required: true
      responses:
        '204':
          description: Prompt elements are valid
        '400':
          description: Invalid elements
        '404':
          description: Prompt or Prompt set not found
  '/media/prompt/{id}/toODML':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    post:
      summary: Generate prompt ODML.
      tags:
        - prompt
      description: 'Returns the ODML string of this prompt. Requires Role: MEDIA user or SYSTEM_USER'
      parameters:
        - in: path
          name: id
          description: Prompt ID
          type: string
          required: true
        - in: body
          name: body
          description: Array of assets with paths
          schema:
            type: array
            items:
              type: object
              properties:
                id:
                  type: string
                  description: Asset UUID
                path:
                  type: string
                  description: Resolved asset path or string
        - in: query
          name: beautify
          description: Beautify the ODML output
          type: boolean
          required: false
      responses:
        '200':
          description: Sucessful ODML generation
          schema:
            type: string
            description: Parsed ODML
        '400':
          description: Invalid elements
        '404':
          description: Prompt or Prompt set not found
  '/media/prompt/{id}/touchmap':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    delete:
      summary: Delete prompt.
      tags:
        - prompt
      description: 'Deletes the touchmap of the specified prompt. Updates the minor version of the parent promptset. Requires Role: MEDIA_DESIGNER'
      parameters:
        - in: path
          name: id
          description: Prompt ID
          type: string
          required: true
      responses:
        '200':
          description: Successful delete operation
          schema:
            type: object
            properties:
              version:
                type: string
                description: Bumped up prompted version
              modified:
                type: string
                format: date-time
                description: Modified date
              modifiedBy:
                type: object
                properties:
                  id:
                    type: string
                    description: User UUID
                  name:
                    type: string
                    description: User name
                  email:
                    type: string
                    description: User email address
        '404':
          description: Prompt or prompt set not found
  '/media/prompt/{id}/elements':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    put:
      summary: Edit prompt elements.
      tags:
        - prompt
      description: 'Edits the elements in the specified prompt. Updates the promptset version if any change happened. Auto-validates the elements, will fail if the elements are invalid. Updates the minor version of the parent promptset. Requires Role: MEDIA_DESIGNER'
      parameters:
        - in: path
          name: id
          description: Prompt ID
          type: string
          required: true
        - in: body
          name: body
          description: Array of ODML elements
          schema:
            type: object
      responses:
        '200':
          description: Successful update operation
          schema:
            type: object
            properties:
              version:
                type: string
                description: Bumped up prompted version
              modified:
                type: string
                format: date-time
                description: Modified date
              modifiedBy:
                type: object
                properties:
                  id:
                    type: string
                    description: User UUID
                  name:
                    type: string
                    description: User name
                  email:
                    type: string
                    description: User email address
        '400':
          description: Invalid elements
        '404':
          description: Prompt or Prompt set not found
  '/media/promptsets/{promptSetId}/fingerprint':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    put:
      summary: Save promptset fingerprint
      tags:
        - prompt
      description: "Saves the promptset's fingerprint. The fingerprint is a Sha256 hash of the promptset package. Requires Role: ICS_SYSTEM"
      parameters:
        - in: path
          name: promptSetId
          description: Prompt Set ID
          type: string
          required: true
        - in: body
          name: body
          schema:
            type: object
            properties:
              secureFingerprint:
                type: string
                description: "the SHA256 hash that identifies this promptset's secure package"
              nonSecureFingerprint:
                type: string
                description: "the SHA256 hash that identifies this promptset's non secure package"
      responses:
        '204':
          description: Successful saved fingerprint
        '400':
          description: Bad Request
        '404':
          description: Promptset not found
  '/media/promptsets/{promptSetId}/signpackage':
    parameters:
      - '$ref': '#/parameters/AuthorizationTokenParam'
      - in: path
        name: promptSetId
        description: Original prompt set ID
        type: string
        required: true
    post:
      summary: 'Sign the asset package. Returns a signature. Requires Role: ICS_SYSTEM'
      tags:
        - prompt-sets
        - internal
      consumes:
        - multipart/form-data
      description: 'Sign the asset package. Returns a signature. Requires Role: ICS_SYSTEM'
      parameters:
        - name: userToken
          in: formData
          description: The bearer jwt token of the person who initiated the packaging
          required: true
          type: string
          minLength: 3
        - name: fileContent
          in: formData
          type: file
          required: true
      responses:
        '200':
          description: Successful operation
          schema:
            type: string
            description: The signature
  '/media/promptsets/{promptSetId}/children':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    get:
      summary: List cloned promptsets
      tags:
        - prompt-sets
      description: 'Returns a list of promptsets that are clones of the specified promptset. Only promptsets that are not in DRAFT are returned. Requires Role: MEDIA_DESIGNER, MEDIA_APPROVER or MEDIA_DEPLOYER.'
      parameters:
        - in: path
          name: promptSetId
          description: Original prompt set ID
          type: string
          required: true
      responses:
        '200':
          description: Successful operation
          schema:
            type: array
            items:
              properties:
                id:
                  type: string
                  description: Cloned prompt set ID
                name:
                  type: string
                  description: Prompt set name
                version:
                  type: string
                  description: version value (semver)
                deviceType:
                  type: string
                  description: Device type
                template:
                  type: object
                  properties:
                    id:
                      type: string
                      description: Template ID
                    name:
                      type: string
                      description: Template name
                created:
                  type: string
                  format: date-time
                  description: Creation date
                createdBy:
                  type: object
                  properties:
                    id:
                      type: string
                      description: User UUID
                    name:
                      type: string
                      description: User name
                    email:
                      type: string
                      description: User email address
                modified:
                  type: string
                  format: date-time
                  description: Modified date
                modifiedBy:
                  type: object
                  properties:
                    id:
                      type: string
                      description: User UUID
                    name:
                      type: string
                      description: User name
                    email:
                      type: string
                      description: User email address
                promptThumbnails:
                  type: array
                  items:
                    type: string
                    description: List of thumbnail URLs
                status:
                  type: string
                  description: Prompt set status
                softwareId:
                  type: string
                  description: Software ID
        '500':
          $ref: '#/responses/internalServerError'
  '/media/promptsets/{promptSetId}/languages':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    put:
      summary: Update languages for a prompt-set
      tags:
        - prompt-sets
      description: 'Update languages for a prompt-set (Enable/Disable/Update Font Face/Size)'
      parameters:
        - in: path
          name: promptSetId
          description: Prompt set ID
          type: string
          required: true
        - in: body
          name: body
          description: Prompt-Set Language Support Payload (1 Default Language Max)
          schema:
            $ref: '#/definitions/PromptSetLanguageSupportPayload'
      consumes:
        - application/json
      responses:
        '200':
          description: The requested company
          schema:
            $ref: '#/definitions/PromptSetLanguages'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          $ref: '#/responses/forbiddenError'
        '400':
          $ref: '#/responses/badPayloadOrParameters'
        '404':
          description: 'Prompt-Set or Template or Device Type not found'
        '409':
          description: 'Prompt set is not editable'
        '500':
          $ref: '#/responses/internalServerError'
    get:
      summary: List languages for a prompt-set
      tags:
        - prompt-sets
      description: 'Returns a list of languages enabled for a prompt-set'
      parameters:
        - in: path
          name: promptSetId
          description: Prompt set ID
          type: string
          required: true
      responses:
        '200':
          description: The requested company
          schema:
            $ref: '#/definitions/PromptSetLanguages'
        '401':
          $ref: '#/responses/authenticationFailed'
        '400':
          $ref: '#/responses/badPayloadOrParameters'
        '500':
          $ref: '#/responses/internalServerError'
  '/media/promptset/{promptSetId}/state/{stateId}/exception':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    post:
      summary: Create prompt exception.
      tags:
        - prompt
      description: "Creates a prompt exception for the specified state. Edits the elements in the specified prompt. Prepopulates the prompt's elements array with default background element based on the `bg` of the promptSet. If the state has an exampleText, it also creates a text element. If the default prompt has a touchmap, the prompt exception will also have the same touchmap by default, otherwise null. Requires Role: MEDIA_DESIGNER"
      parameters:
        - in: path
          name: promptSetId
          description: Prompt Set ID
          type: string
          required: true
        - in: path
          name: stateId
          description: Prompt State ID
          type: string
          required: true
        - in: body
          name: body
          description: Daypart
          schema:
            type: object
            properties:
              dayPart:
                type: object
                properties:
                  id:
                    type: string
      responses:
        '200':
          description: Successful operation
          schema:
            type: object
            properties:
              id:
                type: string
              parentId:
                type: string
                description: the parent state
              elements:
                type: array
                items:
                  type: string
                description: an array of Odml elements
              dayPart:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  start:
                    type: number
                  end:
                    type: number
              touchmap:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  areas:
                    type: array
                    items:
                      type: string
                    description: Array of area elements
              promptSet:
                type: object
                properties:
                  version:
                    type: string
                    description: Bumped up prompt version
                  modified:
                    type: string
                    format: date-time
                    description: Modified date
                  modifiedBy:
                    type: object
                    properties:
                      id:
                        type: string
                        description: User UUID
                      name:
                        type: string
                        description: User name
                      email:
                        type: string
                        description: User email address
        '404':
          description: 'Daypart, Prompt set or Prompt state not found'
        '409':
          description: Daypart already assigned
  /media/promptstates:
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    post:
      summary: 'Create a new promptstate [INTERNAL]'
      tags:
        - prompt-templates
        - internal
      description: 'Creates a promptstate, attached to  a prompttemplate [INTERNAL]'
      parameters:
        - in: body
          name: body
          description: Prompt State parameter
          schema:
            type: object
            properties:
              name:
                type: string
              code:
                type: string
              description:
                type: string
              secure:
                type: boolean
              numericInput:
                type: boolean
              softKeys:
                type: boolean
              sequence:
                type: integer
              exampleText:
                type: string
              attribs:
                type: string
              promptTemplateId:
                type: string
                format: guid
      responses:
        '200':
          description: Successful Response
          schema:
            $ref: '#/definitions/PromptState'
    get:
      summary: 'Get all promptstates [INTERNAL]'
      tags:
        - prompt-templates
        - internal
      description: 'Get a paginated list of prompt states [INTERNAL]'
      parameters:
        - in: query
          name: q
          description: The query string to query from
          type: string
          required: false
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
      responses:
        '200':
          description: List of promptstates
          schema:
            type: object
            properties:
              resultsMetadata:
                $ref: '#/definitions/ResultsMetadata'
              results:
                type: array
                items:
                  $ref: '#/definitions/PromptState'
  '/media/promptstates/{id}':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    put:
      summary: 'Edit a prompt state [INTERNAL]'
      tags:
        - prompt-templates
        - internal
      description: 'Edits a promptstate, attached to  a prompttemplate [INTERNAL]'
      parameters:
        - in: path
          name: id
          required: true
          type: string
        - in: body
          name: body
          description: Prompt State parameter
          schema:
            type: object
            properties:
              name:
                type: string
              code:
                type: string
              description:
                type: string
              secure:
                type: boolean
              numericInput:
                type: boolean
              softKeys:
                type: boolean
              sequence:
                type: integer
              exampleText:
                type: string
              attribs:
                type: string
      responses:
        '200':
          description: Successful Response
          schema:
            $ref: '#/definitions/PromptState'
    delete:
      summary: 'Delete this promptstate [INTERNAL]'
      tags:
        - prompt-templates
        - internal
      description: 'Deletes a prompt states [INTERNAL]'
      parameters:
        - in: path
          name: id
          description: Prompt State id
          type: string
          required: true
      responses:
        '204':
          description: Successful delete operation
  '/media/prompttemplates/{id}/company/{companyId}':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    put:
      summary: 'Assign this prompttemplate to a company [INTERNAL]'
      tags:
        - prompt-templates
        - internal
      description: 'Assign this prompttemplate to a company [INTERNAL]'
      parameters:
        - in: path
          name: id
          type: string
          required: true
        - in: path
          name: companyId
          type: string
          required: true
      responses:
        '204':
          description: Successful Response
    delete:
      summary: 'Unassign a prompt template from a company [INTERNAL]'
      tags:
        - prompt-templates
        - internal
      description: 'Unassign a prompt template from a company [INTERNAL]'
      parameters:
        - in: path
          name: id
          type: string
          required: true
        - in: path
          name: companyId
          type: string
          required: true
      responses:
        '204':
          description: Successful delete operation
  '/media/prompttemplates/{id}':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    put:
      summary: 'Assign this prompttemplate to a company [INTERNAL]'
      tags:
        - prompt-templates
        - internal
      description: 'Assign this prompttemplate to a company [INTERNAL]'
      parameters:
        - in: path
          name: id
          type: string
          required: true
        - in: body
          name: body
          description: The new prompt template values
          schema:
            type: object
            properties:
              name:
                type: string
              description:
                type: string
              defaultBg:
                type: string
      responses:
        '200':
          description: Successful Response
          schema:
            $ref: '#/definitions/PromptTemplate'
  /media/prompttemplates:
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    post:
      summary: 'Create a new promptset [INTERNAL]'
      tags:
        - prompt-templates
        - internal
      description: Create a new promptset
      parameters:
        - in: body
          name: body
          description: Prompt Template param
          schema:
            type: object
            properties:
              name:
                type: string
              description:
                type: string
              defaultBg:
                type: string
              states:
                type: array
                items:
                  $ref: '#/definitions/PromptState'
      responses:
        '200':
          description: Succesful Response
          schema:
            $ref: '#/definitions/PromptTemplate'
    get:
      summary: Get all Prompt Templates
      tags:
        - prompt-templates
      description: |
        Gets all of the available Prompt Templates for the company the current user belongs to.
        There is expected to be at least one prompt template per device type.  There is no filtering
        as the list is expected to be short
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              id:
                type: string
                format: uuid
              name:
                type: string
              description:
                type: string
  '/media/prompttemplates/{id}/states':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: ID of target Prompt Template
        required: true
        type: string
        format: uuid
    get:
      summary: Get all Prompt States for a Prompt Template
      tags:
        - prompt-templates
      description: |
        Get all states for the specified prompt template.
        States will be returned sorted according to the `sequence` field in the DB.

        Requires role: MEDIA
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/PromptState'
  /sitegroups:
    get:
      tags:
        - sitegroups
      summary: Get all sitegroups
      description: Get all sitegroups associated to the current user. If the user is a COMPANY_ADMIN only show sitegroups they own
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: company
          in: query
          description: retrieve the sitegroups that belongs to the company id
          required: false
          type: string
      responses:
        '200':
          description: The active site groups have been retrieved successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/SiteGroupListType'
        '401':
          $ref: '#/responses/authenticationFailed'
        '500':
          $ref: '#/responses/internalServerError'
    post:
      tags:
        - sitegroups
      summary: '[COMPANY_ADMIN] Create a site group'
      description: Create a new site group. Name is required to be unique. Site and companies can be empty arrays.
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: siteGroupObject
          in: body
          description: The site group object to be created
          required: true
          schema:
            $ref: '#/definitions/SiteGroupRequestType'
      responses:
        '200':
          description: The user groups has been created successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/SiteGroupDetailsType'
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: Cannot find sites or companies
        '409':
          description: |-
            Cannot add a company to its own sitegroup.
            Sitegroup name must be unique.
        '500':
          $ref: '#/responses/internalServerError'
  '/sitegroups/{id}':
    get:
      tags:
        - sitegroups
      summary: '[COMPANY_ADMIN] Get sitegroup by id'
      description: Returns the site group details
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          in: path
          description: The uuid of the sitegroup to get
          type: string
          required: true
      responses:
        '200':
          description: Get success
          schema:
            $ref: '#/definitions/SiteGroupDetailsType'
        '401':
          $ref: '#/responses/authenticationFailed'
        '500':
          $ref: '#/responses/internalServerError'
    put:
      tags:
        - sitegroups
      summary: '[COMPANY_ADMIN] Edit sitegroup'
      description: Edit the sitegroup. Requires COMPANY_ADMIN
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          in: path
          description: The uuid of the sitegroup to get
          type: string
          required: true
        - name: siteGroupObject
          in: body
          description: The site group object to edit
          required: true
          schema:
            $ref: '#/definitions/SiteGroupRequestType'
      responses:
        '200':
          description: Successful edit
          schema:
            $ref: '#/definitions/SiteGroupDetailsType'
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: 'Cannot find sitegroup, site or company'
        '409':
          description: Cannot add company to own sitegroup
        '500':
          $ref: '#/responses/internalServerError'
    delete:
      tags:
        - sitegroups
      summary: '[COMPANY_ADMIN] Delete sitegroup'
      description: Delete the sitegroup. Requires COMPANY_ADMIN
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          in: path
          description: The uuid of the sitegroup to delete
          type: string
          required: true
      responses:
        '204':
          description: Successful delete
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: 'Cannot find sitegroup, site or company'
        '409':
          description: Cannot delete a sitegroup that has sites
        '500':
          $ref: '#/responses/internalServerError'
  /acceptable/email:
    post:
      summary: Check if the Specified Email is Acceptable
      description: |
        Ensures that the specified email address is a valid format, and is not already used by another user of the system.
      tags:
        - users
      parameters:
        - name: details
          in: body
          schema:
            type: object
            properties:
              email:
                type: string
                description: The email address to test
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: boolean
            description: True if the email is acceptable. False otherwise
  '/acceptable/offlinepackage/{name}':
    get:
      summary: Check if the specified Offline Package name is acceptable
      description: |
        Ensures that the specified offline package name is valid and is not already used.
      tags:
        - users
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: name
          in: path
          description: The offline package name to test
          type: string
          required: true
      responses:
        '200':
          description: Successful response
          schema:
            type: boolean
            description: True if the name is acceptable. False otherwise
  /acceptable/password:
    post:
      summary: Check if the Specified Password is Acceptable
      description: |
        Ensures that the specified password meets the complexity rules, and is not on the banned passwords list.
      tags:
        - users
      parameters:
        - name: details
          in: body
          schema:
            type: object
            properties:
              password:
                type: string
                description: The password to test
      responses:
        '200':
          description: Successful response
          schema:
            type: boolean
            description: True if the password is acceptable. False otherwise
  /acceptable/sitename:
    post:
      summary: Check if the Specified Site Name is Acceptable
      description: |
        Ensures that the specified site name meets the special character requirements, and that it is unique within the specified company.
      tags:
        - sites
      parameters:
        - name: details
          in: body
          schema:
            type: object
            properties:
              siteName:
                type: string
                description: The site name to test
              companyId:
                type: string
                format: uuit
                description: The company to check the site name within
                default: The company of the calling user
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: boolean
            description: True if the site name is acceptable. False otherwise
  /acceptable/filename:
    post:
      summary: Check if a file with the same name already exists
      description: |
        Ensures that the specified file name is unique
      tags:
        - software
      parameters:
        - name: details
          in: body
          schema:
            type: object
            properties:
              fileName:
                type: string
                description: The file name to test
      responses:
        '200':
          description: Successful response
          schema:
            type: boolean
            description: True if the filename is acceptable. False otherwise
  /reports:
    get:
      tags:
        - report
      summary: List of available reports
      description: Gets a list of all available reports. This includes reports which are not active.
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/ReportType'
  /reports/aggregator/site-pop:
    get:
      tags:
        - report
      summary: Aggregate data into 'site-pop' table
      description: |
        Aggregate data from 'device-data' table into 'site-pop' table for each site that completed a full day (past the midnight treshold) in the last hour
        Requires Role: ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
  /reports/site/pop:
    post:
      tags:
        - report
      summary: List summary of media files played in sites
      description: List sammary of all media files played in selected sites and date range
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: sitePopReportPayload
          in: body
          description: The site-pop report object
          required: true
          schema:
            $ref: '#/definitions/ReportSitePopRequest'
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/ReportSitePopResponse'
  /reports/alarms:
    get:
      tags:
        - report
      summary: List of available alarm types
      description: List should come from config. May one day come from a DB table and be company specific
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              type: object
              properties:
                id:
                  type: string
                  description: ID
                name:
                  type: string
                  description: Alarm type name
                description:
                  type: string
                  description: Alarm type description
  /reports/statuses:
    get:
      tags:
        - report
      summary: List of available status types
      description: List should come from config
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              type: object
              properties:
                id:
                  type: string
                  description: ID
                name:
                  type: string
                  description: Status type name
                description:
                  type: string
                  description: Status type description
  /reports/metrics:
    get:
      tags:
        - report
      summary: List of available metric types
      description: List should come from config
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              type: object
              properties:
                id:
                  type: string
                  description: ID
                name:
                  type: string
                  description: Metric type name
                description:
                  type: string
                  description: Metric type description
  /reports/versions:
    get:
      tags:
        - report
      summary: List of available version types
      description: List should come from config
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              type: object
              properties:
                id:
                  type: string
                  description: ID
                name:
                  type: string
                  description: Version type name
                description:
                  type: string
                  description: Version type description
  /reports/events:
    get:
      tags:
        - report
      summary: List of available event types
      description: List should come from config
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              type: object
              properties:
                id:
                  type: string
                  description: ID
                name:
                  type: string
                  description: Event type name
                description:
                  type: string
                  description: Event type description
  /reports/status-report:
    post:
      tags:
        - report
      summary: Get Report Data for Status
      description: Status report provides the current status for the devices in selected sites. User can request for specific statuses. Results have a flat structure (no nested arrays) in order to support pagination. This API requires the user to be authenticated, but does not require any specific user role.'
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: statusReportPayload
          in: body
          description: The status report object
          required: true
          schema:
            $ref: '#/definitions/ReportStatusRequest'
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/ReportStatusResponseResultObject'
  /reports/status-report/csv:
    post:
      tags:
        - report
      summary: Get Report Data for Status
      description: Status report provides the current status for the devices in selected sites. User can request for specific statuses. Results will be returned as a CSV file download including a headder row. Results are not paginated. his API requires the user to be authenticated, but does not require any specific user role.'
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: statusReportPayload
          in: body
          description: The status report object
          required: true
          schema:
            $ref: '#/definitions/ReportStatusRequest'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/ReportStatus'
  /reports/version-report:
    post:
      tags:
        - report
      summary: Get Report Data for Version
      description: Version report provides the current version for the devices in selected sites. User can request for specific versions. Results have a flat structure (no nested arrays) in order to support pagination. This API requires the user to be authenticated, but does not require any specific user role.'
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: statusReportPayload
          in: body
          description: The versions history report object
          required: true
          schema:
            $ref: '#/definitions/ReportStatusRequest'
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/ReportStatusResponseResultObject'
  /reports/version-report/csv:
    post:
      tags:
        - report
      summary: Get Report Data for Version
      description: Version report provides the current version for the devices in selected sites. User can request for specific versions. Results will be returned as a CSV file download including a headder row. Results are not paginated. his API requires the user to be authenticated, but does not require any specific user role.'
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: statusReportPayload
          in: body
          description: The versions history report object
          required: true
          schema:
            $ref: '#/definitions/ReportStatusRequest'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/ReportStatus'
  /reports/current-sites-devices:
    post:
      summary: Get current devices at each site
      description: |
        tbc
        Gets device information for the list of specified sites.  Specified sites are validated against the list of
        sites the calling user can see. If no sites are specified then all sites are returned for that user.
      tags:
        - report
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: currentSiteDeviceReportPayload
          in: body
          description: The site device report object
          required: true
          schema:
            $ref: '#/definitions/CurrentSiteDeviceRequest'
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/CurrentSiteDevicesResponse'
  /reports/current-sites-devices/csv:
    post:
      summary: Get current devices at each site as CSV
      description: |
        tbc
        Gets device information for the list of specified sites.  Specified sites are validated against the list of
        sites the calling user can see. If no sites are specified then all sites are returned for that user.
      tags:
        - report
      produces:
        - text/csv
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: currentSiteDeviceReportPayload
          in: body
          description: The site device report object
          required: true
          schema:
            $ref: '#/definitions/CurrentSiteDeviceRequest'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/CurrentSiteDeviceItem'
  /reports/api-audit:
    post:
      summary: Get recorded API requests
      description: |-
        Returns a list with all API requests logged in the database where the generated timestamps of the metric falls within the specified time range of the report.
        Can optionally be filtered by user, base route or status.
        All requests to this API are filtered based on the caller's company to only include results from that company. Requires Role: COMPANY_ADMIN
      tags:
        - report
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: ApiAuditPayload
          in: body
          description: The api audit report object
          required: true
          schema:
            $ref: '#/definitions/ApiAuditRequest'
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/ApiAuditResponse'
        '400':
          description: Bad Request or Date range must be no longer than 30 days
          schema:
            $ref: '#/definitions/ErrorMessage'
  /reports/api-audit/csv:
    post:
      summary: Get recorded API requests as CSV
      description: |-
        Returns a list with all API requests logged in the database where the generated timestamps of the metric falls within the specified time range of the report.
        Can optionally be filtered by user, base route or status.
        All requests to this API are filtered based on the caller's company to only include results from that company. Requires Role: COMPANY_ADMIN
      tags:
        - report
      produces:
        - text/csv
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: ApiAuditPayload
          in: body
          description: The api audit report object
          required: true
          schema:
            $ref: '#/definitions/ApiAuditRequest'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/ApiAuditItem'
        '400':
          description: Bad Request or Date range must be no longer than 30 days
          schema:
            $ref: '#/definitions/ErrorMessage'
  /reports/alarm-history-report:
    post:
      summary: Get Report Data for Alarm History
      description: |
        This service queries the reporting-alarm-history table to get results
        where both the triggered and remedy times of the alarm fall within the
        specified time range for the report.

        If an array of site ids is specified then the result should only include
        alarms which were raised at the specified sites, otherwise, data from
        all sites that the user has access to should be returned.

        Similarly, the result should be filtered to only include alarms which
        matchthe specified alarm type(s) passed to this API.

        Results will be ordered by company name, site name, device serial number
        and then alarm type.  Results have a flat structure (no nested arrays)
        in order to support pagination.

        This API requires the user to be authenticated, but does not require any
        specific user role.
      tags:
        - report
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: AlarmHistoryReportPayload
          in: body
          description: The alarm history report object
          required: true
          schema:
            $ref: '#/definitions/AlarmHistoryReportRequest'
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/AlarmHistoryResponse'
  /reports/alarm-history-report/csv:
    post:
      summary: Get Report Data for Alarm History
      description: |
        This service queries the reporting-alarm-history table to get results
        where both the triggered and remedy times of the alarm fall within the
        specified time range for the report.

        If an array of site ids is specified then the result should only include
        alarms which were raised at the specified sites, otherwise, data from
        all sites that the user has access to should be returned.

        Similarly, the result should be filtered to only include alarms which
        matchthe specified alarm type(s) passed to this API.

        Results will be ordered by company name, site name, alarm name and then
        alarm type.  Results will be returned as a CSV file download including a
        headder row. Results are not paginated.

        This API requires the user to be authenticated, but does not require any
        specific user role.
      tags:
        - report
      produces:
        - text/csv
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: AlarmHistoryReportPayload
          in: body
          description: The alarm history report object
          required: true
          schema:
            $ref: '#/definitions/AlarmHistoryReportRequest'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/AlarmHistoryItem'
  /reports/user-history-report:
    post:
      summary: Get Report Data for User Role History
      description: |
        This service queries the ics_user, auth_history, user_change, and user_change_field table to
        get results and be able to show user role and permissions changes.

        This API requires the user to be authenticated, but does not require any
        specific user role.
      tags:
        - report
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: UserHistoryReportPayload
          in: body
          description: The user history report object
          required: true
          schema:
            $ref: '#/definitions/UserHistoryRequest'
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/UserReportHistoryResponse'
  /reports/user-history-report/csv:
    post:
      summary: Get Report Data for User History
      description: |
        This service queries the ics_user, auth_history, user_change, and user_change_field table to
        get results and be able to show user role and permissions changes.

        This API requires the user to be authenticated, but does not require any
        specific user role.

        Results will be returned as a CSV file download including a headder row.
        Results are not paginated.
      tags:
        - report
      produces:
        - text/csv
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: UserHistoryReportPayload
          in: body
          description: The user history report object
          required: true
          schema:
            $ref: '#/definitions/UserHistoryRequest'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/UserReportHistoryItem'
  '/reports/assets-report/{siteId}':
    get:
      summary: 'Downloads assets reports'
      description: 'It returns an xml file if there is only one asset report, or a .zip file containing multiple xml files with all the assets reports for a specific site'
      tags:
        - report
      produces:
        - text/xml
        - application/zip
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: siteId
          in: path
          type: string
          required: true
          description: 'The id of the site you want to retrieve the assets from'
      responses:
        '200':
          description: 'Package was downloaded successfully'
        '400':
          description: 'Bad Request'
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: 'No permission'
        '404':
          description: 'Site Id does not exists, or site has no device assets'
        '405':
          description: 'Wrong method. Must use GET'
        '500':
          $ref: '#/responses/internalServerError'
  /media/assets:
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    post:
      summary: Upload and Create New Asset
      description: "This service uploads a new asset into ICS.  The created asset will belong to the company which the uploader user belongs to. \n The request must contain `multipart/form-data` content (this is different to all other API calls which must contain JSON), and the request body must contain exactly two parts: \n1. The first part must have type `application/x-www-form-urlencoded`. \n2. The second part must contain the uploaded file contents.  The allowed file types in this release are: \n\t* jpg\n * png\n * webm\n\nThe file contents will be streamed directly out to S3 without being persisted on the API server. The file will be saved with the following filename format: `<asset id guid>/<originalfilename>`. \n\nRequired role: POWER_USER or better"
      tags:
        - assets
      consumes:
        - multipart/form-data
      parameters:
        - name: assetName
          in: formData
          description: Name of the asset being created
          required: true
          type: string
          minLength: 3
        - name: fileContent
          in: formData
          type: file
          required: true
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/Asset'
        '400':
          description: Unknown file type or Unknown font format
    get:
      summary: View/Search Available Assets
      tags:
        - assets
      description: |-
        This service returns a list (paginated) of all assets matching the specified filter(s). Assets are limited to only be visbile by users from the company that owns the asset.

        The following filters are all optional, and if specified, will be combined with an `AND` operator:

        * `name` - If specified, then only assets with a name that matches (partial) with the specified asset name will be returned
        * `type` - If specified then only assets of the specified type will be returned (IMAGE_STRICT = only .png or .jpg, IMAGE_GIF = only .gifs)
        * `signed` - If specified then only assets which are signed (or unsigned) will be returned

        Required role: USER or better
      parameters:
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: order
          in: query
          required: false
          type: string
          enum:
            - uploaded
            - name
            - uploader
          default: uploaded
        - name: name
          description: Filter assets by name (partial match)
          in: query
          required: false
          type: string
        - name: type
          description: Filter assets by type
          in: query
          required: false
          type: string
          enum:
            - IMAGE
            - VIDEO
            - IMAGE_STRICT
            - IMAGE_GIF
        - name: active
          description: Filter assets by whether they are active or not or both, this parameter accepts (TRUE,FALSE,BOTH)
          in: query
          required: false
          type: string
          default: 'TRUE'
        - name: signed
          description: Filter assets by whether they are signed
          in: query
          required: false
          type: boolean
        - name: width
          description: Filter to assets with this exact width
          in: query
          required: false
          type: integer
        - name: height
          description: Filter to assets with this exact height
          in: query
          required: false
          type: integer
        - name: uploader
          description: Filter to assets uploaded by a specific user
          in: query
          required: false
          type: string
          format: uuid
        - name: videoExtension
          description: Filter to assets either G6 (.webm AND .bin) or G7 (.webm) device videos
          in: query
          required: false
          type: string
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              resultsMetadata:
                $ref: '#/definitions/ResultsMetadata'
              results:
                type: array
                items:
                  $ref: '#/definitions/Asset'
    put:
      summary: (Internal) Create New Asset
      tags:
        - internal
        - assets
      description: |-
        This service creates a new asset record for a file which already exists in S3. The created asset will belong to the company specified on the API call.

        Required role: ICS_SYSTEM
      parameters:
        - name: asset
          in: body
          description: Details of the asset being created
          required: true
          schema:
            type: object
            properties:
              name:
                type: string
              type:
                type: string
              company:
                type: string
                format: uuid
              width:
                type: integer
              height:
                type: integer
              sourceUrl:
                type: string
                description: The URL of the asset source (in S3)
                format: URL
              thumbnailUrl:
                description: 'The URL of the asset thumbnail image (in S3) Image will be 640px wide, with height variable 400-480px'
                type: string
                format: URL
              package:
                description: Asset package this asset belongs to (if available)
                type: string
                format: uuid
              status:
                description: Status the asset is currently in
                type: string
                enum:
                  - NEW
                  - PROCESSED
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/Asset'
  '/media/assets/{id}':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: ID of target Asset
        required: true
        type: string
        format: uuid
    get:
      summary: Get Asset By ID
      description: |-
        Caller must be from the same company as the asset.

         If called by SYSTEM user, company restriction is ignored and actual file URLs are returned
      tags:
        - assets
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/Asset'
    put:
      summary: Update Asset by ID
      description: Users can only edit the name
      tags:
        - assets
      parameters:
        - name: asset
          in: body
          required: true
          schema:
            type: object
            properties:
              name:
                type: string
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/Asset'
    patch:
      summary: (Internal) Update Asset by ID
      description: |-
        TBC
         Requires Role: ICS_SYSTEM
      tags:
        - assets
      parameters:
        - name: asset
          in: body
          required: true
          schema:
            type: object
            properties:
              name:
                type: string
              type:
                type: string
              thumbnailUrl:
                type: string
                format: url
              sourceUrl:
                type: string
                format: url
              package:
                type: string
                format: uuid
              width:
                type: integer
              height:
                type: integer
              status:
                type: string
                enum:
                  - NEW
                  - PROCESSED
                  - ARCHIVED
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/Asset'
    delete:
      summary: Delete asset by ID
      tags:
        - assets
      description: TBC
      responses:
        '204':
          description: Successful response
  '/media/assets/{id}/thumbnail':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Id of target Asset
        required: true
        type: string
        format: uuid
    get:
      summary: Get Asset Thumbnail Image
      tags:
        - assets
      description: Caller must be from the same company as the asset
      responses:
        '200':
          description: Successful response (image file data)
        '404':
          description: 'Asset does not exist, or no thumbnail available'
  '/media/assets/{id}/source':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Id of target Asset
        required: true
        type: string
        format: uuid
    get:
      summary: Get Asset Source
      tags:
        - assets
      description: Caller must be from the same company as the asset
      responses:
        '200':
          description: Successful response (image file data)
        '404':
          description: 'Asset does not exist, or no source file available'
  /media/assetpackages:
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    get:
      summary: Get Asset Packages
      tags:
        - asset-packages
      parameters:
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: order
          description: Sort order for results
          in: query
          required: false
          type: string
          enum:
            - name-asc
            - name-desc
            - uploaded-asc
            - uploaded-desc
          default: uploaded-desc
        - name: name
          description: Filter results by asset name (partial)
          in: query
          required: false
          type: string
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              resultsMetadata:
                $ref: '#/definitions/ResultsMetadata'
              results:
                type: array
                items:
                  $ref: '#/definitions/AssetPackage'
    post:
      summary: Upload and Create New Asset Package
      tags:
        - asset-packages
      description: |-
        Allowed file types

         * tar.gz.bin
         * tgz.bin
         * pkg
         * zip

         Required Role: POWER_USER or better
      consumes:
        - multipart/form-data
      parameters:
        - name: assetPackageName
          in: formData
          description: Name of the asset package being created
          required: true
          type: string
          minLength: 3
        - name: fileContent
          in: formData
          type: file
          required: true
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/AssetPackage'
  '/media/assetpackages/{id}':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: ID of target Asset package
        required: true
        type: string
        format: uuid
    get:
      summary: |-
        Get Asset Package by ID

         If called by SYSTEM user, company restriction is ignored
      tags:
        - asset-packages
        - internal
      description: Get Asset Package by ID
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/AssetPackage'
    put:
      summary: Update Asset Package by ID
      tags:
        - asset-packages
      description: Allows users to edit the name and deviceType of a package
      parameters:
        - name: assetPackage
          in: body
          required: true
          schema:
            type: object
            properties:
              name:
                type: string
              deviceType:
                type: string
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/AssetPackage'
    patch:
      summary: (INTERNAL) Update Asset Package by ID
      tags:
        - asset-packages
        - internal
      description: Allows internal system to edit package
      parameters:
        - name: assetPackage
          in: body
          required: true
          schema:
            type: object
            properties:
              deviceType:
                type: string
              name:
                type: string
              signed:
                type: boolean
              status:
                type: string
                enum:
                  - NEW
                  - PROCESSED
                  - ERROR
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/AssetPackage'
    delete:
      summary: Delete Asset Package by ID
      tags:
        - asset-packages
      description: Deletes an Asset Package including all assets associated with it
      responses:
        '204':
          description: Successful response
  '/media/assetspackages/{id}/assets':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: ID of target Asset Package
        required: true
        type: string
        format: uuid
    get:
      summary: Get Assets from a Package by ID
      tags:
        - asset-packages
        - assets
      description: Caller must be from the same company as the asset package
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              resultsMetadata:
                $ref: '#/definitions/ResultsMetadata'
              results:
                type: array
                items:
                  $ref: '#/definitions/Asset'
  '/media/touchmaps':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    get:
      summary: Get all touchmaps related to this company. Requires MEDIA_DESIGNER role
      tags:
        - touchmap
      description: Get all touchmaps related to this company. Requires MEDIA_DESIGNER role
      parameters:
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: q
          description: Search query filter by name or description (partial)
          in: query
          required: false
          type: string
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              resultsMetadata:
                $ref: '#/definitions/ResultsMetadata'
              results:
                type: array
                items:
                  $ref: '#/definitions/Touchmap'
    post:
      summary: Create a new touchmap. Requires MEDIA_DESIGNER
      tags:
        - touchmap
      description: Create a new touchmap. Requires MEDIA_DESIGNER
      parameters:
        - name: touchmap
          in: body
          schema:
            type: object
            properties:
              name:
                type: string
              description:
                type: string
              areas:
                type: array
                items:
                  $ref: '#/definitions/TouchmapArea'
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/Touchmap'
  '/media/touchmap/{id}':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Touchmap id
        required: true
        type: string
        format: uuid
    get:
      summary: Get Touchmap by Id
      tags:
        - touchmap
      description: Gets the Touchmap by ID. Requires MEDIA_DESIGNER role.
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/Touchmap'
    put:
      summary: Edit Touchmap by Id
      tags:
        - touchmap
      description: Edits the touchmaps metadata. Requires MEDIA_DESIGNER role.
      parameters:
        - name: touchmap
          in: body
          schema:
            type: object
            properties:
              name:
                type: string
              description:
                type: string
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/Touchmap'
    delete:
      summary: Delete this touchmap
      tags:
        - touchmap
      description: Deletes the touchmap. Requires MEDIA_DESIGNER. Can't delete a touchmap while it is associated to a prompt
      responses:
        '204':
          description: Successful response
        '409':
          description: Cannot delete a touchmap that is associated to a prompt
  '/media/touchmaps/{id}/areas':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Touchmap id
        required: true
        type: string
        format: uuid
    put:
      summary: Update the areas property of a touchmap
      tags:
        - touchmap
      description: Updates the touchmap's touch areas. Requires MEDIA_DESIGNER.
      parameters:
        - in: body
          name: areas
          schema:
            type: array
            items:
              $ref: '#/definitions/TouchmapArea'
      responses:
        '204':
          description: Successful response
  '/media/touchmaps/{id}/promptsets':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Touchmap id
        required: true
        type: string
        format: uuid
    get:
      summary: List all promptsets associated with a touchmap
      tags:
        - touchmap
      description: List all promptsets associated with a touchmap. Requires MEDIA_DESIGNER.
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                version:
                  type: string
                  description: version value (semver)
                name:
                  type: string
                status:
                  type: string
  /media/promptsets:
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    get:
      summary: Get all Prompt Sets
      tags:
        - prompt-sets
      description: |
        Gets the summary of all Prompt Sets for the company the current user belongs to.
        Requires role: USER or better
      parameters:
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: sort
          description: Sort order for results
          in: query
          required: false
          type: string
          enum:
            - modified
            - name
            - creator
            - status
          default: modified
        - name: q
          description: Filter results by Prompt Set name (partial). If a fingerprint is present it will do a partial match to that as well.
          in: query
          required: false
          type: string
        - name: promptSetProfileName
          description: Filter results by promptSetProfileName
          in: query
          required: false
          type: string
        - name: state
          description: Filter to prompt sets in a specific State
          in: query
          required: false
          type: string
          enum:
            - DRAFT
            - PUBLISHING
            - PUBLISHED
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              resultsMetadata:
                $ref: '#/definitions/ResultsMetadata'
              results:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                      format: uuid
                    version:
                      type: string
                      description: version value (semver)
                    name:
                      type: string
                    deviceType:
                      type: string
                    promptSetProfileName:
                      type: string
                    template:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                        name:
                          type: string
                        description:
                          type: string
                    created:
                      type: string
                      format: date-time
                    createdBy:
                      $ref: '#/definitions/User'
                    modified:
                      type: string
                      format: date-time
                    modifiedBy:
                      $ref: '#/definitions/User'
                    promptThumnails:
                      description: |
                        Set of thumbnail image URLs which allow a simple, preview of the
                        prompt set contents.  This will contain the thumbnails of the default
                        prompts assigned to each state (ordered by the state sequence)
                      type: array
                      items:
                        type: string
                        description: Thumbnail image URL
                    status:
                      type: string
                      enum:
                        - DRAFT
                        - PUBLISHING
                        - PUBLISHED
                    childrenCount:
                      type: number
                      description: Total number of children (cloned) promptsets
    post:
      summary: Create new Prompt Set
      tags:
        - prompt-sets
      description: |
        Creates a new Prompt Set using the specified Prompt Template.
        The Prompt Set is initially empty and needs to be populated before
        it can be published.

        The new prompt set belong to the company of the creating user.

        Requires role: POWER_USER
      parameters:
        - name: promptSet
          in: body
          schema:
            type: object
            properties:
              template:
                description: Prompt Template ID
                type: string
                format: uuid
              promptSetProfileName:
                description: promptSet Profile Name
                type: string
              name:
                type: string
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/PromptSet'
  /media/dayparts:
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    get:
      summary: Get all Day parts
      tags:
        - day-parts
      description: |
        Gets all the Dayparts belonging to the user's company.

        Required Role: USER or better.
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              id:
                type: string
                format: uuid
              name:
                type: string
              start:
                type: integer
                format: date-time
              end:
                type: integer
                format: date-time
    post:
      summary: Create new Day part.
      tags:
        - day-parts
      description: |
        Creates a new Day part. The start timestamp should be less than end timestamp.

        Required Role: POWER_USER.
      parameters:
        - name: dayPart
          in: body
          schema:
            type: object
            properties:
              name:
                type: string
              start:
                type: integer
                format: date-time
              end:
                type: integer
                format: date-time
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              id:
                type: string
                format: uuid
              name:
                type: string
              start:
                type: integer
                format: date-time
              end:
                type: integer
                format: date-time
        '400':
          description: Invalid request body
  '/media/dayparts/{id}':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Id of target day part
        required: true
        type: string
        format: uuid
    get:
      summary: Get Day Part by ID
      tags:
        - day-parts
      description: |
        Gets the day part by ID. The daypart must belong to the user's company.

        Required Role: USER or better.
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              id:
                type: string
                format: uuid
              company:
                type: string
                format: uuid
                description: Company's id
              name:
                type: string
              start:
                type: integer
                format: date-time
              end:
                type: integer
                format: date-time
        '404':
          description: Request with invalid ID or if dayparts doesn't belongs to user's company
    put:
      summary: Update Day Part by ID
      tags:
        - day-parts
      description: |
        Updates the specified day part. The day part should belong to the user's company
         and updates with start timestamps greater than end timestamp
         are invalidated.

        Requires role: POWER_USER
      parameters:
        - name: dayPart
          in: body
          schema:
            type: object
            properties:
              name:
                type: string
                description: Name to give to the day part
              start:
                type: integer
                format: date-time
              end:
                type: integer
                format: date-time
      responses:
        '200':
          description: Successful response
          schema:
            description: |
              Day part queried by ID
            type: object
            properties:
              id:
                type: string
                format: uuid
              company:
                type: string
                format: uuid
                description: Company's id
              name:
                type: string
              start:
                type: integer
                format: date-time
              end:
                type: integer
                format: date-time
        '404':
          description: Daypart not found or doesn't belongs to user's company
    delete:
      summary: Delete Day Part by ID
      tags:
        - day-parts
      description: |
        Deletes the specified day part. The daypart should belong to the user's company

         Requires Role: POWER_USER
      responses:
        '204':
          description: Successful response
        '404':
          description: Day part not found or doesn't belongs to the user's company
  '/media/promptsets/{id}':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Id of target Prompt Set
        required: true
        type: string
        format: uuid
    get:
      summary: Get Prompt Set by ID
      tags:
        - prompt-sets
      description: |
        Gets the full details of a specific prompt set by ID.  This includes all
        relevant prompt states, even if the are not populated with a default prompt.

        Requires role: USER or better
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/PromptSet'
    delete:
      summary: Delete Prompt Set by ID
      tags:
        - prompt-sets
      description: |
        Deletes (archives) the specified prompt set.  This does not effect any existing rollouts which reference this prompt set, nor does it effect any assets referenced by this prompt set.

        Requires role: POWER_USER or better
      responses:
        '204':
          description: Successful response
        '404':
          description: Prompt set not found
    patch:
      summary: Update PromptSet as a System User
      tags:
        - prompt-sets
        - internal
      description: Update status of the specified prompt set.
      parameters:
        - name: promptSet
          in: body
          schema:
            type: object
            properties:
              status:
                type: string
      responses:
        '204':
          description: Successful response
        '404':
          description: Prompt set not found
    put:
      summary: Update Prompt Set by ID
      tags:
        - prompt-sets
      description: |
        Edits the promptset metadata. This doesn't update the promptset version.

        Requires role: MEDIA_DESIGNER
      parameters:
        - name: promptSet
          in: body
          schema:
            type: object
            properties:
              name:
                type: string
              fontColor:
                type: string
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/PromptSet'
  '/media/promptsets/{id}/prompts':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - in: path
        name: id
        description: Prompt ID
        type: string
        required: true
    put:
      summary: Save list of prompts
      tags:
        - prompt-sets
      description: |-
        Save the values of prompts in a promptset.

        Requires Role: MEDIA_DESIGNER
      parameters:
        - in: body
          name: prompt
          description: The prompt values to save
          schema:
            type: array
            items:
              type: object
              properties:
                promptId:
                  type: string
                  description: Prompt UUID
                elements:
                  type: object
                  description: Array of ODML elements
                softkeys:
                  type: array
                  items:
                    type: object
                    properties:
                      keycode:
                        type: string
                      fontSize:
                        type: string
                      fontColor:
                        type: string
                      label:
                        type: string
                      softkey:
                        type: number
                touchmap:
                  type: object
                  properties:
                    id:
                      type: string
                      description: touchmap id
                contactless:
                  type: boolean
                  description: 'Only g6 will ever set this to true'
                transactionState:
                  type: string
                  enum:
                    - null
                    - fueling
                    - idle
      responses:
        '200':
          description: Successful save
          schema:
            type: object
            properties:
              version:
                type: string
                description: Bumped up prompt version
              modified:
                type: string
                format: date-time
                description: Modified date
              modifiedBy:
                type: object
                properties:
                  id:
                    type: string
                    description: User UUID
                  name:
                    type: string
                    description: User name
                  email:
                    type: string
                    description: User email address
        '400':
          description: Invalid softkeys/elements
        '404':
          description: Prompt/PromptSet/Touchmap not found
  '/media/promptsets/{id}/bg':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Id of target Prompt Set
        required: true
        type: string
        format: uuid
    put:
      summary: "Update the promptset's background Requires Role: MEDIA_DESIGNER"
      tags:
        - prompt-sets
      description: |-
        Updates the promptset's background.
        Changing the background will automatically update the background of all the non-locked background prompts in the set.
        Updates the promptset version.
        Set force=true to force update even the locked prompts.
        Requires Role: MEDIA_DESIGNER
      parameters:
        - name: bg
          in: query
          description: Can be a 6 character hex color (eg. ff00ff) or an asset id
          type: string
          required: true
        - name: force
          in: query
          description: default = false. Setting to 'true' will force locked background elements to update.
          type: boolean
          required: false
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/PromptSet'
        '400':
          description: Invalid bg string format
        '404':
          description: Cannot find background asset or promptset
  '/media/promptsets/{id}/font':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Id of target Prompt Set
        required: true
        type: string
        format: uuid
    put:
      summary: "Update the promptset's font color/face/type Requires Role: MEDIA_DESIGNER"
      tags:
        - prompt-sets
      description: |-
        Updates the promptset's font color/face/type.
        Changing the font color/face/type will not affect existing text and input prompts in the set.
        Changing the font color/face/type and setting all=true will automatically update existing text and input prompt in the set.
        Updates the promptset version.
        Requires Role: MEDIA_DESIGNER
        At least one of parameter (fontColor, fontFace, fontSize) must be provided.
      parameters:
        - name: fontColor
          in: query
          description: Can be a 6 character hex color (eg. ff00ff)
          type: string
        - name: fontFace
          in: query
          description: Can be a string value or string UUID, only works when All=true is provided. Max-Length = 50 Chars
          maxLength: 50
          type: string
        - name: fontSize
          in: query
          description: Can be a non-negative integer, only works when All=true is provided. Integer between 1 ~ 380
          type: number
          minimum: 1
          maximum: 380
        - name: all
          in: query
          description: default = false. Setting to 'true' will update exisiting text prompts font color.
          type: boolean
          required: false
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/PromptSet'
        '400':
          description: Invalid format
        '404':
          description: Cannot find promptset
  '/media/promptsets/{id}/approvers':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Id of target Prompt Set
        required: true
        type: string
        format: uuid
    get:
      summary: Get list of approvers for this promptset
      tags:
        - prompt-sets
      description: |-
        Returns a list of Approvers.

        Requires Role: ANY MEDIA ROLE
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              type: object
              properties:
                id:
                  type: string
                  format: guid
                name:
                  type: string
                  description: User's fullname
                email:
                  type: string
                  description: User's email
                action:
                  type: string
                  description: APPROVE or REJECT or blank
                modified:
                  type: string
                  description: Last modified date
  '/media/promptsets/{id}/clone':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Id of target Prompt Set
        required: true
        type: string
        format: uuid
    post:
      summary: Clone Prompt Set
      tags:
        - prompt-sets
      description: |
        Creates a new Prompt Set which is a perfect copy of the specified Prompt Set.
        The newly created prompt set will be in `DRAFT` status and will take on new
        name specified in the request body.
      parameters:
        - name: promptSet
          in: body
          schema:
            type: object
            properties:
              name:
                type: string
                description: Name to give the newly cloned prompt set
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/PromptSet'
  '/media/promptsets/{id}/preview':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Id of target Prompt Set
        required: true
        type: string
        format: uuid
    post:
      summary: Preview Prompt Set
      tags:
        - prompt-sets
      description: |-
        Creates a copy of this Prompt with status = GENERATING_PREVIEW. This invokes the lambda packager.
        Requires Role: MEDIA_DESIGNER
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/PromptSet'
        '404':
          description: Cannot find prompt set
        '409':
          description: Invalid status. Only prompts with status = DRAFT can go into PREVIEW
  '/media/promptsets/{id}/approve':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Id of target Prompt Set
        required: true
        type: string
        format: uuid
    post:
      summary: Approve Prompt Set
      tags:
        - prompt-sets
      description: |-
        Triggers the lambda packager and sets the status to PUBLISHING as soon as 2 approvers have approved. This doesn't clone a promptset.
        Requires Role: MEDIA_APPROVER
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/PromptSet'
        '403':
          description: User doesn't have access. Or user is not on the approver list
        '404':
          description: Cannot find prompt set
        '409':
          description: Invalid status. Only prompts with status = FOR_APPROVAL can be approved
  '/media/promptsets/{id}/reject':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Id of target Prompt Set
        required: true
        type: string
        format: uuid
    post:
      summary: Reject Prompt Set
      tags:
        - prompt-sets
      description: |-
        When at least 1 approver rejects a promptset, the promptset status is set to REJECTED. Subsequent requests to approve or reject this promptset will be rejected.
        Requires Role: MEDIA_APPROVER
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/PromptSet'
        '403':
          description: User doesn't have access. Or user is not on the approver list
        '404':
          description: Cannot find prompt set
        '409':
          description: Invalid status. Only prompts with status = FOR_APPROVAL can be rejected
  '/media/promptsets/{id}/forapproval':
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
      - name: id
        in: path
        description: Id of target Prompt Set
        required: true
        type: string
        format: uuid
    post:
      summary: Submit a promptset for approval
      tags:
        - prompt-sets
      description: |-
        Creates a copy of this Prompt with status = FOR_APPROVAL. Notifies the list of approvers.
        Requires Role: MEDIA_DESIGNER
      parameters:
        - name: approvers
          in: body
          schema:
            type: object
            properties:
              approvers:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                      format: guid
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/PromptSet'
        '404':
          description: Cannot find prompt set
        '409':
          description: Invalid status. Only prompts with status = PREVIEW can go into FOR_APPROVAL
  /media/prompttypes:
    parameters:
      - $ref: '#/parameters/AuthorizationTokenParam'
    get:
      summary: Get Prompt Types
      tags:
        - prompt-sets
      description: |-
        Get the available Prompt Types. Can optionally be filtered to only Prompt Types compatible with a specific Prompt State.

        Requires role: USER or better
      parameters:
        - name: state
          description: Filter to only types compatible with the specified prompt state
          in: query
          required: false
          type: string
          format: uuid
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/PromptType'
  /emails/token/value:
    get:
      tags:
        - automation
      summary: Returns the value of an email token by user email and type
      description: Get the email token value given a user email and email token type
      produces:
        - application/json
      parameters:
        - name: userEmail
          in: query
          description: The email address of the user assigned to the email token
          required: true
          type: string
        - name: type
          in: query
          description: The type of email token
          required: true
          type: string
        - '$ref': '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
  /emails/token/expire:
    patch:
      tags:
        - automation
      summary: Update the expiry date of an email token
      description: Update the expiry date of an email token given a users email and email token type
      produces:
        - application/json
      parameters:
        - name: userEmail
          in: query
          description: The email address of the user assigned to the email token
          required: true
          type: string
        - name: type
          in: query
          description: The type of email token
          required: true
          type: string
        - name: expires
          in: body
          description: The expiry date on the email token to update
          required: true
          schema:
            type: object
            properties:
              expires:
                type: string
                description: The expiry date on the email token to update
        - '$ref': '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/EmailTokenUpdateResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
  /prompts/id:
    get:
      tags:
        - automation
      summary: Returns the id of a prompt by prompt set id and prompt state id
      description: Get the id of a prompt given a prompt set and prompt state
      produces:
        - application/json
      parameters:
        - name: promptSetId
          in: query
          description: The id of a prompt set
          required: true
          type: string
        - name: promptStateId
          in: query
          description: The id of a prompt state
          required: true
          type: string
        - '$ref': '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
  '/devices/{id}/states/history':
    get:
      tags:
        - devices
      summary: List all states/erros events of a device
      description: Retrieve up to 24 hours of device states/error events.
      parameters:
        - name: id
          in: path
          type: string
          format: uuid
          required: true
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/PageKeyParam'
        - $ref: '#/parameters/PageMinSizeParam'
        - name: start
          in: query
          required: false
          type: string
          format: date-time
        - name: end
          in: query
          required: false
          type: string
          format: date-time
        - name: level
          in: query
          required: false
          type: array
          items:
            type: string
            enum:
              - trace
              - debug
              - info
              - notice
              - warn
              - error
              - critical
              - fatal
        - name: q
          in: query
          required: false
          type: string
        - name: avanced
          in: query
          required: false
          type: boolean
      responses:
        '200':
          description: Successfully response
          schema:
            $ref: '#/definitions/DeviceStateHistoryResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Serial number does not exist, device is not active or user has no access to it
        '500':
          $ref: '#/responses/internalServerError'
  '/devices/serialnumber/{serialNumber}':
    get:
      tags:
        - devices
      summary: Find a device by serial number
      description: Find a device by serial number
      parameters:
        - $ref: '#/parameters/deviceSerialNumberParam'
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: serviceRecipientId
          in: query
          required: false
          type: string
          format: uuid
      responses:
        '200':
          description: Successfully response
          schema:
            $ref: '#/definitions/DeviceGetResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Serial number does not exist, device is not active or user has no access to it
        '500':
          $ref: '#/responses/internalServerError'
  '/devices/serialnumber/{serialNumber}/certificate':
    put:
      tags:
        - devices
      summary: Update a device certificate
      description: |
        Update a device certificate
        Requires Role: ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/deviceSerialNumberParam'
        - $ref: '#/parameters/AuthorizationTokenParam'
        - in: body
          name: body
          schema:
            type: object
            properties:
              certificate:
                type: string
                description: a device certificate to update
              isJsonCertificates:
                type: boolean
                description: if true then certificate is a stringified array of JSON Object
      responses:
        '200':
          description: Successfully response
          schema:
            $ref: '#/definitions/DeviceCertificateUpdateResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Serial number does not exist, device is not active or user has no access to it
        '500':
          $ref: '#/responses/internalServerError'
  '/devices/{id}':
    get:
      tags:
        - devices
      summary: Find a device by id
      description: Find a device by id
      parameters:
        - $ref: '#/parameters/deviceIdParam'
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successfully response
          schema:
            $ref: '#/definitions/DeviceGetResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Serial number does not exist, device is not active or user has no access to it
        '500':
          $ref: '#/responses/internalServerError'
    put:
      tags:
        - devices
      summary: Update a device by id
      description: Update a device by id
      parameters:
        - $ref: '#/parameters/deviceIdParam'
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: Device data to be updated.
          required: true
          schema:
            $ref: '#/definitions/DeviceUpdate'
      responses:
        '200':
          description: Successfully response
          schema:
            $ref: '#/definitions/DeviceUpdateResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Serial number does not exist, device is not active or user has no access to it
        '500':
          $ref: '#/responses/internalServerError'
  '/devices/{id}/data':
    put:
      tags:
        - devices
      summary: 'Upload status data of this device, the data will be stored for further processing'
      description: 'This allows a device to upload its status data, which may be used to trigger alarm notification etc '
      parameters:
        - $ref: '#/parameters/deviceIdParam'
        - $ref: '#/parameters/deviceAuthTokenParam'
        - $ref: '#/parameters/icsDeviceTime'
        - name: body
          in: body
          description: Message to be uploaded.
          required: true
          schema:
            $ref: '#/definitions/DataUploadPayload'
      responses:
        '204':
          description: Message received successfully
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: id may be null or can't find the target device for the given id
        '405':
          description: Must use PUT method
        '500':
          description: Failed to receive the message
  '/devices/{id}/swap':
    post:
      tags:
        - devices
      summary: Swap this device with another device
      description: Moves this device to the site specified in the request. The newDevice will get the configs of this device.
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/deviceIdParam'
        - name: body
          in: body
          description: device to swap with including their names
          required: True
          schema:
            type: object
            properties:
              oldDeviceName:
                type: string
              newDevice:
                type: object
                properties:
                  id:
                    type: number
                  name:
                    type: number
      responses:
        '200':
          description: 'Device swapped successfuly. Job to install config files has been created.'
          schema:
            type: object
            properties:
              filesCount:
                type: integer
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: id may be null or can't find the target device for the given id
        '405':
          description: Must use POST method
        '500':
          description: Failed to receive the message
  '/devices/{id}/self':
    get:
      tags:
        - devices
      summary: Get Device Metadata
      description: 'Get the device serial number, site and timezone information'
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/deviceIdParam'
        - $ref: '#/parameters/deviceAuthTokenParam'
      responses:
        '200':
          description: Successfully response
          schema:
            type: object
            properties:
              id:
                type: integer
              serial:
                type: string
              type:
                type: string
              site:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  timezone:
                    type: string
                  deviceCount:
                    type: integer
                  company:
                    type: object
                    properties:
                      id:
                        type: string
                      name:
                        type: string
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: id may be null or can't find the target device for the given id
        '500':
          $ref: '#/responses/internalServerError'
  /devices/{id}/rollouts/{rolloutId}:
    get:
      tags:
        - devices
      summary: Get job history of the device's specified rollout
      description: "Get the job history of the device's specified rollout. Accessible by users with USER roles and up."
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/deviceIdParam'
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: rolloutId
          in: path
          description: The rollout id
          required: true
          type: integer
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              softwareName:
                type: string
              installJob:
                type: object
                properties:
                  id:
                    type: string
                  status:
                    type: integer
                  data:
                    type: string
                  history:
                    type: array
                    items:
                      type: object
                      properties:
                        status:
                          type: integer
                        message:
                          type: string
                        created:
                          type: string
                          format: 'date-time'
              downloadJob:
                type: object
                properties:
                  id:
                    type: string
                  status:
                    type: integer
                  data:
                    type: string
                  history:
                    type: array
                    items:
                      type: object
                      properties:
                        status:
                          type: integer
                        message:
                          type: string
                        created:
                          type: string
                          format: 'date-time'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: device or rollout not found or inaccessible
        '500':
          $ref: '#/responses/internalServerError'
  '/devices/{id}/reboot':
    post:
      tags:
        - devices
      summary: Reboot a specific device
      description: Sends a reboot job message to SQS job queue to reboot a specific device.
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/deviceIdParam'
      responses:
        '200':
          description: 'A job message to reboot a specific device was successfully sent to SQS job queue.'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: id may be null or can't find the target device for the given id
        '405':
          description: Must use POST method
        '409':
          description: Should allow to reboot ONLINE devices only
        '500':
          $ref: '#/responses/internalServerError'
  /devices/reboot:
    post:
      tags:
        - devices
      summary: Reboot a set of devices based on specified filters
      description: Sends batches of reboot job messages to SQS job queue to reboot devices.
      produces:
        - application/json
      consumes:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              dryRun:
                type: boolean
                description: If set to true, this API will not actually reboot the device. Default value is true
              devices:
                type: array
                description: Array of device serial numbers
                items:
                  type: string
              sites:
                type: array
                description: Array of site ids (UUID format)
                items:
                  type: string
                  format: uuid
              tags:
                type: array
                description: Array of tag ids
                items:
                  type: number
              siteGroups:
                type: array
                description: Array of siteGroup ids (UUID format)
                items:
                  type: string
                  format: uuid
              schedule:
                type: string
                format: date-time
                description: Datetime in epoch/unix timestamp
            required:
              - dryRun
      responses:
        '200':
          description: 'A job message to reboot specific device/s was successfully sent to SQS job queue.'
          schema:
            type: object
            properties:
              validDevices:
                type: array
                description: Array of serial numbers of valid devices
                items:
                  type: string
              excludedDevices:
                type: array
                description: Array of serial numbers of invalid devices
                items:
                  type: string
        '400':
          description: Bad Request.
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission or Unauthorized User
        '404':
          description: Can't find the target device based on the specified filters
        '405':
          description: Must use POST method
        '500':
          $ref: '#/responses/internalServerError'
  '/devices/{id}/recommission':
    put:
      tags:
        - devices
      summary: Recommission a Device
      description: Reset authentication or recommission a specific device
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/deviceIdParam'
      responses:
        '200':
          description: 'Device have been successfully recomissioned.'
          schema:
            type: object
            properties:
              windowEnd:
                type: string
                description: The device's new registration window end date
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Id may be null or can't find the target device for the given id
        '405':
          description: Must use PUT method
        '500':
          $ref: '#/responses/internalServerError'
  /devices/recommission:
    post:
      tags:
        - devices
      summary: Bulk Recommission Devices
      description: Reset authentication or recommission a set of devices based on specified filters
      produces:
        - application/json
      consumes:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              dryRun:
                type: boolean
                description: If set to true, this API will not actually recommission the device. Default value is true
              devices:
                type: array
                description: Array of device serial numbers
                items:
                  type: string
              sites:
                type: array
                description: Array of site ids (UUID format)
                items:
                  type: string
                  format: uuid
              tags:
                type: array
                description: Array of tag ids
                items:
                  type: number
              siteGroups:
                type: array
                description: Array of siteGroup ids (UUID format)
                items:
                  type: string
                  format: uuid
            required:
              - dryRun
      responses:
        '200':
          description: 'Device/s have been successfully recomissioned.'
          schema:
            type: object
            properties:
              validDevices:
                type: array
                description: Array of serial numbers of valid devices
                items:
                  type: string
              excludedDevices:
                type: array
                description: Array of serial numbers of invalid devices
                items:
                  type: string
              result:
                type: object
                properties:
                  windowEnd:
                    type: string
                    description: The devices' new registration window end date
        '400':
          description: Bad Request.
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission or Unauthorized User
        '404':
          description: Can't find the target device based on the specified filters
        '405':
          description: Must use POST method
        '500':
          $ref: '#/responses/internalServerError'
  /devices/bulkoperations/summary:
    get:
      tags:
        - devices
      summary: Get summary of device bulk operations.
      description: Get summary of device bulk operations.
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: object
            properties:
              resultsMetaData:
                type: object
                properties:
                  totalResults:
                    type: integer
                  pageIndex:
                    type: integer
                  pageSize:
                    type: integer
              results:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                      format: uuid
                    'type':
                      type: string
                    totalDevices:
                      type: integer
                    totalInProgress:
                      type: integer
                    totalCancelled:
                      type: integer
                    totalFailed:
                      type: integer
                    totalCompleted:
                      type: integer
                    scheduledAt:
                      type: string
                    company:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                        name:
                          type: string
                    createdBy:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                        name:
                          type: string
        '400':
          description: Bad Request.
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission or Unauthorized User
        '500':
          $ref: '#/responses/internalServerError'
  '/devices/bulkoperations/{id}/history':
    get:
      tags:
        - devices
      summary: Get history of device bulk operations
      description: Get history of device bulk operations
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/bulkOperationIdParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              type: object
              properties:
                deviceId:
                  type: integer
                deviceName:
                  type: string
                serialNumber:
                  type: string
                jobId:
                  type: string
                  format: uuid
                status:
                  type: integer
                site:
                  type: object
                  properties:
                    id:
                      type: string
                      format: uuid
                    name:
                      type: string
                history:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                      status:
                        type: integer
                      remark:
                        type: string
                      timestamp:
                        type: string
                        format: date-time
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission or Unauthorized User
        '500':
          $ref: '#/responses/internalServerError'
  /devicetypes:
    get:
      tags:
        - devices
      summary: Get Device Types
      description: 'Get the Device Types that ICS supports for the current customer. e.g. G6-200, G7-100'
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              type: object
              properties:
                id:
                  type: integer
                name:
                  type: string
                screenWidth:
                  type: integer
                screenHeight:
                  type: integer
                featureflags:
                  type: array
                  items:
                    type: string
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /device-available-memory:
    get:
      tags:
        - devices
      summary: Get Device Available Memory
      description: 'Get the Device Available Memory pero device type or all'
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: deviceType
          in: query
          description: The device type (e.g. G6-100, G6-300, G6-400, G7-100, etc.)
          required: false
          type: string
      responses:
        '200':
          description: Successful response (can be an object if device type was specified or an array if no device type specified)
          schema:
            type: array
            items:
              type: object
              properties:
                deviceType:
                  type: string
                mbytes:
                  type: integer
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Device available memory with device type was not found
        '500':
          $ref: '#/responses/internalServerError'
  /devices/{id}/challenge-response:
    post:
      tags:
        - devices
      summary: Produce the challenge response for a device
      description: 'Get the response codde for either TAMPER_CLEAR or FACTORY_RESET operations'
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/deviceIdParam'
        - name: body
          in: body
          required: true
          schema:
            $ref: '#/definitions/ChallengeResponse'
      responses:
        '200':
          description: Successful response (can be an object if device type was specified or an array if no device type specified)
          schema:
            type: object
            properties:
              device:
                type: object
                properties:
                  id:
                    type: integer
                    example: 123
                  name:
                    type: string
                    example: Pump 1
                  serialNumber:
                    type: string
                    example: A0217123456
              createdBy:
                type: object
                properties:
                  id:
                    type: integer
                    example: a9019fa9-8521-471f-9eb3-49bc2bd2c16e
                  fullName:
                    type: string
                    example: Donald Duck
                  email:
                    type: string
                    example: <EMAIL>
              targetSerialNumber:
                type: string
              requestedBy:
                type: string
              operation:
                type: string
              challenge:
                type: string
              response:
                type: string
        '400':
          description: Unmet Requirements or Invalid Parameters
        '403':
          description: MFA code validation failed
        '404':
          description: Device not found or Invalid serial number
        '409':
          description: Device does not support tamper clear / factory reset challenge code
  '/media/softkeys/:deviceType':
    get:
      tags:
        - prompt-sets
      summary: Get Softkeys for Device Type
      description: 'Get Softkeys for Device Type (e.g. G6-200, G7-100)'
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              type: object
              properties:
                id:
                  type: integer
                name:
                  type: string
                deviceType:
                  type: string
                side:
                  type: string
                offset:
                  type: integer
                physicalCode:
                  type: string
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /media/keycodes:
    get:
      tags:
        - prompt-sets
      summary: Get all keycodes
      description: Get all keycodes
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              type: object
              properties:
                code:
                  type: integer
                name:
                  type: string
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /software:
    post:
      tags:
        - software
      summary: Upload and register a software resource
      description: Upload a binary software artifact to TMS so it can be scheduled to be delivered to devices. Max file size is 100 MB. Admin role required
      produces:
        - application/json
      consumes:
        - multipart/form-data
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: file
          in: formData
          description: Software update to upload
          required: true
          type: file
        - name: name
          in: query
          description: The unique name of the software update and maximum 100 characters
          required: true
          type: string
        - name: deviceType
          in: query
          description: The device type for the the software update
          required: true
          type: string
        - name: promptSetProfileName
          in: query
          description: The promptSet Profile Name for the the software update
          required: true
          type: string
        - name: description
          in: query
          description: Description of the software update files. Must be encoded and maximum 1000 characters
          required: false
          type: string
        - name: type
          in: query
          description: "The type of software: 'software' or 'media'"
          required: false
          type: string
      responses:
        '200':
          description: Upload success
          schema:
            $ref: '#/definitions/SoftwareFileResponse'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '413':
          description: File is larger than the maximum allowed limit
        '500':
          $ref: '#/responses/internalServerError'
    get:
      tags:
        - software
      summary: Get all the available software resources
      description: Retrieve all the active software artifacts available in the Terminal Management System
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: name
          in: query
          description: Filter the search (Anywhere match and case-insensitive) based on the name of the software
          required: false
          type: string
        - name: deviceType
          in: query
          description: 'Filter the search (Exact match and case-sensitive) based on the device type. For example, deviceType=G7-100'
          required: false
          type: string
        - name: promptSetProfileName
          in: query
          description: 'Filter the search (Exact match and case-sensitive) based on the promptSet Profile Name. For example, promptSetProfileName=G7-100'
          required: false
          type: string
        - name: type
          in: query
          description: "Filter the search based on the type of the rollout: 'software' or 'media'"
          required: false
          type: string
        - name: order
          in: query
          description: 'Sort by allowed values: name. If not specified sort by date-time created.'
          required: false
          type: string
      responses:
        '200':
          description: All software updates retrieved successfully
          schema:
            $ref: '#/definitions/SoftwareResult'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  '/software/{id}':
    put:
      tags:
        - software
      summary: Update a software resource
      description: Update a software file meta data in the terminal management system. Admin role required
      produces:
        - application/json
      consumes:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          in: path
          description: Identifier for the file
          required: true
          type: string
        - name: body
          in: body
          description: Information about the file meta data being updated
          required: true
          schema:
            $ref: '#/definitions/SoftwareFileUpdate'
      responses:
        '200':
          description: File resource meta data successfully updated
          schema:
            $ref: '#/definitions/SoftwareFileResponse'
        '400':
          description: Bad Request
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Software with specified id was not found
        '500':
          $ref: '#/responses/internalServerError'
    delete:
      tags:
        - software
      summary: Delete software resource
      description: Delete a software artifact. Admin role only
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          in: path
          description: Identifier for the file from GET files
          required: true
          type: string
      responses:
        '204':
          description: File successfully deleted
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Not found. Wrong software id
        '409':
          description: Software cannot be currently deleted as it is in use in an active rollout.
        '500':
          $ref: '#/responses/internalServerError'
  /software/internal:
    post:
      tags:
        - software
      summary: Register a software resource (upload need to be done prior to calling this API)
      description: Register a binary software artifact to TMS so it can be scheduled to be delivered to devices. Max file size is 100 MB. ICS_SYSTEM role required
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: 'Simple rollout payload object that will be added. Name, softwareId, startTime and installWindow are mandatory fields. All devices included in the site Ids will have the software deployed. Target Ids specified will have the software deployed.'
          required: true
          schema:
            $ref: '#/definitions/SoftwareCreatePayload'
      responses:
        '200':
          description: Software registered successfully
          schema:
            $ref: '#/definitions/SoftwareCreateResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /software/get-files:
    post:
      tags:
        - software
      summary: Get all the available software resources
      description: Retrieve all the active software artifacts available in the Terminal Management System
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: body
          in: body
          description: Filter the search (Exact match and case-sensitive) based on the device type. For example, deviceType=G7-100
          schema:
            type: object
            required:
              - deviceType
              - filters
            properties:
              deviceType:
                type: string
                description: The device type for the the software update
              type:
                type: string
                description: "Filter the search based on the type of the rollout: 'software' or 'media'"
              order:
                type: string
                description: 'Sort by allowed values: name. If not specified sort by date-time created.'
              filters:
                type: object
                description: Object containing various filter parameters
                properties:
                  createdBy:
                    type: array
                    description: An array of User IDs that have uploaded the files
                  createdBefore:
                    type: integer
                    description: Number of days before which the files are uploaded
                  fileExtensions:
                    type: array
                    description: An array of file extensions of which kind of files to be selected
                  fileName:
                    type: string
                    description: The name or part of the name for the software
                  isDuplicate:
                    type: boolean
                    description: A flag of whether to include the softwares with duplicate file names
      responses:
        '200':
          description: All software updates retrieved successfully
          schema:
            $ref: '#/definitions/SoftwareResult'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /software/delete-files:
    post:
      tags:
        - software
      summary: Delete multiple software resources
      description: Delete multiple software artifacts. Admin role only
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: Filter the search (Exact match and case-sensitive) based on the device type. For example, deviceType=G7-100
          schema:
            type: object
            required:
              - deviceType
              - filters
            properties:
              deviceType:
                type: string
                description: The device Type for which software resources are to be deleted
              filters:
                type: object
                description: Object containing various filter parameters
                properties:
                  createdBy:
                    type: array
                    description: An array of User IDs that have uploaded the files
                  createdBefore:
                    type: integer
                    description: Number of days before which the files are uploaded
                  fileExtensions:
                    type: array
                    description: An array of file extensions of which kind of files to be selected for deletion
                  fileName:
                    type: string
                    description: The name or part of the name for the software
                  isDuplicate:
                    type: boolean
                    description: A flag of whether to include the softwares with duplicate file names
              pageSize:
                type: integer
                description: Number of files to be deleted. -1 if all the files should be deleted
              include:
                type: array
                description: Array of software_id that are to be included while deletion
              exclude:
                type: array
                description: Array of software_id that are to be excluded from deletion
              order:
                type: string
                description: Property based on which the files have to be sorted
      responses:
        '200':
          description: Files deleted successfully. Returns a json containing deleteRequestId which is a unique associated to all the files that were deleted.
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Not found. Wrong software id
        '409':
          description: Software cannot be currently deleted as it is in use in an active rollout.
        '500':
          $ref: '#/responses/internalServerError'
  /software/delete-files/undo:
    post:
      tags:
        - software
      summary: Undo deletion of files
      description: Restore the software artifacts that were deleted earlier. Admin role only
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          schema:
            type: object
            required:
              - deviceType
              - deleteRequestId
            properties:
              deviceType:
                type: string
                description: The device Type for which software resources are to be restored
              deleteRequestId:
                type: array
                description: A unique guid that is created while deletion of files.
      responses:
        '204':
          description: Files restored successfully
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Not found. Wrong software id
        '409':
          description: Software cannot be restored as the restoration time window has expired.
        '500':
          $ref: '#/responses/internalServerError'
  /software/delete-inactive-files:
    post:
      tags:
        - software
      summary: 'Scheduled Operation: Deletes all the inactive files from s3 and DB.'
      description: 'Finds all the files that were set to inactive from the file library in past 24 hours and deletes the files from s3 as well as ICS DB'
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Files deleted successfully
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Not found. No files to delete
        '500':
          $ref: '#/responses/internalServerError'
  /tags:
    get:
      tags:
        - tags
      summary: Get a summary of all tags
      description: |-
        Return a list of tags available for the customer, including the number of sites which have each tag.

        Site count includes only sites the caller can see.
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: All tags retrieved successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/Tag'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  /jobs:
    get:
      summary: Get all available jobs for the current customer
      description: Get all available jobs. Response is filered to jobs that exist within the current customer and matching query filters.
      tags:
        - jobs
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: start
          in: query
          description: Only return events newer than the specified date (ISO format)
          required: false
          type: string
        - name: end
          in: query
          description: Only return events older than the specified date (ISO format)
          required: false
          type: string
        - name: status
          in: query
          description: Only return events with a matching status; Can provide comma separeted list of values as input
          required: false
          type: integer
        - name: deviceId
          in: query
          description: Only return events for specified deviceId
          required: false
          type: integer
        - name: embargoStart
          in: query
          description: Only return events newer than the specified date based on embargo date (ISO format)
          required: false
          type: string
        - name: embargoEnd
          in: query
          description: Only return events older than the specified date based on embargo date (ISO format)
          required: false
          type: string
        - name: destination
          in: query
          description: Only return events for that destination
          required: false
          type: string
        - name: siteId
          in: query
          description: Only return events for that siteId
          required: false
          type: string
        - name: types
          in: query
          description: Only return events for that type, Can provide comma separated list of values as input
          required: false
          type: string
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/JobResponse'
        '401':
          description: Not authorized
    post:
      summary: Create a new job record.
      description: |
        Create a new job record.

        If the job object received contains an `id`, this must be ignored
      tags:
        - jobs
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: job
          description: The job object to create
          in: body
          required: true
          schema:
            $ref: '#/definitions/UpdateJob'
      responses:
        '200':
          description: |
            Successful response

            Returns the created job object with the newly assigned `id` populated
          schema:
            $ref: '#/definitions/JobResponse'
        '401':
          description: Not authorized
        '404':
          description: 'Specified device, or specified dependency does not exist'
  '/jobs/{id}':
    get:
      summary: Get a single job record.
      description: Get a single job record
      tags:
        - jobs
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: Identifier of the job to retrieve
          in: path
          type: string
          required: true
      responses:
        '200':
          description: Successful response
          schema:
            $ref: '#/definitions/JobResponse'
        '401':
          description: Not authorized
        '404':
          description: Specified job does not exist
    patch:
      summary: Update the status of a job.
      description: 'Update the status of a job.  This will update the job record, and also create a record in the job status history table'
      tags:
        - jobs
      parameters:
        - $ref: '#/parameters/deviceAuthTokenParam'
        - name: id
          description: Identifier of the job to retrieve
          in: path
          type: string
          required: true
        - name: statusUpdate
          description: The new status to apply to the job
          in: body
          required: true
          schema:
            type: object
            properties:
              status:
                $ref: '#/definitions/JobStatus'
              message:
                type: string
                description: Message associated with this status update
              data:
                type: object
                description: Additional details about the job
            required:
              - status
      responses:
        '204':
          description: Successful response
        '400':
          description: Bad Request might be due to invalid job uuid or json payload
          schema:
            $ref: '#/definitions/ErrorMessage'
        '401':
          description: Not authorized
        '403':
          description: No permission to access the job because it belongs to another device. This might be due to the device was deleted and registered again with the same serial number.
        '404':
          description: Specified job does not exist
        '409':
          description: Job has been cancelled or expired and cannot be updated
  '/jobs/{id}/cancel':
    post:
      summary: Cancel the specified job.
      description: 'Cancel the specified job.  The job cannot be in progress or already in a terminal state (completed, failed or cancelled).'
      tags:
        - jobs
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: Identifier of the job to cancel
          in: path
          type: string
          required: true
      responses:
        '204':
          description: Successful response
        '401':
          description: Not authorized
        '404':
          description: Specified job does not exist
        '409':
          description: Job cannot be cancelled
  '/jobs/{id}/bulkcancel':
    post:
      summary: Cancels job created by a bulk operation.
      description: 'Cancels the specified jobs created by a bulk operation. The job cannot be in progress or already in a terminal state (completed, failed or cancelled).'
      tags:
        - jobs
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: Bulk operation id of the jobs to cancel
          in: path
          type: string
          required: true
      responses:
        '204':
          description: Successful response. Returns list of job id affected by bulk cancel.
          schema:
            type: array
            items:
              type: string
        '401':
          description: Not authorized
        '404':
          description: Specified bulk operation jobs does not exist
        '403':
          description: User company does not have Remote Mgmt License
  '/jobs/{id}/history':
    get:
      summary: Get the status history of a job
      description: Get the status history of a job
      tags:
        - jobs
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: Identifier of the job to retrieve status history for
          in: path
          type: string
          required: true
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              type: object
              properties:
                jobId:
                  type: string
                changed:
                  type: string
                  format: dateTime
                  description: When the status change occurred
                status:
                  $ref: '#/definitions/JobStatus'
                message:
                  type: string
                  description: Message which was passed with the status update
              required:
                - jobId
                - changed
                - status
        '401':
          description: Not authorized
        '404':
          description: Specified job does not exist
  '/jobs/devices/{id}':
    get:
      tags:
        - jobs
      summary: Get jobs for the specified device.
      description: |
        Get jobs for the specified device. Only returns jobs which are ready to be started, so jobs must
        * Be in 'New' status
        * Have an embargo time which is in the past (or null)
        * Have an expiry time which is in the future (or null)
        * Have all dependency jobs completed
          * if a dependency job continueOnFail=true, the dependency job is complete when its status is either complete, falied or cancelled
          * if a dependency job continueOnFail=false, the dependency job is complete wehn its status is complete
      parameters:
        - $ref: '#/parameters/deviceAuthTokenParam'
        - name: id
          description: Identifier of the device to retrieve jobs for
          in: path
          type: string
          required: true
      responses:
        '200':
          description: Successful response
          schema:
            type: array
            items:
              $ref: '#/definitions/DeviceJobResponse'
        '401':
          description: Not authorized
  /jobs/expire:
    post:
      summary: 'Schedueled Operation: Update all expired jobs to failed.'
      description: |
        Finds all incomplete jobs which have passed their expiry time and updates their status to failed.

        This service also creates an entry in the job status history for the expiry status change.
      tags:
        - jobs
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Successful response
          schema:
            type: integer
            description: Number of jobs which were expired by this service invocation
  /jobs/config-management:
    parameters:
      - '$ref': '#/parameters/AuthorizationTokenParam'
    post:
      summary: 'Creates new Config Management Job(s)'
      description: |
        For each deviceId supplied, a CM Job will be created.

        Reboot jobs will optionally be created depending on appropriate values in the Config Descriptor parameter.
      tags:
        - jobs
      consumes:
        - multipart/form-data
      parameters:
        - name: fileContent
          in: formData
          type: file
          required: true
        - name: deviceIds
          in: formData
          description: Stringified-JSON of an array containing DeviceIds
          required: true
          type: string
        - name: configDescriptor
          in: formData
          description: Stringified-JSON of a config descriptor
          required: true
          type: string
      responses:
        '200':
          description: Notifications retrieved successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/JobDeviceRelation'
  /jobs/statuses:
    parameters:
      - '$ref': '#/parameters/AuthorizationTokenParam'
    post:
      summary: 'Returns statuses for supplied Job Ids'
      description: |
        For each jobId supplied in the request body, the job status will be returned.
        A status will only be returned if it is not 'New', if it belongs to the current Company,
        and if the type if a CM job type.
      tags:
        - jobs
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: body
          in: body
          description: >-
            Array of strings which are Job Id Guids
          required: true
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: Job statuses returned successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/JobStatusResponse'
  /jobs/bulkcancel:
    parameters:
      - '$ref': '#/parameters/AuthorizationTokenParam'
    post:
      summary: 'Cancels CM jobs for supplied Job IDs'
      description: |
        For each jobId supplied in the request body, the job will be cancelled.
        A job will only be cancelled if it is not in a final state, if it belongs to the current Company,
        and if the type if a CM job type.
      tags:
        - jobs
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          description: >-
            Array of strings which are Job Id Guids
          required: true
          schema:
            type: array
            items:
              type: string
      responses:
        '204':
          description: Jobs cancelled successfully
        '404':
          description: No jobs were eligible to be cancelled
  /notifications/ui/create2:
    post:
      tags:
        - notifications
        - internal
      summary: Create a new UI notification
      description: |
        Create a new UI notification that will be consumed by the web UI. Requires ICS_SYSTEM
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: notificationDetail
          in: body
          description: Notification details
          required: true
          schema:
            $ref: '#/definitions/UINotification2Create'
      responses:
        '204':
          description: UI notification created successfully
        '409':
          description: Recipients should belong to the same company
        '500':
          $ref: '#/responses/internalServerError'
  /notifications/ui/create:
    post:
      tags:
        - notifications
        - internal
      summary: Create a new UI notification
      description: |
        DEPRECATED July 2017. Create a new UI notification that will be consumed by the web UI. Must be ICS_SYSTEM role
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: alarmDetail
          in: body
          description: The details of the alarm that will be used to build the UI notification
          required: true
          schema:
            $ref: '#/definitions/UINotificationCreate'
      responses:
        '200':
          description: UI notification created successfully
        '404':
          description: Rule device or site not recognised
        '500':
          $ref: '#/responses/internalServerError'
  /notifications/ui:
    get:
      tags:
        - notifications
      summary: Get a list of notifications for the current user
      description: Get a list of notifications for the current user
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: start
          in: query
          description: Get notifications that were created after this date
          required: false
          type: integer
        - name: end
          in: query
          description: Get notifications that were created before this date
          required: false
          type: integer
      responses:
        '200':
          description: Notifications retrieved successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/UINotification'
        '500':
          $ref: '#/responses/internalServerError'
    delete:
      tags:
        - notifications
        - internal
      summary: Delete notifications
      description: Internal API for purging UI Notifications older than the specified date
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: end
          in: query
          description: Delete notifications that were created before this date
          required: true
          type: integer
      responses:
        '204':
          description: Notifications deleted successfully
        '500':
          $ref: '#/responses/internalServerError'
  '/notifications/ui/{id}':
    put:
      tags:
        - notifications
      summary: Update a UI notification
      description: Update a notification (belonging to the current user). Currently this can only be used to mark the notification as read
      parameters:
        - name: id
          in: path
          description: The id of the notification that is being updated
          required: true
          type: string
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: A simple json that represents if the notification has been read
          required: true
          schema:
            type: object
            properties:
              read:
                type: boolean
      responses:
        '200':
          description: Notification successfully been updated
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: 'Notification not found'
        '500':
          $ref: '#/responses/internalServerError'
  /notifications/ui/markallread:
    post:
      tags:
        - notifications
      summary: Mark All Notifications Read
      description: |
        Marks all notifications for the current user as read. This is faster than marking notifications as read one by one.
        Note that there is no payload required on this POST request. Requires role: USER or better
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '204':
          description: Notifications successfully marked as read
        '401':
          $ref: '#/responses/authenticationFailed'
        '500':
          $ref: '#/responses/internalServerError'
  /notifications/ui/unreadcount:
    get:
      tags:
        - notifications
      summary: Get the number of unread notifications
      description: Get the number of unread notifications for the current user
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: start
          in: query
          description: Get notifications that were created after this date
          required: false
          type: integer
        - name: end
          in: query
          description: Get notifications that were created before this date
          required: false
          type: integer
      responses:
        '200':
          description: Number of unread notifications
          schema:
            type: integer
        '500':
          $ref: '#/responses/internalServerError'
  /offlinepackage/software:
    post:
      summary: Create an offline software or RKI package.
      description: Create an offline software or RKI package. The package will be extracted into a flash disk and executed on the device.
      tags:
        - offlinepackages
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: name
          in: body
          description: The offline software package request
          required: true
          schema:
            $ref: '#/definitions/OfflinePackageSoftwareRequest'
      responses:
        '201':
          description: Successful response
          schema:
            type: object
            items:
              $ref: '#/definitions/OfflinePackageResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: 'Notification not found'
        '500':
          $ref: '#/responses/internalServerError'
  /offlinepackage/rki:
    post:
      summary: Create an offline software or RKI package.
      description: Create an offline software or RKI package. The package will be extracted into a flash disk and executed on the device.
      tags:
        - offlinepackages
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: name
          in: body
          description: The offline RKI package request
          required: true
          schema:
            $ref: '#/definitions/OfflinePackageRkiRequest'
      responses:
        '201':
          description: Successful response
          schema:
            $ref: '#/definitions/OfflinePackageResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: 'Notification not found'
        '500':
          $ref: '#/responses/internalServerError'
  /offlinepackage:
    get:
      summary: Get a list of offline packages.
      description: Get a list of offline packages
      tags:
        - offlinepackages
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: name
          in: query
          description: Search offline packages by name
          type: string
          required: false
        - name: type
          in: query
          description: Search offline packages by type (software, RKI)
          type: string
          required: false
        - name: status
          in: query
          description: Search offline packages by status (DONE, IN_PROGRESS, FAIL)
          type: string
          required: false
      responses:
        '200':
          description: Successful response
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: 'Notification not found'
        '500':
          $ref: '#/responses/internalServerError'
  '/offlinepackage/{id}':
    get:
      summary: ''
      description: ''
      tags:
        - offlinepackages
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: The ID of the offline package to look up
          in: path
          type: string
          format: uuid
          required: true
      responses:
        '200':
          description: Successful response
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: 'Notification not found'
        '500':
          $ref: '#/responses/internalServerError'
    put:
      summary: Rename offline package
      description: Rename an offline package
      tags:
        - offlinepackages
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: The ID of the offline package to look up
          in: path
          type: string
          required: true
        - name: name
          in: body
          description: The offline RKI package request
          required: true
          schema:
            $ref: '#/definitions/OfflinePackagePutRequest'
      responses:
        '200':
          description: Successful response
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: 'Notification not found'
        '500':
          $ref: '#/responses/internalServerError'
    delete:
      summary: Delete an offline package
      description: Delete an offline package
      tags:
        - offlinepackages
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: id
          description: The ID of the offline package to look up
          in: path
          type: string
          format: uuid
          required: true
      responses:
        '204':
          description: Successful response
        '401':
          $ref: '#/responses/authenticationFailed'
        '404':
          description: 'Notification not found'
        '500':
          $ref: '#/responses/internalServerError'
  /rollouts:
    get:
      tags:
        - rollouts
      summary: Get all the software rollouts
      description: Retrieve all the scheduled rollouts in the Terminal Management System
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - $ref: '#/parameters/pageIndexParam'
        - $ref: '#/parameters/pageSizeParam'
        - name: name
          in: query
          description: Filter the search (Anywhere match and case-insensitive) based on the name of the rollout
          required: false
          type: string
        - name: type
          in: query
          description: Filter the search based on the type of the rollout
          required: false
          type: string
      responses:
        '200':
          description: All rollouts retrieved successfully
          schema:
            $ref: '#/definitions/RolloutsResult'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
    post:
      tags:
        - rollouts
      summary: Create a new scheduled rollout
      description: >-
        Create a new rollout in the terminal management system. Admin role
        required
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'
        - name: body
          in: body
          description: >-
            Simple rollout payload object that will be added. Name, softwareId,
            startTime and installWindow are mandatory fields. All devices
            included in the site Ids will have the software deployed. Target Ids
            specified will have the software deployed.
          required: true
          schema:
            $ref: '#/definitions/RolloutCreate'
      responses:
        '200':
          description: New rollout successfully created
          schema:
            $ref: '#/definitions/RolloutResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  '/rollouts/{rolloutId}':
    delete:
      tags:
        - rollouts
      summary: Delete a rollout resource
      description: >-
        Delete the rollout once it is completed or cancelled. Admin role
        required
      parameters:
        - name: rolloutId
          in: path
          description: The id of the rollout
          required: true
          type: integer
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '204':
          description: A rollout that represents by the rollout Id deleted successfully
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '404':
          description: Not found. Wrong rollout id
        '460':
          description: The rollout hasn't completed or cancelled
        '500':
          $ref: '#/responses/internalServerError'
    get:
      tags:
        - rollouts
      summary: Get rollout resource by rollout id
      description: Get the rollout by id
      parameters:
        - name: rolloutId
          in: path
          description: The id of the rollout
          required: true
          type: integer
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: Retrieve a rollout that represents by the rollout Id successfully
          schema:
            $ref: '#/definitions/RolloutResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission. Check role
        '404':
          description: >-
            Not found. Wrong rollout id, cannot access site or rollout belongs
            to another company
        '500':
          $ref: '#/responses/internalServerError'
  '/rollouts/{rolloutId}/deployments':
    get:
      tags:
        - rollouts
      summary: Get deployments for rollout resource
      description: >-
        Get all device deployments in a rollout. A scheduled rollout can deploy
        to multiple devices or a single device
      produces:
        - application/json
      parameters:
        - name: rolloutId
          in: path
          description: The id of the rollout
          required: true
          type: integer
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: All deployments retrieved successfully
          schema:
            type: array
            items:
              $ref: '#/definitions/DeploymentResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
  '/rollouts/{rolloutId}/cancel':
    post:
      tags:
        - rollouts
      summary: Cancel the rollout resource.
      description: >-
        Cancel the rollout by the rollout id. Device deployment will be
        cancelled except the devices that have its' deployment
        completed/failed/in progress. Admin role required. Canceler and the
        rollout creator must belong to the same company
      produces:
        - application/json
      parameters:
        - name: rolloutId
          in: path
          description: The id of the rollout
          required: true
          type: integer
        - $ref: '#/parameters/AuthorizationTokenParam'
      responses:
        '200':
          description: >-
            A rollout that represents by the rollout Id cancelled successfully.
            Cancelled should be true
          schema:
            $ref: '#/definitions/RolloutResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '460':
          description: Cannot cancel rollout because all deployments have completed
        '500':
          $ref: '#/responses/internalServerError'
  /rollouts/createdownload/confirm:
    post:
      tags:
        - rollouts Confirmation
      summary: Get the rollouts confirmation
      description: Retrieve the information if Rollout required MFA or not
      produces:
        - application/json
      parameters:
        - $ref: '#/parameters/AuthorizationTokenParam'

        - name: body
          in: body
          schema:
            type: object
            required:
              - siteCount
              - targetCount
            properties:
              siteCount:
                type: integer
                description: Number of sites to which tenants level Filedownload limit has to be checked
              targetCount:
                type: integer
                description: Number of Devices to which tenants level Filedownload limit has to be checked
      responses:
        '200':
          description: Sitelimit and Targetlimit retrieved successfully
          schema:
            $ref: '#/definitions/CreatedownloadConfirmResponse'
        '401':
          $ref: '#/responses/authenticationFailed'
        '403':
          description: No permission
        '500':
          $ref: '#/responses/internalServerError'
# #############################################################################
# DEFINITIONS
# #############################################################################
definitions:
  TargetBridgeDto:
    type: object
    required:
      - id
      - productType
      - siteId
      - name
      - serialNumber
    properties:
      id:
        type: integer
      productType:
        type: string
        description: Device type
      serialNumber:
        type: string
        description: Serial Number of the target device
      name:
        type: string
        description: Name of the target device
      description:
        type: string
        description: Description of the target device
      siteId:
        type: string
        description: Site id the target device associated to
      siteName:
        type: string
        description: Site name the target device associated to
      lastContact:
        type: integer
        description: Number of milliseconds since 1970
      lastRegistered:
        type: integer
        description: Number of milliseconds since 1970
      status:
        type: integer
        description: Status of the target (0=No_Data_Available, 1=OK, 2=Warning, 3=Offline)
        enum:
          - 0
          - 1
          - 2
          - 3
      keyGroupRef:
        type: string
        description: key group reference
  DeviceUpdateDto:
    type: object
    required:
      - name
      - certificate
    properties:
      productTypeId:
        type: integer
        description:
          The type target device acquired from old product type resource. Either
          deviceType or ProductTypeId one must be presented (REQUIRED required if want to create new Device)
      siteId:
        type: string
        description: The id of the site that the target device will be added to (REQUIRED if want to create new Device)
      name:
        type: string
        description: The name of the target device. Max length is 128 characters only
      description:
        type: string
        description:
          The description of the target device. Max length is 1000 characters
          only
      certificate:
        type: string
        description:
          'PKCS #7 or X.509 Device Encryption Certificate in either hexadecimal
          or base64 format.'
      isJsonCertificates:
        type: boolean
        description: if true then certificate is a stringified array of JSON Object
      macAddress:
        type: string
        description: The device MAC address
      data:
        type: string
        description: Device metadata
  SoftwareTargetDto:
    type: object
    required:
      - id
      - deviceType
      - name
      - serialNumber
    properties:
      id:
        type: integer
        description: Id of the target device
      deviceType:
        $ref: '#/definitions/DeviceTypeSimpleDto'
      serialNumber:
        type: string
        description: Serial Number of the target device
      name:
        type: string
        description: Name of the target device
      lastContact:
        type: integer
        description: Number of milliseconds since 1970
      lastRegistered:
        type: integer
        description: Number of milliseconds since 1970
  AddDeviceByBridge:
    type: object
    required:
      - serialNumber
      - productTypeId
      - deviceType
      - certificate
      - siteId
      - name
    properties:
      productTypeId:
        type: integer
        description:
          The type target device acquired from old product type resource. Either
          deviceType or ProductTypeId one must be presented
      serialNumber:
        type: string
        description:
          The serial number of the target device. Max length 128 alphanumeric
          and dash only.
      siteId:
        type: string
        description: The id of the site that the target device will be added to
      name:
        type: string
        description: The name of the target device. Max length is 128 characters only
      description:
        type: string
        description:
          The description of the target device. Max length is 1000 characters
          only
      certificate:
        type: string
        description:
          'PKCS #7 or X.509 Device Encryption Certificate in either hexadecimal
          or base64 format.'
      isJsonCertificates:
        type: boolean
        description: if true then certificate is a stringified array of JSON Object
      macAddress:
        type: string
        description: The device MAC address
      data:
        type: string
        description: Device metadata
  TargetSimple:
    type: object
    required:
      - id
    properties:
      id:
        type: integer
        description: The id of the target
  SiteSimple:
    type: object
    required:
      - id
    properties:
      id:
        type: string
        description: The id of the site
  RolloutCreate:
    type: object
    required:
      - name
      - softwareId
      - startTime
      - installWindow
    properties:
      name:
        type: string
        description: The name of the rollout
      description:
        type: string
        description: The description of the rollout
      softwareId:
        type: integer
      retry:
        type: boolean
        description: Retry the rollout if last attempt failed
      startTime:
        type: integer
        description: The rollout start time (Number of milliseconds since 1970)
      installWindow:
        type: integer
        description: The install window in milli seconds
      targets:
        type: array
        items:
          $ref: '#/definitions/TargetSimple'
      sites:
        type: array
        items:
          $ref: '#/definitions/SiteSimple'
      deploymentPolicy:
        type: integer
        description: >-
          Default to 0 if not present or out of policy range (one at a time
          within site, device dependency applied), 1 (all at once within site,
          no device dependency)
      type:
        type: string
        description: "Type of rollout: 'software' or 'media'"
      mfaCode:
        type: string
        description: Valid MFA code for 'software' type rollout if selected
          sites are more than 100
  RolloutsResult:
    type: object
    required:
      - resultsMetadata
    properties:
      resultsMetadata:
        $ref: '#/definitions/ResultsMetadata'
      results:
        type: array
        items:
          $ref: '#/definitions/RolloutResponse'
  RolloutResponse:
    type: object
    required:
      - id
      - startTime
      - endTime
      - name
      - cancelled
      - deleted
      - software
      - createdBy
      - deploymentPolicy
      - totalDeploymentsCount
      - deploymentsCompleteCount
      - deploymentsFailCount
      - deploymentsInProgressCount
      - deploymentsCancelCount
      - type
    properties:
      id:
        type: integer
        description: The id of the rollout
      name:
        type: string
        description: The name of the rollout
      description:
        type: string
        description: The description of the rollout
      retry:
        type: boolean
        description: Retry the rollout if last attempt failed
      software:
        $ref: '#/definitions/Software'
      startTime:
        type: integer
        description: The rollout start time (Number of milliseconds since 1970)
      endTime:
        type: integer
        description: The rollout end time (Number of milliseconds since 1970)
      installWindow:
        type: integer
        description: The install window in milli seconds
      totalDeploymentsCount:
        type: integer
        description: The total number of deployments for the rollout
      deploymentsCompleteCount:
        type: integer
        description: The number of completed deployments for the rollout
      deploymentsFailCount:
        type: integer
        description: The number of failed deployments for the rollout
      deploymentsInProgressCount:
        type: integer
        description: The number of in-progress deployments for the rollout
      deploymentsCancelCount:
        type: integer
        description: The number of cancelled deployments for the rollout
      cancelled:
        type: boolean
        description: Rollout is cancelled if true
      deleted:
        type: boolean
        description: Rollout is deleted if true
      cancelledBy:
        $ref: '#/definitions/UserSimple'
      createdBy:
        $ref: '#/definitions/UserSimple'
      deploymentPolicy:
        $ref: '#/definitions/DeploymentPolicy'
      type:
        type: string
        description: Type of rollout - cutrently 'software' or 'media'
      media:
        $ref: '#/definitions/MediaSimple'
  CreatedownloadConfirmResponse:
    type: object
    required:
      - message
    properties:
      message:
        type: string
        description: MFA required for Download or Not
  MediaSimple:
    type: object
    required:
      - status
      - version
    properties:
      status:
        type: string
        description: Media prompt set status
      version:
        type: string
        description: Media prompt set version
  UserSimple:
    type: object
    required:
      - id
      - fullName
    properties:
      fullName:
        type: string
        description: User full name
      id:
        type: string
        description: User id
      company:
        $ref: '#/definitions/CompanySimple'
  CompanySimple:
    type: object
    required:
      - id
      - name
    properties:
      name:
        type: string
        description: The name of the company
      id:
        type: string
        description: The company id
  DeploymentResponse:
    type: object
    required:
      - id
      - status
    properties:
      id:
        type: integer
        description: The id of the Deployment
      status:
        type: integer
        description: Status of the Deployment
      target:
        $ref: '#/definitions/SoftwareTargetDto'
      site:
        $ref: '#/definitions/SiteSimpleDto'
  DeploymentPolicy:
    type: object
    required:
      - id
      - name
    properties:
      name:
        type: string
        description: The name of the deployment policy
      id:
        type: integer
        description: The id of the deployment policy
      description:
        type: string
        description: The description of the policy
  Software:
    type: object
    required:
      - id
    properties:
      id:
        type: integer
        description: The id of the software update
      name:
        type: string
        description: The name of the software update
      softwareFile:
        type: string
        description: File name
      relatedEntity:
        type: string
        description: 'Id of a related entity, for media it refers to a promptset.'
  AlarmRulesSettings:
    type: object
    required:
      - siteId
      - suspendedFrom
      - suspendedUntil
      - devices
    properties:
      siteId:
        type: string
        description: id of site
      suspendedFrom:
        type: integer
        description: suspension window start in miliseconds since 1970; has to be lower than suspendedUntil; can be null if suspendedUntil is null
      suspendedUntil:
        type: integer
        description: suspension window end in miliseconds since 1970; has to be higher than suspendedFrom; ; can be null if suspendedFrom is null
      devices:
        type: array
        description: an array of device ids for which AlarmRulesSettings should be modified
        items:
          type: integer
  AlarmRulesSettingsItem:
    type: object
    required:
      - deviceId
      - siteId
      - suspendedFrom
      - suspendedUntil
      - suspended
    properties:
      deviceId:
        type: string
        description: id of the devcice
      siteId:
        type: string
        description: id of site
      suspendedFrom:
        type: integer
        description: suspension window start in miliseconds since 1970; has to be lower than suspendedUntil; can be null if suspendedUntil is null
      suspendedUntil:
        type: integer
        description: suspension window end in miliseconds since 1970; has to be higher than suspendedFrom; ; can be null if suspendedFrom is null
      suspended:
        type: boolean
        description: value indicating if alarms are suspended at the time of the request
  DeviceMediaStats:
    type: array
    items:
      $ref: '#/definitions/MediaStatsItem'
  MediaStatsItem:
    type: object
    required:
      - count
      - lastPlayed
      - totalPlayTime
      - name
    properties:
      count:
        type: integer
        description: number of times media file played in last 24 hours
      totalPlayTime:
        type: integer
        description: number of miliseconds media file played in last 24 hours
      lastPlayed:
        type: string
        format: date-time
        description: timestamp when media file was last played (timeformat used by GSTV/Playlist API e.g. 2017-10-24 16:15:52.443)
      name:
        type: string
        description: name of the media file
  SiteAlarm:
    type: object
    required:
      - siteId
      - code
      - modified
      - status
    properties:
      siteId:
        type: string
        description: id of site
      code:
        type: string
        description: alarm code
      modified:
        type: string
        format: date-time
        description: timestamp when state has beed recorded by site (in miliseconds since 1970)
      status:
        type: boolean
        description: indicates if alarm is on (true) or off (false)
  DeviceAlarm:
    type: object
    required:
      - deviceId
      - siteId
      - code
      - component
      - modified
      - status
    properties:
      deviceId:
        type: integer
        description: id of device
      siteId:
        type: string
        description: id of site
      code:
        type: string
        description: alarm code
      component:
        type: string
        description: component to which alarm belongs to (used for version_downgrade)
      modified:
        type: string
        format: date-time
        description: timestamp of when alarm status changed (in miliseconds since 1970)
      status:
        type: boolean
        description: indicates if alarm is on (true) or off (false)

  DeviceAuthenticationResponse:
    type: object
    required:
      - token
    properties:
      token:
        type: string
        description: The JWT Token
  SiteSimpleDto:
    type: object
    required:
      - id
      - name
    properties:
      id:
        type: string
        description: The id of the site
      name:
        type: string
        description: The name of the site
  Registration:
    type: object
    required:
      - type
      - registrationKey
    properties:
      type:
        type: string
        description: Type of device
      registrationKey:
        type: string
        description: Registration key
  Authentication:
    type: object
    required:
      - deviceId
      - secretKey
    properties:
      deviceId:
        type: integer
        description: id of device
      secretKey:
        type: string
        description: secret key
  AlarmResponse:
    type: object
    required:
      - id
      - active
    properties:
      id:
        type: integer
        description: The id of the alarm
      name:
        type: string
        description: The unique name of the alarm
      active:
        type: boolean
        description: Alarm is active if true
      rules:
        type: array
        description: List of alarm rules id
        items:
          type: integer
      subjects:
        type: array
        description: List of alarm subjects associated with the alarm
        items:
          $ref: '#/definitions/AlarmSubject'
      notifications:
        type: array
        description: List of alarm notification associated with the alarm
        items:
          $ref: '#/definitions/AlarmNotification'
  AlarmCreate:
    type: object
    required:
      - name
      - rules
      - subjects
      - notifications
    properties:
      name:
        type: string
        description: The name of the alarm that is being added. Must be unique
      active:
        type: boolean
        description: Status of the alarm
      rules:
        type: array
        description: List of alarm rules id associated with the alarm. Minimum 1 rule
        items:
          type: integer
      subjects:
        type: array
        description: List of alarm subjects associated with the alarm. Minimum 1 subject
        items:
          $ref: '#/definitions/AlarmSubject'
      notifications:
        type: array
        description: List of alarm notification associated with the alarm. Minimum 1 notification
        items:
          $ref: '#/definitions/AlarmNotification'
  AlarmUpdate:
    type: object
    required:
      - name
      - active
      - rules
      - subjects
      - notifications
    properties:
      name:
        type: string
        description: The name of the alarm that is being added. Must be unique
      active:
        type: boolean
        description: Status of the alarm
      rules:
        type: array
        description: List of alarm rules id associated with the alarm. Minimum 1 rule
        items:
          type: integer
      subjects:
        type: array
        description: List of alarm subjects associated with the alarm. Minimum 1 subject
        items:
          $ref: '#/definitions/AlarmSubject'
      notifications:
        type: array
        description: List of alarm notification associated with the alarm. Minimum 1 notification
        items:
          $ref: '#/definitions/AlarmNotification'
  AlarmSubject:
    type: object
    required:
      - type
      - id
    properties:
      type:
        type: string
        enum:
          - S
          - T
          - D
        description: 'The type of the subject Site (S), Tag (T) or Device (D)'
      id:
        type: string
        description: 'The id of the subject depends on the type. It could be site, device or tag id'
  AlarmNotification:
    type: object
    required:
      - type
    properties:
      type:
        type: string
        enum:
          - S
          - E
          - U
        description: 'The type of the notification sms (S), email (E), UI (U)'
      userId:
        type: string
        description: The user id if the type is 'U'
      address:
        type: string
        description: The email or mobile phone if the type is 'E' or 'S' respectively
  UINotification:
    type: object
    properties:
      id:
        type: string
        format: guid
      type:
        type: string
        description: Type of UI notification. i.e Alarm
      level:
        type: string
        description: 'Notification level. Will typically be one of: WARN, INFO, ERROR'
      created:
        type: string
        format: 'date-time'
      read:
        type: boolean
      message:
        type: string
        description: The human readable message of this notification
      relatedEntity:
        type: object
        properties:
          type:
            type: string
            description: The type of the related entity.  Eg. \'site\' or \'device\'
          id:
            type: string
            description: The id of the related entity
  UINotification2Create:
    type: object
    required:
      - type
      - recipientIds
      - timestamp
      - level
      - message
    properties:
      type:
        type: string
        description: Type of UI notification. i.e. Alarm
      recipientIds:
        type: array
        items:
          type: string
        description: Array of userIds that will recieve this notification
      timestamp:
        type: string
        format: date-time
        description: when this notification was created. ISO dateformat
      level:
        type: string
        description: SUCCESS|INFO|WARNING|ERROR etc
      relatedEntity:
        type: object
        description: Multi property object but requires 'id' which is a reference to any object in the db
      message:
        type: string
        description: The message that will appear in the UI
  UINotificationCreate:
    type: object
    required:
      - type
      - recipient
      - alarmId
      - alarmRuleId
      - siteId
      - timestamp
      - isTrigger
    properties:
      type:
        type: string
        description: Type of UI notification. i.e Alarm
      recipient:
        type: string
        description: The recipient of the notification
      alarmId:
        type: integer
        description: The id of the alarm
      alarmRuleId:
        type: integer
        description: The id of the alarm rule.
      deviceId:
        type: integer
        description: The id of the device.
      siteId:
        type: string
        description: The id of the site.
      timestamp:
        type: string
        format: date-time
        description: Number of milliseconds since 1970
      isTrigger:
        type: boolean
        description: If true this is an alarm that something bad has happened. If false then the notification is a remedy
      fileUploadRequestId:
        type: string
        description: The UUID of the upload file request
      rkiRequestId:
        type: string
        description: The UUID of the RKI request
  UserResponseResultObject:
    type: object
    required:
      - resultsMetadata
    properties:
      resultsMetadata:
        $ref: '#/definitions/ResultsMetadata'
      results:
        type: array
        items:
          $ref: '#/definitions/User'
  UserUpdatePayload:
    type: object
    required:
      - id
      - email
      - company
    properties:
      id:
        type: string
        format: uuid
        description: User id
      email:
        type: string
      emailVerified:
        type: boolean
      fullName:
        type: string
      groups:
        type: array
        items:
          type: string
          format: uuid
      company:
        type: string
        format: uuid
      mfaConfigured:
        type: boolean
      roles:
        type: array
        items:
          type: string
      status:
        type: number
      userGroups:
        type: array
        items:
          type: string
          format: uuid
  User:
    type: object
    required:
      - id
      - email
      - fullName
    properties:
      id:
        type: string
        format: uuid
        description: User id
      email:
        type: string
        format: email
        description: User email address
      emailVerified:
        type: boolean
      mfaConfigured:
        type: boolean
      fullName:
        type: string
        description: User full name
      company:
        description: The company the user belongs to
        type: object
        required:
          - id
        properties:
          id:
            type: string
            format: uuid
          name:
            type: string
            description: The name of the company
      roles:
        type: array
        description: The authorization roles that the user has
        items:
          type: string
          description: Role name
  UserFullName:
    type: object
    required:
      - fullName
    properties:
      fullName:
        type: string
        description: User full name
  ReportType:
    type: object
    properties:
      id:
        type: integer
        description: Report ID
      module:
        type: string
        description: Report Module
      name:
        type: string
        description: Report Name
      baseURL:
        type: string
        description: Report Base URL
      active:
        type: boolean
  ReportStatusResponseResultObject:
    type: object
    required:
      - resultsMetadata
    properties:
      resultsMetadata:
        $ref: '#/definitions/ResultsMetadata'
      results:
        type: array
        items:
          $ref: '#/definitions/ReportStatus'
  ReportStatus:
    type: object
    properties:
      companyId:
        type: string
        description: The company ID
      companyName:
        type: string
        description: The company name
      siteId:
        type: string
        description: The site ID
      siteName:
        type: string
        description: The site name
      siteAddress:
        type: string
        description: The site address
      sitePhone:
        type: string
        description: The phone number of the site
      deviceId:
        type: integer
        description: The device ID
      deviceName:
        type: string
        description: The device name
      deviceType:
        type: string
        description: The type of device
      deviceSerialNumber:
        type: string
        description: The device serial number
      statusType:
        type: string
        description: The status type
      statusTimestamp:
        type: string
        format: date-time
        description: The timestamp this status created
      statusValue:
        type: string
        description: The value of status
  ReportStatusRequest:
    type: object
    required:
      - statuses
      - start
      - end
    properties:
      start:
        type: string
        description: 'The starting date in ISO format E.g. 2017-01-01T12:00:00Z'
      end:
        type: string
        description: 'The end date in ISO format E.g. 2017-01-01T12:00:00Z'
      pageSize:
        type: integer
        description: The page size for pagination. Default to 100
      pageIndex:
        type: integer
        description: The page index for pagination. Default to 0
      sites:
        type: array
        description: List of site id selected for the status report
        items:
          type: string
      statuses:
        type: array
        description: List of statuses for the status report. E.g. invenco.system.g7opt|sdc-tamper-status
        items:
          type: string
      tags:
        type: array
        description: List of tags for the version report. E.g. 'system'
        items:
          type: string
  ReportSitePopRequest:
    type: object
    properties:
      start:
        type: string
        description: 'The starting date in YYYY-MM-DD format (timezone agnostic)'
      end:
        type: string
        description: 'The end date in YYYY-MM-DD format (timezone agnostic)'
      pageKey:
        type: integer
        description: The page size for pagination. Default to 100
      pageMinSize:
        type: integer
        description: The page index for pagination. Default to 0
      siteIds:
        type: array
        description: List of site ids selected for the site-pop report
        items:
          type: string
      mediaNames:
        type: array
        description: List of media file names for the site-pop report
        items:
          type: string
  CurrentSiteDeviceRequest:
    type: object
    required:
      - sites
    properties:
      pageSize:
        type: integer
        description: The page size for pagination. Default to 100
      pageIndex:
        type: integer
        description: The page index for pagination. Default to 0
      sites:
        type: array
        description: List of site id selected for the report
        items:
          type: string
  ApiAuditRequest:
    type: object
    required:
      - start
      - end
    properties:
      start:
        type: string
        description: 'The starting date in ISO format E.g. 2017-01-01T12:00:00Z'
      end:
        type: string
        description: 'The end date in ISO format E.g. 2017-01-01T12:00:00Z'
      pageSize:
        type: integer
        description: The page size for pagination. Default to 100
      pageIndex:
        type: integer
        description: The page index for pagination. Default to 0
      users:
        type: array
        description: List of user id for the report
        items:
          type: string
      statuses:
        type: array
        description: Filter by the generic HTTP status codes that specifies one of five categories of responses
        items:
          type: string
          enum:
            - 2XX
            - 3XX
            - 4XX
            - 5XX
      base_routes:
        type: array
        description: |-
          Filter by the URI base endpoint. Can optionally be used with the request verb (method).
          Example: `/foo/bar` or `POST /foo/bar`
        items:
          type: string
  AlarmHistoryReportRequest:
    type: object
    required:
      - start
      - end
      - alarms
    properties:
      start:
        type: string
        description: 'The starting date in ISO format E.g. 2017-01-01T12:00:00Z'
      end:
        type: string
        description: 'The end date in ISO format E.g. 2017-01-01T12:00:00Z'
      pageSize:
        type: integer
        description: The page size for pagination. Default to 100
      pageIndex:
        type: integer
        description: The page index for pagination. Default to 0
      sites:
        type: array
        description: Array of site ids to filter the report
        items:
          type: string
      alarms:
        type: array
        description: 'Array of alarm codes to filter the report. E.g device_offline, paper low'
        items:
          type: string
  UserHistoryRequest:
    type: object
    properties:
      user:
        type: string
        description: User email or full name to filter the report
      roles:
        items:
          type: array
          description: User roles and permissions to filter the report
      statuses:
        type: array
        items:
          description: User status to filter the report
      pageSize:
        type: integer
        description: The page size for pagination. Default to 100
      pageIndex:
        type: integer
        description: The page index for pagination. Default to 0
  SetupMFADetails:
    type: object
    required:
      - email
      - password
      - mfaSecret
      - mfaCode
    properties:
      email:
        type: string
        description: The user's email address
      password:
        type: string
        description: The user's password
      mfaSecret:
        type: string
        description: The used MFA secret use
      mfaCode:
        type: string
        description: The user's MFA code
  UserAuthDetails:
    type: object
    required:
      - email
      - password
      - mfaCode
    properties:
      email:
        type: string
        description: The user's email address
      password:
        type: string
        description: The user's password
      mfaCode:
        type: string
        description: The user's MFA code
      captchaResponse:
        type: string
        description: Optional, captcha response from Google recaptcha
  AuthDetails:
    type: object
    required:
      - email
      - password
      - mfaCode
    properties:
      email:
        type: string
        description: The user's email address
      password:
        type: string
        description: The user's password
      mfaCode:
        type: string
        description: The user's MFA code
  MFAConfigDetails:
    type: object
    properties:
      issuer:
        type: string
        description: Issues of this MFA secret. I.e. 'invencocloud.com'
      secret:
        type: object
        properties:
          ascii:
            type: string
            description: Secret value in ASCII encoded format
          base32:
            type: string
            description: Secret value in Base32 encoded format
          hex:
            type: string
            description: Secret value in Hex encoded format
      otpURL:
        type: string
        description: 'The OPT URL used to generate the QR code. Contains application identifier (ICS) and user identifier (email). Refer to format details from Google: https://github.com/google/google-authenticator/wiki/Key-Uri-Format'
      qrCodeData:
        type: string
        description: Base64 encoded data representing the generated QR code
  AuthResp:
    type: object
    required:
      - name
      - email
      - token
      - company
      - roles
      - created
    properties:
      name:
        type: string
        description: The user's full name
      email:
        type: string
        description: The user's email
      created:
        type: string
        format: date-time
        description: The timestamp user was created
      company:
        $ref: '#/definitions/CompanyType'
      roles:
        type: array
        items:
          type: string
      token:
        type: integer
        description: The JWT token
  ErrorMessage:
    type: object
    properties:
      code:
        type: integer
        description: The error code for the message
      message:
        type: string
        description: The error message
  UserType:
    type: object
    properties:
      id:
        type: string
        description: The uuid of the user
      username:
        type: string
        description: user name
      firstName:
        type: string
        description: first name
      lastName:
        type: string
        description: last name
  UserGroupListTypeResponse:
    type: array
    description: An array for user groups and the count of it's users
    items:
      $ref: '#/definitions/UserGroupListObjectType'
  UserGroupListObjectType:
    type: object
    required:
      - id
      - name
    properties:
      id:
        type: string
        description: The uuid of the site group
      name:
        type: string
        description: site group name
      count:
        type: integer
        description: site group name
  UserGroupType:
    type: object
    properties:
      id:
        type: string
        description: The uuid of user group
      name:
        type: string
        description: user group name
      users:
        type: array
        description: array of users associated with this user group
        items:
          $ref: '#/definitions/UserType'
      sitegroups:
        type: array
        description: array of site groups associated with this user group
        items:
          $ref: '#/definitions/SiteGroupType'
  SiteGroupType:
    type: object
    properties:
      id:
        type: string
        description: The uuid of the site group
      name:
        type: string
        description: site group name
  SiteGroupListType:
    type: array
    description: An array for site groups and the count of sites in it
    items:
      $ref: '#/definitions/SiteGroupListItemType'
  SiteGroupListItemType:
    type: object
    required:
      - id
      - name
    properties:
      id:
        type: string
        description: the uuid of this sitegroup
      name:
        type: string
        description: site group name
      siteCount:
        type: number
        description: number of sites are in this sitegroup
      owner:
        $ref: '#/definitions/CompanyType'
  SiteGroupRequestType:
    type: object
    description: The site to be created or edited
    properties:
      name:
        type: string
        description: the name of the site. Uniqueness not required.
      sites:
        type: array
        items:
          properties:
            id:
              type: string
              description: the site uuid
      companies:
        type: array
        items:
          properties:
            id:
              type: string
              description: the company uuid
  SiteGroupDetailsType:
    type: object
    description: A more details version of sitegroups. Show sites and companies.
    properties:
      id:
        type: string
        description: the uuid of this sitegroup
      name:
        type: string
        description: site group name (uniqueness is not required)
      sites:
        type: array
        items:
          type: object
          description: a simplified site object
          properties:
            id:
              type: string
              description: The uuid of this site
            name:
              type: string
              description: The site name
            address:
              type: string
              description: The site address in a formatted string
      companies:
        type: array
        description: Companies who have access to this site group
        items:
          $ref: '#/definitions/CompanyType'
  CompanyType:
    type: object
    required:
      - id
      - name
    properties:
      id:
        type: string
        description: The uuid of the Company
      name:
        type: string
        description: Company name
  CompanyLanguages:
    type: array
    items:
      properties:
        default:
          type: boolean
        isoCode:
          type: string
        language:
          type: string
        languageSupportId:
          type: string
      type: object
  PromptSetLanguageSupport:
    type: object
    properties:
      type:
        description: Font Type
        type: string
      size:
        description: Font Size
        type: integer
      default:
        description: Default Language
        type: boolean
      deleted:
        description: Deleted Status
        type: boolean
  PromptSetLanguageSupportPayload:
    type: array
    items:
      type: object
      properties:
        languageSupportId:
          type: string
          description: Cloned prompt set ID
        promptSetLanguageSupport:
          $ref: '#/definitions/PromptSetLanguageSupport'
  PromptSetLanguage:
    type: object
    properties:
      languageSupportId:
        type: string
        description: Cloned prompt set ID
      language:
        type: string
        description: Prompt set name
      isoCode:
        type: string
        description: version value (semver)
      promptSetLanguageSupport:
        $ref: '#/definitions/PromptSetLanguageSupport'
  PromptSetLanguages:
    type: array
    items:
      $ref: '#/definitions/PromptSetLanguage'
  StatsResponseDto:
    type: object
    required:
      - siteCount
      - name
      - deviceCount
    properties:
      name:
        type: string
        description: The name of the company
      deviceCount:
        type: integer
        description: The number of active device
      siteCount:
        type: integer
        description: The number of active site
  ResultsMetadata:
    type: object
    properties:
      totalResults:
        type: integer
        description: The total results available
      pageIndex:
        type: integer
        description: The page index which has been returned
      pageSize:
        type: integer
        description: The page size which was used to generate these results
  ResultsMetadataDDB:
    type: object
    properties:
      pageKey:
        type: object
        description: Last evaluated object key.
      pageMinSize:
        type: integer
        description: The minimum page size which was used to generate these results
  DeviceStateHistoryResponse:
    type: object
    properties:
      resultsMetadata:
        $ref: '#/definitions/ResultsMetadataDDB'
      results:
        type: array
        items:
          type: object
          properties:
            dv:
              type: string
            dn:
              type: string
            ts:
              type: string
              format: date-time
            lv:
              type: string
            fn:
              type: string
            mt:
              type: string
            msg:
              type: string
  ReportSitePopResponse:
    type: object
    properties:
      resultsMetadata:
        $ref: '#/definitions/ResultsMetadataDDB'
      results:
        type: array
        items:
          type: object
          properties:
            count:
              type: integer
            name:
              type: string
            date:
              type: string
              format: date
            siteName:
              type: string
            referenceId:
              type: string
            runtime:
              type: integer
  CurrentSiteDevicesResponse:
    type: object
    required:
      - resultsMetadata
    properties:
      resultsMetadata:
        $ref: '#/definitions/ResultsMetadata'
      results:
        type: array
        items:
          $ref: '#/definitions/CurrentSiteDeviceItem'
  CurrentSiteDeviceItem:
    type: object
    properties:
      companyId:
        type: string
        format: uuid
      companyName:
        type: string
      siteId:
        type: string
        format: uuid
      siteName:
        type: string
      siteAddress:
        type: string
      sitePhone:
        type: string
      deviceId:
        type: integer
      deviceType:
        type: string
      deviceSerialNumber:
        type: string
      deviceCommissionedDate:
        type: string
        format: date-time
      releaseVersion:
        type: string
  AlarmHistoryResponse:
    type: object
    required:
      - resultsMetadata
    properties:
      resultsMetadata:
        $ref: '#/definitions/ResultsMetadata'
      results:
        type: array
        items:
          $ref: '#/definitions/AlarmHistoryItem'
  AlarmHistoryItem:
    type: object
    properties:
      companyId:
        type: string
        format: uuid
      companyName:
        type: string
      siteId:
        type: string
        format: uuid
      siteName:
        type: string
      siteAddress:
        type: string
      sitePhone:
        type: string
      deviceId:
        type: integer
      deviceType:
        type: string
      deviceSerialNumber:
        type: string
      alarmType:
        type: string
      alarmTriggered:
        type: string
        format: date-time
      alarmRemedied:
        type: string
        format: date-time
  UserReportHistoryResponse:
    type: object
    required:
      - resultsMetadata
    properties:
      resultsMetadata:
        $ref: '#/definitions/ResultsMetadata'
      results:
        type: array
        items:
          $ref: '#/definitions/UserReportHistoryItem'
  UserReportHistoryItem:
    type: object
    properties:
      id:
        type: string
      email:
        type: string
      fullName:
        type: string
      status:
        type: string
      roles:
        type: string
      lastLoggedIn:
        type: string
        format: date-time
  ApiAuditResponse:
    type: object
    required:
      - resultsMetadata
    properties:
      resultsMetadata:
        $ref: '#/definitions/ResultsMetadata'
      results:
        type: array
        items:
          $ref: '#/definitions/ApiAuditItem'
  ApiAuditItem:
    type: object
    properties:
      requestId:
        type: string
        format: uuid
      route:
        type: string
      statusCode:
        type: number
      timestamp:
        type: string
        format: date-time
      user:
        type: object
        properties:
          id:
            type: string
            format: uuid
          fullName:
            type: string
          email:
            type: string
      company:
        type: object
        properties:
          id:
            type: string
            format: uuid
          name:
            type: string
      request:
        type: object
      response:
        type: object
  Asset:
    type: object
    properties:
      id:
        type: string
        format: uuid
      name:
        type: string
      type:
        type: string
      thumbnailUrl:
        type: string
        format: url
      sourceUrl:
        type: string
        format: url
      package:
        type: object
        properties:
          id:
            type: string
            format: uuid
          name:
            type: string
          signed:
            type: boolean
      uploaded:
        type: string
        format: date-time
      uploader:
        $ref: '#/definitions/User'
      width:
        type: integer
      height:
        type: integer
      status:
        type: string
        enum:
          - NEW
          - PROCESSED
          - FAILED
      'properties':
        type: object
      active:
        type: string
  AssetPackage:
    type: object
    properties:
      id:
        type: string
        format: uuid
      deviceType:
        type: string
      name:
        type: string
      signed:
        type: boolean
      uploaded:
        type: string
        format: date-time
      uploader:
        $ref: '#/definitions/User'
      status:
        type: string
        enum:
          - NEW
          - PROCESSED
          - FAILED
  PromptTemplate:
    description: Prompt Template
    type: object
    properties:
      id:
        type: string
        format: uuid
      name:
        type: string
      description:
        type: string
      defaultBg:
        type: string
  PromptState:
    description: |
      Logical state during the retail process. E.g. "Idle" or "Enter Pin"
    type: object
    properties:
      id:
        type: string
        format: uuid
      code:
        description: State ID defined by Forecourt Controller
        type: string
      description:
        type: string
      secure:
        type: boolean
      numericInput:
        description: Whether numeric entry is expected in this state
        type: boolean
      dynamicText:
        description: Whether dynamic text is expected in this state
        type: boolean
      sequence:
        description: Optional order in which prompt states should be shown in the UI
        type: integer
      attribs:
        description: Optional prompt state attributes.
        type: object
  PromptSet:
    description: Set of defined prompts which can be displayed on a device
    type: object
    properties:
      id:
        type: string
        format: uuid
      name:
        type: string
      bg:
        type: string
      fontColor:
        type: string
      deviceType:
        type: string
      promptSetProfileName:
        type: string
      template:
        type: object
        properties:
          id:
            type: string
            format: uuid
          name:
            type: string
      created:
        type: string
        format: date-time
      createdBy:
        $ref: '#/definitions/User'
      modified:
        type: string
        format: date-time
      modifiedBy:
        $ref: '#/definitions/User'
      lang:
        type: object
        properties:
          ISO_CODE:
            description: Language Object
            $ref: '#/definitions/PromptSetLanguage'
      states:
        type: array
        items:
          $ref: '#/definitions/AssignedPrompt'
      secureFingerprint:
        type: string
      nonSecureFingerprint:
        type: string
      status:
        type: string
        enum:
          - DRAFT
          - PUBLISHING
          - PUBLISHED
      vendorAsset:
        type: object
        description: this asset will be included in the promptset package
        properties:
          id:
            type: string
            description: the software id
          url:
            type: string
            description: the download url of this asset
  AssignedPrompt:
    description: Assignment of a prompt to a prompt state
    type: object
    properties:
      id:
        type: string
        description: the prompt state id
      code:
        type: string
      description:
        type: string
      secure:
        type: boolean
      sequence:
        type: number
      attribs:
        type: string
      promptType:
        type: string
      assignments:
        type: array
        items:
          $ref: '#/definitions/Prompt'
  TouchmapArea:
    description: Touchmap area
    type: object
    properties:
      shape:
        type: string
        enum:
          - circle
          - rect
      coords:
        type: string
      keyCode:
        type: string
      keyCodeName:
        type: string
      softkeyId:
        type: string
      softkeyName:
        type: string
  Touchmap:
    description: Touchmap
    type: object
    properties:
      name:
        type: string
      description:
        type: string
      areas:
        type: array
        items:
          $ref: '#/definitions/TouchmapArea'
  Prompt:
    description: Populated view which can be displayed on a device
    type: object
    properties:
      type:
        $ref: '#/definitions/PromptType'
      deviceType:
        type: string
      promptSetLanguageSupportId:
        type: string
      elements:
        type: array
        items:
          type: string
        description: Array of ODML elements
      transactionState:
        type: string
        enum:
          - null
          - fueling
          - idle
      contactless:
        type: boolean
        description: Only G6s can set this to true
      touchmap:
        type: object
        properties:
          id:
            type: string
          areas:
            type: array
            description: Array of ODML area elements
            items:
              $ref: '#/definitions/TouchmapArea'
      primaryAsset:
        type: object
        properties:
          id:
            type: string
            format: uuid
          type:
            type: string
          name:
            type: string
          thumbnailUrl:
            type: string
            format: url
          sourceUrl:
            type: string
            format: url
          package:
            type: object
            properties:
              id:
                type: string
                format: uuid
              name:
                type: string
              signed:
                type: boolean
      backgroundColor:
        type: string
        format: hex-color
      fontColor:
        type: string
        format: hex-color
      fontSize:
        type: integer
      staticText:
        type: string
      textTop:
        type: integer
      softkeys:
        type: array
        items:
          type: object
          properties:
            softkey:
              type: integer
            label:
              type: string
            fontColor:
              type: string
            fontSize:
              type: integer
            keycode:
              type: string
  UpdatePrompt:
    type: object
    properties:
      type:
        description: Prompt type
        properties:
          id:
            type: integer
      primaryAsset:
        type: object
        properties:
          id:
            type: string
            format: uuid
      backgroundColor:
        type: string
        format: hex-color
      fontColor:
        type: string
        format: hex-color
      fontSize:
        type: integer
      staticText:
        type: string
      textTop:
        type: integer
      softkeys:
        type: array
        items:
          type: object
          properties:
            softkey:
              type: integer
            label:
              type: string
            fontColor:
              type: string
            fontSize:
              type: integer
            keycode:
              type: string
  PromptType:
    description: |
      Defines the functionality available when defining a prompt
    type: object
    properties:
      id:
        type: integer
      name:
        type: string
      secure:
        type: boolean
      numericInput:
        type: boolean
      dynamicText:
        type: boolean
      staticText:
        type: boolean
      backgroundImage:
        type: boolean
      backgroundColor:
        type: boolean
      fontColor:
        type: boolean
      fontSize:
        type: boolean
      video:
        type: boolean
      internalName:
        type: string
  Release:
    type: object
    properties:
      release:
        type: string
        description: Release number (format 0000.000.000)
      releaseDate:
        type: string
        format: date-time
        description: Release date/time
      title:
        type: string
        description: Release title
      description:
        type: string
        description: Release summary
      updatedBy:
        type: string
        description: Name of the creator/updater
      dateCreated:
        type: string
        format: date-time
        description: Name of the creator
      dateUpdated:
        type: string
        format: date-time
        description: Last update date/time
  AdminRelease:
    type: object
    properties:
      release:
        type: string
        description: Release number (format 0000.000.000)
      releaseDate:
        type: string
        format: date-time
        description: Release date/time
      title:
        type: string
        description: Release title
      description:
        type: string
        description: Release summary
      updatedBy:
        type: string
        description: Name of the creator/updater
      dateCreated:
        type: string
        format: date-time
        description: Name of the creator
      dateUpdated:
        type: string
        format: date-time
        description: Last update date/time
      visible:
        type: boolean
        description: Releases with visible set to false won't be accessible by normal users
  SiteResponseDto:
    type: object
    required:
      - id
      - name
      - address
      - owner
      - tags
      - siteGroups
    properties:
      id:
        type: string
        description: The id of the site
      referenceId:
        type: string
        description: Reference ID used by GSTV/Playlist API
      created:
        type: string
        description: Created date time
      name:
        type: string
        description: The name of the site that is being added
      address:
        type: string
        description: Address string in Google API Json format
      formattedAddress:
        type: string
        description: Address in human readable format
      contactPhone:
        type: string
        description: Contact phone number
      contactEmail:
        type: string
        description: Contact email
      latitude:
        type: number
        description: Latitude of the site
      longitude:
        type: number
        description: Longtitude of the site
      timezoneId:
        type: string
        description: Time zone id.
      visible:
        type: boolean
        description: Tells if a site is visible in the UI
      tags:
        type: array
        description: List of tags associated with the site
        items:
          $ref: '#/definitions/Tag'
      siteGroups:
        type: array
        description: List of site-groups associated with the site
        items:
          $ref: '#/definitions/SiteGroupType'
      isDefault:
        type: boolean
        description: Is this site the company default site
      owner:
        $ref: '#/definitions/Company'
      status:
        type: integer
        description: 'Status of the site (0=No_Data_Available, 1=OK, 2=Warning, 3=Offline)'
        enum:
          - 0
          - 1
          - 2
          - 3
      keyGroup:
        $ref: '#/definitions/KeyGroupResponseObject'
      suppressOffhoursAlarm:
        type: boolean
        description: Flag for disabling offline alerts during site's off-hours
      externalReferences:
        type: array
        description: List of references associated with the site
        items:
          $ref: '#/definitions/ExternalReferences'
      allowedExternalReferenceTypes:
        type: array
        description: 'Allowed Reference Types'
        items:
          $ref: '#/definitions/allowedExternalReferenceTypes'
  ExternalReferences:
    type: object
    properties:
      referenceId:
        type: string
        description: The id of the external reference
      referenceType:
        type: string
        description: The type of the reference
  allowedExternalReferenceTypes:
    type: string
    description: 'Allowed external types'
    enum:
      - GVR
      - GSTV
      - SALESFORCE
  AllowedExternalReferenceTypesResult:
    type: object
    properties:
      allowedExternalReferenceTypes:
        type: array
        description: 'Allowed Reference Types'
        items:
          $ref: '#/definitions/allowedExternalReferenceTypes'
  SitesResult:
    type: object
    required:
      - resultsMetadata
    properties:
      resultsMetadata:
        $ref: '#/definitions/ResultsMetadata'
      results:
        type: array
        items:
          $ref: '#/definitions/SiteResponseDto'
  SiteCreate:
    type: object
    required:
      - name
      - address
      - formattedAddress
      - latitude
      - longitude
      - siteGroups
      - tags
      - timezoneId
    properties:
      name:
        type: string
        description: The name of the site that is being added. Must be unique
      address:
        type: string
        description: Address string in Google API Json format
      keyGroupId:
        type: string
        description: id of RKI key group
      contactPhone:
        type: string
        description: Contact phone number
      contactEmail:
        type: string
        description: Contact email
      formattedAddress:
        type: string
        description: Formatted address string
      latitude:
        type: number
        description: Latitude of the site
      longitude:
        type: number
        description: Longtitude of the site
      referenceId:
        type: string
        description: Reference ID used by GSTV/Playlist API
      siteGroups:
        type: array
        description: List of site groups the site is associated with
        items:
          $ref: '#/definitions/SiteGroupType'
      tags:
        type: array
        description: List of tags associated with the site
        items:
          $ref: '#/definitions/Tag'
      mfaCode:
        type: string
        description: MFA Code
      timezoneId:
        type: string
        description: Timezone id
      hours:
        type: array
        description: Site's opening and closing hours
        items:
          type: object
          properties:
            openAt:
              type: number
              description: Opening hours in milliseconds value
            closeAt:
              type: number
              description: Closing hours in milliseconds value
      visible:
        type: boolean
        description: Site's visibility flag
      suppressOffhoursAlarm:
        type: boolean
        description: Flag for disabling offline alerts during site's off-hours
      integrationId:
        type: string
        description: Optional Integration ID. Must be unique for the company.
      externalReferences:
        type: array
        description: External References for the site
        items:
          $ref: '#/definitions/ExternalReferences'
  SiteUpdate:
    type: object
    required:
      - name
      - address
      - formattedAddress
      - latitude
      - longitude
      - tags
      - timezoneId
    properties:
      name:
        type: string
        description: The name of the site that is being added. Must be unique
      address:
        type: string
        description: Address string in Google API Json format
      keyGroupId:
        type: string
        description: id of RKI key group
      contactPhone:
        type: string
        description: Contact phone number
      contactEmail:
        type: string
        description: Contact email
      formattedAddress:
        type: string
        description: Formatted address string
      latitude:
        type: number
        description: Latitude of the site
      longitude:
        type: number
        description: Longtitude of the site
      referenceId:
        type: string
        description: Reference ID used by GSTV/Playlist API
      tags:
        type: array
        description: List of tags associated with the site
        items:
          $ref: '#/definitions/Tag'
      mfaCode:
        type: string
        description: MFA Code
      timezoneId:
        type: string
        description: Timezone id
      hours:
        type: array
        description: Site's opening and closing hours
        items:
          type: object
          properties:
            openAt:
              type: number
              description: Opening hours in milliseconds value
            closeAt:
              type: number
              description: Closing hours in milliseconds value
      visible:
        type: boolean
        description: Site's visibility flag
      suppressOffhoursAlarm:
        type: boolean
        description: Flag for disabling offline alerts during site's off-hours
      externalReferences:
        type: array
        description: External References for the site
        items:
          $ref: '#/definitions/ExternalReferences'
  SiteUpdatePatch:
    type: object
    properties:
      name:
        type: string
        description: The name of the site that is being added. Must be unique
  Company:
    type: object
    required:
      - id
    properties:
      id:
        type: string
        description: The id of the company
      name:
        type: string
        description: The name of the company
  KeyGroupResponseObject:
    type: object
    description: the key group object
    properties:
      id:
        type: string
        description: the key group uuid
      name:
        type: string
        description: the key groups name
      ref:
        type: string
        description: the key groups reference
      owner:
        $ref: '#/definitions/Company'
  DevicesBulkmoveResponse:
    type: object
    properties:
      succeeded:
        type: number
        description: Number of devices successfully transferred to the site
      failed:
        type: number
        description: Number of devices failed to transfer to the site
      ignored:
        type: number
        description: Number of devices ignored to transfer to the site
  DeviceUpdate:
    type: object
    properties:
      siteId:
        type: string
        format: uuid
        description: Site id of destination site
      name:
        type: string
        description: Name of the target device
      description:
        type: string
        description: Description of the target device
      deviceType:
        type: string
      configUpdate:
        type: object
        description: Set of device configs changes done by the user
      deploymentType:
        type: string
        description: "Optinal 'maintenance-window', 'immediate', 'schedule'"
      scheduledDateTime:
        type: string
        description: 'Optinal Ex: YYYY-MM-DD HH:mm'
      serialNumber:
        type: string
        description: 'Optinal Ex: [A-Za-z0-9-]'
  DeviceUpdateResponse:
    type: object
    properties:
      id:
        type: integer
      siteId:
        type: string
        format: uuid
      siteName:
        type: string
      lastRegistered:
        type: string
      lastContact:
        type: string
      lastSuccessfulRki:
        type: string
      name:
        type: string
      description:
        type: string
      serialNumber:
        type: string
      keyGroupRef:
        type: string
      keyGroupId:
        type: string
      presence:
        type: string
      status:
        type: number
      alarmRulesSettings:
        type: object
        properties:
          suspendedByDeviceUntil:
            type: number
          suspendedFrom:
            type: number
          suspendedUntil:
            type: number
          suspended:
            type: boolean
      deviceType:
        type: object
        properties:
          id:
            type: string
          name:
            type: string
      promptSet:
        type: object
        properties:
          id:
            type: string
          name:
            type: string
          version:
            type: string
      siteKeygroupId:
        type: string
      config:
        type: object
        description: Device details
  DeviceGetResponse:
    type: object
    properties:
      id:
        type: integer
      siteId:
        type: string
        format: uuid
      siteName:
        type: string
      lastRegistered:
        type: string
      lastContact:
        type: string
      lastSuccessfulRki:
        type: string
      name:
        type: string
      description:
        type: string
      serialNumber:
        type: string
      keyGroupRef:
        type: string
      keyGroupId:
        type: string
      presence:
        type: string
      status:
        type: number
      auxDevice:
        type: object
        description: Value available when the device is Main Device and both device is setup for auxiliary support.
      mainDevice:
        type: object
        description: Value available when the device is Auxiliary device and both device is setup for auxiliary support.
      alarmRulesSettings:
        type: object
        properties:
          suspendedByDeviceUntil:
            type: number
          suspendedFrom:
            type: number
          suspendedUntil:
            type: number
          suspended:
            type: boolean
      deviceType:
        type: object
        properties:
          id:
            type: string
          name:
            type: string
      promptSet:
        type: object
        properties:
          id:
            type: string
          name:
            type: string
          version:
            type: string
      siteKeygroupId:
        type: string
      config:
        type: object
        description: Device details
      inFlight:
        type: boolean
        description: Not available for GET /devices/:serialNumber endpoint
      configSchema:
        type: object
        description: Set of device config editable by the user based on device capability
      configData:
        type: object
        description: Used to populate config schema
      configForm:
        type: object
        description: Used to describe config panel form
  DeviceCertificateUpdateResponse:
    type: object
    properties:
      id:
        type: integer
      certificate:
        type: string
      isJsonCertificates:
        type: boolean
      presence:
        type: string
  DeviceFileCreateResponse:
    type: object
    properties:
      id:
        type: integer
      device_id:
        type: integer
      device_path:
        type: string
      content_type:
        type: string
      application_id:
        type: string
      timestamp:
        type: string
        format: date
  DataUploadPayload:
    type: array
    items:
      $ref: '#/definitions/DataUploadPayloadItem'
  DataUploadPayloadItem:
    type: object
    required:
      - ts
      - dt
      - ds
      - dn
      - dv
    properties:
      ts:
        type: string
      dt:
        type: string
      ap:
        type: integer
      ds:
        type: string
      dn:
        type: string
      dv:
        type: integer
  EmailTokenUpdateResponse:
    type: object
    properties:
      id:
        type: integer
        description: The id of the email token
      user_id:
        type: string
        format: uuid
        description: The id of the user
      type:
        type: string
        description: The type of the email token
      value:
        type: string
        description: The value of the email token
      expires:
        type: string
        format: date
        description: The expiry date of the email token
  SoftwareCreateResponse:
    type: object
    properties:
      softwareId:
        type: string
        description: The id of the software update
      softwareTypeId:
        type: string
        description: The id of the software type (OBSOLETE)
      version:
        type: string
        description: The version of the software update
      description:
        type: string
        description: The description of the software update
      prerequisiteVersion:
        type: string
        description: The version required to install this version
      softwareFile:
        type: string
        description: File name
      softwareFileSignature:
        type: string
        description: File signature
      softwareFileUrl:
        type: string
        description: URL to S3 location where file is stored.
      active:
        type: boolean
        description: Indicated if software is active.
      name:
        type: string
        description: The name of the software update
      dateTimeCreated:
        type: integer
        description: Date when software was craeted
      companyId:
        type: string
        description: Id of the owner company
      size:
        type: integer
        description: File size
      deviceType:
        type: string
        description: Id of the device type
      type:
        type: string
        description: "Software type: 'software' or 'media'"
      relatedEntity:
        type: string
        description: 'Id of a related entity, for media it refers to a promptset'
  SoftwareCreatePayload:
    type: object
    required:
      - deviceType
      - softwareFile
      - softwareFileSignature
      - softwareFileUrl
      - size
      - type
      - companyId
    properties:
      version:
        type: string
        description: The version of the software update
      description:
        type: string
        description: The description of the software update
      prerequisiteVersion:
        type: string
        description: The version required to install this version
      softwareFile:
        type: string
        description: File name
      softwareFileSignature:
        type: string
        description: File signature
      softwareFileUrl:
        type: string
        description: URL to S3 location where file is stored.
      active:
        type: boolean
        description: Indicated if software is active.
      name:
        type: string
        description: The name of the software update
      companyId:
        type: string
        description: Id of the owner company
      size:
        type: integer
        description: File size
      deviceType:
        type: string
        description: Id of the device type
      type:
        type: string
        description: "Software type: 'software' or 'media'"
      relatedEntity:
        type: string
        description: Software related entity
  CompanyRefSerial:
    type: object
    required:
      - companyRef
      - serialNumber
    properties:
      companyRef:
        type: string
        description: Customer company reference name. This will require Invenco to setup the default site for the customer
      serialNumber:
        type: string
        description: the device serial number that will be transferred to the customerRef defaulted site
  DevicesResult:
    type: object
    properties:
      resultsMetadata:
        $ref: '#/definitions/ResultsMetadata'
      results:
        type: array
        items:
          $ref: '#/definitions/TargetResponseDto'
  TargetCreate:
    type: object
    required:
      - serialNumber
      - deviceType
      - siteId
      - name
    properties:
      deviceType:
        type: string
        description: The type device acquired from device type resource. Can be comma delimited (e.g. 'G6-100,G7-100') to support multiple device types filtering.
      serialNumber:
        type: string
        description: The serial number of the target device. Max length 128 alphanumeric and dash only.
      siteId:
        type: string
        description: The id of the site that the target device will be added to
      name:
        type: string
        description: The name of the target device. Max length is 128 characters only
      description:
        type: string
        description: The description of the target device. Max length is 1000 characters only
      certificate:
        type: string
        description: 'PKCS #7 or X.509 Device Encryption Certificate in either hexadecimal or base64 format.'
      isJsonCertificates:
        type: boolean
        description: if true then certificate is a stringified array of JSON Object
      macAddress:
        type: string
        description: The device MAC address
      data:
        type: string
        description: Device metadata
      keyGroupId:
        type: string
        description: Invenco key group where the device is currently sitting; if provided keyGroupRef will be ignored
      keyGroupRef:
        type: string
        description: Futurex key group where the device is currently sitting; only used when keyGroupId not specified
      presence:
        type: string
        description: "Indicates if device is present on the instance. Allowed values 'PRESENT', 'OUT OF INSTANCE'"
      deploymentType:
        type: string
        description: "Optinal 'maintenance-window', 'immediate', 'schedule'"
      scheduledDateTime:
        type: string
        description: 'Optinal Ex: YYYY-MM-DD HH:mm'
  DeviceTypeSimpleDto:
    type: object
    required:
      - id
    properties:
      id:
        type: string
        description: The id of the device type
      name:
        type: string
        description: The display name of the device type
  SiteTargetResponseDto:
    allOf:
      - $ref: '#/definitions/TargetResponseDto'
      - type: object
        properties:
          appVersions:
            type: array
            description: returns the app versions for the device
            items:
              type: object
  TargetResponseDto:
    type: object
    required:
      - id
      - deviceType
      - siteId
      - name
      - serialNumber
    properties:
      id:
        type: integer
      deviceType:
        $ref: '#/definitions/DeviceTypeSimpleDto'
      serialNumber:
        type: string
        description: Serial Number of the target device
      name:
        type: string
        description: Name of the target device
      description:
        type: string
        description: Description of the target device
      siteId:
        type: string
        description: Site id the target device associated to
      siteName:
        type: string
        description: Site name the target device associated to
      siteKeygroup:
        type: string
        description: Key group ID from site
      lastContact:
        type: integer
        description: Number of milliseconds since 1970
      ipAddress:
        type: string
        description: The last known ip address of this device
      gatewayAddress:
        type: string
        description: The gateway address of this device
      lastRegistered:
        type: integer
        description: Number of milliseconds since 1970
      status:
        type: integer
        description: 'Status of the target (0=No_Data_Available, 1=OK, 2=Warning, 3=Offline)'
        enum:
          - 0
          - 1
          - 2
          - 3
      keyGroupRef:
        type: string
        description: key group reference
      keyGroupId:
        type: string
        description: Key group ID
      presence:
        type: string
        description: indicated if device is present on the instance; set by default to 'PRESENT'
      releaseVersion:
        type: string
        description: Invenco release number running on a device
      config:
        type: object
        description: Device config information
      isMaster:
        type: boolean
        description: indicate the device's isMaster config info
      hasRki:
        type: boolean
        description: indicate if the device has associated key cert
      isAuxiliary:
        type: boolean
        description: indicate if the device is an auxiliary device or not
      terminalId:
        type: string
        description: Device's terminal ID config info
      auxStatus:
        type: string
        description: indicates the device's auxiliary status
  SoftwareResult:
    type: object
    required:
      - resultsMetadata
    properties:
      resultsMetadata:
        $ref: '#/definitions/ResultsMetadata'
      results:
        type: array
        items:
          $ref: '#/definitions/SoftwareFileResponse'
  SoftwareFileResponse:
    type: object
    required:
      - id
      - name
      - deviceType
      - promptSetProfileName
    properties:
      id:
        type: string
        description: The id of the software update
      name:
        type: string
        description: The name of the software update
      description:
        type: string
        description: The description of the software update
      deviceType:
        $ref: '#/definitions/DeviceTypeSimpleDto'
      promptSetProfileName:
        type: string
        description: The name of the promptSet Profile Name
      uploadedDate:
        type: string
        description: Date/time when software update was uploaded
      uploadedBy:
        type: object
        properties:
          id:
            type: string
            description: ID of the user who uploaded the software update
          name:
            type: string
            description: Name of the user who uploaded the software update
      updatedDate:
        type: string
        description: Date/time when software update was modified
      updatedBy:
        type: object
        properties:
          id:
            type: string
            description: ID of the user who modified software update
          name:
            type: string
            description: Name of the user who modified software update
      size:
        type: integer
        description: File size
      softwareFile:
        type: string
        description: File name
      type:
        type: string
        description: "Software type: 'software' or 'media'"
      relatedEntity:
        type: string
        description: 'Id of a related entity, for media it refers to a promptset'
  SoftwareFileUpdate:
    type: object
    required:
      - name
      - deviceType
    properties:
      name:
        type: string
        description: The name of the software update
      description:
        type: string
        description: The description of the software update
      deviceType:
        type: string
        description: The device product type
  Tag:
    type: object
    required:
      - name
    properties:
      id:
        type: integer
        description: The id of the tag
      name:
        type: string
        description: The name of the tag
      siteCount:
        type: integer
        description: The number of sites associated with this tag
  JobResponse:
    description: This is the main Job object within the system
    type: object
    required:
      - id
      - deviceId
      - destination
      - type
      - data
      - status
      - embargo
      - expiration
    properties:
      id:
        type: string
        description: The identifier of the job (GUID)
        readOnly: true
      deviceId:
        type: string
        description: The device that this job is for
      destination:
        type: string
        description: The application on the device which should receive this job
      type:
        type: string
        description: The type of job to run
      data:
        type: object
        description: Input data for the job (can be any valid JSON)
      status:
        $ref: '#/definitions/JobStatus'
      createdBy:
        type: string
        description: ID of the user who created this job
        readOnly: true
      createdOn:
        type: integer
        format: int64
        description: When the job was created
        readOnly: true
      embargo:
        type: integer
        format: int64
        description: Time when the job should become available to the device
      expiration:
        type: integer
        format: int64
        description: Time when the job must complete before.  If job has not completed at this time then the job is considered failed.
      dependencies:
        type: array
        items:
          type: object
          properties:
            jobId:
              type: string
              description: ID of the other job that this job depends on
            runOnFail:
              type: boolean
              description: Whether this job should run even if the other job fails
    example:
      id: ********************************
      deviceId: '45'
      destination: invenco.system
      type: sys.download
      status: 0
      data:
        messageVersion: 1
        fileURI: 'http://cdn.ics.invenco.com/********************************/g7-emvapp-1.3.7.pkg'
        fileName: g7-emvapp-1.3.7.pkg
        fileHashAlg: sha256
        fileHash: b94d27b9934d3e08a52e52d7da7dabfac484efe37a5380ee9088f7ace2efcde9
      createdBy: 894dc6c4-5d93-4f6e-8df8-e66295524b12
      createdOn: '2016-04-14T22:41:55.340Z'
      embargo: '2016-04-20T02:00:00.000Z'
      expiration: '2016-04-20T04:00:00.000Z'
  UpdateJob:
    description: This is the Job object used when updating a job
    type: object
    properties:
      deviceId:
        type: string
        description: The device that this job is for
      destination:
        type: string
        description: The application on the device which should receive this job
      type:
        type: string
        description: The type of job to run
      data:
        type: object
        description: Input data for the job (can be any valid JSON)
      embargo:
        type: integer
        format: int64
        description: Time when the job should become available to the device
      expiration:
        type: integer
        format: int64
        description: Time when the job must complete before.  If job has not completed at this time then the job is considered failed.
      dependencies:
        type: array
        items:
          type: object
          properties:
            jobId:
              type: string
              description: ID of the other job that this job depends on
            runOnFail:
              type: boolean
              description: Whether this job should run even if the other job fails
    required:
      - deviceId
      - destination
      - type
      - embargo
  DeviceJobResponse:
    description: This is the Job object which is exposed to the device
    type: object
    required:
      - id
      - deviceId
      - destination
      - type
      - data
    properties:
      id:
        type: string
        description: The identifier of the job (GUID)
        readOnly: true
      deviceId:
        type: string
        description: The device that this job is for
      destination:
        type: string
        description: The application on the device which should receive this job
      type:
        type: string
        description: The type of job to run
      data:
        type: string
        description: Input data for the job (can be any valid JSON) in string format
    example:
      id: ********************************
      deviceId: '45'
      destination: invenco.system
      type: sys.download
      data: '{"messageVersion": 1,"fileURI": "http://cdn.ics.invenco.com/********************************/g7-emvapp-1.3.7.pkg","fileName": "g7-emvapp-1.3.7.pkg","fileHashAlg": "sha256","fileHash": "b94d27b9934d3e08a52e52d7da7dabfac484efe37a5380ee9088f7ace2efcde9"}'
  JobStatus:
    type: integer
    enum:
      - 0
      - 1
      - 2
      - 3
      - 4
      - 5
    description: 'Status of the job. 0=New, 1=Accepted, 2=In progress, 3=Complete (success), 4=Failed, 5=Cancelled'
  JobDeviceRelation:
    description: This is the Job object which is exposed to the device
    type: object
    properties:
      deviceId:
        type: integer
        description: The device that this job is for
      jobId:
        type: string
        description: The identifier of the job (GUID)
  JobStatusResponse:
    description: Encapsulation of JobId and JobStatus
    type: object
    properties:
      jobId:
        type: string
        description: The identifier of the job (GUID)
      jobStatus:
        type: string
        description: The Status of the Job
  OfflinePackageSoftwareRequest:
    description: The offline software package request
    type: object
    required:
      - name
      - softwareIds
    properties:
      name:
        type: string
        description: The name of the package
      softwareIds:
        type: array
        description: The orderred array of software ids
        items:
          type: string
          description: The uuid of the software
  OfflinePackageRkiRequest:
    description: The offline RKI package request
    type: object
    required:
      - name
      - deviceIds
    properties:
      name:
        type: string
        description: The name of the package
      deviceIds:
        type: array
        description: The orderred array of device ids
        items:
          type: integer
          description: The id of the device
  OfflinePackageResponse:
    description: The offline package response
    type: object
    required:
      - id
      - companyId
      - type
      - name
      - status
      - createdOn
    properties:
      id:
        type: string
        description: The uuid of the package
      name:
        type: string
        description: The package name
      companyId:
        type: string
        description: The owner company name
      type:
        type: string
        description: 'The type of the package. e.g. software, RKI'
      files:
        type: array
        description: 'If the package is a software download package, files array contains the list of files included in this package'
        items:
          type: object
          $ref: '#/definitions/SoftwareFileResponse'
      size:
        type: integer
        description: The package file size in bytes
      checksum:
        type: string
        description: The package file sha256 checksum
      createdOn:
        type: string
        format: date-time
      status:
        type: string
        description: The status of the package
        enum:
          - IN_PROGRESS
          - DONE
          - FAILED
    example:
      id: b942d2ae-929f-46f3-b208-81505a423323
      name: firmware_update_v1
      type: software
      files:
        - id: '1'
          name: a device
          description: A device description
          deviceType:
            $ref: '#/definitions/DeviceTypeSimpleDto'
          uploadedDate: 1499053499791
          size: 29891
          softwareFile: cafelate.jpg
          type: software
          relatedEntity: null
      size: 38382921
      checksum: 9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08
      createdOn: '2016-04-20T02:00:00.000Z'
      status: DONE
  OfflinePackagePutRequest:
    description: The offline RKI package request
    type: object
    required:
      - name
    properties:
      name:
        type: string
        description: The new name of the package
  CreateAutomatedRKIRequestResponse:
    description: The offline RKI package request
    type: object
    properties:
      id:
        type: string
        description: Generated KRS id
      name:
        type: string
        description: Name/reason of the RKI request
      creator:
        type: object
        description: Creator of the RKI request
        properties:
          id:
            type: string
          fullName:
            type: string
      authorizer:
        type: string
        description: First authorizer
      secondAuthorizer:
        type: string
        description: Second authorizer
      created:
        type: string
        description: Date created
      expires:
        type: string
        description: Expiry date
      status:
        type: number
        description: RKI request session status
      progress:
        type: object
        description: Progress counter for devices being processed
        properties:
          total:
            type: number
          inProgress:
            type: number
          completed:
            type: number
          failed:
            type: number
      keyGroupRef:
        type: string
        description: Key Group Ref of the RKI request
      companyId:
        type: string
        description: Company Id
      approvers:
        type: array
        description: Approvers of the RKI request
        items:
          type: object
          properties:
            id:
              type: string
            fullName:
              type: string
            email:
              type: string
      deviceRequests:
        type: array
        description: List of devices included in the RKI session
        items:
          type: object
          properties:
            id:
              type: string
            device:
              type: object
              properties:
                id:
                  type: number
                serialNumber:
                  type: string
                name:
                  type: string
                keyGroupRef:
                  type: string
                site:
                  type: object
                  properties:
                    id:
                      type: string
                    name:
                      type: string
                    latitude:
                      type: string
                    longitude:
                      type: string
                    owner:
                      type: object
                      properties:
                        id:
                          type: string
                        name:
                          type: string
            status:
              type: number
            message:
              type: string
            originalKeyGroupRef:
              type: string
  ChallengeResponse:
    type: object
    required:
      - requestedBy
      - challenge
      - serialNumber
      - mfaCode
      - operation
    properties:
      requestedBy:
        type: string
        description: The name of the requesting user
      challenge:
        type: string
        description: Challenge code entered by user
      serialNumber:
        type: string
        example: A0217123456
        description: serial number of the OPT
      mfaCode:
        type: string
        example: 135769
        description: MFA code
      operation:
        type: string
        example: FACTORY_RESET
        description: TAMPER_CLEAR or FACTORY_RESET
  FileUploadCreate:
    type: object
    properties:
      name:
        type: string
      files:
        type: array
        items:
          $ref: '#/definitions/FileAppPath'
      devices:
        type: array
        items:
          $ref: '#/definitions/IntegerId'
      sites:
        type: array
        items:
          $ref: '#/definitions/StringId'
      siteTags:
        type: array
        items:
          $ref: '#/definitions/IntegerId'
      users:
        type: array
        items:
          $ref: '#/definitions/StringId'
  FileAppPath:
    type: object
    properties:
      applicationId:
        type: string
      path:
        type: string
  IntegerId:
    type: object
    properties:
      id:
        type: integer
  StringId:
    type: object
    properties:
      id:
        type: string
  FileUploadRetrieve:
    type: object
    required:
      - id
      - status
      - name
    properties:
      id:
        type: string
        description: UUID of the fileupload request
      name:
        type: string
      files:
        type: array
        items:
          $ref: '#/definitions/FileAppPath'
      status:
        type: integer
      totalJobs:
        type: integer
      inProgressJobs:
        type: integer
      completeJobs:
        type: integer
      failedJobs:
        type: integer
      cancelledJobs:
        type: integer
      packageURL:
        type: string
  DeviceFileRetrieveList:
    type: array
    items:
      $ref: '#/definitions/DeviceFileRetrieveItem'
  DeviceFileRetrieveItem:
    type: object
    properties:
      id:
        type: integer
      filePath:
        type: string
      contentType:
        type: string
      writeable:
        type: boolean
        description: 'If true, user can edit the contents of this file (i.e. config files)'
      lastPulled:
        type: string
        format: date-time
        description: Timestamp of when the file was last uploaded to cloud
      changed:
        type: boolean
        description: True if the cloud file is different from the device file
      pullRequestQueued:
        type: boolean
        description: True if there is an active pull request
      applicationId:
        type: string
  DeviceFileCreateList:
    type: array
    items:
      $ref: '#/definitions/DeviceFileCreateItem'
  DeviceFileCreateItem:
    type: object
    required:
      - filePath
      - applicationId
    properties:
      filePath:
        type: string
      contentType:
        type: string
      hash:
        type: string
      writeable:
        type: boolean
      timestamp:
        type: string
        format: date-time
      applicationId:
        type: string
  CopyFileRequest:
    type: object
    required:
      - sourceDeviceId
      - files
    properties:
      sourceDeviceId:
        type: integer
        description: The id of the source device
      files:
        type: array
        items:
          $ref: '#/definitions/FileAppPath'
  CompanyDataDto:
    type: object
    properties:
      companyId:
        type: string
        description: The company id
      companyName:
        type: string
        description: The name of the company
  SiteDataDto:
    type: object
    properties:
      siteId:
        type: string
        description: The id of the site
      siteName:
        type: string
        description: The name of the site that is being added
      companyId:
        type: string
        description: Id of the company
      active:
        type: boolean
        description: Tells if a site is active or not
      deletedDate:
        type: string
        description: Timestamp of when the site was deleted
  AssetDataDto:
    type: object
    properties:
      assetId:
        type: integer
      siteId:
        type: string
        description: Site id the target device associated to
      assetName:
        type: string
        description: Name of the target device
      deploymentType:
        type: string
        description: DeploymentType of the last updated
        enum:
          - immediate
          - maintenance-window
      lastRegistered:
        type: string
        description: Timestamp of when the asset was last registered
      releaseVersion:
        type: string
        description: Release version of the device firmware
      lastEditedDate:
        type: string
        description: Timestamp of the last edited date of asset
      lastContact:
        type: string
        description: Timestamp of the last point of asset contact
      assetStatus:
        type: integer
        description: 'Status of the target (0=No_Data_Available, 1=OK, 2=Warning, 3=Offline)'
        enum:
          - 0
          - 1
          - 2
          - 3
      deviceType:
        type: string
        description: Type of the device
      serialNumber:
        type: string
        description: Serial Number of the target device
      deletedDate:
        type: string
        description: Timestamp of when the device was deleted
      updatedBy:
        type: string
        description: Id of the creator/updater
  SiteTagDataDto:
    type: object
    properties:
      sitePropertiesData:
        type: array
        items:
          $ref: '#/definitions/SitePropertiesData'
      tagData:
        type: array
        items:
          $ref: '#/definitions/TagData'
  SitePropertiesData:
    type: object
    properties:
      siteId:
        type: string
        description: Id of the site
      propertyName:
        type: string
        description: Name of the property
      propertyValue:
        type: string
        description: Value of the property
      deleted:
        type: boolean
        description: Tells if a site tag is deleted or not
      deploymentType:
        type: string
        description: DeploymentType of the last updated
        enum:
          - immediate
          - maintenance-window
      updatedBy:
        type: string
        description: Id of the creator/updater
  TagData:
    type: object
    properties:
      tagId:
        type: integer
        description: Id of the tag
      tagName:
        type: string
        description: Name of the tag
      companyId:
        type: string
        description: Id of the company
      deleted:
        type: boolean
        description: Tells if a tag is deleted or not
  CustomAttributeUpdateResponse:
    type: array
    items:
      type: object
      properties:
        entityId:
          type: string
          example: 'entityID'
        integrationId:
          type: string
          example: 'integrationId'
        status:
          type: number
          enum: [400, 404, 200]
          example: 404
        errors:
          type: array
          items:
            type: string
            example: "Attribute definition doesn't exist"
  CustomAttributeUpdateBulkResponse:
    type: array
    items:
      type: object
      properties:
        attributeDefinitionId:
          type: string
          example: 'attributeDefinitionId'
        status:
          type: number
          enum: [400, 404, 200]
          example: 404
        errors:
          type: array
          items:
            type: string
            example: "Attribute definition doesn't exist"
  CustomAttributeUpdatePayload:
    type: array
    items:
      type: object
      properties:
        entityId:
          type: string
          description: EntityID (TenantID/SiteID/AssetID)
          example: 'e052702c-815d-449e-b367-5dbb85cb9056'
        integrationId:
          type: string
          description: IntegrationID
          example: '8f3d5d6978tq'
        isStaged:
          type: boolean
          description: Optional property, default = FALSE
          example: true
        attributes:
          type: array
          items:
            type: object
            properties:
              attributeDefinitionId:
                type: string
                example: 4923
              value:
                type: string
                example: 'EMV Online'
        deploymentType:
          type: string
          description: Optional property, default = maintenance-window
          default: 'maintenance-window'
          example: 'maintenance-window | immediate | schedule'
  CustomAttributeBulkUpdatePayload:
    type: object
    properties:
      entityIds:
        type: array
        items:
          type: string
          description: EntityID (TenantID/SiteID/AssetID)
          example: 'e052702c-815d-449e-b367-5dbb85cb9056'
      attributes:
        type: array
        items:
          type: object
          properties:
            attributeDefinitionId:
              type: string
              example: 4923
            value:
              type: string
              example: 'EMV Online'
      deploymentType:
        type: string
        description: Optional property
        example: 'maintenance-window | immediate | schedule'
      scheduledDateTime:
        type: string
        description: Optional property
        example: 'YYYY-MM-DD HH:mm'
      mfaCode:
        type: string
        description: User's MFA code
        example: '251656'
  CustomAttributeData:
    type: object
    properties:
      id:
        type: integer
        description: Id of the Attribute Definition
      isStaged:
        type: boolean
        description: Denotes if the Attribute is staged for site transfer or not
      groupId:
        type: integer
        description: Id of the Attribute Group
      groupName:
        type: string
        description: Name of the Attribute Group
      name:
        type: string
        description: Name of the Attribute
      value:
        type: string
        description: Value of the Attribute. If there is a site override then it will be this value, otherwise fall back to the Attribute Definition Default
      createdAt:
        type: string
        format: date-time
        description: The datetime at which the attribute was created
      updatedAt:
        type: string
        format: date-time
        description: The datetime at which the attribute was last updated
      isSecret:
        type: boolean
        description: Denotes if the attribute is secret
  CustomAttributeDefinition:
    type: object
    properties:
      id:
        type: integer
        description: Id of the Attribute Definition
      isThirdParty:
        type: boolean
        description: Denotes if the Attribute is third party
      isSearchable:
        type: boolean
        description: Denotes if the Attribute is searchable or not
      groupId:
        type: integer
        description: Id of the Attribute Group
      groupName:
        type: string
        description: Name of the Attribute Group
      name:
        type: string
        description: Name of the Attribute
      defaultValue:
        type: string
        description: Default Value of the Attribute
      schema:
        type: string
        description: Stringified JSON schema
      createdAt:
        type: string
        format: date-time
        description: The datetime at which the attribute was created
      updatedAt:
        type: string
        format: date-time
        description: The datetime at which the attribute was last updated
      isSecret:
        type: boolean
        description: Denotes if the attribute is secret
      isUserEditable:
        type: boolean
        description: Denotes if the Attribute is user editable
  MfaJobsDetails:
    type: object
    properties:
      dryRun:
        type: boolean
        description: Dry Run the device selection
      mfaCode:
        type: string
        description: MFA Code
      action:
        type: string
        description: job action eg. MERCHENT_RESET
      devices:
        type: array
        description: array of device serial numbers
        items:
          type: string
      sites:
        type: array
        description: array of site ids
        items:
          type: string
      tags:
        type: array
        description: array of tags
        items:
          type: integer
      siteGroups:
        type: array
        description: array of site groups
        items:
          type: string
    required:
      - mfaCode
      - action
      - devices
  FileUploadZipName:
    type: object
    properties:
      timezone:
        type: string
        description: Timezone of the client(browser)
      devices:
        type: array
        items:
          $ref: '#/definitions/IntegerId'
          required:
            - id
      sites:
        type: array
        items:
          $ref: '#/definitions/StringId'
          required:
            - id
      siteTags:
        type: array
        items:
          $ref: '#/definitions/IntegerId'
          required:
            - id
      deviceId:
        type: integer
  FileUploadZipNameResponse:
    type: object
    required:
      - name
    properties:
      name:
        type: string
        description: Name of the zip file
# #############################################################################
# PARAMETERS
# #############################################################################
parameters:
  siteStateParam:
    name: state
    in: path
    description: The site state name(key)
    required: true
    type: string
  deviceStateParam:
    name: state
    in: path
    description: The device state name(key)
    required: true
    type: string
  deviceStateTypeParam:
    name: type
    in: query
    description: The Device status type
    required: false
    type: string
    enum:
      - ALARM
      - STATUS
      - METRIX
      - VERSION
  searchFilterParam:
    name: searchFilter
    in: query
    description: The search string for finding results.  Must be SQL escaped (wild cards are not allowed)
    required: false
    type: string
  companyIdParam:
    name: companyId
    in: query
    description: The company id
    required: false
    type: string
  siteIdParam:
    name: id
    in: path
    description: The site id
    required: true
    type: string
  deviceIdParam:
    name: id
    in: path
    description: The device id
    required: true
    type: integer
  deviceSerialNumberParam:
    name: serialNumber
    in: path
    description: The device serial number
    required: true
    type: string
  deviceAuthTokenParam:
    name: X-Auth-Token
    in: header
    description: The JWT authorization token used for Device authentication
    required: false
    type: string
  icsDeviceTime:
    name: ics-device-current-time
    in: header
    description: The current time a device clock is set to. Used for adjusting message timevalues to actual time as device clock might not be properly synchronized.
    required: false
    type: string
  AuthorizationTokenParam:
    name: Authorization
    in: header
    description: 'JWT authentication token used for User authentication. Uses the Bearer token header (e.g Authorization: Bearer 1234)'
    required: false
    type: string
  SystemTokenParam:
    name: Authorization
    in: header
    description: 'JWT authentication token used for System authentication. Uses the Bearer token header (e.g Authorization: Bearer 1234)'
    required: false
    type: string
  TenantIdParam:
    name: tenantId
    in: header
    description: 'Tenant ID - to give context to the System Token'
    required: true
    type: string
  PageKeyParam:
    name: pageKey
    in: query
    description: Last evaluated object key. Used for DynamoDB queries.
    required: false
    type: string
  PageMinSizeParam:
    name: pageMinSize
    in: query
    description: Minimum page size used to generate response
    required: false
    type: integer
  pageIndexParam:
    name: pageIndex
    in: query
    description: The page index to return
    required: false
    type: integer
    default: 0
  pageSizeParam:
    name: pageSize
    in: query
    description: The maximum number of records to return in a page
    required: false
    type: integer
    default: 100
  qParam:
    name: q
    in: query
    description: The search string for finding results.  Must be SQL escaped (wild cards are not allowed)
    required: false
    type: string
  presenceParam:
    name: presence
    in: query
    description: 'The presence filter: PRESENT or OUT_OF_INSTANCE'
    required: false
    type: string
  userGroupIdParam:
    name: id
    in: path
    description: The user group id for the user group search
    required: true
    type: integer
  bulkOperationIdParam:
    name: id
    in: path
    description: The bulk operation id
    required: true
    type: string
    format: uuid
  SitesQueryStr:
    name: 'sites[]'
    description: |
      Array of site ids to filter the report to.
      To specify multiple values use this format: `?sites[]=XXX&sites[]=YYY`

      On the server this will result in `var sites = [ 'XXX', 'YYY' ]`
    in: query
    required: false
    type: array
    items:
      type: string
      format: uuid
  AlarmsQueryStr:
    name: 'alarms[]'
    description: |
      Array of alarm codes to filter the report to.
      To specify multiple values use this format: `?alarms[]=XXX&alarms[]=YYY`

      On the server this will result in `var alarms = [ 'XXX', 'YYY' ]`
    in: query
    required: true
    type: array
    items:
      type: string
  MetricQueryStr:
    name: 'metrics[]'
    description: |
      Array of metric codes to filter the report to.
      To specify multiple values use this format: `?metrics[]=XXX&metrics[]=YYY`

      On the server this will result in `var metrics = [ 'XXX', 'YYY' ]`
    in: query
    required: true
    type: array
    items:
      type: string
  StartQueryStr:
    name: start
    description: |
      Start of time range to generate report for
    in: query
    required: true
    type: string
    format: date-time
  EndQueryStr:
    name: end
    description: |
      End of time range to generate report for
    in: query
    required: true
    type: string
    format: date-time
  fileIdParam:
    name: fileId
    in: path
    description: The file id
    required: true
    type: integer
  fileUploadRequestAppParam:
    name: app
    in: query
    description: The application id of the file being uploaded
    required: true
    type: string
  fileUploadRequestIdParam:
    name: id
    in: path
    description: The fileUploadrequest id
    required: true
    type: string
  fileUploadRequestPathParam:
    name: filePath
    in: query
    description: The path of the file being uploaded
    required: true
    type: string
  hierarchyRequestPathParam:
    name: endDate
    in: query
    description: The End Date for heirachy data queries
    required: true
    type: string

# #############################################################################
# RESPONSES
# #############################################################################
responses:
  internalServerError:
    description: Internal Server Error
  authenticationFailed:
    description: Authentication Failed. Check token
  forbiddenError:
    description: The client does not have access rights to the content.
  badPayloadOrParameters:
    description: Bad request. Check payload and URI parameters.
  accessDenied:
    description: Access denied. Unsufficient role privilages to execute the action.
  TooManyRequestsError:
    description: Too many request.

# #############################################################################
# TAGS
# #############################################################################
tags:
  - name: users
    description: Manage users
  - name: usergroups
    description: Manage user groups
  - name: sitegroups
    description: Manage site groups
  - name: companies
    description: Company management
  - name: stats
    description: Statistics about ICS servers
  - name: authentication
    description: Authenticate users
  - name: acceptable
    description: Check acceptable info
  - name: report
    description: Report data
  - name: prompt
    description: Media Management - Prompts
  - name: prompt-sets
    description: Media Management - Prompt Sets
  - name: prompt-templates
    description: Media Management - Prompt Templates
  - name: assets
    description: Media Management - Assets
  - name: asset-packages
    description: Media Management - Asset Packages
  - name: day-parts
    description: Media Management - Day Parts
  - name: sites
    description: Manage the sites
  - name: software
    description: Manage the software update files
  - name: jobs
    description: Jobs for target devices
  - name: devices
    description: Manage target devices
  - name: tags
    description: Manage the tags that are used by site
  - name: notifications
    description: Responsible for sending notifications to users
  - name: hierarchy
    description: For retrieving hierarchy data
  - name: internal
    description: Private services - Used by ICS internally
