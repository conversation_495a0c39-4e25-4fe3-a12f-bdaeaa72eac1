---
# Reusable workflow that will do a clean installation of node dependencies, cache/restore them, build the source code and uploads artifact to Workflow storage
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions
name: Build

on:
  workflow_call:
    inputs:
      package_name:
        required: true
        type: string
      release_number:
        required: true
        type: string
    outputs:
      artifact:
        description: 'Path to artifact file'
        value: ${{ jobs.build.outputs.artifact }}
      build_name:
        description: 'Build name'
        value: ${{ jobs.build.outputs.build_name }}
      build_number:
        description: 'Build number'
        value: ${{ jobs.build.outputs.build_number }}

jobs:
  build:
    runs-on: [self-hosted, linux]

    container:
      image: ghcr.io/invenco-cloud-systems-ics/ics-images/os-al2-generic:latest
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        HUSKY: 0
      credentials:
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    permissions:
      contents: read
      packages: read

    defaults:
      run:
        shell: bash
        working-directory: './'

    outputs:
      artifact: ${{ steps.release.outputs.artifact }}
      build_name: ${{ steps.release.outputs.build_name }}
      build_number: ${{ steps.release.outputs.build_number }}

    env:
      BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
      TEMP_DIR: 'temp-build-dir'

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'

      - name: Build app
        run: |
          npm ci --fund=false --audit=false --ignore-scripts
          npm run build
          (cp .npmrc dist/ && cd dist/ && npm ci --omit=dev --fund=false --audit=false --ignore-scripts && rm .npmrc)

      - name: Set Archive Release Details
        id: release
        run: |
          N2_VERSION=$(date +%y%m%d%H%M%S%3N)
          F_VERSION=${{ inputs.release_number }}-${{ env.BRANCH_NAME }}-${N2_VERSION}.${{ github.run_attempt }}

          # Package variables
          echo "artifact=${{ inputs.package_name }}-${F_VERSION}.zip" >> $GITHUB_OUTPUT

          # Artifactory variables
          echo "build_name=${{ inputs.release_number || github.run_attempt }}" >> $GITHUB_OUTPUT
          echo "build_number=${N2_VERSION}.${{ github.run_attempt }}" >> $GITHUB_OUTPUT

      - name: Copy Source
        run: rsync -a ./dist/* ${{ env.TEMP_DIR }}

      - name: Copy Resources
        run: |
          source_dirs=(
            "./docs/"
            "./handlers/company/teardown/"
            "./lib/.bin/"
            "./resources/"
          )

          # Iterate through source directories and use rsync
          for source in "${source_dirs[@]}"; do
            echo "Copying file for $source"
            rsync -av --relative $source ${{ env.TEMP_DIR }}
          done

          #- name: Update npmrc for package build
          #run: |
          # Update .npmrc to ensure instance can install from private repos
          # sed "s/\${GITHUB_TOKEN}/${{ secrets.AWS_BEANSTALK_TOKEN }}/" .npmrc > tmpfile
          #mv tmpfile "${{ env.TEMP_DIR }}/.npmrc"

      - name: Archive Release
        id: archive
        run: |
          echo "Create a zip package"
          pushd ${{ env.TEMP_DIR }} >/dev/null
          zip -qr ${{ steps.release.outputs.artifact }} .
          popd >/dev/null

      - name: Upload Workflow Artifact
        uses: actions/upload-artifact@v4
        if: success()
        with:
          name: ${{ steps.release.outputs.artifact }}
          path: ${{ env.TEMP_DIR }}/${{ steps.release.outputs.artifact }}
          retention-days: 1
          if-no-files-found: error
