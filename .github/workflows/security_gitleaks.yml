---
# Performs a Gitleaks Scan using a reusable workflow
name: Gitleaks Scan

on:
  schedule:
    - cron: '0 0 * * *'
  push:
    branches: [master, main]

permissions:
  packages: read
  contents: read
  pull-requests: write

jobs:
  run-scan:
    name: Run Gitleaks Scan
    uses: Invenco-Cloud-Systems-ICS/github-actions-internal/.github/workflows/security_gitleaks.yml@main
    with:
      # Issue with artifact upload using Self Hosted Runners: https://github.com/gitleaks/gitleaks-action/issues/125
      enable_artifact_upload: false
    secrets:
      license: ${{ secrets.GITLEAKS_LICENSE }}
