---
# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# Will then upload the artifact to Artifactory as well as to Workflow shared storage (1 day retention)
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions
name: Build & Upload Dev (ICS Node)

on:
  push:
  workflow_dispatch:
    inputs:
      release_number_override:
        required: false
        type: string

permissions:
  contents: read
  packages: read
  id-token: write

jobs:
  lint:
    uses: Invenco-Cloud-Systems-ICS/github-actions-internal/.github/workflows/generic_lint.yml@main

  test:
    needs: lint
    uses: Invenco-Cloud-Systems-ICS/github-actions-internal/.github/workflows/generic_unit_test.yml@main

  build:
    needs: test
    uses: ./.github/workflows/_build.yml
    with:
      package_name: 'ics-api-nodejs'
      release_number: ${{ inputs.release_number_override || vars.ICS_RELEASE_NUMBER }}
    secrets: inherit

  upload:
    uses: Invenco-Cloud-Systems-ICS/github-actions-internal/.github/workflows/artifact_upload.yml@main
    needs: [build, test]
    with:
      artifact: ${{ needs.build.outputs.artifact }}
      build_name: ${{ needs.build.outputs.build_name }}
      build_number: ${{ needs.build.outputs.build_number }}
      harness_pipeline: CORE
      harness_project: ICS_CORE
      project: ics
      repository: ics-core-generic-dev/ics-api-nodejs/
    secrets:
      token: ${{ secrets.JF_ACCESS_TOKEN }}
