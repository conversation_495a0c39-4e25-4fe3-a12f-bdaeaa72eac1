---
# Performs a Coverity on Polaris Scan using a reusable workflow
name: Coverity on Polaris Scan

on:
  schedule:
    - cron: '0 0 * * *'
  push:
    branches: [master, main]

permissions:
  packages: read
  contents: read

jobs:
  run-scan:
    name: Run Coverity on Polaris Scan
    uses: Invenco-Cloud-Systems-ICS/github-actions-internal/.github/workflows/security_coverity-on-polaris.yml@main
    secrets:
      server_url: ${{ secrets.POLARIS_SERVER_URL }}
      access_token: ${{ secrets.POLARIS_ACCESS_TOKEN }}
