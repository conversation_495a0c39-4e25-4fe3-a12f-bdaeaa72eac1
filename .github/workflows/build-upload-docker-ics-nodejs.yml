# Builds a docker image, Upload to <PERSON><PERSON><PERSON> and Facets
name: Build & Upload Docker Image (ICS Node)

on:
  push:
    branches:
      - master
      - release-*
  pull_request:
    branches:
      - master
  workflow_dispatch:
    inputs:
      release_number_override:
        required: false
        type: string
  workflow_call:
    outputs:
      artifact_url:
        description: 'Artifact url'
        value: ${{ jobs.upload.outputs.artifact_url }}

permissions:
  actions: read
  contents: read
  packages: write
  id-token: write

jobs:
  # Build docker image and Upload to Artifactory (Jfrog)
  upload:
    uses: Invenco-Cloud-Systems-ICS/github-actions-internal/.github/workflows/build-upload-docker-image.yml@main
    with:
      jfrog_repository: ics-api-nodejs-docker-dev
      image_name: al2node18-api-nodejs
      target_image: api-nodejs
      harness_project: ICS_CORE
      harness_pipeline: CORE_AWSDEVQA
      release_number_override: ${{ inputs.release_number_override }}
    secrets:
      JF_ACCESS_TOKEN: ${{ secrets.JF_ACCESS_TOKEN }}

  # Upload artifact created in above step to facets, only for release branches
  upload-facets:
    needs: [upload]
    if: startsWith(github.ref_name, 'release-')
    uses: Invenco-Cloud-Systems-ICS/github-actions-internal/.github/workflows/upload-artifact-facets.yml@main
    with:
      control_plane_url: https://invenco.console.facets.cloud
      blueprint_name: ics
      ci_name: api-node-js
      facets_artifactory_name: Jfrog
      run_id: ${{ github.run_number }}
      branch_name: ${{ github.ref_name }}
      artifact_url: ${{ needs.upload.outputs.artifact_url }}
    secrets:
      FACETS_USER_EMAIL: ${{ secrets.FACETS_USER_EMAIL }}
      FACETS_USER_TOKEN: ${{ secrets.FACETS_USER_TOKEN }}
