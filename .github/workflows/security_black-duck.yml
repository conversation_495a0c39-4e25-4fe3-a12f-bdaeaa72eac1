---
# Performs a Black Duck Scan using a reusable workflow
name: Black Duck Scan

on:
  schedule:
    - cron: '0 0 * * *'
  push:
    branches: [master, main]

permissions:
  packages: read
  contents: read
  pull-requests: write

jobs:
  run-scan:
    name: <PERSON> Black <PERSON> Scan
    uses: Invenco-Cloud-Systems-ICS/github-actions-internal/.github/workflows/security_black-duck.yml@main
    secrets:
      server_url: ${{ secrets.BLACKDUCK_URL }}
      access_token: ${{ secrets.BLACKDUCK_API_TOKEN }}
