const fs = require('fs');

const environment = process.env.ENVIRONMENT;
// eslint-disable-next-line import/no-dynamic-require
const config = require(`./build/config/${environment}/config`);
// eslint-disable-next-line import/no-dynamic-require
const fileRenameTemplatesConfig = require(
  `./build/config/${environment}/filerenametemplates`
);
const privateKey = fs.readFileSync(`./build/config/${environment}/key.pem`);
const invalidRKIDevices = config.invalidRKIDevices
  ? config.invalidRKIDevices
  : ['G6-100', 'FUELPOS', 'IPT', 'vOPT', 'E1-100-EPS', 'C1-100', 'EDGE'];
config.offlineTimeoutMinutes = config.offlineTimeoutMinutes || '5 MINUTES';
config.criticalSitePercentage = config.criticalSitePercentage || 0.5;

module.exports = {
  config,
  fileRenameTemplatesConfig,
  environment,
  privateKey,
  invalidRKIDevices,
};
