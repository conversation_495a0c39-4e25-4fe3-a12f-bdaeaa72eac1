{"name": "ics-node-api", "version": "0.0.1", "description": "ICS Node API", "author": "Invenco Group Ltd", "license": "UNLICENSED", "prettier": "@invenco-cloud-systems-ics/eslint-config/prettierConfig", "repository": {"type": "git", "url": "https://github.com/Invenco-Group-Limited/ics-api-nodejs"}, "main": "index.ts", "scripts": {"prepare": "npx husky", "clean": "if [ -d \"dist\" ]; then rm -Rf dist; fi", "build": "npm run clean && tsc", "start": "node index.js | lib/.bin/ics-logger", "start-server": "node server.js | lib/.bin/ics-logger", "start-dev": "env ENVIRONMENT=local nodemon -L index.ts", "start-dev-win": "set ENVIRONMENT=local&& nodemon -L index.ts", "start-dev-debug": "env ENVIRONMENT=local nodemon --exec 'node --inspect --require ts-node/register index.ts'", "debug": "env ENVIRONMENT=local ts-node $NODE_DEBUG_OPTION index.ts", "enverify": "enverify ./build", "test": "env ENVIRONMENT=unit-test vitest run", "test:one": "env ENVIRONMENT=unit-test vitest run tests/unit/rki/approval.handler.test.ts", "coverage": "env ENVIRONMENT=unit-test vitest run --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write './**'"}, "private": true, "nyc": {"exclude": ["**/*.spec.js", "**/*.spec.ts"]}, "lint-staged": {"*": ["npm run lint:fix", "npm run format"]}, "dependencies": {"@aws-sdk/client-ses": "^3.614.0", "@aws-sdk/client-sqs": "^3.614.0", "@invenco-cloud-systems-ics/device-type-onboarding-lib": "^1.0.8", "@invenco-cloud-systems-ics/ics-event-stream-lib": "1.0.6-1", "@jm18457/kafkajs-msk-iam-authentication-mechanism": "^3.1.2", "archiver": "^7.0.1", "async": "^3.2.0", "aws-sdk": "^2.853.0", "axios": "^0.21.3", "bcryptjs": "^2.4.3", "co": "^4.6.0", "csv-parse": "^5.5.0", "csv-stringify": "^1.0.4", "deepmerge": "^1.1.1", "exponential-backoff": "^3.1.1", "extract-zip": "^2.0.1", "file-type": "^19.0.0", "fix-esm": "^1.0.1", "fontkit": "^1.8.1", "formidable": "^1.0.17", "fs-extra": "3.0.1", "ioredis": "^5.3.2", "joi": "^8.4.2", "js-beautify": "^1.14.0", "js-yaml": "3.14.1", "json-stable-stringify": "^1.0.1", "jsonpath-plus": "^7.2.0", "jsonschema": "^1.4.1", "jsonwebtoken": "^7.0.0", "kafkajs": "^2.2.4", "lodash": "4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "newrelic": "^12.6.0", "node-cache": "^4.1.1", "pg": "^8.2.1", "pg-camelcase": "^0.0.3", "pg-query-stream": "^1.0.0", "pino": "^9.5.0", "prettier": "^3.2.5", "promise-state-machine-es6": "^2.1.0", "request": "^2.88.0", "request-promise": "^4.2.6", "restify": "^4.0.4", "restify-cors-middleware": "1.1.1", "restify-errors": "^4.2.3", "restify-jwt": "^0.4.0", "sanitize-html": "^2.13.1", "shortid": "^2.2.8", "speakeasy": "^2.0.0", "split2": "^3.2.2", "sqs-consumer": "^3.4.0", "stream-to-promise": "^2.2.0", "stream-transform": "^0.1.1", "swagger-jsdoc": "^3.5.0", "text-encoding": "^0.7.0", "ts-node": "^10.9.2", "typescript": "^5.1.3", "uuid": "^3.0.1", "uuid-validate": "0.0.2"}, "devDependencies": {"@invenco-cloud-systems-ics/eslint-config": "^2.2.4", "@invenco-cloud-systems-ics/vitest-config": "^1.0.3", "@types/joi": "^17.2.3", "@types/lodash": "^4.14.195", "@types/restify-errors": "^4.3.5", "assert-plus": "^1.0.0", "enverify": "^1.0.2", "eslint": "^8.57.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "madge": "^7.0.0", "nodemon": "^3.1.4", "tunnel-ssh": "^5.1.2", "vitest": "^1.5.3"}, "overrides": {"csv": "6.3.3", "flat": "^5.0.2"}}