const Consumer = require('sqs-consumer');
const AWS = require('aws-sdk');
const co = require('co');
const _ = require('lodash');

const env = require('../env');
const { server } = require('../app');
const {
  sendEventProcessDevicesChange,
} = require('../helpers/process-device-change-events');

function* updateDeviceRecord(device, destinationSite, updateFields) {
  let updateQuery = `
      UPDATE target
      SET site_id = $1,
          password = $2,
          last_registered = $3,
          last_contact = $4,
          registration_key = $5,
          name = $6,
          description = $7,
          registration_window_end = $8,
          key_group_ref = $9,
          key_bundle_count = $10,
          certificate = $11,
          mac_address = $12,
          data = $13,
          status = $14,
          created = $15,
          presence = $16,
          last_edited_date = NOW(),
          is_json_certificates = $18
      WHERE serial_number = $17 AND active = TRUE
    `;

  const registrationWindowEnd = new Date();
  registrationWindowEnd.setFullYear(registrationWindowEnd.getFullYear() + 20);

  device.registrationWindowEnd = registrationWindowEnd; // eslint-disable-line

  let deviceValues = [
    destinationSite,
    null,
    null,
    null,
    device.registrationKey,
    device.name,
    device.description,
    device.registrationWindowEnd,
    device.keyGroupRef,
    device.keyBundleCount,
    device.certificate,
    device.macAddress,
    device.data,
    0,
    device.created,
    'PRESENT',
    device.serialNumber,
    device.isJsonCertificates,
  ];

  if (
    device.presence === 'UPDATE' &&
    updateFields &&
    updateFields.StringValue
  ) {
    const updateFieldsArr = updateFields.StringValue.split(',').map(elem =>
      elem.trim()
    );
    deviceValues = [];
    let set = 'SET ';

    updateFieldsArr.forEach((field, index) => {
      set += `${_.snakeCase(field)} = $${index + 1}`;
      if (index !== updateFieldsArr.length - 1) {
        set += ', ';
      }
      deviceValues.push(device[field]);
    });

    deviceValues.push(device.serialNumber);

    updateQuery = `
          UPDATE target
          ${set}
          WHERE serial_number = $${deviceValues.length} AND active = TRUE;
          `;
  }

  yield server.db.write.execute(updateQuery, deviceValues);
}

function* insertDeviceRecord(device, destinationSite) {
  if (!device.deviceType && device.productTypeId) {
    switch (device.productTypeId) {
      case 6:
        device.deviceType = 'G6-200'; //eslint-disable-line
        break;
      case 9:
        device.deviceType = 'G7-100'; //eslint-disable-line
        break;
      default:
        break;
    }
  }

  const insertQuery = `
      INSERT INTO target(
        site_id,
        password,
        version,
        last_registered,
        last_contact,
        priority_order,
        registration_key,
        name,
        description,
        serial_number,
        active,
        registration_window_end,
        key_group_ref,
        key_bundle_count,
        certificate,
        is_json_certificates,
        mac_address,
        data,
        status,
        created,
        presence,
        device_type
      )
      VALUES( $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22 )
    `;

  const registrationWindowEnd = new Date();
  registrationWindowEnd.setFullYear(registrationWindowEnd.getFullYear() + 20);

  device.registrationWindowEnd = registrationWindowEnd; // eslint-disable-line

  const deviceValues = [
    destinationSite,
    null,
    device.version,
    null,
    null,
    device.priorityOrder,
    device.registrationKey,
    device.name,
    device.description,
    device.serialNumber,
    true,
    device.registrationWindowEnd,
    device.keyGroupRef,
    device.keyBundleCount,
    device.certificate,
    device.isJsonCertificates,
    device.macAddress,
    device.data,
    0,
    device.created,
    'PRESENT',
    device.deviceType,
  ];

  yield server.db.write.execute(insertQuery, deviceValues);
}

function handleMessage(message, done) {
  return co(function* execute() {
    if (
      !message ||
      !message.MessageAttributes ||
      message.MessageAttributes.type.StringValue !== 'DEVICE_IMPORT' ||
      message.MessageAttributes.destinationSite.StringValue.length < 0
    ) {
      return done(new Error('No device to import'));
    }

    const deviceToImport = JSON.parse(message.Body);
    const destinationSite =
      message.MessageAttributes.destinationSite.StringValue;

    const activeSite = yield server.db.read.row(
      `
            SELECT *
            FROM site
            WHERE active = TRUE
              AND site_id = $1
          `,
      [destinationSite]
    );

    if (!activeSite) {
      return done(new Error('Failed to import devices'));
    }

    const existingRecord = yield server.db.read.row(
      `
          SELECT *
          FROM target
          WHERE serial_number = $1 AND active = TRUE
        ;`,
      [deviceToImport.serialNumber.toString()]
    );

    try {
      if (!existingRecord) {
        // Insert Record
        yield insertDeviceRecord(deviceToImport, destinationSite);
      } else {
        // Update Record
        yield updateDeviceRecord(
          deviceToImport,
          destinationSite,
          message.MessageAttributes.updateFields
        );
        if (deviceToImport.siteId !== existingRecord.siteId)
          yield sendEventProcessDevicesChange([existingRecord.targetId]);
      }

      // Device `presence` is updated to 'PRESENT', during the above update/insert
    } catch (err) {
      server.log.error('Failed to import device');
      throw err;
    }

    // SQS message deleted automatically, on calling done
    return done();
  }).catch(err => {
    server.log.error(err);
    return done(new Error('Failed to import device'));
  });
}

const deviceSyncConsumer = Consumer.create({
  queueUrl: env.config.sync.sourceQueueUrl,
  sqs: new AWS.SQS({ region: env.config.AWS.sqs.region }),
  messageAttributeNames: ['type', 'destinationSite', 'updateFields'],
  handleMessage,
});

deviceSyncConsumer.on('error', err => {
  server.log.error(
    'Error when trying to get messages from devices SQS: ',
    err.message
  );
});

function init() {
  if (process.env.ENVIRONMENT === 'local') {
    return;
  }
  deviceSyncConsumer.start();
}

module.exports = {
  init,
  handleMessage,
};
