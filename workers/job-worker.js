/* eslint-disable no-await-in-loop */
const Consumer = require('sqs-consumer');
const AWS = require('aws-sdk');
const uuid = require('uuid/v4');
const uuidValidate = require('uuid-validate');

const { server } = require('../app');
const env = require('../env');
const JobHelper = require('../src/rki/lib/job-helper');
const { KEY_REQUEST_SESSION_STATUS } = require('../helpers/key-request-helper');
const logger = require('../lib/logger').mainLogger();

const jobHelper = new JobHelper(server.db);

function validateMessageAttribute(MessageAttributes, attributeName) {
  if (!MessageAttributes[attributeName]) {
    throw new Error(
      ` [bulk-operation-job-worker] Missing required attribute ${attributeName}`
    );
  }

  return true;
}

function validateRKIMessage(message) {
  let rkiInfo = null;
  try {
    const { Body } = message;

    if (!Body || Object.entries(Body).length === 0) {
      throw new Error('Invalid message format');
    }

    const { requestContext, responsePayload, responseContext, requestPayload } =
      JSON.parse(Body);

    if (requestContext && responseContext) {
      const { condition, requestId } = requestContext;
      const { serial, group, sessionId, bundle, errorType, errorMessage } =
        responsePayload;
      const { keyRequestDeviceId } = requestPayload;

      rkiInfo = responsePayload;
      rkiInfo.keyRequestDeviceId = keyRequestDeviceId;

      if (condition === 'Success' && serial && group && sessionId && bundle) {
        // Success Message Example
        // {
        //     version: '1.0',
        //     timestamp: '2020-04-16T23:21:33.578Z',
        //     requestContext: {
        //         requestId: '8e887f6c-aca7-4742-b1e2-29236bad11dc',
        //         functionArn: 'arn:aws:lambda:ap-southeast-1:361855776854:function:test-rki-service:$LATEST',
        //         condition: 'Success',
        //         approximateInvokeCount: 1
        // },
        //     requestPayload: {
        //         certificate: 'MN',
        //             group: 'Invenco-Group2'
        //             keyRequestDeviceId: 'b8473737-ad78-408b-bec7-5e2ad9e08855'
        //     },
        //     responseContext: {
        //         statusCode: 200,
        //             executedVersion: '$LATEST'
        //     },
        //     responsePayload: rkiBody
        // }
        validateMessageAttribute(rkiInfo, 'serial');
        validateMessageAttribute(rkiInfo, 'group');
        validateMessageAttribute(rkiInfo, 'sessionId');
        validateMessageAttribute(rkiInfo, 'bundle');
        rkiInfo.isRetrieved = true;
      } else if (errorType === 'Error' || errorMessage) {
        // Failed Message Example
        // {
        //     version: '1.0',
        //     timestamp: '2020-04-16T23:28:21.360Z',
        //     requestContext: {
        //         requestId: '43988dbf-6d5a-4bab-8cad-0eb3ef74fb92',
        //         functionArn: 'arn:aws:lambda:ap-southeast-1:361855776854:function:test-rki-service:$LATEST',
        //         condition: 'RetriesExhausted',
        //         approximateInvokeCount: 3
        // },
        //     requestPayload: {
        //         certificate: 'ABC',
        //             group,
        //             keyRequestDeviceId: 'b8473737-ad78-408b-bec7-5e2ad9e08855'
        //     },
        //     responseContext: {
        //         statusCode: 200,
        //             executedVersion: '$LATEST',
        //             functionError: 'Unhandled'
        //     },
        //     responsePayload: {
        //         errorType: 'Error',
        //             errorMessage: 'Certificate was not provided in event object.',
        //             trace: [
        //             'Error: Certificate was not provided in event object.',
        //             '    at Runtime.t.handler (/var/task/index.js:8:58468)'
        //         ]
        //     }
        // }
        rkiInfo.isRetrieved = false;
      } else if (!serial) {
        throw Error(
          `rki message ID: ${requestId}, key request device ID: ${keyRequestDeviceId} is without serial`
        );
      } else if (!group) {
        throw Error(
          `rki message ID: ${requestId}, key request device ID: ${keyRequestDeviceId} is without group`
        );
      } else if (!sessionId) {
        throw Error(
          `rki message ID: ${requestId}, key request device ID: ${keyRequestDeviceId} is without sessionId`
        );
      } else if (!bundle) {
        throw Error(
          `rki message ID: ${requestId}, key request device ID: ${keyRequestDeviceId} is without bundle`
        );
      } else {
        throw Error(
          `rki message ID: ${requestId}, key request device ID: ${keyRequestDeviceId} is without either Success or Error`
        );
      }
    }
  } catch (err) {
    logger.error(`Not a valid rki message: ${err}`);
    return false;
  }

  return rkiInfo;
}

async function handleRKIJobMessage(keyRequestDeviceId, bundle, sessionId) {
  try {
    const keyRequestDevice = await jobHelper.getKeyRequestDeviceById(
      keyRequestDeviceId,
      KEY_REQUEST_SESSION_STATUS.IN_PROGRESS
    );
    const { certs, keys, timestamps } = bundle;
    const data = {
      messageVersion: '1',
      certs,
      keys,
    };
    const timestamp = new Date(timestamps[0]);

    await jobHelper.handleRKIJobCreation(
      keyRequestDevice,
      data,
      sessionId,
      timestamp
    );
  } catch (err) {
    logger.error(`Failed to create job: ${err}`);
    throw err;
  }
}

async function handleKeyRetrieveFailure(keyRequestDeviceId, errorMessage) {
  try {
    const keyRequestDevice = await jobHelper.getKeyRequestDeviceById(
      keyRequestDeviceId,
      KEY_REQUEST_SESSION_STATUS.IN_PROGRESS
    );
    await jobHelper.handleKeyRetrieveFailure(keyRequestDevice, errorMessage);
  } catch (err) {
    logger.error(`Failed to create job: ${err}`);
    throw err;
  }
}

async function handleMessage(message, done) {
  const rkiInfo = validateRKIMessage(message);
  if (rkiInfo) {
    try {
      const { isRetrieved } = rkiInfo;

      if (isRetrieved) {
        const { keyRequestDeviceId, bundle, sessionId } = rkiInfo;

        logger.info(
          { rkiInfo },
          `[JobWorker].[HandleMessage] Starting to handleRKIJobMessage.`
        );
        await handleRKIJobMessage(keyRequestDeviceId, bundle, sessionId);
      } else {
        const { keyRequestDeviceId, errorMessage } = rkiInfo;

        logger.info(
          { rkiInfo },
          `[JobWorker].[HandleMessage] Starting to handleKeyRetrieveFailure.`
        );
        await handleKeyRetrieveFailure(keyRequestDeviceId, errorMessage);
      }
    } catch (err) {
      logger.error(
        { error: err },
        `[JobWorker].[HandleMessage] Failed to process RKI message: ${err.message}.`
      );
      return done(new Error('Failed to process RKI message'));
    }

    return done();
  }

  if (!message || !message.MessageAttributes) {
    return done(
      new Error(
        `[bulk-operation-job-worker] Invalid message format : ${JSON.stringify(message.MessageAttributes)}`
      )
    );
  }

  const { MessageAttributes, Body } = message;
  logger.info(
    { messageAttributes: MessageAttributes },
    `[bulk-operation-job-worker] [JobWorker].[HandleMessage] Handling SQS Message.`
  );

  // make sure required attributes are present,
  // and make sure new attributes are added to the messageAttributeNames[] array in Consumer.create() function below
  try {
    validateMessageAttribute(MessageAttributes, 'device-id');
    validateMessageAttribute(MessageAttributes, 'user-id');
    validateMessageAttribute(MessageAttributes, 'destination');
    validateMessageAttribute(MessageAttributes, 'type');
  } catch (err) {
    logger.error(
      `[bulk-operation-job-worker] bulk operation message error: ${err}`
    );
    return done(err);
  }

  // required attributes
  const deviceId = MessageAttributes['device-id'].StringValue;
  const userId = MessageAttributes['user-id'].StringValue;

  if (!uuidValidate(userId)) {
    return done(new Error('[bulk-operation-job-worker] Invalid user id'));
  }

  const destination = MessageAttributes.destination.StringValue;
  const type = MessageAttributes.type.StringValue;

  // optional attributes
  const dependencies = MessageAttributes.dependencies
    ? MessageAttributes.dependencies.StringValue
    : [];
  const embargo = MessageAttributes.embargo
    ? MessageAttributes.embargo.StringValue
    : new Date(Date.now()).toISOString();

  let expiry = MessageAttributes.expiry
    ? MessageAttributes.expiry.StringValue
    : null;
  if (!expiry) {
    const expiryDate = new Date();
    expiryDate.setHours(expiryDate.getHours() + 2);
    expiry = expiryDate.toISOString();
  } else {
    expiry = new Date(parseInt(expiry, 10)).toISOString();
  }
  const bulkOperationId = MessageAttributes['bulk-operation-id']
    ? MessageAttributes['bulk-operation-id'].StringValue
    : null;

  try {
    // verify the user exists
    const jobInitiator = await server.db.read.row(
      `
            SELECT id, full_name as name, type FROM ics_user WHERE id = $1
        `,
      [userId]
    );

    if (!jobInitiator) {
      return done(
        new Error(`[bulk-operation-job-worker] Cannot find user ${userId}`)
      );
    }

    // verify that the user can access the device
    // System Users are excempt from this rule
    let device;
    if (jobInitiator.type !== 'SYSTEM') {
      device = await server.db.read.row(
        `
                SELECT
                    d.target_id as device_id
                FROM target d
                JOIN user_site_authorization usa ON d.site_id = usa.site_id
                WHERE d.target_id = $1 AND d.active IS true and usa.user_id = $2
            `,
        [deviceId, userId]
      );
    } else {
      device = await server.db.read.row(
        `
                SELECT
                    d.target_id as device_id
                FROM target d
                WHERE d.target_id = $1 AND d.active
            `,
        [deviceId]
      );
    }

    if (!device) {
      logger.error(
        `[bulk-operation-job-worker] Device with id: ${deviceId} not accessible by user: ${userId}`
      );
      return done(
        new Error(
          `[bulk-operation-job-worker] Device with id: ${deviceId} not found`
        )
      );
    }

    let job;
    const connection = await server.db.write.getConnection();

    try {
      await connection.execute('BEGIN');

      const id = uuid();
      const jobResult = await connection.execute(
        `
                INSERT INTO job
                    ( id, device_id, destination, type, data, status, embargo, expiry, created_by, created_on )
                VALUES ( $1, $2, $3, $4, $5, $6, $7, $8, $9, now() )
                RETURNING id, device_id, destination, type, data, status, embargo, expiry as expiration, created_by, created_on;
            `,
        [id, deviceId, destination, type, Body, 0, embargo, expiry, userId]
      );

      const { 0: rowZero } = jobResult.rows;
      job = rowZero;

      if (!job) {
        logger.error(
          `[bulk-operation-job-worker] unable to create a job for message:${JSON.stringify(MessageAttributes)} `
        );
      } else {
        logger.info(
          `[bulk-operation-job-worker] Job id for bulk operation message:${JSON.stringify(MessageAttributes)} is: ${job.id}`
        );
      }

      // Insert into job status history
      await connection.execute(
        `
                INSERT INTO job_status_history (job_id, status, message, created_at)
                VALUES ($1, $2, $3, NOW())
            `,
        [id, 0, 'new job']
      );

      const dependenciesCreated = [];
      if (dependencies && dependencies.length > 0) {
        // eslint-disable-next-line no-restricted-syntax
        for (const dependency of dependencies) {
          const jobDepended = (
            await connection.execute(
              `
                        SELECT j FROM job j WHERE j.id =$1
                    `,
              [dependency.jobId]
            )
          ).rows[0];

          if (!jobDepended) {
            throw new Error(`Job ${dependency.jobId} not found`);
          }

          const jobDependencyResult = (
            await connection.execute(
              `
                        INSERT INTO job_dependency
                            (job_id, dependens_on, continue_on_fail)
                        VALUES ($1, $2, $3)
                        RETURNING dependens_on AS job_id, continue_on_fail AS run_on_fail;
                    `,
              [job.id, dependency.jobId, dependency.runOnFail]
            )
          ).rows[0];

          dependenciesCreated.push(jobDependencyResult);
        }
      }

      if (
        type === 'sys.upload' &&
        /fileuploadrequests\/(.*)\/upload/g.test(Body)
      ) {
        const fileUploadId = /fileuploadrequests\/(.*)\/upload/g.exec(Body)[1];
        // file-uploads have an extra relation table, that needs to be inserted to
        await connection.execute(
          `
                    INSERT INTO file_upload_job (job_id, request_id)
                    VALUES ($1, $2)
                `,
          [job.id, fileUploadId]
        );
      }

      // INSERT INTO bulk_operation_item table
      if (bulkOperationId) {
        logger.info(
          `[bulk-operation-job-worker] Job worker processing bulkOperationRecord: ${bulkOperationId} w/ job_id: ${job.id}`
        );
        const bulkOperationRecord = (
          await connection.execute(
            `
                    SELECT bo FROM bulk_operation bo WHERE bo.id=$1
                `,
            [bulkOperationId]
          )
        ).rows[0];

        if (!bulkOperationRecord) {
          throw new Error(
            ` [bulk-operation-job-worker] Bulk operation record with id: ${bulkOperationId} not found for device id : ${deviceId}`
          );
        }

        // update the bulk_operation_item table to assign the job_id
        const updateBulkOperationItemRes = await connection.execute(
          `
                    UPDATE bulk_operation_item
                    SET
                        date_updated = NOW(),
                        job_id = $1
                    WHERE
                        bulk_operation_id = $2
                        AND device_id = $3;
                `,
          [job.id, bulkOperationId, deviceId]
        );

        if (!updateBulkOperationItemRes) {
          throw new Error(
            `[bulk-operation-job-worker] Bulk operation Item with bulk Operation id : ${bulkOperationId} and device id : ${deviceId} not found`
          );
        }
      }

      await connection.execute('COMMIT');
    } catch (err) {
      logger.error(
        `[bulk-operation-job-worker] Rolling back for bulkOperationId: ${bulkOperationId} for device Id: ${deviceId} job id: ${job.id} with error ${err.message}`
      );
      await connection.execute('ROLLBACK');
      throw err;
    } finally {
      connection.done();
    }

    logger.info(
      `Job ${job.id} was created by ${jobInitiator.id}, ${jobInitiator.name}`
    );
    return done();
  } catch (err) {
    logger.error(`[bulk-operation-job-worker] Failed to create job: ${err}`);
    return done(new Error('[bulk-operation-job-worker] Failed to create job'));
  }
}

const jobCreatorConsumer = Consumer.create({
  queueUrl: env.config.jobs.queue.url,
  sqs: new AWS.SQS({ region: env.config.jobs.queue.region }),
  // Append new MessageAttribute properties here
  messageAttributeNames: [
    'type',
    'device-id',
    'user-id',
    'destination',
    'expiry',
    'embargo',
    'dependencies',
    'bulk-operation-id',
  ],
  handleMessage,
});

jobCreatorConsumer.on('error', err => {
  logger.error(
    '[bulk-operation-job-worker] Error when trying to get messages from jobs SQS: ',
    err.message
  );
});

jobCreatorConsumer.on('processing_error', err => {
  logger.error(
    '[bulk-operation-job-worker] Error processing message from SQS: ',
    err.message
  );
});

function init() {
  jobCreatorConsumer.start();
}

module.exports = {
  init,
  handleMessage,
};

module.exports.private = {
  validateRKIMessage,
};
