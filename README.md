# ICS Node.js Backend

## Unit Test Coverage

| Statements                                                                        | Branches                                                                      | Functions                                                                       | Lines                                                                   |
| --------------------------------------------------------------------------------- | ----------------------------------------------------------------------------- | ------------------------------------------------------------------------------- | ----------------------------------------------------------------------- |
| ![Statements](https://img.shields.io/badge/statements-1.54%25-red.svg?style=flat) | ![Branches](https://img.shields.io/badge/branches-7.69%25-red.svg?style=flat) | ![Functions](https://img.shields.io/badge/functions-2.66%25-red.svg?style=flat) | ![Lines](https://img.shields.io/badge/lines-1.54%25-red.svg?style=flat) |

## Run Locally

### Dependencies

- nvm: [Installation guide](https://github.com/nvm-sh/nvm#installing-and-updating)
- ics-db: [Repository link](https://github.com/Invenco-Cloud-Systems-ICS/db)

<br/><hr/>

### Install Node.js modules

```sh
nvm install
nvm use
npm install
npx husky init
```

<br/><hr/>

### Run locally

- Create local config under build/config/local/ - config.json and key.pem
  - The key.pem can be copied over from the unit-test config, config.json can likewise be copied over but will need some values adjusted.
  - See [api-nodejs setup guide](https://conf.invenco.com/display/ICSInternal/Setting+up+your+ICS+local+environment#SettingupyourICSlocalenvironment-api-nodejs) for further details.

Then when the above has been setup,

Run the following for local development:

```sh
npm run start-dev
```

Or the following for debug mode:

```sh
npm run debug
```

<br/><hr/>

### Troubleshooting MFA failure when running api-nodejs locally on WSL, Windows PC

- When you get MFA code authentication failure, its likely due to WSL system date is out of sync .
- check that the system date in the container match the real current date and time.
- if out of sync then you will need to update the WSL date and time.
- Reason
  - WSL container date time can become out of sync when your Window PC goes to sleep
- Resolution
  - update WSL system date

```sh
sudo hwclock -s
```

- OR
- Restart WSL container by using Windows Powershell, shutdown and start wsl again

```sh
wsl --shutdown
wsl
```

<br/><hr/>

### Run unit tests

- Note that currently the unit test config uses the connection string `postgresql://postgres@postgres/postgres`
  - This is pipeline-focused, referring to a docker service container address. If running postgresql natively, you will likely want to change this.
  - Refer to [api-nodejs setup guide](https://conf.invenco.com/display/ICSInternal/Setting+up+your+ICS+local+environment#SettingupyourICSlocalenvironment-api-nodejs) for further details.

Then when everything mentioned has been setup, run the following:

```sh
npm run test
```

If you encounter a missing aws credentials error when running tests, either login using the aws console or create fake credentials in
you wls home

```sh
mkdir ~/.aws
touch ~/.aws/credentials
```

inside the credentials file add this

```
[default]
aws_access_key_id = 123
aws_secret_access_key = ABC
```

<br/><hr/>

### Note

- `resources` directory should include all non-JS/JSON files require for runtime.

<br/><hr/>

### Local SSH Tunnel

- There is a configurable SSH tunnel (only) for local development if you wish to point your local API at a remote DB and you are not on the company network. It acheives the same as using PuTTY/SSH command line, but with the convenience of it being baked in to the application and working transparently (your db read/write connection strings still point to your end destination, you don't have to explicitly point them at your tunnel entrance.)
- You will need to add the following 'dbSshTunnel' config section to your local config.json, a copy of this can be seen in the unit-test/config.json, or you can copy from below
- You will need to set enabled to true, and update userName, privateKeyPath, and privateKeyPassphrase. These 3 values would be the same as what you use for PuTTY/SSH command line when connecting to bastion jump box.
- If your private key doesn't seem to work, you may have to re-export it in the older OpenSSH format with PuTTYgen.

```json
{
  ...
  "dbSshTunnel":{
      "enabled": true,
      "host": "************",
      "port": 22,
      "userName": "{ssh username}",
      "privateKeyPath" : "{path to ssh private key}",
      "tunnelAddress": "127.0.0.1",
      "writeTunnelPort": 4320,
      "readTunnelPort": 4321,
      "autoClose": false,
      "privateKeyPassphrase": "{passphrase for ssh private key}"
    }
  ...
}
```

<hr/>

### How do I debug in VS Code?

- Add Command to package.json:
  Add the following command inside the scripts block of your package.json file:

  ```json
  "start-dev-debug": "env ENVIRONMENT=local nodemon --exec 'node --inspect --require ts-node/register index.ts'"
  ```

- Generate launch.json in VS Code:
  In VS Code, click on the "Debug" tab to generate a launch.json file.Change the launch.json to the below code

- Update launch.json:
  Modify the launch.json file with the following configuration:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Launch Program",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/index.ts"
    }
  ]
}
```

- Run the Debug Script:
  Click on Debug icon in VScode editor and the select Run Script : start-dev-debug from the dropdown.

- Set Breakpoints and Debug:
  Place breakpoints as needed in your code, then hit the API. The debugger should stop at your breakpoints, allowing you to inspect the code.
