version: '1'
project:
  name: ${scm.git.repo}
  branch: ${scm.git.branch}
  revision:
    name: ${scm.git.commit}
    date: ${scm.git.commit.date}
capture:
  fileSystem:
    ears:
      extensions: [ear]
      files:
        - directory: ${project.projectDir}
        - excludeRegex: node_modules|bower_components|vendor
        - excludeRegex: '^test/.*'
        - excludeRegex: ".*\\.spec\\.js$"
    java:
      files:
        - directory: ${project.projectDir}
        - excludeRegex: node_modules|bower_components|vendor
        - excludeRegex: '^test/.*'
        - excludeRegex: ".*\\.spec\\.js$"
    javascript:
      files:
        - directory: ${project.projectDir}
        - excludeRegex: node_modules|bower_components|vendor
        - excludeRegex: '^test/.*'
        - excludeRegex: ".*\\.spec\\.js$"
    php:
      files:
        - directory: ${project.projectDir}
        - excludeRegex: node_modules|bower_components|vendor
        - excludeRegex: '^test/.*'
        - excludeRegex: ".*\\.spec\\.js$"
    python:
      files:
        - directory: ${project.projectDir}
        - excludeRegex: node_modules|bower_components|vendor
        - excludeRegex: '^test/.*'
        - excludeRegex: ".*\\.spec\\.js$"
    ruby:
      files:
        - directory: ${project.projectDir}
        - excludeRegex: node_modules|bower_components|vendor
        - excludeRegex: '^test/.*'
        - excludeRegex: ".*\\.spec\\.js$"
    typescript:
      files:
        - directory: ${project.projectDir}
        - excludeRegex: node_modules|bower_components|vendor
        - excludeRegex: '^test/.*'
        - excludeRegex: ".*\\.spec\\.js$"
    wars:
      extensions: [war]
      files:
        - directory: ${project.projectDir}
        - excludeRegex: node_modules|bower_components|vendor
        - excludeRegex: '^test/.*'
        - excludeRegex: ".*\\.spec\\.js$"
analyze:
  mode: central
install:
  coverity:
    version: default
serverUrl: https://vontier-invenco.polaris.synopsys.com
