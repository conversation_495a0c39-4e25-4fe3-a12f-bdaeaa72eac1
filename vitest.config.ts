/* eslint-disable import/no-extraneous-dependencies */
/// <reference types="vitest" />

const { defineConfig } = require('vitest/config');
const icsVitestconfig = require('@invenco-cloud-systems-ics/vitest-config');

const excludes = [
  '**/node_modules/**',
  '**/dist/**',
  '**/target/**',
  '**/build/**',
  './.husky/**',
  './.github/**',
];

module.exports = defineConfig({
  ...icsVitestconfig,
  test: {
    ...icsVitestconfig.test,
    exclude: excludes,
    coverage: {
      ...icsVitestconfig.test.coverage,
      exclude: excludes,
    },
  },
});
