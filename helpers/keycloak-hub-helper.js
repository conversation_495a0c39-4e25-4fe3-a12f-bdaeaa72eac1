const axios = require('axios');
const logger = require('../lib/logger').mainLogger();
const { config } = require('../env');

const getUserInfo = async token => {
  const url = `${config.ssoHub.authServerUrl}/auth/realms/${config.ssoHub.realm}/account`;
  try {
    const response = await axios.get(url, {
      headers: {
        Authorization: `bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    logger.error(
      `[keycloak-hub-helper].[getUserInfo] Keycloak Token verification failed %s`,
      error
    );
    return null;
  }
};

const getHubAuthToken = async () => {
  if (!config.ssoHub) return null;

  const url = `${config.ssoHub.authServerUrl}/auth/realms/${config.ssoHub.realm}/protocol/openid-connect/token`;
  const params = new URLSearchParams({
    grant_type: 'password',
    scope: 'openid',
    client_secret: config.ssoHub.client_key,
    client_id: config.ssoHub.client_id,
    username: config.ssoHub.username,
    password: config.ssoHub.password,
  });

  try {
    const response = await axios.post(url, params, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
    return response.data.access_token || null;
  } catch (error) {
    logger.error(
      `[keycloak-hub-helper].[getHubAuthToken] Failed to retrieve Hub auth token: %s`,
      error.message
    );
    return null;
  }
};

const decryptToken = async (token, authToken) => {
  const url = config.ssoHub.decryptUrl;
  const payload = { token };

  try {
    const response = await axios.post(url, payload, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${authToken}`,
      },
    });
    return response.data.token || null;
  } catch (error) {
    logger.error(
      `[keycloak-hub-helper].[decryptToken] Failed to retrieve Decrypted token of: ${token} %s`,
      error.message
    );
    return null;
  }
};

module.exports = {
  getUserInfo,
  getHubAuthToken,
  decryptToken,
};
