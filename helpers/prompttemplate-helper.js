const { server } = require('../app');

module.exports = {
  getPromptTemplateStates: (promptTemplateId, companyId) => {
    const query = `
            SELECT
                ps.id,
                ps.code,
                ps.description,
                ps.secure,
                ps.numeric_input As "numericInput",
                ps.dynamic_text AS "dynamicText",
                ps.sequence,
                ps.example_text,
                ps.prompt_type,
                ps.attribs,
                ps.width,
                ps.font_size
            FROM
                prompt_state ps
            JOIN
                company_prompt_template cpt
            ON
                ps.prompt_template = cpt.prompt_template
            WHERE
                ps.prompt_template = $1 and cpt.company = $2
            ORDER BY
                ps.sequence;
        `;
    return server.db.read.rows(query, [promptTemplateId, companyId]);
  },
};
