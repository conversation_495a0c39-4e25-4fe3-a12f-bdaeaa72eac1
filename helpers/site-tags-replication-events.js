const AWS = require('../lib/aws');
const env = require('../env');
const { eventBusSources } = require('../lib/app-constants');
const logger = require('../lib/logger').mainLogger();

const isSiteTagReplication = () =>
  env.config.AWS.eventBus && env.config.AWS.eventBus.fuelPriceService;

const getSiteTags = async (connection, siteId) =>
  // eslint-disable-next-line no-return-await
  await connection.execute(
    `
    SELECT t.id as ics_tag_id, t.name as tag_name, st.created_date 
    FROM site_tag st
    INNER JOIN tag t ON t.id = st.tag_id
      WHERE st.site_id = $1 AND
            st.deleted = false
    `,
    [siteId]
  );

const sendEventOnUpdateSiteTags = async (connection, siteId) => {
  if (!isSiteTagReplication()) return;
  const results = await getSiteTags(connection, siteId);
  const { rows: siteTags } = results;
  logger.info(
    { siteTags, env: env.config.AWS.eventBus },
    '[site-tags-replication].sendEventOnUpdateSiteTags.start'
  );

  if (siteTags) {
    const eventBridgeParams = {
      data: { siteId, siteTags, updatedDate: new Date().toISOString() },
      detailType:
        env.config.AWS.eventBus.fuelPriceService.rules.updateSiteTags ||
        'fps_update_site_tags',
      source: eventBusSources.SITE_TAGS_REPLICATION,
    };

    const res = await AWS.sendEventsToEventBridge(eventBridgeParams);
    logger.info(
      { eventResponse: res },
      '[site-tags-replication].sendEventOnUpdateSiteTags.completed'
    );
  }
};

module.exports = {
  sendEventOnUpdateSiteTags,
};
