module.exports = config => {
  if (!config.releaseVersion) {
    throw new Error('No release version information found in config file');
  }

  // Create component version vs release number map
  // Format '{device-type}.{state}.{value}' = '{release-number}'
  // Example 'G6-200.invenco.system.g6opt.sdu-ver-app.02.20.0054' = 'R2.2.3'
  const releaseVersionMap = config.releaseVersion.reduce(
    (map, obj) => ({
      ...map,
      ...Object.keys(obj.versionMap).reduce(
        (map2, key) => ({
          ...map2,
          [`${obj.deviceType}.${obj.state}.${key}`]: obj.versionMap[key],
        }),
        {}
      ),
    }),
    {}
  );

  return {
    getReleaseVersion: device => {
      let result;

      if (device.platformVersion) {
        result = device.platformVersion;
      } else {
        result =
          releaseVersionMap[
            `${device.deviceType.id}.${device.releaseVersionKey}.${device.releaseVersion}`
          ] || null;
      }

      delete device.releaseVersionKey; // eslint-disable-line no-param-reassign
      delete device.platformVersion; // eslint-disable-line no-param-reassign

      return result;
    },
    getReleaseVersionFromStates(deviceStates, deviceType) {
      if (!deviceStates) {
        return null;
      }

      let result =
        deviceStates['invenco.system.g6opt.opt-platform-ver'] ||
        deviceStates['invenco.system.g7opt.opt-platform-ver'];

      if (!result) {
        const ver = config.releaseVersion.find(
          version => deviceStates[version.state]
        );

        result = ver
          ? releaseVersionMap[
              `${deviceType}.${ver.state}.${deviceStates[ver.state]}`
            ]
          : null;
      }

      return result;
    },
  };
};
