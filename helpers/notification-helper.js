const rp = require('request-promise');

const { server } = require('../app');
const { config } = require('../env');
const logger = require('../lib/logger').mainLogger();

module.exports = {
  send: (req, notifications) => {
    // Send to java notification service
    const options = {
      uri: config.NOTIFICATION.url,
      headers: { Authorization: req.headers.authorization },
      method: 'POST',
      body: notifications,
    };
    return rp(options);
  },
  createUINotification: async (
    userId,
    message,
    relatedEntity,
    type,
    level = 'INFO'
  ) => {
    try {
      await server.db.write.execute(
        `
            INSERT INTO notification
            ( id, user_id, created, read, message, related_entity, type, level )
            VALUES
            ( uuid_generate_v1mc(), $1, NOW(), false, $2, $3, $4, $5 )`,
        [userId, message, relatedEntity, type, level]
      );

      return true;
    } catch (e) {
      logger.error('notificationHelper.createNotifications() error: %s', e);
      return false;
    }
  },
};
