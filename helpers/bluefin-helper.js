const { server } = require('../app');
const { config } = require('../env');
const logger = require('../lib/logger').mainLogger();
const { getValueJSON } = require('./db-config-helper');
const { pushBluefinKafkaEvents } = require('./kafkaStreamHelper');
const countryCodeMap = require('./country-code.json');

const activateEventCheck = (action, device) =>
  action === 'moveToSite' &&
  device.fromDefaultSite &&
  device.currentDeviceCompany === device.companyId &&
  device.bluefinConfigData?.createDevice === '1';

const createEventCheck = (action, device) =>
  action === 'moveToCompany' &&
  device.companyId !== device.currentDeviceCompany &&
  device.fromDefaultSite &&
  device.toDefaultSite === device.toSiteId;

const storeEventCheck = (action, device) =>
  action === 'moveToSite' &&
  device.toDefaultSite === device.toSiteId &&
  device.currentDeviceCompany === device.companyId &&
  device.bluefinConfigData?.activateDevice === '1';

const parseDeviceBuild = bluefinConfigData => {
  if (bluefinConfigData && bluefinConfigData.deviceBuild) {
    try {
      return JSON.parse(bluefinConfigData.deviceBuild);
    } catch (error) {
      logger.error(
        `[bluefin-helper].[parseDeviceBuild] error parsing device build: %s`,
        error
      );
    }
  }
  return null;
};

const createPayload = async (device, action, defaultConfig) => {
  const payload = {
    id: device.id.toString(),
    ts: Date.now(),
    data: {
      serialNumber: device.serialNumber,
      deviceType: defaultConfig.deviceType,
      injectKey: defaultConfig.injectKey,
      partner: defaultConfig.partner,
      location: {
        id: device.locationId,
        name: device.siteName,
      },
    },
  };

  if (device.currentDeviceCompany !== device.companyId)
    action = 'moveToCompany'; // eslint-disable-line no-param-reassign

  if (createEventCheck(action, device)) {
    const deviceBuild = parseDeviceBuild(device.bluefinConfigData);
    payload.type = 'createDevice';
    payload.data.kif = device.kif.kif;
    payload.data.client = defaultConfig.client;
    payload.data.deviceBuild = deviceBuild || defaultConfig.deviceBuild[0];
  } else if (activateEventCheck(action, device)) {
    payload.type = 'activateDevice';
  } else if (storeEventCheck(action, device)) {
    payload.type = 'storeDevice';
  } else {
    return null;
  }

  return payload;
};

const fetchDefaultBluefinConfig = async () => getValueJSON('bluefin_config');

const fetchBluefinConfig = async data => {
  const { id, key } = data;
  const params = [id];
  let whereClause = '';

  if (key) {
    whereClause = ` AND bc.key = $2`;
    params.push(key);
  }

  const query = `
    SELECT json_object_agg(bc.key, bc.value) as data FROM bluefin_config bc
    WHERE bc.target_id = $1
    ${whereClause};
  `;

  try {
    const result = await server.db.read.row(query, params);
    return result;
  } catch (error) {
    logger.error(
      '[bluefin-helper].[fetchBluefinConfig] Error fetching Bluefin default values: %s',
      error
    );
    return null;
  }
};

const updateBluefinConfig = async configArray => {
  const finalQueries = configArray.flatMap(({ id, key, value }) => {
    const baseQuery = [`(${id}, '${key}', '${JSON.stringify(value)}')`];

    // Keep track of storeDevice
    if (key === 'storeDevice') {
      baseQuery.push(`(${id}, 'activateDevice', '0')`);
    } else if (key === 'activateDevice') {
      baseQuery.push(`(${id}, 'storeDevice', '0')`);
    }

    return baseQuery;
  });

  if (finalQueries.length > 0) {
    const sql = `
      INSERT INTO public.bluefin_config (target_id, "key", value)
      VALUES ${finalQueries.join(', ')}
      ON CONFLICT (target_id, "key")
      DO UPDATE SET value = EXCLUDED.value
    `;

    try {
      await server.db.write.execute(sql);
    } catch (error) {
      logger.error(
        '[bluefin-helper].[updateBluefinConfig] Error inserting or updating data in updateBluefinConfig: %s',
        error
      );
    }
  }
};

const triggerEventstoBluefin = async (devices, action) => {
  const defaultConfig = await fetchDefaultBluefinConfig();

  if (!defaultConfig)
    throw new Error('Failed to fetch Bluefin defaultConfig from DB');

  const items = await Promise.all(
    devices.map(async device => {
      const payload = await createPayload(device, action, defaultConfig);
      return payload || null;
    })
  );

  const validItems = items.filter(item => item !== null);

  if (validItems.length) {
    const result = await pushBluefinKafkaEvents(
      validItems,
      config.AWS.kafka?.bluefinTopics?.deviceUpdates,
      'id'
    );
    await updateBluefinConfig(result.succeeded);
  }
};

const extractAddress = components => {
  const addressFields = components.reduce(
    (acc, { types, long_name: longName, short_name: shortName }) => {
      if (types.includes('street_number')) acc.streetNumber = longName;
      if (types.includes('route')) acc.route = longName;
      if (types.includes('locality')) acc.city = longName;
      if (types.includes('country')) {
        acc.country = longName;
        acc.countryCode = shortName || countryCodeMap[longName].code || '';
      }
      return acc;
    },
    { streetNumber: '', route: '', city: '', country: '', countryCode: '' }
  );

  return {
    address1: addressFields.streetNumber
      ? `${addressFields.streetNumber} ${addressFields.route}`
      : addressFields.route,
    city: addressFields.city,
    country: {
      name: addressFields.country,
      code: addressFields.countryCode,
    },
  };
};

const getAddress = async site => {
  try {
    const address = JSON.parse(site?.address || '{}');

    if (
      address?.address_components &&
      Array.isArray(address.address_components)
    ) {
      return extractAddress(address.address_components);
    }

    const { country, street, number, city } = address;
    return {
      address1: number ? `${number} ${street}` : street,
      city,
      country: {
        name: country,
        code: countryCodeMap[country].code || '',
      },
    };
  } catch (error) {
    logger.error(
      '[bluefin-helper].[getAddress] Failed to parse address JSON',
      error
    );
    return null;
  }
};

const processLocationEvent = async (
  site,
  eventTypeParam,
  currentGvrId = null
) => {
  const gvrReference = site?.externalReferences?.find(
    ref => ref.referenceType === 'GVR'
  );
  const defaultConfig = await fetchDefaultBluefinConfig();

  if (!gvrReference || !site?.bluefinFlag) return;

  const addressData = await getAddress(site);

  if (
    !addressData ||
    !addressData.country?.name ||
    !addressData.address1 ||
    !addressData.city
  ) {
    logger.error(
      '[bluefin-helper].[processLocationEvent] Incomplete address data'
    );
    return;
  }
  const resolvedEventType =
    eventTypeParam === 'updateLocation' &&
    currentGvrId !== gvrReference.referenceId
      ? 'createLocation'
      : eventTypeParam;

  if (resolvedEventType === 'updateLocation' && !site.locationId) {
    logger.info(
      `[bluefin-helper].[processLocationEvent] Missing Bluefin Location Id for site ${site.name}`
    );
    return;
  }

  const payload =
    resolvedEventType === 'createLocation'
      ? {
          id: gvrReference.referenceId,
          ts: Date.now(),
          type: resolvedEventType,
          data: {
            name: site.name,
            locationType: 'KIF',
            siteCode: gvrReference.referenceId,
            ...addressData,
            client: defaultConfig.client,
          },
        }
      : {
          id: site.locationId.toString(),
          ts: Date.now(),
          type: resolvedEventType,
          data: {
            id: site.locationId,
            siteCode: gvrReference.referenceId,
            name: site.name,
            locationType: 'KIF',
            ...addressData,
          },
        };

  await pushBluefinKafkaEvents(
    [payload],
    config.AWS.kafka?.bluefinTopics?.locationUpdates,
    'id'
  );
};

module.exports = {
  triggerEventstoBluefin,
  fetchBluefinConfig,
  updateBluefinConfig,
  parseDeviceBuild,
  processLocationEvent,
};
