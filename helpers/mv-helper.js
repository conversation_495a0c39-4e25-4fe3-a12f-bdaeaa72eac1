const logger = require('../lib/logger').mainLogger();
const { server } = require('../app');

module.exports = {
  async refreshMVUserSiteAuthorization(action) {
    const connection = await server.db.write.getConnection();
    try {
      logger.info(`user_site_authorization MV refresh started from ${action}`);
      await connection.execute(
        `REFRESH MATERIALIZED VIEW CONCURRENTLY user_site_authorization`
      );
      logger.info(`user_site_authorization MV refresh done from ${action}`);
    } catch (error) {
      logger.error({ error }, 'Failed to refresh materialized view');
    } finally {
      await connection.done();
    }
  },
};
