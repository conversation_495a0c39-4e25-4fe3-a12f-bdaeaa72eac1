const logger = require('../lib/logger').mainLogger();

const updateBlueFinHubLocation = async (dbConnection, payload) => {
  const { siteCode: gvrId, id: locationId } = payload;

  if (!gvrId || !locationId) {
    logger.error(
      `[bluefin-helper].[updateBlueFinHubLocation] Error: Missing siteCode (${gvrId}) or locationId (${locationId})`
    );
    return null;
  }

  try {
    const sql = `
    UPDATE public.site s
    SET bluefin_location_id = $1
    FROM site_external_references ser
    WHERE s.site_id = ser.site_id
    AND ser.reference_id = $2
    AND ser.reference_type = 'GVR'
    AND ser.deleted = false;
    `;

    const result = await dbConnection.write.execute(sql, [locationId, gvrId]);

    logger.info(
      `[bluefin-helper].[updateBlueFinHubLocation] Successfully updated bluefin_location_id for siteCode: ${gvrId}, locationId: ${locationId}.`
    );

    return result;
  } catch (error) {
    logger.error(
      '[bluefinConsumer-helper].[updateBlueFinHubLocation] Error updating data for bluefin hub location id: %s, error: %s',
      locationId,
      error.message
    );
    return null;
  }
};

module.exports = {
  updateBlueFinHubLocation,
};
