function getIPFromHeader(req) {
  // Remote address
  // Extract first value in comma-separated string `req.headers['x-forwarded-for']` or get
  // `req.connection.remoteAddress`.
  // "req": {
  //   "headers": {
  //     "x-forwarded-for": "***************, ************, ************",
  //   },
  //   "connection": {
  //     "remoteAddress": "127.0.0.1"
  //   }
  // }
  let { remoteAddress } = req.connection;
  const xForwardedFor = req.headers['x-forwarded-for'];
  if (xForwardedFor) {
    const { 0: firstRow } = xForwardedFor.split(', ');
    remoteAddress = firstRow;
  }

  return remoteAddress;
}

module.exports = {
  getIPFromHeader,
};
