const co = require('co');

const { server } = require('../app');

const deleteEmailTokenByTokenValue = `DELETE FROM email_token em
    WHERE em.value = $1`;

const countOfEmailTokenValueAndType = `SELECT COUNT(et.id)
    FROM email_token et
    WHERE et.value = $1
    AND et.type = $2
    AND et.expires > NOW()`;

/**
 * Function to delete an email token
 *
 * NOTE: You'll need to handle a transaction if required
 */
function deleteEmailToken(connection, token) {
  return co(function* execute() {
    yield connection.execute(deleteEmailTokenByTokenValue, [token]);
  }).catch(err => {
    throw err;
  });
}

/**
 * Function to check if an email token exists
 */
function emailTokenExists(value, tokenType) {
  return co(function* execute() {
    return (
      (yield server.db.read.row(countOfEmailTokenValueAndType, [
        value,
        tokenType,
      ])).count > 0
    );
  }).catch(err => {
    throw err;
  });
}

module.exports = {
  deleteEmailToken,
  emailTokenExists,
};
