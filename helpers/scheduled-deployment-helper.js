const moment = require('moment');
const axios = require('axios');
const restifyErrors = require('restify-errors');
const _ = require('lodash');
const {
  IcsEventStream,
} = require('@invenco-cloud-systems-ics/ics-event-stream-lib');
const env = require('../env');
const { scheduleDeploymentInterval } = require('../lib/app-constants');
const logger = require('../lib/logger').mainLogger();
const deviceHelper = require('./device-helper');

const getEntityId = (sourceType, req) => {
  switch (sourceType) {
    case 'UpdateSiteCustomAttribute':
      return req.body.entityIds.toString();
    case 'AddDevice':
    case 'MoveDevice':
      return req.body.siteId;
    case 'UpdateSiteTags':
      return req.body.site.id;
    default:
      return null;
  }
};

const checkMaintenanceFrameAndRoles = sourceType => async (req, res, next) => {
  try {
    if (_.isEqual(req.body.deploymentType, 'schedule')) {
      const scheduleTime = req.body.scheduledDateTime;
      const minDate = moment.utc().toISOString();
      const maxDate = moment
        .utc()
        .add(scheduleDeploymentInterval, 'days')
        .toISOString();

      if (!scheduleTime || scheduleTime < minDate || scheduleTime > maxDate) {
        return next(
          new restifyErrors.BadRequestError(
            `scheduledDateTime must lie within ${scheduleDeploymentInterval} days`
          )
        );
      }
    }

    if (_.isEqual(req.body.deploymentType, 'schedule')) {
      let requiredFeatureFlags;
      switch (sourceType) {
        case 'UpdateSiteCustomAttribute':
          // eslint-disable-next-line prefer-destructuring
          requiredFeatureFlags = ['SCHEDULING', 'SCHEDULING_UCA'];
          break;
        case 'AddDevice':
          requiredFeatureFlags = ['SCHEDULING', 'SCHEDULING_AD'];
          break;
        case 'MoveDevice':
          requiredFeatureFlags = ['SCHEDULING', 'SCHEDULING_MD'];
          break;
        case 'UpdateSiteTags':
          requiredFeatureFlags = ['SCHEDULING', 'SCHEDULING_UST'];
          break;
        default:
          break;
      }

      if (requiredFeatureFlags && requiredFeatureFlags.length !== 0) {
        const { company } = req.user;
        const companyFeatureFlags = company.featureFlags;
        // eslint-disable-next-line no-restricted-syntax
        for (const featureFlag of requiredFeatureFlags) {
          const hasFeatureFlag = companyFeatureFlags.includes(featureFlag);
          if (!hasFeatureFlag) {
            return next(new restifyErrors.ForbiddenError('Unauthorized User'));
          }
        }
      }
    }
    if (
      _.has(req.body, 'scheduledDateTime') &&
      !_.isEmpty(req.body.scheduledDateTime)
    ) {
      const maintenanceTimeFrom = env.config.maintenanceTime.from;
      const maintenanceTimeTo = env.config.maintenanceTime.to;
      const [startHours, startMinutes] = maintenanceTimeFrom.split(':');
      const [endHours, endMinutes] = maintenanceTimeTo.split(':');
      const scheduledDateTime = moment(req.body.scheduledDateTime).utcOffset(0);
      const startMaintenece = scheduledDateTime.clone();
      startMaintenece.set({
        hour: Number(startHours),
        minute: Number(startMinutes),
        second: 0,
        millisecond: 0,
      });
      const startMWF = startMaintenece.utc().unix();
      const endMaintenece = scheduledDateTime.clone();
      endMaintenece.set({
        hour: Number(endHours),
        minute: Number(endMinutes),
        second: 59,
        millisecond: 0,
      });
      const endMWF = endMaintenece.utc().unix();
      const scheduledUnixTime = scheduledDateTime.utc().unix();
      if (scheduledUnixTime >= startMWF && scheduledUnixTime <= endMWF) {
        return next(
          new restifyErrors.BadRequestError(
            'Schedule Time cannot be in maintenance window frame'
          )
        );
      }
    }
    return next();
  } catch (err) {
    logger.error({ err }, '[checkMaintenanceFrameAndRoles]');
    return next(
      new restifyErrors.BadRequestError(
        'Error in check maintenance frame and roles'
      )
    );
  }
};

const validateExistsScheduled = sourceType => async (req, res, next) => {
  try {
    if (
      _.has(req.body, 'scheduledDateTime') &&
      !_.isEmpty(req.body.scheduledDateTime) &&
      _.isEqual(req.body.deploymentType, 'schedule')
    ) {
      if (
        ['UpdateSiteCustomAttribute'].includes(sourceType) &&
        _.has(req.body, 'entityIds') &&
        _.isEmpty(req.body.entityIds)
      ) {
        return next(
          new restifyErrors.BadRequestError(
            'Invalid Request entityIds not found'
          )
        );
      }
      if (
        ['MoveDevice', 'AddDevice', 'UpdateSiteTags'].includes(sourceType) &&
        _.has(req.body, 'siteId') &&
        _.isEmpty(req.body.siteId)
      ) {
        return next(
          new restifyErrors.BadRequestError('Invalid Request siteId not found')
        );
      }
      if (
        ['UpdateSiteTags'].includes(sourceType) &&
        _.has(req.body.site, 'id') &&
        _.isEmpty(req.body.site.id)
      ) {
        return next(
          new restifyErrors.BadRequestError('Invalid Request siteId not found')
        );
      }
      if (!_.has(req.body, 'devices') && _.has(req.body, 'serialNumber')) {
        req.body.devices = [{ serialNumber: req.body.serialNumber }];
      }
      if (['MoveDevice'].includes(sourceType)) {
        const serialNumbers = req.body.devices.map(el => el.serialNumber);
        const oldSiteIds =
          await deviceHelper.getOldSitesSerailNumberForScheduledTask(
            serialNumbers
          );
        const oldSitesInfo = req.body.devices.map(eachSerialNumbers => {
          const matchedSite = oldSiteIds.find(
            item => item.serialNumber === eachSerialNumbers.serialNumber
          );
          if (matchedSite) {
            return matchedSite;
          }
          return { ...eachSerialNumbers, oldSiteId: null };
        });
        req.body.oldDeviceInfo = oldSitesInfo;
      }
      const entityId = getEntityId(sourceType, req);
      const reqBody = {
        pageIndex: 0,
        pageSize: 100,
        sourceType: [sourceType],
        status: ['PENDING'],
        entityId,
        earliestSchedule: true,
      };
      let data = { fail: false };
      try {
        const result = await axios({
          method: 'GET',
          url: `${env.config.url.api}/schedule-ms/schedules`,
          headers: {
            'content-type': 'application/json',
            authorization: req.headers.authorization,
          },
          params: reqBody,
        });
        data = { fail: false, ...result.data };
      } catch (error) {
        logger.error({ error }, '[callExistSheduledAPIAxios]');
        return next(
          new restifyErrors.BadRequestError(
            error.message ? error.message : error.response.data
          )
        );
      }
      if (!_.isEmpty(data.results)) {
        if (
          moment(`${data.results[0].scheduledAt}`).unix() >=
          moment(`${req.body.scheduledDateTime}`).unix()
        ) {
          data.fail = true;
          data.message = `Schedule already exists for this site at ${data.results[0].scheduledAt}`;
        }
      }
      if (data.fail) {
        return next(new restifyErrors.BadRequestError(data.message));
      }
    }
    return next();
  } catch (err) {
    logger.error({ err }, '[callExistSheduledAPIFun]');
    return next(
      new restifyErrors.BadRequestError('Error in check schedule time API')
    );
  }
};
const payload = (req, type) => {
  const payloadData = {
    tenantId: req.user.company.id,
    type,
    content: {
      body: req.body,
      params: req.params,
      user: {
        company: req.user.company,
        roles: req.user.roles,
        sub: req.user.sub,
      },
    },
    status: 'PENDING',
    scheduled_at: req.body.scheduledDateTime,
    error_details: null,
    created_at: new Date(),
    created_by: req.user.sub,
    updated_at: new Date(),
    updated_by: req.user.sub,
    deleted_at: null,
    deleted_by: null,
  };
  const entityId = getEntityId(type, req);
  payloadData.entityId = entityId;

  return payloadData;
};

const pushDataToKafka = async data => {
  try {
    const config = {
      pubId: 'apiNode',
    };

    const customKafkaConfig = {
      brokerUrl: env.config.AWS.eventstream.brokers[0],
      iamAuth: {
        region: env.config.AWS.eventstream.region,
      },
    };
    const ies = new IcsEventStream(config, customKafkaConfig);

    await ies
      .add('action', 'schedule.deployEvents')
      .add('payload', data)
      .publish()
      .then(() => {
        logger.debug('done publish');
      });
    return { status: 'success', message: 'Data sent to Kafka successfully' };
  } catch (err) {
    logger.error(
      {
        error: err,
        entityId: data.entityId,
      },
      `[ScheduleDeployment].[PushDataToKafka] Entity Id: ${data.entityId.toString()}. Error with Kafka stream: ${err.message}.`
    );
    return {
      status: 'error',
      message: err.message || 'Error in kafka push',
    };
  }
};

const scheduleDeploy = sourceType => async (req, res, next) => {
  const { deploymentType } = req.body;
  if (
    !_.isEmpty(deploymentType) &&
    deploymentType.toLowerCase() === 'schedule'
  ) {
    const payloadData = payload(req, sourceType);
    if (!_.isEmpty(payloadData)) {
      const result = await pushDataToKafka(payloadData);
      if (result.status === 'success') {
        return res.send(200, {
          success: true,
          message: 'Successfully scheduled the event',
        });
      }
    }
    return res.send(400, {
      success: false,
      error: 'Scheduled deployement failed',
    });
  }
  return next();
};

module.exports = {
  checkMaintenanceFrameAndRoles,
  validateExistsScheduled,
  pushDataToKafka,
  payload,
  scheduleDeploy,
};
