const { server } = require('../app');
const krsHandler = require('../src/rki/api/krs/krs.handler');

const RKI_REASON = {
  MOVE: 'MOVE',
  BULKMOVE: 'BULKMOVE',
  CREATE: 'CREATE',
  KEYGROUP_CHANGE: 'KEYGROUP_CHANGE',
};

async function createAutomaticRkiRequestSession(
  userId,
  deviceIds,
  siteId,
  reason
) {
  const { keyGroupRef, issuerCode } = await server.db.read.row(
    `
        SELECT kg.key_group_ref, ci.issuer_code
        FROM site s
        LEFT JOIN key_group kg ON kg.key_group_id = s.key_group_id
        LEFT JOIN certificate_issuer ci ON ci.certificate_issuer_id = kg.certificate_issuer_id
        WHERE s.site_id = $1;
     `,
    [siteId]
  );
  server.log.info(
    `rkiHelper.createAutomaticRkiRequestSession with parameter ${keyGroupRef} ${deviceIds}.`
  );
  // eslint-disable-next-line no-return-await
  return await krsHandler.createRKIRequestSession(
    null,
    deviceIds,
    keyGroupRef,
    issuerCode,
    reason,
    userId,
    siteId
  );
}

module.exports = {
  RKI_REASON,
  createAutomaticRkiRequestSession,
};
