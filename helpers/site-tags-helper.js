const _ = require('lodash');
const { sendEventOnUpdateSiteTags } = require('./site-tags-replication-events');

module.exports = {
  async updateSiteTags({
    companyId,
    siteId,
    tags,
    deploymentType,
    connection,
    userId,
    meta,
  }) {
    const { tagIds, newTagNames } = tags.reduce(
      (acc, tag) => {
        if (tag.id) {
          acc.tagIds.push(tag.id);
        } else {
          acc.newTagNames.push(tag.name);
        }
        return acc;
      },
      { tagIds: [], newTagNames: [] }
    );

    const createTags = async newSiteTagNames => {
      if (!_.isEmpty(tags)) {
        const newTags = await connection.execute(
          `
          INSERT INTO tag (name, company_id)
          SELECT unnest($1::text[]), $2
          RETURNING id;
        `,
          [newSiteTagNames, companyId]
        );

        const newTagIds = newTags.rows.map(newTag => newTag.id);

        await connection.execute(
          `
          INSERT INTO site_tag (tag_id, site_id, deployment_type, updated_by, meta)
          SELECT unnest($1::integer[]), $2, $3, $4, $5;
        `,
          [newTagIds, siteId, deploymentType, userId, meta || null]
        );
        return newTagIds;
      }
      return [];
    };

    const newSiteTags = await createTags(newTagNames);

    if (!_.isEmpty(tagIds)) {
      const assignedTags = await connection.execute(
        `SELECT tag_id FROM site_tag WHERE site_id = $1 AND deleted = false ${
          !_.isEmpty(newSiteTags)
            ? `AND tag_id NOT IN (${newSiteTags.join(',')})`
            : ''
        };`,
        [siteId]
      );

      const assignedTagIds = assignedTags.rows.map(
        existingTag => existingTag.tagId
      );

      const redundantTagIds = _.difference(assignedTagIds, tagIds);

      await connection.execute(
        `
          UPDATE site_tag
          SET deployment_type = $1, updated_by = $2, deleted = true, last_edited_date = NOW()
          WHERE site_id = $3 AND
          tag_id = ANY($4::integer[]);
        `,
        [deploymentType, userId, siteId, redundantTagIds]
      );

      const newSiteTagIds = _.difference(tagIds, assignedTagIds);

      await connection.execute(
        `
          INSERT INTO site_tag (tag_id, site_id, deployment_type, updated_by, meta)
          SELECT unnest($1::integer[]), $2, $3, $4, $5
          ON CONFLICT (site_id, tag_id)
          DO UPDATE SET
            deployment_type = EXCLUDED.deployment_type,
            updated_by = EXCLUDED.updated_by,
            meta = EXCLUDED.meta,
            deleted = false,
            last_edited_date = NOW();
        `,
        [newSiteTagIds, siteId, deploymentType, userId, meta || null]
      );

      await sendEventOnUpdateSiteTags(connection, siteId);
    }
  },

  async saveSiteTags(companyId, siteId, tags, userId, connection) {
    const tagNames = tags.map(tag => tag.name);
    const existingTags = await connection.execute(
      `
        SELECT id, name FROM tag
        WHERE name = ANY($1) AND company_id = $2;
      `,
      [tagNames, companyId]
    );

    const existingTagMap = existingTags.rows.reduce((acc, tag) => {
      acc[tag.name] = tag.id;
      return acc;
    }, {});
    const newTags = tags.filter(tag => !existingTagMap[tag.name]);
    let newTagsWithIds = [];
    if (newTags.length > 0) {
      const insertPromises = newTags.map(async tag => {
        const result = await connection.execute(
          `
          INSERT INTO tag (name, company_id)
          VALUES ($1, $2) RETURNING id;
          `,
          [tag.name, companyId]
        );
        return { ...tag, id: result.rows[0].id };
      });
      newTagsWithIds = await Promise.all(insertPromises);
    }
    const allTags = tags.map(tag => {
      const tagOne = newTagsWithIds.find(newTag => newTag.name === tag.name);
      return {
        id: existingTagMap[tag.name] || (tagOne ? tagOne.id : null),
        name: tag.name,
      };
    });

    const siteTagPromises = allTags.map(tag =>
      connection.execute(
        `
          INSERT INTO site_tag (site_id, tag_id, updated_by)
          VALUES ($1, $2, $3);
        `,
        [siteId, tag.id, userId]
      )
    );

    await Promise.all(siteTagPromises);
    await sendEventOnUpdateSiteTags(connection, siteId);
  },
};
