const _ = require('lodash');

module.exports = {
  updateAlarmParams: alarms =>
    _.flatten(
      alarms.map(a => {
        let alarm = a;

        if (a === 'version_downgrade') {
          alarm = 'version_downgrade%';
        } else if (a === 'removal_device_tampered') {
          alarm = '%removal_tamper%';
        } else if (a === 'destructive_device_tampered') {
          alarm = '%destructive_tamper%';
        } else if (a === 'device_tampered') {
          alarm = [
            'sdc_destructive_tamper',
            'upc_destructive_tamper',
            'sdc_removal_tamper',
            'upc_removal_tamper',
            'destructive_tamper',
          ];
        } else if (a === 'device_unreachable') {
          alarm = ['device_unreachable', 'device_offline'];
        } else if (a === 'site_critical') {
          alarm = ['site_critical', 'site_devices_offline'];
        }

        return alarm;
      })
    ),
};
