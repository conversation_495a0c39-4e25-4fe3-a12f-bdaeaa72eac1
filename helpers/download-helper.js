const { httpHeaders, httpCode } = require('../lib/app-constants');
const AWS = require('../lib/aws');

/**
 * Sets the appropriate response headers and makes a call to download file from S3
 * @param {Object} res
 * @param {Object} s3Config
 * @returns
 */
function downloadPackageFromS3(res, s3Config) {
  res.setHeader(
    httpHeaders.CONTENT_DISPOSITION.CODE,
    `attachment; filename="${s3Config.fileName}"`
  );
  res.setHeader(httpHeaders.FILE_NAME.CODE, s3Config.fileName);
  return new Promise((resolve, reject) => {
    AWS.downloadFromS3({ Bucket: s3Config.s3Bucket, Key: s3Config.s3Key })
      .on('httpHeaders', (s3StatusCode, headers) => {
        if (s3StatusCode === httpCode.OK.STATUS_CODE) {
          res.setHeader(
            httpHeaders.CONTENT_LENGTH.CODE,
            headers[httpHeaders.CONTENT_LENGTH.KEY]
          );
          res.setHeader(
            httpHeaders.CONTENT_TYPE.CODE,
            headers[httpHeaders.CONTENT_TYPE.KEY]
          );
          res.setHeader(
            httpHeaders.LAST_MODIFIED.CODE,
            headers[httpHeaders.LAST_MODIFIED.KEY]
          );
        }
      })
      .createReadStream()
      .on('error', err => {
        reject(err);
      })
      .on('end', () => {
        resolve();
      })
      .pipe(res);
  });
}

module.exports = {
  downloadPackageFromS3,
};
