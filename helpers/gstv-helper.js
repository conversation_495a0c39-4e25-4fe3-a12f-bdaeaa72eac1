const restify = require('restify');
const co = require('co');
const shortId = require('shortid');
const _ = require('lodash');

const { server } = require('../app');
const aws = require('../lib/aws');

const configByTerminal = 'CONFIGURATION_PULLED_BY_TERMINAL';
const playlistByTerminal = 'PLAYLIST_PULLED_BY_TERMINAL';
const mediaByTerminal = 'MEDIA_PULLED_BY_TERMINAL';

function sendGstvNotificationIfJobIsTerminal(
  connection,
  queue,
  job,
  jobStatus
) {
  return co(function* execute() {
    if (jobStatus < 3) {
      return;
    }
    const receivedTime = new Date().toISOString();
    // get the trigger by job id
    const trigger = yield connection.execute(
      `
                        SELECT
                            gjt.trigger
                        FROM
                            gstv_job_trigger gjt
                        WHERE
                            gjt.job_id = $1`,
      [job.id]
    );
    const jobTrigger = trigger.rows[0];
    if (!jobTrigger) {
      server.log.warn(`Job Trigger with job id ${job.id} not found`);
      return;
    }
    const deviceResult = yield connection.execute(
      `
                SELECT d.target_id as device_id,
                 p.device_type,
                 d.site_id,
                 array_to_string(array_agg(t.tag_id), ',') as tag_ids
                FROM target d 
                LEFT JOIN site_tag t on t.site_id = d.site_id AND t.deleted = false
                JOIN product_type p on d.device_type = p.device_type
                WHERE d.target_id = $1 AND d.active IS true
                GROUP BY d.target_id, p.device_type, d.site_id;`,
      [job.deviceId]
    );
    const device = deviceResult.rows[0];
    if (!device) {
      throw new restify.NotFoundError(
        `Device with id ${job.deviceId} not found`
      );
    }

    const jobData = (yield connection.execute(
      `
            SELECT data FROM job WHERE id = $1;
        `,
      [job.id]
    )).rows[0].data;

    if (!jobData) {
      server.log.warn('Job has no data! GSTV job should have data!');
    }

    const data = JSON.parse(jobData);
    let notificationStatus = null;
    if (jobStatus === 3) {
      notificationStatus = 'success';
    } else if (jobStatus > 3) {
      notificationStatus = 'failure';
    }
    const notif = {
      id: shortId.generate(),
      status: notificationStatus,
      terminalId: job.deviceId,
    };
    server.log.info(
      `Processing GSTV job ${job.id} trigger: ${jobTrigger.trigger}`
    );
    if (jobTrigger.trigger === 'networkConfig') {
      notif.guid = data.networkConfig.guid;
      notif.notificationType = configByTerminal;
      notif.siteId = data.siteConfig.configuration[0].siteId;
    } else if (jobTrigger.trigger === 'siteConfig') {
      notif.guid = data.siteConfig.guid;
      notif.notificationType = configByTerminal;
      notif.siteId = data.siteConfig.configuration[0].siteId;
    } else if (jobTrigger.trigger === 'playlist') {
      notif.guid = data.playlist.guid;
      notif.notificationType = playlistByTerminal;
      notif.siteId = data.siteConfig.configuration[0].siteId;
    } else if (jobTrigger.trigger.startsWith('media')) {
      // Bring java fix "media:media_file_name" to R2, should be media_file_name, not :media_file_name
      const filename = jobTrigger.trigger.substring(6);
      if (filename) {
        const filen = _.find(
          data.files,
          obj => obj.media.filename === filename
        );

        if (filen) {
          notif.guid = filen.media.guid;
          notif.notificationType = mediaByTerminal;
          notif.filename = filename;
        } else {
          server.log.info(`GSTV job ${job.id} missing media ${filename}`);
        }
      } else {
        server.log.info(
          `GSTV job trigger missing media filename for job ${job.id}`
        );
      }
    }
    const payload = JSON.stringify(notif);
    server.log.info(`Notifying GSTV for job ${job.id}: ${payload}`);
    if (payload.length > 0) {
      const params = {
        MessageBody: payload,
        QueueUrl: queue[0].url,
        DelaySeconds: 0,
        MessageAttributes: {
          'device-id': {
            DataType: 'String',
            StringValue: device.deviceId.toString(),
          },
          'device-type': {
            DataType: 'String',
            StringValue: device.deviceType.toString(),
          },
          'received-time': { DataType: 'String', StringValue: receivedTime },
          'site-id': {
            DataType: 'String',
            StringValue: device.siteId.toString(),
          },
        },
      };

      if (device.tagIds !== '') {
        params.MessageAttributes['tag-ids'] = {
          DataType: 'String',
          StringValue: device.tagIds,
        };
      }

      const sqs = aws.createSqsClient(
        queue[0].region,
        queue[0].secretAccessKey,
        queue[0].accessKeyId
      );
      yield aws.sendSqsMessage(params, sqs);
    }
  }).catch(err => {
    // ICS-1464 Let the caller continue if sending notification error
    server.log.info(`Recovering GSTV sending notification exception ${err}`);
  });
}

module.exports = {
  sendGstvNotificationIfJobIsTerminal,
};
