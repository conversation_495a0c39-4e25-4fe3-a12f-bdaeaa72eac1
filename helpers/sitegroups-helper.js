const { server } = require('../app');

const getSiteIdsByAuthSiteGroupId = async authSiteGroupId =>
  // eslint-disable-next-line no-return-await
  await server.db.read.rows(
    `
    SELECT 
        site.site_id as id
    FROM 
        site
        JOIN authorization_site_group_site asgs 
          ON site.site_id = asgs.site_id
    WHERE 
        asgs.authorization_site_group_id = $1 
        AND site.active != false;`,
    [authSiteGroupId]
  );

module.exports = {
  getSiteIdsByAuthSiteGroupId,
};
