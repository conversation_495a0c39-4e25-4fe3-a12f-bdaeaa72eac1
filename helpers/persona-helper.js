const restify = require('restify');
const { server } = require('../app');

const validatePersonaTypes = async (req, res, next) => {
  try {
    const { persona, companyId } = req.body;
    const userCompanyId = req.user?.company?.id;

    if (!persona || (persona && persona.toLowerCase() === 'custom')) {
      req.body.persona = 'Custom';
      return next();
    }

    let personaQuery = `select distinct p."name" from persona p 
        inner join persona_tenant pt on p.id = pt.persona_id 
          or p.is_applicable_to_all_tenants 
        where p."name" = $1 and (`;

    const personaParams = [persona];

    if (companyId || userCompanyId) {
      personaQuery += ' pt.tenant_id = $2 or ';
      personaParams.push(companyId || userCompanyId);
    }

    personaQuery += 'p.is_applicable_to_all_tenants);';

    const personaTypes = await server.db.read.rows(personaQuery, personaParams);

    if (personaTypes.length === 0) {
      return next(new restify.BadRequestError('Persona must be of valid type'));
    }

    return next();
  } catch (err) {
    return next(new restify.InternalServerError(err));
  }
};

const checkPersonaRolesExistByCompanyId = async (companyId, persona, roles) => {
  const checkPersonaByCompanyQuery = `
            select distinct r.role_name as role from persona_role pr
              inner join "role" r on pr.role_id = r.role_id
              inner join persona p on pr.persona_id = p.id
              inner join persona_tenant pt on p.id = pt.persona_id or p.is_applicable_to_all_tenants
              where p."name" = $1
              and (pt.tenant_id = $2 or p.is_applicable_to_all_tenants)
        `;

  const result = await server.db.read.rows(checkPersonaByCompanyQuery, [
    persona,
    companyId,
  ]);

  if (result && result.length === roles.length) {
    const allRolesPresent = result.every(pRole => roles.includes(pRole.role));
    if (!allRolesPresent) {
      return false;
    }
    return true;
  }

  return false;
};

module.exports = {
  checkPersonaRolesExistByCompanyId,
  validatePersonaTypes,
};
