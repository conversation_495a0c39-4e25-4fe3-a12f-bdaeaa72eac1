const {
  DeviceConfigurationService,
  DeviceOnboardingAdapter,
} = require('@invenco-cloud-systems-ics/device-type-onboarding-lib');

const { config } = require('../env');

class DeviceOnboardingAdapterSingleton {
  static instance = null;

  static getInstance() {
    if (!this.instance) {
      const configurationService = DeviceConfigurationService.getInstance({
        region: config.AWS.S3.region,
        bucket: config.AWS.S3.deviceTypeOnboardingBucket,
      });

      this.instance = new DeviceOnboardingAdapter(configurationService);
    }
    return this.instance;
  }
}

// eslint-disable-next-line import/no-unused-modules
const deviceOnboardingAdapter = DeviceOnboardingAdapterSingleton.getInstance();
module.exports = { deviceOnboardingAdapter };
