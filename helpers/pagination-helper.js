module.exports = {
  parsePaginationParamsDDB: req => {
    const defaultPageMinSize = req.query.pageMinSize < 0 ? 1000000000 : 100;
    const pageMinSize =
      req.query.pageMinSize > 0
        ? parseInt(req.query.pageMinSize, 10)
        : defaultPageMinSize;
    const pageKey = req.query.pageKey ? req.query.pageKey : null;

    return {
      pageMinSize,
      pageKey,
    };
  },
  parsePaginationBodyDDB: req => {
    const defaultPageMinSize = req.body.pageMinSize < 0 ? 1000000000 : 100;
    const pageMinSize =
      req.body.pageMinSize > 0
        ? parseInt(req.body.pageMinSize, 10)
        : defaultPageMinSize;
    const pageKey = req.body.pageKey ? req.body.pageKey : null;

    return {
      pageMinSize,
      pageKey,
    };
  },
  parsePaginationParams: req => {
    const defaultPageSize = req.query.pageSize < 0 ? 1000000000 : 100;
    const pageSize =
      req.query.pageSize > 0
        ? parseInt(req.query.pageSize, 10)
        : defaultPageSize;
    const pageIndex = req.query.pageIndex
      ? parseInt(req.query.pageIndex, 10)
      : 0;
    const offset = pageSize * pageIndex;

    return {
      pageSize,
      pageIndex,
      offset,
    };
  },
  parsePaginationBody: req => {
    const defaultPageSize = req.body.pageSize < 0 ? 1000000000 : 100;
    const pageSize =
      req.body.pageSize > 0 ? parseInt(req.body.pageSize, 10) : defaultPageSize;
    const pageIndex = req.body.pageIndex ? parseInt(req.body.pageIndex, 10) : 0;
    const offset = pageSize * pageIndex;

    return {
      pageSize,
      pageIndex,
      offset,
    };
  },
};
