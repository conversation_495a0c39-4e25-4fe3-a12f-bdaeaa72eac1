const { server } = require('../app');
const { DAY_IN_SECONDS } = require('../lib/app-constants');
const { default: RedisClient } = require('../lib/redis');
const { gLibCompress, gLibDecompress } = require('./zlip-helper');

function getCompanyByID(companyId) {
  return server.db.read.row(
    `
            SELECT c.id,
                   c.name
              FROM company c
              WHERE c.id = $1;
        `,
    [companyId]
  );
}

function getCompanyByReference(reference, userId, isSystemUser = false) {
  return server.db.read.row(`
                SELECT c.id, c.name, c.reference, s.site_id, k.key_group_ref,
                    array_remove(array_agg(cpt.device_type), NULL) as device_types
                FROM company c
                    JOIN site s ON s.site_id = c.default_site
                    ${
                      isSystemUser
                        ? ''
                        : `
                        JOIN user_site_authorization a ON
                            a.site_id = s.site_id AND
                            a.user_id = '${userId}'
                    `
                    }
                    LEFT JOIN key_group k ON k.key_group_id = s.key_group_id
                    LEFT JOIN company_product_type cpt ON cpt.company = c.id
                WHERE c.reference = '${reference}' 
                GROUP BY c.id, c.name, c.reference, s.site_id, k.key_group_ref;
            `);
}

async function getAllFeatureFlagsByCompanyId(companyId) {
  const fetchCmpny = await server.db.read.rows(
    `
      SELECT cff.feature_flag
      FROM company_feature_flag cff
      WHERE cff.company = $1;
        `,
    [companyId]
  );
  return fetchCmpny.length > 0
    ? fetchCmpny.map(({ featureFlag }) => featureFlag)
    : [];
}

async function getCompanies(name) {
  const params = [];
  let selectStatement = `
            SELECT c.id, c.name
            FROM company c
        `;
  if (name) {
    selectStatement += ' WHERE c.name = $1';
    params.push(name);
  }
  // eslint-disable-next-line no-return-await
  return await server.db.read.rows(`${selectStatement} LIMIT 1000;`, params);
}

const getAllFeatureFlagsByCompanyIdInCache = async companyId => {
  const companyIdKey = `ics_core_company:ftflg:${companyId}`;
  const redisClient = RedisClient.getInstance();
  try {
    const companyCacheValues = await redisClient.getValue(companyIdKey);
    if (companyCacheValues) {
      const companyFtflgsValues = await gLibDecompress(companyCacheValues);
      return JSON.parse(companyFtflgsValues) || [];
    }
    const fetchedCompanyFtflgs = await getAllFeatureFlagsByCompanyId(companyId);
    const compressedValues = await gLibCompress(
      JSON.stringify(fetchedCompanyFtflgs)
    );
    await redisClient.saveKeyValueWithTTL(
      companyIdKey,
      compressedValues,
      0.5 * DAY_IN_SECONDS
    );
    return fetchedCompanyFtflgs;
  } catch (error) {
    throw new Error(
      `Error in get feature flags for companyId from redis cache ${companyId}:`
    );
  }
};

const canManageServiceRecipient = async ({
  serviceManagerId,
  serviceRecipientId,
}) => {
  const result = await server.db.read.row(
    `
                SELECT c.id
                  FROM company c
                  JOIN company_relationship cr ON cr.company_id = c.id
                    AND cr.allowed_company_id = $2
                    AND c.id = $1;
            `,
    [serviceRecipientId, serviceManagerId]
  );
  if (!result) {
    return false;
  }
  return true;
};

const getServiceRecipients = async serviceManagerId => {
  const serviceRecipients = await server.db.read.rows(
    `
                SELECT cr.company_id
                  FROM company_relationship cr WHERE cr.allowed_company_id = $1;
            `,
    [serviceManagerId]
  );
  return serviceRecipients.map(({ companyId }) => companyId);
};

module.exports = {
  getCompanyByID,
  getCompanyByReference,
  getAllFeatureFlagsByCompanyIdInCache,
  getCompanies,
  canManageServiceRecipient,
  getServiceRecipients,
};
