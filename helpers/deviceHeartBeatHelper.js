const timezone = require('moment-timezone');
const env = require('../env');
const KafkaStreamHelper = require('./kafkaStreamHelper');

const addDeviceHeartBeat = async device => {
  if (!env.config.AWS.kafka || !env.config.AWS.kafka.enable) return;
  if (!device.gvrSiteId) return;

  const timeNow = Date.now();

  const heartBeatData = {
    ts: timeNow,
    dt: 'st',
    ds: 'invenco.system',
    dn: 'heartbeat',
    dv: true,
    deviceId: device.deviceId,
    siteId: device.siteId,
    deviceType: device.deviceType,
    companyId: device.companyId,
  };

  let timeInSiteTZ;
  if (device.timezoneId && timezone.tz.names().includes(device.timezoneId)) {
    // convert to site local timezone for aggregation
    const timeInUTC = timezone.tz(timeNow, 'UTC');
    timeInSiteTZ = timeInUTC.clone().tz(device.timezoneId).format();
  } else {
    timeInSiteTZ = timeNow; // if no valid timezone defined for site use UTC
  }
  heartBeatData.timeInSiteTZ = timeInSiteTZ;
  if (device.tagIds) {
    heartBeatData.tagIds = device.tagIds;
  }

  heartBeatData.gvrSiteId = device.gvrSiteId;
  heartBeatData.fuelPosition = device.fuelPosition;

  await KafkaStreamHelper.putDataToKafkaStream(device, [
    {
      PartitionKey: device.deviceId,
      Data: JSON.stringify(heartBeatData),
    },
  ]);
};

module.exports = {
  addDeviceHeartBeat,
};
