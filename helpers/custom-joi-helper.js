// const Joi = require('joi');
// const xssValidate = require('./xss-sanitizer');

// /* eslint-disable-next-line no-unused-vars */
// const customJoi = Joi.extend(joi => ({
//   type: 'string',
//   base: Joi.string(),
//   messages: {
//     'string.xss': '{{#label}} contains unsafe content',
//   },
//   rules: {
//     xss: {
//       validate(value, helpers) {
//         return xssValidate(value, helpers);
//       },
//     },
//   },
// }));

// module.exports = customJoi;
