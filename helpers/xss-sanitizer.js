// const sanitizeHtml = require('sanitize-html');

// const xssValidate = (value, helpers, options = {}) => {
//   const defaultOptions = {
//     allowedTags: ['b', 'i', 'u', 'em', 'strong', 'a'],
//     allowedAttributes: {
//       a: ['href', 'title'],
//     },
//     disallowTags: ['script', 'style', 'object', 'embed'],
//     allowedSchemes: ['http', 'https'],
//     transformTags: {},
//   };

//   const config = { ...defaultOptions, ...options };

//   if (!value || typeof value !== 'string') {
//     return value;
//   }

//   const cleanValue = sanitizeHtml(value, {
//     allowedTags: config.allowedTags,
//     allowedAttributes: config.allowedAttributes,
//     allowedSchemes: config.allowedSchemes,
//     transformTags: {
//       ...config.transformTags,
//       a: (tagName, attribs) => {
//         const newAttribs = { ...attribs };
//         if (newAttribs.href && !/^https?:\/\//i.test(newAttribs.href)) {
//           newAttribs.href = '';
//         }

//         return { tagName, attribs: newAttribs };
//       },
//     },
//   });
//   if (cleanValue !== value && cleanValue !== value.replace(/&/g, '&amp;')) {
//     return helpers.error('string.xss', { value }); // Return error if input changed and not just & to &amp;
//   }

//   return cleanValue;
// };

// module.exports = xssValidate;
