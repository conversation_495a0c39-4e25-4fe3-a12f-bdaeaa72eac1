const zlib = require('zlib');
const { promisify } = require('util');

const gzipAsync = promisify(zlib.gzip);
const gunzipAsync = promisify(zlib.gunzip);
const options = {
  level: 7,
  chunkSize: 32 * 1024,
};

const gLibCompress = async value => {
  try {
    const compressedValue = await gzipAsync(
      Buffer.from(value, 'utf-8'),
      options
    );
    return compressedValue.toString('base64');
  } catch (err) {
    console.error('Compression Error:', err);
    throw err;
  }
};

const gLibDecompress = async value => {
  try {
    const uncompressedValue = await gunzipAsync(
      Buffer.from(value, 'base64'),
      options
    );
    return uncompressedValue.toString('utf-8');
  } catch (err) {
    console.error('Decompression Error:', err);
    throw err;
  }
};

module.exports = {
  gLibCompress,
  gLibDecompress,
};
