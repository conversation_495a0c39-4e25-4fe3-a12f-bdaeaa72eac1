const { default: axios } = require('axios');
const jwt = require('../lib/jwt');
const env = require('../env');
const logger = require('../lib/logger').mainLogger();

const { config } = env;
const { generateCsrfTokenAndHash } = require('../src/csrf/csrf.handler');

module.exports = {
  // TODO: Marcelo - Why are we using this method, check
  createAuthResp(user, flags) {
    const token = jwt.createToken(user);

    const resp = {
      fullName: user.fullName,
      email: user.email,
      created: user.created,
      userId: user.id,
      roles: user.roles,
      company: user.company,
      token,
    };

    if (flags) {
      resp.flags = flags;
    }

    if (config.csrf?.enable) {
      const { csrf, csrfHash } = generateCsrfTokenAndHash(user.id);
      resp.csrf = csrf;
      resp.csrfHash = csrfHash;
    }
    return resp;
  },

  async getBridgeAuthToken() {
    try {
      const { endpoint, login1, login2 } = config.cryptoHub;
      const SUCCESS_MESSAGE = 'OK';
      let bridgeLoginToken = null;

      logger.error(`authHelper.getBridgeAuthToken: ${login1} -- ${login1}`);

      if (!login1 || !login2) {
        throw new Error('Missing login credentials for bridge server');
      }

      const logins = [login1, login2];

      /* eslint-disable no-await-in-loop */
      for (let i = 0; i < logins.length; i++) {
        const credentials = logins[i];
        const payload = {
          ...credentials,
          ...(bridgeLoginToken && { token: bridgeLoginToken }),
        };

        const response = await axios.post(
          `${endpoint}/rest/v1/login/${config.cryptoHub.service_id}`,
          payload
        );
        logger.error(
          `authHelper.getBridgeAuthToken: %s`,
          JSON.stringify(response.data)
        );
        const { msg, token } = response.data;
        if (msg === SUCCESS_MESSAGE) {
          bridgeLoginToken = token;
        } else {
          throw new Error(`Bridge login failed with message: ${msg}`);
        }
      }
      logger.error(`authHelper.getBridgeAuthToken: %s`, bridgeLoginToken);
      return bridgeLoginToken;
    } catch (err) {
      const errorMsg = err.response?.data?.msg;
      logger.error(`authHelper.getBridgeAuthToken: %s`, errorMsg);
      throw err;
    }
  },
};
