const restify = require('restify');

const validateBankPermissions = async (req, res, next) => {
  try {
    const { roles, groups } = req.body;

    if (roles && roles.includes('BANK_USER') && roles.length > 1) {
      return next(
        new restify.BadRequestError('Roles must have valid permissions')
      );
    }
    if (roles && groups && roles.includes('BANK_USER') && groups.length > 0) {
      return next(
        new restify.BadRequestError('Bank User should not be part of any group')
      );
    }

    return next();
  } catch (err) {
    return next(new restify.InternalServerError(err));
  }
};

module.exports = {
  validateBankPermissions,
};
