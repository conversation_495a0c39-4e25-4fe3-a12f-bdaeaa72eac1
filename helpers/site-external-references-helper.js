const { server } = require('../app');
const { eventBusSources } = require('../lib/app-constants');
const logger = require('../lib/logger').mainLogger();
const aws = require('../lib/aws');
const { config } = require('../env');

const siteExternalReferenceHelpers = {
  async getSiteExternalReferences(siteId, externalReferences) {
    const mappedExternalReferences = externalReferences.map(
      ({ referenceId, referenceType }) => ({
        reference_id: referenceId,
        reference_type: referenceType,
      })
    );
    const mappedExternalReferenceStrings = JSON.stringify(
      mappedExternalReferences
    );
    let filter = '';
    const params = [mappedExternalReferenceStrings];
    if (siteId) {
      filter = ' AND site_id != $2';
      params.push(siteId);
    }
    const query = ` SELECT e.reference_type FROM public.site_external_references e, jsonb_to_recordset($1) as ef(reference_id varchar(50), reference_type varchar(20)) 
                WHERE e.reference_id = ef.reference_id AND e.reference_type = ef.reference_type AND deleted = false 
                ${filter}`;

    // eslint-disable-next-line no-return-await
    return await server.db.read.rows(query, params);
  },
  async updateSiteExternalReferences(siteId, userId, connection) {
    await connection.execute(
      `               
                UPDATE public.site_external_references SET deleted = true, deleted_date = now(), deleted_by = $2 
                WHERE site_id = $1 AND deleted = false               
        `,
      [siteId, userId]
    );
  },
  async insertSiteExternalReferences(
    siteId,
    externalReferences,
    userId,
    connection
  ) {
    try {
      const mappedExternalReferences = externalReferences.map(
        ({ referenceId, referenceType }) => ({
          reference_id: referenceId,
          reference_type: referenceType,
        })
      );
      const mappedExternalReferenceStrings = JSON.stringify(
        mappedExternalReferences
      );

      await connection.execute(
        ` 
                INSERT INTO public.site_external_references(site_id, reference_id, reference_type, created_by, created_date, updated_date, updated_by)
                SELECT $1, x.reference_id, x.reference_type, $3 as created_by, now() as created_date, now() as updated_date, $3 as updated_by
                FROM jsonb_to_recordset($2) as x(reference_id varchar(50), reference_type varchar(20))
        `,
        [siteId, mappedExternalReferenceStrings, userId]
      );
    } catch (err) {
      logger.error(
        'Failed to insert site external references for site: {siteId}',
        err
      );
      throw err;
    }
  },
  async sendEventsToEventBridgeOnSiteUpdate(
    siteId,
    referenceId,
    tenantId,
    type,
    integrationType
  ) {
    const integrationId = referenceId;
    const eventBridgeParams = {
      data: {
        siteId,
        tenantId,
        integrationId,
        integrationType,
        type,
      },
      detailType:
        config.AWS.eventBus.proofOfPlay.rules.siteReferenceReplication,
      source: eventBusSources.PUT_SITE,
    };
    await aws.sendEventsToEventBridge(eventBridgeParams);
  },
  resolveExternalReferenceType(externalReferences) {
    if (!externalReferences) {
      return null;
    }
    const reference = externalReferences.find(
      item => item.referenceType === 'MEDIA PROVIDER'
    );
    return reference || null;
  },
};

module.exports = siteExternalReferenceHelpers;
