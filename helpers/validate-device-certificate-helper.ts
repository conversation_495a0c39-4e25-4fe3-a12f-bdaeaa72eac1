import _ from 'lodash';
import restifyErrors from 'restify-errors';
import CONSTANTS from '../src/rki/lib/constants';
import { Certificate, RestifyResponse } from '../src/rki/api/approval/approval';

export const validateDeviceJsonCertificate =
  (sourceType: string) =>
  async (
    req: {
      body: { certificate: string | null; isJsonCertificates: boolean | null };
      log: { error: (arg0: { error: Error }, arg1: string) => void };
    },
    _res: RestifyResponse,
    next: (arg0?: restifyErrors.BadRequestError | undefined) => void
  ) => {
    try {
      if (
        [
          'AddDevice',
          'UpdateDeviceCertificate',
          'bridgeAddDevice',
          'bridgeUpdateDevice',
        ].includes(sourceType) &&
        _.has(req.body, 'isJsonCertificates') &&
        req.body.isJsonCertificates
      ) {
        if (!req.body.certificate) {
          throw new Error(
            `[validateDeviceJsonCertificate][${sourceType}] body.certificate property is missing`
          );
        }
        const parsed = JSON.parse(req.body.certificate);
        if (!Array.isArray(parsed)) {
          throw new Error(
            `[validateDeviceJsonCertificate][${sourceType}] Parsed result is not an array`
          );
        }
        parsed.forEach((element: Certificate) => {
          if (!_.has(element, 'issuer_code')) {
            throw new Error(
              `[validateDeviceJsonCertificate][${sourceType}] issuer_code property is missing`
            );
          }
          if (element.certificate === null) {
            throw new Error(
              `[validateDeviceJsonCertificate][${sourceType}] Certificate is null`
            );
          }
          if (!_.has(element, 'certificate')) {
            throw new Error(
              `[validateDeviceJsonCertificate][${sourceType}] certificate property is missing`
            );
          }
          if (element.issuer_code.length > 25) {
            throw new Error(
              `[validateDeviceJsonCertificate][${sourceType}] Certificate issuer_code exceed 25 characters`
            );
          }
          if (
            !['bridgeAddDevice', 'bridgeUpdateDevice'].includes(sourceType) &&
            !Object.values(CONSTANTS.ValidCertificateIssuerCode).includes(
              element.issuer_code
            )
          ) {
            throw new Error(
              `[validateDeviceJsonCertificate][${sourceType}] Unhandled device certificate issuer_code ${element.issuer_code}`
            );
          }
        });

        const issuerCodes = [
          ...new Set(parsed.map(element => element.issuer_code)),
        ];
        if (issuerCodes.length !== parsed.length) {
          throw new Error(
            `[validateDeviceJsonCertificate][${sourceType}] Duplicated Issuer Codes in JSON certificates`
          );
        }
      }
    } catch (error: Error | any) {
      req.log.error({ error }, '[validateDeviceJsonCertificate]');
      return next(new restifyErrors.BadRequestError(error.message));
    }
    return next();
  };
