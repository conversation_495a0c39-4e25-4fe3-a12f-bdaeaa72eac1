const _ = require('lodash');
const co = require('co');

const { server } = require('../app');
const krdStateMachine = require('../src/rki/lib/key-request-device.statemachine');
const logger = require('../lib/logger').mainLogger();
const KRSHelper = require('../src/rki/api/krs/krs-helper');
const CONSTANTS = require('../src/rki/lib/constants');
const jobHelper = require('./job-helper');

const { jobStatus } = jobHelper;
const KEY_REQUEST_SESSION_STATUS = {
  PENDING_AUTHORIZATION: 0,
  CANCELLED: 1,
  REJECTED: 2,
  EXPIRED: 3,
  APPROVED: 4, // deprecated, use IN_PROGRESS when a session is approved
  IN_PROGRESS: 5,
  COMPLETE: 6,
  FAILED: 7,
  PENDING_SECOND_AUTHORIZATION: 8,
};

async function tryUpdatingKeyRequestSessionToComplete(
  connection,
  keyRequestSessionId
) {
  const { retrievalSuccess, retrieving, ready, installing } =
    krdStateMachine.states;

  await connection.execute(
    `
        UPDATE key_request_session krs
        SET status = $1
        WHERE key_request_session_id = $2
        AND NOT EXISTS (
            SELECT 1
            FROM key_request_device krd
            WHERE krd.key_request_session_id = krs.key_request_session_id
                AND COALESCE(krd.status = ANY('{${retrievalSuccess},${retrieving},${ready},${installing}}'::int[]), true)
            FOR UPDATE
        );
    `,
    [KEY_REQUEST_SESSION_STATUS.COMPLETE, keyRequestSessionId]
  );
}

function updateKeyRequestAndSendNotifications(
  req,
  connection,
  job,
  newStatus,
  message
) {
  return co(function* execute() {
    const keyRequestDeviceResult = yield connection.execute(
      `
            SELECT 
                krd.key_request_device_id,
                krd.key_request_session_id,
                krd.device_id,
                krd.status as device_status_id,
                krs.name,
                krs.company_id,
                krs.request_user_id,
                ( SELECT iu.email FROM ics_user iu WHERE iu.id = krs.request_user_id ) AS request_user_email,
                krs.authorizer_user_id,
                ( SELECT iu.email FROM ics_user iu WHERE iu.id = krs.authorizer_user_id ) AS authorizer_user_email,
                krs.second_authorizer_user_id,
                ( SELECT iu.email FROM ics_user iu WHERE iu.id = krs.second_authorizer_user_id ) AS second_authorizer_user_email,
                krs.key_group_ref,
                t.serial_number
            FROM
                key_request_device krd
            JOIN
                key_request_session krs ON krd.key_request_session_id = krs.key_request_session_id
            JOIN
                target t ON krd.device_id = t.target_id
            WHERE
                krd.job_id = $1`,
      [job.id]
    );
    const keyRequestDevice = keyRequestDeviceResult.rows[0];

    if (!keyRequestDevice) {
      server.log.warn(`Cannot find key request device for job ${job.id}`);
      return;
    }

    const krdSM = yield krdStateMachine.load(
      keyRequestDevice.keyRequestDeviceId,
      connection
    );

    let notificationType = '';

    if (newStatus === jobStatus.FAILED) {
      yield krdSM[krdStateMachine.actions.keyInstallFailed.name](message);

      notificationType = CONSTANTS.NotificationTypes.RKI_DEVICE_FAILED;
    } else if (newStatus === jobStatus.COMPLETE) {
      // Update device's keygroup ref on successful RKI install
      // eslint-disable-next-line max-len
      yield connection.execute(
        'UPDATE target SET key_group_ref = $1, last_rki = now() WHERE target_id = $2',
        [keyRequestDevice.keyGroupRef, keyRequestDevice.deviceId]
      );
      yield krdSM[krdStateMachine.actions.keyInstallSuccess.name](message);

      notificationType = CONSTANTS.NotificationTypes.RKI_DEVICE_SUCCESS;
    } else if (newStatus === jobStatus.IN_PROGRESS) {
      yield krdSM[krdStateMachine.actions.keyInstallInProgress.name]();
    } else {
      server.log.info(
        `Unknown job status ${newStatus} for the key request session. Accept only job status FAIL (4) or COMPLETE (3)`
      );
      return;
    }

    const rkiData = {
      id: keyRequestDevice.keyRequestSessionId,
      companyId: keyRequestDevice.companyId,
      name: keyRequestDevice.name,
      serialNumber: keyRequestDevice.serialNumber,
      isAutomatedRKI: keyRequestDevice.name.includes('[Automated]'),
    };

    let creator = null;
    const approvers = [];

    if (!rkiData.isAutomatedRKI) {
      creator = {
        id: keyRequestDevice.requestUserId,
        email: keyRequestDevice.requestUserEmail,
      };
    } else {
      approvers.push({
        id: keyRequestDevice.secondAuthorizerUserId,
        email: keyRequestDevice.secondAuthorizerUserEmail,
      });
    }
    approvers.push({
      id: keyRequestDevice.authorizerUserId,
      email: keyRequestDevice.authorizerUserEmail,
    });

    yield KRSHelper.createRKINotification(
      notificationType,
      rkiData,
      approvers,
      creator
    );

    yield tryUpdatingKeyRequestSessionToComplete(
      connection,
      keyRequestDevice.keyRequestSessionId
    );
  }).catch(err => {
    throw err;
  });
}

function populateSetColumns(updatingObj) {
  const updatingFields = Object.keys(updatingObj);
  const queryParams = [];
  let setColumns = '';

  updatingFields.forEach((field, index) => {
    if (setColumns.length > 0) {
      setColumns += ', ';
    }
    setColumns += `${_.snakeCase(field)} = $${index + 1}`;
    queryParams.push(updatingObj[field]);
  });

  return { setColumns, queryParams };
}

async function updateKeyRequestSession(
  keyRequestSessionId,
  newKeyRequestSession,
  connection
) {
  const { setColumns, queryParams } = populateSetColumns(newKeyRequestSession);
  const query = `
            UPDATE 
                key_request_session
            SET ${setColumns}
            WHERE key_request_session_id = '${keyRequestSessionId}';
            `;
  try {
    if (connection) {
      await connection.execute(query, queryParams);
    } else {
      await server.db.write.execute(query, queryParams);
    }

    return true;
  } catch (e) {
    logger.error(
      `keyRequestHelper.updateKeyRequestSession(keyRequestSessionId: ${keyRequestSessionId}, newKeyRequestSession: ${newKeyRequestSession.toString()}) error: %s`,
      e
    );
    return false;
  }
}

async function getKeyRequestSessionById(keyRequestSessionId) {
  const query = `
        SELECT krs.*,
        COALESCE(json_agg(json_build_object('keyRequestDeviceId', krd.key_request_device_id, 'status', krd.status)) 
        FILTER (WHERE krd.key_request_device_id IS NOT NULL), '[]') AS key_request_devices,
        (SELECT COALESCE(json_agg(json_build_object('id', iu.id, 'email', iu.email)) 
        FILTER (WHERE iu.id IS NOT NULL), '[]') AS authorizers
        FROM key_request_session_authorizer krsa
        JOIN ics_user iu ON krsa.authorizer_user_id = iu.id
        WHERE krsa.key_request_session_id = $1) AS authorizers,
        kg.certificate_issuer_id,
        ci.issuer_code
        FROM key_request_session krs
        LEFT JOIN key_request_device krd ON krd.key_request_session_id = krs.key_request_session_id
        LEFT JOIN key_group kg ON kg.key_group_ref = krs.key_group_ref AND kg.company_id = krs.company_id
        LEFT JOIN certificate_issuer ci ON ci.certificate_issuer_id = kg.certificate_issuer_id
        WHERE krs.key_request_session_id = $1
        GROUP BY krs.key_request_session_id, kg.certificate_issuer_id, ci.issuer_code;
    `;
  const params = [keyRequestSessionId.toString()];

  try {
    return await server.db.read.row(query, params);
  } catch (e) {
    logger.error(
      `keyRequestHelper.getKeyRequestSessionById(keyRequestSessionId: ${keyRequestSessionId}) error: %s`,
      e
    );
    throw e;
  }
}

async function getKeyRequestDevicesBySessionId(sessionId) {
  try {
    const devices = await server.db.read.rows(
      `
        SELECT krd.*, t.serial_number, t.certificate, t.is_json_certificates, t.key_group_ref FROM key_request_device krd
        JOIN target t on krd.device_id = t.target_id
        WHERE krd.key_request_session_id = '${sessionId}'
      `
    );

    return devices;
  } catch (err) {
    logger.error(
      `keyRequestHelper.getKeyRequestDevicesBySessionId(sessionId: ${sessionId}) error: %s`,
      err
    );
    throw err;
  }
}

async function getKeyRequestDeviceByDeviceId(deviceId, statusList) {
  let whereClause = `WHERE krd.device_id = ${deviceId}`;
  if (Array.isArray(statusList) && statusList.length > 0) {
    const statusListCopy = Array.from(statusList);
    const nullIndex = statusListCopy.findIndex(elem => elem === null);
    statusListCopy.splice(nullIndex, 1);
    let statusFilter = `krd.status IN (${statusListCopy})`;
    if (nullIndex > -1) {
      statusFilter += ' OR krd.status IS NULL';
    }
    whereClause += ` AND (${statusFilter})`;
  }

  const query = `
        SELECT krs.key_request_session_id, krs.status AS key_request_session_status, 
        krs.key_group_ref, krd.key_request_device_id, krd.device_id, krd.status AS key_request_device_status, krs.created
        FROM key_request_device krd
        JOIN key_request_session krs ON krd.key_request_session_id = krs.key_request_session_id
        ${whereClause};
    `;

  try {
    return await server.db.read.rows(query);
  } catch (e) {
    logger.error(
      `keyRequestHelper.getKeyRequestDeviceByDeviceId(deviceId: ${deviceId}, statusList: ${statusList.toString()}) error: %s`,
      e
    );
    throw e;
  }
}

async function cancelPendingKeyRequest(
  keyRequestDeviceId,
  keyRequestSessionId
) {
  const conn = await server.db.write.getConnection();

  try {
    await conn.execute('BEGIN');
    const sm = await krdStateMachine.load(keyRequestDeviceId, conn);
    await sm[krdStateMachine.actions.keyRequestRemoved.name]();
    const pendingKeyReqSession =
      await getKeyRequestSessionById(keyRequestSessionId);
    const removableKRDs = pendingKeyReqSession.keyRequestDevices.filter(
      krd => krd.status === null
    );
    if (pendingKeyReqSession && removableKRDs.length === 1) {
      // If key_request_session has only one device it it, cancel the session
      await updateKeyRequestSession(
        keyRequestSessionId,
        { status: KEY_REQUEST_SESSION_STATUS.CANCELLED },
        conn
      );
    }
    await conn.execute('COMMIT');
  } catch (e) {
    await conn.execute('ROLLBACK');
    logger.error(
      `keyRequestHelper.cancelPendingKeyRequest(keyRequestDeviceId: ${keyRequestDeviceId}, 
                keyRequestSessionId: ${keyRequestSessionId}) error: %s`,
      e
    );
  } finally {
    conn.done();
  }
}
function getExpiredKeyRequestSessions() {
  return server.db.read.rows(
    `SELECT * FROM key_request_session krs
            WHERE (krs.status = ${KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION} 
            OR krs.status = ${KEY_REQUEST_SESSION_STATUS.PENDING_SECOND_AUTHORIZATION}) 
            AND krs.expires_time IS NOT NULL AND krs.expires_time < NOW();`
  );
}

function isAutomatedKRS(keyRequestSession) {
  return (
    keyRequestSession.name &&
    keyRequestSession.name.toString().includes('[Automated]')
  );
}

module.exports = {
  KEY_REQUEST_SESSION_STATUS,
  getKeyRequestSessionById,
  getKeyRequestDevicesBySessionId,
  getKeyRequestDeviceByDeviceId,
  updateKeyRequestSession,
  updateKeyRequestAndSendNotifications,
  cancelPendingKeyRequest,
  isAutomatedKRS,
  getExpiredKeyRequestSessions,
  tryUpdatingKeyRequestSessionToComplete,
};
