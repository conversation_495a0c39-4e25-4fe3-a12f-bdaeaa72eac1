const co = require('co');

const env = require('../env');

const { config } = env;
const AWS = require('../lib/aws');

function uploadLargePayloadToS3(req, device, sqsUrl) {
  return co(function* execute() {
    // upload to s3 bucket+key
    const deviceTime = req.headers['ics-device-current-time'];
    const receivedTime = new Date().toISOString();
    const s3Data = {
      siteId: device.siteId.toString(),
      deviceId: device.deviceId.toString(),
      receivedTime,
      deviceType: device.deviceType.toString(),
      data: req.body,
    };
    if (device.tagIds !== '') {
      s3Data.tagIds = device.tagIds;
    }
    if (deviceTime) {
      s3Data.deviceTime = deviceTime;
    }
    const s3Payload = JSON.stringify(s3Data);
    const key = `${device.deviceId.toString()}-${receivedTime}.json`;
    yield AWS.uploadToS3(
      config.AWS.S3.deviceDataBucket,
      key,
      s3Payload,
      'public-read'
    );
    req.log.debug(`Saved ${key} to S3`);
    const params = {
      MessageBody: JSON.stringify({
        bucket: config.AWS.S3.deviceDataBucket,
        key,
      }),
      QueueUrl: sqsUrl,
      DelaySeconds: 0,
      MessageAttributes: {
        'device-id': {
          DataType: 'String',
          StringValue: device.deviceId.toString(),
        },
        'device-type': {
          DataType: 'String',
          StringValue: device.deviceType.toString(),
        },
        'received-time': { DataType: 'String', StringValue: receivedTime },
        'site-id': {
          DataType: 'String',
          StringValue: device.siteId.toString(),
        },
      },
    };

    if (device.tagIds !== '') {
      params.MessageAttributes['tag-ids'] = {
        DataType: 'String',
        StringValue: device.tagIds.toString(),
      };
    }

    if (deviceTime) {
      params.MessageAttributes['device-time'] = {
        DataType: 'String',
        StringValue: deviceTime,
      };
    }
    return params;
  });
}

module.exports = {
  uploadLargePayloadToS3,
};
