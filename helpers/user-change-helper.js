const co = require('co');

const { server } = require('../app');

const CHANGE_QUERY = `
        INSERT INTO user_change
            ( target_user_id, action, action_user_id, timestamp )
        VALUES
            ( $1, $2, $3, NOW() )
        RETURNING id;
    `;

const FIELD_QUERY = `
        INSERT INTO user_change_field
            ( change_id, field_name, from_value, to_value )
        VALUES
            ( $1, $2, $3, $4 );
    `;

/**
 * Function to record a user change in the database
 *
 * actionUserId is the user who initiated this action
 * changes is an array of fields which were changed { fieldName: 'name', fromValue: '<PERSON>', toValue: 'David' }
 */
function recordUserChange(userId, action, actionUserId, changes, connection) {
  return co(function* execute() {
    let conn = connection;
    let mustCloseConn = false;
    if (!conn) {
      conn = yield server.db.write.getConnection();
      mustCloseConn = true;
    }

    try {
      yield conn.execute('BEGIN');

      const result = yield conn.execute(CHANGE_QUERY, [
        userId,
        action,
        actionUserId,
      ]);

      const changeId = result.rows[0].id;
      for (let i = 0; i < changes.length; i++) {
        const change = changes[i];

        yield conn.execute(FIELD_QUERY, [
          changeId,
          change.fieldName,
          change.fromValue,
          change.toValue,
        ]);
      }

      yield conn.execute('COMMIT');
    } catch (err) {
      server.log.error(err);
      yield conn.execute('ROLLBACK');
      throw err;
    } finally {
      if (mustCloseConn) {
        conn.done();
      }
    }
  });
}

module.exports = {
  recordUserChange,
};
