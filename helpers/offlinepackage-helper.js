const crypto = require('crypto');
const _ = require('lodash');
const co = require('co');
const fs = require('fs-extra');
const streamToPromise = require('stream-to-promise');
const archiver = require('archiver');

const AWS = require('../lib/aws');
const mailer = require('../lib/mailer');
const { server } = require('../app');
const { config } = require('../env');
const { offlinePackage } = require('../lib/app-constants');
const mailerHelper = require('./mailer-helper');

const tmpPkgDirPrefix = '/tmp/offline_package-';
const tmpJbzDirPrefix = '/tmp/jbz-';
const bundleVerFile = `${__dirname}/../resources/offline-package-jbs-templates/bundle.ver`;
const softwareJbsTemplateFile = `${__dirname}/../resources/offline-package-jbs-templates/software.jbs`;
const rkiJbsTemplateFile = `${__dirname}/../resources/offline-package-jbs-templates/rki.jbs`;

let softwareJbsTemplate;
let rkiJbsTemplate;

const CREATE_NOTIFICATION = `INSERT INTO notification
    ( id, user_id, created, read, message, related_entity, type, level )
VALUES
    ( uuid_generate_v1mc(), $1, NOW(), $2, $3, $4, $5, $6 )`;

function getDeviceDir(deviceType) {
  return deviceType.toLowerCase();
}

function isJbzFile(filename) {
  return _.endsWith(filename, '.jbz');
}

function* createArchive(sourceDir, destinationFile, keepSourceDir) {
  const archive = archiver('zip', {
    store: true, // Sets the compression method to STORE
    zlib: {
      level: 0, // No compression
    },
  });

  const output = fs.createWriteStream(destinationFile);

  // listen for all archive data to be written
  output.on('close', () => Promise.resolve());

  archive.on('warning', err => {
    if (err.code === 'ENOENT') {
      server.log.warn(err);
    } else {
      // throw error
      throw err;
    }
  });

  archive.on('error', err => {
    throw err;
  });

  archive.pipe(output);
  archive.directory(sourceDir, false);
  const result = yield archive.finalize();
  server.log.info(`${destinationFile} zip file created`);

  if (!keepSourceDir) {
    yield fs.remove(sourceDir);
  }

  return result;
}

function createSoftwareJbz(software, destination) {
  return co(function* run() {
    const s3Key = `Software Release/${software.id}/${software.softwareFile}`;
    server.log.info(
      `Attempting to download software content for key: ${s3Key}.`
    );

    // TODO: Fix the software bucket and media asset bucket overlapping
    const fileObj = AWS.downloadFromS3({
      Key: s3Key,
      // FIXME: update the hard coded bucket name
      Bucket:
        config.offlinePackage.s3SoftwareBucket ||
        `${process.env.ENVIRONMENT}-ics-tmsfiles`,
    });
    const sha256 = crypto.createHash('sha256');

    if (isJbzFile(software.softwareFile)) {
      return yield AWS.downloadFromS3Stream(fileObj, destination);
    }

    // Create a new .jbz file
    server.log.info(`Creating the jbz file for ${software.softwareFile}`);
    const jbzRootDir = yield fs.mkdtemp(tmpJbzDirPrefix);
    yield fs.mkdir(`${jbzRootDir}/data`);
    yield fs.copy(bundleVerFile, `${jbzRootDir}/bundle.ver`);
    const jbzFileStream = fs.createWriteStream(
      `${jbzRootDir}/data/${software.softwareFile}`
    );

    let shaSum = null;
    try {
      const s3FileStream = fileObj.createReadStream();

      s3FileStream.on('data', d => {
        sha256.update(d);
      });
      s3FileStream.on('end', () => {
        shaSum = sha256.digest('hex');
      });
      s3FileStream.on('error', err => {
        server.log.error('Error stream:', err);
      });

      s3FileStream.pipe(jbzFileStream);
      yield streamToPromise(jbzFileStream);
      yield streamToPromise(s3FileStream);
    } catch (err) {
      server.log.error(err);
      throw err;
    }

    if (!softwareJbsTemplate) {
      softwareJbsTemplate = yield fs.readFile(softwareJbsTemplateFile, 'utf-8');
    }
    const jbs = _.template(softwareJbsTemplate)({
      type: software.deviceType.id.toLowerCase().substr(0, 2),
      fileHash: shaSum,
      fileName: software.softwareFile,
    });
    server.log.info(`Adding main.jbs file for ${software.softwareFile}`);
    yield fs.writeFile(`${jbzRootDir}/main.jbs`, jbs, 'utf-8');

    return yield createArchive(jbzRootDir, destination);
  }).catch(err => {
    throw err;
  });
}

function* createRkiJbz(device, destination) {
  const jbzRootDir = yield fs.mkdtemp(tmpJbzDirPrefix);

  if (!rkiJbsTemplate) {
    rkiJbsTemplate = yield fs.readFile(rkiJbsTemplateFile, 'utf-8');
  }

  if (!device.keyCerts) {
    server.log.warn(`Device ${device.id} has no key certs available.`);
    return;
  }

  const jbs = _.template(rkiJbsTemplate)({
    keyCerts: JSON.stringify(device.keyCerts),
  });
  yield fs.writeFile(`${jbzRootDir}/main.jbs`, jbs, 'utf-8');

  yield createArchive(jbzRootDir, destination);
}

module.exports = {
  *getSoftwareByIdBatch(softwareIds, companyId) {
    const params = _.times(softwareIds.length)
      .map(n => `$${n + 1}`)
      .join(',');
    return yield server.db.read.rows(
      `
            WITH ids (id_list) AS (
                VALUES (array[${params}]::integer[])
            )
            SELECT software_id as id, description, software_file, name, size,
            json_build_object(
                'id', s.device_type,
                'name', cpt.display_name
            ) as device_type
            FROM ids, software s
            LEFT JOIN company_product_type cpt ON 
                cpt.company = s.company_id AND
                cpt.device_type = s.device_type
            WHERE software_id = ANY (ids.id_list) AND
            company_id = $${softwareIds.length + 1} AND
            type = 'software' AND
            active = TRUE
            ORDER BY array_position(ids.id_list, s.software_id);`,
      _.concat(softwareIds, companyId)
    );
  },

  *getDeviceByIdBatch(deviceIds, companyId) {
    const params = _.times(deviceIds.length)
      .map(n => `$${n + 1}`)
      .join(',');
    return yield server.db.read.rows(
      `SELECT target_id as id, t.name, t.description, t.site_id,
            json_build_object(
                'id', t.device_type,
                'name', cpt.display_name
            ) as device_type, t.serial_number, key_certs, rkik.id as key_bundle_id
            FROM target t
            LEFT JOIN site s ON
                t.site_id = s.site_id
            LEFT JOIN company_product_type cpt ON
                cpt.company = s.company_id AND
                cpt.device_type = t.device_type
            LEFT JOIN device_rki_key_bundle rkik ON
                t.target_id = rkik.device_id AND
                rkik.date_created = (SELECT MAX(date_created) FROM device_rki_key_bundle WHERE device_id = t.target_id)                
            WHERE target_id IN (${params}) AND
            s.company_id = $${deviceIds.length + 1} AND
            t.active = TRUE AND t.presence = 'PRESENT'`,
      _.concat(deviceIds, companyId)
    );
  },

  createSoftwarePackage(softwareList, offlinePackageItem, companyId) {
    const notification = {
      message: '',
      path: '/remote/packages',
    };

    let tmpDirRoot;
    return co(function* run() {
      // Create directories
      tmpDirRoot = yield fs.mkdtemp(tmpPkgDirPrefix);
      yield fs.mkdir(`${tmpDirRoot}/vendor`);
      yield fs.mkdir(`${tmpDirRoot}/vendor/ics`);
      yield fs.mkdir(`${tmpDirRoot}/vendor/ics/device`);
      const deviceTypes = _.chain(softwareList)
        .map(software => software.deviceType.id)
        .uniq()
        .value();
      for (let i = 0, len = deviceTypes.length; i < len; i++) {
        yield fs.mkdir(
          `${tmpDirRoot}/vendor/ics/device/${getDeviceDir(deviceTypes[i])}`
        );
        yield fs.mkdir(
          `${tmpDirRoot}/vendor/ics/device/${getDeviceDir(
            deviceTypes[i]
          )}/common`
        );
      }

      yield Promise.all(
        softwareList.map((software, index) =>
          // Check if object exists in S3 before processing
          AWS.downloadMetadataFromS3({
            Key: `Software Release/${software.id}/${software.softwareFile}`,
            Bucket:
              config.offlinePackage.s3SoftwareBucket ||
              `${process.env.ENVIRONMENT}-ics-tmsfiles`,
          }).then(() => {
            let destination = `${tmpDirRoot}/vendor/ics/device/${getDeviceDir(
              software.deviceType.id
            )}/common/${_.padStart(`${index + 1}`, 3, '0')}_${
              software.softwareFile
            }`;
            // ICS-2832 To fix jbz software packaged without .jbz extension
            if (!isJbzFile(software.softwareFile)) {
              destination += '.jbz';
            }
            return createSoftwareJbz(software, destination);
          })
        )
      );

      const filePath = `/tmp/${offlinePackageItem.id}`;
      yield createArchive(tmpDirRoot, filePath);

      const sha256 = crypto.createHash('sha256');
      let shaSum = null;
      const fileStream = fs.createReadStream(filePath);
      fileStream.on('data', d => {
        sha256.update(d);
      });
      fileStream.on('end', () => {
        shaSum = sha256.digest('hex');
      });

      const fileStreamS3 = fs.createReadStream(filePath);
      server.log.info(
        `Uploading offline package ${offlinePackageItem.id} to ${config.offlinePackage.s3OfflinePackageBucket}`
      );

      // TODO: turn this into a params object
      yield AWS.uploadToS3(
        config.offlinePackage.s3OfflinePackageBucket,
        offlinePackageItem.id,
        fileStreamS3,
        'public-read'
      );

      const stat = yield fs.stat(filePath);

      yield fs.unlink(filePath);
      server.log.info(`file ${filePath} deleted`);

      return {
        checksum: shaSum,
        fileSize: stat.size,
      };
    })
      .then(stat =>
        co(function* run() {
          // save the offline package
          const connection = yield server.db.write.getConnection();
          try {
            yield connection.execute('BEGIN');
            yield connection.execute(
              `UPDATE offline_package
                    SET status = 'DONE', date_updated = NOW(), checksum = $1, file_size = $2
                    WHERE id = $3`,
              [stat.checksum, stat.fileSize, offlinePackageItem.id]
            );

            // Create success notification
            server.log.info(
              `Creating success notification for ${offlinePackageItem.name}`
            );
            const relatedEntity = {
              type: 'softwarePackage',
              id: offlinePackageItem.id,
            };

            // Insert into UI notification
            notification.message = `Offline package ${offlinePackageItem.name} is ready for download`;
            yield connection.execute(CREATE_NOTIFICATION, [
              offlinePackageItem.createdBy.id,
              false,
              notification.message,
              relatedEntity,
              offlinePackage.NOTIFICATION_TYPE,
              'SUCCESS',
            ]);

            const { senderEmail } =
              yield mailerHelper.getSenderEmailsByCompanyId(companyId);
            yield mailer.sendNotification(
              { to: offlinePackageItem.createdBy.email, from: senderEmail },
              notification,
              notification.message
            );
            yield connection.execute('COMMIT');
            connection.done();
          } catch (err) {
            server.log.error(err);
            yield connection.execute('ROLLBACK');
            connection.done();
            throw err;
          }

          server.log.info(
            `Offline package ${offlinePackageItem.id}/ ${offlinePackageItem.name}.zip upload successfully in s3 bucket ${config.offlinePackage.s3OfflinePackageBucket}`
          );
        })
      )
      .catch(err => {
        server.log.error(err);
        return co(function* run() {
          // save the offline package
          const connection = yield server.db.write.getConnection();
          try {
            yield connection.execute('BEGIN');
            yield connection.execute(
              `UPDATE offline_package
                        SET status = 'FAILED', date_updated = NOW()
                        WHERE id = $1`,
              [offlinePackageItem.id]
            );

            // Create error notification
            server.log.info(
              `Creating failure notification for ${offlinePackageItem.name}`
            );
            const relatedEntity = {
              type: 'softwarePackage',
              id: offlinePackageItem.id,
            };

            notification.message = `Failed to create offline package ${offlinePackageItem.name}`;
            yield connection.execute(CREATE_NOTIFICATION, [
              offlinePackageItem.createdBy.id,
              false,
              notification.message,
              relatedEntity,
              offlinePackage.NOTIFICATION_TYPE,
              'DANGER',
            ]);

            yield connection.execute('COMMIT');
            connection.done();
          } catch (err1) {
            yield connection.execute('ROLLBACK');
            connection.done();
          }
          server.log.error(
            `Failed to create offline package ${offlinePackageItem.id}`
          );

          // mail failure shouldn't affect job status
          const { senderEmail } =
            yield mailerHelper.getSenderEmailsByCompanyId(companyId);
          yield mailer.sendNotification(
            { to: offlinePackageItem.createdBy.email, from: senderEmail },
            notification,
            notification.message
          );
        }).catch(err1 => {
          server.log.error(err1);
        });
      });
  },

  createRkiPackage(devices, offlinePackageId) {
    let tmpDirRoot;
    return co(function* run() {
      tmpDirRoot = yield fs.mkdtemp(tmpPkgDirPrefix);
      yield fs.mkdir(`${tmpDirRoot}/vendor`);
      yield fs.mkdir(`${tmpDirRoot}/vendor/ics`);
      yield fs.mkdir(`${tmpDirRoot}/vendor/ics/device`);

      const deviceTypes = _.chain(devices)
        .map(device => device.deviceType.id)
        .uniq()
        .value();

      for (let i = 0, len = deviceTypes.length; i < len; i++) {
        yield fs.mkdir(
          `${tmpDirRoot}/vendor/ics/device/${getDeviceDir(deviceTypes[i])}`
        );
      }

      for (let i = 0, len = devices.length; i < len; i++) {
        const deviceType = devices[i].deviceType.id;
        const { serialNumber } = devices[i];
        yield fs.mkdir(
          `${tmpDirRoot}/vendor/ics/device/${getDeviceDir(
            deviceType
          )}/${serialNumber}`
        );
        yield createRkiJbz(
          devices[i],
          `${tmpDirRoot}/vendor/ics/device/${getDeviceDir(
            deviceType
          )}/${serialNumber}/RKI_${serialNumber}.jbz`
        );
      }

      const filePath = `/tmp/${offlinePackageId}`;

      yield createArchive(tmpDirRoot, filePath);

      const sha256 = crypto.createHash('sha256');
      let shaSum = null;
      const fileStream = fs.createReadStream(filePath);
      fileStream.on('data', d => {
        sha256.update(d);
      });
      fileStream.on('end', () => {
        shaSum = sha256.digest('hex');
      });

      const fileStreamS3 = fs.createReadStream(filePath);
      yield AWS.uploadToS3(
        config.offlinePackage.s3OfflinePackageBucket,
        offlinePackageId,
        fileStreamS3,
        'private'
      );

      const stat = yield fs.stat(filePath);

      yield fs.unlink(filePath);

      return {
        fileSize: stat.size,
        checksum: shaSum,
      };
    });
  },
  createArchive,
};
