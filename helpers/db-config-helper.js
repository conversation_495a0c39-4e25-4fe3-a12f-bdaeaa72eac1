const logger = require('../lib/logger').mainLogger();
const { server } = require('../app');

function getConfig(key) {
  return server.db.read.row(
    `
        SELECT *
        FROM ics_config
        WHERE key = $1;
    `,
    [key]
  );
}

function* getValue(key) {
  const config = yield getConfig(key);
  if (!config) {
    throw new Error(`Key not found: ${key}`);
  }
  return config.value;
}

async function getValueJSON(key) {
  try {
    const configRow = await getConfig(key);
    return configRow ? JSON.parse(configRow.value) : undefined;
  } catch (err) {
    logger.error(`Parsing ICS System Config: ${key} Failed`, err);
  }

  return undefined;
}

function setValue(key, value, conn = server.db.write) {
  return conn.execute(
    `
        UPDATE ics_config
        SET value = $1
        WHERE key = $2;
    `,
    [value, key]
  );
}

module.exports = {
  getConfig,
  getValue,
  getValueJSON,
  setValue,
};
