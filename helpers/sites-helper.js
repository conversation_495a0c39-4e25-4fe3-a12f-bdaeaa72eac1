const co = require('co');
const NodeCache = require('node-cache');
const { server } = require('../app');
const env = require('../env');
const C = require('../lib/app-constants');
const { getValueJSON } = require('./db-config-helper');
const { getCompanyLevelJsonValue } = require('./entity-settings-helper');

const enableBluefin = env.config.bluefin && env.config.bluefin.enable;
const BLUEFIN_COMPANY_FLAG_JOIN = enableBluefin
  ? `LEFT JOIN company_feature_flag cff on cff.company = s.company_id and cff.feature_flag = 'BLUEFIN'`
  : '';

const BLUEFIN_COMPANY_FLAG_SELECT = enableBluefin
  ? `, cff.feature_flag as bluefin_flag, s.bluefin_location_id as location_id`
  : '';

const deviceApplicationFilterCache = new NodeCache({
  stdTTL: 60,
});
const WITH_COMPANY_AND_KEY_GROUP = `
        WITH
          company AS (
            SELECT id,
                   name,
                   default_site
            FROM company
          ),
          key_group AS (
            SELECT
              key_group_id     AS "id",
              key_group_name   AS "name",
              key_group_ref    AS "ref",
              json_build_object(
                  'id', c.id,
                  'name', c.name
              ) AS "owner"
            FROM key_group
              JOIN company c ON c.id = key_group.company_id
          )`;

const FIND_SITE_WITH_AUTH_FILTER = `
        SELECT
            s.site_id      AS "id",
            s.name,
            s.address_json AS "address",
            s.phone_number AS "contactPhone",
            s.contact_email,
            s.latitude,
            s.longitude,
            s.formatted_address,
            s.reference_id,
            get_site_health( s.site_id ) as status,
            s.timezone_id,
            s.visible,
            s.hours,
            s.created,
            json_build_object(
                'id', c.id,
                'name', c.name
            ) AS "owner",
            CASE
              WHEN k.id IS NULL THEN NULL
              ELSE row_to_json(k.*)
            END            AS "keyGroup",
            CASE
                WHEN s.site_id = c.default_site THEN TRUE
                ELSE FALSE
            END as "isDefault",
            s.suppress_offhours_alarm,
            s.disable_cm_automatic_deployment as disable_cm_automatic_deployments,
            s.disable_software_downloads as disable_file_downloads
            ${BLUEFIN_COMPANY_FLAG_SELECT}
        FROM site s
            INNER JOIN company c ON s.company_id = c.id
            INNER JOIN user_site_authorization u ON u.site_id = s.site_id AND u.user_id = $1
            LEFT JOIN key_group k ON k.id = s.key_group_id ${BLUEFIN_COMPANY_FLAG_JOIN}`;

function getSiteTags(siteId, companyId) {
  return co(function* execute() {
    return yield server.db.read.rows(
      `
            SELECT t.id, t.name
            FROM site_tag s
                JOIN tag t ON s.tag_id = t.id
            WHERE s.site_id = $1 AND t.company_id = $2 AND s.deleted = false
        `,
      [siteId, companyId]
    );
  });
}

const getExternalReferences = async siteId =>
  // eslint-disable-next-line no-return-await
  await server.db.read.rows(
    `
              SELECT s.reference_id, s.reference_type
                    FROM site_external_references s                
                    WHERE s.site_id = $1
                    AND s.deleted = FALSE
                `,
    [siteId]
  );

function getSiteById(siteId, userId, companyId) {
  return co(function* execute() {
    const site = yield server.db.read.row(
      `
            ${WITH_COMPANY_AND_KEY_GROUP}
            ${FIND_SITE_WITH_AUTH_FILTER}
            WHERE
                s.site_id = $2 AND
                s.active = true;
        `,
      [userId, siteId]
    );

    if (!site) {
      return null;
    }

    site.tags = yield getSiteTags(siteId, companyId);
    site.status = module.exports.translateSiteHealthToNumber(site.status);
    site.externalReferences = yield getExternalReferences(siteId);
    site.allowedExternalReferenceTypes = Object.keys(
      C.sites.ALLOWEDEXTERNALREFERENCETYPES
    );
    server.log.info(
      {
        siteTags: site.tags,
        siteId,
        companyId,
      },
      `[SiteHelper].[GetSiteById] get site succeed.`
    );
    return site;
  });
}

function updateSiteTag(companyId, site) {
  return co(function* execute() {
    site.tags = yield getSiteTags(site.id, companyId); // eslint-disable-line no-param-reassign
    return site;
  });
}

function getTagsForSitesByCompanyId(companyId, sites) {
  return co(function* execute() {
    return yield Promise.all(sites.map(site => updateSiteTag(companyId, site)));
  });
}

function filterTagsByCompany(companyId, arrTags) {
  const result = [];
  arrTags.forEach(({ company_id: compId, ...others }) => {
    if (compId === companyId) {
      result.push(others);
    }
  });
  return result;
}

function filterSiteTagByCompany(companyId, sites) {
  return sites.map(site => {
    const siteTmp = { ...site };

    // filter-out duplicates
    siteTmp.tags = siteTmp.tags
      .filter(
        (tag, index, self) =>
          self.findIndex(t => t.companyId === companyId && t.id === tag.id) ===
          index
      )
      .map(tag => {
        const tagTmp = { ...tag };
        delete tagTmp.companyId;
        return tagTmp;
      });
    return siteTmp;
  });
}

function filterSiteGroupByCompany(companyId, sites) {
  return sites.map(site => {
    const siteTmp = { ...site };

    // filter-out duplicates
    siteTmp.siteGroups = siteTmp.siteGroups
      .filter(
        (sg, index, self) =>
          self.findIndex(t => t.companyId === companyId && t.id === sg.id) ===
          index
      )
      .map(sg => {
        const sgTmp = { ...sg };
        delete sgTmp.companyId;
        return sgTmp;
      });
    return siteTmp;
  });
}

async function getKeyGroupBySiteId(siteId) {
  const FIND_KEY_GROUP_BY_SITE_ID = `
        SELECT kg.key_group_id, kg.key_group_name, kg.key_group_ref, ci.issuer_code
        FROM key_group kg
            LEFT JOIN site s ON s.key_group_id = kg.key_group_id
            LEFT JOIN certificate_issuer ci ON ci.certificate_issuer_id = kg.certificate_issuer_id
        WHERE s.site_id = $1`;

  return server.db.read.row(FIND_KEY_GROUP_BY_SITE_ID, [siteId]);
}

/**
 * @param {Object<string,?any>} device
 */
async function updateDeviceInflight(device) {
  const results = await server.db.read.row(
    `
        SELECT 
            coalesce(krs.status = 5 AND krd.status NOT IN (1, 2, 3) OR krs.status in (0,8), false) AS "inFlight"
        FROM key_request_device krd
        JOIN key_request_session krs ON krs.key_request_session_id=krd.key_request_session_id
        WHERE krs.expires_time > now() AND krd.device_id = $1
        ORDER BY krs.expires_time DESC 
        LIMIT 1`,
    [device.id]
  );

  return Object.assign(device, { inFlight: false }, results);
}

async function getKRSStatusDevice(devices) {
  // eslint-disable-next-line no-return-await
  return await Promise.all(devices.map(device => updateDeviceInflight(device)));
}

function translateSiteHealthToNumber(statusStr) {
  // eslint-disable-next-line no-restricted-syntax
  for (const [k, v] of Object.entries(C.sites.STATUS)) {
    if (statusStr === k) {
      return v;
    }
  }
  return null;
}

function translateSiteHealthToString(statusNum) {
  // eslint-disable-next-line no-restricted-syntax
  for (const [k, v] of Object.entries(C.sites.STATUS)) {
    // eslint-disable-next-line eqeqeq
    if (statusNum == v) {
      return k;
    }
  }
  return null;
}

const getApplicationNameFilter = async ({ companyId, featureFlags }) => {
  if (featureFlags.includes(C.SHOW_APP_VERSIONS)) {
    let globalDeviceApplicationFilter = deviceApplicationFilterCache.get(
      C.DEVICE_APPLCIATION_FILTER_CACHE_KEY
    );
    if (!globalDeviceApplicationFilter) {
      globalDeviceApplicationFilter = await getValueJSON(
        C.deviceApplicationFilter
      );
    }
    if (!globalDeviceApplicationFilter) {
      return undefined;
    }
    deviceApplicationFilterCache.set(
      C.DEVICE_APPLCIATION_FILTER_CACHE_KEY,
      globalDeviceApplicationFilter
    );

    let companyApplicationsEnabled = deviceApplicationFilterCache.get(
      C.COMPANY_ENABLED_APPS_CACHE_KEY
    );
    if (!companyApplicationsEnabled) {
      companyApplicationsEnabled = await getCompanyLevelJsonValue({
        companyId,
        settingName: C.enabledShowAppVersions,
        featureName: C.siteDetails,
      });
      deviceApplicationFilterCache.set(
        C.COMPANY_ENABLED_APPS_CACHE_KEY,
        companyApplicationsEnabled
      );
    }

    if (companyApplicationsEnabled) {
      return globalDeviceApplicationFilter.filter(gda =>
        companyApplicationsEnabled.includes(gda.appName)
      );
    }
    return globalDeviceApplicationFilter;
  }
  return null;
};

const sortAppVersions = (a, b) => {
  const [appVersionA] = Object.keys(a);
  const [appVersionB] = Object.keys(b);
  return appVersionA.localeCompare(appVersionB);
};

const getApplicationVersionOnDevice = async ({
  companyId,
  featureFlags,
  devices,
}) => {
  const deviceApplicationsfilter = await getApplicationNameFilter({
    companyId,
    featureFlags,
  });

  if (!deviceApplicationsfilter) {
    return devices;
  }

  const results = await server.db.read.rows(
    `
      SELECT site_id, device_id, state, value 
      FROM device_versions 
        WHERE state in  (${deviceApplicationsfilter
          .map(a => `'${a.state}'`)
          .join(',')} )
        AND device_id  IN (${devices.map(d => d.id).join(',')})
    `
  );

  const appVersionDevices = devices.map(d => {
    const deviceToAppendAppVersion = d;
    deviceToAppendAppVersion.appVersions = [];
    results.forEach(r => {
      if (d.id === r.deviceId && d.siteId === r.siteId) {
        const appKeyMetric = deviceApplicationsfilter.find(
          akm => akm.state === r.state
        );
        const appVersionObj = {};
        appVersionObj[appKeyMetric.appName] = r.value;
        deviceToAppendAppVersion.appVersions.push(appVersionObj);
      }
    });
    deviceToAppendAppVersion.appVersions.sort(sortAppVersions);
    return deviceToAppendAppVersion;
  });
  return appVersionDevices;
};

module.exports = {
  getSiteById,
  getSiteTags,
  getTagsForSitesByCompanyId,
  filterSiteTagByCompany,
  filterSiteGroupByCompany,
  getKeyGroupBySiteId,
  getKRSStatusDevice,
  updateDeviceInflight,
  translateSiteHealthToNumber,
  translateSiteHealthToString,
  filterTagsByCompany,
  getApplicationVersionOnDevice,
};
