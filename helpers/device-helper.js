const url = require('url');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const _ = require('lodash');
const Joi = require('joi');
const restifyErrors = require('restify-errors');

const axios = require('axios');
const fsPromise = require('fs-extra');
const co = require('co');
const logger = require('../lib/logger').mainLogger();
const { server } = require('../app');
const env = require('../env');
const AWS = require('../lib/aws');
const krdStates =
  require('../src/rki/lib/key-request-device.statemachine').states;
const rolloutService = require('../src/rollout/services/index');
const rolloutHelper = require('../src/rollout/api/rollout-helper');
const C = require('../lib/app-constants');
const CONSTANTS = require('../src/rki/lib/constants');
const rkiHelper = require('./rki-helper');
const sitesHelper = require('./sites-helper');
const keyRequestHelper = require('./key-request-helper');
const jobHelper = require('./job-helper');
const deviceOOSCondition = require('./device-oos-condition.json');
const {
  sendEventProcessDevicesChange,
} = require('./process-device-change-events');

const { createArchive } = require('./offlinepackage-helper');
const { getBridgeAuthToken } = require('./auth-helper');
const { GENERATE_CSR } = require('./job-helper').jobType;

const certloadJBSTemplateFile = `${__dirname}/../resources/jbs-templates/certload.jbs`;
const bundleVerFile = `${__dirname}/../resources/offline-package-jbs-templates/bundle.ver`;

const { triggerEventstoBluefin } = require('./bluefin-helper');

const { KEY_REQUEST_SESSION_STATUS } = keyRequestHelper;

const dottedStringDelimiter = '.';

// eslint-disable-next-line consistent-return
const getSiteTimezone = async siteId => {
  try {
    const currentSite = await server.db.read.row(
      `
      SELECT s.timezone_id FROM site s 
      WHERE s.site_id = $1
    `,
      [siteId]
    );
    return currentSite?.timezoneId;
  } catch (err) {
    logger.error(`deviceHelper.getSiteTimezone(siteId: ${siteId})`, err);
  }
};

const updateSiteTimezone = async (
  currentSiteTimeZoneId,
  destinationSite,
  userId,
  deviceId
) => {
  if (destinationSite.timezoneId === currentSiteTimeZoneId) return;

  const timeZoneAutomation = await server.db.read.row(
    `
      SELECT cff.feature_flag FROM company_feature_flag cff
      JOIN feature_flag ff on cff.feature_flag = ff.code
      WHERE cff.company = $1 
      AND cff.feature_flag = $2
    `,
    [destinationSite.companyId, C.featureFlags.TIMEZONE_AUTOMATION]
  );

  if (timeZoneAutomation?.featureFlag !== C.featureFlags.TIMEZONE_AUTOMATION)
    return;

  // eslint-disable-next-line prefer-const
  let timeConfig = {};
  // eslint-disable-next-line dot-notation
  timeConfig['configUpdate'] = {};
  // eslint-disable-next-line dot-notation
  timeConfig['configUpdate']['cfg.rtc-time-zone'] = destinationSite.timezoneId;
  const conn = await server.db.write.getConnection();
  try {
    await conn.execute(C.dbTransaction.BEGIN);
    const timezoneUpdateJob = await createSysconfigJob(
      conn,
      userId,
      deviceId,
      timeConfig.configUpdate
    );
    // create job for the device while updating time zone
    const now = new Date();
    const expiry = new Date(now.getTime());
    const dependencies = [
      {
        jobId: timezoneUpdateJob.id,
        runOnFail: false,
      },
    ];
    // set to expire in 60 days
    expiry.setDate(now.getDate() + 60);
    const details = {
      destination: 'invenco.system',
      type: 'sys.reboot',
      data: '{}',
      userId,
      deviceId,
      dependencies,
      embargo: now.toISOString(),
      expiry: expiry.toISOString(),
    };
    await jobHelper.createJob(conn, details);
    await conn.execute(C.dbTransaction.COMMIT);
  } catch (err) {
    await conn.execute(C.dbTransaction.ROLLBACK);
    logger.error(
      { err },
      `Updating timezone for the site is failed ${destinationSite.siteId}: ${err}.`
    );
  } finally {
    conn.done();
  }
};

function rewriteLegacyProductType(productTypeName) {
  switch (productTypeName) {
    case 'G6OPT':
      return 'G6-200';
    case 'G7OPT':
      return 'G7-100';
    default:
      return productTypeName;
  }
}

function getDeviceStateHistoryConfig() {
  return JSON.parse(
    fs
      .readFileSync(
        path.join(
          __dirname,
          '../handlers/device/device-state-history.config.json'
        )
      )
      .toString()
  );
}

function getDeviceStateConfig(deviceType) {
  const config = JSON.parse(
    fs
      .readFileSync(
        path.join(__dirname, '../handlers/device/device-state.config.json')
      )
      .toString()
  );
  const matches = config.filter(conf => conf.deviceTypes.includes(deviceType));
  return !deviceType || matches.length === 0 ? config : matches;
}

async function queryDeviceById(userId, deviceId, companyId) {
  // eslint-disable-next-line no-return-await
  return await server.db.read.row(
    `
        SELECT
        t.target_id as id, t.site_id, s.name as site_name, t.last_registered, t.last_contact, t.ip_address, t.name,
        t.description, t.serial_number, t.key_group_ref, t.presence, t.status,
        json_build_object(
            'id', t.device_type,
            'name', cpt.display_name
            ) as device_type
        FROM target t
        JOIN site s ON s.site_id = t.site_id
        JOIN user_site_authorization u ON u.site_id = s.site_id
        JOIN company c ON c.id = s.company_id
        LEFT JOIN company_product_type cpt ON cpt.device_type = t.device_type
        WHERE
            t.active IS true AND
            t.target_id = $1 AND
            u.user_id = $2 AND
            cpt.company = $3
            GROUP BY t.target_id, s.name, cpt.display_name;`,
    [deviceId, userId, companyId]
  );
}

async function markDeviceStatesForDeletion(deviceIds) {
  if (!deviceIds) return false;

  if (env.config.AWS.kinesis) {
    const records = deviceIds.map(id => {
      id = id.toString(); // eslint-disable-line no-param-reassign
      return {
        Data: JSON.stringify({ ACTION: 'DELETE_DEVICE', deviceId: id }),
        PartitionKey: id,
      };
    });

    let index = 0;
    while (index < records.length) {
      const items = records.slice(index, index + 500);
      // eslint-disable-next-line no-await-in-loop
      await AWS.kinesisPutRecords({
        Records: items,
        StreamName: env.config.AWS.kinesis.dataStream,
      });
      index += 500;
    }
  }
  return true;
}

async function sendMoveDeviceMessage(devices) {
  if (!devices) throw new Error('devices are missing');

  if (env.config.AWS.kinesis) {
    const records = devices.map(device => ({
      Data: JSON.stringify({ ACTION: 'MOVE_DEVICE', ...device }),
      PartitionKey: device.deviceId.toString(),
    }));

    let index = 0;
    while (index < records.length) {
      const items = records.slice(index, index + 500);
      // eslint-disable-next-line no-await-in-loop
      await AWS.kinesisPutRecords({
        Records: items,
        StreamName: env.config.AWS.kinesis.dataStream,
      });
      index += 500;
    }
  }

  return true;
}

function getG7TerminalType(serialNumber) {
  const prodTypeTerminalList =
    env.config.g7ProdTypeTerminalSerialPrefixes || [];
  return _.find(prodTypeTerminalList, prefix => serialNumber.startsWith(prefix))
    ? 'PROD'
    : 'VENDEV';
}

function getG6PkiExpiry(pkiLoaded) {
  const expiry = pkiLoaded && /([0-9]){8}(?=:)/.exec(pkiLoaded);
  return (
    expiry &&
    expiry.length > 0 &&
    `${expiry[0].substring(0, 4)}-${expiry[0].substring(
      4,
      6
    )}-${expiry[0].substring(6, 8)} 00:00:00`
  );
}
/**
 *
 * @param {String} deviceSerialNumber
 */
function getDeviceModel(deviceSerialNumber, deviceType = null) {
  const serialNoRegex = /^([BD][AC]|P[AB]|C[ACEIK]|[KLM][ABF])/;
  const nonApplicableProdType = env.config.nonApplicableProdType || [
    'G6-200',
    'IPT',
  ];

  if (deviceType && nonApplicableProdType.indexOf(deviceType) < 0) {
    return serialNoRegex.test(deviceSerialNumber) ? 'VENDEV' : 'PROD';
  }
  return null;
}

const stateTransformations = {
  isMaster: (deviceState, device) => device.serialNumber === deviceState.value,
  isUnicast: deviceState => deviceState.value && deviceState.value.substring(4),
  pkiKeyLoaded: deviceState => deviceState.value === '1',
  pkiExpiry: (deviceState, device) => {
    if (device.deviceType.id === 'G6-200') {
      return getG6PkiExpiry(deviceState.value);
    }
    return deviceState.value;
  },
  terminalType: (deviceState, device) =>
    getDeviceModel(
      device.serialNumber,
      device.deviceType ? device.deviceType.id : null
    ) || deviceState.value,
  dateTime: deviceState => {
    if (!deviceState.value) {
      return null;
    }
    const dt = deviceState.value;
    const deviceTime = new Date(
      dt.substring(0, 4),
      _.parseInt(dt.substring(4, 6)) - 1,
      dt.substring(6, 8),
      dt.substring(8, 10),
      dt.substring(10, 12),
      dt.substring(12, 14)
    );
    const dateTime = new Date(
      deviceTime.getTime() +
        (new Date().getTime() - new Date(deviceState.modified).getTime())
    );
    return dateTime !== 'Invalid Date' && !Number.isNaN(dateTime)
      ? dateTime.toISOString().slice(0, 19)
      : null;
  },
  ksnMismatched: (deviceState, device) =>
    (device.ksn &&
      deviceState.value &&
      !(device.ksn.substring(0, 10) === deviceState.value.substring(0, 10))) ||
    false,
  printerType: (deviceState, device) => {
    if (device.deviceType.id.startsWith('G6')) {
      return 'G6 FUJITSU'; // G6-300 has the same printer as with G6-200
    }
    return deviceState.value;
  },
  default: deviceState => deviceState.value,
};

async function getDeviceConfigs(device, names) {
  const config = {};
  let configList = [];
  const params = [];

  let deviceStateQStr = `
        SELECT state, value, timestamp as modified
        FROM device_states
        WHERE device_id = $1
    `;
  params.push(device.id ?? device.deviceId);

  if (names && names.length > 0) {
    // get specific configs
    // get only the configs from the provided names[]
    configList = getDeviceStateConfig().filter(conf =>
      names.includes(conf.name)
    );
    const configStates = configList.map(item => item.states);
    const flattened = [].concat(...configStates);

    deviceStateQStr += 'AND state = ANY($2::varchar[])';
    params.push(flattened);
  } else {
    // get all configs
    configList = getDeviceStateConfig(device.deviceType.id);
  }

  const deviceStates = await server.db.read.rows(deviceStateQStr, params);
  configList.forEach(state => {
    // get value from DB retrieved data
    const deviceState = state.states
      .map(s => deviceStates.find(ds => ds.state === s))
      .find(s => s != null && s.value != null) || { state: null, value: null }; // set default value

    config[state.name] =
      typeof stateTransformations[state.name] === 'undefined'
        ? stateTransformations.default(deviceState)
        : stateTransformations[state.name](deviceState, device);
  });

  return config;
}

async function updateDeviceAlarmSettingsOnMove(connection, siteId, deviceIds) {
  const alarmSettings = (
    await connection.execute(
      `
        SELECT
            ( SELECT COUNT(*) FROM target WHERE site_id = $1 AND active = true AND presence = 'PRESENT' AND
                target_id NOT IN ( SELECT device_id FROM ics_alarm.alarm_rules_settings WHERE site_id = $1 ) )
                AS unsuspended_device_count,
            COUNT( DISTINCT suspended_from ) AS suspended_from_count,
            COUNT( DISTINCT suspended_until ) AS suspended_until_count,
            ( SELECT json_build_object(
                'suspendedFrom', suspended_from,
                'suspendedUntil' , suspended_until
                )
                FROM ics_alarm.alarm_rules_settings WHERE site_id = $1 LIMIT 1 ) AS window
        FROM
            ics_alarm.alarm_rules_settings
        WHERE
            site_id = $1;`,
      [siteId]
    )
  ).rows[0];

  if (
    alarmSettings &&
    alarmSettings.unsuspendedDeviceCount === 0 &&
    alarmSettings.suspendedFromCount === 1 &&
    alarmSettings.suspendedUntilCount === 1
  ) {
    // all devices in new site have suspension settings with the same suspension window. update the new device accordingly
    let indexes = '';
    for (let i = 4; i < deviceIds.length + 4; i++) {
      indexes = indexes.concat(` ($1, $2, $3, $${i}),`);
    }
    indexes = indexes.substring(0, indexes.length - 1);

    await connection.execute(
      `
            INSERT INTO ics_alarm.alarm_rules_settings (site_id, suspended_from, suspended_until, device_id)
            VALUES ${indexes}
            ON CONFLICT (device_id)
            DO UPDATE SET
                site_id = EXCLUDED.site_id,
                suspended_from = EXCLUDED.suspended_from,
                suspended_until = EXCLUDED.suspended_until;`,
      [
        siteId,
        alarmSettings.window.suspendedFrom,
        alarmSettings.window.suspendedUntil,
        ...deviceIds,
      ]
    );
  } else {
    // reset alarm settings for the moving devices if new site has a mixed alarm settings
    await connection.execute(
      `
            DELETE FROM ics_alarm.alarm_rules_settings
            WHERE device_id = ANY( $1 ); `,
      [deviceIds]
    );
  }
}

async function updateDevicesSiteOnMove(
  conn,
  { toSiteId, deploymentType, meta = {} },
  useId,
  deviceIds
) {
  await conn.execute(
    'UPDATE target SET site_id = $1, last_edited_date = NOW(), deployment_type = $2, updated_by = $3 , meta = $4 WHERE target_id = ANY($5)',
    [toSiteId, deploymentType, useId, meta, deviceIds]
  );
  await conn.execute(
    `
                        UPDATE device_states SET
                            site_id = $1
                            WHERE device_id = ANY($2);
                    `,
    [toSiteId, deviceIds]
  );
  await conn.execute(
    `
                        UPDATE device_versions SET
                            site_id = $1
                            WHERE device_id = ANY($2);
                    `,
    [toSiteId, deviceIds]
  );
  await conn.execute(
    `
                        UPDATE device_alarms SET
                            site_id = $1
                            WHERE device_id = ANY($2);
                    `,
    [toSiteId, deviceIds]
  );
}

function isG7100orG6300(deviceType) {
  return (
    deviceType.toLowerCase() === 'g6-300' ||
    deviceType.toLowerCase() === 'g6-400' ||
    deviceType.toLowerCase() === 'g6-500' ||
    deviceType.toLowerCase() === 'omnia' ||
    deviceType.toLowerCase() === 'win-server' ||
    deviceType.toLowerCase() === 'linux-server' ||
    deviceType.toLowerCase().startsWith('g7')
  );
}

function isG6100orG6200(deviceType) {
  return ['g6-100', 'g6-200'].includes(deviceType.toLowerCase());
}

async function cleanUpKeyRequestDevice(device, destinationKeyGroup) {
  const results = { pending: [] };
  const { approval, retrieving, ready, installing } = krdStates;
  const pendingStates = [approval, retrieving, ready, installing];
  const pendingSessionStatus = [
    KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
    KEY_REQUEST_SESSION_STATUS.APPROVED,
    KEY_REQUEST_SESSION_STATUS.IN_PROGRESS,
    KEY_REQUEST_SESSION_STATUS.PENDING_SECOND_AUTHORIZATION,
  ];

  let pendingKrds = await keyRequestHelper.getKeyRequestDeviceByDeviceId(
    device.id,
    pendingStates
  );
  pendingKrds = pendingKrds.filter(
    krd => pendingSessionStatus.indexOf(krd.keyRequestSessionStatus) > -1
  );

  // Is there any pending rki requests for this device?
  if (pendingKrds && pendingKrds.length > 0) {
    // eslint-disable-next-line no-restricted-syntax
    for (const krd of pendingKrds) {
      // Cancel key_request_device if destination keygroup is not the same w/ the current key_request_device keygroup
      //
      // Else, add device to a list of krds w/ same keygroup
      if (krd.keyGroupRef !== destinationKeyGroup.keyGroupRef) {
        const isCancellable =
          [
            KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
            KEY_REQUEST_SESSION_STATUS.PENDING_SECOND_AUTHORIZATION,
          ].indexOf(krd.keyRequestSessionStatus) > -1;

        // Cancel krds if it's still in approval state
        //
        // Else if same keygroup with destination, add it to pending list
        if (
          isCancellable &&
          krd.keyRequestDeviceStatus === krdStates.approval
        ) {
          // eslint-disable-next-line no-await-in-loop
          await keyRequestHelper.cancelPendingKeyRequest(
            krd.keyRequestDeviceId,
            krd.keyRequestSessionId
          );
        }
      } else if (krd.keyGroupRef === destinationKeyGroup.keyGroupRef) {
        results.pending.push(krd);
      }
    }
  }

  // Sort request by latest timestamp and removed krds except for the last (latest) request
  if (results.pending.length > 1) {
    results.pending.sort((a, b) => b.created - a.created);

    for (let i = results.pending.length - 1; i >= 1; i--) {
      const {
        keyRequestDeviceId,
        keyRequestSessionId,
        keyRequestSessionStatus,
        keyRequestDeviceStatus,
      } = results.pending[i];
      const isCancellable =
        [
          KEY_REQUEST_SESSION_STATUS.PENDING_AUTHORIZATION,
          KEY_REQUEST_SESSION_STATUS.PENDING_SECOND_AUTHORIZATION,
        ].indexOf(keyRequestSessionStatus) > -1;

      // We can only cancel krds if it's still in approval state
      if (isCancellable && keyRequestDeviceStatus === krdStates.approval) {
        // eslint-disable-next-line no-await-in-loop
        await keyRequestHelper.cancelPendingKeyRequest(
          keyRequestDeviceId,
          keyRequestSessionId
        );
        results.pending.splice(i, 1);
      }
    }
  }

  return results;
}

async function updateDeviceTerminalLocation(
  config,
  deviceId,
  siteId,
  state,
  value,
  timestamp
) {
  const result = { succeeded: [], failed: [], ignored: [] };
  logger.info(
    `Updating device terminal location for deviceId: ${deviceId}, siteId: ${siteId}, state: ${state}, value: ${value}`
  );

  const conn = await server.db.write.getConnection();

  try {
    await conn.execute('BEGIN');

    if (
      config.terminalLocation === undefined ||
      config.terminalLocation === null
    ) {
      await conn.execute(
        'INSERT INTO device_states (device_id, site_id, state, value, timestamp) VALUES ($1, $2, $3, $4, $5)',
        [deviceId, siteId, state, value, timestamp]
      );
    } else {
      await conn.execute(
        'UPDATE device_states SET value = $4, timestamp = $5 WHERE device_id = $1 AND site_id = $2 AND state = $3',
        [deviceId, siteId, state, value, timestamp]
      );
    }

    await conn.execute('COMMIT');
    result.succeeded.push(deviceId);
  } catch (e) {
    logger.error(
      `Error updating deviceId: ${deviceId} at siteId: ${siteId} at terminal location: ${state} with value: ${value}`,
      e
    );
    result.failed.push(deviceId);
    await conn.execute('ROLLBACK');
  } finally {
    conn.done();
  }
  return result;
}

async function moveDevices(devices, userId, ACTION) {
  if (!devices) {
    return new Error('Device(s) is required');
  }

  if (Object.prototype.toString.call(devices) === '[object Object]') {
    // eslint-disable-next-line no-param-reassign
    devices = [devices];
  }

  const result = { succeeded: [], failed: [], ignored: [] };
  const sitesToRefresh = [];
  const devicesToRKI = {};
  const bluefinDevices = [];

  if (devices.length < 1) {
    return result;
  }

  logger.info(
    `Devices [${devices.map(elem => elem.id).toString()}] are moving to site: ${
      devices[0].toSiteId
    }`
  );

  // eslint-disable-next-line no-restricted-syntax
  for (const device of devices) {
    let isDeviceMoved = false;
    let conn;
    // eslint-disable-next-line no-await-in-loop
    const currentSiteTimeZoneId = await getSiteTimezone(device.fromSiteId);

    try {
      // eslint-disable-next-line no-await-in-loop
      conn = await server.db.write.getConnection();

      // eslint-disable-next-line no-await-in-loop
      await conn.execute('BEGIN');
      // Device Move
      // eslint-disable-next-line no-await-in-loop
      await updateDeviceAlarmSettingsOnMove(conn, device.toSiteId, [device.id]);
      // eslint-disable-next-line no-await-in-loop
      await updateDevicesSiteOnMove(conn, device, userId, [device.id]);
      // eslint-disable-next-line no-await-in-loop
      await conn.execute('COMMIT');

      sitesToRefresh.push({
        deviceId: device.id.toString(),
        companyId: device.companyId,
        fromSiteId: device.fromSiteId,
        toSiteId: device.toSiteId,
        deploymentType: device.deploymentType,
        meta: device.meta,
      });

      result.succeeded.push(device.id);
      isDeviceMoved = true;

      if (device.bluefinFlag) {
        bluefinDevices.push(device);
      }
    } catch (e) {
      logger.error(
        `deviceHelper.moveDevices(deviceId: ${device.id}) move error: %s`,
        e
      );
      result.failed.push(device.id);
      // eslint-disable-next-line no-await-in-loop
      await conn.execute('ROLLBACK');
    } finally {
      conn.done();
    }

    if (isDeviceMoved) {
      try {
        const siteDetails = {
          siteId: device.toSiteId,
          timezoneId: device.timezoneId,
          companyId: device.companyId,
        };

        if (currentSiteTimeZoneId) {
          // eslint-disable-next-line no-await-in-loop
          await updateSiteTimezone(
            currentSiteTimeZoneId,
            siteDetails,
            userId,
            device.id
          );
        } else {
          logger.debug(
            `[updateDevice][Timezone] No timezone details found for site=${device.fromSiteId}, device time zone update will be skipped`
          );
        }

        // eslint-disable-next-line no-await-in-loop
        const destinationKeyGroup = await sitesHelper.getKeyGroupBySiteId(
          device.toSiteId
        );
        if (destinationKeyGroup) {
          let isRkiRequired =
            device.keyGroupRef !== destinationKeyGroup.keyGroupRef;
          // eslint-disable-next-line no-await-in-loop
          const results = await cleanUpKeyRequestDevice(
            device,
            destinationKeyGroup
          );

          if (results.pending.length) {
            logger.info(
              `Pending key request devices found (device id: ${
                device.id
              }): [${results.pending
                .map(elem => elem.keyRequestDeviceId)
                .toString()}]`
            );
            isRkiRequired = false;
          }

          if (isRkiRequired) {
            devicesToRKI[device.toSiteId] = devicesToRKI[device.toSiteId]
              ? devicesToRKI[device.toSiteId].concat(device.id)
              : [device.id];
          }
        }
      } catch (e) {
        logger.error(
          `deviceHelper.moveDevices(deviceId: ${device.id}) RKI request error: %s`,
          e
        );
      }
    }
  }

  if (Object.keys(devicesToRKI).length > 0) {
    // eslint-disable-next-line no-restricted-syntax
    for (const [siteId, deviceIds] of Object.entries(devicesToRKI)) {
      logger.info(
        `Creating automatic rki request session for device ids: [${deviceIds.toString()}]`
      );
      // send RKI request
      try {
        // eslint-disable-next-line no-await-in-loop
        await rkiHelper.createAutomaticRkiRequestSession(
          userId,
          deviceIds,
          siteId,
          devices.length === 1
            ? rkiHelper.RKI_REASON.MOVE
            : rkiHelper.RKI_REASON.BULKMOVE
        );
      } catch (e) {
        logger.error(
          'rkiHelper.createAutomaticRkiRequestSession() Error: %s',
          e
        );
      }
    }
  }

  if (sitesToRefresh.length > 0) {
    try {
      await sendMoveDeviceMessage(sitesToRefresh);
    } catch (e) {
      logger.error('deviceHelper.sendMoveDeviceMessage() Error: %s', e);
    }
  }
  if (devices.length) {
    try {
      await sendEventProcessDevicesChange(devices.map(device => device.id));
    } catch (e) {
      logger.error(
        { e },
        'deviceHelper.sendEventProcessDevicesChange() Error: %s'
      );
    }
  }

  if (
    bluefinDevices.length > 0 &&
    env.config.bluefin &&
    env.config.bluefin.enable
  ) {
    try {
      await triggerEventstoBluefin(bluefinDevices, ACTION);
    } catch (e) {
      logger.error('deviceHelper.triggerEventstoBluefin() %s', e);
    }
  }
  return result;
}

async function getDeviceStates(deviceId) {
  return (
    (
      await server.db.read.row(
        `
        SELECT json_object_agg(
            ds.state,
            json_build_object('value', ds.value, 'timestamp', ds.timestamp)
        ) as data
        FROM (
            SELECT replace(ds.state, 'invenco.system.', '') state, ds.value, ds.timestamp
            FROM device_states ds
            WHERE device_id = $1
        ) ds
    `,
        [deviceId]
      )
    ).data || {}
  );
}

function getOOSCategoryAndCondition({ name, value }) {
  if (name && value) {
    // eslint-disable-next-line no-restricted-syntax
    for (const obj of deviceOOSCondition) {
      // eslint-disable-next-line no-restricted-syntax
      for (const [k, v] of Object.entries(obj)) {
        if (k === name && v === value) {
          return obj;
        }
        if (k === name && k === 'invenco.system.g6opt.upc-lasttamper-event') {
          return obj;
        }
      }
    }
  } else {
    // eslint-disable-next-line no-restricted-syntax
    for (const obj of deviceOOSCondition) {
      // eslint-disable-next-line no-restricted-syntax
      for (const [k] of Object.entries(obj)) {
        if (k === name) {
          return obj;
        }
        if (k === name && k === 'invenco.system.g6opt.upc-lasttamper-event') {
          return obj;
        }
      }
    }
  }
  return null;
}

async function getDeviceConfigSchemaAndForm() {
  let configSchema = {};
  let configForm = {};
  let bluefinDefaultConfig = {};

  try {
    const res = await server.db.replica.row(`
            SELECT json_object_agg(key, COALESCE(value::json, '{}'::json)) as data
            FROM ics_config ic
            WHERE key = ANY('{config_schema,config_form,bluefin_config}'::varchar[])
        `);
    configForm = res.data.config_form;
    configSchema = res.data.config_schema;
    bluefinDefaultConfig = res.data.bluefin_config;
  } catch (err) {
    logger.error('deviceHelper.getDeviceConfigSchemaAndForm() error: %s', err);
    throw err;
  }

  return { configSchema, configForm, bluefinDefaultConfig };
}

function validateConfig(configSchema, configUpdate) {
  const schema = {};

  Object.keys(configUpdate).forEach(k => {
    if (configSchema[k] && configSchema[k].type === 'string') {
      let base = Joi.string();

      if (configSchema[k].options && configSchema[k].options.enum) {
        base = base.valid(configSchema[k].options.enum);
      }

      schema[k] = base;
    } else if (configSchema[k] && configSchema[k].type === 'number') {
      let base = Joi.number();

      if (!configSchema[k].options) {
        return;
      }

      if (configSchema[k].options.minimum) {
        base = base.min(parseFloat(configSchema[k].options.minimum));
      }

      if (configSchema[k].options.maximum) {
        base = base.max(parseFloat(configSchema[k].options.maximum));
      }

      schema[k] = base;
    }
  });

  const { error } = Joi.validate(configUpdate, Joi.object(schema));

  return error;
}

async function getSysconfigData(deviceId, options = { order: 'DESC' }) {
  let sysConfigs = [];

  try {
    sysConfigs = await server.db.replica.rows(
      `
            SELECT COALESCE( jsh.data, j.data ) AS data,
                CASE
                    WHEN jsh.data ISNULL THEN false
                    ELSE true
                END AS from_device,
                jsh.created_at,
                jsh.status,
                j.device_id,
                j.type,
                j.destination,
                j.created_by
            FROM job j
            INNER JOIN LATERAL (
                SELECT * FROM job_status_history
                WHERE job_id = j.id
                ORDER BY id DESC
                LIMIT 1
            ) jsh on true
            WHERE j.device_id = $1 AND j.type = 'sys.configure'
                AND j.status != 5 AND j.expiry > NOW()
            ORDER by j.created_on ${options.order}
        `,
      [deviceId]
    );

    /* eslint-disable no-param-reassign */
    sysConfigs.forEach(sysConfig => {
      sysConfig.data = JSON.parse(sysConfig.data.replace(/\\"/g, '"'));
      sysConfig.timestamp = sysConfig.data.ts
        ? sysConfig.data.ts
        : new Date(sysConfig.createdAt).toISOString();
      sysConfig.params = _.fromPairs(_.chunk(sysConfig.data.params, 2));
    });
  } catch (err) {
    logger.debug(
      `Error encountered while fetching sys config data [id: ${deviceId}]`,
      err
    );
    throw err;
  }

  return sysConfigs;
}

async function getLatestConfigData(deviceId, configSchema) {
  // replace config data from sysconfig if job is completed or
  // job failed in OPT - partial updates might occur
  const shouldOverride = data => {
    if (
      data.status === jobHelper.jobStatus.COMPLETE ||
      (data.status === jobHelper.jobStatus.FAILED && data.fromDevice)
    ) {
      return true;
    }
    return false;
  };

  const calculatePending = (cfg, data, sysconfig) => {
    if (new Date(data.timestamp) < new Date(sysconfig.timestamp)) {
      if (shouldOverride(sysconfig)) {
        data.value = sysconfig.params[cfg];
        data.timestamp = sysconfig.timestamp;
        delete data.pending; // remove any pending values
      } else {
        data.pending = sysconfig.params[cfg];
      }
    }
  };

  const sysconfigs = await getSysconfigData(deviceId, { order: 'ASC' });
  const deviceStates = await getDeviceStates(deviceId);
  const configData = {};

  // populate `configData` with values in deviceStates based on configSchema params
  Object.keys(configSchema).forEach(cfg => {
    if (deviceStates[cfg]) {
      configData[cfg] = {
        value: deviceStates[cfg].value,
        timestamp: deviceStates[cfg].timestamp,
      };
    }
  });

  sysconfigs.forEach(sysconfig => {
    Object.keys(sysconfig.params).forEach(cfg => {
      // missing in device states but in config schema
      if (!deviceStates[cfg] && configSchema[cfg]) {
        if (!configData[cfg]) {
          configData[cfg] = {
            value: '',
            pending: sysconfig.params[cfg],
            timestamp: sysconfig.timestamp,
          };
          // if sysconfig job completed
          if (shouldOverride(sysconfig)) {
            configData[cfg].value = sysconfig.params[cfg];
            delete configData[cfg].pending;
          }
        } else {
          calculatePending(cfg, configData[cfg], sysconfig);
        }
      } else if (deviceStates[cfg] && configSchema[cfg]) {
        calculatePending(cfg, configData[cfg], sysconfig);
      }
    });
  });

  return configData;
}

async function createSysconfigJob(conn, userId, deviceId, configUpdate) {
  if (_.isEmpty(configUpdate)) {
    return null;
  }

  const data = {
    params: Object.keys(configUpdate).reduce(
      (acc, configName) => [
        ...acc,
        configName,
        String(configUpdate[configName]),
      ],
      []
    ),
  };

  const now = new Date();
  const expiry = new Date(now.getTime());

  // set to expire in 60 days
  expiry.setDate(now.getDate() + 60);

  const result = await jobHelper.createJob(conn, {
    destination: 'invenco.system',
    type: 'sys.configure',
    data,
    userId,
    deviceId,
    expiry: expiry.toISOString(),
    embargo: now.toISOString(),
  });

  return result;
}

async function isDeviceConfigurable(deviceId) {
  const deviceCapability = await server.db.read.row(
    `
        SELECT true as is_configurable
        FROM device_states
        WHERE device_id = $1 AND state = 'invenco.system.caps.system-config' AND value = '1'
    `,
    [deviceId]
  );

  return deviceCapability && deviceCapability.isConfigurable;
}

// device does not need to report capability metric for this list of configs
const deviceCapExceptions = ['db.target.name', 'db.target.description'];

async function canEditConfig(deviceId, userRoles, companyFeatureFlags) {
  const deviceConfigurable = await isDeviceConfigurable(deviceId);
  const userHasRole = roles => {
    if (roles.length === 0) {
      return false;
    }
    // check if user is a member of this role
    // eslint-disable-next-line no-restricted-syntax
    for (const role of roles) {
      const userRole = _.find(userRoles, uRole => uRole === role);
      if (userRole) {
        return true;
      }
    }
    return false;
  };
  const canChangeConfig =
    userHasRole(['COMPANY_ADMIN', 'POWER_USER', 'ANALYST', 'SPECIALIST']) &&
    companyFeatureFlags.indexOf('REMOTE_MANAGEMENT') !== -1;

  return configName => {
    if (
      canChangeConfig &&
      (deviceConfigurable || deviceCapExceptions.includes(configName))
    ) {
      return true;
    }
    return false;
  };
}

async function fetchConfigsFromTarget(deviceId, configSchema) {
  // builds a string for all configs that starts with db.target in the form:
  //
  // 'db.target.<column_name>',
  //    json_build_object('value', <column_name>, 'timestamp', date_updated)
  //
  const selectFields = Object.keys(configSchema)
    .filter(key => key.startsWith('db.target'))
    .map(
      key =>
        `'${key}',json_build_object('value',${
          key.split(/db.target./)[1]
        },'timestamp',date_updated)`
    )
    .join(',');

  const result = await server.db.replica.row(
    `
        SELECT json_build_object(${selectFields}) as data
        FROM target WHERE target_id = $1;
    `,
    [deviceId]
  );

  return result.data;
}

function translateDeviceHealthToNumber(statusStr) {
  // eslint-disable-next-line no-restricted-syntax
  for (const [k, v] of Object.entries(C.devices.STATUS)) {
    if (statusStr === k) {
      return v;
    }
  }
  return null;
}

function translateDeviceHealthToString(statusNum) {
  // eslint-disable-next-line no-restricted-syntax
  for (const [k, v] of Object.entries(C.devices.STATUS)) {
    // eslint-disable-next-line eqeqeq
    if (statusNum == v) {
      return k;
    }
  }
  return null;
}

function getOOSConditionMetrics(arrOOSFilter) {
  const result = [];
  // eslint-disable-next-line no-restricted-syntax
  for (const oos of arrOOSFilter) {
    const [category, condition] = oos.split('|');
    // eslint-disable-next-line no-restricted-syntax
    for (const oosObj of _.filter(deviceOOSCondition, {
      category,
      condition,
    })) {
      // eslint-disable-next-line no-restricted-syntax
      for (const [k, v] of Object.entries(oosObj)) {
        if (k !== 'category' && k !== 'condition') {
          const obj = {};
          obj[k] = v;
          result.push(obj);
        }
      }
    }
  }
  return result;
}

function constructFullNameFromDeviceData(ds, dn) {
  return `${ds}.${dn}`;
}

function doesTranslationPropertyExist(dataIngestionRules, fullName) {
  if (!dataIngestionRules) {
    throw new restifyErrors.NotFoundError(
      'Data ingestion rules not found in the configuration'
    );
  }

  if (!dataIngestionRules.translations) {
    throw new restifyErrors.NotFoundError(
      'Data ingestion rules does not include the translations'
    );
  }

  return dataIngestionRules.translations[fullName];
}

function getDsAndDnFromTranslatedFullName(fullName) {
  const dsAndDnSeperatingDotPosition = 3;
  const tokens = fullName.split(dottedStringDelimiter);
  const dn = tokens
    .splice(dsAndDnSeperatingDotPosition)
    .join(dottedStringDelimiter);
  const ds = tokens.join(dottedStringDelimiter);
  return { ds, dn };
}

function getCompanyNameUsingDeviceDataFullName(fullName) {
  const arrayFirstIndex = 0;
  const positionOfCompanyNameInDottedString = 1;
  return fullName.split(
    dottedStringDelimiter,
    positionOfCompanyNameInDottedString
  )[arrayFirstIndex];
}

function logDeviceDataRegexMismatch(
  fullName,
  regexString,
  deviceId,
  deviceData
) {
  const regex = new RegExp(regexString);
  if (!regex.test(fullName)) {
    logger.debug(
      { invalidDeviceFullName: fullName, deviceId },
      `The fn (Full Name) constructed from the device data for the device id ${deviceId} with payload ${JSON.stringify(
        deviceData
      )} did not match the regex.`
    );
  }
}

function logDeviceDataInvalidCompany(
  fullName,
  validCompanies,
  deviceId,
  deviceData
) {
  const companyName = getCompanyNameUsingDeviceDataFullName(fullName);
  if (!validCompanies[companyName]) {
    logger.warn(
      { invalidCompanyName: companyName, deviceId },
      `The company named ${companyName} for the device id ${deviceId} with payload ${JSON.stringify(
        deviceData
      )} does not exist in the list of valid companies.`
    );
  }
}

function getTranslatedData(dataIngestionRules, fn, deviceData) {
  const translatedFn = dataIngestionRules.translations[fn];
  const { ds, dn } = getDsAndDnFromTranslatedFullName(translatedFn);
  return {
    ...deviceData,
    ds,
    dn,
  };
}

function cacheAndLogInvalidDeviceData(
  fn,
  dataIngestionRules,
  deviceId,
  deviceData,
  deviceDataFullNames
) {
  logDeviceDataRegexMismatch(
    fn,
    dataIngestionRules.regex,
    deviceId,
    deviceData
  );
  // added full name as a key in the cache object for fast lookup
  deviceDataFullNames[fn] = true;
  logDeviceDataInvalidCompany(
    fn,
    dataIngestionRules.validCompanies,
    deviceId,
    deviceData
  );
}

function getMappedDeviceData(
  deviceDataList,
  deviceId,
  deviceDataFullNames,
  dataIngestionRules
) {
  const mappedDeviceDataList = deviceDataList.map(deviceData => {
    const fn = constructFullNameFromDeviceData(deviceData.ds, deviceData.dn);
    if (doesTranslationPropertyExist(dataIngestionRules, fn)) {
      return getTranslatedData(dataIngestionRules, fn, deviceData);
    }
    if (!deviceDataFullNames[fn]) {
      cacheAndLogInvalidDeviceData(
        fn,
        dataIngestionRules,
        deviceId,
        deviceData,
        deviceDataFullNames
      );
    }
    return deviceData;
  });
  return mappedDeviceDataList;
}

function normaliseDottedStrings(
  deviceDataList,
  deviceId,
  deviceDataFullNames,
  normaliseDottedStringsConfig,
  deviceDataIngestionRules
) {
  if (normaliseDottedStringsConfig.enabled) {
    const { dataIngestionRules } = deviceDataIngestionRules;
    return getMappedDeviceData(
      deviceDataList,
      deviceId,
      deviceDataFullNames,
      dataIngestionRules
    );
  }
  return deviceDataList;
}

function getNormalisedDeviceData(
  deviceDataList,
  deviceId,
  deviceDataFullNames,
  normaliseDottedStringsConfig,
  deviceDataIngestionRules
) {
  if (
    normaliseDottedStringsConfig === undefined ||
    normaliseDottedStringsConfig.enabled === undefined
  ) {
    throw new restifyErrors.NotFoundError(
      'Flag to enable or disable normalisation of dotted strings cannot be found in the configuration'
    );
  }
  return normaliseDottedStrings(
    deviceDataList,
    deviceId,
    deviceDataFullNames,
    normaliseDottedStringsConfig,
    deviceDataIngestionRules
  );
}

async function updateFuelPosition(deviceId, fuelPosition, req) {
  try {
    await server.db.write.execute(
      'UPDATE target set fuel_position = $2 where target_id = $1',
      [deviceId, fuelPosition]
    );
  } catch (err) {
    logger.error(
      `deviceHelper.updateFuelPosition(deviceId: ${deviceId}) update error: %s`,
      err
    );
    req.log.error({ err });
  }
}

/**
 * Get all the sites for ScheduledTask
 */
async function getOldSitesSerailNumberForScheduledTask(serialNumbers) {
  try {
    return await server.db.read.rows(
      `select site_id as "oldSiteId", serial_number as "serialNumber" from target where serial_number = ANY($1)`,
      [serialNumbers]
    );
  } catch (err) {
    logger.error(
      `deviceHelper.getOldSitesSerailNumberForScheduledTask(serialNumber: ${serialNumbers}) select error: %s`,
      err
    );
    throw err;
  }
}

function handleSlashes(str) {
  return str.includes('\\') ? str.replace(/\\/g, '') : str;
}

async function validateDevices(deviceSummary, promptSetProfile) {
  // Fetch prompt set profile data
  const promptSetProfileData = await server.db.read.rows(
    `
    SELECT 
    legacy_device_type,
    name,
    main_resolutions,
    aux_resolutions,
    sec_model
    FROM prompt_set_profile 
    WHERE name=$1
    `,
    [promptSetProfile]
  );
  // Check if data is returned
  if (_.isEmpty(promptSetProfileData) || promptSetProfileData?.length === 0) {
    throw new restifyErrors.NotFoundError(
      'No Record found with the matching Profile Name'
    );
  }
  // Extract profile data
  const { auxResolutions, mainResolutions, secModel } = promptSetProfileData[0];

  // Fetch target IDs
  const fetchQuery = auxResolutions
    ? `
  SELECT target_id 
  FROM device_profile
  WHERE main_display_resolution=$1
  AND aux_display_resolution=$2
  AND sec_model=$3
  `
    : `
  SELECT target_id 
  FROM device_profile
  WHERE main_display_resolution=$1
  AND aux_display_resolution IS NULL
  AND sec_model=$2
  `;
  const params = auxResolutions
    ? [mainResolutions, auxResolutions, secModel]
    : [mainResolutions, secModel];

  const targetIds = await server.db.read.rows(fetchQuery, params);

  // Check if no target IDs are found
  if (_.isEmpty(targetIds) || targetIds?.length === 0) {
    throw new restifyErrors.NotFoundError(
      'Did not find any compatible devices'
    );
  }

  // Get target IDs from the result set
  const ids = targetIds.map(row => row.targetId);
  // Filter device summary
  const result = deviceSummary.reduce((acc, deviceObj) => {
    const filteredDevices = deviceObj.devices.filter(device =>
      ids.includes(device.id)
    );
    if (filteredDevices.length > 0) {
      acc.push({
        ...deviceObj,
        devices: filteredDevices,
      });
    }
    return acc;
  }, []);

  return { error: false, deviceSummary: result };
}

const getCertificateIssuerCodes = async deviceId => {
  const device = await server.db.read.row(
    `SELECT certificate, is_json_certificates FROM target WHERE target_id = $1`,
    [deviceId]
  );
  if (!device || !device.certificate) {
    return [];
  }
  if (device.isJsonCertificates) {
    try {
      const certificates = JSON.parse(device.certificate);
      return certificates.map(cert => cert.issuer_code);
    } catch (e) {
      logger.error('Error parsing certificates', e);
      return [];
    }
  }
  return [CONSTANTS.ValidCertificateIssuerCode.LEGACY];
};

const getGeneratedCSRS = async (deviceId, requests) => {
  const connection = await server.db.write.getConnection();
  try {
    await connection.execute(C.dbTransaction.BEGIN);
    const user = await server.db.read.row(
      `
        SELECT id 
        from ics_user 
        WHERE email = $1 and type = $2
      `,
      [env.config.user.email, C.user.TYPE.SYSTEM]
    );

    if (!user) {
      throw new Error('User not found');
    }

    const now = new Date();
    const expiry = new Date(now.getTime());
    // set to expire in 60 days
    expiry.setDate(now.getDate() + 60);
    const jobPayload = {
      deviceId,
      destination: C.job.destination.system,
      type: GENERATE_CSR,
      data: {
        requests,
      },
      embargo: now.toISOString(),
      expiry: expiry.toISOString(),
      userId: user.id,
    };

    await jobHelper.createJob(connection, jobPayload);
    await connection.execute(C.dbTransaction.COMMIT);
  } catch (err) {
    logger.error(
      `[getGeneratedCSRS] Failed to get the generated CSRs : ${err}`
    );
    await connection.execute(C.dbTransaction.ROLLBACK);
    throw err;
  } finally {
    connection.done();
  }
};

const buildRolloutDeviceCertificateBundle = async (
  certificateBundleUploadData,
  deviceId
) => ({
  name: certificateBundleUploadData.name,
  description: certificateBundleUploadData.description,
  softwareId: certificateBundleUploadData.id,
  startTime: Date.now(),
  installWindow: C.certificateBundleRollout.INSTALL_WINDOWS, // Using mininmun default timing provided on ICS UI, i.e. 1hr in ms(can be modified if required)
  installFrequency: C.certificateBundleRollout.INSTALL_FREQUENCY, // Using mininmun default frequency provided on ICS UI, i.e. 15 min in ms
  targets: [
    {
      id: deviceId,
    },
  ],
  sites: [
    {
      id: certificateBundleUploadData.siteId,
    },
  ],
  deploymentPolicy: C.certificateBundleRollout.DEPLOYMENT_POLICY,
  type: certificateBundleUploadData.type,
});

const rolloutDeviceCertificateBundle = async (
  req,
  certificateBundleUploadData,
  deviceId
) => {
  try {
    logger.error(
      `[rolloutDeviceCertificateBundle] rolloutDto: ${deviceId} -- ${JSON.stringify(certificateBundleUploadData)}`
    );
    const request = {
      body: await buildRolloutDeviceCertificateBundle(
        certificateBundleUploadData,
        deviceId
      ),
    };
    const rolloutDto = await rolloutHelper.rolloutDtoFromReq(
      request,
      certificateBundleUploadData.uploadedBy.id,
      certificateBundleUploadData.uploadedBy.email
    );
    const serviceContext = {
      rolloutDto,
      companyId: certificateBundleUploadData.companyId,
      userId: certificateBundleUploadData.uploadedBy.id,
      userRoles: [certificateBundleUploadData.roleName],
      targetIds: [deviceId],
      siteIds: [certificateBundleUploadData.siteId],
      requestId: req.getId(),
      requestBody: req.body,
    };
    return rolloutService.create(serviceContext);
  } catch (error) {
    logger.error(
      `[rolloutDeviceCertificateBundle] Error: Certificate bundle: ${certificateBundleUploadData.id} for device: ${deviceId} rollout has failed.`
    );
    throw error;
  }
};

const uploadDeviceCertificateBundle = async (
  req,
  fileStream,
  filePath,
  fileName,
  deviceId,
  jobId
) => {
  const connection = await server.db.write.getConnection();
  try {
    const softwareType = C.certificateBundleUpload.TYPE;
    const softwareTypeId = C.certificateBundleUpload.SOFTWARE_TYPE.PATCH;
    const description = `SQ-3 to SQ-5 upgrade certificate bundle package for Device-${deviceId}`;
    const created = new Date().toISOString();
    const apiUrl = await url.parse(env.config.url.api);
    const data = await server.db.read.row(
      `SELECT u.id as user_id, u.full_name as user_name, u.email, s.company_id, 
      s.site_id, t.device_type, r.role_name
      from job j 
      inner join ics_user u on u.id = j.created_by 
      inner join user_role ur on ur.user_id = u.id
      inner join role r on r.role_id = ur.role_id
      inner join target t on t.target_id = j.device_id 
      inner join site s on s.site_id = t.site_id
      WHERE j.id = $1 and j.device_id = $2 and r.role_name = 'ICS_SYSTEM';`,
      [jobId, deviceId]
    );
    logger.error(
      `[uploadDeviceCertificateBundle] Data- ${deviceId} - ${JSON.stringify(data)}`
    );
    const { userId, userName, companyId, deviceType, email, siteId, roleName } =
      data;
    const companyDeviceDataQuery = `
              SELECT device_type, display_name AS display_name
              FROM company_product_type pt
              WHERE
                  company = $1 AND
                  device_type = $2;  
          `;
    const companyDeviceData = await server.db.read.row(companyDeviceDataQuery, [
      companyId,
      deviceType,
    ]);
    logger.error(
      `[uploadDeviceCertificateBundle] companDeviceData- ${deviceId} - ${JSON.stringify(companyDeviceData)}`
    );

    if (!companyDeviceData) {
      throw new Error(
        `Invalid product type name ${JSON.stringify(deviceType)}.`
      );
    }

    if (fileName.length > 100) {
      throw new Error('File name is too long (max 100 characters).');
    }

    // Get display name
    const name = `Device-${deviceId}_${companyDeviceData.displayName}`;

    // Check if certificate bundle with same name already exists (we don't allow duplicate names)
    const query = `
                          SELECT *
                          FROM software s
                          WHERE
                              LOWER(s.name) = LOWER($1) AND
                              s.company_id = $2 AND
                              active = TRUE;
                      `;
    const duplicateNameCertificateBundle = await server.db.read.row(query, [
      name,
      companyId,
    ]);

    if (duplicateNameCertificateBundle) {
      logger.error(
        `[uploadDeviceCertificateBundle] Certificate bundle package with name ${name} already exists.`
      );
      throw new Error(
        `Certificate bundle package with name ${name} already exists.`
      );
    }

    // Create new certificate bundle file entry
    await connection.execute(C.dbTransaction.BEGIN);
    const insertQuery = `
                          INSERT INTO software (
                              device_type, description, software_file, name, type, company_id, 
                              date_time_created, uploaded_by, active, software_type_id
                          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                          RETURNING
                              description, company_id, 
                              json_build_object( 
                                    'id', device_type, 
                                    'name', '${companyDeviceData.displayName}'
                                ) AS device_type,
                                software_id as id, name, software_file as certificate_file, type,
                                date_time_created AS uploaded_date;
                      `;
    const insertResult = await connection.execute(insertQuery, [
      deviceType,
      description,
      fileName,
      name,
      softwareType,
      companyId,
      created,
      userId,
      false,
      softwareTypeId,
    ]);
    logger.error(
      `[uploadDeviceCertificateBundle] insertResult- ${deviceId} - ${JSON.stringify(insertResult)}`
    );
    const { 0: certificateBundleData } = { ...insertResult.rows };
    if (certificateBundleData && certificateBundleData.id) {
      logger.error(
        `[uploadDeviceCertificateBundle] Uploading certificate bundle package ${certificateBundleData.id} to ${env.config.offlinePackage.s3SoftwareBucket}`
      );

      let sha256 = null;
      let size = 0;
      const hash = crypto.createHash(C.crypto.SHA256);
      fileStream.on('data', fileData => {
        if (fileData) {
          size += fileData.length;
          hash.update(fileData);
        }
      });

      fileStream.on('end', () => {
        sha256 = hash.digest(C.crypto.HEX);
      });

      fileStream.on('error', async err => {
        logger.error(
          `Error: SHA-256 encryption failed for Device-${deviceId}.`
        );
        await connection.execute(C.dbTransaction.ROLLBACK);
        throw new Error(
          `Error: File encryption for Device: ${deviceId} failed`,
          err
        );
      });

      const params = {
        Key: `Software Release/${certificateBundleData.id}/${fileName}`,
        Body: fileStream,
        ACL: 'public-read',
        Bucket: env.config.offlinePackage.s3SoftwareBucket,
      };
      return await AWS.uploadToS3(
        params.Bucket,
        params.Key,
        params.Body,
        params.ACL
      )
        .then(async () => {
          await fsPromise.unlink(filePath);

          const certificateBundleFileUrl = `${apiUrl.protocol}//${apiUrl.host}/Software%20Release/${certificateBundleData.id}/${fileName}`;

          await connection.execute(
            `
                            UPDATE software
                              SET
                                  active = TRUE,
                                  software_file_url = $1,
                                  size = $2,
                                  software_file_signature = $3
                              WHERE software_id = $4;
                          `,
            [certificateBundleFileUrl, size, sha256, certificateBundleData.id]
          );
          certificateBundleData.certificateFileUrl = certificateBundleFileUrl;
          certificateBundleData.size = size;
          certificateBundleData.uploadedBy = {
            id: userId,
            name: userName,
            email,
          };
          certificateBundleData.deviceId = deviceId;
          certificateBundleData.siteId = siteId;
          certificateBundleData.roleName = roleName;
          logger.error(
            `[uploadDeviceCertificateBundle] Certificate bundle package upload successful for Device-${deviceId} in S3 bucket.`
          );
          await connection.execute(C.dbTransaction.COMMIT);
          return {
            status: C.certificateBundleUpload.UPLOAD_STATUS.SUCCESS,
            data: certificateBundleData,
            message: `Certificate bundle package upload successful for Device-${deviceId}`,
          };
        })
        .catch(async error => {
          logger.error(
            `[uploadDeviceCertificateBundle] Error: Failed to upload certificate bundle package for Device-${deviceId} in S3 bucket.`
          );
          await connection.execute(C.dbTransaction.ROLLBACK);
          throw error;
        });
    }
    logger.error(
      `[uploadDeviceCertificateBundle] Error: Failed to store certificate bundle package details for Device-${deviceId}.`
    );
    await connection.execute(C.dbTransaction.ROLLBACK);
    return {
      status: C.certificateBundleUpload.UPLOAD_STATUS.FAILED,
      data: insertResult,
      message: `Certificate bundle package upload failed for Device-${deviceId}`,
    };
  } catch (err) {
    logger.error(
      `[uploadDeviceCertificateBundle] Error: Certificate bundle package upload failed for Device-${deviceId}`
    );
    await connection.execute(C.dbTransaction.ROLLBACK);
    throw err;
  } finally {
    connection.done();
  }
};

const createJBZBundle = async (
  req,
  certificates,
  deviceId,
  deviceType,
  jobId
) => {
  try {
    const certloadJBSTemplate = await fsPromise.readFile(
      certloadJBSTemplateFile,
      'utf-8'
    );

    const jbs = _.template(certloadJBSTemplate)({
      type: deviceType.toLowerCase().substr(0, 2),
      certificates,
    });

    const paths = {
      tmpDir: '/tmp/jbs',
      destination: '/tmp/JBZbundle',
      fileName: 'SQ3-SQ5_certload.jbz',
      filePath: () => `${paths.destination}/${paths.fileName}`,
    };

    await fsPromise.ensureDir(paths.tmpDir);
    await fsPromise.ensureDir(paths.destination);
    await fsPromise.writeFile(`${paths.tmpDir}/main.jbs`, jbs, 'utf-8');
    await fsPromise.copy(bundleVerFile, `${paths.tmpDir}/bundle.ver`);
    await co(createArchive(paths.tmpDir, paths.filePath()));
    const fileStream = await fsPromise.createReadStream(paths.filePath());

    logger.error(
      `[createJBZBundle] Starting certificate bundle upload for Device: ${deviceId}`
    );
    const uploadResponse = await uploadDeviceCertificateBundle(
      req,
      fileStream,
      paths.filePath(),
      paths.fileName,
      deviceId,
      jobId
    );

    logger.error(
      `[createJBZBundle] uploadResponse: ${deviceId} -- ${JSON.stringify(uploadResponse)}`
    );

    if (
      uploadResponse &&
      uploadResponse.status === C.certificateBundleUpload.UPLOAD_STATUS.SUCCESS
    ) {
      logger.error(
        `[createJBZBundle] Starting certificate bundle rollout for Device: ${deviceId}`
      );
      rolloutDeviceCertificateBundle(req, uploadResponse.data, deviceId);
    }
  } catch (err) {
    logger.error(`[createJBZBundle] Failed to create JBZ bundle : ${err}`);
    throw err;
  }
};

const fetchCertificates = async (req, generatedCSRS, deviceId, jobId) => {
  const { csrs: CSRs } = generatedCSRS;
  const certificates = [];
  const FETCH_STATUS_SUCCESS = 'OK';
  const { endpoint } = env.config.cryptoHub;
  const device = await server.db.read.row(
    `
      SELECT 
      serial_number, device_type
      FROM target
      WHERE target_id = $1
    `,
    [deviceId]
  );

  const token = await getBridgeAuthToken();

  const params = {
    token,
  };

  try {
    await Promise.all(
      CSRs.map(async CSR => {
        // convert base64 encoded CSR to hex format
        const hexEncodedCSR = Buffer.from(CSR.csr, 'base64').toString('hex');
        params.CR = hexEncodedCSR;
        params.CA = CSR.hierarchy === 1 ? 'Invenco-APC-HK' : 'Invenco-APC-TRP';

        const response = await axios.post(
          `${endpoint}/ca/v1/csr/${env.config.cryptoHub.service_id}`,
          params
        );

        const message = response.data?.msg ?? null;
        if (message === FETCH_STATUS_SUCCESS) {
          // convert certificate to base64 encoded format
          const cert = Buffer.from(response?.data?.cert, 'hex').toString(
            'base64'
          );
          certificates.push({
            channel: CSR.channel,
            hierarchy: CSR.hierarchy,
            function: CSR.function,
            certs: [cert],
          });
        } else {
          logger.error(
            `[fetchCertificates] Error: ${JSON.stringify(response.data)}`
          );
          throw new Error(`Error while fetching certificates ${message}`);
        }
      })
    );
    await createJBZBundle(
      req,
      certificates,
      deviceId,
      device?.deviceType,
      jobId
    );
  } catch (err) {
    logger.error(`[fetchCertificates] Error:  ${err}`);
  }
};

const startJBZBundlingToLoadCertificates = async (deviceId, dataValue) => {
  try {
    logger.error(`[getGeneratedCSRS] SEQ3-SEQ5: ${JSON.stringify(dataValue)}`);
    await getGeneratedCSRS(deviceId, JSON.parse(dataValue).requests);
  } catch (err) {
    logger.error(
      `[startJBZBundlingToLoadCertificates] Failed to start JBZ bundling to load certificates: ${err}`
    );
  }
};

module.exports = {
  queryDeviceById,
  rewriteLegacyProductType,
  markDeviceStatesForDeletion,
  updateDeviceAlarmSettingsOnMove,
  getDeviceConfigs,
  getG7TerminalType,
  getG6PkiExpiry,
  getDeviceStateConfig,
  getDeviceStateHistoryConfig,
  updateDevicesSiteOnMove,
  sendMoveDeviceMessage,
  isG7100orG6300,
  isG6100orG6200,
  moveDevices,
  updateDeviceTerminalLocation,
  cleanUpKeyRequestDevice,
  getDeviceStates,
  getDeviceConfigSchemaAndForm,
  validateConfig,
  createSysconfigJob,
  getLatestConfigData,
  getSysconfigData,
  canEditConfig,
  fetchConfigsFromTarget,
  deviceCapExceptions,
  getDeviceModel,
  translateDeviceHealthToNumber,
  translateDeviceHealthToString,
  getOOSCategoryAndCondition,
  getOOSConditionMetrics,
  getNormalisedDeviceData,
  updateFuelPosition,
  getOldSitesSerailNumberForScheduledTask,
  handleSlashes,
  getSiteTimezone,
  updateSiteTimezone,
  validateDevices,
  startJBZBundlingToLoadCertificates,
  fetchCertificates,
  getCertificateIssuerCodes,
};

module.exports.private = {
  logDeviceDataInvalidCompany,
  logDeviceDataRegexMismatch,
  getCompanyNameUsingDeviceDataFullName,
  getDsAndDnFromTranslatedFullName,
  doesTranslationPropertyExist,
  constructFullNameFromDeviceData,
  getTranslatedData,
  cacheAndLogInvalidDeviceData,
  getMappedDeviceData,
  normaliseDottedStrings,
  uploadDeviceCertificateBundle,
};
