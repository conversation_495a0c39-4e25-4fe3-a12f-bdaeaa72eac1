const co = require('co');
const _ = require('lodash');
const Joi = require('joi');
const uuid = require('uuid/v4');
const shortid = require('shortid');
const restify = require('restify');
const beautify = require('js-beautify');

const promptsetRepository = require('../handlers/media/promptsets.repository');
const promptsetService = require('../handlers/media/promptsets.service')(
  promptsetRepository
);
const constants = require('../lib/app-constants');
const { server } = require('../app');
const AWS = require('../lib/aws');
const { config } = require('../env');
const {
  promptstateService,
  promptService,
} = require('../src/media-mgmt/imports.service');
const errors = require('../src/media-mgmt/errors');
const { promptsetRepo } = require('../src/media-mgmt/imports.repo');
const deviceHelper = require('./device-helper');

const fontWeight = [
  'thin',
  'light',
  'regular',
  'medium',
  'semibold',
  'bold',
  'extrabold',
  'black',
  '100',
  '300',
  '400',
  '500',
  '600',
  '700',
  '800',
  '900',
].join('|');

const fontStyles = 'italic|oblique';

const IS_HEX_COLOR_REGEX = /^[0-9A-F]{6}$/i;
const IS_UUID =
  /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

// Regexp that will match font face name ex: Open Sans Bold Italic
// Captures "Bold" in match[1] and "Italic" in [2]
// match[1] is undefined if "Bold" is missing, same with match[2] if "Italic" is missing
const FONT_WEIGHT_STYLE_MATCH_REGEX = new RegExp(
  `(?:.*\\b(${fontWeight})\\b)?(?:.*\\b(${fontStyles})\\b)?`,
  'i'
);
const FONT_WEIGHT_STYLE_REPLACEMENT_REGEX = new RegExp(
  `\\s\\b${fontWeight}\\b|\\s\\b${fontStyles}\\b`,
  'ig'
);

function bumpMinorVersion(version = '0.0.0') {
  return version
    .split('.')
    .map((number, index) => {
      if (index === 1) {
        // Increment minor
        return parseInt(number, 10) + 1;
      }
      if (index === 2) {
        // Reset patch
        return 0;
      }
      return number;
    })
    .join('.');
}

function bumpPatchVersion(version = '0.0.0') {
  return version
    .split('.')
    .map((number, index) => {
      if (index === 2) {
        // Increment patch
        return parseInt(number, 10) + 1;
      }
      return number;
    })
    .join('.');
}

function getSoftKeysMap(deviceType) {
  return co(function* execute() {
    const query = `
                SELECT
                  s.id,
                  s.name,
                  s.device_type,
                  s.side,
                  s.offset
                FROM softkeys AS s
                WHERE device_type = $1;`;

    const result = yield server.db.read.rows(query, [deviceType]);
    const softkeysMap = {};
    // eslint-disable-next-line no-restricted-syntax
    for (const softkey of result) {
      softkeysMap[softkey.id] = softkey;
    }
    return softkeysMap;
  }).catch(err => {
    throw err;
  });
}

function getDayPartsMap(companyId) {
  return co(function* execute() {
    const result = yield server.db.read.rows(
      `
            SELECT
              id,
              name,
              start,
              "end"
            FROM day_part
            WHERE company = $1`,
      [companyId]
    );

    const dayPartMap = {};
    // eslint-disable-next-line no-restricted-syntax
    for (const dayPart of result) {
      dayPartMap[dayPart.id] = dayPart;
    }

    return dayPartMap;
  }).catch(err => {
    throw err;
  });
}

function getStatesMapForPromptSet(promptSetId) {
  return co(function* execute() {
    const result = yield server.db.read.rows(
      `
            SELECT
              st.*
            FROM prompt_set ps
              INNER JOIN prompt_template pt ON ps.prompt_template = pt.id
              INNER JOIN prompt_state st ON pt.id = st.prompt_template
            WHERE
              ps.id = $1`,
      [promptSetId]
    );

    const statesMap = {};
    // eslint-disable-next-line no-restricted-syntax
    for (const state of result) {
      statesMap[state.id] = state;
    }
    return statesMap;
  }).catch(err => {
    throw err;
  });
}

function getPromptTypesMap() {
  return co(function* execute() {
    const result = yield server.db.read.rows(`
            SELECT
              id,
              name,
              secure,
              dynamic_text,
              static_text,
              background_asset,
              background_color,
              font_color,
              font_size,
              video_asset,
              html_asset
            FROM prompt_type
        `);

    const typeMap = {};
    // eslint-disable-next-line no-restricted-syntax
    for (const type of result) {
      typeMap[type.id] = type;
    }
    return typeMap;
  }).catch(err => {
    throw err;
  });
}

function groupLanguageObject(promptSetLanguages, groupKey) {
  return promptSetLanguages.length > 0
    ? promptSetLanguages.reduce((a, v) => ({ ...a, [v[groupKey]]: v }), {})
    : {};
}

function validateAsset(assetId, company, allowedTypes, secure, promptTypeName) {
  return co(function* execute() {
    // TODO should probably implement some kind of asset cache to improve performance
    const asset = yield server.db.read.row(
      `
            SELECT
              a.id,
              a.name,
              a.type,
              p.signed
            FROM asset a
              LEFT JOIN asset_package p ON a.asset_package = p.id
            WHERE
              a.active = TRUE AND
              a.id = $1 AND
              a.company = $2;`,
      [assetId, company]
    );

    server.log.info('THE ASSET: ', asset, allowedTypes);
    if (!asset) {
      return new restify.NotFoundError(`Asset ${assetId} not found`);
    }
    if (allowedTypes.indexOf(asset.type) < 0) {
      return new restify.BadRequestError(
        `Prompt type ${promptTypeName} does not allow asset type ${asset.type}`
      );
    }
    if (secure && !asset.signed) {
      return new restify.BadRequestError(
        `Prompt type ${promptTypeName} requires signed assets`
      );
    }

    // TODO validate asset type (if package has a fixed type)

    return null;
  }).catch(err => {
    throw err;
  });
}

function validatePrompt(prompt, typeMap, company, softKeysMap, deviceType) {
  return co(function* execute() {
    const type = typeMap[prompt.type.id];

    if (!type) {
      return new restify.NotFoundError(
        `Prompt type ${prompt.type.id} not found`
      );
    }

    // Only G7 have a predefined html asset
    if (deviceHelper.isG7100orG6300(deviceType) && type.htmlAsset) {
      if (!prompt.primaryAsset || !prompt.primaryAsset.id) {
        return new restify.BadRequestError(
          `Prompt type ${type.name} requires a html file to be specified`
        );
      }

      server.log.info(
        `Validating HTML asset ${prompt.primaryAsset.id} as type:`,
        type
      );
      const error = yield validateAsset(
        prompt.primaryAsset.id,
        company,
        ['HTML'],
        type.secure,
        type.name
      );
      if (error) return error;

      // having an html asset trumps any other setting
      return null;
    }

    if (type.dynamicText || prompt.staticText) {
      if (prompt.textTop === undefined || prompt.textTop === null) {
        return new restify.BadRequestError(
          'Prompt textTop property has to be specified when staticText or dynamicText are defined/not null'
        );
      }
    }

    if (type.staticText && !prompt.staticText) {
      return new restify.BadRequestError(
        `Prompt type ${type.name} requires static text to be specified`
      );
    }
    if (type.backgroundColor && !prompt.backgroundColor) {
      return new restify.BadRequestError(
        `Prompt type ${type.name} requires a background color to be specified`
      );
    }
    if (type.fontColor && !prompt.fontColor) {
      return new restify.BadRequestError(
        `Prompt type ${type.name} requires a font color to be specified`
      );
    }
    if (type.fontSize && !prompt.fontSize) {
      return new restify.BadRequestError(
        `Prompt type ${type.name} requires a font size to be specified`
      );
    }

    if (type.backgroundAsset) {
      if (!prompt.primaryAsset || !prompt.primaryAsset.id) {
        return new restify.BadRequestError(
          `Prompt type ${type.name} requires a background image to be specified`
        );
      }

      const error = yield validateAsset(
        prompt.primaryAsset.id,
        company,
        ['IMAGE'],
        type.secure,
        type.name
      );
      if (error) return error;
    }

    if (type.videoAsset) {
      if (!prompt.primaryAsset || !prompt.primaryAsset.id) {
        return new restify.BadRequestError(
          `Prompt type ${type.name} requires a video to be specified`
        );
      }

      const error = yield validateAsset(
        prompt.primaryAsset.id,
        company,
        ['VIDEO'],
        type.secure,
        type.name
      );
      if (error) return error;
    }

    const seenSofkeys = {};
    // eslint-disable-next-line no-restricted-syntax
    for (const softkeyAssignment of prompt.softkeys) {
      const softkey = softKeysMap[softkeyAssignment.softkey];
      if (!softkey) {
        return new restify.BadRequestError(
          `Invalid softkey assignment. Softkey with id = ${softkey.id} and name = ${softkey.name} not defined for device type ${deviceType}`
        );
      }
      if (seenSofkeys[softkey.id]) {
        return new restify.BadRequestError(
          `Soft key ${softkey.name} assigned twice."`
        );
      }
      seenSofkeys[softkey.id] = true;
    }
    return null;
  }).catch(err => {
    throw err;
  });
}

function deletePromptSetContent(promptSetId, conn) {
  return co(function* execute() {
    // ALL PROMPT DELETES IN QUERIES BELOW WILL CAUSE CASCADE DELETE ON REFERENCING ROWS IN TABLE softkeys_assignment

    // Delete All Exceptions and Referenced Prompts
    yield conn.execute(
      `
            WITH del_exceptions AS (
                DELETE FROM prompt_exception px
                WHERE EXISTS (
                    SELECT 1
                    FROM prompt_assignment pa
                    WHERE px.prompt_assignment = pa.id
                    AND pa.prompt_set = $1
                )
                RETURNING px.prompt
            )
            DELETE FROM prompt p
            USING del_exceptions dx
            WHERE
            p.id = dx.prompt;
            `,
      [promptSetId]
    );

    // Delete All Prompt Assignments and Default Prompts
    yield conn.execute(
      `
                WITH del_assignments AS (
                    DELETE FROM prompt_assignment pa
                    WHERE pa.prompt_set = $1
                    RETURNING pa.default_prompt AS prompt
                )
                DELETE FROM prompt p
                USING del_assignments da
                WHERE
                p.id = da.prompt;
                `,
      [promptSetId]
    );
  }).catch(err => {
    throw err;
  });
}

function insertPromptSetContent(
  promptSetId,
  user,
  assignments,
  conn,
  deviceType,
  auxResolutions,
  promptSetLanguageSupports = []
) {
  return co(function* execute() {
    const companyId = _.get(user, 'company.id', null);
    const userEmail = _.get(user, 'email', null);
    const userId = _.get(user, 'sub', null);

    if (!companyId || !userEmail || !userId) {
      throw new Error(errors.user.notFound);
    }

    const promptsToCreate = [];
    const softkeyToCreate = [];
    const psoToCreate = [];
    const auxPromptIdsObj = {};
    // eslint-disable-next-line no-restricted-syntax
    for (const assignment of assignments) {
      const { prompts } = assignment;

      // eslint-disable-next-line no-restricted-syntax
      for (const prompt of prompts) {
        const pState = _.get(prompt, 'promptState', null);
        const promptLang = promptSetLanguageSupports.find(
          psls => psls.languageSupportId === prompt.promptSetLanguageId
        );

        // Save Default Prompt
        let promptId = uuid();
        if (prompt.screenOption === 'aux')
          Object.assign(auxPromptIdsObj, {
            ...auxPromptIdsObj,
            [prompt.id]: promptId,
          });

        promptsToCreate.push({
          id: promptId,
          deleted: false,
          prompt_type: prompt.type.id,
          device_type: deviceType,
          company: companyId,
          primary_asset: prompt.primaryAsset ? prompt.primaryAsset.id : null,
          background_color: prompt.backgroundColor,
          font_color: prompt.fontColor,
          font_size: prompt.fontSize,
          static_text: prompt.staticText,
          text_top: prompt.textTop,
          elements: prompt.elements,
          touchmap_id: prompt.touchmap ? prompt.touchmap.id : null,
          prompt_set: promptSetId,
          prompt_state: pState,
          transaction_state: prompt.transactionState || null,
          contactless: prompt.contactless || false,
          thumbnail_url: prompt.thumbnailUrl,
          prompt_set_language_support_id: promptLang
            ? promptLang.promptSetLanguageSupportId
            : null,
          ...(auxResolutions !== null && {
            screen_option: prompt.screenOption,
          }),
          ...(auxResolutions !== null && { aux_prompt: prompt.auxPrompt }),
        });

        let pso;
        if (!pState) {
          pso = yield promptstateService.getPromptStateByPrompt(prompt);
          psoToCreate.push({
            prompt_state_ovr_id: uuid(),
            prompt_id: promptId,
            prompt_state_name: '',
            code: pso.code,
            description: pso.description,
            secure: pso.secure,
            attribs: pso.attribs,
            numeric_input: pso.numericInput,
            dynamic_text: pso.dynamicText,
            soft_keys: pso.softKeys,
            active: true,
            prompt_type: pso.promptType,
            allow_video: pso.allowVideo,
            width: pso.width,
            font_size: pso.fontSize,
            example_text: pso.exampleText,
            created: new Date().toISOString(),
            created_by: userId,
            modified: new Date().toISOString(),
            modified_by: userId,
          });
        }

        // eslint-disable-next-line no-restricted-syntax
        for (const softkeyAssignment of prompt.softkeys) {
          const softkeyAssignmentId = uuid();
          softkeyToCreate.push({
            id: softkeyAssignmentId,
            prompt: promptId,
            softkey: softkeyAssignment.softkey,
            label: softkeyAssignment.label,
            font_color: softkeyAssignment.fontColor,
            font_size: softkeyAssignment.fontSize,
            keycode: softkeyAssignment.keycode,
          });
        }

        // eslint-disable-next-line no-restricted-syntax
        for (const exception of assignment.exceptions) {
          const exceptionPrompt = exception.prompt;
          // eslint-disable-next-line no-loop-func
          const exceptionLang = promptSetLanguageSupports.find(
            psls =>
              psls.languageSupportId === exceptionPrompt.promptSetLanguageId
          );
          if (
            exceptionLang &&
            prompt.promptSetLanguageId !== exceptionPrompt.promptSetLanguageId
          ) {
            continue; // eslint-disable-line no-continue
          }
          // Save Exception Prompt
          promptId = uuid();
          if (exception.screenOption === 'aux')
            Object.assign(auxPromptIdsObj, {
              ...auxPromptIdsObj,
              [exception.id]: promptId,
            });

          promptsToCreate.push({
            id: promptId,
            deleted: false,
            prompt_type: exceptionPrompt.type.id,
            device_type: deviceType,
            company: companyId,
            primary_asset: exceptionPrompt.primaryAsset
              ? exceptionPrompt.primaryAsset.id
              : null,
            background_color: exceptionPrompt.backgroundColor,
            font_color: exceptionPrompt.fontColor,
            font_size: exceptionPrompt.fontSize,
            static_text: exceptionPrompt.staticText,
            text_top: exceptionPrompt.textTop,
            elements: exceptionPrompt.elements,
            touchmap_id: exceptionPrompt.touchmap
              ? exceptionPrompt.touchmap.id
              : null,
            prompt_set: promptSetId,
            prompt_state: pState,
            contactless: exceptionPrompt.contactless || false,
            day_part: exception.dayPart.id,
            transaction_state: exceptionPrompt.transactionState,
            thumbnail_url: exceptionPrompt.thumbnailUrl,
            prompt_set_language_support_id: exceptionLang
              ? exceptionLang.promptSetLanguageSupportId
              : null,
            ...(auxResolutions !== null && {
              screen_option: prompt.screenOption,
            }),
            ...(auxResolutions !== null && { aux_prompt: prompt.auxPrompt }),
          });

          if (pso) {
            psoToCreate.push({
              prompt_state_ovr_id: uuid(),
              prompt_id: promptId,
              prompt_state_name: '',
              code: pso.code,
              description: pso.description,
              secure: pso.secure,
              attribs: pso.attribs,
              numeric_input: pso.numericInput,
              dynamic_text: pso.dynamicText,
              soft_keys: pso.softKeys,
              active: true,
              prompt_type: pso.promptType,
              allow_video: pso.allowVideo,
              width: pso.width,
              font_size: pso.fontSize,
              example_text: pso.exampleText,
              created: new Date().toISOString(),
              created_by: userId,
              modified: new Date().toISOString(),
              modified_by: userId,
            });
          }
        }
      }
    }

    // Replacing old aux prompt ids with new ones
    if (Object.keys(auxPromptIdsObj).length > 0) {
      promptsToCreate.forEach(item => {
        // eslint-disable-next-line no-param-reassign
        if (item.aux_prompt) item.aux_prompt = auxPromptIdsObj[item.aux_prompt];
      });
    }

    // Write Prompts to DB
    yield conn.execute(
      `
            INSERT INTO prompt
            SELECT * FROM jsonb_populate_recordset(NULL::prompt, $1::jsonb)
        `,
      [JSON.stringify(promptsToCreate)]
    );

    // Write SoftKeys to DB
    yield conn.execute(
      `
            INSERT INTO softkey_assignments
            SELECT * FROM jsonb_populate_recordset(NULL::softkey_assignments, $1::jsonb)
        `,
      [JSON.stringify(softkeyToCreate)]
    );

    // Write PSO to DB
    yield conn.execute(
      `
            INSERT INTO prompt_state_override
            SELECT * FROM jsonb_populate_recordset(NULL::prompt_state_override, $1::jsonb)
        `,
      [JSON.stringify(psoToCreate)]
    );
  }).catch(err => {
    throw err;
  });
}

/**
 * Get Prompt Set by ID
 */
async function getPromptSetByID(promptSetId, userId, inConnection) {
  let connection = inConnection;
  let needsToClose = false;

  try {
    if (!connection) {
      connection = await server.db.read.getConnection();
      needsToClose = true;
    }

    const promptSet = (
      await connection.execute(
        `
            with related_users as not materialized (
                SELECT
                    u.id,
                    u.full_name AS name,
                    u.email
                FROM ics_user u
            )
            SELECT
                ps.id,
                ps.name,
                ps.version,
                ps.secure_fingerprint,
                ps.non_secure_fingerprint,
                ps.created,
                ps.company,
                ps.modified,
                ps.status,
                ps.bg,
                ps.prompt_set_profile_name,
                pt.device_type,
                pt.screen_height,
                pt.screen_width,
                ( select software_id
                    from software
                    where related_entity = ps.id::TEXT
                    limit 1 ) as software_id,
                (select to_jsonb(u) from (
                    SELECT *
                    FROM related_users u
                    where u.id = ps.created_by
                ) u) AS created_by,
                (select to_jsonb(u) from (
                    SELECT *
                    FROM related_users u
                    where u.id = ps.modified_by
                ) u) AS modified_by,
                (select to_jsonb(t) from (
                    select
                        pt.id,
                        pt.name as name
                    from prompt_template pt
                    where pt.id = ps.prompt_template
                ) t) AS template,
                ps.prompt_template as prompt_template_id,
                ps.font_color,
                (select to_jsonb(u) from (
                    SELECT *
                    FROM related_users u
                    WHERE u.id = ps.first_approver
                ) u) AS first_approver,
                ps.first_approved_time,
                (select to_jsonb(u) from (
                    SELECT *
                    FROM related_users u
                    WHERE u.id = ps.second_approver
                ) u) AS second_approver,
                ps.second_approved_time,
                exists(
                    select 1
                    from prompt_set_approvers psa
                    where prompt_set_id = ps.id
                      and psa.approver_id = ($2)::uuid
                ) AS is_approver,
                ( select to_jsonb(cps) from (
                    select id, name
                    from prompt_set
                    where id = ps.cloned_from
                ) cps ) cloned_from,
                ( select to_jsonb(rps) from (
                    select id, name
                    from prompt_set
                    where id = ps.root_id
                ) rps ) root
            FROM prompt_set ps
            INNER JOIN LATERAL (
                select
                    device_type,
                    screen_height,
                    screen_width
                from product_type
                where device_type = ps.device_type
            ) pt ON TRUE
            WHERE ps.active
              AND ps.id = ($1)::uuid
        `,
        [promptSetId, userId]
      )
    ).rows[0];

    if (!promptSet) {
      return null;
    }

    // get vendor assets
    const { 0: fisrtRow } = (
      await connection.execute(
        `
            SELECT
                s.software_id as id,
                s.software_file_url as url
            FROM software s
            JOIN company_vendor_assets cva on cva.software = s.software_id
            WHERE cva.company = $1
              AND cva.device_type = $2
        `,
        [promptSet.company, promptSet.deviceType]
      )
    ).rows;
    promptSet.vendorAsset = fisrtRow;

    // get prompts
    const prompts = (
      await connection.execute(
        `
            SELECT
                p.id,
                coalesce(pso.code, s.code) as code,
                p.elements::JSONB,
                p.prompt_state,
                p.transaction_state,
                p.contactless,
                p.thumbnail_url,
                p.screen_option,
                p.aux_prompt,
                ls.language_support_id as prompt_set_language_id,
                ( select to_jsonb(tm) from (
                    select id, areas
                    from touchmap
                    where id = p.touchmap_id
                ) tm ) touchmap,
                ( select coalesce(jsonb_agg(to_jsonb(t)),'[]'::jsonb) from (
                    select
                        sa.font_color "fontColor",
                        sa.font_size "fontSize",
                        sa.keycode,
                        sa.label,
                        sa.softkey
                    from softkey_assignments sa
                    where sa.prompt = p.id
                ) t ) AS softkeys
            FROM prompt p
            LEFT JOIN prompt_state s
                on p.prompt_state = s.id
            LEFT JOIN prompt_state_override pso
                on p.id = pso.prompt_id
            LEFT JOIN prompt_set_language_support psls on p.prompt_set_language_support_id = psls.prompt_set_language_support_id
            LEFT JOIN language_support ls ON psls.language_support_id = ls.language_support_id
            WHERE p.prompt_set = ($1)::uuid
            AND p.day_part IS NULL
            AND p.deleted = false
            ORDER BY code
        `,
        [promptSetId]
      )
    ).rows;

    // get exception prompts
    const exceptions = (
      await connection.execute(
        `
            SELECT
                p.id,
                coalesce(pso.code, s.code) as code,
                p.elements::JSONB,
                p.prompt_state,
                p.transaction_state,
                p.contactless,
                p.thumbnail_url,
                ls.language_support_id as prompt_set_language_id,
                ( select to_jsonb(tm) from (
                    select id, areas
                    from touchmap
                    where id = p.touchmap_id
                ) tm ) touchmap,
                ( select to_jsonb(tm) from (
                    select
                        id,
                        company,
                        name,
                        start,
                        "end",
                        active
                    from day_part
                    where id = p.day_part
                ) tm ) "dayPart",
                (select jsonb_agg(to_jsonb(t)) from (
                    select
                        sa.font_color "fontColor",
                        sa.font_size "fontSize",
                        sa.keycode,
                        sa.label,
                        sa.softkey
                    from softkey_assignments sa
                    where sa.prompt = p.id
                ) t ) AS softkeys
            FROM prompt p
            LEFT JOIN prompt_state s
                on p.prompt_state = s.id
            LEFT JOIN prompt_state_override pso
                on p.id = pso.prompt_id
            LEFT JOIN prompt_set_language_support psls on p.prompt_set_language_support_id = psls.prompt_set_language_support_id
            LEFT JOIN language_support ls ON psls.language_support_id = ls.language_support_id
            WHERE p.prompt_set = ($1)::uuid
              AND p.day_part IS NOT NULL
              AND p.deleted = false;
        `,
        [promptSetId]
      )
    ).rows;

    const states = (
      await connection.execute(
        `
            SELECT 
                coalesce(pso.prompt_state_ovr_id, s.id) as id
                , coalesce(pso.prompt_state_name, s.name) as name
                , coalesce(pso.code,s.code) As code
                , coalesce(pso.description, s.description) as description
                , coalesce(pso.secure, s.secure) as secure
                , coalesce(pso.numeric_input, s.numeric_input) as numeric_input
                , coalesce(pso.dynamic_text,s.dynamic_text) as dynamic_text
                , coalesce(pso.soft_keys, s.soft_keys) as soft_keys
                , coalesce(pso.attribs, s.attribs) as attribs
                , coalesce(pso.prompt_type, s.prompt_type) as prompt_type
                , coalesce(pso.width, s.width) as width
                , coalesce(pso.font_size, s.font_size) as font_size
                , p.screen_option as screen_option
                , p.aux_prompt as aux_prompt
                , '' as sequence
            FROM prompt_set ps
            INNER JOIN prompt p
                on ps.id = p.prompt_set
            LEFT JOIN prompt_state s
                on p.prompt_state = s.id
            LEFT JOIN prompt_state_override pso
                on p.id = pso.prompt_id
            WHERE p.prompt_set = $1
            AND p.day_part is null
            ORDER BY code
        `,
        [promptSetId]
      )
    ).rows;

    const codeVisited = {};
    const statesDistinct = states
      .map(state => {
        if (!codeVisited[state.code]) {
          codeVisited[state.code] = true;
          return state;
        }
        return null;
      })
      .filter(state => state !== null);

    // state, prompt, exceptions
    let assignments;
    if (prompts.length === 0) {
      assignments = [];
    } else {
      assignments = statesDistinct
        .map(state => {
          const defaultPrompts = prompts.filter(i => i.code === state.code);

          // An old promptset trying to match a new prompt state
          if (!defaultPrompts || defaultPrompts.length <= 0) {
            return null;
          }

          // eslint-disable-next-line no-restricted-syntax
          for (const prompt of defaultPrompts) {
            prompt.type = { id: 1 };
          }

          const exceptionsLocal = _(exceptions)
            .filter({ code: state.code })
            .map(exception => {
              exception.type = { id: 1 }; // eslint-disable-line no-param-reassign
              return {
                prompt: exception,
                dayPart: exception.dayPart,
              };
            })
            .value();
          return {
            state,
            prompts: defaultPrompts,
            exceptions: exceptionsLocal,
          };
        })
        .filter(item => item !== null);
    }

    promptSet.assignments = assignments;

    // Get Prompt Set Language Support
    const promptSetLanguages =
      await promptsetService.getPromptSetLanguageById(promptSetId);
    promptSet.lang = groupLanguageObject(promptSetLanguages, 'isoCode');

    return promptSet;
  } finally {
    if (needsToClose) {
      connection.done();
    }
  }
}

/**
 * @deprecated since R2022.2.1
 * @param {*} elements
 * @returns {null}
 */
function findThumbnailableAsset(elements) {
  // Lookup first image element
  const image = elements.find(element => element.type === 'image');

  if (image && image.value) {
    return image.value;
  }

  const bg = elements.find(element => element.type === 'bg');

  if (bg && bg.value && !IS_HEX_COLOR_REGEX.test(bg.value)) {
    return bg.value;
  }

  return null;
}

function getUniquePromptAssets(promptSet) {
  let totalAssets = [];

  function getAssetsFromElements(prompt) {
    const assets = [];
    prompt.elements.forEach(element => {
      if (element.type === 'image' || element.type === 'video') {
        assets.push(element.value);
      } else if (element.type === 'bg') {
        if (!/^[0-9A-F]{6}$/i.test(element.value)) {
          assets.push(element.value);
        }
      }
    });
    return assets;
  }

  promptSet.assignments.forEach(assignment => {
    // eslint-disable-next-line no-restricted-syntax
    for (const prompt of assignment.prompts) {
      totalAssets = [...totalAssets, ...getAssetsFromElements(prompt)];
    }

    assignment.exceptions.forEach(exception => {
      totalAssets = [
        ...totalAssets,
        ...getAssetsFromElements(exception.prompt),
      ];
    });
  });

  return _.uniqWith(totalAssets, (curr, prev) => curr === prev);
}

// ==========================
// We need this because UI expects a tree structure
// ( We're not replacing getPromptSetByID because that structure is better for our db queries )
// ==========================
function toUIPromptSet(
  promptSet,
  assets = null,
  softkeys = null,
  keycodes = null
) {
  const modifiedPromptSet = { ...promptSet }; // clone first, no side effects

  function addTouchmapCodeNames(touchmap) {
    const modifiedTouchmap = { ...touchmap };

    modifiedTouchmap.areas = touchmap.areas.map(area => {
      if (area.keyCode) {
        // eslint-disable-next-line no-param-reassign
        area.keyCodeName = _.result(
          _.find(keycodes, { code: area.keyCode }),
          'name'
        ); // eslint-disable-line no-param-reassign
      }

      if (area.softkeyId) {
        // eslint-disable-next-line no-param-reassign
        area.softkeyName = _.result(
          _.find(softkeys, { id: parseInt(area.softkeyId, 10) }),
          'name'
        ); // eslint-disable-line no-param-reassign
      }

      return area;
    });

    return modifiedTouchmap;
  }

  // Get all asset references and preload their data
  function addAssetMetadataToElements(prompt) {
    return prompt.elements.map(element => {
      const newElement = { ...element };
      const asset = assets && assets.find(val => val.id === newElement.value);
      if (asset) {
        newElement.filename = asset.filename;
        newElement.name = asset.name;
      }
      return newElement;
    });
  }

  // Re-structure the promptSet object so its easy to display in the UI
  modifiedPromptSet.states = promptSet.assignments.map(assignment => {
    const assignments = [];

    // assign a stateId to the prompt itself so it's easier to lookup
    const defaultPrompts = assignment.prompts.map(prompt => {
      const defaultPrompt = Object.assign(prompt, {
        parentId: assignment.state.id,
        type: 'default',
      });

      defaultPrompt.elements = addAssetMetadataToElements(defaultPrompt);

      if (defaultPrompt.touchmap) {
        defaultPrompt.touchmap = addTouchmapCodeNames(defaultPrompt.touchmap);
      }
      return defaultPrompt;
    });

    assignments.push(...defaultPrompts);
    // eslint-disable-next-line no-restricted-syntax
    for (const exceptionAssignment of assignment.exceptions) {
      const exceptionPrompt = Object.assign(exceptionAssignment.prompt, {
        parentId: assignment.state.id,
        dayPart: exceptionAssignment.dayPart,
        type: 'exception',
      });

      exceptionPrompt.elements = addAssetMetadataToElements(exceptionPrompt);

      if (exceptionPrompt.touchmap) {
        exceptionPrompt.touchmap = addTouchmapCodeNames(
          exceptionPrompt.touchmap
        );
      }
      assignments.push(exceptionPrompt);
    }

    return {
      id: assignment.state.id,
      code: assignment.state.code,
      description: assignment.state.description,
      screenOption: assignment.state.screenOption,
      auxPrompt: assignment.state.auxPrompt,
      secure: assignment.state.secure,
      sequence: assignment.state.sequence,
      attribs: assignment.state.attribs,
      exampleText: assignment.state.exampleText,
      numericInput: assignment.state.numericInput,
      softKeys: assignment.state.softKeys,
      dynamicText: assignment.state.dynamicText,
      promptType: assignment.state.promptType,
      // TODO: might need to refactor promptset-state to its own table
      transactionState: defaultPrompts[0].transactionState,
      assignments,
    };
  });
  delete modifiedPromptSet.assignments;
  return modifiedPromptSet;
}

// ===================================
// Prepopulate prompts with defaults
// TODO: prevent default elements from overlapping
// ===================================
function createDefaultElements(state, template, promptSetProfile) {
  function generateDefaultPosition(width, screenWidth, screenHeight) {
    let left;
    if (width <= 251) {
      left = screenWidth / 2;
    } else if (width > 250 && width <= 0.66 * screenWidth) {
      left = screenWidth / 3;
    } else {
      left = 20;
    }

    return {
      top: screenHeight / 3,
      width,
      left,
    };
  }

  const elements = [];
  const isG6100orG6200 = deviceHelper.isG6100orG6200(
    promptSetProfile.legacyDeviceType
  );
  const isG7100orG6300 = deviceHelper.isG7100orG6300(
    promptSetProfile.legacyDeviceType
  );

  const defaultPosition = generateDefaultPosition(
    state.width,
    promptSetProfile.screenWidth,
    promptSetProfile.screenHeight
  );

  function createTextElement(size, value, type, line) {
    const elementLeft =
      defaultPosition.left - defaultPosition.width / 2 > 20
        ? defaultPosition.left - defaultPosition.width / 2
        : 20;
    const textElm = {
      id: shortid.generate(),
      type,
      value,
      color: template.defaultFontColor,
      top:
        promptSetProfile.screenHeight +
        20 -
        promptSetProfile.screenHeight * (1 / line),
      left: elementLeft,
      width: defaultPosition.width,
    };
    if (isG7100orG6300) {
      textElm.textAlign = 'center';
      textElm.face = 'Liberation Sans';
      textElm.size = size * 2;
    } else {
      textElm.face = 'FreeSans';
      textElm.size = size;
    }

    return textElm;
  }

  elements.push({
    id: shortid.generate(),
    type: 'bg',
    value: template.defaultBg,
    lock: false,
  });

  // G6 secure assets cannot have texts
  let line = 0;
  if (!(isG6100orG6200 && state.secure)) {
    // Default text element if there's one
    if (state.exampleText) {
      elements.push(
        createTextElement(
          state.fontSize,
          state.exampleText,
          'text',
          (line += 1)
        )
      );
    }

    // default dynamic text
    if (state.dynamicText) {
      elements.push(createTextElement(24, '{{Message}}', 'text', (line += 1)));
    }
  }

  // default input field
  if (state.numericInput) {
    elements.push(createTextElement(24, '????', 'input', (line += 1)));
  }

  return elements;
}

function getClassRegexErrorMessage() {
  const errorMsg = 'Invalid class name';
  return new Error(errorMsg);
}

function getSchema(deviceType, keyCodes) {
  const isG7100orG6300 = deviceType
    ? deviceHelper.isG7100orG6300(deviceType)
    : false;
  const validClassesRegex =
    /^([a-zA-Z][a-zA-Z0-9_-]+\s)*[a-zA-Z][a-zA-Z0-9_-]+$/;
  return {
    bg: Joi.object().keys({
      id: Joi.string().optional(),
      type: Joi.string(),
      value: [Joi.string().regex(IS_HEX_COLOR_REGEX), Joi.string().guid()],
      lock: Joi.boolean(),
    }),
    text: Joi.object().keys({
      id: Joi.string().optional(),
      type: Joi.string(),
      userclass: Joi.string()
        .optional()
        .allow('')
        .regex(validClassesRegex)
        .error(getClassRegexErrorMessage()),
      value: Joi.string(),
      face: Joi.string(),
      bold: Joi.boolean().optional(),
      italic: Joi.boolean().optional(),
      size: Joi.number(),
      color: Joi.string().regex(IS_HEX_COLOR_REGEX),
      top: Joi.number(),
      left: Joi.number(),
      width: Joi.number().optional(),
      height: Joi.number().optional(),
      textAlign: deviceType && isG7100orG6300 ? Joi.string() : Joi.optional(),
    }),
    input: Joi.object().keys({
      id: Joi.string().optional(),
      type: Joi.string(),
      userclass: Joi.string()
        .optional()
        .allow('')
        .regex(validClassesRegex)
        .error(getClassRegexErrorMessage()),
      top: Joi.number(),
      left: Joi.number(),
      size: Joi.number(),
      color: Joi.string(),
      face: Joi.string(),
      width: Joi.number().optional(),
      height: Joi.number().optional(),
      textAlign: deviceType && isG7100orG6300 ? Joi.string() : Joi.optional(),
      value: Joi.string().optional(),
    }),
    image: Joi.object().keys({
      id: Joi.string().optional(),
      type: Joi.string(),
      userclass: Joi.string()
        .optional()
        .allow('')
        .regex(validClassesRegex)
        .error(getClassRegexErrorMessage()),
      value: Joi.string().guid(),
      top: Joi.number(),
      left: Joi.number(),
      size: Joi.number().optional(),
      width: Joi.number().optional(), // used by ui to show border on image
      height: Joi.number().optional(), // used by ui to show border on image
    }),
    clip: Joi.object().keys({
      id: Joi.string().optional(),
      type: Joi.string(),
      userclass: Joi.string()
        .optional()
        .allow('')
        .regex(validClassesRegex)
        .error(getClassRegexErrorMessage()),
      value: Joi.string().guid(),
      top: Joi.number(),
      left: Joi.number(),
      loop: Joi.number().optional(),
      vol: Joi.number().optional(),
    }),
    video: Joi.object().keys({
      id: Joi.string().optional(),
      type: Joi.string(),
      userclass: Joi.string()
        .optional()
        .allow('')
        .regex(validClassesRegex)
        .error(getClassRegexErrorMessage()),
      value: Joi.string().guid(),
      loop: Joi.number().optional(),
      vol: Joi.number().optional(),
      top: Joi.number().optional(),
      left: Joi.number().optional(),
      width: Joi.number().optional(),
      height: Joi.number().optional(),
      size: Joi.number().optional(),
    }),
    area: Joi.object().keys({
      id: Joi.string().optional(),
      type: Joi.string(),
      shape: Joi.string().valid('rect', 'circle'),
      coords: Joi.alternatives().when('shape', {
        is: 'rect',
        then: Joi.string().regex(/^[0-9]+,[0-9]+,[0-9]+,[0-9]+$/),
        otherwise: Joi.string().regex(/^[0-9]+,[0-9]+,[0-9]+$/),
      }),
      alt: Joi.string(),
      keyCode: keyCodes
        ? Joi.string().valid(keyCodes).optional()
        : Joi.string(),
    }),
  };
}

function stripProperties(elements) {
  const schemaMap = getSchema(); // Assume this returns an object mapping types to schemas

  return elements
    .filter(element => !!schemaMap[element.type]) // Keep only elements with a valid type
    .map(element => {
      const schema = schemaMap[element.type];
      return Joi.validate(element, schema, {
        presence: 'required',
        stripUnknown: true,
      }).value;
    });
}

/**
 * @param {*} classValue
 * @param {*} isSecure
 * @param {*} deviceType
 */
function throwErrorIfClassIsNotApplicable(classValue, isSecure, deviceType) {
  const classNotSupportedByDeviceErrorMsg =
    'Current device type does not support class attribute';
  const classNotAllowedOnSecuredPromptErrorMsg =
    'Classes are not allowed in secure prompt';
  const isClassPresent = classValue === '' || classValue;
  // This business logic is based on the acceptance criteria mentioned on REQ-3698
  if (isClassPresent && !deviceHelper.isG7100orG6300(deviceType)) {
    throw new restify.errors.BadRequestError(classNotSupportedByDeviceErrorMsg);
  }

  // This business logic is based on the acceptance criteria mentioned on REQ-3698
  if (isClassPresent && deviceHelper.isG7100orG6300(deviceType) && isSecure) {
    throw new restify.errors.BadRequestError(
      classNotAllowedOnSecuredPromptErrorMsg
    );
  }
}

function validateLegacyPromptRequirements(
  prompt,
  promptState,
  elements,
  inputElements
) {
  if (inputElements.length > 0) {
    if (!promptState.numericInput) {
      throw new restify.errors.BadRequestError({
        body: {
          code: 'BadRequestError',
          elementId: inputElements[0].id,
          message:
            'Prompt type does not allow numeric input but element was found',
        },
      });
    }
  } else if (promptState.numericInput) {
    throw new restify.errors.BadRequestError(
      'Prompt type requires numeric input but none found'
    );
  }

  const videoElement = elements.find(element => element.type === 'video');

  if (videoElement && !promptState.allowVideo) {
    throw new restify.errors.BadRequestError({
      body: {
        code: 'BadRequestError',
        elementId: videoElement.id,
        message: 'Prompt type does not allow video but element was found',
      },
    });
  }

  if (!promptState.softKeys && prompt.softkeyAssignments) {
    throw new restify.errors.BadRequestError(
      'Prompt type does not allow softkeys but assignment was found'
    );
  }
}

function validateNewPromptRequirements(
  prompt,
  promptState,
  elements,
  inputElements
) {
  if (promptState.promptType === 'data' || promptState.promptType === 'pin') {
    if (!inputElements.length) {
      throw new restify.errors.BadRequestError(
        'Prompt type requires numeric input but none found'
      );
    }
  }
}

async function validate(elements, prompt, promptState, boundary) {
  let keyCodes = [];
  if (prompt.areas && prompt.areas.length) {
    keyCodes = await server.db.read.rows('SELECT k.code FROM keycodes k', []);

    if (keyCodes && keyCodes.length) {
      keyCodes = keyCodes.map(keyCode => keyCode.code);
    }
  }

  const schemaMap = getSchema(prompt.deviceType, keyCodes);

  elements.forEach(element => {
    if (!element) {
      return;
    }

    const schema = schemaMap[element.type];
    if (!schema) {
      return;
    }

    const { error } = Joi.validate(element, schema, {
      presence: 'required',
      allowUnknown: true,
    });
    if (error) {
      throw new restify.errors.BadRequestError({
        body: {
          code: 'BadRequestError',
          message: error.message,
          elementId: element.id,
          context: error.details,
        },
      });
    }

    // secure prompts in G6 cannot contain text !!MAX SECURITY!!
    if (
      element.type === 'text' &&
      promptState.secure &&
      deviceHelper.isG6100orG6200(prompt.deviceType)
    ) {
      throw new restify.errors.BadRequestError({
        body: {
          code: 'BadRequestError',
          elementId: element.id,
          message: 'Device type does not allow text elements in secure prompts',
        },
      });
    }

    // make sure texts are within boundaries
    if (element.type === 'text' || element.type === 'input') {
      const { width, height } = boundary;
      const bounds = { x: 0, y: 0, width, height };

      if (
        element.top < bounds.y ||
        element.left < bounds.x ||
        element.top + element.size > bounds.height ||
        element.left + element.width > bounds.width
      ) {
        throw new restify.errors.BadRequestError({
          body: {
            code: 'BadRequestError',
            elementId: element.id,
            message: `Element "${element.type}" is out of bounds`,
          },
        });
      }
    }

    throwErrorIfClassIsNotApplicable(
      element.userclass,
      promptState.secure,
      prompt.deviceType
    );
  });

  const dynamicTextElement = elements.find(element => {
    const HAS_TEMPLATE_REGEX = /{{.+}}/;
    return element.type === 'text' && HAS_TEMPLATE_REGEX.test(element.value);
  });

  if (!dynamicTextElement && promptState.dynamicText) {
    throw new restify.errors.BadRequestError(
      'Prompt type requires dynamic text but none found'
    );
  }

  const inputElements = elements.filter(element => element.type === 'input');

  if (inputElements.length && inputElements.length > 1) {
    throw new restify.errors.BadRequestError(
      'Prompt cannot have multiple input elements'
    );
  } else if (promptState.numericInput && !inputElements.length) {
    throw new restify.errors.BadRequestError(
      'Prompt type requires numeric input but none found'
    );
  }

  const isLegacyPrompt = await promptstateService.checkIfLegacyPrompt(prompt);
  if (isLegacyPrompt) {
    validateLegacyPromptRequirements(
      prompt,
      promptState,
      elements,
      inputElements
    );
  } else {
    validateNewPromptRequirements(prompt, promptState, elements, inputElements);
  }
}

function getBgColor(elements) {
  const bg = elements.find(
    element => element.type === 'bg' && IS_HEX_COLOR_REGEX.test(element.value)
  );

  if (bg) {
    return bg.value;
  }

  return null;
}

function minifyODML(odml) {
  return odml
    .split('\n')
    .map(line => {
      let trimmed = line.trim();
      if (trimmed.length && trimmed[trimmed.length - 1] !== '>') {
        trimmed = `${trimmed} `;
      }
      return trimmed;
    })
    .join('')
    .replace(/\s+(\/?>)/g, '$1');
}

function beautifyODML(odml) {
  const options = {
    indent_size: 2,
    indent_inner_html: true,
    preserve_newlines: false,
    unformatted: [],
    inline: [],
    extra_liners: [],
  };

  return beautify.html(odml, options);
}

/**
 * @param {string} face
 * @returns
 */
function getFontProperties(face) {
  const match = face.match(FONT_WEIGHT_STYLE_MATCH_REGEX);
  const isItalic = match && match[2] && match[2].toLowerCase() === 'italic';
  const isBoldAndItalic = match && match[1] && isItalic;
  const isBoldOnly = match && match[1] && match[1].toLowerCase() === 'bold';

  // Font weight is bold if and only if subfamily is **italic** with any variant of bold
  // or the subfamily is bold alone.
  const weight = match && (isBoldAndItalic || isBoldOnly) ? 'bold' : 'normal';

  const italic = isItalic ? 'italic' : 'normal';
  const isBold = weight === 'bold';

  return { weight, italic, isItalic, isBold };
}

/**
 * @param {*} isG7100orG6300
 * @param {*} params
 * @returns
 */
function findIfClassIsApplicable(isG7100orG6300, params) {
  if (!params || Object.keys(params).length === 0) {
    return false;
  }

  return isG7100orG6300 && !params.secure;
}

/**
 * @param {*} classValue
 * @param {*} isClassApplicable
 * @returns
 */
function getHtmlClassProperty(classValue, isClassApplicable) {
  return classValue && isClassApplicable ? `class="${classValue}"` : '';
}

function parseElementsToODML(
  deviceType,
  elements,
  assets,
  beautified = false,
  params = {}
) {
  const findAssetById = assetId => {
    const asset = assets.find(a => a.id === assetId);

    if (!asset) {
      throw new restify.BadRequestError(`Could not find asset ${assetId}`);
    }
    return asset;
  };

  const isG6100orG6200 = deviceHelper.isG6100orG6200(deviceType);
  const isG7100orG6300 = deviceHelper.isG7100orG6300(deviceType);
  const isClassApplicable = findIfClassIsApplicable(isG7100orG6300, params);

  // Because G6 doesn't like # even if
  // spec says it should ಠ_ಠ
  const hashMeMaybe = color => (isG7100orG6300 ? `#${color}` : color);

  const elementMap = {
    bg: element => {
      if (IS_HEX_COLOR_REGEX.test(element.value)) {
        return null;
      }
      return `
                <img src='${findAssetById(element.value).path}'/>
            `;
    },
    text: element => {
      const isCustomFont =
        element.face &&
        Joi.validate(element.face, Joi.string().guid()).error === null;

      const face = isCustomFont
        ? findAssetById(element.face).face
        : element.face || '';

      if (!face) return null;

      // OPT firmware can not handle weight or style in font family name,
      // so need to replace them, this is essentially the preferred font
      // family name
      // This applies only to secure prompts, as secure prompts is using
      // custom parser for HTML while non-secure prompts uses WebKit
      // e.g  Roboto Black Italic => Roboto, Saria Semibold => Saria
      const fontFamilyName =
        isCustomFont && params && params.secure
          ? face.replace(FONT_WEIGHT_STYLE_REPLACEMENT_REGEX, '').trim()
          : face;

      const value = element.value.replace(/\n/g, '<br/>');

      // Support custom font face i.e. 'Roboto Black Italic'
      // All sequoia devices should have font-weight, otherwise use <b> tag
      //
      const { weight, italic, isItalic, isBold } = getFontProperties(face);

      return `
                <div
                    ${getHtmlClassProperty(
                      element.userclass,
                      isClassApplicable
                    )}
                    style='position:fixed;
                        top:${element.top}px;
                        left:${element.left}px;
                        width:${element.width || 0}px;
                        height:${element.height || 0}px;
                        font-family:"${fontFamilyName}";
                        font-size:${element.size}px;
                        color:${hashMeMaybe(element.color)};
                        ${
                          element.textAlign
                            ? `text-align:${element.textAlign};`
                            : ''
                        }
                        ${isG7100orG6300 ? `font-weight:${weight};` : ''}
                        ${isG7100orG6300 ? `font-style:${italic};` : ''}'>
                            ${element.bold || isBold ? '<b>' : ''}
                            ${element.italic || isItalic ? '<i>' : ''}
                            ${value}
                            ${element.italic || isItalic ? '</i>' : ''}
                            ${element.bold || isBold ? '</b>' : ''}
                </div>
            `;
    },
    input: element => {
      const isCustomFont =
        element.face &&
        Joi.validate(element.face, Joi.string().guid()).error === null;
      const face = isCustomFont
        ? findAssetById(element.face).face
        : element.face || '';

      if (!face) return null;

      // OPT firmware can not handle weight or style in font family name,
      // so need to replace them, this is essentially the preferred font
      // family name
      // e.g.  Roboto Black Italic => Roboto, Saria Semibold => Saria
      const fontFamilyName =
        isCustomFont && params && params.secure
          ? face.replace(FONT_WEIGHT_STYLE_REPLACEMENT_REGEX, '').trim()
          : face;

      const { weight, italic } = getFontProperties(face);

      return `
                <input
                    ${getHtmlClassProperty(
                      element.userclass,
                      isClassApplicable
                    )}
                    style='position:fixed;
                        top:${element.top}px;
                        left:${element.left}px;
                        font-family:"${fontFamilyName}";
                        color: #${element.color};
                        font-size:${element.size}px;
                        ${
                          isG7100orG6300 && element.width
                            ? `width:${element.width}px;`
                            : ''
                        }
                        ${
                          isG7100orG6300 && element.textAlign
                            ? `text-align:${element.textAlign};`
                            : ''
                        }
                        ${isG7100orG6300 ? `font-weight:${weight};` : ''}
                        ${isG7100orG6300 ? `font-style:${italic};` : ''}'
                ></input>`;
    },
    image: element => {
      if (
        isG6100orG6200 &&
        findAssetById(element.value).path.endsWith('.gif')
      ) {
        // gif is a special case where G6 treats them as clips
        return elementMap.clip(element);
      }
      return `
                <img
                    ${getHtmlClassProperty(
                      element.userclass,
                      isClassApplicable
                    )}
                    src='${findAssetById(element.value).path}'
                    style='position:fixed;top:${element.top}px;left:${
                      element.left
                    }px;'
                />
            `;
    },
    clip: element => `
                <embed
                    ${getHtmlClassProperty(
                      element.userclass,
                      isClassApplicable
                    )}
                    src='${findAssetById(element.value).path}'
                    loop='${element.loop ? element.loop : 0}'
                    style='position:fixed;top:${element.top}px;left:${
                      element.left
                    }px;'
                />
        `,
    video: element => {
      const asset = findAssetById(element.value);

      // G7 supports html5 videos, G6 doesnt
      return isG6100orG6200
        ? `
                    <video
                        videosrc='${asset.videoPath}'
                        audiosrc='${asset.audioPath}'
                        loop='${element.loop ? element.loop : 0}'
                        ${element.vol ? `vol='${element.vol}'` : ''}>
                    </video>
                `
        : `
                    <video autoplay loop
                        ${getHtmlClassProperty(
                          element.userclass,
                          isClassApplicable
                        )}
                        style='position:fixed;top:${element.top || 0}px;left:${
                          element.left || 0
                        }px;'
                        >
                        <source src='${asset.path}'
                            type='video/webm'/>
                    </video>
                `;
    },
  };

  const parsed = elements
    .map(element => {
      const map = elementMap[element.type];
      if (!map) {
        return null;
      }
      return map(element);
    })
    .join('');

  function generateBodyTag(bgColor) {
    if (!bgColor) {
      return '<body>';
    }

    if (isG7100orG6300) {
      return `<body style="background-color:#${bgColor}">`;
    }

    return `<body bgcolor='${bgColor}'>`;
  }

  function generateHeader(secure, fonts) {
    if (secure === false && fonts && fonts.length) {
      const fontFaces = fonts
        .map(font => {
          const { weight, italic } = getFontProperties(font.face);

          return `
                    @font-face {
                            font-family: "${font.face}";
                            font-weight: ${weight};
                            font-style: ${italic};
                            src: url("${font.path}")
                    }
                `;
        })
        .join('\n');

      return `
                <head>
                  <style type="text/css">
                  ${fontFaces}
                  </style>
                </head>
            `;
    }
    return '';
  }

  const fonts = assets.filter(asset => !!asset.face);
  const normalised = minifyODML(`
        ${isG7100orG6300 ? '<html>' : ''}
            ${isG7100orG6300 ? generateHeader(params.secure, fonts) : ''}
            ${generateBodyTag(getBgColor(elements))}
                ${parsed}
            </body>
        ${isG7100orG6300 ? '</html>' : ''}
    `);
  return beautified ? beautifyODML(normalised) : normalised;
}

// Get G6 Mock Assets - Used for calculating ODML size
function getG6MockAssets(elements, secure) {
  const filename = '1234.xyz'; // Filename must be 8 chars long

  const nonsecureMap = {
    bg: '../nonsecure/images',
    image: '../nonsecure/images',
    video: '../nonsecure/videos',
    text: '../nonsecure/fonts',
  };

  const assetKeyMap = {
    bg: 'value',
    image: 'value',
    video: 'value',
    text: 'face',
    input: 'face',
  };

  return elements
    .filter(element => {
      const assetKey = assetKeyMap[element.type];
      return (
        assetKey &&
        element[assetKey] &&
        Joi.validate(element.face, Joi.string().guid()).error === null
      );
    })
    .map(element => {
      const assetKey = assetKeyMap[element.type];
      const asset = {};
      asset.id = element[assetKey];
      if (['input', 'text'].includes(element.type)) {
        asset.face = secure
          ? filename
          : `${nonsecureMap[element.type]}/${filename}`;
      } else {
        asset.path = secure
          ? filename
          : `${nonsecureMap[element.type]}/${filename}`;
      }
      return asset;
    });
}

/**
 * Check if given list of prompt have at least 1 idle, throw bad request error if idle prompt does not exist.
 * If multiple languages enabled, then check for an idle prompt in each language, otherwise all set to 'undefined' language.
 * @param {Array<Prompt>} prompts array of prompt
 */
function checkIdlePrompts(prompts) {
  // Prompts without language Id, will have 'undefined' as default.
  const allPromptsByLang = _.groupBy(
    prompts,
    prompt => prompt.promptSetLanguageId || undefined
  );

  // eslint-disable-next-line no-restricted-syntax
  for (const languageId of Object.keys(allPromptsByLang)) {
    const idlePrompts = allPromptsByLang[languageId].filter(
      defaultPrompt =>
        defaultPrompt.transactionState === constants.transactionState.IDLE
    );

    if (idlePrompts.length <= 0) {
      const numLangs = Object.keys(allPromptsByLang).length;
      const multiLanguageMsg = numLangs > 1 ? ' in each language' : '';
      throw new restify.errors.BadRequestError(
        `At least 1 idle prompt is required${multiLanguageMsg}`
      );
    }
  }
}

/**
 * Invoke Lambda for Creating Thumbnail
 * @param {String} lambdaFunctionName
 * @param {Object} data
 * @returns
 */
function invokeLambda(lambdaFunctionName, data) {
  const lambdaParams = {
    FunctionName: lambdaFunctionName,
    InvocationType: 'Event',
    Payload: JSON.stringify(data),
  };
  return AWS.invokeLambda(lambdaParams);
}

/**
 * Check prompts and create thumbnail
 * Note. Thumbnail will be genearted if Prompt is not daypart and the first prompt when ordered by code
 * When languageSupportID is provided, this will exclude non default Language prompts.
 * @param {PromptSet} promptSet
 * @param {Array<UUID>} promptFilterIds optional, read promptSet object and filter out prompts by given ID if provided
 * @param {Boolean} force force create thumbnail
 */
async function createPromptSetThumbnail(
  promptSet,
  promptFilterIds = [],
  force = false
) {
  const defaultPromptSetLanguageId = Object.values(promptSet.lang)
    .map(
      lang => lang.promptSetLanguageSupport.default && lang.languageSupportId
    )
    .filter(Boolean)
    .find(Boolean);

  const promptSetId = promptSet.id;
  const prompts = promptSet.assignments
    .flatMap(assignment => assignment.prompts)
    .filter(p =>
      promptFilterIds.length > 0 ? promptFilterIds.includes(p.id) : true
    );

  // Sort the Prompt list by Code, only if not force generate.
  if (!force) {
    // Sort the prompt by prompt states code
    prompts.sort((a, b) =>
      a.code.localeCompare(b.code, 'en-us', { ignorePunctuation: true })
    );
  }
  const firstPromptCode = prompts.length > 0 && prompts.find(Boolean).code;

  const isValidForThumb = prompt => {
    const isDefaultLanguage = defaultPromptSetLanguageId
      ? defaultPromptSetLanguageId === prompt.promptSetLanguageId
      : true;
    return prompt.id && prompt.code === firstPromptCode && isDefaultLanguage;
  };
  // eslint-disable-next-line no-restricted-syntax
  for (const prompt of prompts) {
    if (force ? true : isValidForThumb(prompt)) {
      // eslint-disable-next-line no-await-in-loop
      await invokeLambda(config.media.promptThumbnailLambda, {
        promptId: prompt.id,
        promptSetId,
      });
    }
  }
}

/**
 * Search PromptSet Template by templateID
 * @param {UUID} companyId
 * @param {UUID} templateId
 * @returns
 */
async function getPromptSetTemplateById(companyId, templateId) {
  // eslint-disable-next-line no-return-await
  return await server.db.read.row(
    `
        SELECT pt.id, pt.name, pt.default_bg_color as "defaultBg", pt.default_font_color
            FROM company_prompt_template cpt
        JOIN prompt_template pt ON cpt.prompt_template = pt.id
        WHERE cpt.company = $1 AND pt.id = $2;
`,
    [companyId, templateId]
  );
}

/**
 * Search ProductType by type name
 * @param {string} productTypeName
 * @returns
 */
async function getProductTypeByName(productTypeName) {
  // eslint-disable-next-line no-return-await
  return await server.db.read.row(
    `
        SELECT device_type, screen_width, screen_height
            FROM product_type
        WHERE device_type = $1;
`,
    [productTypeName]
  );
}

/**
 * Search promptSetProfileName by type name
 * @param {string} promptSetProfileName
 * @returns
 */
async function getPromptSetProfileByName(promptSetProfileName) {
  const res = await server.db.read.row(
    `
        SELECT *
            FROM prompt_set_profile
        WHERE name = $1;
`,
    [promptSetProfileName]
  );
  return res;
}

/**
 * Update Prompt-Set version
 * @param {UUID} promptSetId
 * @param {UUID} userId
 * @param {string} versionStr
 * @param {Object} connection
 */
async function updatePromptSetVersion(
  promptSetId,
  userId,
  versionStr,
  connection
) {
  await (connection || server.db.write).execute(
    `
    UPDATE prompt_set SET
        version = $1,
        modified = NOW(),
        modified_by = $2
    WHERE id = $3
    RETURNING modified;
`,
    [versionStr, userId, promptSetId]
  );
}

/* eslint-disable consistent-return */
async function validatePromptLinking(auxPrompt, prompt, reqPromptCode) {
  const auxPromptItem = await promptService.getPromptById(auxPrompt);
  if (auxPromptItem) {
    if (
      auxPromptItem.screenOption ===
      constants.PROMPT_CONSTANTS.SCREEN_OPTION.AUX
    ) {
      const mainPromptType = await promptsetRepo.getPromptTypeById(
        prompt.promptType
      );
      const auxPromptType = await promptsetRepo.getPromptTypeById(
        auxPromptItem.promptType
      );
      const StandardTypeCheck =
        mainPromptType.name ===
          constants.PROMPT_CONSTANTS.PROMPT_TYPE.STANDARD &&
        constants.PROMPT_CONSTANTS.PROMPT_TYPES_LIST.includes(
          auxPromptType.name
        );
      const DataTypeCheck =
        mainPromptType.name === constants.PROMPT_CONSTANTS.PROMPT_TYPE.DATA &&
        auxPromptType.name === constants.PROMPT_CONSTANTS.PROMPT_TYPE.STANDARD;
      if (!StandardTypeCheck && !DataTypeCheck)
        return new restify.BadRequestError(
          errors.prompt.invalidPromptLinked(
            mainPromptType.name,
            auxPromptType.name,
            reqPromptCode
          )
        );
    } else {
      return new restify.NotFoundError(errors.prompt.invalidPromptSelected);
    }
  } else {
    return new restify.BadRequestError(errors.prompt.auxPromptNotFound);
  }
}

module.exports = {
  bumpMinorVersion,
  bumpPatchVersion,
  getSoftKeysMap,
  getPromptSetByID,
  getPromptSetTemplateById,
  getProductTypeByName,
  getPromptSetProfileByName,
  getStatesMapForPromptSet,
  getPromptTypesMap,
  getDayPartsMap,
  validatePrompt,
  deletePromptSetContent,
  insertPromptSetContent,
  findThumbnailableAsset,
  getUniquePromptAssets,
  toUIPromptSet,
  createDefaultElements,
  validate,
  getFontProperties,
  stripProperties,
  parseElementsToODML,
  getG6MockAssets,
  checkIdlePrompts,
  groupLanguageObject,
  createPromptSetThumbnail,
  invokeLambda,
  IS_UUID,
  updatePromptSetVersion,
  validatePromptLinking,
};

module.exports.private = {
  getClassRegexErrorMessage,
  throwErrorIfClassIsNotApplicable,
  findIfClassIsApplicable,
  getHtmlClassProperty,
};
