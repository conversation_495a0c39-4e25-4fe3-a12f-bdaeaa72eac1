const { v4 } = require('uuid');
const timezone = require('moment-timezone');
const moment = require('moment');
const { server } = require('../app');
const env = require('../env');
const constants = require('../lib/app-constants');
const aws = require('../lib/aws');

const jobStatus = {
  NEW: 0,
  ACCEPT: 1,
  IN_PROGRESS: 2,
  COMPLETE: 3,
  FAILED: 4,
  CANCELLED: 5,
};

const FPS_JOB_TYPE = ['fuelprice.update', 'fuelprice.delete'];

const okToSendFpsJobStatus = () =>
  env.config &&
  env.config.AWS &&
  env.config.AWS.eventBus &&
  env.config.AWS.eventBus.fuelPriceService;

const isFpsJobs = job =>
  FPS_JOB_TYPE.includes(job.type) && job.destination === 'igl.infx-fps';

const statusNames = Object.keys(jobStatus);
const mapStatusName = status => statusNames[status];
const sendFpsJobStatusEventsToEventBridge = async jobsPayload => {
  if (!okToSendFpsJobStatus()) {
    return null;
  }
  server.log.info(
    {
      jobsPayloadCount: jobsPayload.length,
    },
    `[job-helper].[sendFpsJobStatusEventsToEventBridge]`
  );

  const updatedDate = new Date().toISOString();
  const jobsData = jobsPayload.map(
    ({
      jobId,
      jobStatus: status,
      jobType,
      jobDestination,
      updatedBy,
      failReason,
    }) => ({
      jobDestination,
      jobFailReason: failReason || null,
      jobType,
      jobId,
      jobStatus: mapStatusName(status),
      updatedDate,
      updatedBy,
    })
  );

  const promises = [];
  jobsData.forEach(job => {
    const eventBridgeParams = {
      data: job,
      detailType:
        env.config.AWS.eventBus.fuelPriceService.rules.updateJobStatuses ||
        'fps_update_job_statuses',
      source: constants.eventBusSources.FPS_UPDATE_JOB_STATUS,
    };

    promises.push(aws.sendEventsToEventBridge(eventBridgeParams));
  });
  return Promise.allSettled(promises);
};

/**
 * Insert into job status history
 * @param {*} conn
 * @param {*} id
 * @param {*} jobStatusHistoryMessage
 */
async function createJobStatusHistory(conn, id, jobStatusHistoryMessage) {
  const message = jobStatusHistoryMessage || 'new job';
  // eslint-disable-next-line no-return-await
  return await conn.execute(
    `
        INSERT INTO job_status_history (job_id, status, message, created_at)
        VALUES ($1, $2, $3, NOW());
    `,
    [id, module.exports.jobStatus.NEW, message]
  );
}

/**
 *
 * @param {*} currentDateTime
 * @param {*} expirationTimeToAdd
 * @param {*} expirationType
 * @returns
 */
const getExpiryDateTime = (
  currentDateTime,
  expirationTimeToAdd,
  expirationType
) => moment(currentDateTime).add(expirationTimeToAdd, expirationType).format();

/**
 * Convert datetime to a UTC timestamp
 * @param {Date} dateTime
 * @returns
 */
const getUTCTimestamp = dateTime => timezone.tz(dateTime, 'UTC').format();

module.exports = {
  isFpsJobs,
  okToSendFpsJobStatus,
  sendFpsJobStatusEventsToEventBridge,
  getExpiryDateTime,
  getUTCTimestamp,
  jobStatus,
  jobType: {
    FUTUREX_KEYLOAD_RKI: 'sys.keyload_pki_tr34',
    ROLLOUT_DOWNLOAD: 'sys.download',
    ROLLOUT_INSTALL: 'sys.install',
    FILE_INSTALL: 'sys.install.file',
    PLAYLIST_UPDATE: 'playlist.update',
    REMOTE_CONFIG: 'sys.configure',
    FILE_UPLOAD: 'sys.upload',
    REBOOT: 'sys.reboot',
    RESTART: 'sys.restart',
    CONFIG_UPDATE: 'sys.configfile-update',
    GENERATE_CSR: 'sys.generate-csr',
  },
  /**
   * @param {string} userId
   * @param {string} jobId
   * @returns {Promise<boolean>}
   */
  async checkJobUserPermission(userId, jobId) {
    return !!(await server.db.read.row(
      `
            SELECT 1 as count
            FROM job j
            JOIN target t ON j.device_id = t.target_id
            WHERE j.id = $1
              AND exists ( 
                select 1 
                from user_site_authorization usa
                where usa.site_id = t.site_id
                  and usa.user_id = $2 
              )
        `,
      [jobId, userId]
    ));
  },
  /**
   * @param conn
   * @param {Object} details
   * @param {String} jobStatusHistoryMessage
   * @returns {Promise<Object>}
   */
  async createJob(conn, details, jobStatusHistoryMessage = null) {
    const {
      deviceId,
      destination,
      type,
      data,
      embargo,
      expiry,
      userId,
      dependencies,
    } = details;

    // Create the job
    const id = v4();
    const jobResult = await conn.execute(
      `
            INSERT INTO job
                (id, device_id, destination, type, data, status, embargo, expiry, created_by, created_on )
            VALUES ( $1, $2, $3, $4, $5, $6, $7, $8, $9, now() )
            RETURNING id, device_id, destination, type, data, status, embargo, expiry as expiration, created_by, created_on;
        `,
      [
        id,
        deviceId,
        destination,
        type,
        data,
        module.exports.jobStatus.NEW,
        embargo,
        expiry,
        userId,
      ]
    );
    const job = jobResult.rows[0];

    // Insert into job status history
    await createJobStatusHistory(conn, id, jobStatusHistoryMessage);
    // Insert job dependencies if they exist
    if (dependencies && dependencies.length > 0) {
      const dependencyPromises = dependencies.map(async dependencyDto => {
        // Check if the dependent job exists
        const jobDependOn = await conn.execute(
          'SELECT j FROM job j WHERE j.id = $1;',
          [dependencyDto.jobId]
        );

        if (jobDependOn.rows.length === 0) {
          throw new Error(`Job ${dependencyDto.jobId} not found`);
        }

        // Insert the job dependency
        return conn.execute(
          `
            INSERT INTO job_dependency
                (job_id, dependens_on, continue_on_fail)
            VALUES ($1, $2, $3);
          `,
          [id, dependencyDto.jobId, dependencyDto.runOnFail]
        );
      });

      // Wait for all dependency inserts to complete
      await Promise.all(dependencyPromises);
    }
    return job;
  },

  /**
   * Change the protocal from http to https
   * @param {*} job
   */
  modifyJobDataProtocolHttpToHttps(job) {
    const data = job.data || '';
    const regex = env.config.hostnameRegex;
    if (data.match(regex)) {
      const modifiedData = data.replace(/http:\/\//gi, 'https://');
      job.data = modifiedData; // eslint-disable-line no-param-reassign
    }

    return job;
  },

  /**
   * Builds and returns the job object as per the peremeters passed
   * @param {Number} deviceId
   * @param {String} destination
   * @param {String} type
   * @param {String} data
   * @param {String} embargo
   * @param {String} expiry
   * @param {String} userId
   * @returns {Object}
   */
  buildJob(deviceId, destination, type, data, embargo, expiry, userId) {
    return {
      deviceId,
      destination,
      type,
      data,
      embargo,
      expiry,
      userId,
    };
  },

  /**
   * Saves the job dependency in the database
   * @param {Object} connection
   * @param {Object} jobDependency
   * @returns {Promise<Object>}
   */
  saveJobDependency(connection, jobDependency) {
    const params = [
      jobDependency.jobId,
      jobDependency.dependensOn,
      jobDependency.continueOnFail,
    ];
    const query = `
    INSERT INTO job_Dependency( job_id, dependens_on, continue_on_fail ) 
    VALUES ( $1, $2, $3 );
`;
    try {
      return connection.execute(query, params);
    } catch (err) {
      server.log.debug(
        {
          query,
          params,
        },
        `saveJobDependency error`
      );
      throw err;
    }
  },

  sendPlaylistStatusHistoryToFirehose(jobStatusHistory) {
    const batchSize =
      env.config.AWS.firehose.playlistStatusHistoryToFirehoseBatchSize; // Adjust the batch size as needed
    const qCalls = [];
    let index = 0;
    while (index < jobStatusHistory.length) {
      const items = jobStatusHistory.slice(index, index + batchSize);
      qCalls.push(
        aws.firehosePutRecords({
          Records: items.map(elm => ({
            Data: JSON.stringify(elm),
          })),
          DeliveryStreamName:
            env.config.AWS.firehose.playlistStatusHistorydataStream.streamName,
        })
      );
      index += batchSize;
    }
    Promise.all(qCalls)
      .then(results => {
        server.log.debug(results, 'All batches sent successfully:');
      })
      .catch(err => {
        server.log.error(err, 'Error sending some batches:');
      });
  },
};

module.exports.private = {
  createJobStatusHistory,
};
