const { server } = require('../app');
const mainLogger = require('../lib/logger').mainLogger();

const logger = mainLogger;
const getCompanyEntitySettingsConfig = async ({
  companyId,
  settingName,
  featureName,
}) =>
  server.db.read.row(
    `
      SELECT entity_value 
      FROM entity_settings
        WHERE entity_id = $1
        AND entity_settings_definitions_id = 
            (SELECT entity_settings_definitions.entity_settings_definitions_id 
              FROM entity_settings_definitions
                WHERE setting_name = $2 and feature_name = $3);
      `,
    [companyId, settingName, featureName]
  );

const getCompanyLevelJsonValue = async ({
  companyId,
  settingName,
  featureName,
}) => {
  try {
    logger.debug(
      `[EntitySettingsHelper].[getCompanyLevelJsonValue] Cache expired or first call: ${settingName} `
    );
    const configRow = await getCompanyEntitySettingsConfig({
      companyId,
      settingName,
      featureName,
    });
    return configRow ? JSON.parse(configRow.entityValue) : undefined;
  } catch (err) {
    logger.error(
      { error: err },
      `[EntitySettingsHelper].[getCompanyLevelJsonValue] Parsing entity settings: ${settingName} failed`
    );
  }
  return undefined;
};

module.exports = {
  getCompanyLevelJsonValue,
  getCompanyEntitySettingsConfig,
};
