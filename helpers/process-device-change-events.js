const AWS = require('../lib/aws');
const env = require('../env');
const { eventBusSources } = require('../lib/app-constants');
const logger = require('../lib/logger').mainLogger();
const { server } = require('../app');

const isFpsProcessDeviceChange = () =>
  env.config.AWS.eventBus && env.config.AWS.eventBus.fuelPriceService;

const getDevicesData = async deviceIds => {
  const deviceList = deviceIds.join(',');
  // eslint-disable-next-line no-return-await
  return await server.db.read.rows(
    `
      SELECT t.target_id as device_id, t.site_id, t.last_edited_date as updated_date, t.delete_timestamp as deleted_date, t.updated_by
      FROM target t
      WHERE t.target_id in (${deviceList});
      `
  );
};

const doSendProcessDeviceChangeEvent = deviceData => {
  if (!deviceData) {
    // eslint-disable-next-line prefer-promise-reject-errors
    return Promise.reject('Device Data Not Found');
  }
  logger.info(
    { deviceData },
    '[process_device_change_events].doSendProcessDeviceChangeEvent deviceData'
  );
  const eventBridgeParams = {
    data: {
      ...deviceData,
      updatedDate: deviceData.updatedDate
        ? deviceData.updatedDate.toISOString()
        : null,
      deletedDate: deviceData.deletedDate
        ? deviceData.deletedDate.toISOString()
        : null,
    },
    detailType:
      env.config.AWS.eventBus.fuelPriceService.rules.processDeviceChange ||
      'fps_process_device_change',
    source: eventBusSources.FPS_UPDATE_DEVICE_SITE,
  };

  return AWS.sendEventsToEventBridge(eventBridgeParams);
};

const sendEventProcessDevicesChange = deviceIds => {
  if (!isFpsProcessDeviceChange() || !deviceIds.length) {
    return Promise.resolve();
  }

  logger.info(
    { deviceIds, env: env.config.AWS.eventBus },
    '[process_device_change_events].sendEventProcessDevicesChange.start'
  );

  return new Promise((resolve, reject) => {
    getDevicesData(deviceIds)
      .then(devicesData => {
        Promise.allSettled(
          devicesData.map(deviceData =>
            doSendProcessDeviceChangeEvent(deviceData)
          )
        ).finally(() => resolve());
      })
      .catch(err => reject(err));
  });
};

module.exports = {
  sendEventProcessDevicesChange,
};
