const { server } = require('../app');
const constants = require('../lib/app-constants');

const selectActiveUserByEmail = `SELECT u.id, u.company_id, u.last_locked
    FROM ics_user u
    WHERE lower(email) = lower($1)
    AND status = ${constants.userStatus.ACTIVE}`;

const updateUserPasswordById = `UPDATE ics_user
    SET password_hash = $1
    WHERE id = $2`;

const getUserMfaSecret = `SELECT u.mfa_secret
    FROM ics_user u
    WHERE id = $1`;

const getUserPasswordHash = `SELECT password_hash
    FROM ics_user
    WHERE id = $1`;

const SQL_SELECTS = {
  USER_JWT_TOKEN_SELECT: `
        WITH company AS (
          SELECT
            c.id,
            c.name,
            COALESCE(json_agg(cf.feature_flag) FILTER (WHERE cf.feature_flag IS NOT NULL), '[]') AS "featureFlags",
            c.session_expiry_user_mins AS "sessionExpiryUserMins"
          FROM company c
          LEFT JOIN company_feature_flag cf ON c.id = cf.company
          GROUP BY c.id
        )
        SELECT
            u.id,
            u.email,
            u.created,
            u.full_name,
        row_to_json(c.*) AS company,
        array(
            SELECT r.role_name as name
            FROM role as r JOIN user_role ur ON ur.user_id = u.id AND ur.role_id = r.role_id
        ) as roles
        FROM ics_user as u
        LEFT JOIN company c ON c.id = u.company_id
    `,

  USER_DTO_SELECT: `SELECT
        u.id,
        u.email,
        (u.email_verified IS NOT NULL) AS email_verified,
        (u.mfa_secret IS NOT NULL) AS mfa_configured,
        u.full_name,
        (select row_to_json(_) from (select c.id, c.name) as _) as company,
        array(SELECT
            r.role_name as name
            FROM role as r
            JOIN user_role ur ON ur.user_id = u.id AND ur.role_id = r.role_id) as roles
        FROM ics_user as u
            JOIN company c ON c.id = u.company_id`,

  USER_FULL_SELECT: `SELECT
        u.id,
        u.email,
        (u.email_verified IS NOT NULL) AS email_verified,
        (u.mfa_secret IS NOT NULL) AS mfa_configured,
        u.status,
        u.full_name,
        u.last_locked,
        u.failed_login_attempts,
        p.name as persona,
        (select row_to_json(_) from (
            select 
                c.id, 
                c.name, 
                array(SELECT feature_flag
                    FROM company_feature_flag cff
                    WHERE cff.company = u.company_id) as "featureFlags") 
            as _ ) as company,
        array(SELECT
            r.role_name as name
            FROM role as r
            JOIN user_role ur ON ur.user_id = u.id AND ur.role_id = r.role_id) as roles
        FROM ics_user as u
            JOIN company c ON c.id = u.company_id
            JOIN persona p ON p.id = u.persona_id`,
};

const getUserFeatureFlagsById = `SELECT 
      c.id, 
      c.name,
      COALESCE(
        json_agg(cf.feature_flag) FILTER (WHERE cf.feature_flag IS NOT NULL), '[]'
      ) AS "featureFlags",
      c.session_expiry_user_mins AS "sessionExpiryUserMins"
    FROM 
      company c
    LEFT JOIN 
      company_feature_flag cf 
      ON c.id = cf.company
    WHERE 
      EXISTS (
        SELECT 1 
        FROM ics_user iu
        WHERE iu.company_id = c.id
        AND iu.id = $1
      )
    GROUP BY 
      c.id, c.name, c.session_expiry_user_mins;
  `;

async function getFullUser(userIds) {
  // eslint-disable-next-line no-return-await
  return await server.db.read.rows(
    `
        ${SQL_SELECTS.USER_FULL_SELECT}
        WHERE u.id = ANY($1)
        ORDER BY u.full_name ASC
    `,
    [userIds]
  );
}

async function getJwtTokenUser(userId, statuses) {
  // eslint-disable-next-line no-return-await
  return await server.db.read.row(
    `
        ${SQL_SELECTS.USER_JWT_TOKEN_SELECT}
        WHERE
        u.id = $1 AND
        u.status = ANY($2);
    `,
    [userId, statuses]
  );
}

/**
 * Function to find an active user by email
 */
async function findUserByEmail(email) {
  // eslint-disable-next-line no-return-await
  return await server.db.read.row(selectActiveUserByEmail, [email]);
}

/**
 * Function to get the mfa secret of a user by its id
 */
async function getUserMfaSecretById(id) {
  return (await server.db.read.row(getUserMfaSecret, [id])).mfaSecret;
}

/**
 * Function to get the password hash of a user by its id
 */
async function getUserPasswordHashById(id) {
  return (await server.db.read.row(getUserPasswordHash, [id])).passwordHash;
}

/**
 * Function to update a user password
 *
 * NOTE: You'll need to handle a transaction if required
 */
async function updateUserPassword(connection, userId, passwordHash) {
  await connection.execute(updateUserPasswordById, [passwordHash, userId]);
}

module.exports = {
  getFullUser,
  getUserMfaSecretById,
  findUserByEmail,
  updateUserPassword,
  getUserPasswordHashById,
  getJwtTokenUser,
  SQL_SELECTS,
  getUserFeatureFlagsById,
};
