[{"invenco.system.g7opt.upc-dataentry-mode": "epp", "category": "Security", "categoryAlt": "security", "condition": "Secure Channel Lost"}, {"invenco.system.g6opt.upc-dataentry-mode": "epp", "category": "Security", "categoryAlt": "security", "condition": "Secure Channel Lost"}, {"invenco.system.g7opt.sdc-tamper-status": "separated", "category": "OPT Tampered", "categoryAlt": "tamper", "condition": "SDC Tampered (removal)"}, {"invenco.system.g7opt.sdc-tamper-status": "tampered", "category": "OPT Tampered", "categoryAlt": "tamper", "condition": "SDC Tampered (destructive)"}, {"invenco.system.g7opt.upc-tamper-status": "separated", "category": "OPT Tampered", "categoryAlt": "tamper", "condition": "UPC Tampered (removal)"}, {"invenco.system.g7opt.upc-tamper-status": "tampered", "category": "OPT Tampered", "categoryAlt": "tamper", "condition": "UPC Tampered (destructive)"}, {"invenco.system.g6opt.upc-lasttamper-event": null, "category": "OPT Tampered", "categoryAlt": "tamper", "condition": "UPC Tampered (destructive)"}, {"invenco.system.g7opt.sdc-tamper-mode": "safe", "category": "OPT in Safe Mode", "categoryAlt": "mode", "condition": "SDC Safe Mode"}, {"invenco.system.g7opt.upc-tamper-mode": "safe", "category": "OPT in Safe Mode", "categoryAlt": "mode", "condition": "UPC Safe Mode"}, {"invenco.system.g6opt.upc-tamper-mode": "safe", "category": "OPT in Safe Mode", "categoryAlt": "mode", "condition": "UPC Safe Mode"}, {"invenco.system.g7opt.apc-tamper-mode": "safe", "category": "OPT in Safe Mode", "categoryAlt": "mode", "condition": "APC Safe Mode"}, {"invenco.system.g7opt.upc-channel-status": "down", "category": "Component Disconnected", "categoryAlt": "channel", "condition": "UPC Disconnected"}, {"invenco.system.g6opt.upc-channel-status": "down", "category": "Component Disconnected", "categoryAlt": "channel", "condition": "UPC Disconnected"}, {"invenco.emvapp.pos.channel": "0 - offline", "category": "Site Integration", "categoryAlt": "integration", "condition": "POS Disconnected"}, {"invenco.emvapp.pos.channel": "2 - error", "category": "Site Integration", "categoryAlt": "integration", "condition": "POS Error"}, {"invenco.system.g7opt.exception-cert-sponsor-mismatch": "1", "category": "Security", "categoryAlt": "security", "condition": "Component Certificate Mismatch"}]