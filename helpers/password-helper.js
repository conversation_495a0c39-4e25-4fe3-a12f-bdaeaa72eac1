const co = require('co');
const restify = require('restify');
const { server } = require('../app');
const { config } = require('../env');
const constants = require('../lib/app-constants');
const hashPassword = require('../lib/hash-password');
const userHelper = require('./users-helper');

const passwordRegex = config.passwordRegex || constants.passwordRegex;

/**
 * Function to check if passed password is not on the banned list and of valid length
 */
function isPasswordAcceptable(req, password) {
  return co(function* execute() {
    if (password.length < 12 || password.length > 100) {
      req.log.info(`Password length out of bounds: ${password.length}`);
      return false;
    }
    const passwordRegExp = new RegExp(passwordRegex);
    if (!passwordRegExp.test(password)) {
      req.log.info(
        'A minimum length of 12 characters, contain both numeric and alphabetic characters.'
      );
      return false;
    }

    const result = yield server.db.read.row(
      `
            SELECT COUNT(bp.id)
            FROM banned_password bp
            WHERE bp.password = $1`,
      [password]
    );
    const { count } = result;

    if (count > 0) {
      req.log.info('Password was found on the banned list, not valid');
      return false;
    }
    return true;
  });
}

async function isSamePassword(req, userId, newPassword) {
  // Get the last four password hashes for the user
  const lastPasswords = await server.db.read.rows(
    `SELECT ucf.to_value AS "passwordHash" FROM user_change uc
     JOIN user_change_field ucf ON ucf.change_id = uc.id
     WHERE uc.target_user_id = $1 AND ucf.field_name = 'password_hash'
     ORDER BY uc.timestamp DESC
     LIMIT 4`,
    [userId]
  );

  // Run password verifications concurrently
  const results = await Promise.all(
    lastPasswords.map(pw => hashPassword.verify(newPassword, pw.passwordHash))
  );

  const isSamePwd = results.includes(true); // Check if any password match was found

  if (isSamePwd) {
    req.log.info(
      'New password cannot be the same as any of the last 4 passwords'
    );
    return true; // Invalid password
  }

  return false; // Password is valid
}

async function validatePassword(req, userId, password) {
  // Fetch user feature flags
  const getUserFeatureFlag = await server.db.read.row(
    userHelper.getUserFeatureFlagsById,
    [userId]
  );

  if (!getUserFeatureFlag?.featureFlags?.includes('PCI_DSS_COMPLIANCE'))
    return true;
  // Check that the new password is valid (not same as last 4 passwords)
  const isPasswordReused = await isSamePassword(req, userId, password);

  if (isPasswordReused) {
    req.log.info('Invalid password');
    // Return an error response for invalid password
    throw new restify.ConflictError('Invalid password');
  }
  req.log.info('Password is valid');
  return true;
}

module.exports = {
  isPasswordAcceptable,
  isSamePassword,
  validatePassword,
};
