const { Kafka, logLevel } = require('kafkajs');
const AWS = require('aws-sdk');
const {
  createMechanism,
} = require('@jm18457/kafkajs-msk-iam-authentication-mechanism');

const env = require('../env');
const logger = require('../lib/logger').mainLogger();
const { handleBlueFinHubLocation } = require('../handlers/bluefin');

let retryCount = 0;
const maxRetries = env.config.AWS.kafka?.max_retries || 3;
let isPlannedDisconnect = false;

const sts = new AWS.STS();

const kafkaState = {
  kafka: null,
  producer: null,
  consumer: null,
  nextRetry: null,
  retryDuration: 0, // 30 seconds
  isProducerConnected: false,
  isConsumerConnected: false,
  isConsumerReconnecting: false,
};
const kafkaCredentials = {
  expiryDate: null,
  IAMRole: null,
};

const KAFKA_CLIENT_TYPE = {
  PRODUCER: 'producer',
  CONSUMER: 'consumer',
};

const setProducerConnectionFlag = flag => {
  kafkaState.isProducerConnected = flag;
};

const getCredDuration = () => {
  let duration = env.config.AWS.kafka.credential_duration_seconds
    ? env.config.AWS.kafka.credential_duration_seconds
    : 3600; // Default 1hrs
  if (duration > 3600) {
    logger.error(
      `[KafkaStreamHelper].[GetCredDuration] Max Configuration Exceeded. Kafka Config Duration: ${duration}. Set Back to default: 3600.`
    );
    duration = 3600;
  }
  return duration;
};

const disconnectKafkaClient = async (client, type) => {
  if (!client) return;
  try {
    if (type === KAFKA_CLIENT_TYPE.PRODUCER) {
      await client.disconnect();
      await setProducerConnectionFlag(false);
    } else if (type === KAFKA_CLIENT_TYPE.CONSUMER) {
      isPlannedDisconnect = true;
      await client.disconnect();
      isPlannedDisconnect = false;
    }
  } catch (error) {
    logger.error(
      { error },
      `[KafkaStreamHelper].[disconnectKafkaClient] Error disconnecting Kafka ${type}: %s`
    );
  }
};

const resetKafkaStateRetry = () => {
  kafkaState.nextRetry = null;
  kafkaState.retryDuration = 0;
};
const setKafkaStateNextRetry = now => {
  kafkaState.nextRetry = now.setSeconds(
    now.getSeconds() + kafkaState.retryDuration
  );
  kafkaState.retryDuration += 30;
};

const resetKafkaState = async type => {
  const { CONSUMER, PRODUCER } = KAFKA_CLIENT_TYPE;

  if (type === CONSUMER) {
    if (kafkaState.consumer) {
      await disconnectKafkaClient(kafkaState.consumer, CONSUMER);
      kafkaState.consumer = null;
    }
  }

  if ((type === CONSUMER || type === PRODUCER) && kafkaState.producer) {
    await disconnectKafkaClient(kafkaState.producer, PRODUCER);
    kafkaState.producer = null;
  }
  if (kafkaState.kafka && kafkaCredentials.expiry < new Date()) {
    kafkaState.kafka = null;
  }
};

const initializeKafkaClients = () => {
  if (!kafkaState.producer) {
    logger.info(
      `[KafkaStreamHelper].[initializeKafkaClients] Creating Kafka producer.`
    );
    kafkaState.producer = kafkaState.kafka.producer({
      allowAutoTopicCreation: true,
    });
  }
  if (!kafkaState.consumer) {
    logger.info(
      `[KafkaStreamHelper].[initializeKafkaClients] Creating Kafka consumer.`
    );
    kafkaState.consumer = kafkaState.kafka.consumer({
      groupId: env.config.AWS.kafka.consumer_group_id,
      maxWaitTimeInMs: 10000,
    });
    kafkaState.consumer.listenerAdded = false;
  }
};

const getKafkaIAMRoleCredentials = async now => {
  const durationSeconds = getCredDuration();
  // Set expiry to 1 minute before actual expiry
  const expiryDate = new Date(Date.now() + (durationSeconds - 60) * 1000);
  try {
    logger.info(
      `[KafkaStreamHelper].[GetKafkaIAMRoleCredentials] Renewing Kafka IAM Role Duration: ${durationSeconds}.`
    );
    const data = await sts
      .assumeRole({
        DurationSeconds: durationSeconds,
        RoleArn: env.config.AWS.kafka.role_arn,
        RoleSessionName: env.config.AWS.kafka.role_session_name,
      })
      .promise();
    if (data) {
      resetKafkaStateRetry();
      kafkaCredentials.expiry = expiryDate;
      kafkaCredentials.IAMRole = data;
      logger.info(
        `[KafkaStreamHelper].[GetKafkaIAMRoleCredentials] Kafka IAM Role Expiration: ${expiryDate}.`
      );
      return kafkaState;
    }

    setKafkaStateNextRetry(now);
    logger.info(
      `[KafkaStreamHelper].[GetKafkaIAMRoleCredentials] Retry Renewing Kafka IAM Role ${kafkaState.nextRetry}`
    );
  } catch (e) {
    setKafkaStateNextRetry(now);
    logger.info(
      `[KafkaStreamHelper].[GetKafkaIAMRoleCredentials] Retry Renewing Kafka IAM Role ${kafkaState.nextRetry}`
    );
    logger.error(
      { error: e },
      `[KafkaStreamHelper].[GetKafkaIAMRoleCredentials] Error assuming Kafka IAM role: ${e.message}.`
    );
  }
  return kafkaState;
};

const getKafka = async type => {
  // do not do kafka if run locally
  if (env.environment === 'local') {
    return undefined;
  }
  const now = new Date();
  // skip if next retry is in future
  if (kafkaState.nextRetry && kafkaState.nextRetry > now) {
    return kafkaState;
  }
  // return current kafka if not expired
  if (
    !kafkaState.nextRetry &&
    kafkaCredentials.expiry > now &&
    kafkaState.kafka
  ) {
    initializeKafkaClients();
    return kafkaState;
  }

  await resetKafkaState(type);
  const { nextRetry } = await getKafkaIAMRoleCredentials(now);
  if (nextRetry) return kafkaState;

  const credentials = {
    accessKeyId: kafkaCredentials.IAMRole.Credentials.AccessKeyId,
    secretAccessKey: kafkaCredentials.IAMRole.Credentials.SecretAccessKey,
    sessionToken: kafkaCredentials.IAMRole.Credentials.SessionToken,
  };

  kafkaState.kafka = new Kafka({
    logLevel: logLevel.ERROR,
    brokers: env.config.AWS.kafka.brokers,
    clientId: env.config.AWS.kafka.client_id,
    ssl: true,
    sasl: createMechanism({
      region: env.config.AWS.kafka.region,
      credentials,
    }),
    connectionTimeout: env.config.AWS.kafka.connection_timeout || 1000,
    requestTimeout: env.config.AWS.kafka.request_timeout || 30000,
    authenticationTimeout: env.config.AWS.kafka.authentication_timeout || 15000,
    reauthenticationThreshold:
      env.config.AWS.kafka.reauthentication_threshold || 30000,
    lockTimeout: env.config.AWS.kafka.lock_timeout || 10000,
  });
  initializeKafkaClients();
  return kafkaState;
};

const putDataToKafkaStream = async (device, items) => {
  if (!env.config.AWS.kafka || !env.config.AWS.kafka.enable) {
    return;
  }
  const { siteId, deviceId } = device;

  const { producer, isProducerConnected } = await getKafka(
    KAFKA_CLIENT_TYPE.PRODUCER
  );
  if (!producer) {
    return;
  }

  const messagesInput = [];
  items.map(element =>
    messagesInput.push({
      key: element.PartitionKey.toString(),
      value: JSON.stringify(element.Data),
    })
  );

  // Producing
  try {
    if (!isProducerConnected) {
      await producer.connect();
      logger.info(
        `[KafkaStreamHelper].[PutDataToKafkaStream] kafka producer connected`
      );
      await setProducerConnectionFlag(true);
    }

    logger.info(
      `[KafkaStreamHelper].[PutDataToKafkaStream] Device Id: ${deviceId} GVR Id: ${device.gvrSiteId}. Sending data from Kafka producer.`
    );
    await producer.send({
      topic: env.config.AWS.kafka.topic,
      messages: messagesInput,
      timeout: env.config.AWS.kafka.producer_timeout
        ? env.config.AWS.kafka.producer_timeout
        : 30000,
    });
    logger.info(
      `[KafkaStreamHelper].[PutDataToKafkaStream] Device Id: ${deviceId} GVR Id: ${device.gvrSiteId}. Data send to Kafka successfully.`
    );
  } catch (err) {
    logger.error(
      {
        siteId,
        deviceId,
        error: err,
      },
      `[KafkaStreamHelper].[PutDataToKafkaStream] Error with Kafka stream: ${err.message}.`
    );
    await producer.disconnect();
    logger.info(
      `[KafkaStreamHelper].[PutDataToKafkaStream] kafka producer disconnected due to error while sending data.`
    );
    await setProducerConnectionFlag(false);
  }
};

const consumeDataFromKafka = async server => {
  const kafkaConfig = env.config.AWS.kafka;
  logger.info(
    { kafkaConfig },
    '[KafkaStreamHelper].[ConsumeDataFromKafka] Kafka configs.'
  );
  if (!kafkaConfig || !kafkaConfig.enable || !kafkaConfig.enable_consumer) {
    logger.error(
      '[KafkaStreamHelper].[ConsumeDataFromKafka] Kafka consumer is disabled.'
    );
    return;
  }
  const { consumer } = await getKafka(KAFKA_CLIENT_TYPE.CONSUMER);
  if (!consumer) {
    logger.error(
      '[KafkaStreamHelper].[ConsumeDataFromKafka] Kafka consumer initialization failed.'
    );
    return;
  }

  try {
    const icsLocation = env.config.AWS.kafka?.bluefinTopics?.icsLocation;

    if (!icsLocation) {
      logger.error(
        '[KafkaStreamHelper].[ConsumeDataFromKafka] Kafka topic for ICS location is not configured.'
      );
      return;
    }

    if (!kafkaState.isConsumerConnected) {
      await consumer.connect();

      await consumer.subscribe({
        topic: icsLocation,
        fromBeginning: true,
      });
    }

    const logTopicMessage = (message, topic) => {
      logger.debug(
        { messageValue: message },
        `[KafkaStreamHelper].[ConsumeDataFromKafka] Processing data from kafka consumer for topic: ${topic}.`
      );
    };

    const topicHandlers = {
      [icsLocation]: async payload => {
        logTopicMessage(payload.message, payload.topic);
        await handleBlueFinHubLocation({
          dbConnection: server.db,
          kafkaPayload: payload,
        });
      },
    };

    if (!consumer?.listenerAdded) {
      consumer.listenerAdded = true;

      consumer.on('consumer.connect', () => {
        kafkaState.isConsumerConnected = true;
        retryCount = 0;
        logger.info(
          '[KafkaStreamHelper].[ConsumeDataFromKafka] Kafka consumer CONNECTED event received.'
        );
      });

      consumer.on('consumer.crash', async event => {
        if (kafkaState.isConsumerReconnecting) {
          return;
        }
        if (retryCount >= maxRetries) {
          logger.error(
            '[KafkaStreamHelper].[ConsumeDataFromKafka] Max retries reached.'
          );
          return;
        }
        retryCount += 1;
        logger.error(
          { error: event },
          `[KafkaStreamHelper].[ConsumeDataFromKafka] Consumer crashed. Reconnecting now...`
        );
        await reconnectConsumer(server);
      });

      consumer.on('consumer.disconnect', async () => {
        if (isPlannedDisconnect || kafkaState.isConsumerReconnecting) {
          if (kafkaState.isConsumerReconnecting) {
            logger.warn(
              '[KafkaStreamHelper].[ConsumeDataFromKafka] Consumer is already reconnecting.'
            );
          }
          return;
        }

        if (retryCount >= maxRetries) {
          logger.error(
            '[KafkaStreamHelper].[ConsumeDataFromKafka] Max retries reached.'
          );
          return;
        }
        retryCount += 1;
        logger.info(
          `[KafkaStreamHelper].[ConsumeDataFromKafka] Consumer disconnected. Reconnecting now...`
        );
        await reconnectConsumer(server);
      });
    }

    await consumer.run({
      eachMessage: async payload => {
        const { topic } = payload;
        const handler = topicHandlers[topic];

        if (handler) {
          try {
            await handler(payload);
            logger.info(
              `[KafkaStreamHelper].[ConsumeDataFromKafka] Successfully processed message from topic: ${topic}.`
            );
          } catch (error) {
            logger.error(
              { error },
              `[KafkaStreamHelper].[ConsumeDataFromKafka] Error processing message from topic: ${topic}.`
            );
          }
        } else {
          logTopicMessage(payload, topic);
        }
      },
    });
  } catch (err) {
    logger.error(
      { error: err },
      `[KafkaStreamHelper].[ConsumeDataFromKafka] Error with Kafka stream while reading the content: ${err.message}.`
    );
  }
};

async function reconnectConsumer(server) {
  if (kafkaState.isConsumerReconnecting) {
    logger.warn(
      '[KafkaStreamHelper].[reconnectConsumer] Reconnection already in progress.'
    );
    return;
  }
  kafkaState.isConsumerReconnecting = true;
  try {
    await resetKafkaState(KAFKA_CLIENT_TYPE.CONSUMER);
    resetKafkaStateRetry();
    kafkaState.isConsumerConnected = false;

    await consumeDataFromKafka(server);
  } catch (error) {
    logger.error(
      { error },
      '[KafkaStreamHelper].[reconnectConsumer] Error while reconnecting Kafka consumer: %s',
      error.message
    );
  } finally {
    kafkaState.isConsumerReconnecting = false;
  }
}

const pushBluefinKafkaEvents = async (items, topic, keyField) => {
  const result = {
    succeeded: [],
    failed: [],
  };

  if (!env.config.AWS.kafka || !env.config.AWS.kafka.enable || !topic) {
    return result;
  }

  const { producer, isProducerConnected } = await getKafka(
    KAFKA_CLIENT_TYPE.PRODUCER
  );
  if (!producer) {
    return result;
  }

  try {
    if (!isProducerConnected) {
      await producer.connect();
      logger.info(
        `[KafkaStreamHelper].[pushBluefinKafkaEvents] kafka producer connected`
      );
      await setProducerConnectionFlag(true);
    }

    const kafkaSendPromises = items.map(async item => {
      const message = {
        key: item[keyField].toString(),
        value: JSON.stringify(item),
      };

      try {
        await producer.send({
          topic,
          messages: [message],
          timeout: env.config.AWS.kafka.producer_timeout || 30000,
        });

        result.succeeded.push({
          id: item[keyField],
          key: item.type,
          value: 1,
        });

        logger.info(
          `[KafkaStreamHelper].[pushBluefinKafkaEvents] Pushed ${item.type} event, payload:  ${message.value}`
        );
      } catch (error) {
        result.failed.push(item);
        logger.error(
          `[KafkaStreamHelper].[pushBluefinKafkaEvents] Failed ${item.type} event for ${keyField}:${item[keyField]} %s`,
          error
        );
      }
    });

    await Promise.all(kafkaSendPromises);
  } catch (error) {
    logger.error(
      `[KafkaStreamHelper].[pushBluefinKafkaEvents] Error during Bluefin Kafka processing %s`,
      error
    );
    await producer.disconnect();
    await setProducerConnectionFlag(false);
  }

  return result;
};

module.exports = {
  getKafkaIAMRoleCredentials,
  putDataToKafkaStream,
  consumeDataFromKafka,
  pushBluefinKafkaEvents,
};
