const {
  IcsEventStream,
} = require('@invenco-cloud-systems-ics/ics-event-stream-lib');

const { server } = require('../app');
const eventConfig = require('../lib/app-constants');
const env = require('../env');

let icsEventStream;
if (env.config.AWS.eventstream.enable) {
  const customKafkaConfig = {
    brokerUrl: env.config.AWS.eventstream.brokers[0],
    iamAuth: {
      region: env.config.AWS.eventstream.region,
    },
  };

  icsEventStream = new IcsEventStream(
    eventConfig.eventStreamConfig,
    customKafkaConfig
  );
}

async function putDataToKafkaIES(ies, device, items) {
  try {
    const { siteId, deviceId } = device;
    const messagesInput = items.map(element => ({
      deviceId: element.PartitionKey.toString(),
      deviceValue: JSON.stringify(element.Data),
    }));
    await ies
      .add('pubId', eventConfig.eventStreamConfig.pubId)
      .add('action', 'ingest.dottedStrings')
      .add('corrId', 'corr-id')
      .add('device_id', deviceId)
      .add('site_id', siteId)
      .add('payload.deviceData', messagesInput)
      .publish();

    server.log.debug(
      '[EventStreamHelper].[PutDataToKafkaIES] Success in publish.'
    );
  } catch (error) {
    server.log.error(
      `[EventStreamHelper].[PutDataToKafkaIES] Error in publish: ${error.message}.`
    );
  }
}

function putToEventStream(device, items) {
  if (!env.config.AWS.eventstream || !env.config.AWS.eventstream.enable) {
    return;
  }
  try {
    putDataToKafkaIES(icsEventStream, device, items);
    server.log.debug(`send EventStream data To Kafka...`);
  } catch (error) {
    server.log.error(
      `[EventStreamHelper].[PutToEventStream] Error in sending EventStream data To Kafka: ${error.message}.`
    );
  }
}

module.exports = {
  putToEventStream,
};
