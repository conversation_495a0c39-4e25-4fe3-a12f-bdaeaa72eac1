export class BridgeHelper {
  constructor(db, logger) {
    this.db = db;
    this.deviceType = {
      REGULUS: { ics: 'C1-100', legacy: 'REGULUS' },
      FUELPOS: { ics: 'FUELPOS', legacy: 'FUELPOS' },
      G7_100: { ics: 'G7-100', legacy: 'G7OPT' },
      G6_200: { ics: 'G6-200', legacy: 'G6OPT' },
      G6_300: { ics: 'G6-300', legacy: 'G6300' },
      G6_400: { ics: 'G6-400', legacy: 'G6400' },
      G6_500: { ics: 'G6-500', legacy: 'G6500' },
      EDGE: { ics: 'EDGE', legacy: 'EDGE' },
      OMNIA: { ics: 'OMNIA', legacy: 'OMNIA' },
      WIN_SERVER: { ics: 'WIN-SERVER', legacy: 'WINSERVER' },
      LINUX_SERVER: { ics: 'LINUX-SERVER', legacy: 'LINUXSERVER' },
    };
    this.logger = logger;
  }

  getICSBridgeDeviceType(productTypeId) {
    switch (productTypeId) {
      case 6:
        return this.deviceType.G6_200.ics;

      case 9:
        return this.deviceType.FUELPOS.ics;

      case 10:
        return this.deviceType.G7_100.ics;

      case 12:
        return this.deviceType.REGULUS.ics;

      case 13:
        return this.deviceType.G6_300.ics;

      case 14:
        return this.deviceType.G6_400.ics;

      case 15:
        return this.deviceType.G6_500.ics;

      case 30:
        return this.deviceType.EDGE.ics;

      case 40:
        return this.deviceType.OMNIA.ics;

      case 50:
        return this.deviceType.WIN_SERVER.ics;

      case 51:
        return this.deviceType.LINUX_SERVER.ics;

      default:
        return null;
    }
  }

  convertLegacyProductType(deviceICSType) {
    switch (deviceICSType) {
      case this.deviceType.G6_200.ics:
        return this.deviceType.G6_200.legacy;

      case this.deviceType.FUELPOS.ics:
        return this.deviceType.FUELPOS.legacy;

      case this.deviceType.G7_100.ics:
        return this.deviceType.G7_100.legacy;

      case this.deviceType.REGULUS.ics:
        return this.deviceType.REGULUS.legacy;

      case this.deviceType.G6_300.ics:
        return this.deviceType.G6_300.legacy;

      case this.deviceType.G6_400.ics:
        return this.deviceType.G6_400.legacy;

      case this.deviceType.G6_500.ics:
        return this.deviceType.G6_500.legacy;

      case this.deviceType.EDGE.ics:
        return this.deviceType.EDGE.legacy;

      case this.deviceType.OMNIA.ics:
        return this.deviceType.OMNIA.legacy;

      case this.deviceType.WIN_SERVER.ics:
        return this.deviceType.WIN_SERVER.legacy;

      case this.deviceType.LINUX_SERVER.ics:
        return this.deviceType.LINUX_SERVER.legacy;

      default:
        return null;
    }
  }

  async validateDeviceExistence(serialNumber, bridgeDeviceType) {
    const device = await this.db.read.row(
      `
          SELECT * FROM target t
          WHERE t.active IS true AND t.serial_number = $1 AND t.device_type = $2
      ;`,
      [serialNumber, bridgeDeviceType]
    );
    return device;
  }

  async validateSiteExistence(siteId) {
    const site = await this.db.read.row(
      `
          SELECT site_id, name, company_id
          FROM site
          WHERE site_id = $1 AND active = true
      ;`,
      [siteId]
    );
    return site || null;
  }

  async saveNewIssuerCodes(newIssuerCodes) {
    const issuerId = await this.db.write.execute(
      `INSERT INTO certificate_issuer
          ( issuer_code, description )
        VALUES
          ( UNNEST(ARRAY[$1::text[]]), 'NEW ISSUER CODE' )
        ON CONFLICT(issuer_code) DO NOTHING
        RETURNING certificate_issuer_id;
      `,
      [newIssuerCodes]
    );
    return issuerId;
  }

  async processIssuerCode(certificateString) {
    try {
      const systemIssuerRows = await this.db.read.rows(`
        SELECT ci.issuer_code
        FROM certificate_issuer ci;
      `);

      const systemIssuerCodes = systemIssuerRows.map(row => row.issuerCode);

      let certificateArray;
      try {
        certificateArray = JSON.parse(certificateString);
      } catch (error) {
        this.logger.error(
          error,
          '[processIssuerCode] Failed to parse certificate string.'
        );
        throw new Error('Invalid certificate string');
      }

      const certificateIssuerCodes = certificateArray
        .map(certificate => certificate.issuer_code)
        .filter(code => code !== undefined); // Filter out any undefined values

      const systemIssuerCodesSet = new Set(systemIssuerCodes);
      const newIssuerCodes = certificateIssuerCodes.filter(
        issuerCode => !systemIssuerCodesSet.has(issuerCode)
      );

      if (newIssuerCodes.length) {
        await this.saveNewIssuerCodes(newIssuerCodes);
      }
    } catch (error) {
      this.logger.error('Error processing issuer codes:', error);
    }
  }

  async getCompanyProductType(deviceType, companyId) {
    const type = await this.db.read.row(
      `
      SELECT pt.device_type
          FROM company_product_type cpt
          INNER JOIN product_type pt ON pt.device_type = cpt.device_type
          WHERE cpt.company = $1 AND pt.device_type = $2 AND pt.active = true
      ;`,
      [companyId, deviceType]
    );
    return type ? type.deviceType : null;
  }

  async getDeviceBySN(sn) {
    const device = await this.db.read.row(
      `
          SELECT t.target_id, t.serial_number, t.serial_number, t.last_contact, t.last_registered, t.name,
          t.description, t.site_id, get_device_health( t.target_id ) as status, t.key_group_ref, t.device_type,
          t.presence, s.name as site_name
          FROM target t
          JOIN site s ON s.site_id = t.site_id
          WHERE t.active IS true AND t.serial_number = $1
      ;`,
      [sn]
    );
    return device || null;
  }

  // eslint-disable-next-line class-methods-use-this
  pruneResponseData(device) {
    const responseKeys = [
      'id',
      'serialNumber',
      'lastContact',
      'lastRegistered',
      'name',
      'description',
      'siteId',
      'status',
      'siteName',
      'keyGroupRef',
      'productType',
    ];

    return responseKeys.reduce((acc, key) => {
      acc[key] = device[key];
      return acc;
    }, {});
  }
}
