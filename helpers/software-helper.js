const uuid = require('uuid/v4');
const { server } = require('../app');
const env = require('../env');

const getDeletionLimit = async companyId => {
  const deletionLimitQuery = `SELECT * from entity_settings_definitions esd 
                                    LEFT JOIN entity_settings es 
                                    ON esd.entity_settings_definitions_id = es.entity_settings_definitions_id 
                                    AND es.entity_id = $1 
                                    WHERE esd.feature_name = 'file_library' 
                                    AND esd.setting_name = 'FileDeletionLimit' 
                                    AND NOT esd.deleted
                                    `;

  const deletionLimit = await server.db.read.row(deletionLimitQuery, [
    companyId,
  ]);

  let maxFileDeleteLimit = 3000;

  if (deletionLimit && deletionLimit.entityValue) {
    maxFileDeleteLimit = deletionLimit.entityValue;
  } else if (deletionLimit && deletionLimit.defaultValue) {
    maxFileDeleteLimit = deletionLimit.defaultValue;
  }

  return maxFileDeleteLimit;
};

const addApiAuditLog = async ({ req, response, statusCode }) => {
  const connection = await server.db.write.getConnection();
  try {
    const companyId = req.user.company.id;
    const userId = req.user.sub;
    let remoteAddress = req.connection?.remoteAddress ?? '';
    const xForwardedFor = req.headers?.['x-forwarded-for'] || false;
    if (xForwardedFor) {
      // eslint-disable-next-line prefer-destructuring
      remoteAddress = xForwardedFor.split(', ')[0];
    }

    let { url } = req;
    if (req.url && env.config.base) {
      url = req.url.replace(`/${env.config.base}/`, '/rest/v1/');
    }

    const route = `${req.method} ${url}`;

    const request = req.body || {};

    await connection.execute('BEGIN');
    const timestamp = new Date().toISOString();
    // eslint-disable-next-line prefer-const
    let params = [];

    params.push(
      uuid(),
      userId,
      companyId,
      route,
      request,
      response,
      statusCode,
      timestamp,
      remoteAddress
    );

    const insertQuery = `
                        INSERT INTO report_api_audit ( request_id, user_id, company_id, route, request, response, status_code, timestamp, remote_address )
                        VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    `;

    await connection.execute(insertQuery, params);
    await connection.execute('COMMIT');
  } catch (err) {
    await connection.execute('ROLLBACK');
  } finally {
    connection.done();
  }
};

module.exports = {
  getDeletionLimit,
  addApiAuditLog,
};
